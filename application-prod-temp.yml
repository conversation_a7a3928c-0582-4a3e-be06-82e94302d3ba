server:
  tomcat:
    uri-encoding: UTF-8
#spring数据源
spring:
  main:
    allow-bean-definition-overriding: true #允许覆盖bean
  datasource:
    #MYsql连接字符串
    url: jdbc:mysql://${DATABASE_SER:192.168.99.240:53308/inkssom}?useUnicode=true&characterEncoding=utf-8&allowMutilQueries=true&serverTimezone=Asia/Shanghai&useSSL=false
    username: ${DATABASE_USER:root}
    password: ${DATABASE_PD:asd@123456}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 10
  flyway:
    enabled: false   # 禁用Spring Boot自动Flyway配置，使用自定义DataSourceHelper
    initsql: ${INIT_SQL:http://dev.inksyun.com:31080/utils/File/proxy/appsetup/inkssom_init.sql}
  mail:
    username: <EMAIL>
    password: ASDqwe@!@#
    host: smtp.qiye.aliyun.com
    default-encoding: UTF-8
    properties:
      mail.smtp.socketFactory.fallback: true
      mail.smtp.starttls.enable: true
    #    toEmail: <EMAIL>  #收件人
    toEmail: <EMAIL>  #登录日志的收件人
    ipAddress: 96  #ip地址
  web: #配置静态资源访问路径
    resources:
      static-locations: classpath:/
  mvc:
    view:
      suffix: .html


mybatis:
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inks.service.sa.lbl.**.domain
  #配置打印SQL语句到控制台


oss:
  type: minio
  bucket: inkssom
  minio:
    bucket: inkssom
    dirsuffix: ymlsuffix
    access-key: minioadmin
    secret-key: minioadmin
    endpoint: http://dev.inksyun.com:9080
    urlprefix: http://dev.inksyun.com:9080/
    #    urlprefix: http://dev.inksyun.com:9080/${oss.minio.bucket}/
  #  aliyun:
  #    bucket: inksoms
  #    access-key-id: LTAI5tL76QGhNx5eSkzkLnbv
  #    access-key-secret: ******************************
  #    endpoint: http://oss.oms.inksyun.com
  #    urlprefix: http://oss.oms.inksyun.com/
  aliyun:
    bucket: inkstable
    access-key-id: LTAI5t7gvbML44MA1pxPbr21
    access-key-secret: ******************************
    endpoint: https://oss-cn-qingdao.aliyuncs.com
    urlprefix: https://inkstable.oss-cn-qingdao.aliyuncs.com/
backup:
  sqlserver:
    dbname: inksdeli
    scheduling:
      cron: 0 0 1 * * *   # 每天凌晨1点执行备份
    password: 123456      # 下载后的备份文件解压密码
    directory: D:\backup\sqlserver  # 临时备份文件存放目录()
#      cron: 0 1 * * * *  # 在线Cron表达式生成器:https://cron.qqe2.com/

#oss:
#  bucket: inkslbl
#  minio:
#    access-key: minioadmin
#    secret-key: minioadmin
#    endpoint: http://inks.tpddns.net:9080

#雪花算法:  数据中心id,工作机器id
inks:
  license: ${LICENSE_KEY:}
  redisType: mysql
  user:
    wxscan-create: true    #微信公众号扫码可直接创建admin权限用户，默认false
  feign:
    GrfUrl: ${GRFURL:http://dev.inksyun.com:18801}
    UtsUrl: ${UTSURL:http://192.168.99.96:10684}
  # 调用oam公众号接口获取openid #内网测试号:http://192.168.99.96:10677 [wx58c9e35cc9fb9be5] 公网应凯科技:http://oam.inksyun.com [wx7850d75f765d0dce]
  oam:
    api: http://oam.inksyun.com
    appid: wx7850d75f765d0dce
  tid: tid-inks-som