apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: svc-sa-som
  name: svc-sa-som
  namespace: inksdev   #一定要写名称空间
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: svc-sa-som
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: svc-sa-som
    spec:
      imagePullSecrets:
        - name: aliyun-docker-hub  #提前在项目下配置访问阿里云的账号密码
      containers:
        - image: $REGISTRY/$DOCKERHUB_NAMESPACE/svc-sa-som:SNAPSHOT-$BUILD_NUMBER
          imagePullPolicy: Always
          name: svc-sa-som
          ports:
            - containerPort: 8080
              protocol: TCP
          env: #appconfig模板，网址和数字需加引号转字符
            - name: DATABASE_SER
              value: '192.168.99.240:53308/inkssom'
            - name: DATABASE_USER
              value: root
            - name: DATABASE_PD
              value: asd@123456
            - name: LICENSE_KEY
              value: ''
            - name: GRFURL
              value: 'http://dev.inksyun.com:18801'
            - name: UTSURL
              value: 'http://dev.inksyun.com:30484'
            - name: INIT_SQL
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: svc-sa-som
  name: svc-sa-som
  namespace: inksdev
spec:
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
      nodePort: 30465
  selector:
    app: svc-sa-som
  sessionAffinity: None
  type: NodePort
