package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 库存信息(MatInventory)实体类 无金额
 *
 * <AUTHOR>
 * @since 2021-12-12 14:23:42
 */
public class MatInventoryQtyPojo implements Serializable {
    private static final long serialVersionUID = 766111558268133938L;
    // 仓库名称
    @Excel(name = "仓库名称")
    private String storename;
    // 货品编码
    @Excel(name = "货品编码")
    private String goodsuid;
    // 名称
    @Excel(name = "名称")
    private String goodsname;
    // 规格
    @Excel(name = "规格")
    private String goodsspec;
    // 货品单位
    @Excel(name = "货品单位")
    private String goodsunit;
    // 商品数量
    @Excel(name = "数量")
    private Double quantity;
    // 末次入库
    @Excel(name = "末次入库")
    private String endinuid;
    // 入库时间
    @Excel(name = "入库时间",databaseFormat = "yyyyMMddHHmmss", format = "yyyy-MM-dd")
    private Date endindate;
    // 末次出库
    @Excel(name = "末次出库")
    private String endoutuid;
    // 出库时间
    @Excel(name = "出库时间", format = "yyyy-MM-dd")
    private Date endoutdate;
    // 外部编码
    @Excel(name = "外部编码")
    private String partid;
    // 批号
    @Excel(name = "批号")
    private String batchno;
    // 库位
    @Excel(name = "库位")
    private String location;
    // 最小包装SN
    @Excel(name = "最小包装SN")
    private String packsn;


    public String getStorename() {
        return storename;
    }

    public void setStorename(String storename) {
        this.storename = storename;
    }


    public String getGoodsuid() {
        return goodsuid;
    }


    public void setGoodsuid(String goodsuid) {
        this.goodsuid = goodsuid;
    }


    public String getGoodsname() {
        return goodsname;
    }


    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }


    public String getGoodsspec() {
        return goodsspec;
    }


    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }


    public String getGoodsunit() {
        return goodsunit;
    }


    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }


    public String getEndinuid() {
        return endinuid;
    }

    public void setEndinuid(String endinuid) {
        this.endinuid = endinuid;
    }

    public Date getEndindate() {
        return endindate;
    }

    public void setEndindate(Date endindate) {
        this.endindate = endindate;
    }

    public String getEndoutuid() {
        return endoutuid;
    }

    public void setEndoutuid(String endoutuid) {
        this.endoutuid = endoutuid;
    }

    public Date getEndoutdate() {
        return endoutdate;
    }

    public void setEndoutdate(Date endoutdate) {
        this.endoutdate = endoutdate;
    }


    public String getPartid() {
        return partid;
    }


    public void setPartid(String partid) {
        this.partid = partid;
    }

    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getPacksn() {
        return packsn;
    }

    public void setPacksn(String packsn) {
        this.packsn = packsn;
    }


}

