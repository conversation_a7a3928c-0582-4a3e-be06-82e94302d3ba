package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.GoodsPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 申领项目(MatRequisitionitem)Pojo
 *
 * <AUTHOR>
 * @since 2024-01-17 09:38:52
 */
public class MatRequisitionitemPojo extends GoodsPojo implements Serializable {
    private static final long serialVersionUID = -51463365412380009L;
     // id
  @Excel(name = "id")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 货品id
  @Excel(name = "货品id")    
  private String goodsid;
     // 领用数量
  @Excel(name = "领用数量")    
  private Double quantity;
     // 计划完成
  @Excel(name = "计划完成")    
  private Date plandate;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 拣货数量
  @Excel(name = "拣货数量")    
  private Double pickqty;
     // 完成数量
  @Excel(name = "完成数量")    
  private Double finishqty;
     // 已领成本
  @Excel(name = "已领成本")    
  private Double finishcost;
     // 状态编码
  @Excel(name = "状态编码")    
  private String statecode;
     // 状态日期
  @Excel(name = "状态日期")    
  private Date statedate;
     // 应用单号
  @Excel(name = "应用单号")    
  private String citeuid;
     // 引用子项id
  @Excel(name = "引用子项id")    
  private String citeitemid;
     // 客户
  @Excel(name = "客户")    
  private String customer;
     // 客户PO
  @Excel(name = "客户PO")    
  private String custpo;
     // 退仓数量
  @Excel(name = "退仓数量")    
  private Double backqty;
     // 退仓成本
  @Excel(name = "退仓成本")    
  private Double backcost;
     // 关闭
  @Excel(name = "关闭")    
  private Integer closed;
     // 标准数量
  @Excel(name = "标准数量")    
  private Double stdqty;
     // 仓库ID
  @Excel(name = "仓库ID")    
  private String storeid;
     // 库位
  @Excel(name = "库位")    
  private String location;
     // 批号
  @Excel(name = "批号")    
  private String batchno;
     // 销售单号
  @Excel(name = "销售单号")    
  private String machuid;
     // 销售子项id
  @Excel(name = "销售子项id")    
  private String machitemid;
     // 订单批次
  @Excel(name = "订单批次")    
  private String machbatch;
     // 销售Goodsid
  @Excel(name = "销售Goodsid")    
  private String machitemgoodid;
     // 销售客户id
  @Excel(name = "销售客户id")    
  private String machgroupid;
     // 主计划号
  @Excel(name = "主计划号")    
  private String mainplanuid;
     // 主计划Itemid
  @Excel(name = "主计划Itemid")    
  private String mainplanitemid;
     // 主计划Goodsid
  @Excel(name = "主计划Goodsid")    
  private String mainplanitemgoodid;
     // Mrp单号
  @Excel(name = "Mrp单号")    
  private String mrpuid;
     // Mrp子项id
  @Excel(name = "Mrp子项id")    
  private String mrpitemid;
     // MrpItemGoodsid
  @Excel(name = "MrpItemGoodsid")    
  private String mrpitemgoodid;
     // 1厂制2委制
  @Excel(name = "1厂制2委制")    
  private Integer wkbilltype;
     // 加工单号
  @Excel(name = "加工单号")    
  private String workuid;
     // 加工单Itemid
  @Excel(name = "加工单Itemid")    
  private String workitemid;
     // 加工单ItemGoodsid
  @Excel(name = "加工单ItemGoodsid")    
  private String workitemgoodid;
     // 物料子项id
  @Excel(name = "物料子项id")    
  private String workitemmatid;
     // 主料商品ID
  @Excel(name = "主料商品ID")    
  private String parentgoodsid;
     // 作废
  @Excel(name = "作废")    
  private Integer disannulmark;
     // 来源:0=其他
  @Excel(name = "来源:0=其他")    
  private Integer sourcetype;
     // 属性Josn
  @Excel(name = "属性Josn")    
  private String attributejson;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 自定义6
  @Excel(name = "自定义6")    
  private String custom6;
     // 自定义7
  @Excel(name = "自定义7")    
  private String custom7;
     // 自定义8
  @Excel(name = "自定义8")    
  private String custom8;
     // 自定义9
  @Excel(name = "自定义9")    
  private String custom9;
     // 自定义10
  @Excel(name = "自定义10")    
  private String custom10;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 租户名称
  @Excel(name = "租户名称")    
  private String tenantname;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 货品id
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
   // 领用数量
    public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
   // 计划完成
    public Date getPlandate() {
        return plandate;
    }
    
    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 拣货数量
    public Double getPickqty() {
        return pickqty;
    }
    
    public void setPickqty(Double pickqty) {
        this.pickqty = pickqty;
    }
        
   // 完成数量
    public Double getFinishqty() {
        return finishqty;
    }
    
    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }
        
   // 已领成本
    public Double getFinishcost() {
        return finishcost;
    }
    
    public void setFinishcost(Double finishcost) {
        this.finishcost = finishcost;
    }
        
   // 状态编码
    public String getStatecode() {
        return statecode;
    }
    
    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }
        
   // 状态日期
    public Date getStatedate() {
        return statedate;
    }
    
    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }
        
   // 应用单号
    public String getCiteuid() {
        return citeuid;
    }
    
    public void setCiteuid(String citeuid) {
        this.citeuid = citeuid;
    }
        
   // 引用子项id
    public String getCiteitemid() {
        return citeitemid;
    }
    
    public void setCiteitemid(String citeitemid) {
        this.citeitemid = citeitemid;
    }
        
   // 客户
    public String getCustomer() {
        return customer;
    }
    
    public void setCustomer(String customer) {
        this.customer = customer;
    }
        
   // 客户PO
    public String getCustpo() {
        return custpo;
    }
    
    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }
        
   // 退仓数量
    public Double getBackqty() {
        return backqty;
    }
    
    public void setBackqty(Double backqty) {
        this.backqty = backqty;
    }
        
   // 退仓成本
    public Double getBackcost() {
        return backcost;
    }
    
    public void setBackcost(Double backcost) {
        this.backcost = backcost;
    }
        
   // 关闭
    public Integer getClosed() {
        return closed;
    }
    
    public void setClosed(Integer closed) {
        this.closed = closed;
    }
        
   // 标准数量
    public Double getStdqty() {
        return stdqty;
    }
    
    public void setStdqty(Double stdqty) {
        this.stdqty = stdqty;
    }
        
   // 仓库ID
    public String getStoreid() {
        return storeid;
    }
    
    public void setStoreid(String storeid) {
        this.storeid = storeid;
    }
        
   // 库位
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
        
   // 批号
    public String getBatchno() {
        return batchno;
    }
    
    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }
        
   // 销售单号
    public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
   // 销售子项id
    public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
   // 订单批次
    public String getMachbatch() {
        return machbatch;
    }
    
    public void setMachbatch(String machbatch) {
        this.machbatch = machbatch;
    }
        
   // 销售Goodsid
    public String getMachitemgoodid() {
        return machitemgoodid;
    }
    
    public void setMachitemgoodid(String machitemgoodid) {
        this.machitemgoodid = machitemgoodid;
    }
        
   // 销售客户id
    public String getMachgroupid() {
        return machgroupid;
    }
    
    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }
        
   // 主计划号
    public String getMainplanuid() {
        return mainplanuid;
    }
    
    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }
        
   // 主计划Itemid
    public String getMainplanitemid() {
        return mainplanitemid;
    }
    
    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }
        
   // 主计划Goodsid
    public String getMainplanitemgoodid() {
        return mainplanitemgoodid;
    }
    
    public void setMainplanitemgoodid(String mainplanitemgoodid) {
        this.mainplanitemgoodid = mainplanitemgoodid;
    }
        
   // Mrp单号
    public String getMrpuid() {
        return mrpuid;
    }
    
    public void setMrpuid(String mrpuid) {
        this.mrpuid = mrpuid;
    }
        
   // Mrp子项id
    public String getMrpitemid() {
        return mrpitemid;
    }
    
    public void setMrpitemid(String mrpitemid) {
        this.mrpitemid = mrpitemid;
    }
        
   // MrpItemGoodsid
    public String getMrpitemgoodid() {
        return mrpitemgoodid;
    }
    
    public void setMrpitemgoodid(String mrpitemgoodid) {
        this.mrpitemgoodid = mrpitemgoodid;
    }
        
   // 1厂制2委制
    public Integer getWkbilltype() {
        return wkbilltype;
    }
    
    public void setWkbilltype(Integer wkbilltype) {
        this.wkbilltype = wkbilltype;
    }
        
   // 加工单号
    public String getWorkuid() {
        return workuid;
    }
    
    public void setWorkuid(String workuid) {
        this.workuid = workuid;
    }
        
   // 加工单Itemid
    public String getWorkitemid() {
        return workitemid;
    }
    
    public void setWorkitemid(String workitemid) {
        this.workitemid = workitemid;
    }
        
   // 加工单ItemGoodsid
    public String getWorkitemgoodid() {
        return workitemgoodid;
    }
    
    public void setWorkitemgoodid(String workitemgoodid) {
        this.workitemgoodid = workitemgoodid;
    }
        
   // 物料子项id
    public String getWorkitemmatid() {
        return workitemmatid;
    }
    
    public void setWorkitemmatid(String workitemmatid) {
        this.workitemmatid = workitemmatid;
    }
        
   // 主料商品ID
    public String getParentgoodsid() {
        return parentgoodsid;
    }
    
    public void setParentgoodsid(String parentgoodsid) {
        this.parentgoodsid = parentgoodsid;
    }
        
   // 作废
    public Integer getDisannulmark() {
        return disannulmark;
    }
    
    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }
        
   // 来源:0=其他
    public Integer getSourcetype() {
        return sourcetype;
    }
    
    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }
        
   // 属性Josn
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

