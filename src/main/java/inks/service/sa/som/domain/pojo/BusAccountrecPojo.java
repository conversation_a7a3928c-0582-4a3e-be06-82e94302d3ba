package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 结转记录(BusAccountrec)实体类
 *
 * <AUTHOR>
 * @since 2022-07-25 08:28:53
 */
public class BusAccountrecPojo implements Serializable {
    private static final long serialVersionUID = 635784459115360284L;
    // id
    @Excel(name = "id")
    private String id;
    // 结转年份
    @Excel(name = "结转年份")
    private Integer carryyear;
    // 结转月份
    @Excel(name = "结转月份")
    private Integer carrymonth;
    // 开始日期
    @Excel(name = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date startdate;
    // 结束日期
    @Excel(name = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date enddate;
    // 经办人
    @Excel(name = "经办人")
    private String operator;
    // 经办人id
    @Excel(name = "经办人id")
    private String operatorid;
    // 行号yyyymm
    @Excel(name = "行号yyyymm")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date modifydate;
    // 期初金额
    @Excel(name = "期初金额")
    private Double billopenamount;
    // 入账金额
    @Excel(name = "入账金额")
    private Double billinamount;
    // 出账金额
    @Excel(name = "出账金额")
    private Double billoutamount;
    // 期末金额
    @Excel(name = "期末金额")
    private Double billcloseamount;
    // 期初金额
    @Excel(name = "期初金额")
    private Double invoopenamount;
    // 入账金额
    @Excel(name = "入账金额")
    private Double invoinamount;
    // 出账金额
    @Excel(name = "出账金额")
    private Double invooutamount;
    // 期末金额
    @Excel(name = "期末金额")
    private Double invocloseamount;
    // 期初金额
    @Excel(name = "期初金额")
    private Double arapopenamount;
    // 入账金额
    @Excel(name = "入账金额")
    private Double arapinamount;
    // 出账金额
    @Excel(name = "出账金额")
    private Double arapoutamount;
    // 期末金额
    @Excel(name = "期末金额")
    private Double arapcloseamount;
    // 打印次数
    @Excel(name = "打印次数")
    private Integer printcount;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 结转年份
    public Integer getCarryyear() {
        return carryyear;
    }

    public void setCarryyear(Integer carryyear) {
        this.carryyear = carryyear;
    }

    // 结转月份
    public Integer getCarrymonth() {
        return carrymonth;
    }

    public void setCarrymonth(Integer carrymonth) {
        this.carrymonth = carrymonth;
    }

    // 开始日期
    public Date getStartdate() {
        return startdate;
    }

    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }

    // 结束日期
    public Date getEnddate() {
        return enddate;
    }

    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }

    // 经办人
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    // 经办人id
    public String getOperatorid() {
        return operatorid;
    }

    public Double getInvoopenamount() {
        return invoopenamount;
    }

    public void setInvoopenamount(Double invoopenamount) {
        this.invoopenamount = invoopenamount;
    }

    public Double getInvoinamount() {
        return invoinamount;
    }

    public void setInvoinamount(Double invoinamount) {
        this.invoinamount = invoinamount;
    }

    public Double getInvooutamount() {
        return invooutamount;
    }

    public void setInvooutamount(Double invooutamount) {
        this.invooutamount = invooutamount;
    }

    public Double getInvocloseamount() {
        return invocloseamount;
    }

    public void setInvocloseamount(Double invocloseamount) {
        this.invocloseamount = invocloseamount;
    }

    public Double getArapopenamount() {
        return arapopenamount;
    }

    public void setArapopenamount(Double arapopenamount) {
        this.arapopenamount = arapopenamount;
    }

    public Double getArapinamount() {
        return arapinamount;
    }

    public void setArapinamount(Double arapinamount) {
        this.arapinamount = arapinamount;
    }

    public Double getArapoutamount() {
        return arapoutamount;
    }

    public void setArapoutamount(Double arapoutamount) {
        this.arapoutamount = arapoutamount;
    }

    public Double getArapcloseamount() {
        return arapcloseamount;
    }

    public void setArapcloseamount(Double arapcloseamount) {
        this.arapcloseamount = arapcloseamount;
    }

    public void setOperatorid(String operatorid) {
        this.operatorid = operatorid;
    }

    // 行号yyyymm
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    public Double getBillopenamount() {
        return billopenamount;
    }

    public void setBillopenamount(Double billopenamount) {
        this.billopenamount = billopenamount;
    }

    public Double getBillinamount() {
        return billinamount;
    }

    public void setBillinamount(Double billinamount) {
        this.billinamount = billinamount;
    }

    public Double getBilloutamount() {
        return billoutamount;
    }

    public void setBilloutamount(Double billoutamount) {
        this.billoutamount = billoutamount;
    }

    public Double getBillcloseamount() {
        return billcloseamount;
    }

    public void setBillcloseamount(Double billcloseamount) {
        this.billcloseamount = billcloseamount;
    }

    public Integer getPrintcount() {
        return printcount;
    }

    public void setPrintcount(Integer printcount) {
        this.printcount = printcount;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

