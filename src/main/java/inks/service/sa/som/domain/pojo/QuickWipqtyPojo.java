package inks.service.sa.som.domain.pojo;

import java.util.Date;

public class QuickWipqtyPojo {
    // wipID WipNote.id
    private String wipid;
    // 过速记录ID WipQty.id
    private String wipqtyid;
    // RowNum
    private Integer rownum;
    // 加工单号
    private String workuid;
    // 工序ID
    private String wpid;
    // 生产车间id
    private String groupid;
    // 仓库id
    private String storeid;
    // 仓库name
    private String storename;
    //制表
    private String lister;
    //数量
    private Double qty;
    // 规格JSON
    private String specjson;
    // 工作参数
    private String workparam;
    // 工位id
    private String statid;
    // 工位编码
    private String statcode;
    // 工位名称
    private String statname;
    // 本次工时
    private Double worktime;
    // 开始时间
    private Date startdate;
    // 批号(/quickInStore完工入库前端传入)
    private String batchno;
    // 备注
    private String remark;

    // 出/入组的操作人(前端传入)
    private String worker;
    // 完成出库
    private Integer storeout;

    public Integer getStoreout() {
        return storeout;
    }

    public void setStoreout(Integer storeout) {
        this.storeout = storeout;
    }

    public String getWipid() {
        return wipid;
    }

    public void setWipid(String wipid) {
        this.wipid = wipid;
    }

    public String getWorker() {
        return worker;
    }

    public void setWorker(String worker) {
        this.worker = worker;
    }

    public String getWipqtyid() {
        return wipqtyid;
    }

    public void setWipqtyid(String wipqtyid) {
        this.wipqtyid = wipqtyid;
    }

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    public String getWorkuid() {
        return workuid;
    }

    public void setWorkuid(String workuid) {
        this.workuid = workuid;
    }

    public String getWpid() {
        return wpid;
    }

    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    public Date getStartdate() {
        return startdate;
    }

    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }

    public void setWpid(String wpid) {
        this.wpid = wpid;
    }

    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    public String getStoreid() {
        return storeid;
    }

    public void setStoreid(String storeid) {
        this.storeid = storeid;
    }

    public String getStorename() {
        return storename;
    }

    public void setStorename(String storename) {
        this.storename = storename;
    }

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    public Double getQty() {
        return qty;
    }

    public void setQty(Double qty) {
        this.qty = qty;
    }

    public String getSpecjson() {
        return specjson;
    }

    public void setSpecjson(String specjson) {
        this.specjson = specjson;
    }

    public String getWorkparam() {
        return workparam;
    }

    public void setWorkparam(String workparam) {
        this.workparam = workparam;
    }

    public String getStatid() {
        return statid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setStatid(String statid) {
        this.statid = statid;
    }

    public String getStatcode() {
        return statcode;
    }

    public void setStatcode(String statcode) {
        this.statcode = statcode;
    }

    public String getStatname() {
        return statname;
    }

    public void setStatname(String statname) {
        this.statname = statname;
    }

    public Double getWorktime() {
        return worktime;
    }

    public void setWorktime(Double worktime) {
        this.worktime = worktime;
    }
}
