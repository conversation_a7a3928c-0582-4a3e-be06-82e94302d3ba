package inks.service.sa.som.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 销售扣款Item(BusDeductionitem)Entity
 *
 * <AUTHOR>
 * @since 2022-04-16 16:58:46
 */
public class BusDeductionitemEntity implements Serializable {
    private static final long serialVersionUID = 475717658207446710L;
          // id
         private String id;
          // Pid
         private String pid;
          // 商品编码
         private String goodsid;
          // 数量
         private Double quantity;
          // 含税单价
         private Double taxprice;
          // 含税金额
         private Double taxamount;
          // 税额
         private Double taxtotal;
          // 记录税率
         private Integer itemtaxrate;
          // 单价
         private Double price;
          // 金额(备用)
         private Double amount;
          // 备注
         private String remark;
          // 引用号
         private String citeuid;
          // 引用子项id
         private String citeitemid;
          // 订单号
         private String machuid;
          // 订单子项id
         private String machitemid;
          // 客户订单号
         private String custpo;
          // 行号
         private Integer rownum;
          // 发票数量
         private Double invoqty;
          // 发票关闭
         private Integer invoclosed;
          // 作废
         private Integer disannulmark;
          // 作废经办id
         private String disannullisterid;
          // 作废经办
         private String disannullister;
          // 作废日期
         private Date disannuldate;
          // 自定义1
         private String custom1;
          // 自定义2
         private String custom2;
          // 自定义3
         private String custom3;
          // 自定义4
         private String custom4;
          // 自定义5
         private String custom5;
          // 自定义6
         private String custom6;
          // 自定义7
         private String custom7;
          // 自定义8
         private String custom8;
          // 自定义9
         private String custom9;
          // 自定义10
         private String custom10;
          // 租户id
         private String tenantid;
          // 乐观锁
         private Integer revision;

    // id
      public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
    // Pid
      public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
    // 商品编码
      public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
    // 数量
      public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
    // 含税单价
      public Double getTaxprice() {
        return taxprice;
    }
    
    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }
        
    // 含税金额
      public Double getTaxamount() {
        return taxamount;
    }
    
    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }
        
    // 税额
      public Double getTaxtotal() {
        return taxtotal;
    }
    
    public void setTaxtotal(Double taxtotal) {
        this.taxtotal = taxtotal;
    }
        
    // 记录税率
      public Integer getItemtaxrate() {
        return itemtaxrate;
    }
    
    public void setItemtaxrate(Integer itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }
        
    // 单价
      public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
    // 金额(备用)
      public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
        
    // 备注
      public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
    // 引用号
      public String getCiteuid() {
        return citeuid;
    }
    
    public void setCiteuid(String citeuid) {
        this.citeuid = citeuid;
    }
        
    // 引用子项id
      public String getCiteitemid() {
        return citeitemid;
    }
    
    public void setCiteitemid(String citeitemid) {
        this.citeitemid = citeitemid;
    }
        
    // 订单号
      public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
    // 订单子项id
      public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
    // 客户订单号
      public String getCustpo() {
        return custpo;
    }
    
    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }
        
    // 行号
      public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
    // 发票数量
      public Double getInvoqty() {
        return invoqty;
    }
    
    public void setInvoqty(Double invoqty) {
        this.invoqty = invoqty;
    }
        
    // 发票关闭
      public Integer getInvoclosed() {
        return invoclosed;
    }
    
    public void setInvoclosed(Integer invoclosed) {
        this.invoclosed = invoclosed;
    }
        
    // 作废
      public Integer getDisannulmark() {
        return disannulmark;
    }
    
    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }
        
    // 作废经办id
      public String getDisannullisterid() {
        return disannullisterid;
    }
    
    public void setDisannullisterid(String disannullisterid) {
        this.disannullisterid = disannullisterid;
    }
        
    // 作废经办
      public String getDisannullister() {
        return disannullister;
    }
    
    public void setDisannullister(String disannullister) {
        this.disannullister = disannullister;
    }
        
    // 作废日期
      public Date getDisannuldate() {
        return disannuldate;
    }
    
    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }
        
    // 自定义1
      public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
    // 自定义2
      public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
    // 自定义3
      public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
    // 自定义4
      public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
    // 自定义5
      public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
    // 自定义6
      public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
    // 自定义7
      public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
    // 自定义8
      public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
    // 自定义9
      public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
    // 自定义10
      public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
    // 租户id
      public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
    // 乐观锁
      public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

