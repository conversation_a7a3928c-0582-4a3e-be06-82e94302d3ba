package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 出入库项目(MatAccessitem)Pojo
 *
 * <AUTHOR>
 * @since 2022-05-12 13:01:25
 */
public class MatAccessitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -43990559352086994L;
         // ID
       @Excel(name = "ID")
  private String id;
         // Pid
       @Excel(name = "Pid")
  private String pid;
         // 商品id
       @Excel(name = "商品id")
  private String goodsid;
         // 数量
       @Excel(name = "数量")
  private Double quantity;
         // 单价
       @Excel(name = "单价")
  private Double price;
         // 金额
       @Excel(name = "金额")
  private Double amount;
         // 含税单价
       @Excel(name = "含税单价")
  private Double taxprice;
         // 含税金额
       @Excel(name = "含税金额")
  private Double taxamount;
         // 记录税率
       @Excel(name = "记录税率")
  private Integer itemtaxrate;
         // 税额
       @Excel(name = "税额")
  private Double taxtotal;
         // 备注
       @Excel(name = "备注")
  private String remark;
         // 应用单号
       @Excel(name = "应用单号")
  private String citeuid;
         // 引用子项id
       @Excel(name = "引用子项id")
  private String citeitemid;
         // 状态
       @Excel(name = "状态")
  private String statecode;
         // 状态时间
       @Excel(name = "状态时间")
  private Date statedate;
         // 行号
       @Excel(name = "行号")
  private Integer rownum;
         // 库位编码
       @Excel(name = "库位编码")
  private String location;
         // 批号
       @Excel(name = "批号")
  private String batchno;
         // 最小包装SN
       @Excel(name = "最小包装SN")
  private String packsn;
         // 有效期
       @Excel(name = "有效期")
  private Date expidate;
         // 客户
       @Excel(name = "客户")
  private String customer;
         // 客户PO
       @Excel(name = "客户PO")
  private String custpo;
         // 销售单号
       @Excel(name = "销售单号")
  private String machuid;
         // 销售子项id
       @Excel(name = "销售子项id")
  private String machitemid;
         // 销售客户id
       @Excel(name = "销售客户id")
  private String machgroupid;
         // 主计划号
       @Excel(name = "主计划号")
  private String mainplanuid;
         // 主计划Itemid
       @Excel(name = "主计划Itemid")
  private String mainplanitemid;
         // Mrp单号
       @Excel(name = "Mrp单号")
  private String mrpuid;
         // Mrp子项id
       @Excel(name = "Mrp子项id")
  private String mrpitemid;
         // 库存id防重选
       @Excel(name = "库存id防重选")
  private String inveid;
         // skuid
       @Excel(name = "skuid")
  private String skuid;
         // 属性Josn
       @Excel(name = "属性Josn")
  private String attributejson;
    // 标签条码集
    @Excel(name = "标签条码集")
    private String labelcodes;
    // 标签合计数
    @Excel(name = "标签合计数")
    private Double labelqty;
    // 来源:0=其他
    @Excel(name = "来源:0=其他")
    private Integer sourcetype;
    // 采购单号
    @Excel(name = "采购单号")
    private String orderuid;
    // 采购子项id
    @Excel(name = "采购子项id")
    private String orderitemid;
    // 采购订单的OrderNo
    @Excel(name = "采购订单的OrderNo")
    private String orderno;
    // 加工单号
    @Excel(name = "加工单号")
    private String workuid;
    // 加工子项id
    @Excel(name = "加工子项id")
    private String workitemid;
    // 委外单号
    @Excel(name = "委外单号")
    private String subcuid;
    // 委外子项id
    @Excel(name = "委外子项id")
    private String subcitemid;
    // 客供单号
    @Excel(name = "客供单号")
    private String custuid;
    // 客供子项id
    @Excel(name = "客供子项id")
    private String cuistitemid;
         // 自定义1
       @Excel(name = "自定义1")
  private String custom1;
         // 自定义2
       @Excel(name = "自定义2")
  private String custom2;
         // 自定义3
       @Excel(name = "自定义3")
  private String custom3;
         // 自定义4
       @Excel(name = "自定义4")
  private String custom4;
         // 自定义5
       @Excel(name = "自定义5")
  private String custom5;
         // 自定义6
       @Excel(name = "自定义6")
  private String custom6;
         // 自定义7
       @Excel(name = "自定义7")
  private String custom7;
         // 自定义8
       @Excel(name = "自定义8")
  private String custom8;
         // 自定义9
       @Excel(name = "自定义9")
  private String custom9;
         // 自定义10
       @Excel(name = "自定义10")
  private String custom10;
         // 租户id
       @Excel(name = "租户id")
  private String tenantid;
         // 乐观锁
       @Excel(name = "乐观锁")
  private Integer revision;
    // 生产过数
    private String wkqtyid;

     // ID
       public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

     // Pid
       public String getPid() {
        return pid;
    }

    public String getLabelcodes() {
        return labelcodes;
    }

    public void setLabelcodes(String labelcodes) {
        this.labelcodes = labelcodes;
    }

    public Double getLabelqty() {
        return labelqty;
    }

    public void setLabelqty(Double labelqty) {
        this.labelqty = labelqty;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public Integer getSourcetype() {
        return sourcetype;
    }

    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }

    // 商品id
       public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

     // 数量
       public Double getQuantity() {
        return quantity;
    }

    public String getOrderno() {
        return orderno;
    }

    public void setOrderno(String orderno) {
        this.orderno = orderno;
    }

    public String getOrderuid() {
        return orderuid;
    }

    public void setOrderuid(String orderuid) {
        this.orderuid = orderuid;
    }

    public String getOrderitemid() {
        return orderitemid;
    }

    public void setOrderitemid(String orderitemid) {
        this.orderitemid = orderitemid;
    }

    public String getWorkuid() {
        return workuid;
    }

    public void setWorkuid(String workuid) {
        this.workuid = workuid;
    }

    public String getWorkitemid() {
        return workitemid;
    }

    public void setWorkitemid(String workitemid) {
        this.workitemid = workitemid;
    }

    public String getSubcuid() {
        return subcuid;
    }

    public void setSubcuid(String subcuid) {
        this.subcuid = subcuid;
    }

    public String getSubcitemid() {
        return subcitemid;
    }

    public void setSubcitemid(String subcitemid) {
        this.subcitemid = subcitemid;
    }

    public String getCustuid() {
        return custuid;
    }

    public void setCustuid(String custuid) {
        this.custuid = custuid;
    }

    public String getCuistitemid() {
        return cuistitemid;
    }

    public void setCuistitemid(String cuistitemid) {
        this.cuistitemid = cuistitemid;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

     // 单价
       public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

     // 金额
       public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

     // 含税单价
       public Double getTaxprice() {
        return taxprice;
    }

    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }

     // 含税金额
       public Double getTaxamount() {
        return taxamount;
    }

    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }

     // 记录税率
       public Integer getItemtaxrate() {
        return itemtaxrate;
    }

    public void setItemtaxrate(Integer itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }

     // 税额
       public Double getTaxtotal() {
        return taxtotal;
    }

    public void setTaxtotal(Double taxtotal) {
        this.taxtotal = taxtotal;
    }

     // 备注
       public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

     // 应用单号
       public String getCiteuid() {
        return citeuid;
    }

    public void setCiteuid(String citeuid) {
        this.citeuid = citeuid;
    }

     // 引用子项id
       public String getCiteitemid() {
        return citeitemid;
    }

    public void setCiteitemid(String citeitemid) {
        this.citeitemid = citeitemid;
    }

     // 状态
       public String getStatecode() {
        return statecode;
    }

    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }

     // 状态时间
       public Date getStatedate() {
        return statedate;
    }

    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }

     // 行号
       public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

     // 库位编码
       public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

     // 批号
       public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

     // 最小包装SN
       public String getPacksn() {
        return packsn;
    }

    public void setPacksn(String packsn) {
        this.packsn = packsn;
    }

     // 有效期
       public Date getExpidate() {
        return expidate;
    }

    public void setExpidate(Date expidate) {
        this.expidate = expidate;
    }

     // 客户
       public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

     // 客户PO
       public String getCustpo() {
        return custpo;
    }

    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }

     // 销售单号
       public String getMachuid() {
        return machuid;
    }

    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }

     // 销售子项id
       public String getMachitemid() {
        return machitemid;
    }

    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }

     // 销售客户id
       public String getMachgroupid() {
        return machgroupid;
    }

    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }

     // 主计划号
       public String getMainplanuid() {
        return mainplanuid;
    }

    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }

     // 主计划Itemid
       public String getMainplanitemid() {
        return mainplanitemid;
    }

    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }

     // Mrp单号
       public String getMrpuid() {
        return mrpuid;
    }

    public void setMrpuid(String mrpuid) {
        this.mrpuid = mrpuid;
    }

     // Mrp子项id
       public String getMrpitemid() {
        return mrpitemid;
    }

    public void setMrpitemid(String mrpitemid) {
        this.mrpitemid = mrpitemid;
    }

     // 库存id防重选
       public String getInveid() {
        return inveid;
    }

    public void setInveid(String inveid) {
        this.inveid = inveid;
    }

     // skuid
       public String getSkuid() {
        return skuid;
    }

    public void setSkuid(String skuid) {
        this.skuid = skuid;
    }

     // 属性Josn
       public String getAttributejson() {
        return attributejson;
    }

    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }

     // 自定义1
       public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

     // 自定义2
       public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

     // 自定义3
       public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

     // 自定义4
       public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

     // 自定义5
       public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

     // 自定义6
       public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

     // 自定义7
       public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

     // 自定义8
       public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

     // 自定义9
       public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

     // 自定义10
       public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

     // 租户id
       public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

     // 乐观锁
       public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getWkqtyid() {
        return wkqtyid;
    }

    public void setWkqtyid(String wkqtyid) {
        this.wkqtyid = wkqtyid;
    }
}

