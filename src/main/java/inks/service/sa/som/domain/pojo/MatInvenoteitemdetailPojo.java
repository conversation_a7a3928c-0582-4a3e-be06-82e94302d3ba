package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 盘点项目(MatInvenoteitem)Pojo
 *
 * <AUTHOR>
 * @since 2022-03-14 21:02:45
 */
public class MatInvenoteitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = 504990489304602229L;
         // id
       @Excel(name = "id")    
  private String id;
         // Pid
       @Excel(name = "Pid")    
  private String pid;
         // 仓库ID备用
       @Excel(name = "仓库ID备用")    
  private String inveid;
         // 商品ID
       @Excel(name = "商品ID")    
  private String goodsid;
         // 产品编码
       @Excel(name = "产品编码")    
  private String itemcode;
         // 产品名称
       @Excel(name = "产品名称")    
  private String itemname;
         // 产品规格
       @Excel(name = "产品规格")    
  private String itemspec;
         // 产品单位
       @Excel(name = "产品单位")    
  private String itemunit;
         // 账面数量
       @Excel(name = "账面数量")    
  private Double quantity;
         // 账面金额
       @Excel(name = "账面金额")    
  private Double amount;
         // 库位
       @Excel(name = "库位")    
  private String location;
         // 批号
       @Excel(name = "批号")    
  private String batchno;
         // 最小包装SN
       @Excel(name = "最小包装SN")    
  private String packsn;
         // 有效期
       @Excel(name = "有效期")    
  private Date expidate;
         // RowNum
       @Excel(name = "RowNum")    
  private Integer rownum;
         // 最后单据
       @Excel(name = "最后单据")    
  private String enduid;
         // 末次入库
       @Excel(name = "末次入库")    
  private String endinuid;
         // 入库时间
       @Excel(name = "入库时间")    
  private Date endindate;
         // 末次出库
       @Excel(name = "末次出库")    
  private String endoutuid;
         // 出库时间
       @Excel(name = "出库时间")    
  private Date endoutdate;
         // 盘点数量
       @Excel(name = "盘点数量")    
  private Double currqty;
         // 盘点金额
       @Excel(name = "盘点金额")    
  private Double curramt;
         // 溢出数量
       @Excel(name = "溢出数量")    
  private Double overflowqty;
         // 溢出金额
       @Excel(name = "溢出金额")    
  private Double overflowamt;
         // 完成数量
       @Excel(name = "完成数量")    
  private Double finishqty;
         // 完成金额
       @Excel(name = "完成金额")    
  private Double finishamt;
         // 备注
       @Excel(name = "备注")    
  private String remark;
         // 自定义1
       @Excel(name = "自定义1")    
  private String custom1;
         // 自定义2
       @Excel(name = "自定义2")    
  private String custom2;
         // 自定义3
       @Excel(name = "自定义3")    
  private String custom3;
         // 自定义4
       @Excel(name = "自定义4")    
  private String custom4;
         // 自定义5
       @Excel(name = "自定义5")    
  private String custom5;
         // 自定义6
       @Excel(name = "自定义6")    
  private String custom6;
         // 自定义7
       @Excel(name = "自定义7")    
  private String custom7;
         // 自定义8
       @Excel(name = "自定义8")    
  private String custom8;
         // 自定义9
       @Excel(name = "自定义9")    
  private String custom9;
         // 自定义10
       @Excel(name = "自定义10")    
  private String custom10;
         // 租户id
       @Excel(name = "租户id")    
  private String tenantid;
         // 租户
       @Excel(name = "租户")    
  private String tenantidname;
         // 乐观锁
       @Excel(name = "乐观锁")    
  private Integer revision;

     // id
       public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
     // Pid
       public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
     // 仓库ID备用
       public String getInveid() {
        return inveid;
    }
    
    public void setInveid(String inveid) {
        this.inveid = inveid;
    }
        
     // 商品ID
       public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
     // 产品编码
       public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
     // 产品名称
       public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
     // 产品规格
       public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
     // 产品单位
       public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
     // 账面数量
       public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
     // 账面金额
       public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
        
     // 库位
       public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
        
     // 批号
       public String getBatchno() {
        return batchno;
    }
    
    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }
        
     // 最小包装SN
       public String getPacksn() {
        return packsn;
    }
    
    public void setPacksn(String packsn) {
        this.packsn = packsn;
    }
        
     // 有效期
       public Date getExpidate() {
        return expidate;
    }
    
    public void setExpidate(Date expidate) {
        this.expidate = expidate;
    }
        
     // RowNum
       public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
     // 最后单据
       public String getEnduid() {
        return enduid;
    }
    
    public void setEnduid(String enduid) {
        this.enduid = enduid;
    }
        
     // 末次入库
       public String getEndinuid() {
        return endinuid;
    }
    
    public void setEndinuid(String endinuid) {
        this.endinuid = endinuid;
    }
        
     // 入库时间
       public Date getEndindate() {
        return endindate;
    }
    
    public void setEndindate(Date endindate) {
        this.endindate = endindate;
    }
        
     // 末次出库
       public String getEndoutuid() {
        return endoutuid;
    }
    
    public void setEndoutuid(String endoutuid) {
        this.endoutuid = endoutuid;
    }
        
     // 出库时间
       public Date getEndoutdate() {
        return endoutdate;
    }
    
    public void setEndoutdate(Date endoutdate) {
        this.endoutdate = endoutdate;
    }
        
     // 盘点数量
       public Double getCurrqty() {
        return currqty;
    }
    
    public void setCurrqty(Double currqty) {
        this.currqty = currqty;
    }
        
     // 盘点金额
       public Double getCurramt() {
        return curramt;
    }
    
    public void setCurramt(Double curramt) {
        this.curramt = curramt;
    }
        
     // 溢出数量
       public Double getOverflowqty() {
        return overflowqty;
    }
    
    public void setOverflowqty(Double overflowqty) {
        this.overflowqty = overflowqty;
    }
        
     // 溢出金额
       public Double getOverflowamt() {
        return overflowamt;
    }
    
    public void setOverflowamt(Double overflowamt) {
        this.overflowamt = overflowamt;
    }
        
     // 完成数量
       public Double getFinishqty() {
        return finishqty;
    }
    
    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }
        
     // 完成金额
       public Double getFinishamt() {
        return finishamt;
    }
    
    public void setFinishamt(Double finishamt) {
        this.finishamt = finishamt;
    }
        
     // 备注
       public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
     // 自定义1
       public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
     // 自定义2
       public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
     // 自定义3
       public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
     // 自定义4
       public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
     // 自定义5
       public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
     // 自定义6
       public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
     // 自定义7
       public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
     // 自定义8
       public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
     // 自定义9
       public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
     // 自定义10
       public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
     // 租户id
       public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
     // 租户
       public String getTenantidname() {
        return tenantidname;
    }
    
    public void setTenantidname(String tenantidname) {
        this.tenantidname = tenantidname;
    }
        
     // 乐观锁
       public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

