package inks.service.sa.som.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 应付账单(BuyAccount)实体类
 *
 * <AUTHOR>
 * @since 2024-03-05 13:48:24
 */
public class BuyAccountEntity implements Serializable {
    private static final long serialVersionUID = -31055101691402142L;
      // id
      private String id;
      // 编码
      private String refno;
      // 单据类型
      private String billtype;
      // 单据日期
      private Date billdate;
      // 项目id
      private String projectid;
      // 项目编码
      private String projcode;
      // 项目名称
      private String projname;
      // 单据标题
      private String billtitle;
      // 客户id
      private String groupid;
      // 结转年份
      private Integer carryyear;
      // 结转月份
      private Integer carrymonth;
      // 开始日期
      private Date startdate;
      // 结束日期
      private Date enddate;
      // 经办人
      private String operator;
      // 经办人id
      private String operatorid;
      // 行号yyyymm
      private Integer rownum;
      // 摘要
      private String summary;
      // 创建者
      private String createby;
      // 创建者id
      private String createbyid;
      // 新建日期
      private Date createdate;
      // 制表
      private String lister;
      // 制表id
      private String listerid;
      // 修改日期
      private Date modifydate;
      // 期初金额
      private Double billopenamount;
      // 入账金额
      private Double billinamount;
      // 出账金额
      private Double billoutamount;
      // 期末金额
      private Double billcloseamount;
      // 期初金额
      private Double invoopenamount;
      // 入账金额
      private Double invoinamount;
      // 出账金额
      private Double invooutamount;
      // 期末金额
      private Double invocloseamount;
      // 期初金额
      private Double arapopenamount;
      // 入账金额
      private Double arapinamount;
      // 出账金额
      private Double arapoutamount;
      // 期末金额
      private Double arapcloseamount;
      // 打印次数
      private Integer printcount;
      // 自定义1
      private String custom1;
      // 自定义2
      private String custom2;
      // 自定义3
      private String custom3;
      // 自定义4
      private String custom4;
      // 自定义5
      private String custom5;
      // 自定义6
      private String custom6;
      // 自定义7
      private String custom7;
      // 自定义8
      private String custom8;
      // 自定义9
      private String custom9;
      // 自定义10
      private String custom10;
      // 组织id
      private String deptid;
      // 租户id
      private String tenantid;
      // 租户名称
      private String tenantname;
      // 乐观锁
      private Integer revision;

       // id
     
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
       // 编码
     
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }
       // 单据类型
     
    public String getBilltype() {
        return billtype;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
       // 单据日期
     
    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
       // 项目id
     
    public String getProjectid() {
        return projectid;
    }

    public void setProjectid(String projectid) {
        this.projectid = projectid;
    }
       // 项目编码
     
    public String getProjcode() {
        return projcode;
    }

    public void setProjcode(String projcode) {
        this.projcode = projcode;
    }
       // 项目名称
     
    public String getProjname() {
        return projname;
    }

    public void setProjname(String projname) {
        this.projname = projname;
    }
       // 单据标题
     
    public String getBilltitle() {
        return billtitle;
    }

    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
       // 客户id
     
    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }
       // 结转年份
     
    public Integer getCarryyear() {
        return carryyear;
    }

    public void setCarryyear(Integer carryyear) {
        this.carryyear = carryyear;
    }
       // 结转月份
     
    public Integer getCarrymonth() {
        return carrymonth;
    }

    public void setCarrymonth(Integer carrymonth) {
        this.carrymonth = carrymonth;
    }
       // 开始日期
     
    public Date getStartdate() {
        return startdate;
    }

    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }
       // 结束日期
     
    public Date getEnddate() {
        return enddate;
    }

    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }
       // 经办人
     
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
       // 经办人id
     
    public String getOperatorid() {
        return operatorid;
    }

    public void setOperatorid(String operatorid) {
        this.operatorid = operatorid;
    }
       // 行号yyyymm
     
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
       // 摘要
     
    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }
       // 创建者
     
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
       // 创建者id
     
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
       // 新建日期
     
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
       // 制表
     
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
       // 制表id
     
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
       // 修改日期
     
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
       // 期初金额
     
    public Double getBillopenamount() {
        return billopenamount;
    }

    public void setBillopenamount(Double billopenamount) {
        this.billopenamount = billopenamount;
    }
       // 入账金额
     
    public Double getBillinamount() {
        return billinamount;
    }

    public void setBillinamount(Double billinamount) {
        this.billinamount = billinamount;
    }
       // 出账金额
     
    public Double getBilloutamount() {
        return billoutamount;
    }

    public void setBilloutamount(Double billoutamount) {
        this.billoutamount = billoutamount;
    }
       // 期末金额
     
    public Double getBillcloseamount() {
        return billcloseamount;
    }

    public void setBillcloseamount(Double billcloseamount) {
        this.billcloseamount = billcloseamount;
    }
       // 期初金额
     
    public Double getInvoopenamount() {
        return invoopenamount;
    }

    public void setInvoopenamount(Double invoopenamount) {
        this.invoopenamount = invoopenamount;
    }
       // 入账金额
     
    public Double getInvoinamount() {
        return invoinamount;
    }

    public void setInvoinamount(Double invoinamount) {
        this.invoinamount = invoinamount;
    }
       // 出账金额
     
    public Double getInvooutamount() {
        return invooutamount;
    }

    public void setInvooutamount(Double invooutamount) {
        this.invooutamount = invooutamount;
    }
       // 期末金额
     
    public Double getInvocloseamount() {
        return invocloseamount;
    }

    public void setInvocloseamount(Double invocloseamount) {
        this.invocloseamount = invocloseamount;
    }
       // 期初金额
     
    public Double getArapopenamount() {
        return arapopenamount;
    }

    public void setArapopenamount(Double arapopenamount) {
        this.arapopenamount = arapopenamount;
    }
       // 入账金额
     
    public Double getArapinamount() {
        return arapinamount;
    }

    public void setArapinamount(Double arapinamount) {
        this.arapinamount = arapinamount;
    }
       // 出账金额
     
    public Double getArapoutamount() {
        return arapoutamount;
    }

    public void setArapoutamount(Double arapoutamount) {
        this.arapoutamount = arapoutamount;
    }
       // 期末金额
     
    public Double getArapcloseamount() {
        return arapcloseamount;
    }

    public void setArapcloseamount(Double arapcloseamount) {
        this.arapcloseamount = arapcloseamount;
    }
       // 打印次数
     
    public Integer getPrintcount() {
        return printcount;
    }

    public void setPrintcount(Integer printcount) {
        this.printcount = printcount;
    }
       // 自定义1
     
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
       // 自定义2
     
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
       // 自定义3
     
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
       // 自定义4
     
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
       // 自定义5
     
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
       // 自定义6
     
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
       // 自定义7
     
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
       // 自定义8
     
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
       // 自定义9
     
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
       // 自定义10
     
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
       // 组织id
     
    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
       // 租户id
     
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
       // 租户名称
     
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
       // 乐观锁
     
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

