package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 往来单位(AppWorkgroup)实体类
 *
 * <AUTHOR>
 * @since 2022-07-06 16:08:49
 */
public class AppWorkgroupPojo implements Serializable {
    private static final long serialVersionUID = 106916055698059316L;
    // id
    private String id;
    // 分组id
    private String wggroupid;
    // 编码
    @Excel(name = "编码")
    private String groupuid;
    // 名称
    @Excel(name = "名称")
    private String groupname;
    // 缩写
    @Excel(name = "缩写")
    private String abbreviate;
    // 组分类
    @Excel(name = "组分类")
    private String groupclass;
    // 联系人
    @Excel(name = "联系人")
    private String linkman;
    // 联系电话
    @Excel(name = "联系电话")
    private String telephone;
    // 传真
    @Excel(name = "传真")
    private String groupfax;
    // 地址
    @Excel(name = "地址")
    private String groupadd;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 有效期
    private Date invaliddate;
    // 组类别
    private String grouptype;
    // 信用日期单位
    @Excel(name = "信用日期单位")
    private String creditduint;
    // 信用日期数量
    @Excel(name = "信用日期数量")
    private Integer creditdquantity;
    // 信用金额单位
    @Excel(name = "信用金额单位")
    private String creditcuint;
    // 信用金额数量
    @Excel(name = "信用金额数量")
    private Integer creditcquantity;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 联系手机
    @Excel(name = "联系手机")
    private String mobile;
    // 次联系人
    @Excel(name = "次联系人")
    private String linkmans;
    // 次联系电话
    @Excel(name = "次联系电话")
    private String telephones;
    // 次联系手机
    @Excel(name = "次联系手机")
    private String mobiles;
    // 国家
    @Excel(name = "国家")
    private String country;
    // 省份
    @Excel(name = "省份")
    private String province;
    // 邮编
    @Excel(name = "邮编")
    private String groupzip;
    // 送货地址
    @Excel(name = "送货地址")
    private String deliveradd;
    // 发票地址
    @Excel(name = "发票地址")
    private String invoiceadd;
    // 业务员
    @Excel(name = "业务员")
    private String seller;
    // 厂商标签
    @Excel(name = "厂商标签")
    private String grouplabel;
    // 厂商等级
    @Excel(name = "厂商等级")
    private String grouplevel;
    // 厂商状态
    @Excel(name = "厂商状态")
    private String groupstate;
    // 厂商来源
    @Excel(name = "厂商来源")
    private String source;
    // 信用描述
    @Excel(name = "信用描述")
    private String credit;
    // 付款方式
    @Excel(name = "付款方式")
    private String paymentmethod;
    // 信用代码
    @Excel(name = "信用代码")
    private String creditcode;
    // 开户银行
    @Excel(name = "开户银行")
    private String depositbank;
    // 银行账号
    @Excel(name = "银行账号")
    private String bankaccount;
    // 有效
    private Integer enabledmark;
    // 删除标识
    private Integer deletemark;
    // 删除人员
    private String deletelister;
    // 删除人员id
    private String deletelisterid;
    // 删除日期
    private Date deletedate;
    // 结算科目id
    private String fmaccoid;
    // 预支科目id
    private String foreaccoid;
    // 销售订单结余额
    private Double busmachremamt;
    // 销售发货结余额
    private Double busdeliremamt;
    // 销售发票结余额
    private Double businvoremamt;
    // 销售结转期末额
    private Double busaccocloseamt;
    // 销售结转本期额
    private Double busacconowamt;
    // 采购订单结余额
    private Double buyorderremamt;
    // 采购收货结余额
    private Double buyfiniremamt;
    // 采购发票结余额
    private Double buyinvoremamt;
    // 采购结转期末额
    private Double buyaccocloseamt;
    // 采购结转本期额
    private Double buyacconowamt;
    // 地级市
    private String city;
    // 县
    private String county;
    // 街道
    private String street;
    // 地址
    private String localadd;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    private String tenantid;
    // 乐观锁
    private Integer revision;

    // 账单期末
    private Double accountamount;
    // 未结入账
    private Double freeinamount;
    // 未结出账
    private Double freeoutamount;
    // 未结金额
    private Double freeamount;
    // 合计金额
    private Double totalamount;
    //  逾期发票
    private Integer overdueinvoice;
    //  总计发票
    private Integer totalinvoice;

    // 发票结余
    private Double invoremamount;
    // 待开发票
    private Double salefreeamount;
    // 销售预收
    private Double deporemamount;
    // 应收总计
    private Double saletotalamount;

    // 结算科目id
    private String fmacconame;
    // 预支科目id
    private String foreacconame;
    // 结算科目id
    private String fmaccocode;
    // 预支科目id
    private String foreaccocode;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 分组id
    public String getWggroupid() {
        return wggroupid;
    }

    public void setWggroupid(String wggroupid) {
        this.wggroupid = wggroupid;
    }

    // 编码
    public String getGroupuid() {
        return groupuid;
    }

    public void setGroupuid(String groupuid) {
        this.groupuid = groupuid;
    }

    public String getLocaladd() {
        return localadd;
    }

    public void setLocaladd(String localadd) {
        this.localadd = localadd;
    }

    // 名称
    public String getGroupname() {
        return groupname;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }

    // 缩写
    public String getAbbreviate() {
        return abbreviate;
    }

    public void setAbbreviate(String abbreviate) {
        this.abbreviate = abbreviate;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    // 组分类
    public String getGroupclass() {
        return groupclass;
    }

    public void setGroupclass(String groupclass) {
        this.groupclass = groupclass;
    }

    // 联系人
    public String getLinkman() {
        return linkman;
    }

    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }

    // 联系电话
    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    // 传真
    public String getGroupfax() {
        return groupfax;
    }

    public void setGroupfax(String groupfax) {
        this.groupfax = groupfax;
    }

    // 地址
    public String getGroupadd() {
        return groupadd;
    }

    public void setGroupadd(String groupadd) {
        this.groupadd = groupadd;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 有效期
    public Date getInvaliddate() {
        return invaliddate;
    }

    public void setInvaliddate(Date invaliddate) {
        this.invaliddate = invaliddate;
    }

    // 组类别
    public String getGrouptype() {
        return grouptype;
    }

    public void setGrouptype(String grouptype) {
        this.grouptype = grouptype;
    }

    // 信用日期单位
    public String getCreditduint() {
        return creditduint;
    }

    public void setCreditduint(String creditduint) {
        this.creditduint = creditduint;
    }

    // 信用日期数量
    public Integer getCreditdquantity() {
        return creditdquantity;
    }

    public void setCreditdquantity(Integer creditdquantity) {
        this.creditdquantity = creditdquantity;
    }

    // 信用金额单位
    public String getCreditcuint() {
        return creditcuint;
    }

    public void setCreditcuint(String creditcuint) {
        this.creditcuint = creditcuint;
    }

    // 信用金额数量
    public Integer getCreditcquantity() {
        return creditcquantity;
    }

    public void setCreditcquantity(Integer creditcquantity) {
        this.creditcquantity = creditcquantity;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 联系手机
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    // 次联系人
    public String getLinkmans() {
        return linkmans;
    }

    public void setLinkmans(String linkmans) {
        this.linkmans = linkmans;
    }

    // 次联系电话
    public String getTelephones() {
        return telephones;
    }

    public void setTelephones(String telephones) {
        this.telephones = telephones;
    }

    // 次联系手机
    public String getMobiles() {
        return mobiles;
    }

    public void setMobiles(String mobiles) {
        this.mobiles = mobiles;
    }

    // 国家
    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    // 省份
    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    // 邮编
    public String getGroupzip() {
        return groupzip;
    }

    public void setGroupzip(String groupzip) {
        this.groupzip = groupzip;
    }

    // 送货地址
    public String getDeliveradd() {
        return deliveradd;
    }

    public void setDeliveradd(String deliveradd) {
        this.deliveradd = deliveradd;
    }

    // 发票地址
    public String getInvoiceadd() {
        return invoiceadd;
    }

    public void setInvoiceadd(String invoiceadd) {
        this.invoiceadd = invoiceadd;
    }

    // 业务员
    public String getSeller() {
        return seller;
    }

    public void setSeller(String seller) {
        this.seller = seller;
    }

    // 厂商标签
    public String getGrouplabel() {
        return grouplabel;
    }

    public void setGrouplabel(String grouplabel) {
        this.grouplabel = grouplabel;
    }

    // 厂商等级
    public String getGrouplevel() {
        return grouplevel;
    }

    public void setGrouplevel(String grouplevel) {
        this.grouplevel = grouplevel;
    }

    // 厂商状态
    public String getGroupstate() {
        return groupstate;
    }

    public void setGroupstate(String groupstate) {
        this.groupstate = groupstate;
    }

    // 厂商来源
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    // 信用描述
    public String getCredit() {
        return credit;
    }

    public void setCredit(String credit) {
        this.credit = credit;
    }

    // 付款方式
    public String getPaymentmethod() {
        return paymentmethod;
    }

    public void setPaymentmethod(String paymentmethod) {
        this.paymentmethod = paymentmethod;
    }

    // 信用代码
    public String getCreditcode() {
        return creditcode;
    }

    public void setCreditcode(String creditcode) {
        this.creditcode = creditcode;
    }

    // 开户银行
    public String getDepositbank() {
        return depositbank;
    }

    public void setDepositbank(String depositbank) {
        this.depositbank = depositbank;
    }

    // 银行账号
    public String getBankaccount() {
        return bankaccount;
    }

    public void setBankaccount(String bankaccount) {
        this.bankaccount = bankaccount;
    }

    // 有效
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 删除标识
    public Integer getDeletemark() {
        return deletemark;
    }

    public void setDeletemark(Integer deletemark) {
        this.deletemark = deletemark;
    }

    // 删除人员
    public String getDeletelister() {
        return deletelister;
    }

    public void setDeletelister(String deletelister) {
        this.deletelister = deletelister;
    }

    // 删除人员id
    public String getDeletelisterid() {
        return deletelisterid;
    }

    public void setDeletelisterid(String deletelisterid) {
        this.deletelisterid = deletelisterid;
    }

    // 删除日期
    public Date getDeletedate() {
        return deletedate;
    }

    public void setDeletedate(Date deletedate) {
        this.deletedate = deletedate;
    }

    public Double getBusmachremamt() {
        return busmachremamt;
    }

    public void setBusmachremamt(Double busmachremamt) {
        this.busmachremamt = busmachremamt;
    }

    public Double getBusdeliremamt() {
        return busdeliremamt;
    }

    public void setBusdeliremamt(Double busdeliremamt) {
        this.busdeliremamt = busdeliremamt;
    }

    public Double getBusinvoremamt() {
        return businvoremamt;
    }

    public void setBusinvoremamt(Double businvoremamt) {
        this.businvoremamt = businvoremamt;
    }

    public Double getBusaccocloseamt() {
        return busaccocloseamt;
    }

    public void setBusaccocloseamt(Double busaccocloseamt) {
        this.busaccocloseamt = busaccocloseamt;
    }

    public Double getBusacconowamt() {
        return busacconowamt;
    }

    public void setBusacconowamt(Double busacconowamt) {
        this.busacconowamt = busacconowamt;
    }

    public Double getBuyorderremamt() {
        return buyorderremamt;
    }

    public void setBuyorderremamt(Double buyorderremamt) {
        this.buyorderremamt = buyorderremamt;
    }

    public Double getBuyfiniremamt() {
        return buyfiniremamt;
    }

    public void setBuyfiniremamt(Double buyfiniremamt) {
        this.buyfiniremamt = buyfiniremamt;
    }

    public Double getBuyinvoremamt() {
        return buyinvoremamt;
    }

    public void setBuyinvoremamt(Double buyinvoremamt) {
        this.buyinvoremamt = buyinvoremamt;
    }

    public Double getBuyaccocloseamt() {
        return buyaccocloseamt;
    }

    public void setBuyaccocloseamt(Double buyaccocloseamt) {
        this.buyaccocloseamt = buyaccocloseamt;
    }

    public Double getBuyacconowamt() {
        return buyacconowamt;
    }

    public void setBuyacconowamt(Double buyacconowamt) {
        this.buyacconowamt = buyacconowamt;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public Double getAccountamount() {
        return accountamount;
    }

    public void setAccountamount(Double accountamount) {
        this.accountamount = accountamount;
    }

    public Double getFreeinamount() {
        return freeinamount;
    }

    public void setFreeinamount(Double freeinamount) {
        this.freeinamount = freeinamount;
    }

    public Double getFreeoutamount() {
        return freeoutamount;
    }

    public void setFreeoutamount(Double freeoutamount) {
        this.freeoutamount = freeoutamount;
    }

    public Double getFreeamount() {
        return freeamount;
    }

    public void setFreeamount(Double freeamount) {
        this.freeamount = freeamount;
    }

    public Double getTotalamount() {
        return totalamount;
    }

    public void setTotalamount(Double totalamount) {
        this.totalamount = totalamount;
    }

    public Integer getOverdueinvoice() {
        return overdueinvoice;
    }

    public void setOverdueinvoice(Integer overdueinvoice) {
        this.overdueinvoice = overdueinvoice;
    }

    public Integer getTotalinvoice() {
        return totalinvoice;
    }

    public void setTotalinvoice(Integer totalinvoice) {
        this.totalinvoice = totalinvoice;
    }

    public Double getInvoremamount() {
        return invoremamount;
    }

    public void setInvoremamount(Double invoremamount) {
        this.invoremamount = invoremamount;
    }

    public Double getSalefreeamount() {
        return salefreeamount;
    }

    public void setSalefreeamount(Double salefreeamount) {
        this.salefreeamount = salefreeamount;
    }

    public Double getDeporemamount() {
        return deporemamount;
    }

    public void setDeporemamount(Double deporemamount) {
        this.deporemamount = deporemamount;
    }

    public Double getSaletotalamount() {
        return saletotalamount;
    }

    public void setSaletotalamount(Double saletotalamount) {
        this.saletotalamount = saletotalamount;
    }

    public String getFmaccoid() {
        return fmaccoid;
    }

    public void setFmaccoid(String fmaccoid) {
        this.fmaccoid = fmaccoid;
    }

    public String getForeaccoid() {
        return foreaccoid;
    }

    public void setForeaccoid(String foreaccoid) {
        this.foreaccoid = foreaccoid;
    }

    public String getFmacconame() {
        return fmacconame;
    }

    public void setFmacconame(String fmacconame) {
        this.fmacconame = fmacconame;
    }

    public String getForeacconame() {
        return foreacconame;
    }

    public void setForeacconame(String foreacconame) {
        this.foreacconame = foreacconame;
    }

    public String getFmaccocode() {
        return fmaccocode;
    }

    public void setFmaccocode(String fmaccocode) {
        this.fmaccocode = fmaccocode;
    }

    public String getForeaccocode() {
        return foreaccocode;
    }

    public void setForeaccocode(String foreaccocode) {
        this.foreaccocode = foreaccocode;
    }
}

