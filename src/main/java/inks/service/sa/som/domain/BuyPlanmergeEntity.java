package inks.service.sa.som.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 合并项目(BuyPlanmerge)Entity
 *
 * <AUTHOR>
 * @since 2024-03-18 12:58:11
 */
public class BuyPlanmergeEntity implements Serializable {
    private static final long serialVersionUID = 927645797843542803L;
    // id
    private String id;
    // 计划单号
    private String pid;
    // 分类
    private String itemtype;
    // 商品id
    private String goodsid;
    // 产品编码
    private String itemcode;
    // 产品名称
    private String itemname;
    // 产品规格
    private String itemspec;
    // 产品单位
    private String itemunit;
    // 数量
    private Double quantity;
    // 含税单价
    private Double taxprice;
    // 含税金额
    private Double taxamount;
    // 税额
    private Double taxtotal;
    // 记录税率
    private Integer itemtaxrate;
    // 单价
    private Double price;
    // 金额
    private Double amount;
    // 计划完成
    private Date plandate;
    // 关联厂商
    private String groupid;
    // 备注
    private String remark;
    // 状态
    private String statecode;
    // 状态日期
    private Date statedate;
    // 关闭
    private Integer closed;
    // 采购数量
    private Double buyqty;
    // 收货数量
    private Double finishqty;
    // 行号
    private Integer rownum;
    // 引用号
    private String citeuid;
    // 引用ID
    private String citeitemid;
    // 销售单号
    private String machuid;
    // 销售子项id
    private String machitemid;
    // 销售客户id
    private String machgroupid;
    // 主计划号
    private String mainplanuid;
    // 主计划Itemid
    private String mainplanitemid;
    // Mrp单号
    private String mrpuid;
    // 客户
    private String customer;
    // 客户PO
    private String custpo;
    // 预设批号
    private String batchno;
    // 属性Josn
    private String attributejson;
    // 来源:0=其他
    private Integer sourcetype;
    // item行数
    private Integer itemcount;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 自定义6
    private String custom6;
    // 自定义7
    private String custom7;
    // 自定义8
    private String custom8;
    // 自定义9
    private String custom9;
    // 自定义10
    private String custom10;
    // 租户id
    private String tenantid;
    // 乐观锁
    private Integer revision;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 计划单号
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 分类
    public String getItemtype() {
        return itemtype;
    }

    public void setItemtype(String itemtype) {
        this.itemtype = itemtype;
    }

    // 商品id
    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    // 产品编码
    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }

    // 产品名称
    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    // 产品规格
    public String getItemspec() {
        return itemspec;
    }

    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }

    // 产品单位
    public String getItemunit() {
        return itemunit;
    }

    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }

    // 数量
    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    // 含税单价
    public Double getTaxprice() {
        return taxprice;
    }

    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }

    // 含税金额
    public Double getTaxamount() {
        return taxamount;
    }

    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }

    // 税额
    public Double getTaxtotal() {
        return taxtotal;
    }

    public void setTaxtotal(Double taxtotal) {
        this.taxtotal = taxtotal;
    }

    // 记录税率
    public Integer getItemtaxrate() {
        return itemtaxrate;
    }

    public void setItemtaxrate(Integer itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }

    // 单价
    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    // 金额
    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    // 计划完成
    public Date getPlandate() {
        return plandate;
    }

    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }

    // 关联厂商
    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 状态
    public String getStatecode() {
        return statecode;
    }

    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }

    // 状态日期
    public Date getStatedate() {
        return statedate;
    }

    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }

    // 关闭
    public Integer getClosed() {
        return closed;
    }

    public void setClosed(Integer closed) {
        this.closed = closed;
    }

    // 采购数量
    public Double getBuyqty() {
        return buyqty;
    }

    public void setBuyqty(Double buyqty) {
        this.buyqty = buyqty;
    }

    // 收货数量
    public Double getFinishqty() {
        return finishqty;
    }

    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 引用号
    public String getCiteuid() {
        return citeuid;
    }

    public void setCiteuid(String citeuid) {
        this.citeuid = citeuid;
    }

    // 引用ID
    public String getCiteitemid() {
        return citeitemid;
    }

    public void setCiteitemid(String citeitemid) {
        this.citeitemid = citeitemid;
    }

    // 销售单号
    public String getMachuid() {
        return machuid;
    }

    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }

    // 销售子项id
    public String getMachitemid() {
        return machitemid;
    }

    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }

    // 销售客户id
    public String getMachgroupid() {
        return machgroupid;
    }

    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }

    // 主计划号
    public String getMainplanuid() {
        return mainplanuid;
    }

    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }

    // 主计划Itemid
    public String getMainplanitemid() {
        return mainplanitemid;
    }

    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }

    // Mrp单号
    public String getMrpuid() {
        return mrpuid;
    }

    public void setMrpuid(String mrpuid) {
        this.mrpuid = mrpuid;
    }

    // 客户
    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    // 客户PO
    public String getCustpo() {
        return custpo;
    }

    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }

    // 预设批号
    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    // 属性Josn
    public String getAttributejson() {
        return attributejson;
    }

    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }

    // 来源:0=其他
    public Integer getSourcetype() {
        return sourcetype;
    }

    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }

    // item行数
    public Integer getItemcount() {
        return itemcount;
    }

    public void setItemcount(Integer itemcount) {
        this.itemcount = itemcount;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

