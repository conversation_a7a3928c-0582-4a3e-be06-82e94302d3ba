package inks.service.sa.som.domain.pojo;

// 货品批次管理
public class MatGoodsBatchAttrPojo {

    // 默认仓库
    private String storeid;
    // 受权仓库
    private String storelistname;
    // 受权仓库GUID
    private String storelistguid;
    // 批次管理
    private Integer batchmg;
    // 批次独立
    private Integer batchonly;
    // Sku库存
    private Integer skumark;
    // SN包装
    private Integer packsnmark;

    //必须设置过期ExpiMark
    private Integer expimark;
    //AgePrice 库存单价
    private double ageprice;




    public String getStoreid() {
        return storeid;
    }

    public void setStoreid(String storeid) {
        this.storeid = storeid;
    }

    public String getStorelistname() {
        return storelistname;
    }

    public void setStorelistname(String storelistname) {
        this.storelistname = storelistname;
    }

    public String getStorelistguid() {
        return storelistguid;
    }

    public void setStorelistguid(String storelistguid) {
        this.storelistguid = storelistguid;
    }

    public Integer getBatchmg() {
        return batchmg;
    }

    public void setBatchmg(Integer batchmg) {
        this.batchmg = batchmg;
    }

    public Integer getBatchonly() {
        return batchonly;
    }

    public void setBatchonly(Integer batchonly) {
        this.batchonly = batchonly;
    }

    public Integer getSkumark() {
        return skumark;
    }

    public void setSkumark(Integer skumark) {
        this.skumark = skumark;
    }

    public Integer getPacksnmark() {
        return packsnmark;
    }

    public void setPacksnmark(Integer packsnmark) {
        this.packsnmark = packsnmark;
    }

    public Integer getExpimark() {
        return expimark;
    }

    public void setExpimark(Integer expimark) {
        this.expimark = expimark;
    }

    public double getAgeprice() {
        return ageprice;
    }

    public void setAgeprice(double ageprice) {
        this.ageprice = ageprice;
    }
}
