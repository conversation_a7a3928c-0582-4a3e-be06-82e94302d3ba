package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.WorkgroupPojo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 往来核销(FmPayapply)实体类
 *
 * <AUTHOR>
 * @since 2022-07-13 21:29:59
 */
public class FmPayapplyPojo extends WorkgroupPojo implements Serializable {
    private static final long serialVersionUID = -83093263818406619L;
    // id
    @Excel(name = "id")
    private String id;
    // 编码
    @Excel(name = "编码")
    private String refno;
    // 单据类型
    @Excel(name = "单据类型")
    private String billtype;
    // 单据日期
    @Excel(name = "单据日期")
    private Date billdate;
    // 单据标题
    @Excel(name = "单据标题")
    private String billtitle;
    // 预付单位id
    @Excel(name = "预付单位id")
    private String paygroupid;
    // 应付单位id
    @Excel(name = "应付单位id")
    private String applygroupid;
    // 预付金额
    @Excel(name = "预付金额")
    private Double payamount;
    // 核销金额
    @Excel(name = "核销金额")
    private Double applied;
    // 经办人员
    @Excel(name = "经办人员")
    private String operator;
    // 摘要
    @Excel(name = "摘要")
    private String summary;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<FmPayapplyitemPojo> item;

    // 子表
    private List<FmPayapplycashPojo> cash;


    // 编码
    private String paygroupuid;
    // 名称
    private String paygroupname;
    // 缩写
    private String payabbreviate;

    // 编码
    private String applygroupuid;
    // 名称
    private String applygroupname;
    // 缩写
    private String applyabbreviate;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 编码
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    // 单据类型
    public String getBilltype() {
        return billtype;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }

    // 单据日期
    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }

    // 单据标题
    public String getBilltitle() {
        return billtitle;
    }

    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }

    // 预付单位id
    public String getPaygroupid() {
        return paygroupid;
    }

    public void setPaygroupid(String paygroupid) {
        this.paygroupid = paygroupid;
    }

    // 应付单位id
    public String getApplygroupid() {
        return applygroupid;
    }

    public void setApplygroupid(String applygroupid) {
        this.applygroupid = applygroupid;
    }

    // 预付金额
    public Double getPayamount() {
        return payamount;
    }

    public void setPayamount(Double payamount) {
        this.payamount = payamount;
    }

    // 核销金额
    public Double getApplied() {
        return applied;
    }

    public void setApplied(Double applied) {
        this.applied = applied;
    }

    // 经办人员
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    // 摘要
    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


    public List<FmPayapplyitemPojo> getItem() {
        return item;
    }

    public void setItem(List<FmPayapplyitemPojo> item) {
        this.item = item;
    }

    public List<FmPayapplycashPojo> getCash() {
        return cash;
    }

    public void setCash(List<FmPayapplycashPojo> cash) {
        this.cash = cash;
    }

    public String getPaygroupuid() {
        return paygroupuid;
    }

    public void setPaygroupuid(String paygroupuid) {
        this.paygroupuid = paygroupuid;
    }

    public String getPaygroupname() {
        return paygroupname;
    }

    public void setPaygroupname(String paygroupname) {
        this.paygroupname = paygroupname;
    }

    public String getPayabbreviate() {
        return payabbreviate;
    }

    public void setPayabbreviate(String payabbreviate) {
        this.payabbreviate = payabbreviate;
    }

    public String getApplygroupuid() {
        return applygroupuid;
    }

    public void setApplygroupuid(String applygroupuid) {
        this.applygroupuid = applygroupuid;
    }

    public String getApplygroupname() {
        return applygroupname;
    }

    public void setApplygroupname(String applygroupname) {
        this.applygroupname = applygroupname;
    }

    public String getApplyabbreviate() {
        return applyabbreviate;
    }

    public void setApplyabbreviate(String applyabbreviate) {
        this.applyabbreviate = applyabbreviate;
    }
}

