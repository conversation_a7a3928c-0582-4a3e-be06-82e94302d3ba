package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.GoodsPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 成本项目(BusOrdercostitem)Pojo
 *
 * <AUTHOR>
 * @since 2022-05-30 08:18:23
 */
public class BusOrdercostitemPojo extends GoodsPojo implements Serializable {
    private static final long serialVersionUID = 902510324092154651L;
    // ID
    @Excel(name = "ID")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 商品ID
    @Excel(name = "商品ID")
    private String goodsid;
    // 产品编码
    @Excel(name = "产品编码")
    private String itemcode;
    // 产品名称
    @Excel(name = "产品名称")
    private String itemname;
    // 产品规格
    @Excel(name = "产品规格")
    private String itemspec;
    // 产品单位
    @Excel(name = "产品单位")
    private String itemunit;
    // 数量
    @Excel(name = "数量")
    private Double quantity;
    // 标准销价
    @Excel(name = "标准销价")
    private Double stdprice;
    // 标准金额
    @Excel(name = "标准金额")
    private Double stdamount;
    // 折扣
    @Excel(name = "折扣")
    private Double rebate;
    // 二级折扣
    @Excel(name = "二级折扣")
    private Double rebatesec;
    // 未税单价
    @Excel(name = "未税单价")
    private Double price;
    // 未税金额
    @Excel(name = "未税金额")
    private Double amount;
    // 税率
    @Excel(name = "税率")
    private Integer itemtaxrate;
    // 含税单价
    @Excel(name = "含税单价")
    private Double taxprice;
    // 税额
    @Excel(name = "税额")
    private Double taxtotal;
    // 含税金额
    @Excel(name = "含税金额")
    private Double taxamount;
    // 计划交期
    @Excel(name = "计划交期")
    private Date plandate;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 作废
    @Excel(name = "作废")
    private Integer disannulmark;
    // 作废经办id
    @Excel(name = "作废经办id")
    private String disannullisterid;
    // 作废经办
    @Excel(name = "作废经办")
    private String disannullister;
    // 作废日期
    @Excel(name = "作废日期")
    private Date disannuldate;
    // 虚拟货品
    @Excel(name = "虚拟货品")
    private Integer virtualitem;
    // 属性Josn
    @Excel(name = "属性Josn")
    private String attributejson;
    // 成本Item
    @Excel(name = "成本Item")
    private String costitemjson;
    // 成本分类
    @Excel(name = "成本分类")
    private String costgroupjson;
    // 关闭
    @Excel(name = "关闭")
    private Integer closed;
    // 已转订单
    @Excel(name = "已转订单")
    private Integer machmark;
    // 项目1
    @Excel(name = "项目1")
    private String costitem1;
    // 项目2
    @Excel(name = "项目2")
    private String costitem2;
    // 项目3
    @Excel(name = "项目3")
    private String costitem3;
    // 项目4
    @Excel(name = "项目4")
    private String costitem4;
    // 项目5
    @Excel(name = "项目5")
    private String costitem5;
    // 项目6
    @Excel(name = "项目6")
    private String costitem6;
    // 项目7
    @Excel(name = "项目7")
    private String costitem7;
    // 项目8
    @Excel(name = "项目8")
    private String costitem8;
    // 项目9
    @Excel(name = "项目9")
    private String costitem9;
    // 项目10
    @Excel(name = "项目10")
    private String costitem10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 商品ID
    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    // 产品编码
    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }

    // 产品名称
    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    // 产品规格
    public String getItemspec() {
        return itemspec;
    }

    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }

    // 产品单位
    public String getItemunit() {
        return itemunit;
    }

    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }

    // 数量
    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    // 标准销价
    public Double getStdprice() {
        return stdprice;
    }

    public void setStdprice(Double stdprice) {
        this.stdprice = stdprice;
    }

    // 标准金额
    public Double getStdamount() {
        return stdamount;
    }

    public void setStdamount(Double stdamount) {
        this.stdamount = stdamount;
    }

    // 折扣
    public Double getRebate() {
        return rebate;
    }

    public void setRebate(Double rebate) {
        this.rebate = rebate;
    }

    // 二级折扣
    public Double getRebatesec() {
        return rebatesec;
    }

    public void setRebatesec(Double rebatesec) {
        this.rebatesec = rebatesec;
    }

    // 未税单价
    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    // 未税金额
    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    // 税率
    public Integer getItemtaxrate() {
        return itemtaxrate;
    }

    public void setItemtaxrate(Integer itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }

    // 含税单价
    public Double getTaxprice() {
        return taxprice;
    }

    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }

    // 税额
    public Double getTaxtotal() {
        return taxtotal;
    }

    public void setTaxtotal(Double taxtotal) {
        this.taxtotal = taxtotal;
    }

    // 含税金额
    public Double getTaxamount() {
        return taxamount;
    }

    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }

    // 计划交期
    public Date getPlandate() {
        return plandate;
    }

    public void setPlandate(Date plandate) {
        this.plandate = plandate;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 作废
    public Integer getDisannulmark() {
        return disannulmark;
    }

    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }

    // 作废经办id
    public String getDisannullisterid() {
        return disannullisterid;
    }

    public void setDisannullisterid(String disannullisterid) {
        this.disannullisterid = disannullisterid;
    }

    // 作废经办
    public String getDisannullister() {
        return disannullister;
    }

    public void setDisannullister(String disannullister) {
        this.disannullister = disannullister;
    }

    // 作废日期
    public Date getDisannuldate() {
        return disannuldate;
    }

    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }

    // 虚拟货品
    public Integer getVirtualitem() {
        return virtualitem;
    }

    public void setVirtualitem(Integer virtualitem) {
        this.virtualitem = virtualitem;
    }

    // 属性Josn
    public String getAttributejson() {
        return attributejson;
    }

    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }

    // 成本Item
    public String getCostitemjson() {
        return costitemjson;
    }

    public void setCostitemjson(String costitemjson) {
        this.costitemjson = costitemjson;
    }

    // 成本分类
    public String getCostgroupjson() {
        return costgroupjson;
    }

    public void setCostgroupjson(String costgroupjson) {
        this.costgroupjson = costgroupjson;
    }

    // 关闭
    public Integer getClosed() {
        return closed;
    }

    public void setClosed(Integer closed) {
        this.closed = closed;
    }

    // 已转订单
    public Integer getMachmark() {
        return machmark;
    }

    public void setMachmark(Integer machmark) {
        this.machmark = machmark;
    }

    // 项目1
    public String getCostitem1() {
        return costitem1;
    }

    public void setCostitem1(String costitem1) {
        this.costitem1 = costitem1;
    }

    // 项目2
    public String getCostitem2() {
        return costitem2;
    }

    public void setCostitem2(String costitem2) {
        this.costitem2 = costitem2;
    }

    // 项目3
    public String getCostitem3() {
        return costitem3;
    }

    public void setCostitem3(String costitem3) {
        this.costitem3 = costitem3;
    }

    // 项目4
    public String getCostitem4() {
        return costitem4;
    }

    public void setCostitem4(String costitem4) {
        this.costitem4 = costitem4;
    }

    // 项目5
    public String getCostitem5() {
        return costitem5;
    }

    public void setCostitem5(String costitem5) {
        this.costitem5 = costitem5;
    }

    // 项目6
    public String getCostitem6() {
        return costitem6;
    }

    public void setCostitem6(String costitem6) {
        this.costitem6 = costitem6;
    }

    // 项目7
    public String getCostitem7() {
        return costitem7;
    }

    public void setCostitem7(String costitem7) {
        this.costitem7 = costitem7;
    }

    // 项目8
    public String getCostitem8() {
        return costitem8;
    }

    public void setCostitem8(String costitem8) {
        this.costitem8 = costitem8;
    }

    // 项目9
    public String getCostitem9() {
        return costitem9;
    }

    public void setCostitem9(String costitem9) {
        this.costitem9 = costitem9;
    }

    // 项目10
    public String getCostitem10() {
        return costitem10;
    }

    public void setCostitem10(String costitem10) {
        this.costitem10 = costitem10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

