package inks.service.sa.som.domain.pojo;

import java.io.Serializable;
import java.util.HashSet;

/** 用于D05M11B1/pullMrpitem 前端传入Mrp计算参数:
 */

public class MrpCalculationPojo implements Serializable {
    private static final long serialVersionUID = -43928667041107379L;

    // 参与计算当前库存的仓库id集合
    private HashSet<String> storeids;

    // 当前库存
    private boolean mativqty;
    // 采购待入
    private boolean buyremqty;
    // 生产待入
    private boolean wkwsremqty;
    // 加工待入
    private boolean wkscremqty;
    // 订单待出
    private boolean busremqty;
    // Mrp待用
    private boolean mrpremqty;
    // 领料待出
    private boolean reqremqty;
    // 安全库存
    private boolean safestock;

    public HashSet<String> getStoreids() {
        return storeids;
    }

    public void setStoreids(HashSet<String> storeids) {
        this.storeids = storeids;
    }

    public boolean isMativqty() {
        return mativqty;
    }

    public void setMativqty(boolean mativqty) {
        this.mativqty = mativqty;
    }

    public boolean isBuyremqty() {
        return buyremqty;
    }

    public void setBuyremqty(boolean buyremqty) {
        this.buyremqty = buyremqty;
    }

    public boolean isWkwsremqty() {
        return wkwsremqty;
    }

    public void setWkwsremqty(boolean wkwsremqty) {
        this.wkwsremqty = wkwsremqty;
    }

    public boolean isWkscremqty() {
        return wkscremqty;
    }

    public void setWkscremqty(boolean wkscremqty) {
        this.wkscremqty = wkscremqty;
    }

    public boolean isBusremqty() {
        return busremqty;
    }

    public void setBusremqty(boolean busremqty) {
        this.busremqty = busremqty;
    }

    public boolean isMrpremqty() {
        return mrpremqty;
    }

    public void setMrpremqty(boolean mrpremqty) {
        this.mrpremqty = mrpremqty;
    }

    public boolean isReqremqty() {
        return reqremqty;
    }

    public void setReqremqty(boolean reqremqty) {
        this.reqremqty = reqremqty;
    }

    public boolean isSafestock() {
        return safestock;
    }

    public void setSafestock(boolean safestock) {
        this.safestock = safestock;
    }
}

