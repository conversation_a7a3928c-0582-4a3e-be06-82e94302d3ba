package inks.service.sa.som.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 费用报销单(FmExpenses)实体类
 *
 * <AUTHOR>
 * @since 2025-06-26 15:43:17
 */
@Data
public class FmExpensesPojo implements Serializable {
    private static final long serialVersionUID = -51281875874484631L;
     // 主键ID
     @Excel(name = "主键ID")
    private String id;
     // 流水号
     @Excel(name = "流水号")
    private String refno;
     // 单据日期
     @Excel(name = "单据日期")
    private Date billdate;
     // 类型
     @Excel(name = "类型")
    private String billtype;
     // 标题
     @Excel(name = "标题")
    private String billtitle;
     // 部门ID
     @Excel(name = "部门ID")
    private String deptid;
     // 部门名称
     @Excel(name = "部门名称")
    private String deptname;
     // 合计金额
     @Excel(name = "合计金额")
    private Double billamount;
     // 金额大写繁体
     @Excel(name = "金额大写繁体")
    private String amountupper;
     // 附件张数
     @Excel(name = "附件张数")
    private Integer attachcount;
     // item行数
     @Excel(name = "item行数")
    private Integer itemcount;
     // 审核员
     @Excel(name = "审核员")
    private String assessor;
     // 审核员id
     @Excel(name = "审核员id")
    private String assessorid;
     // 审核日期
     @Excel(name = "审核日期")
    private Date assessdate;
     // 排序码
     @Excel(name = "排序码")
    private Integer rownum;
     // 摘要
     @Excel(name = "摘要")
    private String summary;
     // 创建者
     @Excel(name = "创建者")
    private String createby;
     // 创建者ID
     @Excel(name = "创建者ID")
    private String createbyid;
     // 新建日期
     @Excel(name = "新建日期")
    private Date createdate;
     // 制表人
     @Excel(name = "制表人")
    private String lister;
     // 制表人ID
     @Excel(name = "制表人ID")
    private String listerid;
     // 修改日期
     @Excel(name = "修改日期")
    private Date modifydate;
     // 自定义1
     @Excel(name = "自定义1")
    private String custom1;
     // 自定义2
     @Excel(name = "自定义2")
    private String custom2;
     // 自定义3
     @Excel(name = "自定义3")
    private String custom3;
     // 自定义4
     @Excel(name = "自定义4")
    private String custom4;
     // 自定义5
     @Excel(name = "自定义5")
    private String custom5;
     // 自定义6
     @Excel(name = "自定义6")
    private String custom6;
     // 自定义7
     @Excel(name = "自定义7")
    private String custom7;
     // 自定义8
     @Excel(name = "自定义8")
    private String custom8;
     // 自定义9
     @Excel(name = "自定义9")
    private String custom9;
     // 自定义10
     @Excel(name = "自定义10")
    private String custom10;
     // 租户ID
     @Excel(name = "租户ID")
    private String tenantid;
     // 乐观锁
     @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<FmExpensesitemPojo> item;
    

}

