package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.GoodsPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 采购验收项目(BuyFinishingitem)Pojo
 *
 * <AUTHOR>
 * @since 2024-01-17 09:26:49
 */
public class BuyFinishingitemPojo extends GoodsPojo implements Serializable {
    private static final long serialVersionUID = -91209465675734696L;
     // id
  @Excel(name = "id")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 货品编码
  @Excel(name = "货品编码")    
  private String goodsid;
     // 产品编码
  @Excel(name = "产品编码")    
  private String itemcode;
     // 产品名称
  @Excel(name = "产品名称")    
  private String itemname;
     // 产品规格
  @Excel(name = "产品规格")    
  private String itemspec;
     // 产品单位
  @Excel(name = "产品单位")    
  private String itemunit;
     // 数量
  @Excel(name = "数量")    
  private Double quantity;
     // 含税单价
  @Excel(name = "含税单价")    
  private Double taxprice;
     // 含税金额
  @Excel(name = "含税金额")    
  private Double taxamount;
     // 未税单价
  @Excel(name = "未税单价")    
  private Double price;
     // 未税金额
  @Excel(name = "未税金额")    
  private Double amount;
     // 税额
  @Excel(name = "税额")    
  private Double taxtotal;
     // 记录税率
  @Excel(name = "记录税率")    
  private Integer itemtaxrate;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 订单编号
  @Excel(name = "订单编号")    
  private String orderno;
     // 订单流水号
  @Excel(name = "订单流水号")    
  private String orderuid;
     // 订单子项id
  @Excel(name = "订单子项id")    
  private String orderitemid;
     // 状态
  @Excel(name = "状态")    
  private String statecode;
     // 状态时间
  @Excel(name = "状态时间")    
  private Date statedate;
     // 检验单id
  @Excel(name = "检验单id")    
  private String inspectid;
     // 检验单号
  @Excel(name = "检验单号")    
  private String inspectuid;
     // 合格数量
  @Excel(name = "合格数量")    
  private Double passedqty;
     // 已入库
  @Excel(name = "已入库")    
  private Double finishqty;
     // 关闭
  @Excel(name = "关闭")    
  private Integer closed;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 发票数量
  @Excel(name = "发票数量")    
  private Double invoqty;
     // 发票关闭
  @Excel(name = "发票关闭")    
  private Integer invoclosed;
     // 虚拟货品
  @Excel(name = "虚拟货品")    
  private Integer virtualitem;
     // 客户
  @Excel(name = "客户")    
  private String customer;
     // 客户PO
  @Excel(name = "客户PO")    
  private String custpo;
     // 指定库位
  @Excel(name = "指定库位")    
  private String location;
     // 指定批号
  @Excel(name = "指定批号")    
  private String batchno;
     // 销售单号
  @Excel(name = "销售单号")    
  private String machuid;
     // 销售子项id
  @Excel(name = "销售子项id")    
  private String machitemid;
     // 订单批次
  @Excel(name = "订单批次")    
  private String machbatch;
     // 销售客户id
  @Excel(name = "销售客户id")    
  private String machgroupid;
     // 主计划号
  @Excel(name = "主计划号")    
  private String mainplanuid;
     // 主计划Itemid
  @Excel(name = "主计划Itemid")    
  private String mainplanitemid;
     // Mrp单号
  @Excel(name = "Mrp单号")    
  private String mrpuid;
     // Mrp子项id
  @Excel(name = "Mrp子项id")    
  private String mrpitemid;
     // MrpObjGoodsid
  @Excel(name = "MrpObjGoodsid")
  private String mrpobjgoodsid;
     // 已转销售
  @Excel(name = "已转销售")    
  private Double deliqty;
     // 作废
  @Excel(name = "作废")    
  private Integer disannulmark;
     // 作废经办id
  @Excel(name = "作废经办id")    
  private String disannullisterid;
     // 作废经办
  @Excel(name = "作废经办")    
  private String disannullister;
     // 作废日期
  @Excel(name = "作废日期")    
  private Date disannuldate;
     // 属性Josn
  @Excel(name = "属性Josn")    
  private String attributejson;
     // 来源:0=其他
  @Excel(name = "来源:0=其他")    
  private Integer sourcetype;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 自定义6
  @Excel(name = "自定义6")    
  private String custom6;
     // 自定义7
  @Excel(name = "自定义7")    
  private String custom7;
     // 自定义8
  @Excel(name = "自定义8")    
  private String custom8;
     // 自定义9
  @Excel(name = "自定义9")    
  private String custom9;
     // 自定义10
  @Excel(name = "自定义10")    
  private String custom10;
     // 自定义11
  @Excel(name = "自定义11")    
  private String custom11;
     // 自定义12
  @Excel(name = "自定义12")    
  private String custom12;
     // 自定义13
  @Excel(name = "自定义13")    
  private String custom13;
     // 自定义14
  @Excel(name = "自定义14")    
  private String custom14;
     // 自定义15
  @Excel(name = "自定义15")    
  private String custom15;
     // 自定义16
  @Excel(name = "自定义16")    
  private String custom16;
     // 自定义17
  @Excel(name = "自定义17")    
  private String custom17;
     // 自定义18
  @Excel(name = "自定义18")    
  private String custom18;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;

    //g2.GoodsName as MrpObjGoodsName,
    //g2.GoodsUid as MrpObjGoodsUid
    private String mrpobjgoodsname;
    private String mrpobjgoodsuid;
   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public String getMrpobjgoodsname() {
        return mrpobjgoodsname;
    }

    public void setMrpobjgoodsname(String mrpobjgoodsname) {
        this.mrpobjgoodsname = mrpobjgoodsname;
    }

    public String getMrpobjgoodsuid() {
        return mrpobjgoodsuid;
    }

    public void setMrpobjgoodsuid(String mrpobjgoodsuid) {
        this.mrpobjgoodsuid = mrpobjgoodsuid;
    }

    // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 货品编码
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
   // 产品编码
    public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
   // 产品名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
   // 产品规格
    public String getItemspec() {
        return itemspec;
    }
    
    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }
        
   // 产品单位
    public String getItemunit() {
        return itemunit;
    }
    
    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }
        
   // 数量
    public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
   // 含税单价
    public Double getTaxprice() {
        return taxprice;
    }
    
    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }
        
   // 含税金额
    public Double getTaxamount() {
        return taxamount;
    }
    
    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }
        
   // 未税单价
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
        
   // 未税金额
    public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
        
   // 税额
    public Double getTaxtotal() {
        return taxtotal;
    }
    
    public void setTaxtotal(Double taxtotal) {
        this.taxtotal = taxtotal;
    }
        
   // 记录税率
    public Integer getItemtaxrate() {
        return itemtaxrate;
    }
    
    public void setItemtaxrate(Integer itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 订单编号
    public String getOrderno() {
        return orderno;
    }
    
    public void setOrderno(String orderno) {
        this.orderno = orderno;
    }
        
   // 订单流水号
    public String getOrderuid() {
        return orderuid;
    }
    
    public void setOrderuid(String orderuid) {
        this.orderuid = orderuid;
    }
        
   // 订单子项id
    public String getOrderitemid() {
        return orderitemid;
    }
    
    public void setOrderitemid(String orderitemid) {
        this.orderitemid = orderitemid;
    }
        
   // 状态
    public String getStatecode() {
        return statecode;
    }
    
    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }
        
   // 状态时间
    public Date getStatedate() {
        return statedate;
    }
    
    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }
        
   // 检验单id
    public String getInspectid() {
        return inspectid;
    }
    
    public void setInspectid(String inspectid) {
        this.inspectid = inspectid;
    }
        
   // 检验单号
    public String getInspectuid() {
        return inspectuid;
    }
    
    public void setInspectuid(String inspectuid) {
        this.inspectuid = inspectuid;
    }
        
   // 合格数量
    public Double getPassedqty() {
        return passedqty;
    }
    
    public void setPassedqty(Double passedqty) {
        this.passedqty = passedqty;
    }
        
   // 已入库
    public Double getFinishqty() {
        return finishqty;
    }
    
    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }
        
   // 关闭
    public Integer getClosed() {
        return closed;
    }
    
    public void setClosed(Integer closed) {
        this.closed = closed;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 发票数量
    public Double getInvoqty() {
        return invoqty;
    }
    
    public void setInvoqty(Double invoqty) {
        this.invoqty = invoqty;
    }
        
   // 发票关闭
    public Integer getInvoclosed() {
        return invoclosed;
    }
    
    public void setInvoclosed(Integer invoclosed) {
        this.invoclosed = invoclosed;
    }
        
   // 虚拟货品
    public Integer getVirtualitem() {
        return virtualitem;
    }
    
    public void setVirtualitem(Integer virtualitem) {
        this.virtualitem = virtualitem;
    }
        
   // 客户
    public String getCustomer() {
        return customer;
    }
    
    public void setCustomer(String customer) {
        this.customer = customer;
    }
        
   // 客户PO
    public String getCustpo() {
        return custpo;
    }
    
    public void setCustpo(String custpo) {
        this.custpo = custpo;
    }
        
   // 指定库位
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
        
   // 指定批号
    public String getBatchno() {
        return batchno;
    }
    
    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }
        
   // 销售单号
    public String getMachuid() {
        return machuid;
    }
    
    public void setMachuid(String machuid) {
        this.machuid = machuid;
    }
        
   // 销售子项id
    public String getMachitemid() {
        return machitemid;
    }
    
    public void setMachitemid(String machitemid) {
        this.machitemid = machitemid;
    }
        
   // 订单批次
    public String getMachbatch() {
        return machbatch;
    }
    
    public void setMachbatch(String machbatch) {
        this.machbatch = machbatch;
    }
        
   // 销售客户id
    public String getMachgroupid() {
        return machgroupid;
    }
    
    public void setMachgroupid(String machgroupid) {
        this.machgroupid = machgroupid;
    }
        
   // 主计划号
    public String getMainplanuid() {
        return mainplanuid;
    }
    
    public void setMainplanuid(String mainplanuid) {
        this.mainplanuid = mainplanuid;
    }
        
   // 主计划Itemid
    public String getMainplanitemid() {
        return mainplanitemid;
    }
    
    public void setMainplanitemid(String mainplanitemid) {
        this.mainplanitemid = mainplanitemid;
    }
        
   // Mrp单号
    public String getMrpuid() {
        return mrpuid;
    }
    
    public void setMrpuid(String mrpuid) {
        this.mrpuid = mrpuid;
    }
        
   // Mrp子项id
    public String getMrpitemid() {
        return mrpitemid;
    }
    
    public void setMrpitemid(String mrpitemid) {
        this.mrpitemid = mrpitemid;
    }
        
   // MrpObjGoodsid
    public String getMrpobjgoodsid() {
        return mrpobjgoodsid;
    }

    public void setMrpobjgoodsid(String mrpobjgoodsid) {
        this.mrpobjgoodsid = mrpobjgoodsid;
    }

   // 已转销售
    public Double getDeliqty() {
        return deliqty;
    }
    
    public void setDeliqty(Double deliqty) {
        this.deliqty = deliqty;
    }
        
   // 作废
    public Integer getDisannulmark() {
        return disannulmark;
    }
    
    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }
        
   // 作废经办id
    public String getDisannullisterid() {
        return disannullisterid;
    }
    
    public void setDisannullisterid(String disannullisterid) {
        this.disannullisterid = disannullisterid;
    }
        
   // 作废经办
    public String getDisannullister() {
        return disannullister;
    }
    
    public void setDisannullister(String disannullister) {
        this.disannullister = disannullister;
    }
        
   // 作废日期
    public Date getDisannuldate() {
        return disannuldate;
    }
    
    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }
        
   // 属性Josn
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
   // 来源:0=其他
    public Integer getSourcetype() {
        return sourcetype;
    }
    
    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 自定义11
    public String getCustom11() {
        return custom11;
    }
    
    public void setCustom11(String custom11) {
        this.custom11 = custom11;
    }
        
   // 自定义12
    public String getCustom12() {
        return custom12;
    }
    
    public void setCustom12(String custom12) {
        this.custom12 = custom12;
    }
        
   // 自定义13
    public String getCustom13() {
        return custom13;
    }
    
    public void setCustom13(String custom13) {
        this.custom13 = custom13;
    }
        
   // 自定义14
    public String getCustom14() {
        return custom14;
    }
    
    public void setCustom14(String custom14) {
        this.custom14 = custom14;
    }
        
   // 自定义15
    public String getCustom15() {
        return custom15;
    }
    
    public void setCustom15(String custom15) {
        this.custom15 = custom15;
    }
        
   // 自定义16
    public String getCustom16() {
        return custom16;
    }
    
    public void setCustom16(String custom16) {
        this.custom16 = custom16;
    }
        
   // 自定义17
    public String getCustom17() {
        return custom17;
    }
    
    public void setCustom17(String custom17) {
        this.custom17 = custom17;
    }
        
   // 自定义18
    public String getCustom18() {
        return custom18;
    }
    
    public void setCustom18(String custom18) {
        this.custom18 = custom18;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

