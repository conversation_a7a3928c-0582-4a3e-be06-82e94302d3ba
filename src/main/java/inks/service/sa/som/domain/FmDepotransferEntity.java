package inks.service.sa.som.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 预收核转(FmDepotransfer)实体类
 *
 * <AUTHOR>
 * @since 2025-05-14 17:21:14
 */
@Data
public class FmDepotransferEntity implements Serializable {
    private static final long serialVersionUID = -97071078261630089L;
     // id
    private String id;
     // 编码
    private String refno;
     // 单据类型
    private String billtype;
     // 单据日期
    private Date billdate;
     // 单据标题
    private String billtitle;
     // 客户id
    private String groupid;
     // 预付金额
    private Double payamount;
     // 核销金额
    private Double applied;
     // 经办人员
    private String operator;
     // 摘要
    private String summary;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;


}

