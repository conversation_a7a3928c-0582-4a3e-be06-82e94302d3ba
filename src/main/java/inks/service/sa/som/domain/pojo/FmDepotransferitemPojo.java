package inks.service.sa.som.domain.pojo;

import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.WorkgroupPojo;
import lombok.Data;

/**
 * 预收核转Item，即预收单(FmDepotransferitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-05-14 17:21:35
 */
@Data
public class FmDepotransferitemPojo  extends WorkgroupPojo implements Serializable {
    private static final long serialVersionUID = -52785902812834701L;
     // id
  @Excel(name = "id")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 预收单id
  @Excel(name = "预收单id")    
  private String paybillid;
     // 预收单单号(备查)
  @Excel(name = "预收单单号(备查)")    
  private String payrefno;
     // 预收单客户id
  @Excel(name = "预收单客户id")    
  private String paygroupid;
     // 预收单金额
  @Excel(name = "预收单金额")    
  private Double payamount;
     // 转入金额
  @Excel(name = "转入金额")    
  private Double amount;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 自定义6
  @Excel(name = "自定义6")    
  private String custom6;
     // 自定义7
  @Excel(name = "自定义7")    
  private String custom7;
     // 自定义8
  @Excel(name = "自定义8")    
  private String custom8;
     // 自定义9
  @Excel(name = "自定义9")    
  private String custom9;
     // 自定义10
  @Excel(name = "自定义10")    
  private String custom10;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;


}

