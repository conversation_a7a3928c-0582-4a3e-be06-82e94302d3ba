package inks.service.sa.som.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;
import lombok.Data;

/**
 * 费用报销单明细表(FmExpensesitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-06-26 15:43:25
 */
@Data
public class FmExpensesitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = 747662928320865570L;
     // 主键ID
  @Excel(name = "主键ID")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 费用发生日期
  @Excel(name = "费用发生日期")    
  private Date expedate;
     // 报销项目
  @Excel(name = "报销项目")    
  private String expeitem;
     // 费用类别
  @Excel(name = "费用类别")    
  private String expetype;
     // 金额
  @Excel(name = "金额")    
  private Double amount;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 创建者
  @Excel(name = "创建者")    
  private String createby;
     // 创建者ID
  @Excel(name = "创建者ID")    
  private String createbyid;
     // 新建日期
  @Excel(name = "新建日期")    
  private Date createdate;
     // 制表人
  @Excel(name = "制表人")    
  private String lister;
     // 制表人ID
  @Excel(name = "制表人ID")    
  private String listerid;
     // 修改日期
  @Excel(name = "修改日期")    
  private Date modifydate;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 自定义6
  @Excel(name = "自定义6")    
  private String custom6;
     // 自定义7
  @Excel(name = "自定义7")    
  private String custom7;
     // 自定义8
  @Excel(name = "自定义8")    
  private String custom8;
     // 自定义9
  @Excel(name = "自定义9")    
  private String custom9;
     // 自定义10
  @Excel(name = "自定义10")    
  private String custom10;
     // 租户ID
  @Excel(name = "租户ID")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;
    //PiDept.DeptName
    @Excel(name = "部门名称")
    private String deptname;


}

