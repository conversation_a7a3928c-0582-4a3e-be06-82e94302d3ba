package inks.service.sa.som.domain.constant;

public class MyConstant {

    //开始异步10线程获取订单生产成本预算
    public static final String ASYNC_MACHPRICE_STATE = "async_machprice_state";

    // 开始异步:获取MRP运算详细信息
    public static final String ASYNC_MRP_PULLITEMLIST_STATE = "async_mrpPullItemList_state:";
    public static final String ASYNC_MRP_PULLITEMLIST_RESULT = "async_mrpPullItemList_result:";

    //销售结转redis状态key
    public static final String BUSBATCHCREATE_CODE = "busbatchcreate_codes:";

    public static final String PAGELISTBYSALE_CODE = "pagelistbysale_code:";
    //"receipt_pages:"
    public static final String RECEIPT_PAGES = "receipt_pages:";
    //采购结转redis状态key
    public static final String BUYBATCHCREATE_CODE = "buybatchcreate_codes:";

    public static final String PRINTBATCH_STATE = "printbatch_codes:";
    //仓库结转redis状态key
    public static final String STOREBATCHCREATE_CODE = "storebatchcreate_codes:";

    //开始异步10线程获取订单生产成本预算
    public static final String ASYNC_PLAN_PRICE_FROMORDER_STATE = "async_planprice_fromorder_state:";


    // 开始异步:获取MRP运算详细信息 传mrp主表id
    public static final String ASYNC_MRP_PULLMRPITEMBYMRPID_STATE = "async_mrp_pullMrpitemByMrpid_state:";

    // Mrp相关的Redis锁
    public static final String MRP_LOCK = "mrp_lock:";
}
