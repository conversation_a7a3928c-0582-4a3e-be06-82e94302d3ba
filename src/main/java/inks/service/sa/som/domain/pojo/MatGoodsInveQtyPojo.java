package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.List;

/**
 * 库存信息(MatInventory)实体类
 *
 * <AUTHOR>
 * @since 2021-12-12 14:23:42
 */
public class MatGoodsInveQtyPojo implements Serializable {
    private static final long serialVersionUID = 766111558268133938L;

    private String id;
    // 货品编码
    @Excel(name = "货品编码")
    private String goodsuid;
    // 名称
    @Excel(name = "名称")
    private String goodsname;
    // 规格
    @Excel(name = "规格")
    private String goodsspec;
    // 货品单位
    @Excel(name = "货品单位")
    private String goodsunit;
    // 货品材质
    private String goodsmaterial;
    // 商品数量
    @Excel(name = "数量")
    private Double quantity;
    // 外部编码
    @Excel(name = "外部编码")
    private String partid;
    // 采购待入
    @Excel(name = "采购待入")
    private Double buyremqty;
    // 生产待入
    @Excel(name = "生产待入")
    private Double wkwsremqty;
    // 加工待入
    @Excel(name = "加工待入")
    private Double wkscremqty;
    // 订单待出
    @Excel(name = "订单待出")
    private Double busremqty;
    // Mrp待用
    @Excel(name = "Mrp待用")
    private Double mrpremqty;
    // 独立待领
    @Excel(name = "待领")
    private Double requremqty;
    // 库存数量
    @Excel(name = "账面库存")
    private Double stoqty;
    // 包装数量（辅助系数）PackQty
    @Excel(name = "包装数量")
    private Double packqty;
    // 客户名
    @Excel(name = "客户名")
    private String groupname;

    private List<MatInventoryPojo> item;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGoodsuid() {
        return goodsuid;
    }


    public void setGoodsuid(String goodsuid) {
        this.goodsuid = goodsuid;
    }


    public String getGoodsname() {
        return goodsname;
    }


    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }


    public String getGoodsspec() {
        return goodsspec;
    }


    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }


    public String getGoodsunit() {
        return goodsunit;
    }


    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }

    public String getGoodsmaterial() {
        return goodsmaterial;
    }

    public void setGoodsmaterial(String goodsmaterial) {
        this.goodsmaterial = goodsmaterial;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public String getPartid() {
        return partid;
    }

    public void setPartid(String partid) {
        this.partid = partid;
    }

    public String getGroupname() {
        return groupname;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }

    public Double getBuyremqty() {
        return buyremqty;
    }

    public void setBuyremqty(Double buyremqty) {
        this.buyremqty = buyremqty;
    }

    public Double getWkwsremqty() {
        return wkwsremqty;
    }

    public void setWkwsremqty(Double wkwsremqty) {
        this.wkwsremqty = wkwsremqty;
    }

    public Double getWkscremqty() {
        return wkscremqty;
    }

    public void setWkscremqty(Double wkscremqty) {
        this.wkscremqty = wkscremqty;
    }

    public Double getBusremqty() {
        return busremqty;
    }

    public void setBusremqty(Double busremqty) {
        this.busremqty = busremqty;
    }

    public Double getMrpremqty() {
        return mrpremqty;
    }

    public void setMrpremqty(Double mrpremqty) {
        this.mrpremqty = mrpremqty;
    }

    public Double getRequremqty() {
        return requremqty;
    }

    public void setRequremqty(Double requremqty) {
        this.requremqty = requremqty;
    }

    public Double getStoqty() {
        return stoqty;
    }

    public void setStoqty(Double stoqty) {
        this.stoqty = stoqty;
    }


    public List<MatInventoryPojo> getItem() {
        return item;
    }


    public void setItem(List<MatInventoryPojo> item) {
        this.item = item;
    }

    public Double getPackqty() {
        return packqty;
    }

    public void setPackqty(Double packqty) {
        this.packqty = packqty;
    }
}

