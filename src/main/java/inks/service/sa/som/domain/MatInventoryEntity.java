package inks.service.sa.som.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 库存信息(MatInventory)实体类
 *
 * <AUTHOR>
 * @since 2022-05-12 14:53:44
 */
public class MatInventoryEntity implements Serializable {
    private static final long serialVersionUID = -45573910950833915L;
         // id
         private String id;
         // 仓库ID
         private String storeid;
         // 商品ID
         private String goodsid;
         // 商品数量
         private Double quantity;
         // 金额
         private Double amount;
         // 最后单据
         private String enduid;
         // 末次入库
         private String endinuid;
         // 入库时间
         private Date endindate;
         // 末次出库
         private String endoutuid;
         // 出库时间
         private Date endoutdate;
         // 批号
         private String batchno;
         // 库位
         private String location;
         // 最小包装SN
         private String packsn;
         // skuid
         private String skuid;
         // 属性Josn
         private String attributejson;
         // 有效期
         private Date expidate;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 制表id
         private String listerid;
         // 修改日期
         private Date modifydate;
         // 自定义1
         private String custom1;
         // 自定义2
         private String custom2;
         // 自定义3
         private String custom3;
         // 自定义4
         private String custom4;
         // 自定义5
         private String custom5;
         // 自定义6
         private String custom6;
         // 自定义7
         private String custom7;
         // 自定义8
         private String custom8;
         // 自定义9
         private String custom9;
         // 自定义10
         private String custom10;
         // 租户id
         private String tenantid;
         // 乐观锁
         private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 仓库ID
    public String getStoreid() {
        return storeid;
    }
    
    public void setStoreid(String storeid) {
        this.storeid = storeid;
    }
        
// 商品ID
    public String getGoodsid() {
        return goodsid;
    }
    
    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }
        
// 商品数量
    public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
// 金额
    public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
        
// 最后单据
    public String getEnduid() {
        return enduid;
    }
    
    public void setEnduid(String enduid) {
        this.enduid = enduid;
    }
        
// 末次入库
    public String getEndinuid() {
        return endinuid;
    }
    
    public void setEndinuid(String endinuid) {
        this.endinuid = endinuid;
    }
        
// 入库时间
    public Date getEndindate() {
        return endindate;
    }
    
    public void setEndindate(Date endindate) {
        this.endindate = endindate;
    }
        
// 末次出库
    public String getEndoutuid() {
        return endoutuid;
    }
    
    public void setEndoutuid(String endoutuid) {
        this.endoutuid = endoutuid;
    }
        
// 出库时间
    public Date getEndoutdate() {
        return endoutdate;
    }
    
    public void setEndoutdate(Date endoutdate) {
        this.endoutdate = endoutdate;
    }
        
// 批号
    public String getBatchno() {
        return batchno;
    }
    
    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }
        
// 库位
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
        
// 最小包装SN
    public String getPacksn() {
        return packsn;
    }
    
    public void setPacksn(String packsn) {
        this.packsn = packsn;
    }
        
// skuid
    public String getSkuid() {
        return skuid;
    }
    
    public void setSkuid(String skuid) {
        this.skuid = skuid;
    }
        
// 属性Josn
    public String getAttributejson() {
        return attributejson;
    }
    
    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }
        
// 有效期
    public Date getExpidate() {
        return expidate;
    }
    
    public void setExpidate(Date expidate) {
        this.expidate = expidate;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
// 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
// 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
// 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
// 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

