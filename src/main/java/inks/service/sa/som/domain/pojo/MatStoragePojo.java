package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 仓库管理(MatStorage)实体类
 *
 * <AUTHOR>
 * @since 2023-06-29 12:47:43
 */
public class MatStoragePojo implements Serializable {
    private static final long serialVersionUID = 408324695812615863L;
         // id
         @Excel(name = "id") 
    private String id;
         // 仓库编码
         @Excel(name = "仓库编码") 
    private String storecode;
         // 仓库名称
         @Excel(name = "仓库名称") 
    private String storename;
         // 地址
         @Excel(name = "地址") 
    private String storeadd;
         // 仓管员
         @Excel(name = "仓管员") 
    private String operator;
         // 电话
         @Excel(name = "电话") 
    private String storetel;
         // 备注
         @Excel(name = "备注") 
    private String remark;
         // 排序
         @Excel(name = "排序") 
    private Integer rownum;
         // 允许编编
         @Excel(name = "允许编编") 
    private Integer allowedit;
         // 允许删除
         @Excel(name = "允许删除") 
    private Integer allowdelete;
         // 有效标识
         @Excel(name = "有效标识") 
    private Integer enabledmark;
         // 创建者
         @Excel(name = "创建者") 
    private String createby;
         // 创建者id
         @Excel(name = "创建者id") 
    private String createbyid;
         // 新建日期
         @Excel(name = "新建日期") 
    private Date createdate;
         // 制表
         @Excel(name = "制表") 
    private String lister;
         // 制表id
         @Excel(name = "制表id") 
    private String listerid;
         // 修改日期
         @Excel(name = "修改日期") 
    private Date modifydate;
         // 删除标识
         @Excel(name = "删除标识") 
    private Integer deletemark;
         // 删除经办
         @Excel(name = "删除经办") 
    private String deletelister;
         // 删除经办id
         @Excel(name = "删除经办id") 
    private String deletelisterid;
         // 删除日期
         @Excel(name = "删除日期") 
    private Date deletedate;
         // 产成品仓
         @Excel(name = "产成品仓") 
    private Integer machmark;
         // 0存货仓1线边仓2非存货仓
         @Excel(name = "0存货仓1线边仓2非存货仓") 
    private Integer storetype;
         // 1可用量计算
         @Excel(name = "1可用量计算") 
    private Integer usablemark;
    // DMS标识
    private Integer dmsmark;
    // SCM标识
    private Integer scmmark;
         // 自定义1
         @Excel(name = "自定义1") 
    private String custom1;
         // 自定义2
         @Excel(name = "自定义2") 
    private String custom2;
         // 自定义3
         @Excel(name = "自定义3") 
    private String custom3;
         // 自定义4
         @Excel(name = "自定义4") 
    private String custom4;
         // 自定义5
         @Excel(name = "自定义5") 
    private String custom5;
         // 自定义6
         @Excel(name = "自定义6") 
    private String custom6;
         // 自定义7
         @Excel(name = "自定义7") 
    private String custom7;
         // 自定义8
         @Excel(name = "自定义8") 
    private String custom8;
         // 自定义9
         @Excel(name = "自定义9") 
    private String custom9;
         // 自定义10
         @Excel(name = "自定义10") 
    private String custom10;
         // 租户id
         @Excel(name = "租户id") 
    private String tenantid;
         // 乐观锁
         @Excel(name = "乐观锁") 
    private Integer revision;



    // -----3个项目id-----
    @Excel(name = "项目id")
    private String projectid;
    // 项目编码
    @Excel(name = "项目编码")
    private String projcode;
    // 项目名称
    @Excel(name = "项目名称")
    private String projname;

    public String getProjectid() {
        return projectid;
    }
    public void setProjectid(String projectid) {
        this.projectid = projectid;
    }
    public String getProjcode() {
        return projcode;
    }
    public void setProjcode(String projcode) {
        this.projcode = projcode;
    }
    public String getProjname() {
        return projname;
    }
    public void setProjname(String projname) {
        this.projname = projname;
    }

     // id
       public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
     // 仓库编码
       public String getStorecode() {
        return storecode;
    }
    
    public void setStorecode(String storecode) {
        this.storecode = storecode;
    }
        
     // 仓库名称
       public String getStorename() {
        return storename;
    }
    
    public void setStorename(String storename) {
        this.storename = storename;
    }
        
     // 地址
       public String getStoreadd() {
        return storeadd;
    }
    
    public void setStoreadd(String storeadd) {
        this.storeadd = storeadd;
    }
        
     // 仓管员
       public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
        
     // 电话
       public String getStoretel() {
        return storetel;
    }
    
    public void setStoretel(String storetel) {
        this.storetel = storetel;
    }

    public Integer getDmsmark() {
        return dmsmark;
    }

    public void setDmsmark(Integer dmsmark) {
        this.dmsmark = dmsmark;
    }

    public Integer getScmmark() {
        return scmmark;
    }

    public void setScmmark(Integer scmmark) {
        this.scmmark = scmmark;
    }

    // 备注
       public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
     // 排序
       public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
     // 允许编编
       public Integer getAllowedit() {
        return allowedit;
    }
    
    public void setAllowedit(Integer allowedit) {
        this.allowedit = allowedit;
    }
        
     // 允许删除
       public Integer getAllowdelete() {
        return allowdelete;
    }
    
    public void setAllowdelete(Integer allowdelete) {
        this.allowdelete = allowdelete;
    }
        
     // 有效标识
       public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
     // 创建者
       public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
     // 创建者id
       public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
     // 新建日期
       public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
     // 制表
       public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
     // 制表id
       public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
     // 修改日期
       public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
     // 删除标识
       public Integer getDeletemark() {
        return deletemark;
    }
    
    public void setDeletemark(Integer deletemark) {
        this.deletemark = deletemark;
    }
        
     // 删除经办
       public String getDeletelister() {
        return deletelister;
    }
    
    public void setDeletelister(String deletelister) {
        this.deletelister = deletelister;
    }
        
     // 删除经办id
       public String getDeletelisterid() {
        return deletelisterid;
    }
    
    public void setDeletelisterid(String deletelisterid) {
        this.deletelisterid = deletelisterid;
    }
        
     // 删除日期
       public Date getDeletedate() {
        return deletedate;
    }
    
    public void setDeletedate(Date deletedate) {
        this.deletedate = deletedate;
    }
        
     // 产成品仓
       public Integer getMachmark() {
        return machmark;
    }
    
    public void setMachmark(Integer machmark) {
        this.machmark = machmark;
    }
        
     // 0存货仓1线边仓2非存货仓
       public Integer getStoretype() {
        return storetype;
    }
    
    public void setStoretype(Integer storetype) {
        this.storetype = storetype;
    }
        
     // 1可用量计算
       public Integer getUsablemark() {
        return usablemark;
    }
    
    public void setUsablemark(Integer usablemark) {
        this.usablemark = usablemark;
    }
        
     // 自定义1
       public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
     // 自定义2
       public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
     // 自定义3
       public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
     // 自定义4
       public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
     // 自定义5
       public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
     // 自定义6
       public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
     // 自定义7
       public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
     // 自定义8
       public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
     // 自定义9
       public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
     // 自定义10
       public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
     // 租户id
       public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
     // 乐观锁
       public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

