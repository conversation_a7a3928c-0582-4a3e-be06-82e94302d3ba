package inks.service.sa.som.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 销售订单(BusMachining)实体类
 *
 * <AUTHOR>
 * @since 2025-05-22 16:12:56
 */
public class BusMachiningEntity implements Serializable {
    private static final long serialVersionUID = -65974809700455620L;
     // ID
    private String id;
     // 编码
    private String refno;
     // 单据类型
    private String billtype;
     // 单据标题
    private String billtitle;
     // 单据日期
    private Date billdate;
     // 项目id
    private String projectid;
     // 项目编码
    private String projcode;
     // 项目名称
    private String projname;
     // 客户ID
    private String groupid;
     // 客户订单号
    private String custorderid;
     // 交货方式
    private String logisticsmode;
     // 交货港口
    private String logisticsport;
     // 出口国家
    private String country;
     // 预收款
    private Double advaamount;
     // 业务员
    private String salesman;
     // 业务员id
    private String salesmanid;
     // 税率
    private Integer taxrate;
     // 摘要
    private String summary;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 含税金额
    private Double billtaxamount;
     // 税额
    private Double billtaxtotal;
     // 未税金额
    private Double billamount;
     // 单据状态
    private String billstatecode;
     // 状态日期
    private Date billstatedate;
     // 单据计划
    private Date billplandate;
     // 最新工序id
    private String billwkwpid;
     // 最新工序编码
    private String billwkwpcode;
     // 最新工序名称
    private String billwkwpname;
     // 分组编码
    private String groupcode;
     // item行数
    private Integer itemcount;
     // 拣货行数
    private Integer pickcount;
     // 完成行数
    private Integer finishcount;
     // 作废行数
    private Integer disannulcount;
     // 打印次数
    private Integer printcount;
     // 生产行数
    private Integer wkitemcount;
     // 生产完成
    private Integer wkfinishcount;
     // 已转Wip行数
    private Integer wkwipcount;
     // 结款方式
    private String payment;
     // 正在进行OA
    private Integer oaflowmark;
     // 附件记数
    private Integer billattacount;
     // 成本预算
    private Double billcostbudgetamt;
     // 预收款
    private Double firstamt;
     // 收款额
    private Double lastamt;
     // 开票款
    private Double invoamt;
     // 发票行数
    private Integer invocount;
     // 币种id
    private Integer moneyid;
     // 币种名字
    private String moneyname;
     // 主计划行数
    private Integer mainplancount;
     // 优惠金额
    private Double billsubtotal;
     // 优惠券
    private String couponids;
     // 优惠券名称
    private String couponnames;
     // 成交含税金额
    private Double finishtaxamount;
     // 成交金额
    private Double finishamount;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 部门id
    private String deptid;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

   // ID
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 编码
    public String getRefno() {
        return refno;
    }
    
    public void setRefno(String refno) {
        this.refno = refno;
    }
        
   // 单据类型
    public String getBilltype() {
        return billtype;
    }
    
    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
        
   // 单据标题
    public String getBilltitle() {
        return billtitle;
    }
    
    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
        
   // 单据日期
    public Date getBilldate() {
        return billdate;
    }
    
    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
        
   // 项目id
    public String getProjectid() {
        return projectid;
    }
    
    public void setProjectid(String projectid) {
        this.projectid = projectid;
    }
        
   // 项目编码
    public String getProjcode() {
        return projcode;
    }
    
    public void setProjcode(String projcode) {
        this.projcode = projcode;
    }
        
   // 项目名称
    public String getProjname() {
        return projname;
    }
    
    public void setProjname(String projname) {
        this.projname = projname;
    }
        
   // 客户ID
    public String getGroupid() {
        return groupid;
    }
    
    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }
        
   // 客户订单号
    public String getCustorderid() {
        return custorderid;
    }
    
    public void setCustorderid(String custorderid) {
        this.custorderid = custorderid;
    }
        
   // 交货方式
    public String getLogisticsmode() {
        return logisticsmode;
    }
    
    public void setLogisticsmode(String logisticsmode) {
        this.logisticsmode = logisticsmode;
    }
        
   // 交货港口
    public String getLogisticsport() {
        return logisticsport;
    }
    
    public void setLogisticsport(String logisticsport) {
        this.logisticsport = logisticsport;
    }
        
   // 出口国家
    public String getCountry() {
        return country;
    }
    
    public void setCountry(String country) {
        this.country = country;
    }
        
   // 预收款
    public Double getAdvaamount() {
        return advaamount;
    }
    
    public void setAdvaamount(Double advaamount) {
        this.advaamount = advaamount;
    }
        
   // 业务员
    public String getSalesman() {
        return salesman;
    }
    
    public void setSalesman(String salesman) {
        this.salesman = salesman;
    }
        
   // 业务员id
    public String getSalesmanid() {
        return salesmanid;
    }
    
    public void setSalesmanid(String salesmanid) {
        this.salesmanid = salesmanid;
    }
        
   // 税率
    public Integer getTaxrate() {
        return taxrate;
    }
    
    public void setTaxrate(Integer taxrate) {
        this.taxrate = taxrate;
    }
        
   // 摘要
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 审核员
    public String getAssessor() {
        return assessor;
    }
    
    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }
        
   // 审核员id
    public String getAssessorid() {
        return assessorid;
    }
    
    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }
        
   // 审核日期
    public Date getAssessdate() {
        return assessdate;
    }
    
    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }
        
   // 含税金额
    public Double getBilltaxamount() {
        return billtaxamount;
    }
    
    public void setBilltaxamount(Double billtaxamount) {
        this.billtaxamount = billtaxamount;
    }
        
   // 税额
    public Double getBilltaxtotal() {
        return billtaxtotal;
    }
    
    public void setBilltaxtotal(Double billtaxtotal) {
        this.billtaxtotal = billtaxtotal;
    }
        
   // 未税金额
    public Double getBillamount() {
        return billamount;
    }
    
    public void setBillamount(Double billamount) {
        this.billamount = billamount;
    }
        
   // 单据状态
    public String getBillstatecode() {
        return billstatecode;
    }
    
    public void setBillstatecode(String billstatecode) {
        this.billstatecode = billstatecode;
    }
        
   // 状态日期
    public Date getBillstatedate() {
        return billstatedate;
    }
    
    public void setBillstatedate(Date billstatedate) {
        this.billstatedate = billstatedate;
    }
        
   // 单据计划
    public Date getBillplandate() {
        return billplandate;
    }
    
    public void setBillplandate(Date billplandate) {
        this.billplandate = billplandate;
    }
        
   // 最新工序id
    public String getBillwkwpid() {
        return billwkwpid;
    }
    
    public void setBillwkwpid(String billwkwpid) {
        this.billwkwpid = billwkwpid;
    }
        
   // 最新工序编码
    public String getBillwkwpcode() {
        return billwkwpcode;
    }
    
    public void setBillwkwpcode(String billwkwpcode) {
        this.billwkwpcode = billwkwpcode;
    }
        
   // 最新工序名称
    public String getBillwkwpname() {
        return billwkwpname;
    }
    
    public void setBillwkwpname(String billwkwpname) {
        this.billwkwpname = billwkwpname;
    }
        
   // 分组编码
    public String getGroupcode() {
        return groupcode;
    }
    
    public void setGroupcode(String groupcode) {
        this.groupcode = groupcode;
    }
        
   // item行数
    public Integer getItemcount() {
        return itemcount;
    }
    
    public void setItemcount(Integer itemcount) {
        this.itemcount = itemcount;
    }
        
   // 拣货行数
    public Integer getPickcount() {
        return pickcount;
    }
    
    public void setPickcount(Integer pickcount) {
        this.pickcount = pickcount;
    }
        
   // 完成行数
    public Integer getFinishcount() {
        return finishcount;
    }
    
    public void setFinishcount(Integer finishcount) {
        this.finishcount = finishcount;
    }
        
   // 作废行数
    public Integer getDisannulcount() {
        return disannulcount;
    }
    
    public void setDisannulcount(Integer disannulcount) {
        this.disannulcount = disannulcount;
    }
        
   // 打印次数
    public Integer getPrintcount() {
        return printcount;
    }
    
    public void setPrintcount(Integer printcount) {
        this.printcount = printcount;
    }
        
   // 生产行数
    public Integer getWkitemcount() {
        return wkitemcount;
    }
    
    public void setWkitemcount(Integer wkitemcount) {
        this.wkitemcount = wkitemcount;
    }
        
   // 生产完成
    public Integer getWkfinishcount() {
        return wkfinishcount;
    }
    
    public void setWkfinishcount(Integer wkfinishcount) {
        this.wkfinishcount = wkfinishcount;
    }
        
   // 已转Wip行数
    public Integer getWkwipcount() {
        return wkwipcount;
    }
    
    public void setWkwipcount(Integer wkwipcount) {
        this.wkwipcount = wkwipcount;
    }
        
   // 结款方式
    public String getPayment() {
        return payment;
    }
    
    public void setPayment(String payment) {
        this.payment = payment;
    }
        
   // 正在进行OA
    public Integer getOaflowmark() {
        return oaflowmark;
    }
    
    public void setOaflowmark(Integer oaflowmark) {
        this.oaflowmark = oaflowmark;
    }
        
   // 附件记数
    public Integer getBillattacount() {
        return billattacount;
    }
    
    public void setBillattacount(Integer billattacount) {
        this.billattacount = billattacount;
    }
        
   // 成本预算
    public Double getBillcostbudgetamt() {
        return billcostbudgetamt;
    }
    
    public void setBillcostbudgetamt(Double billcostbudgetamt) {
        this.billcostbudgetamt = billcostbudgetamt;
    }
        
   // 预收款
    public Double getFirstamt() {
        return firstamt;
    }
    
    public void setFirstamt(Double firstamt) {
        this.firstamt = firstamt;
    }
        
   // 收款额
    public Double getLastamt() {
        return lastamt;
    }
    
    public void setLastamt(Double lastamt) {
        this.lastamt = lastamt;
    }
        
   // 开票款
    public Double getInvoamt() {
        return invoamt;
    }
    
    public void setInvoamt(Double invoamt) {
        this.invoamt = invoamt;
    }
        
   // 发票行数
    public Integer getInvocount() {
        return invocount;
    }
    
    public void setInvocount(Integer invocount) {
        this.invocount = invocount;
    }
        
   // 币种id
    public Integer getMoneyid() {
        return moneyid;
    }
    
    public void setMoneyid(Integer moneyid) {
        this.moneyid = moneyid;
    }
        
   // 币种名字
    public String getMoneyname() {
        return moneyname;
    }
    
    public void setMoneyname(String moneyname) {
        this.moneyname = moneyname;
    }
        
   // 主计划行数
    public Integer getMainplancount() {
        return mainplancount;
    }
    
    public void setMainplancount(Integer mainplancount) {
        this.mainplancount = mainplancount;
    }
        
   // 优惠金额
    public Double getBillsubtotal() {
        return billsubtotal;
    }
    
    public void setBillsubtotal(Double billsubtotal) {
        this.billsubtotal = billsubtotal;
    }
        
   // 优惠券
    public String getCouponids() {
        return couponids;
    }
    
    public void setCouponids(String couponids) {
        this.couponids = couponids;
    }
        
   // 优惠券名称
    public String getCouponnames() {
        return couponnames;
    }
    
    public void setCouponnames(String couponnames) {
        this.couponnames = couponnames;
    }
        
   // 成交含税金额
    public Double getFinishtaxamount() {
        return finishtaxamount;
    }
    
    public void setFinishtaxamount(Double finishtaxamount) {
        this.finishtaxamount = finishtaxamount;
    }
        
   // 成交金额
    public Double getFinishamount() {
        return finishamount;
    }
    
    public void setFinishamount(Double finishamount) {
        this.finishamount = finishamount;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 部门id
    public String getDeptid() {
        return deptid;
    }
    
    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

