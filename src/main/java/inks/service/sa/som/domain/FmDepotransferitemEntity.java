package inks.service.sa.som.domain;

import java.io.Serializable;
import lombok.Data;

/**
 * 预收核转Item(FmDepotransferitem)Entity
 *
 * <AUTHOR>
 * @since 2025-05-14 17:21:35
 */
@Data
public class FmDepotransferitemEntity implements Serializable {
    private static final long serialVersionUID = 400410756461803674L;
     // id
    private String id;
     // Pid
    private String pid;
     // 预收单id
    private String paybillid;
     // 预收单单号(备查)
    private String payrefno;
     // 预收单客户id
    private String paygroupid;
     // 预收单金额
    private Double payamount;
     // 转入金额
    private Double amount;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;


}

