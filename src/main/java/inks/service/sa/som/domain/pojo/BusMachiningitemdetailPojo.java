package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单项目(BusMachiningitem)Pojo
 *
 * <AUTHOR>
 * @since 2022-06-06 08:36:55
 */
public class BusMachiningitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -10356574021554259L;
    // ID
    @Excel(name = "ID")
    private String id;
    // 制令单号
    @Excel(name = "制令单号")
    private String pid;
    // 订单批次
    @Excel(name = "订单批次")
    private String machbatch;
    // 商品ID
    @Excel(name = "商品ID")
    private String goodsid;
    // 数量
    @Excel(name = "数量")
    private Double quantity;
    // 含税单价
    @Excel(name = "含税单价")
    private Double taxprice;
    // 含税金额
    @Excel(name = "含税金额")
    private Double taxamount;
    // 税率(备用)
    @Excel(name = "税率(备用)")
    private Integer itemtaxrate;
    // 税额
    @Excel(name = "税额")
    private Double taxtotal;
    // 未税单价
    @Excel(name = "未税单价")
    private Double price;
    // 未税金额
    @Excel(name = "未税金额")
    private Double amount;
    // 原始交期
    @Excel(name = "原始交期")
    private Date itemorgdate;
    // 评审交期
    @Excel(name = "评审交期")
    private Date itemplandate;
    // 生产需求
    @Excel(name = "生产需求")
    private Double wkqty;
    // 库存发货
    @Excel(name = "库存发货")
    private Double stoqty;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 技术状态
    @Excel(name = "技术状态")
    private String engstatetext;
    // 状态日期
    @Excel(name = "状态日期")
    private Date engstatedate;
    // 生产状态
    @Excel(name = "生产状态")
    private String wkstatetext;
    // 状态日期
    @Excel(name = "状态日期")
    private Date wkstatedate;
    // 业务状态
    @Excel(name = "业务状态")
    private String busstatetext;
    // 状态日期
    @Excel(name = "状态日期")
    private Date busstatedate;
    // 采购数量
    @Excel(name = "采购数量")
    private Double buyquantity;
    // 生产数量
    @Excel(name = "生产数量")
    private Double wkquantity;
    // 生产入库
    @Excel(name = "生产入库")
    private Double inquantity;
    // 拣货数量
    @Excel(name = "拣货数量")
    private Double pickqty;
    // 发出数量
    @Excel(name = "发出数量")
    private Double finishqty;
    // 已出入库
    @Excel(name = "已出入库")
    private Double outquantity;
    // 第二单位数
    @Excel(name = "第二单位数")
    private Double outsecqty;
    // 版本新旧
    @Excel(name = "版本新旧")
    private String editioninfo;
    // 完成日期
    @Excel(name = "完成日期")
    private Date itemcompdate;
    // 虚拟货品
    @Excel(name = "虚拟货品")
    private Integer virtualitem;
    // 关闭
    @Excel(name = "关闭")
    private Integer closed;
    // 标准销价
    @Excel(name = "标准销价")
    private Double stdprice;
    // 标准金额
    @Excel(name = "标准金额")
    private Double stdamount;
    // 折扣
    @Excel(name = "折扣")
    private Double rebate;
    // MRP单号
    @Excel(name = "MRP单号")
    private String mrpuid;
    // MRP单id
    @Excel(name = "MRP单id")
    private String mrpid;
    // 最大允收
    @Excel(name = "最大允收")
    private Double maxqty;
    // 指定库位
    @Excel(name = "指定库位")
    private String location;
    // 指定批号
    @Excel(name = "指定批号")
    private String batchno;
    // 作废
    @Excel(name = "作废")
    private Integer disannulmark;
    // Wip已用
    @Excel(name = "Wip已用")
    private Integer wipused;
    // 当前工序id
    @Excel(name = "当前工序id")
    private String wkwpid;
    // 当前工序编码
    @Excel(name = "当前工序编码")
    private String wkwpcode;
    // 当前工序名称
    @Excel(name = "当前工序名称")
    private String wkwpname;
    // 当前工序行号
    @Excel(name = "当前工序行号")
    private Integer wkrownum;
    // 订单成本预算
    @Excel(name = "订单成本预算")
    private String ordercostuid;
    // 订单成本预算id
    @Excel(name = "订单成本预算id")
    private String ordercostitemid;
    // 报价单
    @Excel(name = "报价单")
    private String quotuid;
    // 报价单id
    @Excel(name = "报价单id")
    private String quotitemid;
    // Bom类型
    @Excel(name = "Bom类型")
    private Integer bomtype;
    // 订单BOMid
    @Excel(name = "订单BOMid")
    private String bomid;
    // 订单BomRefno
    @Excel(name = "订单BomRefno")
    private String bomuid;
    // 订单Bom状态
    @Excel(name = "订单Bom状态")
    private String bomstate;
    @Excel(name = "")
    private String attributejson;
    // spu文本
    @Excel(name = "spu文本")
    private String attributestr;
    // 生产类型 订单 样品
    @Excel(name = "生产类型 订单 样品")
    private String machtype;
    // 0新单1翻单
    @Excel(name = "0新单1翻单")
    private Integer reordermark;
    // 主料Code
    @Excel(name = "主料Code")
    private String matcode;
    // 主料已领
    @Excel(name = "主料已领")
    private Integer matused;
    // 成本Item
    @Excel(name = "成本Item")
    private String costitemjson;
    // 成本分类
    @Excel(name = "成本分类")
    private String costgroupjson;
    // 主料id
    private String matid;
    // 直接材料
    private Double matcostamt;
    // 直接人工
    private Double laborcostamt;
    // 直接费用
    private Double directcostamt;
    // 间接费用
    private Double indirectcostamt;
    // 来源:0=其他1=核价单2=报价单
    @Excel(name = "来源:0=其他1=核价单2=报价单")
    private Integer sourcetype;
    // 物料已采购
    @Excel(name = "物料已采购")
    private Double matbuyqty;
    // 物料已领
    @Excel(name = "物料已领")
    private Double matuseqty;
    // 平均预收额
    @Excel(name = "平均预收额")
    private Double avgfirstamt;
    // 平均收款额
    @Excel(name = "平均收款额")
    private Double avglastamt;
    // 平均开票
    @Excel(name = "平均开票")
    private Double avginvoamt;
    // 发票数量
    @Excel(name = "发票数量")
    private Double invoqty;
    // 主计划数
    @Excel(name = "主计划数")
    private Double mainplanqty;
    // 主计划关闭
    @Excel(name = "主计划关闭")
  private Integer mainplanclosed;
     // 开票额
  @Excel(name = "开票额")
  private Double invoamt;
     // 单价优惠
  @Excel(name = "单价优惠")
  private Double subprice;
     // 优惠总额
  @Excel(name = "优惠总额")
  private Double subamount;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 自定义11
    @Excel(name = "自定义11")
    private String custom11;
    // 自定义12
    @Excel(name = "自定义12")
    private String custom12;
    // 自定义13
    @Excel(name = "自定义13")
    private String custom13;
    // 自定义14
    @Excel(name = "自定义14")
    private String custom14;
    // 自定义15
    @Excel(name = "自定义15")
    private String custom15;
    // 自定义16
    @Excel(name = "自定义16")
    private String custom16;
    // 自定义17
    @Excel(name = "自定义17")
    private String custom17;
    // 自定义18
    @Excel(name = "自定义18")
    private String custom18;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // 客户订单号
    @Excel(name = "客户订单号")
    private String custorderid;

    // 交货方式
    @Excel(name = "交货方式")
    private String logisticsmode;
    // 交货港口
    @Excel(name = "交货港口")
    private String logisticsport;
    // 出口国家
    @Excel(name = "出口国家")
    private String country;
    // 预付款
    @Excel(name = "预付款")
    private Double advaamount;
    // 业务员
    @Excel(name = "业务员")
    private String salesman;
    // 客户ID
    private String groupid;

    // 摘要
    private String summary;
    // 订单specid
    @Excel(name = "订单specid")
    private String specid;
    // 订单SpecRefNo
    @Excel(name = "订单SpecRefNo")
    private String specuid;
    // 订单Spec状态
    @Excel(name = "订单Spec状态")
    private Integer specstate;
    //    付款方式
    private String paymentmethod;


    // Mat_Goods.Surface,颜色
    private String surface;
    // Mat_Goods.Material,材质
    private String material;
    // Mat_Goods.Drawing,款号
    private String drawing;
    // Mat_Goods.BrandName,品牌
    private String brandname;

    //采购额 BuytaxAmt=sum(采购合同item.taxamount) where machitemid
    private Double buytaxamt;


    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Double getBuytaxamt() {
        return buytaxamt;
    }

    public void setBuytaxamt(Double buytaxamt) {
        this.buytaxamt = buytaxamt;
    }

    // 制令单号
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 商品ID
    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    public String getMachbatch() {
        return machbatch;
    }

    public void setMachbatch(String machbatch) {
        this.machbatch = machbatch;
    }

    public Double getAvginvoamt() {
        return avginvoamt;
    }

    public void setAvginvoamt(Double avginvoamt) {
        this.avginvoamt = avginvoamt;
    }

    public Double getMainplanqty() {
        return mainplanqty;
    }

    public void setMainplanqty(Double mainplanqty) {
        this.mainplanqty = mainplanqty;
    }

    public Integer getMainplanclosed() {
        return mainplanclosed;
    }

    public void setMainplanclosed(Integer mainplanclosed) {
        this.mainplanclosed = mainplanclosed;
    }

    public Double getInvoqty() {
        return invoqty;
    }

    public void setInvoqty(Double invoqty) {
        this.invoqty = invoqty;
    }

    // 数量
    public Double getQuantity() {
        return quantity;
    }

    public String getSpecid() {
        return specid;
    }

    public void setSpecid(String specid) {
        this.specid = specid;
    }

    public String getSurface() {
        return surface;
    }

    public void setSurface(String surface) {
        this.surface = surface;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public String getDrawing() {
        return drawing;
    }

    public void setDrawing(String drawing) {
        this.drawing = drawing;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public String getSpecuid() {
        return specuid;
    }

    public void setSpecuid(String specuid) {
        this.specuid = specuid;
    }

    public Integer getSpecstate() {
        return specstate;
    }

    public void setSpecstate(Integer specstate) {
        this.specstate = specstate;
    }

    public String getAttributestr() {
        return attributestr;
    }

    public void setAttributestr(String attributestr) {
        this.attributestr = attributestr;
    }

    public Double getMatbuyqty() {
        return matbuyqty;
    }

    public void setMatbuyqty(Double matbuyqty) {
        this.matbuyqty = matbuyqty;
    }

    public Double getMatuseqty() {
        return matuseqty;
    }

    public void setMatuseqty(Double matuseqty) {
        this.matuseqty = matuseqty;
    }

    public String getPaymentmethod() {
        return paymentmethod;
    }

    public void setPaymentmethod(String paymentmethod) {
        this.paymentmethod = paymentmethod;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    // 含税单价
    public Double getTaxprice() {
        return taxprice;
    }

    public Double getAvgfirstamt() {
        return avgfirstamt;
    }

    public void setAvgfirstamt(Double avgfirstamt) {
        this.avgfirstamt = avgfirstamt;
    }

    public Double getAvglastamt() {
        return avglastamt;
    }

    public void setAvglastamt(Double avglastamt) {
        this.avglastamt = avglastamt;
    }

    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }

    // 含税金额
    public Double getTaxamount() {
        return taxamount;
    }

    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }

    // 税率(备用)
    public Integer getItemtaxrate() {
        return itemtaxrate;
    }

    public void setItemtaxrate(Integer itemtaxrate) {
        this.itemtaxrate = itemtaxrate;
    }

    // 税额
    public Double getTaxtotal() {
        return taxtotal;
    }

    public void setTaxtotal(Double taxtotal) {
        this.taxtotal = taxtotal;
    }

    // 未税单价
    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    // 未税金额
    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    // 原始交期
    public Date getItemorgdate() {
        return itemorgdate;
    }

    public void setItemorgdate(Date itemorgdate) {
        this.itemorgdate = itemorgdate;
    }

    // 评审交期
    public Date getItemplandate() {
        return itemplandate;
    }

    public void setItemplandate(Date itemplandate) {
        this.itemplandate = itemplandate;
    }

    // 生产需求
    public Double getWkqty() {
        return wkqty;
    }

    public void setWkqty(Double wkqty) {
        this.wkqty = wkqty;
    }

    // 库存发货
    public Double getStoqty() {
        return stoqty;
    }

    public void setStoqty(Double stoqty) {
        this.stoqty = stoqty;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 技术状态
    public String getEngstatetext() {
        return engstatetext;
    }

    public void setEngstatetext(String engstatetext) {
        this.engstatetext = engstatetext;
    }

    // 状态日期
    public Date getEngstatedate() {
        return engstatedate;
    }

    public void setEngstatedate(Date engstatedate) {
        this.engstatedate = engstatedate;
    }

    // 生产状态
    public String getWkstatetext() {
        return wkstatetext;
    }

    public void setWkstatetext(String wkstatetext) {
        this.wkstatetext = wkstatetext;
    }

    // 状态日期
    public Date getWkstatedate() {
        return wkstatedate;
    }

    public void setWkstatedate(Date wkstatedate) {
        this.wkstatedate = wkstatedate;
    }

    // 业务状态
    public String getBusstatetext() {
        return busstatetext;
    }

    public void setBusstatetext(String busstatetext) {
        this.busstatetext = busstatetext;
    }

    // 状态日期
    public Date getBusstatedate() {
        return busstatedate;
    }

    public void setBusstatedate(Date busstatedate) {
        this.busstatedate = busstatedate;
    }

    // 采购数量
    public Double getBuyquantity() {
        return buyquantity;
    }

    public void setBuyquantity(Double buyquantity) {
        this.buyquantity = buyquantity;
    }

    // 生产数量
    public Double getWkquantity() {
        return wkquantity;
    }

    public void setWkquantity(Double wkquantity) {
        this.wkquantity = wkquantity;
    }

    // 生产入库
    public Double getInquantity() {
        return inquantity;
    }

    public void setInquantity(Double inquantity) {
        this.inquantity = inquantity;
    }

    // 拣货数量
    public Double getPickqty() {
        return pickqty;
    }

    public void setPickqty(Double pickqty) {
        this.pickqty = pickqty;
    }

    // 发出数量
    public Double getFinishqty() {
        return finishqty;
    }

    public void setFinishqty(Double finishqty) {
        this.finishqty = finishqty;
    }

    // 已出入库
    public Double getOutquantity() {
        return outquantity;
    }

    public void setOutquantity(Double outquantity) {
        this.outquantity = outquantity;
    }

    // 第二单位数
    public Double getOutsecqty() {
        return outsecqty;
    }

    public void setOutsecqty(Double outsecqty) {
        this.outsecqty = outsecqty;
    }

    // 版本新旧
    public String getEditioninfo() {
        return editioninfo;
    }

    public void setEditioninfo(String editioninfo) {
        this.editioninfo = editioninfo;
    }

    // 完成日期
    public Date getItemcompdate() {
        return itemcompdate;
    }

    public void setItemcompdate(Date itemcompdate) {
        this.itemcompdate = itemcompdate;
    }

    // 虚拟货品
    public Integer getVirtualitem() {
        return virtualitem;
    }

    public void setVirtualitem(Integer virtualitem) {
        this.virtualitem = virtualitem;
    }

    // 关闭
    public Integer getClosed() {
        return closed;
    }

    public void setClosed(Integer closed) {
        this.closed = closed;
    }

    // 标准销价
    public Double getStdprice() {
        return stdprice;
    }

    public void setStdprice(Double stdprice) {
        this.stdprice = stdprice;
    }

    // 标准金额
    public Double getStdamount() {
        return stdamount;
    }

    public void setStdamount(Double stdamount) {
        this.stdamount = stdamount;
    }

    // 折扣
    public Double getRebate() {
        return rebate;
    }

    public void setRebate(Double rebate) {
        this.rebate = rebate;
    }

    // MRP单号
    public String getMrpuid() {
        return mrpuid;
    }

    public void setMrpuid(String mrpuid) {
        this.mrpuid = mrpuid;
    }

    // MRP单id
    public String getMrpid() {
        return mrpid;
    }

    public void setMrpid(String mrpid) {
        this.mrpid = mrpid;
    }

    // 最大允收
    public Double getMaxqty() {
        return maxqty;
    }

    public void setMaxqty(Double maxqty) {
        this.maxqty = maxqty;
    }

    // 指定库位
    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    // 指定批号
    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    // 作废
    public Integer getDisannulmark() {
        return disannulmark;
    }

    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }

    // Wip已用
    public Integer getWipused() {
        return wipused;
    }

    public void setWipused(Integer wipused) {
        this.wipused = wipused;
    }

    // 当前工序id
    public String getWkwpid() {
        return wkwpid;
    }

    public void setWkwpid(String wkwpid) {
        this.wkwpid = wkwpid;
    }

    // 当前工序编码
    public String getWkwpcode() {
        return wkwpcode;
    }

    public void setWkwpcode(String wkwpcode) {
        this.wkwpcode = wkwpcode;
    }

    // 当前工序名称
    public String getWkwpname() {
        return wkwpname;
    }

    public void setWkwpname(String wkwpname) {
        this.wkwpname = wkwpname;
    }

    // 当前工序行号
    public Integer getWkrownum() {
        return wkrownum;
    }

    public void setWkrownum(Integer wkrownum) {
        this.wkrownum = wkrownum;
    }

    // 订单成本预算
    public String getOrdercostuid() {
        return ordercostuid;
    }

    public void setOrdercostuid(String ordercostuid) {
        this.ordercostuid = ordercostuid;
    }

    // 订单成本预算id
    public String getOrdercostitemid() {
        return ordercostitemid;
    }

    public void setOrdercostitemid(String ordercostitemid) {
        this.ordercostitemid = ordercostitemid;
    }

    // 报价单
    public String getQuotuid() {
        return quotuid;
    }

    public void setQuotuid(String quotuid) {
        this.quotuid = quotuid;
    }

    // 报价单id
    public String getQuotitemid() {
        return quotitemid;
    }

    public void setQuotitemid(String quotitemid) {
        this.quotitemid = quotitemid;
    }

    // Bom类型
    public Integer getBomtype() {
        return bomtype;
    }

    public void setBomtype(Integer bomtype) {
        this.bomtype = bomtype;
    }

    // 订单BOMid
    public String getBomid() {
        return bomid;
    }

    public void setBomid(String bomid) {
        this.bomid = bomid;
    }

    // 订单BomRefno
    public String getBomuid() {
        return bomuid;
    }

    public void setBomuid(String bomuid) {
        this.bomuid = bomuid;
    }

    // 订单Bom状态
    public String getBomstate() {
        return bomstate;
    }

    public void setBomstate(String bomstate) {
        this.bomstate = bomstate;
    }

    public String getAttributejson() {
        return attributejson;
    }

    public void setAttributejson(String attributejson) {
        this.attributejson = attributejson;
    }

    public String getMachtype() {
        return machtype;
    }

    public void setMachtype(String machtype) {
        this.machtype = machtype;
    }

    public Integer getReordermark() {
        return reordermark;
    }

    public void setReordermark(Integer reordermark) {
        this.reordermark = reordermark;
    }

    public String getMatcode() {
        return matcode;
    }

    public void setMatcode(String matcode) {
        this.matcode = matcode;
    }

    public Integer getMatused() {
        return matused;
    }

    public void setMatused(Integer matused) {
        this.matused = matused;
    }

    public String getCostitemjson() {
        return costitemjson;
    }

    public void setCostitemjson(String costitemjson) {
        this.costitemjson = costitemjson;
    }

    public String getCostgroupjson() {
        return costgroupjson;
    }

    public void setCostgroupjson(String costgroupjson) {
        this.costgroupjson = costgroupjson;
    }

    public String getMatid() {
        return matid;
    }

    public void setMatid(String matid) {
        this.matid = matid;
    }

    public Double getMatcostamt() {
        return matcostamt;
    }

    public void setMatcostamt(Double matcostamt) {
        this.matcostamt = matcostamt;
    }

    public Double getLaborcostamt() {
        return laborcostamt;
    }

    public void setLaborcostamt(Double laborcostamt) {
        this.laborcostamt = laborcostamt;
    }

    public Double getDirectcostamt() {
        return directcostamt;
    }

    public void setDirectcostamt(Double directcostamt) {
        this.directcostamt = directcostamt;
    }

    public Double getIndirectcostamt() {
        return indirectcostamt;
    }

    public void setIndirectcostamt(Double indirectcostamt) {
        this.indirectcostamt = indirectcostamt;
    }
    // 开票额
    public Double getInvoamt() {
        return invoamt;
    }

    public void setInvoamt(Double invoamt) {
        this.invoamt = invoamt;
    }

   // 单价优惠
    public Double getSubprice() {
        return subprice;
    }

    public void setSubprice(Double subprice) {
        this.subprice = subprice;
    }

   // 优惠总额
    public Double getSubamount() {
        return subamount;
    }

    public void setSubamount(Double subamount) {
        this.subamount = subamount;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 自定义11
    public String getCustom11() {
        return custom11;
    }

    public void setCustom11(String custom11) {
        this.custom11 = custom11;
    }

    // 自定义12
    public String getCustom12() {
        return custom12;
    }

    public void setCustom12(String custom12) {
        this.custom12 = custom12;
    }

    // 自定义13
    public String getCustom13() {
        return custom13;
    }

    public void setCustom13(String custom13) {
        this.custom13 = custom13;
    }

    // 自定义14
    public String getCustom14() {
        return custom14;
    }

    public void setCustom14(String custom14) {
        this.custom14 = custom14;
    }

    // 自定义15
    public String getCustom15() {
        return custom15;
    }

    public void setCustom15(String custom15) {
        this.custom15 = custom15;
    }

    // 自定义16
    public String getCustom16() {
        return custom16;
    }

    public void setCustom16(String custom16) {
        this.custom16 = custom16;
    }

    // 自定义17
    public String getCustom17() {
        return custom17;
    }

    public void setCustom17(String custom17) {
        this.custom17 = custom17;
    }

    // 自定义18
    public String getCustom18() {
        return custom18;
    }

    public void setCustom18(String custom18) {
        this.custom18 = custom18;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getCustorderid() {
        return custorderid;
    }

    public void setCustorderid(String custorderid) {
        this.custorderid = custorderid;
    }

    public String getLogisticsmode() {
        return logisticsmode;
    }

    public void setLogisticsmode(String logisticsmode) {
        this.logisticsmode = logisticsmode;
    }

    public String getLogisticsport() {
        return logisticsport;
    }

    public void setLogisticsport(String logisticsport) {
        this.logisticsport = logisticsport;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Double getAdvaamount() {
        return advaamount;
    }

    public void setAdvaamount(Double advaamount) {
        this.advaamount = advaamount;
    }

    public String getSalesman() {
        return salesman;
    }

    public void setSalesman(String salesman) {
        this.salesman = salesman;
    }

    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Integer getSourcetype() {
        return sourcetype;
    }

    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }
}

