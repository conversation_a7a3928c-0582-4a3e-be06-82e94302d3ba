package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 发票to收款(BusAccountarap)Pojo
 *
 * <AUTHOR>
 * @since 2023-05-16 11:31:29
 */
public class BusAccountarapdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -58881314547863236L;
         // id
       @Excel(name = "id")    
  private String id;
         // Pid
       @Excel(name = "Pid")    
  private String pid;
         // 方向open/in/out/close
       @Excel(name = "方向open/in/out/close")    
  private String direction;
         // 模块编码
       @Excel(name = "模块编码")    
  private String modulecode;
         // 单据类型
       @Excel(name = "单据类型")    
  private String billtype;
         // 单据日期
       @Excel(name = "单据日期")    
  private Date billdate;
         // 单据标题
       @Excel(name = "单据标题")    
  private String billtitle;
         // 单居Refno
       @Excel(name = "单居Refno")    
  private String billuid;
         // 单据id
       @Excel(name = "单据id")    
  private String billid;
         // 期初金额
       @Excel(name = "期初金额")    
  private Double openamount;
         // 入账金额
       @Excel(name = "入账金额")    
  private Double inamount;
         // 出账金额
       @Excel(name = "出账金额")    
  private Double outamount;
         // 期末金额
       @Excel(name = "期末金额")    
  private Double closeamount;
         // 行号
       @Excel(name = "行号")    
  private Integer rownum;
         // 备注
       @Excel(name = "备注")    
  private String remark;
         // 自定义1
       @Excel(name = "自定义1")    
  private String custom1;
         // 自定义2
       @Excel(name = "自定义2")    
  private String custom2;
         // 自定义3
       @Excel(name = "自定义3")    
  private String custom3;
         // 自定义4
       @Excel(name = "自定义4")    
  private String custom4;
         // 自定义5
       @Excel(name = "自定义5")    
  private String custom5;
         // 自定义6
       @Excel(name = "自定义6")    
  private String custom6;
         // 自定义7
       @Excel(name = "自定义7")    
  private String custom7;
         // 自定义8
       @Excel(name = "自定义8")    
  private String custom8;
         // 自定义9
       @Excel(name = "自定义9")    
  private String custom9;
         // 自定义10
       @Excel(name = "自定义10")    
  private String custom10;
         // 租户id
       @Excel(name = "租户id")    
  private String tenantid;
         // 乐观锁
       @Excel(name = "乐观锁")    
  private Integer revision;

     // id
       public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
     // Pid
       public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
     // 方向open/in/out/close
       public String getDirection() {
        return direction;
    }
    
    public void setDirection(String direction) {
        this.direction = direction;
    }
        
     // 模块编码
       public String getModulecode() {
        return modulecode;
    }
    
    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
        
     // 单据类型
       public String getBilltype() {
        return billtype;
    }
    
    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
        
     // 单据日期
       public Date getBilldate() {
        return billdate;
    }
    
    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
        
     // 单据标题
       public String getBilltitle() {
        return billtitle;
    }
    
    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
        
     // 单居Refno
       public String getBilluid() {
        return billuid;
    }
    
    public void setBilluid(String billuid) {
        this.billuid = billuid;
    }
        
     // 单据id
       public String getBillid() {
        return billid;
    }
    
    public void setBillid(String billid) {
        this.billid = billid;
    }
        
     // 期初金额
       public Double getOpenamount() {
        return openamount;
    }
    
    public void setOpenamount(Double openamount) {
        this.openamount = openamount;
    }
        
     // 入账金额
       public Double getInamount() {
        return inamount;
    }
    
    public void setInamount(Double inamount) {
        this.inamount = inamount;
    }
        
     // 出账金额
       public Double getOutamount() {
        return outamount;
    }
    
    public void setOutamount(Double outamount) {
        this.outamount = outamount;
    }
        
     // 期末金额
       public Double getCloseamount() {
        return closeamount;
    }
    
    public void setCloseamount(Double closeamount) {
        this.closeamount = closeamount;
    }
        
     // 行号
       public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
     // 备注
       public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
     // 自定义1
       public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
     // 自定义2
       public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
     // 自定义3
       public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
     // 自定义4
       public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
     // 自定义5
       public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
     // 自定义6
       public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
     // 自定义7
       public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
     // 自定义8
       public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
     // 自定义9
       public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
     // 自定义10
       public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
     // 租户id
       public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
     // 乐观锁
       public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

