package inks.service.sa.som.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

import java.io.Serializable;

/**
 * 收入明细(FmIncomeitem)Pojo
 *
 * <AUTHOR>
 * @since 2022-08-29 15:52:47
 */
public class FmIncomeitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -80991172288426324L;
    // id
    @Excel(name = "id")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 费用id
    @Excel(name = "费用id")
    private String costtypeid;
    // 费用名称
    @Excel(name = "费用名称")
    private String itemname;
    // 描述
    @Excel(name = "描述")
    private String itemdepict;
    // 金额
    @Excel(name = "金额")
    private Double amount;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 编码
    private String costcode;
    // 名称
    private String costname;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 费用id
    public String getCosttypeid() {
        return costtypeid;
    }

    public void setCosttypeid(String costtypeid) {
        this.costtypeid = costtypeid;
    }

    // 费用名称
    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    // 描述
    public String getItemdepict() {
        return itemdepict;
    }

    public void setItemdepict(String itemdepict) {
        this.itemdepict = itemdepict;
    }

    // 金额
    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getCostcode() {
        return costcode;
    }

    public void setCostcode(String costcode) {
        this.costcode = costcode;
    }

    public String getCostname() {
        return costname;
    }

    public void setCostname(String costname) {
        this.costname = costname;
    }
}

