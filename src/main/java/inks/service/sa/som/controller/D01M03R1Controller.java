package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.service.AppWorkgroupService;
import inks.service.sa.som.service.BusMachiningitemService;
import inks.service.sa.som.service.D01M03R1Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 销售订单(BusMachining)报表
 *
 * <AUTHOR>
 * @since 2021-11-13 10:19:45
 */
@RestController
@RequestMapping("D01M03R1")
@Api(tags = "D01M03R1:销售订单报表")
public class D01M03R1Controller {

    @Resource
    private AppWorkgroupService appWorkgroupService;


    @Resource
    private D01M03R1Service d01M03R1Service;


    @Resource
    private BusMachiningitemService busMachiningitemService;

    @Resource
    private SaRedisService saRedisService;


    /**
     * 通过主键查询单条数据
     *
     * @param json 主键
     * @return 单条数据
     */
    @ApiOperation(value = "获取客户订单金额排名", notes = "获取客户订单金额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupMax", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByGroupMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M03R1Service.getSumAmtByGroupMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 订单逾期
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "订单逾期", notes = "订单逾期", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<ChartPojo> getPageList() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.d01M03R1Service.getPageList(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 热销产品
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "热销产品", notes = "热销产品", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGoodsMax", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByGoodsMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M03R1Service.getSumAmtByGoodsMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description
     * @date 2021/12/30
     * @param * @param null
     * @return 业务员订单金额占比
     */
    @ApiOperation(value = "业务员订单金额占比", notes = "业务员订单金额占比", produces = "application/json")
    @RequestMapping(value = "/getSumAmtBySalesman", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtBySalesman(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M03R1Service.getSumAmtBySalesman(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    /*
     *
     * <AUTHOR>
     * @description 查询在线订单数
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
//    @ApiOperation(value = "查询在线订单数", notes = "查询在线订单数", produces = "application/json")
//    @RequestMapping(value = "/getListSize", method = RequestMethod.GET)
////    @PreAuthorize(hasPermi = "App_Wg_Workshop.list")
//    public R<Integer> getListSize() {
//        try {
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            return R.ok(this.appWorkgroupService.getListSize(loginUser.getTenantid()));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
}
