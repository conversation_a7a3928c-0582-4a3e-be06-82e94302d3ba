package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.FmCashcarryoverrecPojo;
import inks.service.sa.som.service.FmCashcarryoverrecService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 出纳结账(Fm_CashCarryoverRec)表控制层
 *
 * <AUTHOR>
 * @since 2022-09-26 19:41:47
 */

public class FmCashcarryoverrecController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(FmCashcarryoverrecController.class);

    @Resource
    private FmCashcarryoverrecService fmCashcarryoverrecService;
    @Resource
    private SaRedisService saRedisService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取出纳结账详细信息", notes = "获取出纳结账详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Fm_CashCarryoverRec.List")
    public R<FmCashcarryoverrecPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmCashcarryoverrecService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Fm_CashCarryoverRec.List")
    public R<PageInfo<FmCashcarryoverrecPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Fm_CashCarryoverRec.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.fmCashcarryoverrecService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增出纳结账", notes = "新增出纳结账", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Fm_CashCarryoverRec.Add")
    public R<FmCashcarryoverrecPojo> create(@RequestBody String json) {
        try {
            FmCashcarryoverrecPojo fmCashcarryoverrecPojo = JSONArray.parseObject(json, FmCashcarryoverrecPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            fmCashcarryoverrecPojo.setCreateby(loginUser.getRealName());   // 创建者
            fmCashcarryoverrecPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            fmCashcarryoverrecPojo.setCreatedate(new Date());   // 创建时间
            fmCashcarryoverrecPojo.setLister(loginUser.getRealname());   // 制表
            fmCashcarryoverrecPojo.setListerid(loginUser.getUserid());    // 制表id  
            fmCashcarryoverrecPojo.setModifydate(new Date());   //修改时间
            fmCashcarryoverrecPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.fmCashcarryoverrecService.insert(fmCashcarryoverrecPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改出纳结账", notes = "修改出纳结账", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Fm_CashCarryoverRec.Edit")
    public R<FmCashcarryoverrecPojo> update(@RequestBody String json) {
        try {
            FmCashcarryoverrecPojo fmCashcarryoverrecPojo = JSONArray.parseObject(json, FmCashcarryoverrecPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            fmCashcarryoverrecPojo.setLister(loginUser.getRealname());   // 制表
            fmCashcarryoverrecPojo.setListerid(loginUser.getUserid());    // 制表id  
            fmCashcarryoverrecPojo.setTenantid(loginUser.getTenantid());   //租户id
            fmCashcarryoverrecPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.fmCashcarryoverrecService.update(fmCashcarryoverrecPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除出纳结账", notes = "删除出纳结账", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Fm_CashCarryoverRec.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmCashcarryoverrecService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Fm_CashCarryoverRec.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        FmCashcarryoverrecPojo fmCashcarryoverrecPojo = this.fmCashcarryoverrecService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(fmCashcarryoverrecPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

