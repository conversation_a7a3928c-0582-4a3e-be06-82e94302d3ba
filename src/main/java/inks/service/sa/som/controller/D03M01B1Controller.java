package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.domain.pojo.SaJustauthPojo;
import inks.sa.common.core.feign.SaUtsFeignClient;
import inks.sa.common.core.service.SaJustauthService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.som.domain.pojo.BuyPlanPojo;
import inks.service.sa.som.domain.pojo.BuyPlanitemdetailPojo;
import inks.service.sa.som.service.BuyPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.StringWriter;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 采购计划(BuyPlan)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 14:56:14
 */
@RestController
@RequestMapping("D03M01B1")
@Api(tags = "D03M01B1:采购计划:MRP需求")
public class D03M01B1Controller extends BuyPlanController {
    @Resource
    private SaJustauthService saJustauthService;

    @Resource
    private BuyPlanService buyPlanService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private SaUtsFeignClient saUtsFeignClient;

    @ApiOperation(value = "合并多条生采购计划子表为一条，生成一个新的采购计划主子表 入参：[\"aaa\",\"bbb\"]", notes = "", produces = "application/json")
    @RequestMapping(value = "/mergeItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Plan.Add")
    public R mergeItem(@RequestBody String json) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // 拿到需要合并的采购计划子表id集合并查询出来
            List<String> planItemids = JSON.parseObject(json, new TypeReference<List<String>>() {
            });
            if (planItemids.size() <= 1) {
                throw new BaseBusinessException("请选择需要合并的多条采购计划子表");
            }
            buyPlanService.mergeItem(planItemids, loginUser);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查旬结存明细含未审核", notes = "按条件分页查询结存明细含未审核?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Plan.List")
    public R<PageInfo<BuyPlanitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid, Integer appl) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Plan.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += "  and Buy_PlanItem.FinishQty<Buy_PlanItem.Quantity";
            qpfilter += "  and Buy_PlanItem.BuyQty<Buy_PlanItem.Quantity";
            qpfilter += " and Buy_PlanItem.DisannulMark=0 and Buy_PlanItem.Closed=0 ";  // 未关闭、未注销
            // 过滤掉 MergeMark 为 2 和 3 的条件
            qpfilter += " and Buy_PlanItem.MergeMark NOT IN (2, 3)";
            if (groupid != null) {
                qpfilter += " and Buy_PlanItem.Groupid='" + groupid + "'";
            }
            // 审批通过的
            if (appl != null && appl == 1) {
                qpfilter += " and Buy_Plan.Assessorid<>''";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyPlanService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询含未审核", notes = "按条件分页查询含未审核?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Plan.List")
    public R<PageInfo<BuyPlanPojo>> getOnlinePageTh(@RequestBody String json, String groupid, Integer appl) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Plan.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += "  and Buy_Plan.ItemCount>Buy_Plan.FinishCount+Buy_Plan.DisannulCount";
            if (groupid != null) {
                qpfilter += " and Buy_Plan.Groupid='" + groupid + "'";
            }
            // 审批通过的
            if (appl != null && appl == 1) {
                qpfilter += " and Buy_Plan.Assessorid<>''";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyPlanService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "采购计划子表(或加上合并表)刷新单价，金额（读取采购单子表最新）", produces = "application/json")
    @RequestMapping(value = "/syncPriceFromOrderItem", method = RequestMethod.GET)
    public R syncPriceFromOrderItem(String planitemid, String goodsid, String mergeid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyPlanService.syncPriceFromOrderItem(planitemid, goodsid, mergeid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "异步多线程--采购计划子表(或加上合并表)刷新单价，金额（读取采购单子表最新）", produces = "application/json")
    @RequestMapping(value = "/syncPriceFromOrderStart", method = RequestMethod.GET)
    public R<String> syncPriceFromOrderItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyPlanService.syncPriceFromOrderStart(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "发出第三方审批", notes = "发出第三方审批wex/ding", produces = "application/json")
    @RequestMapping(value = "/sendapprovel", method = RequestMethod.GET)
    public R<ApprrecPojo> sendapprovel(String key, String apprid, String type, String remark) {
        try {
            if (type == null) type = "wxe";  // 默认走企业微信
            String verifyKey = CacheConstants.APPR_CODES_KEY + apprid;
            //获取token
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //从redis中获取模板对象
            // Object obj = saRedisService.getCacheObject(verifyKey);
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
            ApprovePojo approvePojo = new ApprovePojo();
            BuyPlanPojo billEntity = this.buyPlanService.getBillEntity(key, loginUser.getTenantid());
            approvePojo.setObject(billEntity);

            // 发起oms审批,先判断是否正在审批 (最下面发起oms审批成功后需设置OaFlowMark=1)
            if (billEntity.getOaflowmark() != null && billEntity.getOaflowmark() == 1) {
                return R.fail("该单据已发起OA审批");
            }
            if ("oms".equals(type)) {
//                // 发起oms审批,先判断是否正在审批 (最下面发起oms审批成功后需设置OaFlowMark=1)
//                if (machiningBillEntity.getOaflowmark() != null && machiningBillEntity.getOaflowmark() == 1) {
//                    return R.fail("该单据已发起OA审批");
//                }
                //创建VM数据对象
                VelocityContext context = new VelocityContext();
                context.put("approvePojo", approvePojo);
                String str = apprrecPojo.getDatatemp();
                // 初始化并取得Velocity引擎
                VelocityEngine ve = new VelocityEngine();
                ve.init();
                // 转换输出
                StringWriter writer = new StringWriter();
                ve.evaluate(context, writer, "", str); // 关键方法
                //写回String
                str = writer.toString();
                apprrecPojo.setDatatemp(str);
//                String data = JSONObject.toJSONString(approvePojo.getObject());
//                apprrecPojo.setDatatemp(data);
            } else {
                //创建VM数据对象
                VelocityContext context = new VelocityContext();

                //获得第三方账号
                SaJustauthPojo justauthByUserid = saJustauthService.getJustauthByUserid(loginUser.getUserid(), type, loginUser.getTenantid());
                JustauthPojo justauthPojo = new JustauthPojo();
                org.springframework.beans.BeanUtils.copyProperties(justauthByUserid, justauthPojo);

                approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
                approvePojo.setUserid(justauthPojo.getAuthuuid());
                approvePojo.setModelcode(apprrecPojo.getTemplateid());
                context.put("approvePojo", approvePojo);
                String str = apprrecPojo.getDatatemp();
                // 初始化并取得Velocity引擎
                VelocityEngine ve = new VelocityEngine();
                ve.init();
                // 转换输出
                StringWriter writer = new StringWriter();
                ve.evaluate(context, writer, "", str); // 关键方法
                //写回String
                str = writer.toString();
                apprrecPojo.setDatatemp(str);
            }


            //新建审批记录
            apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
            apprrecPojo.setApprname("订单审批");
            apprrecPojo.setResultcode("");
            apprrecPojo.setBillid(key);    // 单据ID
            apprrecPojo.setUserid("");
            apprrecPojo.setApprtype("");
            apprrecPojo.setCreateby(loginUser.getRealname());
            apprrecPojo.setCreatebyid(loginUser.getUserid());
            apprrecPojo.setCreatedate(new Date());
            apprrecPojo.setLister(loginUser.getRealname());
            apprrecPojo.setListerid(loginUser.getUserid());
            apprrecPojo.setModifydate(new Date());
            apprrecPojo.setTenantid(loginUser.getTenantid());
            //发起Flowable审批时加入的评论备注
            apprrecPojo.setRemark(remark == null ? "" : remark);
            //将企业微信审批信息存入redis
            String CachKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
            saRedisService.setCacheObject(CachKey, apprrecPojo, (long) (60 * 12), TimeUnit.MINUTES);
            if ("wxe".equals(type)) {
                R r = this.saUtsFeignClient.wxeapprovel(apprrecPojo.getId(), loginUser.getTenantid());
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r.getMsg());
                }
            } else if ("ding".equals(type)) {
                R r = this.saUtsFeignClient.dingapprovel(apprrecPojo.getId(), loginUser.getTenantid());
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r.getMsg());
                }

            }
//            else {//type为oms
//                R r = this.aDingAndWxeController.omsapprovel(apprrecPojo.getId(), loginUser.getTenantid(), loginUser.getToken());
//                if (r.getCode() != 200) {
//                    return R.fail("发起审批失败" + r.getMsg());
//                }
//                // 发起oms审批成功,需设置OaFlowMark=1 并更新单据
//                machiningBillEntity.setOaflowmark(1);
//                busMachiningService.update(machiningBillEntity);
//            }
            // 发起oms审批成功,需设置OaFlowMark=1 并更新单据
            billEntity.setOaflowmark(1);
            buyPlanService.updateOaflowmark(billEntity);
            return R.ok(apprrecPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "审批回调修改状态", notes = "审批回调修改状态", produces = "application/json")
    @RequestMapping(value = "/justapprovel", method = RequestMethod.GET)
    public R<BuyPlanPojo> justapprovel(String key, String type, String approved) {
        try {
            PrintColor.red("/justapprovel 审批回调修改状态 approved:" + approved);
            //1.读取审批记录
            String verifyKey = CacheConstants.APPR_CODES_KEY + key;
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
            //2.1 获得单据数据
            BuyPlanPojo billPojo = this.buyPlanService.getEntity(apprrecPojo.getBillid(), apprrecPojo.getTenantid());
            //3. 写入审核批
            //获得第三方账号
            if (type == null) type = "wxe";
            // oms审批即将完成,需设置OaFlowMark=0
            billPojo.setOaflowmark(0);
            buyPlanService.updateOaflowmark(billPojo);//只更新oaflowmark
            // 若oms审批点击拒绝(或由发起人取消发起),则设置完OaFlowMark=0直接结束方法
            if ("false".equals(approved)) {
                return R.ok();
            }
            if ("oms".equals(type)) {
//                // oms审批即将完成,需设置OaFlowMark=0
//                busMachiningPojo.setOaflowmark(0);
//                busMachiningMapper.updateOaflowmark(busMachiningPojo);//只更新oaflowmark
//                // 若oms审批点击拒绝(或由发起人取消发起),则设置完OaFlowMark=0直接结束方法
//                if ("false".equals(approved)) {
//                    return R.ok();
//                }
                // 点击同意审批：审批人字段赋值, if包裹外面的approval方法会进行审核
                billPojo.setAssessorid(apprrecPojo.getUserid());
                billPojo.setAssessor(apprrecPojo.getRealname()); //审核员
            } else {
                //获得第三方账号
                SaJustauthPojo justauthByUuid = saJustauthService.getJustauthByUuid(apprrecPojo.getCallbackuuid(), type, apprrecPojo.getTenantid());
                JustauthPojo justauthPojo = new JustauthPojo();
                org.springframework.beans.BeanUtils.copyProperties(justauthByUuid, justauthPojo);
                billPojo.setAssessorid(justauthPojo.getUserid());
                billPojo.setAssessor(justauthPojo.getRealname()); //审核员
            }
            billPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.buyPlanService.approval(billPojo));
        } catch (Exception e) {
            System.out.println("写入审核失败：" + e.getMessage());
            return R.fail(e.getMessage());
        }
    }
}
