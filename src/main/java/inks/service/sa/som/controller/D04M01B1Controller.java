package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.MatAccessEntity;
import inks.service.sa.som.domain.pojo.MatAccessPojo;
import inks.service.sa.som.domain.pojo.MatAccessitemdetailPojo;
import inks.service.sa.som.service.MatAccessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 出入库主表(MatAccess)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-12 14:43:14
 */
@RestController
@RequestMapping("D04M01B1")
@Api(tags = "D04M01B1:收货入库&购退出库")
public class D04M01B1Controller extends MatAccessController {

    @Resource
    private MatAccessService matAccessService;


    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaBillcodeService saBillcodeService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Access.List")
    public R<PageInfo<MatAccessitemdetailPojo>> getPageList(@RequestBody String json, String type, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Access.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setFilterstr(" and Mat_Access.BillType IN ('收货入库','购退出库','收货红冲','购退红冲')");
            return R.ok(this.matAccessService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Access.List")
    public R<PageInfo<MatAccessPojo>> getBillList(@RequestBody String json, String type, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Access.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and Mat_Access.BillType IN ('收货入库','购退出库','收货红冲','购退红冲')");
            return R.ok(this.matAccessService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Access.List")
    public R<PageInfo<MatAccessPojo>> getPageTh(@RequestBody String json, String type, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Access.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and Mat_Access.BillType IN ('收货入库','购退出库','收货红冲','购退红冲')");
            return R.ok(this.matAccessService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增出入库主表", notes = "新增出入库主表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Access.Add")
    public R<MatAccessPojo> create(@RequestBody String json) {
        try {
            MatAccessPojo matAccessPojo = JSONArray.parseObject(json, MatAccessPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D04M01B1", loginUser.getTenantid(),"Mat_Access");
            matAccessPojo.setRefno(refNo);
            matAccessPojo.setCreateby(loginUser.getRealname());   // 创建者
            matAccessPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matAccessPojo.setCreatedate(new Date());   // 创建时间
            matAccessPojo.setLister(loginUser.getRealname());   // 制表
            matAccessPojo.setListerid(loginUser.getUserid());    // 制表id            
            matAccessPojo.setModifydate(new Date());   //修改时间
            matAccessPojo.setTenantid(loginUser.getTenantid());   //租户id
            matAccessPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            return R.ok(this.matAccessService.insert(matAccessPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param key 实体
     * @return 新增结果
     */
    @ApiOperation(value = "红冲单据", notes = "红冲单据", produces = "application/json")
    @RequestMapping(value = "/createRed", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Access.Add")
    public R<MatAccessPojo> createRed(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatAccessPojo matAccessPojo = this.matAccessService.getBillEntity(key, loginUser.getTenantid());
            //新建原始单
            MatAccessEntity matAccessEntityOrg = new MatAccessEntity();
            matAccessEntityOrg.setId(matAccessPojo.getId());
            matAccessEntityOrg.setTenantid(matAccessPojo.getTenantid());


            //生成单据编码  红冲单的编码
            String refNoRed = saBillcodeService.getSerialNo("D04M01B1", loginUser.getTenantid(),"Mat_Access");
            matAccessEntityOrg.setReturnuid(refNoRed);//原单设置红冲单号

            matAccessPojo.setRefno(refNoRed);
            matAccessPojo.setOrguid(matAccessPojo.getRefno());//红冲单设置原单号

            matAccessPojo.setCreateby(loginUser.getRealname());   // 创建者
            matAccessPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matAccessPojo.setCreatedate(new Date());   // 创建时间
            matAccessPojo.setLister(loginUser.getRealname());   // 制表
            matAccessPojo.setListerid(loginUser.getUserid());    // 制表id
            matAccessPojo.setModifydate(new Date());   //修改时间
            matAccessPojo.setTenantid(loginUser.getTenantid());   //租户id
            matAccessPojo.setBilldate(new Date());
            matAccessPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            return R.ok(this.matAccessService.insertRed(matAccessPojo, matAccessEntityOrg));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新领料单成本
     * key为领料单子表id: Mat_RequisitionItem.id
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新领料单成本", notes = "刷新领料单成本", produces = "application/json")
    @RequestMapping(value = "/updateRequMatCostAmt", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Access.Edit")
    public R<Integer> updateRequMatCostAmt(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matAccessService.updateRequMatCostAmt(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新销售订单成本
     * key为销售订单子表id: Bus_MachiningItem.id
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新销售订单成本", notes = "刷新销售订单成本", produces = "application/json")
    @RequestMapping(value = "/updateMachMatCostAmt", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Access.Edit")
    public R<Integer> updateMachMatCostAmt(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matAccessService.updateMachMatCostAmt(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

