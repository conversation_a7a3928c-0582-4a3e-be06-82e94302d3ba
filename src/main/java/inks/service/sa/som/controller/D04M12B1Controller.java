package inks.service.sa.som.controller;

import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.service.MatCombinService;
import inks.service.sa.som.service.MatCombinitemService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 拆装单(MatCombin)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-13 20:48:55
 */
@RestController
@RequestMapping("D04M12B1")
@Api(tags = "D04M12B1:拆装单")
public class D04M12B1Controller extends MatCombinController {


    @Resource
    private MatCombinService matCombinService;


    @Resource
    private MatCombinitemService matCombinitemService;

    @Resource
    private SaRedisService saRedisService;


//    @Resource
//    private JobFeignService jobFeignService;

//    @ApiOperation(value = "打印列表", notes = "打印列表", produces = "application/json")
//    @RequestMapping(value = "/printWsLabel", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Mat_Combin.Print")
//    public R printWsLabel(String key, String sn) {
//        // 获得用户数据
//        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//
//        QueryParam queryParam = new QueryParam();
//        queryParam.setOrderBy("Mat_Combin.CreateDate");
//        queryParam.setTenantid(loginUser.getTenantid());
//        queryParam.setFilterstr(" and Mat_CombinItem.id='" + key + "'");
//        //获取列表信息
//        List<MatCombinitemdetailPojo> lst = this.matCombinService.getPageList(queryParam).getList();
//        //表头转MAP(空)
//        Map<String, Object> map = new IdentityHashMap<>();
//
//        map.put("code", "print");
//        map.put("msg", "打印");
//        map.put("data", JSONObject.toJSONString(lst));
//        String message = JSONObject.toJSONString(map);
//
//        jobFeignService.sending(sn, message);
//        System.out.println("发出审批:D04M12.wslabelprintersn:" + sn);
//        return R.ok("打印完成");
//    }

}
