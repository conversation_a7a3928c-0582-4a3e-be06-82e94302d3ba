package inks.service.sa.som.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.MatCargospacePojo;
import inks.service.sa.som.service.MatCargospaceService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 仓库货位(Mat_CargoSpace)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-13 20:43:50
 */
public class MatCargospaceController {

    @Resource
    private MatCargospaceService matCargospaceService;


    @Resource
    private SaRedisService saRedisService;


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取仓库货位详细信息", notes = "获取仓库货位详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CargoSpace.List")
    public R<MatCargospacePojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matCargospaceService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_CargoSpace.List")
    public R<PageInfo<MatCargospacePojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_CargoSpace.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.matCargospaceService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增仓库货位", notes = "新增仓库货位", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_CargoSpace.Add")
    public R<MatCargospacePojo> create(@RequestBody String json) {
        try {
            MatCargospacePojo matCargospacePojo = JSONArray.parseObject(json, MatCargospacePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // 检查是否存在相同的仓库编码或仓库名称
            if (this.matCargospaceService.checkCodeOrName(matCargospacePojo.getSpacecode(), matCargospacePojo.getSpacename(), loginUser.getTenantid())) {
                return R.fail("货位编码或货位名称已存在");
            }
            matCargospacePojo.setCreateby(loginUser.getRealname());   // 创建者
            matCargospacePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matCargospacePojo.setCreatedate(new Date());   // 创建时间
            matCargospacePojo.setLister(loginUser.getRealname());   // 制表
            matCargospacePojo.setListerid(loginUser.getUserid());    // 制表id  
            matCargospacePojo.setModifydate(new Date());   //修改时间
            matCargospacePojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.matCargospaceService.insert(matCargospacePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改仓库货位", notes = "修改仓库货位", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_CargoSpace.Edit")
    public R<MatCargospacePojo> update(@RequestBody String json) {
        try {
            MatCargospacePojo matCargospacePojo = JSONArray.parseObject(json, MatCargospacePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            matCargospacePojo.setLister(loginUser.getRealname());   // 制表
            matCargospacePojo.setListerid(loginUser.getUserid());    // 制表id  
            matCargospacePojo.setTenantid(loginUser.getTenantid());   //租户id
            matCargospacePojo.setModifydate(new Date());   //修改时间
//            matCargospacePojo.setAssessor(""); // 审核员
//            matCargospacePojo.setAssessorid(""); // 审核员id
//            matCargospacePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.matCargospaceService.update(matCargospacePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除仓库货位", notes = "删除仓库货位", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CargoSpace.Delete")
    //@OperLog(title = "删除仓库货位")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matCargospaceService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CargoSpace.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        MatCargospacePojo matCargospacePojo = this.matCargospaceService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(matCargospacePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取XML内容
        String xml = saRedisService.getValue("report_codes:" + ptid);
        String content = "";
        if (xml != null || !"".equals(xml) || xml != "") {
            content = xml;
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "导入数据", notes = "导入数据", produces = "application/json")
    @RequestMapping(value = "/importExecl", method = RequestMethod.POST)
    public R<List<MatCargospacePojo>> importExecl(String groupid, MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //去掉标题和表头
            ImportParams importParams = POIUtil.createImportParams(0, 1);
            //将表格数据写入List
            List<MatCargospacePojo> list = POIUtil.importExcel(file.getInputStream(), MatCargospacePojo.class, importParams);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "模板导出", notes = "模板导出", produces = "application/json")
    @RequestMapping(value = "/exportModel", method = RequestMethod.GET)
    public void exportModel(HttpServletRequest request, HttpServletResponse response) {
        List<MatCargospacePojo> list = new ArrayList<>();
        //创建表格
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("库位信息", ""),
                MatCargospacePojo.class, list);
        try {
            //下载模板
            POIUtil.downloadWorkbook(workbook, request, response, "库位信息模板");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}

