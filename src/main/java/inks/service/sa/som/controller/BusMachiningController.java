package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AmountUtils;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.som.domain.constant.MyConstant;
import inks.service.sa.som.domain.pojo.BusMachiningPojo;
import inks.service.sa.som.domain.pojo.BusMachiningitemPojo;
import inks.service.sa.som.domain.pojo.BusMachiningitemdetailPojo;
import inks.service.sa.som.domain.pojo.MatSpecorderitemPojo;
import inks.service.sa.som.mapper.AppWorkgroupMapper;
import inks.service.sa.som.mapper.BusMachiningMapper;
import inks.service.sa.som.mapper.BusMachiningitemMapper;
import inks.service.sa.som.service.BusMachiningService;
import inks.service.sa.som.service.BusMachiningitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;
import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;

/**
 * 销售订单(BusMachining)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-25 15:00:11
 */

public class BusMachiningController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(BusMachiningController.class);

    @Resource
    private BusMachiningService busMachiningService;

    @Resource
    private BusMachiningitemService busMachiningitemService;
    @Resource
    private BusMachiningitemMapper busMachiningitemMapper;

    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaBillcodeService saBillcodeService;


    @Resource
    private BusMachiningMapper busMachiningMapper;
    @Resource
    private AppWorkgroupMapper appWorkgroupMapper;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取销售订单详细信息", notes = "获取销售订单详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<BusMachiningPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busMachiningService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "打印销售订单PageTh报表(分页)", notes = "打印PageTh报表", produces = "application/json")
    @RequestMapping(value = "/printPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public void printPageTh(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Bus_Machining.CreateDate");
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BusMachiningPojo> lst = this.busMachiningService.getPageTh(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusMachiningPojo pojo = new BusMachiningPojo();
                    lst.add(pojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningitemdetailPojo>> getPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.busMachiningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取销售订单详细信息", notes = "获取销售订单详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<BusMachiningPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busMachiningService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningPojo>> getBillList(@RequestBody String json, Integer online) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpFilter = "";
            if (online != null && online == 1) {
                qpFilter += " and Bus_Machining.FinishCount<Bus_Machining.ItemCount";
            }
            queryParam.setFilterstr(qpFilter);
            return R.ok(this.busMachiningService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<PageInfo<BusMachiningPojo>> getPageTh(@RequestBody String json, Integer online, String seller) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.CreateDate");
            if (queryParam.getDateRange() != null && (queryParam.getDateRange().getDateColumn() == null || "".equals(queryParam.getDateRange().getDateColumn())))
                queryParam.getDateRange().setDateColumn("Bus_Machining.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpFilter = "";
            if (online != null && online == 1) {
                qpFilter += " and Bus_Machining.FinishCount<Bus_Machining.ItemCount";
            }
            if (StringUtils.isNotBlank(seller)) {
                List<String> groupIdsBySeller = appWorkgroupMapper.getGroupIdsBySeller(seller, loginUser.getTenantid());
                qpFilter += " and Bus_Machining.Groupid in (select distinct id from App_Workgroup where Seller='" + seller + "' and Tenantid='" + loginUser.getTenantid() + "')";
            }
            qpFilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpFilter);
            return R.ok(this.busMachiningService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增销售订单", notes = "新增销售订单", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Add")
    public R<BusMachiningPojo> create(@RequestBody String json) {
        try {
            BusMachiningPojo busMachiningPojo = JSONArray.parseObject(json, BusMachiningPojo.class);
            // item转json,校验各项金额相差不能大于1
            String lessThanOne = AmountUtils.lstLessThanOne(JSONObject.toJSONString(busMachiningPojo.getItem()));
            PrintColor.red("=========lessThanOne校验各项金额相差不能大于1:" + lessThanOne);
            if (lessThanOne != null) return R.fail(lessThanOne);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D01M03B1", loginUser.getTenantid(), "Bus_Machining");
            busMachiningPojo.setRefno(refNo);
            busMachiningPojo.setCreateby(loginUser.getRealName());   // 创建者
            busMachiningPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busMachiningPojo.setCreatedate(new Date());   // 创建时间
            busMachiningPojo.setLister(loginUser.getRealname());   // 制表
            busMachiningPojo.setListerid(loginUser.getUserid());    // 制表id            
            busMachiningPojo.setModifydate(new Date());   //修改时间
            busMachiningPojo.setTenantid(loginUser.getTenantid());   //租户id

            return R.ok(this.busMachiningService.insert(busMachiningPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改销售订单", notes = "修改销售订单", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Edit")
    public R<BusMachiningPojo> update(@RequestBody String json) {
        try {
            BusMachiningPojo busMachiningPojo = JSONArray.parseObject(json, BusMachiningPojo.class);
            // item转json,校验各项金额相差不能大于1
            String lessThanOne = AmountUtils.lstLessThanOne(JSONObject.toJSONString(busMachiningPojo.getItem()));
            PrintColor.red("=========lessThanOne校验各项金额相差不能大于1:" + lessThanOne);
            if (lessThanOne != null) return R.fail(lessThanOne);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            busMachiningPojo.setLister(loginUser.getRealname());   // 制表
            busMachiningPojo.setListerid(loginUser.getUserid());    // 制表id   
            busMachiningPojo.setModifydate(new Date());   //修改时间
            busMachiningPojo.setAssessor(""); //审核员
            busMachiningPojo.setAssessorid(""); //审核员
            busMachiningPojo.setAssessdate(new Date()); //审核时间
            busMachiningPojo.setTenantid(loginUser.getTenantid());   //租户id

            return R.ok(this.busMachiningService.update(busMachiningPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除销售订单", notes = "删除销售订单", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.Delete")
    //@OperLog(title = "删除销售订单")
    public R<BusMachiningPojo> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BusMachiningPojo delPojo = this.busMachiningService.getEntity(key, loginUser.getTenantid());
            //检查引用
            List<BusMachiningitemPojo> lst = this.busMachiningitemService.getList(key, loginUser.getTenantid());
            for (BusMachiningitemPojo item : lst) {
                List<String> lstcite = this.busMachiningService.getItemCiteBillName(item.getId(), item.getPid(), loginUser.getTenantid());
                if (!lstcite.isEmpty()) {
                    return R.fail("禁止删除,被以下单据引用:" + lstcite);
                }
            }
            this.busMachiningService.delete(key, loginUser.getTenantid());
            return R.ok(delPojo, delPojo.getRefno() + "删除完成");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增销售订单Item", notes = "新增销售订单Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Add")
    public R<BusMachiningitemPojo> createItem(@RequestBody String json) {
        try {
            BusMachiningitemPojo busMachiningitemPojo = JSONArray.parseObject(json, BusMachiningitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            busMachiningitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.busMachiningitemService.insert(busMachiningitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除销售订单Item", notes = "删除销售订单Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busMachiningitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核销售订单", notes = "审核销售订单", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.Approval")
    public R<BusMachiningPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(key, loginUser.getTenantid());
            if (busMachiningPojo.getAssessor().equals("")) {
                busMachiningPojo.setAssessor(loginUser.getRealname()); //审核员
                busMachiningPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                busMachiningPojo.setAssessor(""); //审核员
                busMachiningPojo.setAssessorid(""); //审核员
            }
            busMachiningPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busMachiningService.approval(busMachiningPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 作废单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "作废销售订单", notes = "作废销售订单,?type=1作废，0为反作废", produces = "application/json")
    @RequestMapping(value = "/disannul", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Edit")
    public R<BusMachiningPojo> disannul(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<BusMachiningitemPojo> lst = JSONArray.parseArray(json, BusMachiningitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //  BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(lst.get(0).getPid(), loginUser.getTenantid());

            return R.ok(this.busMachiningService.disannul(lst, type, loginUser));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 作废单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "中止销售订单", notes = "中止销售订单,?type=1中止，0为反作废", produces = "application/json")
    @RequestMapping(value = "/closed", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Edit")
    //@OperLog(title = "中止销售订单", businessType = BusinessType.UPDATE)
    public R<BusMachiningPojo> closed(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<BusMachiningitemPojo> lst = JSONArray.parseArray(json, BusMachiningitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busMachiningService.closed(lst, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表 key=billid,ptid打印模版", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BusMachiningPojo busMachiningPojo = this.busMachiningService.getBillEntity(key, loginUser.getTenantid());


        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busMachiningPojo);
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = busMachiningPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusMachiningitemPojo busMachiningitemPojo = new BusMachiningitemPojo();
                    busMachiningPojo.getItem().add(busMachiningitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(busMachiningPojo.getItem());
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);

        // 刷入打印Num++
        BusMachiningPojo billPrintPojo = new BusMachiningPojo();
        billPrintPojo.setId(busMachiningPojo.getId());
        billPrintPojo.setPrintcount(busMachiningPojo.getPrintcount() + 1);
        billPrintPojo.setTenantid(busMachiningPojo.getTenantid());
        this.busMachiningService.updatePrintcount(billPrintPojo);

        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 云打印报表
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(key, loginUser.getTenantid());


            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(busMachiningPojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<BusMachiningitemPojo> lstitem = this.busMachiningitemService.getList(key, loginUser.getTenantid());
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrcostListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "销售订单" + busMachiningPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setKeyValue("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限

            // 刷入打印Num++
            BusMachiningPojo billPrintPojo = new BusMachiningPojo();
            billPrintPojo.setId(busMachiningPojo.getId());
            billPrintPojo.setPrintcount(busMachiningPojo.getPrintcount() + 1);
            billPrintPojo.setTenantid(busMachiningPojo.getTenantid());
            this.busMachiningService.updatePrintcount(billPrintPojo);

            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),{ids:xxx}", produces = "application/json")
    @RequestMapping(value = "/printWebItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public R<String> printWebItem(String key, String ptid, String sn, Integer cmd, @RequestBody String json, Integer redis) {
        try {
            Map<String, Object> mapids = JSONArray.parseObject(json, Map.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //获取单据信息
            BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(key, loginUser.getTenantid());
//            //=========获取单据表头信息========
//            BusMachiningitemPojo busMachiningitemPojo = this.busMachiningitemService.getEntity(key, loginUser.getTenantid());
//            //=========获取单据表头信息========
//            BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(busMachiningitemPojo.getPid(), loginUser.getTenantid());

            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(busMachiningPojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<BusMachiningitemPojo> lstitem = this.busMachiningService.getItemListByIds(mapids.get("ids").toString(), key, loginUser.getTenantid());
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);
            // 将costgroupjson转为Map
            for (Map<String, Object> map2 : lst) {
                if (map2.get("costgroupjson") != null && !map2.get("costgroupjson").toString().isEmpty()) {
                    List<Map<String, Object>> listObjectSec = JSONArray.parseObject(map2.get("costgroupjson").toString(), List.class);
                    for (Map<String, Object> mapList : listObjectSec) {
                        if (mapList.get("key") != null && mapList.get("value") != null)
                            map2.put(mapList.get("key").toString(), mapList.get("value").toString());
                    }
                }
            }
            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "销售订单" + busMachiningPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setKeyValue("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表 key=billid,ptid打印模版,{ids:xxx}", produces = "application/json")
    @RequestMapping(value = "/printItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public void printItem(String key, String ptid, @RequestBody String json) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        Map<String, Object> mapids = JSONArray.parseObject(json, Map.class);
        //获取单据信息
        BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(key, loginUser.getTenantid());
        busMachiningPojo.setItem(this.busMachiningService.getItemListByIds(mapids.get("ids").toString(), key, loginUser.getTenantid()));

        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busMachiningPojo);
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = busMachiningPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusMachiningitemPojo busMachiningitemPojo = new BusMachiningitemPojo();
                    busMachiningPojo.getItem().add(busMachiningitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(busMachiningPojo.getItem());

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);

        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "批量打印单据", notes = "json={KEY,KEY},ptid打印模版", produces = "application/json")
    @RequestMapping(value = "/printBatchBillStart", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public R<String> printBatchBillStart(@RequestBody String json, String ptid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                throw new BaseBusinessException("未找到报表");
            }

            // 单据id集
            List<String> ids = JSONArray.parseArray(json, String.class);
            // id
            String uuid = UUID.randomUUID().toString();
            // 开始异常
            this.busMachiningService.printBatchBillStart(ids, uuid, "true", reportsPojo, loginUser);
            return R.ok(uuid);
        } catch (Exception ex) {
            return R.fail(ex.getMessage());
        }

    }

    @ApiOperation(value = "获取批量打印状态", notes = "获取批量打印状态，key=?", produces = "application/json")
    @RequestMapping(value = "/getPrintBatchBillState", method = RequestMethod.GET)
    public R<Map<String, Object>> getPrintBatchBillState(@RequestParam String key) {
        Map<String, Object> PrintState = this.saRedisService.getCacheObject(MyConstant.PRINTBATCH_STATE + key, Map.class);
        return R.ok(PrintState);
    }


//    @ApiOperation(value = "显示批量打印单据PDF", notes = "显示批量打印单据PDF,key=?", produces = "application/json")
//    @RequestMapping(value = "/getPrintBatchBillResult", method = RequestMethod.GET)
//    public void getPrintBatchBillResult(@RequestParam String key) throws IOException, JRException {
//        HttpServletResponse response = ServletUtils.getResponse();
//        ServletOutputStream os = response.getOutputStream();
//        try {
//            // Redis的byte[]
//            String cachekey = "report_pages:" + key;
//            byte[] base64file = this.saRedisService.getValue(cachekey);
//            // Byte[]转打印报表
//            Object objPrintAll = StreamUtils.toObject(base64file);
//            JasperPrint printAll = (JasperPrint) objPrintAll;
//            //打印PDF数据流
//            JasperExportManager.exportReportToPdfStream(printAll, os);
//        } catch (JRException e) {
//            e.printStackTrace();
//        } catch (BaseBusinessException base) {
//            base.getMessage();
//        } finally {
//            os.flush();
//        }
//    }

    @ApiOperation(value = "云打印BusMachining明细报表(分页PageList)", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public R<String> printWebPageList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis, Integer online) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            if (online != null && online == 1) {
                qpfilter = " and Bus_MachiningItem.FinishQty<Bus_MachiningItem.Quantity";
                qpfilter += " and Bus_MachiningItem.DisannulMark=0 and Bus_MachiningItem.Closed=0 ";  // 未关闭、未注销
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BusMachiningitemdetailPojo> lstitem = this.busMachiningService.getPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "BusMachining明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setKeyValue("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "批量云打印报表(List<BusMachiningPojo>不带item)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),groupid不为null则只获取第一条客户信息", produces = "application/json")
    @RequestMapping(value = "/printWebPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public R<String> printWebPageTh(@RequestBody String json, String ptid, String groupid, String sn, Integer cmd, Integer redis, Integer online) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Machining.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Machining.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            if (online != null && online == 1) {
                qpfilter = " and Bus_Machining.FinishCount+Bus_Machining.DisannulCount<Bus_Machining.ItemCount";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BusMachiningPojo> lstTh = this.busMachiningService.getPageTh(queryParam).getList();
            // 获取单据表头.表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (groupid != null && lstTh.size() > 0) {
                map.put("groupname", lstTh.get(0).getGroupname());
                map.put("abbreviate", lstTh.get(0).getAbbreviate());
                map.put("groupuid", lstTh.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstTh);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "wip批量打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setKeyValue("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "批量云打印BusMachiningItem+工艺卡List单据(ids)", notes = "json={KEY,KEY},ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printSpecBatchWebBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public R<String> printSpecBatchWebBill(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<String> lstptJson = new ArrayList<>();
//            String ptRefNoMain = "";


            for (String key : lstkeys) {
                //=========获取单据表头信息========
                QueryParam queryParam = new QueryParam();
                queryParam.setFilterstr(" and Bus_MachiningItem.id =" + key);
                queryParam.setTenantid(loginUser.getTenantid());
                queryParam.setOrderBy("Bus_Machining.CreateDate");
                List<BusMachiningitemdetailPojo> BusMachiningitemdetailPojos = this.busMachiningMapper.getPageList(queryParam);
                if (CollectionUtils.isEmpty(BusMachiningitemdetailPojos)) {
                    throw new BaseBusinessException(key + ":无效单据,刷新后再试");
                }
                BusMachiningitemdetailPojo BusMachiningitemdetailPojo = BusMachiningitemdetailPojos.get(0);

                // 获取单据表头.表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(BusMachiningitemdetailPojo);
                // 将attributejson展开转为map
                if (map.get("attributejson") != null && !map.get("attributejson").toString().isEmpty()) {
                    List<Map<String, Object>> listObjectSec = JSONArray.parseObject(map.get("attributejson").toString(), List.class);
                    for (Map<String, Object> mapList : listObjectSec) {
                        if (mapList.get("key") != null && mapList.get("value") != null)
                            map.put(mapList.get("key").toString(), mapList.get("value").toString());
                    }
                }
                // 查询Mat_SpecOrder表,从中获取PnlX、PnlY和Pnl2Pcs字段的值并追加到map中
                Map<String, Object> specOrderMap = busMachiningitemMapper.getSpecOrder(BusMachiningitemdetailPojo.getSpecid(), loginUser.getTenantid());
                map.put("PnlX", specOrderMap.get("PnlX"));
                map.put("PnlY", specOrderMap.get("PnlY"));
                map.put("Pnl2Pcs", specOrderMap.get("Pnl2Pcs"));

                // 获取单据表头.加入公司信息
                PrintUtils.addCompanyInfo(map, loginUser);
                //=========获取单据Item信息========
                List<MatSpecorderitemPojo> lstitem = this.busMachiningitemMapper.getSpecOrderItemList(BusMachiningitemdetailPojo.getSpecid(), loginUser.getTenantid());
                // 单据Item. 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = attrcostListToMaps(lstitem);
                // === 整理Map.row=====
                Map<String, Object> maprow = new LinkedHashMap<>();
                maprow.put("row", lst);
                // === 整理report=xml+grparam=====
                Map<String, Object> mapreport = new LinkedHashMap<>();
                mapreport.put("xml", maprow);
                mapreport.put("_grparam", map);
                // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                Map<String, Object> mapdata = new LinkedHashMap<>();
                mapdata.put("report", mapreport);
                // ====Map转Json ==== 注 时间转String 格式；
                String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
                lstptJson.add(ptJson);
//                ptRefNoMain += busMachiningitemPojo.getRefno() + ",";
                // 刷入打印Num++
            }
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else {
                mapPrint.put("code", "batchprint");
            }
//            String[] lstRefno = ptRefNoMain.split(",");
            mapPrint.put("msg", "BusMachining工艺卡打印");
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setKeyValue("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            mapPrint.put("ptid", ptid);   // 报表id
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "一页两联打印", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBillMulti", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public R<String> printWebBillMulti(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(key, loginUser.getTenantid());

            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(busMachiningPojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            QueryParam queryParam = new QueryParam();
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setFilterstr(" and Bus_MachiningItem.Pid='" + key + "'");
            queryParam.setOrderBy("Bus_MachiningItem.RowNum");
            queryParam.setPageNum(1);
            queryParam.setPageSize(1000);
            List<BusMachiningitemdetailPojo> lstitem = this.busMachiningService.getPageList(queryParam).getList();
            List<Map<String, Object>> lstMap = attrcostListToMaps(lstitem);
            // 一式两份
            List<Map<String, Object>> lst = lstMap.stream()
                    .map(item -> {
                        Map<String, Object> hashMap = new HashMap<>(item);
                        hashMap.put("ToWho", 1);
                        hashMap.put("PageNo", 1);
                        return hashMap;
                    })
                    .collect(Collectors.toList());
            List<Map<String, Object>> lstCopy = lstMap.stream()
                    .map(item -> {
                        Map<String, Object> hashMap = new HashMap<>(item);
                        hashMap.put("ToWho", 2);
                        hashMap.put("PageNo", 1);
                        return hashMap;
                    })
                    .collect(Collectors.toList());
            lst.addAll(lstCopy);
            //            PrintColor.red("lst:" + lst);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "BusMachining" + busMachiningPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限

            // 刷入打印Num++
            BusMachiningPojo billPrintPojo = new BusMachiningPojo();
            billPrintPojo.setId(busMachiningPojo.getId());
            billPrintPojo.setPrintcount(busMachiningPojo.getPrintcount() + 1);
            billPrintPojo.setTenantid(busMachiningPojo.getTenantid());
            this.busMachiningService.updatePrintcount(billPrintPojo);

            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

