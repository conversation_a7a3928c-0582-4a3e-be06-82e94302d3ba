package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.BuyFinishingPojo;
import inks.service.sa.som.domain.pojo.BuyFinishingitemdetailPojo;
import inks.service.sa.som.service.BuyFinishingService;
import inks.service.sa.som.service.D03M03R1Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

@RestController
@RequestMapping("D03M03R1")
@Api(tags = "D03M03R1:采购收货：全类型")
public class D03M03R1Controller {
    @Resource
    private D03M03R1Service d03M03R1Service;


    @Resource
    private BuyFinishingService buyFinishingService;


    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingitemdetailPojo>> getPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyFinishingService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingPojo>> getPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyFinishingService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineInPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingitemdetailPojo>> getOnlineInPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Buy_FinishingItem.FinishQty<Buy_FinishingItem.Quantity";
            qpfilter += " and Buy_FinishingItem.DisannulMark=0 and Buy_FinishingItem.Closed=0 and Buy_FinishingItem.VirtualItem=0";
            qpfilter += " and Buy_Finishing.BillType in ('采购验收','其他收货')";
            qpfilter += " and Buy_Finishing.Assessor<>''";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyFinishingService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingPojo>> getOnlinePageTh(@RequestBody String json, String groupid, Integer appl) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += " and Buy_Finishing.FinishCount+Buy_Finishing.DisannulCount<Buy_Finishing.ItemCount";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            if (appl != null && appl == 1) {
                qpfilter += " and Buy_Finishing.Assessor<>''";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyFinishingService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineInPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingPojo>> getOnlineInPageTh(@RequestBody String json, String groupid, Integer appl) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Buy_Finishing.BillType in ('采购验收','采购退货')";
            qpfilter += " and Buy_Finishing.FinishCount+Buy_Finishing.DisannulCount<Buy_Finishing.ItemCount";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            if (appl != null && appl == 1) {
                qpfilter += " and Buy_Finishing.Assessor<>''";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyFinishingService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineOutPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingitemdetailPojo>> getOnlineOutPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Buy_FinishingItem.FinishQty<Buy_FinishingItem.Quantity";
            qpfilter += " and Buy_FinishingItem.DisannulMark=0 and Buy_FinishingItem.Closed=0 and Buy_FinishingItem.VirtualItem=0";
            qpfilter += " and Buy_Finishing.BillType in ('采购退货','其他退货')";
            qpfilter += " and Buy_Finishing.Assessorid<>''";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyFinishingService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Buy_FinishingItem.DisannulMark=0 and Buy_FinishingItem.Closed=0 and Buy_FinishingItem.FinishQty<Buy_FinishingItem.Quantity";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyFinishingService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineInvoPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingitemdetailPojo>> getOnlineInvoPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and ABS(Buy_FinishingItem.InvoQty)<ABS(Buy_FinishingItem.Quantity)";
            qpfilter += " and Buy_FinishingItem.DisannulMark=0 and Buy_FinishingItem.InvoClosed=0";
            qpfilter += " and Buy_Finishing.Assessorid<>''";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyFinishingService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePaidPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingPojo>> getOnlinePaidPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Buy_Finishing.BillType in ('采购验收','其他收货')";
            qpfilter += " and Buy_Finishing.InvoCount=0 and Buy_Finishing.BillPaid<Buy_Finishing.BillTaxAmount";
            qpfilter += " and Buy_Finishing.Assessor<>''";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyFinishingService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "采购趋势图年", notes = "采购趋势图年", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByYear", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByYear(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange().getDateColumn() == null || queryParam.getDateRange().getDateColumn().equals(""))
                queryParam.getDateRange().setDateColumn("Bus_Machining.BillDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03M03R1Service.getSumAmtByYear(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "采购趋势图月", notes = "采购趋势图月", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByMonth", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<List<ChartPojo>> getSumAmtByMonth(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null)  //未定义时间
                queryParam.setDateRange(new DateRange("Buy_Finishing.BillDate", DateUtils.addMonths(new Date(), -1), new Date()));
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03M03R1Service.getSumAmtByMonth(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "采购趋势图周", notes = "采购趋势图周", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByDay", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<List<ChartPojo>> getSumAmtByDay(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null)  //未定义时间
                queryParam.setDateRange(new DateRange("Buy_Finishing.BillDate", DateUtils.addMonths(new Date(), -1), new Date()));
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03M03R1Service.getSumAmtByDay(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "采购饼状图年", notes = "采购饼状图年", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByYearMax", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<List<ChartPojo>> getSumAmtByYearMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange().getDateColumn() == null || queryParam.getDateRange().getDateColumn().equals(""))
                queryParam.getDateRange().setDateColumn("Bus_Machining.BillDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03M03R1Service.getSumAmtByYearMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "采购饼状图月", notes = "采购饼状图月", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByMonthMax", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<List<ChartPojo>> getSumAmtByMonthMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null)  //未定义时间
                queryParam.setDateRange(new DateRange("Buy_Finishing.BillDate", DateUtils.addMonths(new Date(), -1), new Date()));
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03M03R1Service.getSumAmtByMonthMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "采购饼状图周", notes = "采购饼状图周", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByDayMax", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<List<ChartPojo>> getSumAmtByDayMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null)  //未定义时间
                queryParam.setDateRange(new DateRange("Buy_Finishing.BillDate", DateUtils.addMonths(new Date(), -1), new Date()));
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03M03R1Service.getSumAmtByDayMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取销售单客户金额排名", notes = "获取销售单客户金额排名", produces = "application/json")
    @RequestMapping(value = "/getSumPageListByGroup", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingitemdetailPojo>> getSumPageListByGroup(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.BillDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03M03R1Service.getSumPageListByGroup(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取销售单客户金额排名", notes = "获取销售单客户金额排名", produces = "application/json")
    @RequestMapping(value = "/getSumPageListByGoods", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingitemdetailPojo>> getSumPageListByGoods(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.BillDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03M03R1Service.getSumPageListByGoods(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 打印单据
     *
     * @param ptid 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印销售对账明细报表", notes = "打印销售对账明细报表", produces = "application/json")
    @RequestMapping(value = "/printAccountList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.Print")
    public void printAccountList(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Buy_Finishing.BillDate");
        queryParam.setTenantid(loginUser.getTenantid());
        String qpfilter = " and Buy_Finishing.Groupid='" + groupid + "'";
        // 加入场景   Eric ********
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        List<BuyFinishingitemdetailPojo> lstDetail = this.buyFinishingService.getPageList(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        map.put("startdate", queryParam.getDateRange().getStartDate());
        map.put("enddate", queryParam.getDateRange().getEndDate());
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        map.put("lister", loginUser.getRealname());
        if (lstDetail.size() > 0) {
            map.put("groupuid", lstDetail.get(0).getGroupuid());
            map.put("groupname", lstDetail.get(0).getGroupname());
            map.put("abbreviate", lstDetail.get(0).getAbbreviate());
        }
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lstDetail.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BuyFinishingitemdetailPojo busDelieryitemPojo = new BuyFinishingitemdetailPojo();
                    lstDetail.add(busDelieryitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(lstDetail);

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param ptid 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印销售对账明细报表", notes = "打印销售对账明细报表", produces = "application/json")
    @RequestMapping(value = "/printAccountTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.Print")
    public void printAccountTh(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Buy_Finishing.BillDate");
        queryParam.setTenantid(loginUser.getTenantid());
        String qpfilter = " and Buy_Finishing.Groupid='" + groupid + "'";
        // 加入场景   Eric ********
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        List<BuyFinishingPojo> lstDetail = this.buyFinishingService.getPageTh(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        map.put("startdate", queryParam.getDateRange().getStartDate());
        map.put("enddate", queryParam.getDateRange().getEndDate());
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        map.put("lister", loginUser.getRealname());
        if (lstDetail.size() > 0) {
            map.put("groupuid", lstDetail.get(0).getGroupuid());
            map.put("groupname", lstDetail.get(0).getGroupname());
            map.put("abbreviate", lstDetail.get(0).getAbbreviate());
        }
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lstDetail.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BuyFinishingPojo busDelieryPojo = new BuyFinishingPojo();
                    lstDetail.add(busDelieryPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(lstDetail);

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "批量云打印报表 销售对账明细报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),groupid不为null则只获取第一条客户信息", produces = "application/json")
    @RequestMapping(value = "/printWebAccountTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.Print")
    public R<String> printWebAccountTh(@RequestBody String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BuyFinishingPojo> lstTh = this.buyFinishingService.getPageTh(queryParam).getList();
            // 获取单据表头.表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (groupid != null && lstTh.size() > 0) {
                map.put("groupname", lstTh.get(0).getGroupname());
                map.put("abbreviate", lstTh.get(0).getAbbreviate());
                map.put("groupuid", lstTh.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstTh);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "Buy_Finishing批量打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setKeyValue("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
