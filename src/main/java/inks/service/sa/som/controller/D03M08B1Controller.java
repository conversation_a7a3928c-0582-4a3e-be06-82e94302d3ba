package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.AppWgSupplierPojo;
import inks.service.sa.som.domain.pojo.BuyAccountPojo;
import inks.service.sa.som.domain.pojo.BuyAccountitemPojo;
import inks.service.sa.som.domain.pojo.BuyAccountrecPojo;
import inks.service.sa.som.service.BuyAccountService;
import inks.service.sa.som.service.BuyAccountitemService;
import inks.service.sa.som.service.BuyAccountrecService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 应付账单(BuyAccount)表控制层
 *
 * <AUTHOR>
 * @since 2022-06-09 08:28:58
 */
@RestController
@RequestMapping("D03M08B1")
@Api(tags = "D03M08B1:采购账单")
public class D03M08B1Controller extends BuyAccountController {

    @Resource
    private BuyAccountrecService buyAccountrecService;

    @Resource
    private BuyAccountService buyAccountService;


    @Resource
    private BuyAccountitemService buyAccountitemService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Account.List")
    public R<PageInfo<BuyAccountPojo>> getPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Account.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Buy_Account.groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyAccountService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取销售账单详细信息", notes = "获取销售账单详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByNew", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Account.List")
    public R<BuyAccountPojo> getBillEntityByNew(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BuyAccountPojo buyAccountPojo = new BuyAccountPojo();
            BuyAccountrecPojo busAccountrecPojo = this.buyAccountrecService.getEntityByMax(loginUser.getTenantid());
            buyAccountPojo.setTenantid(loginUser.getTenantid());
            buyAccountPojo.setGroupid(key);
            if (busAccountrecPojo == null) {
                buyAccountPojo.setStartdate(DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-01-01 00:00:00", new Date())));
            } else {
                buyAccountPojo.setStartdate(DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1));
            }
            buyAccountPojo.setEnddate(new Date());
            List<BuyAccountitemPojo> lst = this.buyAccountService.pullItemList(buyAccountPojo);
            buyAccountPojo.setItem(lst);
            return R.ok(buyAccountPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "拉取采购账单item", notes = "拉取采购账单item,传账单主表", produces = "application/json")
    @RequestMapping(value = "/pullItemList", method = RequestMethod.POST)
    public R<List<BuyAccountitemPojo>> pullItemList(@RequestBody String json) {
        try {
            BuyAccountPojo busAccountPojo = JSONArray.parseObject(json, BuyAccountPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            busAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.buyAccountService.pullItemList(busAccountPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getInitPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Account.List")
    public R<PageInfo<BuyAccountPojo>> getInitPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Account.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "and Buy_Account.BillType='期初建账'";
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyAccountService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按年月分页查询", notes = "按年月分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageThByMonth", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Account.List")
    public R<PageInfo<BuyAccountPojo>> getPageThByMonth(@RequestBody String json, Integer year, Integer month) {
        try {
            if (year == null || month == null) return R.fail("请传year和month参数");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Account.RowNum");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and Buy_Account.carryyear=" + year + " and Buy_Account.carrymonth=" + month);
            return R.ok(this.buyAccountService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = " 获取结转记录最新记录", notes = "获取结转记录最新记录, key为 Groupid", produces = "application/json")
    @RequestMapping(value = "/getMaxEntityByGroup", method = RequestMethod.GET)
    public R<BuyAccountPojo> getMaxEntityByGroup(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyAccountService.getMaxEntityByGroup(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "获取结转记录最新记录", notes = "获取结转记录最新记录, key为 Groupid", produces = "application/json")
    @RequestMapping(value = "/getMaxBillEntityByGroup", method = RequestMethod.GET)
    public R<BuyAccountPojo> getMaxBillEntityByGroup(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyAccountService.getMaxBillEntityByGroup(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "跨期拉取采购对账item", notes = "跨期拉取采购对账item,包含已对账和未对账", produces = "application/json")
    @RequestMapping(value = "/getBuyAccountList", method = RequestMethod.POST)
    public R<List<BuyAccountitemPojo>> getBuyAccountList(@RequestBody String json, String groupid) {
        try {
            List<BuyAccountitemPojo> lstRt = new ArrayList<>();
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            Date _endDate = queryParam.getDateRange().getEndDate();
            // 当前结算到哪一时间
            BuyAccountrecPojo buyAccountrecPojo = this.buyAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (buyAccountrecPojo != null) {
                queryParam.getDateRange().setEndDate(buyAccountrecPojo.getEnddate());
                queryParam.setFilterstr(" and Buy_Account.Groupid='" + groupid + "'");
                List<BuyAccountitemPojo> lstAccoitem = this.buyAccountService.getMultItemList(queryParam);
                if (lstAccoitem != null) {
                    for (int i = 0; i < lstAccoitem.size(); i++) {
                        BuyAccountitemPojo item = lstAccoitem.get(i);
                        BuyAccountitemPojo rtitem = new BuyAccountitemPojo();
                        BeanUtils.copyProperties(item, rtitem);
                        if (i > 0) rtitem.setOpenamount(0D);  //去掉中间的期初；
                        lstRt.add(rtitem);
                    }
                }
            }

            BuyAccountPojo buyAccountPojo = new BuyAccountPojo();
            buyAccountPojo.setTenantid(loginUser.getTenantid());
            buyAccountPojo.setGroupid(groupid);
            if (buyAccountrecPojo != null) {
                buyAccountPojo.setStartdate(DateUtils.addSeconds(buyAccountrecPojo.getEnddate(), 1));
            }
            buyAccountPojo.setEnddate(_endDate);
            List<BuyAccountitemPojo> lstpullItem = this.buyAccountService.pullItemList(buyAccountPojo);

            if (lstRt.size() > 0) {
                if (lstpullItem != null) {
                    lstRt.get(lstRt.size() - 1).setCloseamount(0D);  //去掉中间的期末；
                    for (int i = 0; i < lstpullItem.size(); i++) {
                        BuyAccountitemPojo item = lstpullItem.get(i);
                        BuyAccountitemPojo rtitem = new BuyAccountitemPojo();
                        BeanUtils.copyProperties(item, rtitem);
                        if (i == 0) rtitem.setOpenamount(0D); //去掉中间的期初；
                        lstRt.add(rtitem);
                    }
                }
                return R.ok(lstRt);
            } else {
                return R.ok(lstpullItem);
            }

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param json 主键
     * @return 单条数据
     */
    @ApiOperation(value = "获取供应商实时应付款报表", notes = "获取供应商实时应付款报表", produces = "application/json")
    @RequestMapping(value = "/getNowPageList", method = RequestMethod.POST)
    public R<PageInfo<BuyAccountPojo>> getNowPageList(@RequestBody String json, Integer online) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("GroupUid");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BuyAccountrecPojo busAccountrecPojo = this.buyAccountrecService.getEntityByMax(loginUser.getTenantid());
            Date startDate = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy", new Date()) + "-01-01");
            Date endDate = new Date();
            if (busAccountrecPojo != null) {
                startDate = DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1);
            }
            DateRange dateRange = new DateRange("", startDate, endDate);
            queryParam.setDateRange(dateRange);
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            PageInfo<BuyAccountPojo> nowPageList = this.buyAccountService.getNowPageList(queryParam, online);
//            if (online != null && online==1) {
//                List<BuyAccountPojo> filteredList = nowPageList.getList()
//                        .stream()
//                        .filter(pojo -> pojo.getBillcloseamount() > 0)
//                        .collect(Collectors.toList());
//                nowPageList.setList(filteredList);
//            }
            return R.ok(nowPageList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取供应商详细信息", notes = "取供应商详细信息", produces = "application/json")
    @RequestMapping(value = "/getSupplierGeneral", method = RequestMethod.GET)
    public R<AppWgSupplierPojo> getSupplierGeneral(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            AppWgSupplierPojo appWorkgroupPojo = this.buyAccountService.getSupplierGeneral(key, loginUser.getTenantid());
            return R.ok(appWorkgroupPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印列表", notes = "打印列表", produces = "application/json")
    @RequestMapping(value = "/printList", method = RequestMethod.POST)
    public void printList(@RequestBody String json, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取列表信息
        BuyAccountPojo buyAccountPojo = JSONObject.parseObject(json, BuyAccountPojo.class);
        //表头转MAP(空)
        Map<String, Object> map = new HashMap<>();
        map.put("groupname", buyAccountPojo.getGroupname());
        map.put("startdate", buyAccountPojo.getStartdate());
        map.put("enddate", buyAccountPojo.getEnddate());
        //lst转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(buyAccountPojo.getItem());
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

}
