package inks.service.sa.som.controller; /*
 *功能描述
 * <AUTHOR>
 * @date  2021/12/31
 * @param 销售大屏总接口
 */

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.service.D03MBIR1Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("D03MBIR1")
@Api(tags = "D03MBIR1:采购大屏报表")
public class D03MBIR1Controller {
    @Resource
    private D03MBIR1Service d03MBIR1Service;
    /**
     * 引用Token服务
     */
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "供应商金额排名", notes = "供应商金额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupMax", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<List<ChartPojo>> getSumAmtByGroupMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.d03MBIR1Service.getSumAmtByGroupMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "产品排行", notes = "产品排行", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGoodsMax", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<List<ChartPojo>> getSumAmtByGoodsMax(@RequestBody String json) {
        try {
            // 获得用户数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03MBIR1Service.getSumAmtByGoodsMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "采购趋势图年", notes = "采购趋势图年", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByYear", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<List<ChartPojo>> getSumAmtByYear(@RequestBody String json, Integer trend) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03MBIR1Service.getSumAmtByYear(queryParam, trend));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "采购趋势图月", notes = "采购趋势图月", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByMonth", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<List<ChartPojo>> getSumAmtByMonth(@RequestBody String json, Integer trend) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03MBIR1Service.getSumAmtByMonth(queryParam, trend));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "采购趋势图周", notes = "采购趋势图周", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByDay", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<List<ChartPojo>> getSumAmtByDay(@RequestBody String json, Integer trend) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03MBIR1Service.getSumAmtByDay(queryParam, trend));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /*
     *
     * <AUTHOR>
     * @description 本月采购额
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "标签：根据日期范围汇总采购额、货品数量、记录数", notes = "采购额(value)、货品数量(valueb)、明细数(valuec)", produces = "application/json")
    @RequestMapping(value = "/getTagSumAmtQtyByDate", method = RequestMethod.POST)
    public R<ChartPojo> getTagSumAmtQtyByDate(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03MBIR1Service.getTagSumAmtQtyByDate(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    @ApiOperation(value = "应付返回的记录数", notes = "应付返回的记录数", produces = "application/json")
//    @RequestMapping(value = "/getItemCountSumAmtGroupMonth", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Buy_Order.List")
//    public R<List<ChartPojo>> getItemCountSumAmtGroupMonth() {
//        try {
//            // 获得用户数据
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            return R.ok(this.d03MBIR1Service.getItemCountSumAmtGroupMonth(loginUser.getTenantid()));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//    @ApiOperation(value = "预警记录", notes = "预警记录", produces = "application/json")
//    @RequestMapping(value = "/getList", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Buy_Order.List")
//    public R<List<Map<String,Object>>> getList() {
//        try {
//            // 获得用户数据
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            return R.ok(this.d03MBIR1Service.getList(loginUser.getTenantid()));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//
//    @ApiOperation(value = "采购饼状图年", notes = "采购饼状图年", produces = "application/json")
//    @RequestMapping(value = "/getSumAmtByYearMax", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Buy_Order.List")
//    public R<List<ChartPojo>> getSumAmtByYearMax(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            // 获得用户数据
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d03MBIR1Service.getSumAmtByYearMax(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//    @ApiOperation(value = "采购饼状图月", notes = "采购饼状图月", produces = "application/json")
//    @RequestMapping(value = "/getSumAmtByMonthMax", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Deliery.List")
//    public R<List<ChartPojo>> getSumAmtByMonthMax(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            // 获得用户数据
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d03MBIR1Service.getSumAmtByMonthMax(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//    @ApiOperation(value = "采购饼状图周", notes = "采购饼状图周", produces = "application/json")
//    @RequestMapping(value = "/getSumAmtByDayMax", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Deliery.List")
//    public R<List<ChartPojo>> getSumAmtByDayMax(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            // 获得用户数据
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d03MBIR1Service.getSumAmtByDayMax(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//    @ApiOperation(value = "本月采购额", notes = "本月采购额", produces = "application/json")
//    @RequestMapping(value = "/getItemCountByMonth", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Buy_Order.List")
//    public R<List<ChartPojo>> getItemCountByMonth(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            // 获得用户数据
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d03MBIR1Service.getItemCountByMonth(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

}
