package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.BuyInvoiceitemdetailPojo;
import inks.service.sa.som.service.BuyInvoiceService;
import inks.service.sa.som.service.BuyInvoiceitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 采购开票(BuyInvoice)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:01:34
 */
@RestController
@RequestMapping("D03M05B2")
@Api(tags = "D03M05B2:采购开票：其他应付")
public class D03M05B2Controller extends BuyInvoiceController {


    @Resource
    private BuyInvoiceService buyInvoiceService;


    @Resource
    private BuyInvoiceitemService buyInvoiceitemService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private SaBillcodeService saBillcodeService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Invoice.List")
    public R<PageInfo<BuyInvoiceitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Invoice.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Buy_Invoice.DisannulMark=0 and Buy_Invoice.Closed=0 and Buy_Invoice.Paid<Buy_Invoice.TaxAmount";
            if (groupid != null) {
                qpfilter += " and Buy_Invoice.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyInvoiceService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
