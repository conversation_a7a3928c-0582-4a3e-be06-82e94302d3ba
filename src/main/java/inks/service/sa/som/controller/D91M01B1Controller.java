package inks.service.sa.som.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.alibaba.fastjson.TypeReference;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.MatGoodsPojo;
import inks.service.sa.som.service.MatGoodsService;
import inks.service.sa.som.service.MatGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 货品信息(Mat_Goods)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 08:36:12
 */
@RestController
@RequestMapping("D91M01B1")
@Api(tags = "D91M01B1:货品信息")
public class D91M01B1Controller extends MatGoodsController {


    @Resource
    private MatGoodsService matGoodsService;


    @Resource
    private SaRedisService saRedisService;


    @Resource
    private MatGroupService matGroupService;

    @ApiOperation(value = "根据料号获取货品信息", notes = "根据料号获取货品信息", produces = "application/json")
    @RequestMapping(value = "/getMapEntityByGoodsUid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<Map<String, Object>> getMapEntityByGoodsUid(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            MatGoodsPojo matGoods = this.matGoodsService.getEntityByGoodsUid(key, loginUser.getTenantid());
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(matGoods);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据料号获取货品信息", notes = "根据料号获取货品信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByGoodsUid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<MatGoodsPojo> getEntityByGoodsUid(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            MatGoodsPojo matGoods = this.matGoodsService.getEntityByGoodsUid(key, loginUser.getTenantid());
            return R.ok(matGoods);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据goodsname+goodsspec获取货品信息", notes = "根据料号获取货品信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByNameSpec", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<MatGoodsPojo> getEntityByNameSpec(@RequestBody String json) {
        try {
            MatGoodsPojo matGoodsPojo = JSONObject.parseObject(json, MatGoodsPojo.class);
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            MatGoodsPojo matGoods = this.matGoodsService.getEntityByNameSpec(matGoodsPojo.getGoodsname(), matGoodsPojo.getGoodsspec(), loginUser.getTenantid());
            return R.ok(matGoods);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据goodsname+goodsspec+Partid获取货品信息", notes = "根据料号获取货品信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByNameSpecPart", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<MatGoodsPojo> getEntityByNameSpecPart(@RequestBody String json) {
        try {
            MatGoodsPojo matGoodsPojo = JSONObject.parseObject(json, MatGoodsPojo.class);
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            MatGoodsPojo matGoods = this.matGoodsService.getEntityByNameSpecPart(matGoodsPojo.getGoodsname(), matGoodsPojo.getGoodsspec(), matGoodsPojo.getPartid(), loginUser.getTenantid());
            return R.ok(matGoods);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "根据外部编码获取最新货品信息", notes = "根据外部编码获取最新货品信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByPartid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<MatGoodsPojo> getEntityByPartid(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatGoodsPojo matGoods = this.matGoodsService.getEntityByPartid(key, loginUser.getTenantid());
            return R.ok(matGoods);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "参数type  0：goods，1：order ;弹窗让客户选择 【默认厂商】 来源 goods,【末次订单】来源上一个采购订单；", notes = "", produces = "application/json")
    @RequestMapping(value = "/getSupplierByGood", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<List<Map<String, Object>>> getSupplierByGood(@RequestBody String json,@RequestParam(defaultValue = "0") Integer type) {
        try {
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            List<String> goodsidList = JSON.parseObject(json, new TypeReference<List<String>>() {});

            // 返回3个字段 goodsid,groupid,groupname
            List<Map<String, Object>> groupAndGoodsList = this.matGoodsService.getSupplierByGood(goodsidList, type);
            return R.ok(groupAndGoodsList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "导出数据", notes = "导出数据", produces = "application/json")
    @RequestMapping(value = "/exportList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public void exportList(@RequestBody String json, String groupid, HttpServletRequest request, HttpServletResponse response) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "") {
                queryParam.setOrderBy("CreateDate");
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.deletemark=0";
            if (groupid != null) {
                qpfilter += " and Mat_Goods.Groupid='" + groupid + "'";
            }
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("货品案档", ""),
                    MatGoodsPojo.class, this.matGoodsService.getPageList(queryParam).getList());
            POIUtil.downloadWorkbook(workbook, request, response, "货品案档");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "按条件分页查询不包括指定货品", notes = "按条件分页查询不包括指定货品", produces = "application/json")
    @RequestMapping(value = "/getPageListUseBom", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<PageInfo<MatGoodsPojo>> getPageListUseBom(@RequestBody String json, String key) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            qpfilter += " and Mat_Goods.GoodsState<>'成品' and Mat_Goods.id<>'" + key + "'";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询 安全库存预警", notes = "按条件分页查询 安全库存预警", produces = "application/json")
    @RequestMapping(value = "/getSafetyPageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getSafetyPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            qpfilter += " and Mat_Goods.IvQuantity<Mat_Goods.SafeStock and Mat_Goods.enabledmark=1";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询 预设数量预警", notes = "按条件分页查询 安全库存预警", produces = "application/json")
    @RequestMapping(value = "/getAlertsQtyPageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getAlertsQtyPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            qpfilter += " and Mat_Goods.IvQuantity<Mat_Goods.AlertsQty and Mat_Goods.enabledmark=1";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据快速码获取最新货品信息", notes = "根据快速码获取最新货品信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByCode", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<MatGoodsPojo> getEntityByCode(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatGoodsPojo matGoods = this.matGoodsService.getEntityByQuickCode(key, loginUser.getTenantid());
            return R.ok(matGoods);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询货品", notes = "按条件分页查询货品,state m物料,s半成品,p为成品", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<PageInfo<MatGoodsPojo>> getPageList(@RequestBody String json, String state) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || Objects.equals(queryParam.getOrderBy(), ""))
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            if (state != null) {
                String statetype = "";
                if (state.contains("m"))
                    statetype += "'物料',";
                if (state.contains("s"))
                    statetype += "'半成品',";
                if (state.contains("p"))
                    statetype += "'成品',";
                if (!statetype.isEmpty()) {
                    statetype = statetype.substring(0, statetype.length() - 1);
                }
                qpfilter += " and Mat_Goods.GoodsState IN (" + statetype + ")";
            }
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "没有Bomid的:按条件分页查询货品", notes = "按条件分页查询货品,state m物料,s半成品,p为成品", produces = "application/json")
    @RequestMapping(value = "/getNoBomPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<PageInfo<MatGoodsPojo>> getNoBomPageList(@RequestBody String json, String state) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || Objects.equals(queryParam.getOrderBy(), ""))
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            if (state != null) {
                String statetype = "";
                if (state.contains("m"))
                    statetype += "'物料',";
                if (state.contains("s"))
                    statetype += "'半成品',";
                if (state.contains("p"))
                    statetype += "'成品',";
                if (!statetype.isEmpty()) {
                    statetype = statetype.substring(0, statetype.length() - 1);
                }
                qpfilter += " and Mat_Goods.GoodsState IN (" + statetype + ")";
            }
            qpfilter += " and (Mat_Goods.Bomid is null or Mat_Goods.Bomid='')";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询有效货品", notes = "按条件分页查询货品,state m物料,s半成品,p为成品", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<PageInfo<MatGoodsPojo>> getOnlinePageList(@RequestBody String json, String state) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0 and Mat_Goods.EnabledMark=1";
            if (state != null) {
                String statetype = "";
                if (state.contains("m"))
                    statetype += "'物料',";
                if (state.contains("s"))
                    statetype += "'半成品',";
                if (state.contains("p"))
                    statetype += "'成品',";
                if (!statetype.isEmpty()) {
                    statetype = statetype.substring(0, statetype.length() - 1);
                }
                qpfilter += " and Mat_Goods.GoodsState IN (" + statetype + ")";
            }
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "未转作业指示的货品", notes = "按条件分页查询货品,state m物料,s半成品,p为成品", produces = "application/json")
    @RequestMapping(value = "/getOnlineSpecPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.List")
    public R<PageInfo<MatGoodsPojo>> getOnlineSpecPageList(@RequestBody String json, @RequestParam(required = false) String state) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || Objects.equals(queryParam.getOrderBy(), ""))
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0 and Mat_Goods.EnabledMark=1";
            qpfilter += " and Mat_Goods.Specid=''"; //未转作业指示的货品
            if (state != null) {
                String statetype = "";
                if (state.contains("m"))
                    statetype += "'物料',";
                if (state.contains("s"))
                    statetype += "'半成品',";
                if (state.contains("p"))
                    statetype += "'成品',";
                if (!statetype.isEmpty()) {
                    statetype = statetype.substring(0, statetype.length() - 1);
                }
                qpfilter += " and Mat_Goods.GoodsState IN (" + statetype + ")";
            }
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询 产成品信息", notes = "按条件分页查询 产成品（成品与半成品）", produces = "application/json")
    @RequestMapping(value = "/getProPageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getProPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            qpfilter += " and (Mat_Goods.GoodsState='成品' or Mat_Goods.GoodsState='半成品')";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询 物料信息", notes = "按条件分页查询 物料信息", produces = "application/json")
    @RequestMapping(value = "/getMatPageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getMatPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            qpfilter += " and Mat_Goods.GoodsState='物料'";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "批量更新货品库存总量", notes = "批量更新货品库存总量", produces = "application/json")
    @RequestMapping(value = "/updateIvQty", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateIvQty() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateIvQty(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询 虚拟品", notes = "按条件分页查询 虚拟品", produces = "application/json")
    @RequestMapping(value = "/getVirPageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getVirPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=1";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询 有效虚拟品", notes = "按条件分页查询 虚拟品", produces = "application/json")
    @RequestMapping(value = "/getVirOnlinePageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getVirOnlinePageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Goods.VirtualItem=1 and Mat_Goods.EnabledMark=1";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询 未检验货品", notes = "按条件分页查询 虚拟品", produces = "application/json")
    @RequestMapping(value = "/getInspOnlinPageList", method = RequestMethod.POST)
    public R<PageInfo<MatGoodsPojo>> getInspOnlinPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            // 已检验货品 inspid有值
            String qpfilter = " and (Mat_Goods.Inspid is null or Mat_Goods.Inspid='') and Mat_Goods.EnabledMark=1";
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增虚似品信息", notes = "新增虚似品信息", produces = "application/json")
    @RequestMapping(value = "/createVir", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Goods.Add")
    public R<MatGoodsPojo> createVir(@RequestBody String json) {
        try {
            MatGoodsPojo matGoodsPojo = JSONArray.parseObject(json, MatGoodsPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            matGoodsPojo.setCreateby(loginUser.getRealname());   // 创建者
            matGoodsPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matGoodsPojo.setCreatedate(new Date());   // 创建时间
            matGoodsPojo.setLister(loginUser.getRealname());   // 制表
            matGoodsPojo.setListerid(loginUser.getUserid());    // 制表id
            matGoodsPojo.setModifydate(new Date());   //修改时间
            matGoodsPojo.setTenantid(loginUser.getTenantid());   //租户id
            matGoodsPojo.setVirtualitem(1);
            return R.ok(this.matGoodsService.insert(matGoodsPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新销售待出数
     * key为goodsid
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新销售待出数", notes = "刷新销售待出数", produces = "application/json")
    @RequestMapping(value = "/updateGoodsBusRemQty", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateGoodsBusRemQty(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateGoodsBusRemQty(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新收货待入数
     * key为goodsid
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新收货待入数", notes = "刷新收货待入数", produces = "application/json")
    @RequestMapping(value = "/updateGoodsBuyRemQty", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateGoodsBuyRemQty(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateGoodsBuyRemQty(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新生产待入数
     * key为goodsid
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新生产待入数", notes = "刷新生产待入数", produces = "application/json")
    @RequestMapping(value = "/updateGoodsWkWsRemQty", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateGoodsWkWsRemQty(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateGoodsWkWsRemQty(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新加工待入数
     * key为goodsid
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新加工待入数", notes = "刷新加工待入数", produces = "application/json")
    @RequestMapping(value = "/updateGoodsWkScRemQty", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateGoodsWkScRemQty(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateGoodsWkScRemQty(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新领料待出数
     * key为goodsid
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新领料待出数", notes = "刷新领料待出数", produces = "application/json")
    @RequestMapping(value = "/updateGoodsRequRemQty", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateGoodsRequRemQty(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateGoodsRequRemQty(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 刷新一个货品的当前库存数和库存单价
     * key为goodsid
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "刷新当前库存数和库存单价", notes = "刷新当前库存数和库存单价", produces = "application/json")
    @RequestMapping(value = "/updateGoodsIvQuantity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Goods.Edit")
    public R<Integer> updateGoodsIvQuantity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matGoodsService.updateGoodsIvQuantity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
