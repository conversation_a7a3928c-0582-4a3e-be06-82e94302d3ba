package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.BusInvoicePojo;
import inks.service.sa.som.domain.pojo.BusInvoiceitemPojo;
import inks.service.sa.som.domain.pojo.BusInvoiceitemdetailPojo;
import inks.service.sa.som.service.BusInvoiceService;
import inks.service.sa.som.service.BusInvoiceitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 销售开票(BusInvoice)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:30:26
 */
@RestController
@RequestMapping("D01M05B1")
@Api(tags = "D01M05B1:销售开票")
public class D01M05B1Controller extends BusInvoiceController {

    @Resource
    private BusInvoiceService busInvoiceService;


    @Resource
    private BusInvoiceitemService busInvoiceitemService;

    @Resource
    private SaRedisService saRedisService;


//    /**
//     * 引用FeignService服务
//     */
//   // @Resource
//    private SystemFeignService systemFeignService;


    @ApiOperation(value = "按条件分页查询待收款发票明细", notes = "按条件分页查询待收款发票明细", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Invoice.List")
    public R<PageInfo<BusInvoiceitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Bus_Invoice.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_Invoice.DisannulMark=0 and Bus_Invoice.Closed=0 and Bus_Invoice.Receipted<Bus_Invoice.TaxAmount";
            if (groupid != null) {
                qpfilter += " and Bus_Invoice.Groupid='" + groupid + "'";
            }
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busInvoiceService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询待收款发票单含未审核", notes = "按条件分页查询待收款发票单含未审核?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Invoice.List")
    public R<PageInfo<BusInvoicePojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Invoice.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Invoice.DisannulMark=0 and Bus_Invoice.Closed=0 and Bus_Invoice.Receipted<Bus_Invoice.TaxAmount";
            if (groupid != null) {
                qpfilter += " and Bus_Invoice.Groupid='" + groupid + "'";
            }
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busInvoiceService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询待收款发票单含未审核", notes = "按条件分页查询待收款发票单含未审核?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineDocPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Invoice.List")
    public R<PageInfo<BusInvoicePojo>> getOnlineDocPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Invoice.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Invoice.DisannulMark=0 ";
            qpfilter += " and Bus_Invoice.FmDocMark !=1";
            if (groupid != null) {
                qpfilter += " and Bus_Invoice.Groupid='" + groupid + "'";
            }
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busInvoiceService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询待收款发票单", notes = "按条件分页查询待收款发票单?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineRecePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Invoice.List")
    public R<PageInfo<BusInvoicePojo>> getOnlineRecePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Invoice.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Invoice.DisannulMark=0 and Bus_Invoice.Closed=0 and Bus_Invoice.Receipted<Bus_Invoice.TaxAmount";
            qpfilter += " and Bus_Invoice.Assessor<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Invoice.Groupid='" + groupid + "'";
            }
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busInvoiceService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "拉取客户所有项目", notes = "拉取客户所有项目?groupid,json为标准分页时间", produces = "application/json")
    @RequestMapping(value = "/pullItem", method = RequestMethod.POST)
    public R<List<BusInvoiceitemPojo>> pullItem(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = new QueryParam();
            if (json != null) queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            if (groupid == null) {
                return R.fail("请加入groupid");
            }
            return R.ok(this.busInvoiceService.pullItem(queryParam, groupid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除销售开票", notes = "删除销售开票", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Invoice.Delete")
    //@OperLog(title = "删除销售开票")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<String> lstcite = this.busInvoiceService.getCiteBillName(key, loginUser.getTenantid());
            if (lstcite.size() > 0) {
                return R.fail( "禁止删除,被以下单据引用:" + lstcite);
            }
            return R.ok(this.busInvoiceService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
