package inks.service.sa.som.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.MatBomdetailPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.som.constants.MyConstants;
import inks.service.sa.som.domain.export.MatBomExeclPojo;
import inks.service.sa.som.domain.export.MatBomlayerPojo;
import inks.service.sa.som.domain.export.MatBomlayeritemPojo;
import inks.service.sa.som.domain.pojo.MatBomPojo;
import inks.service.sa.som.domain.pojo.MatBomitemPojo;
import inks.service.sa.som.domain.pojo.MatGoodsPojo;
import inks.service.sa.som.service.MatBomService;
import inks.service.sa.som.service.MatBomitemService;
import inks.service.sa.som.service.MatGoodsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 物料Bom(Mat_Bom)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-11 14:41:37
 */
@RestController
@RequestMapping("D91M02B1")
@Api(tags = "D91M02B1:标准BOM")
public class D91M02B1Controller extends MatBomController {

    /**
     * 服务对象
     */
    @Resource
    private MatBomService matBomService;

    /**
     * 服务对象Item
     */
    @Resource
    private MatBomitemService matBomitemService;

    @Resource
    private SaRedisService saRedisService;

    /**
     * 服务对象
     */
    @Resource
    private MatGoodsService matGoodsService;
    //@Resource
    //private RedisTemplate<String, Object> redisTemplate;
//    /**
//     * 通过主键查询单条数据
//     *
//     * @param key 主键
//     * @param qty 主键
//     * @return 单条数据
//     */
//    @ApiOperation(value = " 获取结构清单Tree信息", notes = "获取结构清单Tree信息", produces = "application/json")
//    @RequestMapping(value = "/getTreeEntity", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Mat_Bom.List")
//    public R<MatBomtreePojo> getTreeEntity(String key, @RequestBody Double qty) {
//        try {
//            // 获得用户数据
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            MatBomtreePojo matBomtreePojo = this.matBomService.getTreeEntity(key, loginUser.getTenantid());
//            return R.ok(matBomtreePojo);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取结构清单List信息", notes = "获取结构清单List信息", produces = "application/json")
    @RequestMapping(value = "/getBomDetail", method = RequestMethod.GET)
    public R<List<MatBomdetailPojo>> getBomDetail(String key) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<MatBomdetailPojo> lst = this.matBomService.getBomDetail(key, 1D, loginUser.getTenantid());
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取结构清单Tree信息List", notes = "获取结构清单Tree信息List", produces = "application/json")
    @RequestMapping(value = "/getBomDetailByGoodsid", method = RequestMethod.GET)
    public R<List<MatBomdetailPojo>> getBomDetailByGoodsid(String key, Double qty) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // 读取货品信息
            MatGoodsPojo matGoodsPojo = this.matGoodsService.getEntity(key, loginUser.getTenantid());
            if (matGoodsPojo == null || matGoodsPojo.getBomid() == null) {
                R.fail("未找到相关标准BOM表");
            }
            List<MatBomdetailPojo> lst = this.matBomService.getBomDetail(matGoodsPojo.getBomid(), qty, loginUser.getTenantid());

            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "获取物料Bom详细信息", notes = "获取物料Bom详细信息,key=goodsid", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByGoodsid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Bom.List")
    public R<MatBomPojo> getBillEntityByGoodsid(String key, Integer need, Double qty) {
        try {
            // 获得用户数据
            if (need == null) need = 0;
            if (qty == null) qty = 1D;
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matBomService.getBillEntityByGoodsid(key, qty, need, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "ByLayer获取物料Bom详细信息", notes = "获取物料Bom详细信息,key=Mat_Bom.id", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByLayer", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Bom.List")
    public R<MatBomlayerPojo> getBillEntityByLayer(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matBomService.getBillEntityByLayer(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 用于生产加工单的物料计算   key为goodsid
     */
    @ApiOperation(value = "ByLayer物料计算多层Bom", notes = "物料计算,key=Goods.id,qty=生产数量", produces = "application/json")
    @RequestMapping(value = "/getItemAllByLayer", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Bom.List")
    public R<MatBomlayerPojo> getItemAllByLayer(@RequestBody String json) {
        try {
            JSONObject parseObject = JSONArray.parseObject(json);
            Object objectKey = parseObject.get("key");
            Object objectQty = parseObject.get("qty");

            String key = objectKey.toString();
            Double qty = Double.parseDouble(objectQty.toString());
            Integer need = (Integer) parseObject.get("need");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //通过goodsid查bomid
            String bomid = matBomService.findBomidByGoodsid(key, loginUser.getTenantid());
            return R.ok(this.matBomService.getItemAllByLayer(bomid, loginUser.getTenantid(), qty, need));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //    --------------------------------异步进行/getItemAllByLayerByLayer 物料计算多层Bom-----------------------------------------
    @ApiOperation(value = "Bom异步开始：ByLayer物料计算多层Bom", notes = "物料计算,key=Goods.id,qty=生产数量", produces = "application/json")
    @RequestMapping(value = "/getItemAllByLayerStart", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Bom.List")
    public R<String> getItemAllByLayerStart(@RequestBody String json) {
        try {
            JSONObject parseObject = JSON.parseObject(json);
            String key = parseObject.getString("key");
            Double qty = parseObject.getDouble("qty");
            Integer need = parseObject.getInteger("need");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //通过goodsid查bomid
            String bomid = matBomService.findBomidByGoodsid(key, loginUser.getTenantid());
            //生成uuid 作为redisHash的Key
            String hKey = UUID.randomUUID().toString();
            //异步进行   结果MatBomlayerPojo存进了String resultKey = BOM_CALCULATION_RESULT + hKey;
            this.matBomService.getItemAllByLayerStart(hKey, bomid, loginUser.getTenantid(), qty, need);
            return R.ok(hKey);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "Bom异步状态：ByLayer物料计算多层Bom", notes = "?", produces = "application/json")
    @RequestMapping(value = "/getItemAllByLayerState", method = RequestMethod.GET)
    public R<Map<String, Object>> getItemAllByLayerState(@RequestParam String key) {
        Map<String, Object> PrintState = this.saRedisService.getCacheMapValue(MyConstants.BOM_CALCULATION, key);
        return R.ok(PrintState);
    }

    @ApiOperation(value = "Bom异步结果：ByLayer物料计算多层Bom", notes = "", produces = "application/json")
    @RequestMapping(value = "/getItemAllByLayerResult", method = RequestMethod.GET)
    public R<MatBomlayerPojo> getItemAllByLayerResult(@RequestParam String key) {
        String resultKey = MyConstants.BOM_CALCULATION_RESULT + key;
        String json = this.saRedisService.getCacheObject(resultKey, String.class);
        MatBomlayerPojo matBomlayerPojo = JSONArray.parseObject(json, MatBomlayerPojo.class);
        //// 获取到结果后删除指定hash.hKey 即传入的key
        //redisService.delete(MyConstants.BOM_CALCULATION, key);
        return R.ok(matBomlayerPojo);
    }

    //传入Bom.id查询下级所有未审核的Bom
    @ApiOperation(value = "传入Bom.id查询下级所有未审核的Bom", notes = "传入Bom.id查询下级所有未审核的Bom", produces = "application/json")
    @RequestMapping(value = "/getUnAssessBomList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Bom.List")
    public R<Map<String, Object>> getUnAssessBomList(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // 构建传入递归的空List和计数器
            List<MatBomPojo> unAssessBomList = new ArrayList<>();
            AtomicInteger atomicCount = new AtomicInteger(0);
            this.matBomService.getUnAssessBomListRecursive(key, unAssessBomList, atomicCount, loginUser.getTenantid());
            PrintColor.red("===============计数器bom.id下总共几条子表：" + atomicCount);
            // 构建返回的结果对象 unAssessBomList  atomicCount
            Map<String, Object> result = new HashMap<>();
            result.put("unAssessBomList", unAssessBomList);
            result.put("atomicCount", atomicCount.get());
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "批量审核Bom 传入bomid的List", notes = "", produces = "application/json")
    @RequestMapping(value = "/approvalBatch", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Bom.Approval")
    public R<String> approvalBatch(@RequestBody String json) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<String> keys = JSON.parseObject(json, new TypeReference<List<String>>() {
            });
            int i = this.matBomService.approvalBatch(keys, loginUser, new Date());
            return R.ok("审核成功Bom: " + i + "条");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据主表货品id,子表货品id获取物料信息", notes = "根据主表货品id,子表货品id获取物料信息, key=主表goodsid, itemkey=子表goodsid", produces = "application/json")
    @RequestMapping(value = "/getItemListByGoodsid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Bom.List")
    public R<List<MatBomitemPojo>> getItemListByGoodsid(String key, String itemkey) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatBomPojo matBomPojo = this.matBomService.getEntityByGoodsid(key, loginUser.getTenantid());
            if (matBomPojo == null) return R.fail("未找到BOM主表");
            List<MatBomitemPojo> lstitem = this.matBomitemService.getListByGoodsid(itemkey, matBomPojo.getId(), loginUser.getTenantid());
            return R.ok(lstitem);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据当前货品id,查询当前货品id被引用的BOM主表List
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "传入货品id,查询当前货品id被引用的BOM主表List", notes = "根据当前货品id,查询被引用的BOM主表List", produces = "application/json")
    @RequestMapping(value = "/getListByItemGoodsid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Bom.List")
    public R<List<MatBomPojo>> getListByItemGoodsid(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<MatBomPojo> lstitem = this.matBomitemService.getListByItemGoodsid(key, loginUser.getTenantid());
            return R.ok(lstitem);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "传入货品id,查询当前货品id被引用的BOM对应的Goods货品信息", notes = "根据当前货品id,查询被引用的BOM主表List", produces = "application/json")
    @RequestMapping(value = "/getParentBom", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Bom.List")
    public R<List<MatGoodsPojo>> getParentBom(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<MatGoodsPojo> lstitem = this.matBomitemService.getParentBom(key, loginUser.getTenantid());
            return R.ok(lstitem);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * esay poi导入导出 时间2021-12-13 song
     *
     *
     *
     * */
    @ApiOperation(value = "模板导出", notes = "模板导出", produces = "application/json")
    @RequestMapping(value = "/exportModel", method = RequestMethod.GET)
    public void exportModel(HttpServletRequest request, HttpServletResponse response) {
        try {
            List<MatBomExeclPojo> list = new ArrayList<>();
            //创建表格
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("订单BOM", ""),
                    MatBomExeclPojo.class, list);
            //下载模板
            POIUtil.downloadWorkbook(workbook, request, response, "订单BOM模板");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导出数据", notes = "导出数据", produces = "application/json")
    @RequestMapping(value = "/exportList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Bom.List")
    public void exportList(@RequestBody String json, HttpServletRequest request, HttpServletResponse response) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "") {
                queryParam.setOrderBy("CreateDate");
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("订单BOM", "订单BOM"),
                    MatBomExeclPojo.class, this.matBomitemService.getPageList(queryParam).getList());
            POIUtil.downloadWorkbook(workbook, request, response, "订单BOM信息");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导入数据", notes = "导入数据", produces = "application/json")
    @RequestMapping(value = "/importExecl", method = RequestMethod.POST)
    public R<List<MatBomExeclPojo>> importExecl(String pid, MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //去掉标题和表头
            ImportParams importParams = POIUtil.createImportParams(0, 1);
            //将表格数据写入List
            List<MatBomExeclPojo> list = POIUtil.importExcel(file.getInputStream(), MatBomExeclPojo.class, importParams);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < list.size(); i++) {
                list.get(i).setId(inksSnowflake.getSnowflake().nextIdStr());
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Bom.List")
    public R<PageInfo<MatBomPojo>> getBillList(@RequestBody String json, String state) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Bom.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            if (state != null) {
                String statetype = "";
                if (state.indexOf("m") >= 0)
                    statetype += "'物料',";
                if (state.indexOf("s") >= 0)
                    statetype += "'半成品',";
                if (state.indexOf("p") >= 0)
                    statetype += "'成品',";
                if (statetype.length() > 0) {
                    statetype = statetype.substring(0, statetype.length() - 1);
                }
                qpfilter += " and Mat_Goods.GoodsState IN (" + statetype + ")";
            }
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matBomService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Bom.List")
    public R<PageInfo<MatBomPojo>> getPageTh(@RequestBody String json, String state) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Mat_Bom.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Mat_Goods.VirtualItem=0";
            if (state != null) {
                String statetype = "";
                if (state.contains("m"))
                    statetype += "'物料',";
                if (state.contains("s"))
                    statetype += "'半成品',";
                if (state.contains("p"))
                    statetype += "'成品',";
                if (!statetype.isEmpty()) {
                    statetype = statetype.substring(0, statetype.length() - 1);
                }
                qpfilter += " and Mat_Goods.GoodsState IN (" + statetype + ")";
            }
            // 加入场景   Nanno 20230203
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matBomService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * esay poi Layer模版  Eric 20221229
     *
     *
     * */
    @ApiOperation(value = "Layer模板导出", notes = "Layer模板导出", produces = "application/json")
    @RequestMapping(value = "/exportLayerModel", method = RequestMethod.GET)
    public void exportLayerModel(HttpServletRequest request, HttpServletResponse response) {
        try {
            List<MatBomlayeritemPojo> list = new ArrayList<>();
            //创建表格
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("标准BOM", ""),
                    MatBomlayeritemPojo.class, list);
            //下载模板
            POIUtil.downloadWorkbook(workbook, request, response, "标准BOM模板");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "导入Layer数据", notes = "导入Layer数据", produces = "application/json")
    @RequestMapping(value = "/importLayerExecl", method = RequestMethod.POST)
    public R<List<MatBomlayeritemPojo>> importLayerExecl(MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //去掉标题和表头
            ImportParams importParams = POIUtil.createImportParams(0, 1);
            //将表格数据写入List
            List<MatBomlayeritemPojo> list = POIUtil.importExcel(file.getInputStream(), MatBomlayeritemPojo.class, importParams);
            //设置layernum
            list.stream().forEach(a -> {
                if ("1".equals(a.getLayer1())) {
                    a.setLayernum(1);
                } else if ("2".equals(a.getLayer2())) {
                    a.setLayernum(2);
                } else if ("3".equals(a.getLayer3())) {
                    a.setLayernum(3);
                } else if ("4".equals(a.getLayer4())) {
                    a.setLayernum(4);
                } else if ("5".equals(a.getLayer5())) {
                    a.setLayernum(5);
                } else if ("6".equals(a.getLayer6())) {
                    a.setLayernum(6);
                }
            });
            //
            for (int i = 0; i < list.size() - 1; i++) {
                // 1 根据l1~l6 计算当前行的layernum;
                Integer layernumCurr = list.get(i).getLayernum();
                // 2 根据l1~l6 计算下一行的layernum;
                Integer layernumNext = list.get(i + 1).getLayernum();
                // 3 如果当前行的layernum>=下一行的layernum, 为物料matmark=1 不为物料=0
                if (layernumCurr >= layernumNext) {
                    list.get(i).setMatmark(1);
                } else {
                    list.get(i).setMatmark(0);
                }
            }
            //最后一位matmark=1
            list.get(list.size() - 1).setMatmark(1);
            list.stream().filter(a -> a.getMatmark() == 1).forEach(a -> System.out.println(a.getRownum()));

            //模拟存储
//            MatBomlayerPojo matBomlayerPojo = new MatBomlayerPojo();
//            matBomlayerPojo.setGoodsid("12138");
//            matBomlayerPojo.setItem(list);
//            String s = JSON.toJSONString(matBomlayerPojo);
//            createBomByLayer(s);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增物料Bom", notes = "新增物料Bom", produces = "application/json")
    @RequestMapping(value = "/createBomByLayer", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Bom.Add")
    public R<MatBomlayerPojo> createBomByLayer(@RequestBody String json) {
        try {
            MatBomlayerPojo matBomlayerPojo = JSONArray.parseObject(json, MatBomlayerPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatBomlayerPojo bomByLayer = this.matBomService.createBomByLayer(matBomlayerPojo);
            return R.ok(bomByLayer);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
