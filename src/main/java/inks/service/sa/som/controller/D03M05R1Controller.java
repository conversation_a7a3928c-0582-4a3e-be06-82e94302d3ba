package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.service.D03M05R1Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("D03M05R1")
@Api(tags = "D03M05R1:采购开票：全类型")
public class D03M05R1Controller {

    @Resource
    private D03M05R1Service d03M05R1Service;

    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "本月采购额", notes = "本月采购额", produces = "application/json")
    @RequestMapping(value = "/getItemCountByMonth", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getItemCountByMonth(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange().getDateColumn() == null || queryParam.getDateRange().getDateColumn().equals(""))
                queryParam.getDateRange().setDateColumn("Bus_Machining.BillDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03M05R1Service.getItemCountByMonth(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "供应商金额占比", notes = "供应商金额占比", produces = "application/json")
    @RequestMapping(value = "/sumChartMaxByGroup", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> sumChartMaxByGroup(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange().getDateColumn() == null || queryParam.getDateRange().getDateColumn().equals(""))
                queryParam.getDateRange().setDateColumn("Bus_Machining.BillDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d03M05R1Service.sumChartMaxByGroup(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
