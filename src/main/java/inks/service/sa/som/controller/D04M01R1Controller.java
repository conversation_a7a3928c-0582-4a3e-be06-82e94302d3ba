package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.MatAccessEntity;
import inks.service.sa.som.domain.pojo.MatAccessPojo;
import inks.service.sa.som.domain.pojo.MatAccessitemPojo;
import inks.service.sa.som.domain.pojo.MatAccessitemdetailPojo;
import inks.service.sa.som.domain.pojo.MatInventoryPojo;
import inks.service.sa.som.service.MatAccessService;
import inks.service.sa.som.service.MatInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 出入库主表(MatAccess)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-12 14:43:14
 */
@RestController
@RequestMapping("D04M01R1")
@Api(tags = "D04M01R1:出入库报表")
public class D04M01R1Controller extends MatAccessController {

    @Resource
    private MatAccessService matAccessService;
    @Resource
    private SaBillcodeService saBillcodeService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private MatInventoryService matInventoryService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<MatAccessitemdetailPojo>> getPageList(@RequestBody String json, String type, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Access.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Mat_Access.Groupid='" + groupid + "'";
            }
            if (type != null && !"".equals(type)) {
                String filtertype = this.getFilterType(type);
                qpfilter += " and Mat_Access.BillType IN (" + filtertype + ")";

            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matAccessService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "过滤红冲记录按条件分页查询,getPageList的基础上加个过滤条件:查询OrgUid和ReturnUid为空的数据", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getValidPageList", method = RequestMethod.POST)
    public R<PageInfo<MatAccessitemdetailPojo>> getValidPageList(@RequestBody String json, String type, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Access.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Mat_Access.Groupid='" + groupid + "'";
            }
            if (isNotBlank(type)) {
                String filtertype = this.getFilterType(type);
                qpfilter += " and Mat_Access.BillType IN (" + filtertype + ")";
            }
            // 上面接口getPageList的基础上加个过滤条件:查询OrgUid和ReturnUid为空的数据
            qpfilter += " and Mat_Access.OrgUid='' and Mat_Access.ReturnUid=''";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matAccessService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Access.List")
    public R<PageInfo<MatAccessPojo>> getBillList(@RequestBody String json, String type, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Access.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Mat_Access.Groupid='" + groupid + "'";
            }

            if (type != null && !"".equals(type)) {
                String filtertype = this.getFilterType(type);
                qpfilter += " and Mat_Access.BillType IN (" + filtertype + ")";

            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matAccessService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Access.List")
    public R<PageInfo<MatAccessPojo>> getPageTh(@RequestBody String json, String type, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Access.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Mat_Access.Groupid='" + groupid + "'";
            }
            if (type != null && !"".equals(type)) {
                String filtertype = this.getFilterType(type);
                qpfilter += " and Mat_Access.BillType IN (" + filtertype + ")";

            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matAccessService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //将type代码转类型
    private String getFilterType(String type) {
        String filtertype = "";
        if (type.indexOf("a") >= 0)
            filtertype += "'收货入库','收货红冲',";
        if (type.indexOf("b") >= 0)
            filtertype += "'购退出库','购退红冲',";
        if (type.indexOf("c") >= 0)
            filtertype += "'发货出库','发货红冲',";
        if (type.indexOf("d") >= 0)
            filtertype += "'客退入库','客退红冲',";

        if (type.indexOf("e") >= 0)
            filtertype += "'领料出库','领料红冲',";
        if (type.indexOf("f") >= 0)
            filtertype += "'退料入库','退料红冲',";
        if (type.indexOf("g") >= 0)
            filtertype += "'生产入库','生产红冲',";
        if (type.indexOf("h") >= 0)
            filtertype += "'加工入库','加工红冲',";

        if (type.indexOf("i") >= 0)
            filtertype += "'其他入库','他入红冲',";
        if (type.indexOf("l") >= 0)
            filtertype += "'其他出库','他出红冲',";
        if (type.indexOf("j") >= 0)
            filtertype += "'报废出库','报废红冲',";
        if (type.indexOf("k") >= 0)
            filtertype += "'盘盈入库','盘盈红冲',";
        if (type.indexOf("m") >= 0)
            filtertype += "'盘亏出库','盘亏红冲',";
        if (type.indexOf("n") >= 0)
            filtertype += "'库存调价',";
        if (type.indexOf("o") >= 0)
            filtertype += "'厂制入库','厂制红冲',";
        if (type.indexOf("p") >= 0)
            filtertype += "'委制入库','委制红冲',";
        if (filtertype.length() > 0) {
            filtertype = filtertype.substring(0, filtertype.length() - 1);
        }
        return filtertype;
    }


    @ApiOperation(value = " 新增出入库主表", notes = "新增出入库主表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Access.Add")
    public R<MatAccessPojo> create(@RequestBody String json) {
        try {
            MatAccessPojo matAccessPojo = JSONArray.parseObject(json, MatAccessPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D04M01M1", loginUser.getTenantid(),"Mat_Access");
            matAccessPojo.setRefno(refNo);
            matAccessPojo.setCreateby(loginUser.getRealname());   // 创建者
            matAccessPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matAccessPojo.setCreatedate(new Date());   // 创建时间
            matAccessPojo.setLister(loginUser.getRealname());   // 制表
            matAccessPojo.setListerid(loginUser.getUserid());    // 制表id
            matAccessPojo.setModifydate(new Date());   //修改时间
            matAccessPojo.setTenantid(loginUser.getTenantid());   //租户id
            matAccessPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            return R.ok(this.matAccessService.insert(matAccessPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增出入库单(修改库存中预期单价,接收的item.getPrice()即为预期单价,数量必定为0,金额为预期单价*DB库存数量-DB库存金额)", notes = "新增出入库主表", produces = "application/json")
    @RequestMapping(value = "/createUnitPrice", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Access.Add")
    public R<MatAccessPojo> createUnitPrice(@RequestBody String json) {
        try {
            MatAccessPojo matAccessPojo = JSONArray.parseObject(json, MatAccessPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D04M01M1", loginUser.getTenantid(),"Mat_Access");
            matAccessPojo.setRefno(refNo);
            matAccessPojo.setCreateby(loginUser.getRealname());   // 创建者
            matAccessPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matAccessPojo.setCreatedate(new Date());   // 创建时间
            matAccessPojo.setLister(loginUser.getRealname());   // 制表
            matAccessPojo.setListerid(loginUser.getUserid());    // 制表id
            matAccessPojo.setModifydate(new Date());   //修改时间
            matAccessPojo.setTenantid(loginUser.getTenantid());   //租户id
            matAccessPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            // 遍历传入子表,修改金额为 预期单价*DB库存数量-DB库存金额
            List<MatAccessitemPojo> itemLst = matAccessPojo.getItem();
            for (MatAccessitemPojo item : itemLst) {
                MatInventoryPojo matInventoryPojo = new MatInventoryPojo();
                matInventoryPojo.setGoodsid(item.getGoodsid());
                matInventoryPojo.setTenantid(loginUser.getTenantid());
                matInventoryPojo.setStoreid(matAccessPojo.getStoreid());
                matInventoryPojo.setBatchno(item.getBatchno());
                matInventoryPojo.setLocation(item.getLocation());
                matInventoryPojo.setPacksn(item.getPacksn());
                matInventoryPojo.setSkuid(item.getSkuid());
                MatInventoryPojo inventoryPojoDB = matInventoryService.getEntityByIf(matInventoryPojo);
                Double price = item.getPrice();// 预期单价
                // 金额=预期单价*DB库存数量-DB库存金额
                item.setAmount(price * inventoryPojoDB.getQuantity() - inventoryPojoDB.getAmount());
            }
            return R.ok(this.matAccessService.insert(matAccessPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param key 实体
     * @return 新增结果
     */
    @ApiOperation(value = "红冲单据", notes = "红冲单据", produces = "application/json")
    @RequestMapping(value = "/createRed", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Access.Add")
    public R<MatAccessPojo> createRed(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatAccessPojo matAccessPojo = this.matAccessService.getBillEntity(key, loginUser.getTenantid());
            //新建原始单
            MatAccessEntity matAccessEntityOrg = new MatAccessEntity();
            matAccessEntityOrg.setId(matAccessPojo.getId());
            matAccessEntityOrg.setTenantid(matAccessPojo.getTenantid());


            //生成单据编码  红冲单的编码
            String refNoRed = saBillcodeService.getSerialNo("D04M01M1", loginUser.getTenantid(),"Mat_Access");
            matAccessEntityOrg.setReturnuid(refNoRed);//原单设置红冲单号

            matAccessPojo.setRefno(refNoRed);
            matAccessPojo.setOrguid(matAccessPojo.getRefno());//红冲单设置原单号

            matAccessPojo.setOrguid(matAccessPojo.getRefno());
            matAccessPojo.setCreateby(loginUser.getRealname());   // 创建者
            matAccessPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matAccessPojo.setCreatedate(new Date());   // 创建时间
            matAccessPojo.setLister(loginUser.getRealname());   // 制表
            matAccessPojo.setListerid(loginUser.getUserid());    // 制表id
            matAccessPojo.setModifydate(new Date());   //修改时间
            matAccessPojo.setTenantid(loginUser.getTenantid());   //租户id
            matAccessPojo.setBilldate(new Date());
            matAccessPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            return R.ok(this.matAccessService.insertRed(matAccessPojo, matAccessEntityOrg));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineDocPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Access.List")
    public R<PageInfo<MatAccessPojo>> getOnlineDocPageTh(@RequestBody String json, String type, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Access.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Mat_Access.FmDocMark=0";
            if (groupid != null) {
                qpfilter += " and Mat_Access.Groupid='" + groupid + "'";
            }
            if (type != null && !"".equals(type)) {
                String filtertype = this.getFilterType(type);
                qpfilter += " and Mat_Access.BillType IN (" + filtertype + ")";

            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matAccessService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "(分页)查询出入库表中每个货品的总数量,count数", notes = "(分页)查询库存信息表中每个货品的总数量,count数", produces = "application/json")
    @RequestMapping(value = "/getSumPageListByGoods", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Access.List")
    public R<PageInfo<Map<String, Object>>> getSumPageListByGoods(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matAccessService.getSumPageListByGoods(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    //------------------------------------传单据id+仓库id生成出/入库单------------------------------------
    @ApiOperation(value = "(接收采购验收单id+仓库id)生成入库单", notes = "(接收采购验收单id+仓库id)生成入库单", produces = "application/json")
    @RequestMapping(value = "/quickInStoreByBuyFinishing", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Access.Edit")
    public R<Boolean> quickInStoreByDeliery(String id, String storeid) {
        try {
            MatAccessPojo matAccessPojo = new MatAccessPojo();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D04M01B1", loginUser.getTenantid(),"Mat_Access");
            matAccessPojo.setRefno(refNo);
            matAccessPojo.setCreateby(loginUser.getRealname());   // 创建者
            matAccessPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matAccessPojo.setCreatedate(new Date());   // 创建时间
            matAccessPojo.setLister(loginUser.getRealname());   // 制表
            matAccessPojo.setListerid(loginUser.getUserid());    // 制表id
            matAccessPojo.setModifydate(new Date());   //修改时间
            matAccessPojo.setTenantid(loginUser.getTenantid());   //租户id
            matAccessPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            return R.ok(this.matAccessService.quickInStoreByBuyFinishing(id, storeid, matAccessPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "(接收领料单id+仓库id)生成出库单 MatRequisition", notes = "(接收领料单id+仓库id)生成c库单", produces = "application/json")
    @RequestMapping(value = "/quickInStoreByMatRequisition", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Access.Edit")
    public R<Boolean> quickInStoreByMatRequisition(String id, String storeid, String billtitle) {
        try {
            if (StringUtils.isBlank(billtitle)) {
                return R.fail("billtitle不能为空");
            }
            MatAccessPojo matAccessPojo = new MatAccessPojo();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D04M01B1", loginUser.getTenantid(),"Mat_Access");
            matAccessPojo.setRefno(refNo);
            matAccessPojo.setCreateby(loginUser.getRealname());   // 创建者
            matAccessPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matAccessPojo.setCreatedate(new Date());   // 创建时间
            matAccessPojo.setLister(loginUser.getRealname());   // 制表
            matAccessPojo.setListerid(loginUser.getUserid());    // 制表id
            matAccessPojo.setModifydate(new Date());   //修改时间
            matAccessPojo.setTenantid(loginUser.getTenantid());   //租户id
            matAccessPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            return R.ok(this.matAccessService.quickInStoreByMatRequisition(id, storeid, billtitle, matAccessPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

