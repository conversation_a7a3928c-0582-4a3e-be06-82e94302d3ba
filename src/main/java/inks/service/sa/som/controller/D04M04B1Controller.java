package inks.service.sa.som.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.MatGoodsInveQtyPojo;
import inks.service.sa.som.domain.pojo.MatInventoryPojo;
import inks.service.sa.som.domain.pojo.MatInventoryQtyPojo;
import inks.service.sa.som.service.MatInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 库存信息(Mat_Inventory)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-12 14:23:41
 */
@RestController
@RequestMapping("D04M04B1")
@Api(tags = "D04M04B1:库存信息")
public class D04M04B1Controller extends MatInventoryController {

    @Resource
    private MatInventoryService matInventoryService;


    @Resource
    private SaRedisService saRedisService;


    /**
     * @return R<MatInventoryPojo>
     * @Description
     * <AUTHOR>
     * @param[1] key goodsid
     * @param[2] storeid 仓库id
     * @time 2023/4/19 9:19
     */

    @ApiOperation(value = " 获取库存信息详细信息", notes = "获取库存信息详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByStoreGoods", method = RequestMethod.GET)
    public R<MatInventoryPojo> getEntityByStoreGoods(String key, String storeid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matInventoryService.getEntityByStoreGoods(key, storeid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListByStore", method = RequestMethod.POST)
    public R<PageInfo<MatInventoryPojo>> getPageListByStore(@RequestBody String json, String store) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Inventory.CreateDate");
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Inventory.Storeid='" + store + "'";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInventoryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "Dms按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getDmsPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<PageInfo<MatInventoryPojo>> getDmsPageList(@RequestBody String json, String store) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Inventory.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Storage.DmsMark=1";
            if (store != null) {
                qpfilter += " and Mat_Inventory.Storeid='" + store + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInventoryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "Scm按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getScmPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<PageInfo<MatInventoryPojo>> getScmPageList(@RequestBody String json, String store) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Inventory.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Storage.ScmMark=1";
            if (store != null) {
                qpfilter += " and Mat_Inventory.Storeid='" + store + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInventoryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询？key=货品id", produces = "application/json")
    @RequestMapping(value = "/geOnlinePageListByGoods", method = RequestMethod.POST)
    public R<PageInfo<MatInventoryPojo>> getOnlinePageListByGoods(@RequestBody String json, String key) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Inventory.CreateDate");
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Inventory.Quantity!=0";
            if (key != null) {
                qpfilter += " and Mat_Inventory.Goodsid='" + key + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInventoryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "Dms按条件分页查询Online", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getDmsOnlinePageList", method = RequestMethod.POST)
    public R<PageInfo<MatInventoryPojo>> getDmsOnlinePageList(@RequestBody String json, String storeid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Inventory.CreateDate");
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Inventory.Quantity>0 and Mat_Storage.DmsMark=1";
            if (storeid != null) {
                qpfilter += " and Mat_Inventory.Storeid='" + storeid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInventoryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "Scm按条件分页查询Online", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getScmOnlinePageList", method = RequestMethod.POST)
    public R<PageInfo<MatInventoryPojo>> getScmOnlinePageList(@RequestBody String json, String storeid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Inventory.CreateDate");
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Inventory.Quantity>0 and Mat_Storage.ScmMark=1";
            if (storeid != null) {
                qpfilter += " and Mat_Inventory.Storeid='" + storeid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInventoryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    public R<PageInfo<MatInventoryPojo>> getOnlinePageList(@RequestBody String json, String storeid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Inventory.CreateDate");
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Inventory.Quantity>0";
            if (storeid != null) {
                qpfilter += " and Mat_Inventory.Storeid='" + storeid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInventoryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getZeroPageList", method = RequestMethod.POST)
    public R<PageInfo<MatInventoryPojo>> getZeroPageList(@RequestBody String json, String storeid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Inventory.CreateDate");
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Inventory.Quantity=0";
            if (storeid != null) {
                qpfilter += " and Mat_Inventory.Storeid='" + storeid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInventoryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "导出数据", notes = "导出数据", produces = "application/json")
    @RequestMapping(value = "/exportOnlineList", method = RequestMethod.POST)
    public void exportOnlineList(@RequestBody String json, String storeid, HttpServletRequest request, HttpServletResponse response) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy())) {
                queryParam.setOrderBy("CreateDate");
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_Inventory.Quantity>0";
            if (storeid != null) {
                qpfilter += " and Mat_Inventory.Storeid='" + storeid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("库存信息", ""),
                    MatInventoryQtyPojo.class, this.matInventoryService.getQtyPageList(queryParam).getList());
            POIUtil.downloadWorkbook(workbook, request, response, "库存信息");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 未知，没有用到
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询货品结余(账面库存)", notes = "按条件分页查询货品结余", produces = "application/json")
    @RequestMapping(value = "/getQtyPageListByGoods", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<PageInfo<MatGoodsInveQtyPojo>> getQtyPageListByGoods(@RequestBody String json, Integer type, Integer status) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (type != null && type == -1) { // 查账面库存为负数的
                qpfilter += " AND (IvQuantity + Buyremqty + Wkwsremqty + Wkscremqty - Requremqty - BusRemQty) < 0";
            }
            if (status != null) {
                if (status == 0) {
                    qpfilter += " AND Mat_Goods.GoodsState='物料'";
                } else if (status == 1) {
                    qpfilter += " AND Mat_Goods.GoodsState='半成品'";
                } else if (status == 2) {
                    qpfilter += " AND Mat_Goods.GoodsState='成品'";
                }
            }
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInventoryService.getQtyPageListByGoods(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 未知，没有用到
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询订单主料结余列表", notes = "按条件分页查询订单主料结余列表,itemid当前订单行id", produces = "application/json")
    @RequestMapping(value = "/getMachMatQtyPageListByGoods", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<PageInfo<MatGoodsInveQtyPojo>> getMachMatQtyPageListByGoods(@RequestBody String json, String itemid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
//            if (itemid != null) {
//                qpfilter+=" and Bus_MachiningItem.id<>'" + itemid + "'";
//            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInventoryService.getMachMatQtyPageListByGoods(queryParam, itemid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
