package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AmountUtils;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.som.domain.pojo.BuyFinishingPojo;
import inks.service.sa.som.domain.pojo.BuyFinishingitemPojo;
import inks.service.sa.som.domain.pojo.BuyFinishingitemdetailPojo;
import inks.service.sa.som.service.BuyFinishingService;
import inks.service.sa.som.service.BuyFinishingitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;
import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;

/**
 * 采购验收(BuyFinishing)表控制层
 *
 * <AUTHOR>
 * @since 2022-05-06 20:34:18
 */

public class BuyFinishingController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(BuyFinishingController.class);

    @Resource
    private BuyFinishingService buyFinishingService;

    @Resource
    private BuyFinishingitemService buyFinishingitemService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private SaBillcodeService saBillcodeService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取采购验收详细信息", notes = "获取采购验收详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<BuyFinishingPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyFinishingService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingitemdetailPojo>> getPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.buyFinishingService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取采购验收详细信息", notes = "获取采购验收详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<BuyFinishingPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyFinishingService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.buyFinishingService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingPojo>> getPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.buyFinishingService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增采购验收", notes = "新增采购验收", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.Add")
    public R<BuyFinishingPojo> create(@RequestBody String json) {
        try {
            BuyFinishingPojo buyFinishingPojo = JSONArray.parseObject(json, BuyFinishingPojo.class);
            // item转json,校验各项金额相差不能大于1
            String lessThanOne = AmountUtils.lstLessThanOne(JSONObject.toJSONString(buyFinishingPojo.getItem()));
            PrintColor.red("=========lessThanOne校验各项金额相差不能大于1:" + lessThanOne);
            if (lessThanOne != null) return R.fail(lessThanOne);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D03M03B1", loginUser.getTenantid(), "Buy_Finishing");
            buyFinishingPojo.setRefno(refNo);
            buyFinishingPojo.setCreateby(loginUser.getRealName());   // 创建者
            buyFinishingPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            buyFinishingPojo.setCreatedate(new Date());   // 创建时间
            buyFinishingPojo.setLister(loginUser.getRealname());   // 制表
            buyFinishingPojo.setListerid(loginUser.getUserid());    // 制表id            
            buyFinishingPojo.setModifydate(new Date());   //修改时间
            buyFinishingPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.buyFinishingService.insert(buyFinishingPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改采购验收", notes = "修改采购验收", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.Edit")
    public R<BuyFinishingPojo> update(@RequestBody String json) {
        try {
            BuyFinishingPojo buyFinishingPojo = JSONArray.parseObject(json, BuyFinishingPojo.class);
            // item转json,校验各项金额相差不能大于1
            String lessThanOne = AmountUtils.lstLessThanOne(JSONObject.toJSONString(buyFinishingPojo.getItem()));
            PrintColor.red("=========lessThanOne校验各项金额相差不能大于1:" + lessThanOne);
            if (lessThanOne != null) return R.fail(lessThanOne);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            buyFinishingPojo.setLister(loginUser.getRealname());   // 制表
            buyFinishingPojo.setListerid(loginUser.getUserid());    // 制表id   
            buyFinishingPojo.setModifydate(new Date());   //修改时间
            buyFinishingPojo.setAssessor(""); //审核员
            buyFinishingPojo.setAssessorid(""); //审核员
            buyFinishingPojo.setAssessdate(new Date()); //审核时间
            buyFinishingPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.buyFinishingService.update(buyFinishingPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除采购验收", notes = "删除采购验收", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Finishing.Delete")
    //@OperLog(title = "删除采购验收")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String refno = this.buyFinishingService.delete(key, loginUser.getTenantid());
            return R.ok(1, "id:" + key + "  refno:" + refno);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增采购验收Item", notes = "新增采购验收Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.Add")
    public R<BuyFinishingitemPojo> createItem(@RequestBody String json) {
        try {
            BuyFinishingitemPojo buyFinishingitemPojo = JSONArray.parseObject(json, BuyFinishingitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            buyFinishingitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.buyFinishingitemService.insert(buyFinishingitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除采购验收Item", notes = "删除采购验收Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Finishing.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyFinishingitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核采购验收", notes = "审核采购验收", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Finishing.Approval")
    public R<BuyFinishingPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BuyFinishingPojo buyFinishingPojo = this.buyFinishingService.getEntity(key, loginUser.getTenantid());
            if (buyFinishingPojo.getAssessor().equals("")) {
                buyFinishingPojo.setAssessor(loginUser.getRealname()); //审核员
                buyFinishingPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                buyFinishingPojo.setAssessor(""); //审核员
                buyFinishingPojo.setAssessorid(""); //审核员
            }
            buyFinishingPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.buyFinishingService.approval(buyFinishingPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    @ApiOperation(value = "发出第三方审批", notes = "发出第三方审批wex/ding", produces = "application/json")
//    @RequestMapping(value = "/sendapprovel", method = RequestMethod.GET)
//    public R<ApprrecPojo> sendapprovel(String key, String apprid, String type) {
//        try {
//            if (type == null) type = "wxe";  // 默认走企业微信
//            String verifyKey = CacheConstants.APPR_CODES_KEY + apprid;
//            //获取token
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            //创建VM数据对象
//            VelocityContext context = new VelocityContext();
//            //从redis中获取模板对象
//            // Object obj = saRedisService.getValue(verifyKey);
//            ApprrecPojo apprrecPojo = saRedisService.getValue(verifyKey);
//            ApprovePojo approvePojo = new ApprovePojo();
//            //获得第三方账号
//            R rja = systemFeignService.getJustauthByUserid(loginUser.getUserid(), type, loginUser.getTenantid());
//            JustauthPojo justauthPojo = new JustauthPojo();
//            if (rja.getCode() == 200) {
//                org.springframework.beans.BeanUtils.copyProperties(rja.getData(), justauthPojo);
//            } else {
//                return R.fail("获得第三方账号出错" + rja.getMsg().toString());
//            }
//            approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
//            approvePojo.setUserid(justauthPojo.getAuthuuid());
//            approvePojo.setModelcode(apprrecPojo.getTemplateid());
//            approvePojo.setObject(this.buyFinishingService.getBillEntity(key, loginUser.getTenantid()));
//            context.put("approvePojo", approvePojo);
//            String str = apprrecPojo.getDatatemp();
//            // 初始化并取得Velocity引擎
//            VelocityEngine ve = new VelocityEngine();
//            ve.init();
//            // 转换输出
//            StringWriter writer = new StringWriter();
//            ve.evaluate(context, writer, "", str); // 关键方法
//            //写回String
//            str = writer.toString();
//            //新建审批记录
//            apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
//            apprrecPojo.setDatatemp(str);
//            apprrecPojo.setApprname("订单审批");
//            apprrecPojo.setResultcode("");
//            apprrecPojo.setBillid(key);    // 单据ID
//            apprrecPojo.setUserid("");
//            apprrecPojo.setApprtype("");
//            apprrecPojo.setCreateby(loginUser.getRealname());
//            apprrecPojo.setCreatebyid(loginUser.getUserid());
//            apprrecPojo.setCreatedate(new Date());
//            apprrecPojo.setLister(loginUser.getRealname());
//            apprrecPojo.setListerid(loginUser.getUserid());
//            apprrecPojo.setModifydate(new Date());
//            apprrecPojo.setTenantid(loginUser.getTenantid());
//            //将企业微信审批信息存入redis
//            String CachKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
//            saRedisService.setKeyValue(CachKey, apprrecPojo, (long) (60 * 12), TimeUnit.MINUTES);
//            if ("wxe".equals(type)) {
//                R r = this.utilsFeignService.wxeapprovel(apprrecPojo.getId(), loginUser.getTenantid());
//                if (r.getCode() != 200) {
//                    return R.fail("发起审批失败" + r.toString());
//                }
//            } else {
//                R r = this.utilsFeignService.dingapprovel(apprrecPojo.getId(), loginUser.getTenantid());
//                if (r.getCode() != 200) {
//                    return R.fail("发起审批失败" + r.toString());
//                }
//            }
//            return R.ok(apprrecPojo);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//
//    @ApiOperation(value = "审批回调修改状态", notes = "审批回调修改状态", produces = "application/json")
//    @RequestMapping(value = "/justapprovel", method = RequestMethod.GET)
//    public R<BuyFinishingPojo> justapprovel(String key, String type) {
//        try {
//            System.out.println("审核通过,写入审核信息");
//            //1.读取审批记录
//            String verifyKey = CacheConstants.APPR_CODES_KEY + key;
//            ApprrecPojo apprrecPojo = saRedisService.getValue(verifyKey);
//            //2. 获得单据数据
//            BuyFinishingPojo buyFinishingPojo = this.buyFinishingService.getEntity(apprrecPojo.getBillid(), apprrecPojo.getTenantid());
//            //3. 写入审核批
//            //获得第三方账号
//            if (type == null) type = "wxe";
//            R rja = systemFeignService.getJustauthByUuid(apprrecPojo.getCallbackuuid(), type, apprrecPojo.getTenantid());
//            JustauthPojo justauthPojo = new JustauthPojo();
//            if (rja.getCode() == 200) {
//                org.springframework.beans.BeanUtils.copyProperties(rja.getData(), justauthPojo);
//            } else {
//                System.out.println("写入审核:获得第三方账号出错：" + rja.getMsg());
//                return R.fail("获得第三方账号出错" + rja.getMsg());
//            }
//            buyFinishingPojo.setAssessorid(justauthPojo.getUserid());
//            buyFinishingPojo.setAssessor(justauthPojo.getRealname()); //审核员
//            buyFinishingPojo.setAssessdate(new Date()); //审核时间
//            return R.ok(this.buyFinishingService.approval(buyFinishingPojo));
//        } catch (Exception e) {
//            System.out.println("写入审核失败：" + e.getMessage());
//            return R.fail(e.getMessage());
//        }
//    }


    /**
     * 作废单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "作废采购验收", notes = "作废采购验收,?type=1作废，0为反作废", produces = "application/json")
    @RequestMapping(value = "/disannul", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Edit")
    public R<BuyFinishingPojo> disannul(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<BuyFinishingitemPojo> lst = JSONArray.parseArray(json, BuyFinishingitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //  BuyFinishingPojo buyFinishingPojo = this.buyFinishingService.getEntity(lst.get(0).getPid(), loginUser.getTenantid());

            return R.ok(this.buyFinishingService.disannul(lst, type, loginUser));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 作废单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "中止采购验收", notes = "中止采购验收,?type=1中止，0为反作废", produces = "application/json")
    @RequestMapping(value = "/closed", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Edit")
    public R<BuyFinishingPojo> closed(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<BuyFinishingitemPojo> lst = JSONArray.parseArray(json, BuyFinishingitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyFinishingService.closed(lst, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Finishing.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BuyFinishingPojo buyFinishingPojo = this.buyFinishingService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(buyFinishingPojo);
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = buyFinishingPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BuyFinishingitemPojo buyFinishingitemPojo = new BuyFinishingitemPojo();
                    buyFinishingPojo.getItem().add(buyFinishingitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(buyFinishingPojo.getItem());
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Finishing.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            BuyFinishingPojo buyFinishingPojo = this.buyFinishingService.getEntity(key, loginUser.getTenantid());

            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(buyFinishingPojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<BuyFinishingitemPojo> lstitem = this.buyFinishingitemService.getList(key, loginUser.getTenantid());
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "收货单据" + buyFinishingPojo.getRefno());
            mapPrint.put("data", ptJson);
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印BuyFinishing明细报表(分页PageList)", notes = "打印明细报表", produces = "application/json")
    @RequestMapping(value = "/printPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.Print")
    public void printPageList(@RequestBody String json, String groupid, String ptid, HttpServletRequest request) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Buy_Finishing.BillDate");
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
        }
        // 获取请求的路径,/分割
        String[] pathParts = request.getRequestURI().split("/");
        String moduleCode = pathParts[pathParts.length - 2];
        if ("D03M03B1".equals(moduleCode)) {
            qpfilter += " and Buy_Finishing.BillType in ('采购验收','采购退货')";
        } else if ("D03M03B2".equals(moduleCode)) {
            qpfilter += " and Buy_Finishing.BillType in ('其他收货','其他退货')";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BuyFinishingitemdetailPojo> lst = this.buyFinishingService.getPageList(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BuyFinishingitemdetailPojo itemdetailPojo = new BuyFinishingitemdetailPojo();
                    lst.add(itemdetailPojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "打印BuyFinishing单据报表(分页PageTh)", notes = "打印PageTh报表", produces = "application/json")
    @RequestMapping(value = "/printPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.Print")
    public void printPageTh(@RequestBody String json, String groupid, String ptid, HttpServletRequest request) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Buy_Finishing.BillDate");
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
        }
        // 获取请求的路径,/分割
        String[] pathParts = request.getRequestURI().split("/");
        String moduleCode = pathParts[pathParts.length - 2];
        if ("D03M03B1".equals(moduleCode)) {
            qpfilter += " and Buy_Finishing.BillType in ('采购验收','采购退货')";
        } else if ("D03M03B2".equals(moduleCode)) {
            qpfilter += " and Buy_Finishing.BillType in ('其他收货','其他退货')";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BuyFinishingPojo> lst = this.buyFinishingService.getPageTh(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BuyFinishingPojo pojo = new BuyFinishingPojo();
                    lst.add(pojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "批量打印单据,传入ids", notes = "json={KEY,KEY},ptid打印模版", produces = "application/json")
    @RequestMapping(value = "/printBatchBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.Print")
    public void printBatchBill(@RequestBody String json, String ptid) throws IOException, JRException {
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            String content;
            if (reportsPojo != null) {
                content = reportsPojo.getRptdata();
            } else {
                throw new BaseBusinessException("未找到报表");
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            //数据填充
            JasperPrint printAll = new JasperPrint();
            for (int a = 0; a < lstkeys.size(); a++) {
                String key = lstkeys.get(a);
                //=========获取单据表头信息========
                BuyFinishingPojo pojo = this.buyFinishingService.getBillEntity(key, loginUser.getTenantid());
                if (pojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }

                //表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(pojo);
                // 加入公司信息
                PrintUtils.addCompanyInfo(map, loginUser);
                // 判定是否需要追行
                if (reportsPojo.getPagerow() > 0) {
                    int index = 0;
                    // 取行余数
                    index = pojo.getItem().size() % reportsPojo.getPagerow();
                    if (index > 0) {
                        // 补全空白行
                        for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                            BuyFinishingitemPojo itemPojo = new BuyFinishingitemPojo();
                            pojo.getItem().add(itemPojo);
                        }
                    }
                }

                // 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = attrcostListToMaps(pojo.getItem());
                //item转数据源
                JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);

                //报表生成
                InputStream stream = new ByteArrayInputStream(content.getBytes());
                //编译报表
                JasperReport jasperReport = JasperCompileManager.compileReport(stream);

                //数据填充
                JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
                if (a == 0) {
                    printAll = print;
                } else {
                    List<JRPrintPage> pages = print.getPages();
                    for (JRPrintPage page : pages) {
                        printAll.addPage(page);
                    }
                }
            }
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(printAll, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    @ApiOperation(value = "批量云打印BuyFinishing单据(ids)", notes = "json={KEY,KEY},ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printBatchWebBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.Print")
    public R<String> printBatchWebBill(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<String> lstptJson = new ArrayList<>();
            String ptRefNoMain = "";

            for (String key : lstkeys) {
                //=========获取单据表头信息========
                BuyFinishingPojo buyFinishingPojo = this.buyFinishingService.getEntity(key, loginUser.getTenantid());
                if (buyFinishingPojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }

                // 获取单据表头.表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(buyFinishingPojo);

                // 获取单据表头.加入公司信息
                PrintUtils.addCompanyInfo(map, loginUser);
                //=========获取单据Item信息========
                List<BuyFinishingitemPojo> lstitem = this.buyFinishingitemService.getList(key, loginUser.getTenantid());
                // 单据Item. 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = attrcostListToMaps(lstitem);
                // === 整理Map.row=====
                Map<String, Object> maprow = new LinkedHashMap<>();
                maprow.put("row", lst);
                // === 整理report=xml+grparam=====
                Map<String, Object> mapreport = new LinkedHashMap<>();
                mapreport.put("xml", maprow);
                mapreport.put("_grparam", map);
                // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                Map<String, Object> mapdata = new LinkedHashMap<>();
                mapdata.put("report", mapreport);
                // ====Map转Json ==== 注 时间转String 格式；
                String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
                lstptJson.add(ptJson);
                ptRefNoMain += buyFinishingPojo.getRefno() + ",";
                // 刷入打印Num++
                BuyFinishingPojo billPrintPojo = new BuyFinishingPojo();
                billPrintPojo.setId(buyFinishingPojo.getId());
                billPrintPojo.setPrintcount(buyFinishingPojo.getPrintcount() + 1);
                billPrintPojo.setTenantid(buyFinishingPojo.getTenantid());
                this.buyFinishingService.updatePrintcount(billPrintPojo);
            }
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else {
                mapPrint.put("code", "batchprint");
            }
            String[] lstRefno = ptRefNoMain.split(",");
            mapPrint.put("msg", "BuyFinishing：" + lstRefno[0] + "~" + lstRefno[lstRefno.length - 1]);    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setKeyValue("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            mapPrint.put("ptid", ptid);   // 报表id
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "云打印BuyFinishing明细报表(分页PageList)", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.Print")
    public R<String> printWebPageList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis, HttpServletRequest request) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 获取请求的路径,/分割
            String[] pathParts = request.getRequestURI().split("/");
            String moduleCode = pathParts[pathParts.length - 2];
            if ("D03M03B1".equals(moduleCode)) {
                qpfilter += " and Buy_Finishing.BillType in ('采购验收','采购退货')";
            } else if ("D03M03B2".equals(moduleCode)) {
                qpfilter += " and Buy_Finishing.BillType in ('其他收货','其他退货')";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BuyFinishingitemdetailPojo> lstitem = this.buyFinishingService.getPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "BuyFinishing明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setKeyValue("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "批量云打印报表(List<BuyFinishingPojo>不带item)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),groupid不为null则只获取第一条客户信息", produces = "application/json")
    @RequestMapping(value = "/printWebPageTh", method = RequestMethod.POST)
    //    @PreAuthorize(hasPermi = "Buy_Finishing.Print")
    public R<String> printWebPageTh(@RequestBody String json, String ptid, String groupid, String sn, Integer cmd, Integer redis, HttpServletRequest request) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 获取请求的路径,/分割
            String[] pathParts = request.getRequestURI().split("/");
            String moduleCode = pathParts[pathParts.length - 2];
            if ("D03M03B1".equals(moduleCode)) {
                qpfilter += " and Buy_Finishing.BillType in ('采购验收','采购退货')";
            } else if ("D03M03B2".equals(moduleCode)) {
                qpfilter += " and Buy_Finishing.BillType in ('其他收货','其他退货')";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BuyFinishingPojo> lstTh = this.buyFinishingService.getPageTh(queryParam).getList();
            // 获取单据表头.表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (groupid != null && lstTh.size() > 0) {
                map.put("groupname", lstTh.get(0).getGroupname());
                map.put("abbreviate", lstTh.get(0).getAbbreviate());
                map.put("groupuid", lstTh.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstTh);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "wip批量打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setKeyValue("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "一页两联打印", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBillMulti", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Finishing.Print")
    public R<String> printWebBillMulti(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            BuyFinishingPojo buyFinishingPojo = this.buyFinishingService.getEntity(key, loginUser.getTenantid());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            //R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            //if (r.getCode() == 200) {
            //    Map<String, String> tencfg = r.getData(); //
            //    String printapproved = tencfg.get("system.bill.printapproved");
            //    if (printapproved != null && printapproved.equals("true") && buyFinishingPojo.getAssessor().equals("")) {
            //        throw new BaseBusinessException("请先审核单据");
            //    }
            //} else {
            //    throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            //}

            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(buyFinishingPojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            QueryParam queryParam = new QueryParam();
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setFilterstr(" and Buy_FinishingItem.Pid='" + key + "'");
            queryParam.setOrderBy("Buy_FinishingItem.RowNum");
            queryParam.setPageNum(1);
            queryParam.setPageSize(1000);
            List<BuyFinishingitemdetailPojo> lstitem = this.buyFinishingService.getPageList(queryParam).getList();
            List<Map<String, Object>> lstMap = attrcostListToMaps(lstitem);
            // 一式两份
            List<Map<String, Object>> lst = lstMap.stream()
                    .map(item -> {
                        Map<String, Object> hashMap = new HashMap<>(item);
                        hashMap.put("ToWho", 1);
                        hashMap.put("PageNo", 1);
                        return hashMap;
                    })
                    .collect(Collectors.toList());
            List<Map<String, Object>> lstCopy = lstMap.stream()
                    .map(item -> {
                        Map<String, Object> hashMap = new HashMap<>(item);
                        hashMap.put("ToWho", 2);
                        hashMap.put("PageNo", 1);
                        return hashMap;
                    })
                    .collect(Collectors.toList());
            lst.addAll(lstCopy);
            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "BuyFinishing" + buyFinishingPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限
            // 刷入打印Num++
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

