package inks.service.sa.som.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.constant.MyConstant;
import inks.service.sa.som.domain.pojo.MatCarryoverPojo;
import inks.service.sa.som.domain.pojo.MatCarryoverrecPojo;
import inks.service.sa.som.service.MatCarryoverService;
import inks.service.sa.som.service.MatCarryoverrecService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
 * 仓库结账(Mat_CarryoverRec)表控制层
 *
 * <AUTHOR>
 * @since 2022-10-05 12:50:55
 */
@RestController
@RequestMapping("D04M05B1")
@Api(tags = "D04M05B1:仓库结账")
public class D04M05B1Controller extends MatCarryoverrecController {

    @Resource
    private MatCarryoverrecService matCarryoverrecService;


    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaBillcodeService saBillcodeService;


    @Resource
    private MatCarryoverService matCarryoverService;

    /**
     * 新增数据
     *
     * @return 新增结果
     */
    @ApiOperation(value = " 新增结转记录", notes = "新增结转记录", produces = "application/json")
    @RequestMapping(value = "/open", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CarryoverRec.Add")
    public R<MatCarryoverrecPojo> open(Integer year, Integer month) {
        try {
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));


            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatCarryoverrecPojo matCarryoverrecPojo = this.matCarryoverrecService.getEntityByMax(loginUser.getTenantid());
            if (matCarryoverrecPojo != null) {
                return R.fail("已有开账记录,禁止重复开账");
            }
            matCarryoverrecPojo = new MatCarryoverrecPojo();
            matCarryoverrecPojo.setCarryyear(year);
            matCarryoverrecPojo.setCarrymonth(month);
            matCarryoverrecPojo.setRownum(Integer.parseInt(strRowNum));
            matCarryoverrecPojo.setStartdate(DateUtils.parseDate(year + "-" + month + "-1 00:00:00"));
            matCarryoverrecPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(matCarryoverrecPojo.getStartdate(), 1), -1));
            matCarryoverrecPojo.setCreateby(loginUser.getRealName());   // 创建者
            matCarryoverrecPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matCarryoverrecPojo.setCreatedate(new Date());   // 创建时间
            matCarryoverrecPojo.setLister(loginUser.getRealname());   // 制表
            matCarryoverrecPojo.setListerid(loginUser.getUserid());    // 制表id
            matCarryoverrecPojo.setModifydate(new Date());   //修改时间
            matCarryoverrecPojo.setTenantid(loginUser.getTenantid());   //租户id
            matCarryoverrecPojo.setTenantname(loginUser.getTenantinfo().getTenantname());

            if (DateUtils.getTimestamp(matCarryoverrecPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            return R.ok(this.matCarryoverrecService.insert(matCarryoverrecPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = " 获取结转记录最新记录", notes = "获取结转记录最新记录", produces = "application/json")
    @RequestMapping(value = "/getEntityByMax", method = RequestMethod.GET)
    public R<MatCarryoverrecPojo> getEntityByMax() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matCarryoverrecService.getEntityByMax(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @return 新增结果
     */
    @ApiOperation(value = "初期化全部仓库账单", notes = "初期化全部仓库账单", produces = "application/json")
    @RequestMapping(value = "/batchCreate", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Account.Add")
    public R<Integer> batchCreate(Integer year, Integer month) {
        try {
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));
            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatCarryoverrecPojo matCarryoverrecPojo = this.matCarryoverrecService.getEntityByMax(loginUser.getTenantid());
            if (matCarryoverrecPojo == null) {
                R.fail("请先开账");
            }
            MatCarryoverPojo matCarryoverPojo = new MatCarryoverPojo();
            matCarryoverPojo.setStartdate(DateUtils.addSeconds(matCarryoverrecPojo.getEnddate(), 1));
            matCarryoverPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(matCarryoverPojo.getStartdate(), 1), -1));
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D04M05B1", loginUser.getTenantid(),"Bus_Account");
            matCarryoverPojo.setRefno(refNo);
            matCarryoverPojo.setBilltype("仓库结转");
            matCarryoverPojo.setCarryyear(year);
            matCarryoverPojo.setCarrymonth(month);
            matCarryoverPojo.setRownum(Integer.parseInt(strRowNum));
            matCarryoverPojo.setCreateby(loginUser.getRealName());   // 创建者
            matCarryoverPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matCarryoverPojo.setCreatedate(new Date());   // 创建时间
            matCarryoverPojo.setLister(loginUser.getRealname());   // 制表
            matCarryoverPojo.setListerid(loginUser.getUserid());    // 制表id
            matCarryoverPojo.setModifydate(new Date());   //修改时间
            matCarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            matCarryoverPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            if (DateUtils.getTimestamp(matCarryoverPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            return R.ok(this.matCarryoverService.batchCreate(matCarryoverPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除结转记录", notes = "删除结转记录", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CarryoverRec.Delete")
    //@OperLog(title = "删除结转记录")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatCarryoverrecPojo matCarryoverrecPojo = this.matCarryoverrecService.getEntityByMax(loginUser.getTenantid());
            if (key.equals(matCarryoverrecPojo.getId())) {
                return R.ok(this.matCarryoverrecService.delete(key, loginUser.getTenantid()));
            } else {
                return R.fail("只可以对最后一期返结账");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    //---------------------------做异步任务-------------------------

    @ApiOperation(value = "Start初期化全部仓库账单", notes = "初期化全部仓库账单", produces = "application/json")
    @RequestMapping(value = "/batchCreateStart", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_CarryoverRec.Add")
    public R<String> batchCreateStart(Integer year, Integer month) {
        try {
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));
            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatCarryoverrecPojo matCarryoverrecPojo = this.matCarryoverrecService.getEntityByMax(loginUser.getTenantid());
            if (matCarryoverrecPojo == null) {
                R.fail("请先开账");
            }
            MatCarryoverPojo matCarryoverPojo = new MatCarryoverPojo();
            matCarryoverPojo.setStartdate(DateUtils.addSeconds(matCarryoverrecPojo.getEnddate(), 1));
            matCarryoverPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(matCarryoverPojo.getStartdate(), 1), -1));
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D04M06B1", loginUser.getTenantid(),"Mat_Carryover");
            matCarryoverPojo.setRefno(refNo);
            matCarryoverPojo.setBilltype("仓库结转");
            matCarryoverPojo.setCarryyear(year);
            matCarryoverPojo.setCarrymonth(month);
            matCarryoverPojo.setRownum(Integer.parseInt(strRowNum));
            matCarryoverPojo.setCreateby(loginUser.getRealName());   // 创建者
            matCarryoverPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matCarryoverPojo.setCreatedate(new Date());   // 创建时间
            matCarryoverPojo.setLister(loginUser.getRealname());   // 制表
            matCarryoverPojo.setListerid(loginUser.getUserid());    // 制表id
            matCarryoverPojo.setModifydate(new Date());   //修改时间
            matCarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            matCarryoverPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            if (DateUtils.getTimestamp(matCarryoverPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            // -----开始异步  批量生产账单---
            // uuid作为Redis的hkey
            String uuid = inksSnowflake.getSnowflake().nextIdStr();
            this.matCarryoverService.batchCreateStart(matCarryoverPojo, uuid);
            return R.ok(uuid);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "State初期化全部销售账单", notes = "取客户应收款列表By销售单", produces = "application/json")
    @RequestMapping(value = "/batchCreateState", method = RequestMethod.GET)
    public R<Map<String, Object>> batchCreateState(@RequestParam String key) {
        Map<String, Object> state = this.saRedisService.getCacheObject(MyConstant.STOREBATCHCREATE_CODE + key, Map.class);
        return R.ok(state);
    }


}
