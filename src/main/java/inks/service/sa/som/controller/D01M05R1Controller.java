package inks.service.sa.som.controller;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.service.D01M05R1Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("D01M05R1")
@Api(tags = "D01M05R1:销售发票报表")
public class D01M05R1Controller {
    @Resource
    private D01M05R1Service d01M05R1Service;

    @Resource
    private SaRedisService saRedisService;

    /*
     *
     * <AUTHOR>
     * @description 根据当前月查询本月开票
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "根据当前月查询本月开票", notes = "根据当前月查询本月开票", produces = "application/json")
    @RequestMapping(value = "/getTagSumAmtByMonth", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Bus_Invoice.List")
    public R<List<ChartPojo>> getTagSumAmtByMonth() {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.d01M05R1Service.getTagSumAmtByMonth(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
