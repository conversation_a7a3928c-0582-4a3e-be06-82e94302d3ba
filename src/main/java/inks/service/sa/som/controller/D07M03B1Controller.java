package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.StringUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.FmCashcarryoverPojo;
import inks.service.sa.som.domain.pojo.FmCashcarryoveritemPojo;
import inks.service.sa.som.service.FmCashcarryoverService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 出纳账单(FmCashcarryover)表控制层
 *
 * <AUTHOR>
 * @since 2022-09-26 19:41:23
 */
@RestController
@RequestMapping("D07M03B1")
@Api(tags = "D07M03B1:出纳账单")
public class D07M03B1Controller extends FmCashcarryoverController {
    @Resource
    private FmCashcarryoverService fmCashcarryoverService;

    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "现金银行明细", notes = "现金银行明细", produces = "application/json")
    @RequestMapping(value = "/getBillItemList", method = RequestMethod.POST)
    public R<PageInfo<FmCashcarryoveritemPojo>> getBillItemList(@RequestBody String json, String groupid, String cashaccid) {
        try {
            QueryParam queryParam = JSON.parseObject(json, QueryParam.class);
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String filter = "";
            if (StringUtils.isNotEmpty(groupid)) {
                filter += " and App_Workgroup.id='" + groupid + "'";
            }
            if (StringUtils.isNotEmpty(cashaccid)) {
                filter += " and CashAccid='" + cashaccid + "'";
            }
            queryParam.setFilterstr(filter);
            return R.ok(this.fmCashcarryoverService.getBillItemList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按年月分页查询", notes = "按年月分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageThByMonth", method = RequestMethod.POST)
    public R<PageInfo<FmCashcarryoverPojo>> getPageThByMonth(@RequestBody String json, Integer year, Integer month) {
        try {
            if (year == null || month == null) return R.fail("请传year和month参数");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Fm_CashCarryover.RowNum");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and Fm_CashCarryover.carryyear=" + year + " and Fm_CashCarryover.carrymonth=" + month);
            return R.ok(this.fmCashcarryoverService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    /**
//     * 新增数据
//     *
//     * @param json 实体
//     * @return 新增结果
//     */
//    @ApiOperation(value = "跨期拉取销售对账item", notes = "跨期拉取销售对账item,包含已对账和未对账", produces = "application/json")
//    @RequestMapping(value = "/getSaleAccountList", method = RequestMethod.POST)
//    public R<List<BusAccountitemPojo>> getSaleAccountList(@RequestBody String json, String groupid) {
//        try {
//            List<BusAccountitemPojo> lstRt = new ArrayList<>();
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            // 获得用户数据
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            Date _endDate = queryParam.getDateRange().getEndDate();
//            // 当前结算到哪一时间
//            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
//            if (busAccountrecPojo != null) {
//                queryParam.getDateRange().setEndDate(busAccountrecPojo.getEnddate());
//                queryParam.setFilterstr(" and Bus_Account.Groupid='" + groupid + "'");
//                List<BusAccountitemPojo> lstAccoitem = this.busAccountService.getMultItemList(queryParam);
//                if (lstAccoitem != null) {
//                    for (BusAccountitemPojo item : lstAccoitem) {
//                        BusAccountitemPojo rtitem = new BusAccountitemPojo();
//                        BeanUtils.copyProperties(item, rtitem);
//                        lstRt.add(rtitem);
//                    }
//                }
//            }
//
//            BusAccountPojo busAccountPojo = new BusAccountPojo();
//            busAccountPojo.setTenantid(loginUser.getTenantid());
//            busAccountPojo.setGroupid(groupid);
//            if (busAccountrecPojo != null) {
//                busAccountPojo.setStartdate(DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1));
//            }
//            busAccountPojo.setEnddate(_endDate);
//            List<BusAccountitemPojo> lstpullItem = this.busAccountService.pullItemList(busAccountPojo);
//
//            if (lstRt.size() > 0) {
//                if (lstpullItem != null) {
//                    for (BusAccountitemPojo item : lstpullItem) {
//                        BusAccountitemPojo rtitem = new BusAccountitemPojo();
//                        BeanUtils.copyProperties(item, rtitem);
//                        lstRt.add(rtitem);
//                    }
//                }
//                return R.ok(lstRt);
//            } else {
//                return R.ok(lstpullItem);
//            }
//
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//
//    /**
//     * 通过主键查询单条数据
//     *
//     * @param json 主键
//     * @return 单条数据
//     */
//    @ApiOperation(value = "获取客户实时应收款报表", notes = "获取客户实时应付款报表", produces = "application/json")
//    @RequestMapping(value = "/getNowPageList", method = RequestMethod.POST)
//    public R<PageInfo<BusAccountPojo>> getNowPageList(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
//                queryParam.setOrderBy("GroupUid");
//            // 获得用户数据
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
//            Date startDate = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy", new Date()) + "-01-01");
//            Date endDate = new Date();
//            if (busAccountrecPojo != null) {
//                startDate = DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1);
//            }
//            DateRange dateRange = new DateRange("", startDate, endDate);
//            queryParam.setDateRange(dateRange);
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.busAccountService.getNowPageList(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
}
