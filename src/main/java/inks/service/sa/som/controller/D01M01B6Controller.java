package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.AppWorkgroupPojo;
import inks.service.sa.som.service.AppWorkgroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 潜在客户(App_Wg_Prospects)表控制层
 * Customer 客户 Supplier 供应商 Branch 其他部门  Workshop 生产车间  Factory 外协厂商 Prospects潜在客户
 *
 * <AUTHOR>
 * @since 2021-11-11 09:10:03
 */
@RestController
@RequestMapping("D01M01B6")
@Api(tags = "D01M01B6:往来单位:潜在客户")
public class D01M01B6Controller extends AppWorkgroupController {

    @Resource
    private AppWorkgroupService appWorkgroupService;


    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Prospects.List")
    public R<PageInfo<AppWorkgroupPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("App_Workgroup.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setFilterstr(" and GroupType='潜在客户'");
            return R.ok(this.appWorkgroupService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增潜在客户", notes = "新增潜在客户", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Prospects.Add")
    public R<AppWorkgroupPojo> create(@RequestBody String json) {
        try {
            AppWorkgroupPojo appWorkgroupPojo = JSONArray.parseObject(json, AppWorkgroupPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            appWorkgroupPojo.setCreateby(loginUser.getRealname());   // 创建者
            appWorkgroupPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            appWorkgroupPojo.setCreatedate(new Date());   // 创建时间
            appWorkgroupPojo.setLister(loginUser.getRealname());   // 制表
            appWorkgroupPojo.setListerid(loginUser.getUserid());    // 制表id
            appWorkgroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            appWorkgroupPojo.setGrouptype("潜在客户");
            appWorkgroupPojo.setId(""); // 去掉null
            //重名检查
            AppWorkgroupPojo appWorkgroupPojo1 = this.appWorkgroupService.getEntityByUid(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail( "编码重复");
            }
            appWorkgroupPojo1 = this.appWorkgroupService.getEntityByName(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail( "名称重复");
            }
            return R.ok(this.appWorkgroupService.insert(appWorkgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改往来单位", notes = "修改往来单位", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "App_Wg_Prospects.Edit")
    public R<AppWorkgroupPojo> update(@RequestBody String json) {
        try {
            AppWorkgroupPojo appWorkgroupPojo = JSONArray.parseObject(json, AppWorkgroupPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            appWorkgroupPojo.setLister(loginUser.getRealname());   // 制表
            appWorkgroupPojo.setListerid(loginUser.getUserid());    // 制表id
            appWorkgroupPojo.setTenantid(loginUser.getTenantid());   //租户id
            appWorkgroupPojo.setModifydate(new Date());   //修改时间

            //重名检查
            AppWorkgroupPojo appWorkgroupPojo1 = this.appWorkgroupService.getEntityByUid(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail( "编码重复");
            }
            appWorkgroupPojo1 = this.appWorkgroupService.getEntityByName(appWorkgroupPojo);
            if (appWorkgroupPojo1 != null) {
                return R.fail( "名称重复");
            }

            return R.ok(this.appWorkgroupService.update(appWorkgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除往来单位", notes = "删除往来单位", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "App_Wg_Prospects.Delete")
    //@OperLog(title = "删除潜在客户信息")
    public R<AppWorkgroupPojo> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //检查引用
            List<String> lstcite = this.appWorkgroupService.getCiteBillName(key, loginUser.getTenantid());
            if (lstcite.size() > 0) {
                return R.fail( "禁止删除,被以下单据引用:" + lstcite);
            }
            // 未删除前先查询
            AppWorkgroupPojo workgroupDB = appWorkgroupService.getEntity(key, loginUser.getTenantid());
            this.appWorkgroupService.delete(key, loginUser.getTenantid());
            return R.ok(workgroupDB, workgroupDB.getGroupname() + "删除成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

