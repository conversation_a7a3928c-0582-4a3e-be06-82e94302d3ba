package inks.service.sa.som.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.constant.MyConstant;
import inks.service.sa.som.domain.pojo.BuyAccountPojo;
import inks.service.sa.som.domain.pojo.BuyAccountrecPojo;
import inks.service.sa.som.service.BuyAccountService;
import inks.service.sa.som.service.BuyAccountrecService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * 采购结账(Buy_AccountRec)表控制层
 *
 * <AUTHOR>
 * @since 2022-07-28 20:24:37
 */
@RestController
@RequestMapping("D03M10B1")
@Api(tags = "D03M10B1:采购结账")
public class D03M10B1Controller extends BuyAccountrecController {

    @Resource
    private BuyAccountrecService buyAccountrecService;

    @Resource
    private BuyAccountService buyAccountService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private SaBillcodeService saBillcodeService;

    /**
     * 新增数据
     *
     * @return 新增结果
     */
    @ApiOperation(value = " 新增结转记录", notes = "新增结转记录", produces = "application/json")
    @RequestMapping(value = "/open", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_AccountRec.Add")
    public R<BuyAccountrecPojo> open(Integer year, Integer month) {
        try {
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));


            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BuyAccountrecPojo buyAccountrecPojo = this.buyAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (buyAccountrecPojo != null) {
                return R.fail("已有开账记录,禁止重复开账");
            }
            buyAccountrecPojo = new BuyAccountrecPojo();
            buyAccountrecPojo.setCarryyear(year);
            buyAccountrecPojo.setCarrymonth(month);
            buyAccountrecPojo.setRownum(Integer.parseInt(strRowNum));
            buyAccountrecPojo.setStartdate(DateUtils.parseDate(year + "-" + month + "-1 00:00:00"));
            buyAccountrecPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(buyAccountrecPojo.getStartdate(), 1), -1));
            buyAccountrecPojo.setCreateby(loginUser.getRealName());   // 创建者
            buyAccountrecPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            buyAccountrecPojo.setCreatedate(new Date());   // 创建时间
            buyAccountrecPojo.setLister(loginUser.getRealname());   // 制表
            buyAccountrecPojo.setListerid(loginUser.getUserid());    // 制表id
            buyAccountrecPojo.setModifydate(new Date());   //修改时间
            buyAccountrecPojo.setTenantid(loginUser.getTenantid());   //租户id
            buyAccountrecPojo.setTenantname(loginUser.getTenantinfo().getTenantname());

            if (DateUtils.getTimestamp(buyAccountrecPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            return R.ok(this.buyAccountrecService.insert(buyAccountrecPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = " 获取结转记录最新记录", notes = "获取结转记录最新记录", produces = "application/json")
    @RequestMapping(value = "/getEntityByMax", method = RequestMethod.GET)
    public R<BuyAccountrecPojo> getEntityByMax() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyAccountrecService.getEntityByMax(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @return 新增结果
     */
    @ApiOperation(value = "初期化全部销售账单", notes = "初期化全部销售账单", produces = "application/json")
    @RequestMapping(value = "/batchCreate", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Account.Add")
    public R<Integer> batchCreate(Integer year, Integer month) {
        try {
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));
            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BuyAccountrecPojo buyAccountrecPojo = this.buyAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (buyAccountrecPojo == null) {
                R.fail("请先开账");
            }
            BuyAccountPojo buyAccountPojo = new BuyAccountPojo();
            buyAccountPojo.setStartdate(DateUtils.addSeconds(buyAccountrecPojo.getEnddate(), 1));
            buyAccountPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(buyAccountPojo.getStartdate(), 1), -1));
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D03M10B1", loginUser.getTenantid(),"Bus_Account");
            buyAccountPojo.setRefno(refNo);
            buyAccountPojo.setBilltype("采购账单");
            buyAccountPojo.setCarryyear(year);
            buyAccountPojo.setCarrymonth(month);
            buyAccountPojo.setRownum(Integer.parseInt(strRowNum));
            buyAccountPojo.setCreateby(loginUser.getRealName());   // 创建者
            buyAccountPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            buyAccountPojo.setCreatedate(new Date());   // 创建时间
            buyAccountPojo.setLister(loginUser.getRealname());   // 制表
            buyAccountPojo.setListerid(loginUser.getUserid());    // 制表id
            buyAccountPojo.setModifydate(new Date());   //修改时间
            buyAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            buyAccountPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            if (DateUtils.getTimestamp(buyAccountPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            return R.ok(this.buyAccountService.batchCreate(buyAccountPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除结转记录", notes = "删除结转记录", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_AccountRec.Delete")
    //@OperLog(title = "删除结转记录")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BuyAccountrecPojo buyAccountrecPojo = this.buyAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (key.equals(buyAccountrecPojo.getId())) {
                return R.ok(this.buyAccountrecService.delete(key, loginUser.getTenantid()));
            } else {
                return R.fail("只可以对最后一期返结账");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    //---------------------------做异步任务-------------------------
    @ApiOperation(value = "Start初期化全部采购账单", notes = "Start初期化全部采购账单", produces = "application/json")
    @RequestMapping(value = "/batchCreateStart", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Account.Add")
    public R<String> batchCreateStart(Integer year, Integer month) {
        try {
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));
            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BuyAccountrecPojo buyAccountrecPojo = this.buyAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (buyAccountrecPojo == null) {
                R.fail("请先开账");
            }
            BuyAccountPojo buyAccountPojo = new BuyAccountPojo();
            buyAccountPojo.setStartdate(DateUtils.addSeconds(buyAccountrecPojo.getEnddate(), 1));
            buyAccountPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(buyAccountPojo.getStartdate(), 1), -1));
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D03M10B1", loginUser.getTenantid(),"Bus_Account");
            buyAccountPojo.setRefno(refNo);
            buyAccountPojo.setBilltype("采购账单");
            buyAccountPojo.setCarryyear(year);
            buyAccountPojo.setCarrymonth(month);
            buyAccountPojo.setRownum(Integer.parseInt(strRowNum));
            buyAccountPojo.setCreateby(loginUser.getRealName());   // 创建者
            buyAccountPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            buyAccountPojo.setCreatedate(new Date());   // 创建时间
            buyAccountPojo.setLister(loginUser.getRealname());   // 制表
            buyAccountPojo.setListerid(loginUser.getUserid());    // 制表id
            buyAccountPojo.setModifydate(new Date());   //修改时间
            buyAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            buyAccountPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            if (DateUtils.getTimestamp(buyAccountPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            // -----开始异步  批量生产账单---
            // uuid作为Redis的hkey
            String uuid = UUID.randomUUID().toString();
            this.buyAccountService.batchCreateStart(buyAccountPojo, uuid);
            return R.ok(uuid);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "State初期化全采购售账单", notes = "State初期化全采购售账单", produces = "application/json")
    @RequestMapping(value = "/batchCreateState", method = RequestMethod.GET)
    public R<Map<String, Object>> batchCreateState(@RequestParam String key) {
        Map<String, Object> state = this.saRedisService.getCacheObject(MyConstant.BUYBATCHCREATE_CODE + key, Map.class);
        return R.ok(state);
    }
}
