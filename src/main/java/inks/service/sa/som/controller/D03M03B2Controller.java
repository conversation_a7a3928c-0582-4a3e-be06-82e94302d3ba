package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.BuyFinishingPojo;
import inks.service.sa.som.domain.pojo.BuyFinishingitemdetailPojo;
import inks.service.sa.som.service.BuyFinishingService;
import inks.service.sa.som.service.BuyFinishingitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 采购验收(BuyFinishing)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 14:59:51
 */
@RestController
@RequestMapping("D03M03B2")
@Api(tags = "D03M03B2:其他收货")
public class D03M03B2Controller extends BuyFinishingController {

    @Resource
    private BuyFinishingService buyFinishingService;


    @Resource
    private BuyFinishingitemService buyFinishingitemService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private SaBillcodeService saBillcodeService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingitemdetailPojo>> getPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Buy_Finishing.BillType in ('其他收货','其他退货')";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyFinishingService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Buy_FinishingItem.FinishQty<Buy_FinishingItem.Quantity";
            qpfilter += " and Buy_FinishingItem.DisannulMark=0 and Buy_FinishingItem.Closed=0";
            qpfilter += " and Buy_Finishing.BillType in ('其他收货','其他退货')";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyFinishingService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingPojo>> getPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Buy_Finishing.BillType in ('其他收货','其他退货')";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyFinishingService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Finishing.List")
    public R<PageInfo<BuyFinishingPojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Buy_Finishing.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Buy_Finishing.BillType in ('其他收货','其他退货')";
            qpfilter += " and Buy_Finishing.FinishCount+Buy_Finishing.DisannulCount<Buy_Finishing.ItemCount";
            if (groupid != null) {
                qpfilter += " and Buy_Finishing.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyFinishingService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
