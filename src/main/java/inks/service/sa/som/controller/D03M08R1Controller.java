package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.BuyAccountPojo;
import inks.service.sa.som.service.BuyAccountService;
import inks.service.sa.som.service.BuyAccountitemService;
import inks.service.sa.som.service.BuyAccountrecService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 应付账单(BuyAccount)表控制层
 *
 * <AUTHOR>
 * @since 2022-06-09 08:28:58
 */
@RestController
@RequestMapping("D03M08R1")
@Api(tags = "D03M08R1:采购账单,临时为了打印")
public class D03M08R1Controller {

    @Resource
    private BuyAccountrecService buyAccountrecService;

    @Resource
    private BuyAccountService buyAccountService;


    @Resource
    private BuyAccountitemService buyAccountitemService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "打印列表", notes = "打印列表", produces = "application/json")
    @RequestMapping(value = "/printList", method = RequestMethod.POST)
    public void printList(@RequestBody String json, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取列表信息
        BuyAccountPojo buyAccountPojo = JSONObject.parseObject(json, BuyAccountPojo.class);
        //表头转MAP(空)
        Map<String, Object> map = new HashMap<>();
        map.put("groupname", buyAccountPojo.getGroupname());
        map.put("startdate", buyAccountPojo.getStartdate());
        map.put("enddate", buyAccountPojo.getEnddate());
        //lst转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(buyAccountPojo.getItem());
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

}
