package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.FmDepotransferPojo;
import inks.service.sa.som.domain.pojo.FmDepotransferitemPojo;
import inks.service.sa.som.domain.pojo.FmDepotransferitemdetailPojo;
import inks.service.sa.som.service.FmDepotransferService;
import inks.service.sa.som.service.FmDepotransferitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 预收核转(Fm_DepoTransfer)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-14 17:21:14
 */
//@RestController
//@RequestMapping("fmDepotransfer")
public class FmDepotransferController {

    @Resource
    private FmDepotransferService fmDepotransferService;
    @Resource
    private FmDepotransferitemService fmDepotransferitemService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaBillcodeService saBillcodeService;

    private final static Logger logger = LoggerFactory.getLogger(FmDepotransferController.class);


    @ApiOperation(value = " 获取预收核转详细信息", notes = "获取预收核转详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Fm_DepoTransfer.List")
    public R<FmDepotransferPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmDepotransferService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_DepoTransfer.List")
    public R<PageInfo<FmDepotransferitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Fm_DepoTransfer.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.fmDepotransferService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取预收核转详细信息", notes = "获取预收核转详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Fm_DepoTransfer.List")
    public R<FmDepotransferPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmDepotransferService.getBillEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_DepoTransfer.List")
    public R<PageInfo<FmDepotransferPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Fm_DepoTransfer.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.fmDepotransferService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_DepoTransfer.List")
    public R<PageInfo<FmDepotransferPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Fm_DepoTransfer.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.fmDepotransferService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增预收核转", notes = "新增预收核转", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_DepoTransfer.Add")
    public R<FmDepotransferPojo> create(@RequestBody String json) {
        try {
            FmDepotransferPojo fmDepotransferPojo = JSONArray.parseObject(json, FmDepotransferPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D07M12B1", loginUser.getTenantid(), "Fm_DepoTransfer");
            fmDepotransferPojo.setRefno(refNo);
            fmDepotransferPojo.setCreateby(loginUser.getRealName());   // 创建者
            fmDepotransferPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            fmDepotransferPojo.setCreatedate(new Date());   // 创建时间
            fmDepotransferPojo.setLister(loginUser.getRealname());   // 制表
            fmDepotransferPojo.setListerid(loginUser.getUserid());    // 制表id            
            fmDepotransferPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.fmDepotransferService.insert(fmDepotransferPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改预收核转", notes = "修改预收核转", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_DepoTransfer.Edit")
    public R<FmDepotransferPojo> update(@RequestBody String json) {
        try {
            FmDepotransferPojo fmDepotransferPojo = JSONArray.parseObject(json, FmDepotransferPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            fmDepotransferPojo.setLister(loginUser.getRealname());   // 制表
            fmDepotransferPojo.setListerid(loginUser.getUserid());    // 制表id   
            fmDepotransferPojo.setModifydate(new Date());   //修改时间
            fmDepotransferPojo.setAssessor(""); //审核员
            fmDepotransferPojo.setAssessorid(""); //审核员
            fmDepotransferPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.fmDepotransferService.update(fmDepotransferPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除预收核转", notes = "删除预收核转", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Fm_DepoTransfer.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmDepotransferService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value = " 新增预收核转Item", notes = "新增预收核转Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_DepoTransfer.Add")
    public R<FmDepotransferitemPojo> createItem(@RequestBody String json) {
        try {
            FmDepotransferitemPojo fmDepotransferitemPojo = JSONArray.parseObject(json, FmDepotransferitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmDepotransferitemService.insert(fmDepotransferitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 修改预收核转Item", notes = "修改预收核转Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_DepoTransfer.Edit")
    public R<FmDepotransferitemPojo> updateItem(@RequestBody String json) {
        try {
            FmDepotransferitemPojo fmDepotransferitemPojo = JSONArray.parseObject(json, FmDepotransferitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmDepotransferitemService.update(fmDepotransferitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除预收核转Item", notes = "删除预收核转Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Fm_DepoTransfer.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmDepotransferitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "审核预收核转", notes = "审核预收核转", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Fm_DepoTransfer.Approval")
    public R<FmDepotransferPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            FmDepotransferPojo fmDepotransferPojo = this.fmDepotransferService.getEntity(key);
            if (fmDepotransferPojo.getAssessor().equals("")) {
                fmDepotransferPojo.setAssessor(loginUser.getRealname()); //审核员
                fmDepotransferPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                fmDepotransferPojo.setAssessor(""); //审核员
                fmDepotransferPojo.setAssessorid(""); //审核员
            }
            fmDepotransferPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.fmDepotransferService.approval(fmDepotransferPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Fm_DepoTransfer.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        FmDepotransferPojo fmDepotransferPojo = this.fmDepotransferService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(fmDepotransferPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = fmDepotransferPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    FmDepotransferitemPojo fmDepotransferitemPojo = new FmDepotransferitemPojo();
                    fmDepotransferPojo.getItem().add(fmDepotransferitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(fmDepotransferPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

