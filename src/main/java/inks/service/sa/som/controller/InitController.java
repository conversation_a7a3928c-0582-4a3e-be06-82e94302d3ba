package inks.service.sa.som.controller;

import inks.common.core.domain.R;
import inks.service.sa.som.mapper.InitMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@RestController
@Api(tags = "Mysql初始化数据表")
@RequestMapping("/Init")
public class InitController {
    @Resource
    private InitMapper initMapper;


    /** 需要被清空的全部表名  以下暂不清空："Mat_Goods","Mat_Group","Mat_Sku","Mat_SkuModel","Mat_Storage",*/
    private static final List<String> TABLES = Arrays.asList(
            "Bus_Account","Bus_AccountArap","Bus_AccountInvo","Bus_AccountItem","Bus_AccountRec",
            "Bus_Deduction","Bus_DeductionItem","Bus_Deliery","Bus_DelieryItem","Bus_Deposit",
            "Bus_DepositCash","Bus_DepositItem","Bus_Invoice","Bus_InvoiceItem","Bus_Machining",
            "Bus_MachiningItem","Bus_OrderCost","Bus_OrderCostItem","Bus_Quotation","Bus_QuotationItem",
            "Bus_Receipt","Bus_ReceiptCash","Bus_ReceiptItem",
            "Buy_Account","Buy_AccountArap","Buy_AccountInvo","Buy_AccountItem","Buy_AccountRec",
            "Buy_Deduction","Buy_DeductionItem","Buy_Finishing","Buy_FinishingItem",
            "Buy_Invoice","Buy_InvoiceItem","Buy_Order","Buy_OrderItem",
            "Buy_Plan","Buy_PlanItem","Buy_PlanMerge","Buy_Prepayments","Buy_PrepaymentsCash",
            "Buy_PrepaymentsItem","Buy_Voucher","Buy_VoucherCash","Buy_VoucherItem",
            "Fm_Account","Fm_CashAccount","Fm_Cost","Fm_CostItem","Fm_CostType",
            "Fm_DepoTransfer","Fm_DepoTransferItem","Fm_DepoTransferMach","Fm_Income","Fm_IncomeItem",
            "Fm_PayApply","Fm_PayApplyCash","Fm_PayApplyItem","Fm_PayRequest","Fm_PayRequestItem",
            "Mat_AcceType","Mat_Access","Mat_AccessItem","Mat_Attribute","Mat_Bom","Mat_BomItem",
            "Mat_Brand","Mat_CargoSpace","Mat_Carryover","Mat_CarryoverItem","Mat_CarryoverRec",
            "Mat_Combin","Mat_CombinItem","Mat_InveNote","Mat_InveNoteItem",
            "Mat_Inventory","Mat_Requisition","Mat_RequisitionItem",
            "Mat_Transfer","Mat_TransferItem",
            "Wk_MainPlan","Wk_MainPlanItem","Wk_Mrp","Wk_MrpItem","Wk_MrpLog","Wk_MrpObj",
            "Wk_Subcontract","Wk_SubcontractItem","Wk_SubcontractMat",
            "Wk_WipNote","Wk_WipNoteItem","Wk_Worksheet","Wk_WorksheetItem","Wk_WorksheetMat"
    );

    @ApiOperation(value = "初始化数据表 bus/buy/manu/store/fm", produces = "application/json")
    @GetMapping("/initTable")
    public R<String> initTable() {
        long start = System.currentTimeMillis();
        try {
            // 逐张表执行 DELETE
            TABLES.forEach(initMapper::clearTable);

            long cost = System.currentTimeMillis() - start;
            return R.ok("已清空 " + TABLES.size() + " 张表，耗时 " + cost + " ms");
        } catch (Exception e) {
            return R.fail("初始化数据表失败: " + e.getMessage());
        }
    }


}