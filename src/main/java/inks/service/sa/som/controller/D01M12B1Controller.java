package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.AppWorkgroupPojo;
import inks.service.sa.som.domain.pojo.BusAccountPojo;
import inks.service.sa.som.domain.pojo.BusAccountitemPojo;
import inks.service.sa.som.domain.pojo.BusAccountrecPojo;
import inks.service.sa.som.service.AppWorkgroupService;
import inks.service.sa.som.service.BusAccountService;
import inks.service.sa.som.service.BusAccountrecService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 销售账单(BusAccount)表控制层
 *
 * <AUTHOR>
 * @since 2022-04-03 14:28:09
 */
@RestController
@RequestMapping("D01M12B1")
@Api(tags = "D01M12B1:销售账单")
public class D01M12B1Controller extends BusAccountController {

    @Resource
    private AppWorkgroupService appWorkgroupService;

    @Resource
    private BusAccountService busAccountService;

    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaBillcodeService saBillcodeService;

    @Resource
    private BusAccountrecService busAccountrecService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取指定客户销售账单详细信息", notes = "获取指定客户销售账单详细信息,key=Groupid", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByNew", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Account.List")
    public R<BusAccountPojo> getBillEntityByNew(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BusAccountPojo busAccountPojo = new BusAccountPojo();
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            AppWorkgroupPojo wgPojo = this.appWorkgroupService.getEntity(key, loginUser.getTenantid());
            busAccountPojo.setTenantid(loginUser.getTenantid());
            busAccountPojo.setGroupid(key);
            busAccountPojo.setGroupname(wgPojo.getGroupname());
            busAccountPojo.setGroupuid(wgPojo.getGroupuid());
            if (busAccountrecPojo == null) {
                busAccountPojo.setStartdate(DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-01-01 00:00:00", new Date())));
            } else {
                busAccountPojo.setStartdate(DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1));
            }
            busAccountPojo.setEnddate(new Date());
            List<BusAccountitemPojo> lst = this.busAccountService.pullItemList(busAccountPojo);
            busAccountPojo.setItem(lst);
            return R.ok(busAccountPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "拉取销售账单item", notes = "根据主表信息拉取销售账单item,传账单主表", produces = "application/json")
    @RequestMapping(value = "/pullItemList", method = RequestMethod.POST)
    public R<List<BusAccountitemPojo>> pullItemList(@RequestBody String json) {
        try {
            BusAccountPojo busAccountPojo = JSONArray.parseObject(json, BusAccountPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            busAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busAccountService.pullItemList(busAccountPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Account.List")
    public R<PageInfo<BusAccountPojo>> getPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Account.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            if (groupid != null) {
                queryParam.setFilterstr(" and Bus_Account.groupid='" + groupid + "'");
            }
            return R.ok(this.busAccountService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按年月分页查询", notes = "按年月分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageThByMonth", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Account.List")
    public R<PageInfo<BusAccountPojo>> getPageThByMonth(@RequestBody String json, Integer year, Integer month) {
        try {
            if (year == null || month == null) return R.fail("请传year和month参数");
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Account.RowNum");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and Bus_Account.carryyear=" + year + " and Bus_Account.carrymonth=" + month);
            return R.ok(this.busAccountService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getInitPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Account.List")
    public R<PageInfo<BusAccountPojo>> getInitPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Account.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and Bus_Account.BillType='期初建账'");
            return R.ok(this.busAccountService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "初期化全部销售账单", notes = "根据主表信息，初期化全部销售账单", produces = "application/json")
    @RequestMapping(value = "/batchInit", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Account.Add")
    public R<Integer> batchInit(@RequestBody String json) {
        try {
            BusAccountPojo busAccountPojo = JSONArray.parseObject(json, BusAccountPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D01M12B1", loginUser.getTenantid(),"Bus_Account");
            busAccountPojo.setRefno(refNo);
            busAccountPojo.setBilltype("期初建账");
            busAccountPojo.setCreateby(loginUser.getRealName());   // 创建者
            busAccountPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busAccountPojo.setCreatedate(new Date());   // 创建时间
            busAccountPojo.setLister(loginUser.getRealname());   // 制表
            busAccountPojo.setListerid(loginUser.getUserid());    // 制表id
            busAccountPojo.setModifydate(new Date());   //修改时间
            busAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busAccountService.batchInit(busAccountPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = " 获取指定客户结转记录最新记录", notes = "获取指定客户结转记录最新记录, key为 Groupid", produces = "application/json")
    @RequestMapping(value = "/getMaxEntityByGroup", method = RequestMethod.GET)
    public R<BusAccountPojo> getMaxEntityByGroup(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busAccountService.getMaxEntityByGroup(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "获取指定客户结转记录最新记录", notes = "获取指定客户结转记录最新记录, key为 Groupid", produces = "application/json")
    @RequestMapping(value = "/getMaxBillEntityByGroup", method = RequestMethod.GET)
    public R<BusAccountPojo> getMaxBillEntityByGroup(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busAccountService.getMaxBillEntityByGroup(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "跨期拉取销售对账item", notes = "跨期拉取销售对账item,包含已对账和未对账", produces = "application/json")
    @RequestMapping(value = "/getSaleAccountList", method = RequestMethod.POST)
    public R<List<BusAccountitemPojo>> getSaleAccountList(@RequestBody String json, String groupid) {
        try {
            List<BusAccountitemPojo> lstRt = new ArrayList<>();
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            Date _endDate = queryParam.getDateRange().getEndDate();
            // 当前结算到哪一时间
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (busAccountrecPojo != null) {
                queryParam.getDateRange().setEndDate(busAccountrecPojo.getEnddate());
                queryParam.setFilterstr(" and Bus_Account.Groupid='" + groupid + "'");
                List<BusAccountitemPojo> lstAccoitem = this.busAccountService.getMultItemList(queryParam);
                if (lstAccoitem != null) {
                    for (int i = 0; i < lstAccoitem.size(); i++) {
                        BusAccountitemPojo item = lstAccoitem.get(i);
                        BusAccountitemPojo rtitem = new BusAccountitemPojo();
                        BeanUtils.copyProperties(item, rtitem);
                        // if (i>0) rtitem.setOpenamount(0D);  //去掉中间的期初；
                        lstRt.add(rtitem);
                    }
                }
            }

            BusAccountPojo busAccountPojo = new BusAccountPojo();
            busAccountPojo.setTenantid(loginUser.getTenantid());
            busAccountPojo.setGroupid(groupid);
            busAccountPojo.setStartdate(new Date());
            if (busAccountrecPojo != null) {
                busAccountPojo.setStartdate(DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1));
            }
            busAccountPojo.setEnddate(_endDate);
            List<BusAccountitemPojo> lstpullItem = this.busAccountService.pullItemList(busAccountPojo);

            if (lstRt.size() > 0) {
                if (lstpullItem != null) {
                    // lstRt.get(lstRt.size()-1).setCloseamount(0D);  //去掉中间的期末；
                    for (int i = 0; i < lstpullItem.size(); i++) {
                        BusAccountitemPojo item = lstpullItem.get(i);
                        BusAccountitemPojo rtitem = new BusAccountitemPojo();
                        BeanUtils.copyProperties(item, rtitem);
                        // if (i==0) rtitem.setOpenamount(0D); //去掉中间的期初；
                        lstRt.add(rtitem);
                    }
                }
                return R.ok(lstRt);
            } else {
                return R.ok(lstpullItem);
            }

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param json 主键
     * @return 单条数据
     */
    @ApiOperation(value = "获取客户实时应收款报表", notes = "获取客户实时应付款报表", produces = "application/json")
    @RequestMapping(value = "/getNowPageList", method = RequestMethod.POST)
    public R<PageInfo<BusAccountPojo>> getNowPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("GroupUid");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            Date startDate = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy", new Date()) + "-01-01");
            Date endDate = new Date();
            if (busAccountrecPojo != null) {
                startDate = DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1);
            }
            DateRange dateRange = new DateRange("", startDate, endDate);
            queryParam.setDateRange(dateRange);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.busAccountService.getNowPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印列表", notes = "打印列表", produces = "application/json")
    @RequestMapping(value = "/printList", method = RequestMethod.POST)
    public void printList(@RequestBody String json, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取列表信息
        BusAccountPojo busAccountPojo = JSONObject.parseObject(json, BusAccountPojo.class);
        //表头转MAP(空)
        Map<String, Object> map = new HashMap<>();
        map.put("groupname", busAccountPojo.getGroupname());
        map.put("startdate", busAccountPojo.getStartdate());
        map.put("enddate", busAccountPojo.getEnddate());
        //lst转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(busAccountPojo.getItem());
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


}
