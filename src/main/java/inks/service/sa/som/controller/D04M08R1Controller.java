package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.MatRequisitionPojo;
import inks.service.sa.som.domain.pojo.MatRequisitionitemPojo;
import inks.service.sa.som.domain.pojo.MatRequisitionitemdetailPojo;
import inks.service.sa.som.service.MatRequisitionService;
import inks.service.sa.som.service.MatRequisitionitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 物料申领(Mat_Requisition)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-13 13:21:22
 */
@RestController
@RequestMapping("D04M08R1")
@Api(tags = "D04M08R1:领料报表")
public class D04M08R1Controller extends MatRequisitionController {
    /**
     * 服务对象
     */
    @Resource
    private MatRequisitionService matRequisitionService;

    /**
     * 服务对象Item
     */
    @Resource
    private MatRequisitionitemService matRequisitionitemService;

    @Resource
    private SaRedisService saRedisService;

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_RequisitionItem.FinishQty<Mat_RequisitionItem.Quantity and Mat_RequisitionItem.Closed=0 and  Mat_RequisitionItem.DisannulMark=0";
            if (groupid != null) {
                qpfilter += " and Mat_Requisition.Groupid='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matRequisitionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionPojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Mat_Requisition.FinishCount<Mat_Requisition.ItemCount";
            if (groupid != null) {
                qpfilter += " and Mat_Requisition.Groupid='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matRequisitionService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "通过工单Itemid获通领料明细", notes = "通过工单Itemid获通领料明细", produces = "application/json")
    @RequestMapping(value = "/pullMatListByWork", method = RequestMethod.GET)
    public R<List<MatRequisitionitemPojo>> pullMatListByWork(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            if (loginUser == null) {
                R.fail("用户未登录");
            }
            return R.ok(this.matRequisitionitemService.getListByWorkitemid(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 获取销售单客户金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "按领料单据汇总领料数量和成本", notes = "按领料单据汇总领料数量和成本", produces = "application/json")
    @RequestMapping(value = "/getSumPageListByGroup", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionitemdetailPojo>> getSumPageListByGroup(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.BillDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.matRequisitionService.getSumPageListByGroup(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 获取销售单客户金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "按货品信息汇总领料数量和成本", notes = "按货品信息汇总领料数量和成本", produces = "application/json")
    @RequestMapping(value = "/getSumPageListByGoods", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionitemdetailPojo>> getSumPageListByGoods(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.BillDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.matRequisitionService.getSumPageListByGoods(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 获取销售单客户金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "按项目查领用明细", notes = "按项目查领用明细", produces = "application/json")
    @RequestMapping(value = "/getPageListByProject", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionitemdetailPojo>> getPageListByProject(@RequestBody String json, String projectid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.BillDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            if (projectid != null) {
                qpfilter += " and Mat_Requisition.Projectid='" + projectid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.matRequisitionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询订单领料明细", notes = "按条件分页查询订单领料明细", produces = "application/json")
    @RequestMapping(value = "/getMachPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionitemdetailPojo>> getMachPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_RequisitionItem.MachItemid!=''";
            if (groupid != null) {
                qpfilter += " and Mat_Requisition.Groupid='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matRequisitionService.getMachPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

