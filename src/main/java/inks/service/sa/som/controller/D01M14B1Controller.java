package inks.service.sa.som.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.constant.MyConstant;
import inks.service.sa.som.domain.pojo.BusAccountPojo;
import inks.service.sa.som.domain.pojo.BusAccountrecPojo;
import inks.service.sa.som.service.BusAccountService;
import inks.service.sa.som.service.BusAccountrecService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * 结转记录(Bus_AccountRec)表控制层
 *
 * <AUTHOR>
 * @since 2022-07-25 08:28:53
 */
@RestController
@RequestMapping("D01M14B1")
@Api(tags = "D01M14B1:结账记录")
public class D01M14B1Controller extends BusAccountrecController {

    @Resource
    private BusAccountrecService busAccountrecService;

    @Resource
    private SaRedisService saRedisService;

    @Resource
    private BusAccountService busAccountService;


    @Resource
    private SaBillcodeService saBillcodeService;

    /**
     * 新增数据
     *
     * @return 新增结果
     */
    @ApiOperation(value = " 新增结转记录", notes = "新增结转记录", produces = "application/json")
    @RequestMapping(value = "/open", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_AccountRec.Add")
    public R<BusAccountrecPojo> open(Integer year, Integer month) {
        try {
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));


            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (busAccountrecPojo != null) {
                return R.fail("已有开账记录,禁止重复开账");
            }
            busAccountrecPojo = new BusAccountrecPojo();
            busAccountrecPojo.setCarryyear(year);
            busAccountrecPojo.setCarrymonth(month);
            busAccountrecPojo.setRownum(Integer.parseInt(strRowNum));
            busAccountrecPojo.setStartdate(DateUtils.parseDate(year + "-" + month + "-1 00:00:00"));
            busAccountrecPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(busAccountrecPojo.getStartdate(), 1), -1));
            busAccountrecPojo.setCreateby(loginUser.getRealName());   // 创建者
            busAccountrecPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busAccountrecPojo.setCreatedate(new Date());   // 创建时间
            busAccountrecPojo.setLister(loginUser.getRealname());   // 制表
            busAccountrecPojo.setListerid(loginUser.getUserid());    // 制表id
            busAccountrecPojo.setModifydate(new Date());   //修改时间
            busAccountrecPojo.setTenantid(loginUser.getTenantid());   //租户id
            busAccountrecPojo.setTenantname(loginUser.getTenantinfo().getTenantname());

            if (DateUtils.getTimestamp(busAccountrecPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            return R.ok(this.busAccountrecService.insert(busAccountrecPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = " 获取结转记录最新记录", notes = "获取结转记录最新记录", produces = "application/json")
    @RequestMapping(value = "/getEntityByMax", method = RequestMethod.GET)
    public R<BusAccountrecPojo> getEntityByMax() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busAccountrecService.getEntityByMax(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @return 新增结果
     */
    @ApiOperation(value = "初期化全部销售账单", notes = "初期化全部销售账单", produces = "application/json")
    @RequestMapping(value = "/batchCreate", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Account.Add")
    public R<Integer> batchCreate(Integer year, Integer month) {
        try {
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));
            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (busAccountrecPojo == null) {
                R.fail("请先开账");
            }
            BusAccountPojo busAccountPojo = new BusAccountPojo();
            busAccountPojo.setStartdate(DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1));
            busAccountPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(busAccountPojo.getStartdate(), 1), -1));

            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D01M12B1", loginUser.getTenantid(),"Bus_Account");
            busAccountPojo.setRefno(refNo);
            busAccountPojo.setBilltype("销售账单");
            busAccountPojo.setCarryyear(year);
            busAccountPojo.setCarrymonth(month);
            busAccountPojo.setRownum(Integer.parseInt(strRowNum));
            busAccountPojo.setCreateby(loginUser.getRealName());   // 创建者
            busAccountPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busAccountPojo.setCreatedate(new Date());   // 创建时间
            busAccountPojo.setLister(loginUser.getRealname());   // 制表
            busAccountPojo.setListerid(loginUser.getUserid());    // 制表id
            busAccountPojo.setModifydate(new Date());   //修改时间
            busAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            busAccountPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            if (DateUtils.getTimestamp(busAccountPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            return R.ok(this.busAccountService.batchCreate(busAccountPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除结转记录", notes = "删除结转记录", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_AccountRec.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (key.equals(busAccountrecPojo.getId())) {
                return R.ok(this.busAccountrecService.delete(key, loginUser.getTenantid()));
            } else {
                return R.fail("只可以对最后一期返结账");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    //---------------------------做异步任务-------------------------
    @ApiOperation(value = "Start初期化全部销售账单", notes = "初期化全部销售账单", produces = "application/json")
    @RequestMapping(value = "/batchCreateStart", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Account.Add")
    public R<String> batchCreateStart(Integer year, Integer month) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));
            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;
            BusAccountrecPojo busAccountrecPojo = this.busAccountrecService.getEntityByMax(loginUser.getTenantid());
            if (busAccountrecPojo == null) {
                R.fail("请先开账");
            }
            BusAccountPojo busAccountPojo = new BusAccountPojo();
            busAccountPojo.setStartdate(DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1));
            busAccountPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(busAccountPojo.getStartdate(), 1), -1));
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D01M12B1", loginUser.getTenantid(),"Bus_Account");
            busAccountPojo.setRefno(refNo);
            busAccountPojo.setBilltype("销售账单");
            busAccountPojo.setCarryyear(year);
            busAccountPojo.setCarrymonth(month);
            busAccountPojo.setRownum(Integer.parseInt(strRowNum));
            busAccountPojo.setCreateby(loginUser.getRealName());   // 创建者
            busAccountPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busAccountPojo.setCreatedate(new Date());   // 创建时间
            busAccountPojo.setLister(loginUser.getRealname());   // 制表
            busAccountPojo.setListerid(loginUser.getUserid());    // 制表id
            busAccountPojo.setModifydate(new Date());   //修改时间
            busAccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            busAccountPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            if (DateUtils.getTimestamp(busAccountPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            // -----开始异步  批量生产账单---
            // uuid作为Redis的hkey
            String redisKey = UUID.randomUUID().toString();
            this.busAccountService.batchCreateStart(busAccountPojo, redisKey);
            return R.ok(redisKey);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "State初期化全部销售账单", notes = "取客户应收款列表By销售单", produces = "application/json")
    @RequestMapping(value = "/batchCreateState", method = RequestMethod.GET)
    public R<Map<String, Object>> batchCreateState(@RequestParam String key) {
        Object cacheMapValue = this.saRedisService.getCacheMapValue(MyConstant.BUSBATCHCREATE_CODE, key);
        Map<String, Object> state = (Map<String, Object>) cacheMapValue;
        return R.ok(state);
    }


}
