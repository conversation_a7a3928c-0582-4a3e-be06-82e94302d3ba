package inks.service.sa.som.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.MatStoragePojo;
import inks.service.sa.som.service.MatStorageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 仓库管理(Mat_Storage)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-13 20:42:18
 */
@RestController
@RequestMapping("D04M21S1")
@Api(tags = "D04M21S1:仓库管理")
public class D04M21S1Controller extends MatStorageController {

    @Resource
    private MatStorageService matStorageService;


    @Resource
    private SaRedisService saRedisService;

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "查询MachMark=1的仓库", notes = "查询MachMark=1的仓库", produces = "application/json")
    @RequestMapping(value = "/getMachList", method = RequestMethod.GET)
    public R<List<MatStoragePojo>> getMachList() {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matStorageService.getMachList(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "导入数据", notes = "导入数据", produces = "application/json")
    @RequestMapping(value = "/importExecl", method = RequestMethod.POST)
    public R<List<MatStoragePojo>> importExecl(String groupid, MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //去掉标题和表头
            ImportParams importParams = POIUtil.createImportParams(0, 1);
            //将表格数据写入List
            List<MatStoragePojo> list = POIUtil.importExcel(file.getInputStream(), MatStoragePojo.class, importParams);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "模板导出", notes = "模板导出", produces = "application/json")
    @RequestMapping(value = "/exportModel", method = RequestMethod.GET)
    public void exportModel(HttpServletRequest request, HttpServletResponse response) {
        List<MatStoragePojo> list = new ArrayList<>();
        //创建表格
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("仓库信息", ""),
                MatStoragePojo.class, list);
        try {
            //下载模板
            POIUtil.downloadWorkbook(workbook, request, response, "仓库信息模板");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
