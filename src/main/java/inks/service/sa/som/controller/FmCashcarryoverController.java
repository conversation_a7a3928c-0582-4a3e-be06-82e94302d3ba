package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.FmCashcarryoverPojo;
import inks.service.sa.som.domain.pojo.FmCashcarryoveritemPojo;
import inks.service.sa.som.domain.pojo.FmCashcarryoveritemdetailPojo;
import inks.service.sa.som.service.FmCashcarryoverService;
import inks.service.sa.som.service.FmCashcarryoveritemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 出纳账单(FmCashcarryover)表控制层
 *
 * <AUTHOR>
 * @since 2022-09-26 19:41:23
 */

public class FmCashcarryoverController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(FmCashcarryoverController.class);

    @Resource
    private FmCashcarryoverService fmCashcarryoverService;

    @Resource
    private FmCashcarryoveritemService fmCashcarryoveritemService;
    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaBillcodeService saBillcodeService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取出纳账单详细信息", notes = "获取出纳账单详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Fm_CashCarryover.List")
    public R<FmCashcarryoverPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmCashcarryoverService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Fm_CashCarryover.List")
    public R<PageInfo<FmCashcarryoveritemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Fm_CashCarryover.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.fmCashcarryoverService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取出纳账单详细信息", notes = "获取出纳账单详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Fm_CashCarryover.List")
    public R<FmCashcarryoverPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmCashcarryoverService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Fm_CashCarryover.List")
    public R<PageInfo<FmCashcarryoverPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Fm_CashCarryover.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.fmCashcarryoverService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Fm_CashCarryover.List")
    public R<PageInfo<FmCashcarryoverPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Fm_CashCarryover.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.fmCashcarryoverService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增出纳账单", notes = "新增出纳账单", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Fm_CashCarryover.Add")
    public R<FmCashcarryoverPojo> create(@RequestBody String json) {
        try {
            FmCashcarryoverPojo fmCashcarryoverPojo = JSONArray.parseObject(json, FmCashcarryoverPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //sa生成单据编码
            String refNo = saBillcodeService.getSerialNo("D07M04B1", loginUser.getTenantid(),"Fm_CashCarryover");
            fmCashcarryoverPojo.setRefno(refNo);
            fmCashcarryoverPojo.setCreateby(loginUser.getRealName());   // 创建者
            fmCashcarryoverPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            fmCashcarryoverPojo.setCreatedate(new Date());   // 创建时间
            fmCashcarryoverPojo.setLister(loginUser.getRealname());   // 制表
            fmCashcarryoverPojo.setListerid(loginUser.getUserid());    // 制表id            
            fmCashcarryoverPojo.setModifydate(new Date());   //修改时间
            fmCashcarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.fmCashcarryoverService.insert(fmCashcarryoverPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改出纳账单", notes = "修改出纳账单", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Fm_CashCarryover.Edit")
    public R<FmCashcarryoverPojo> update(@RequestBody String json) {
        try {
            FmCashcarryoverPojo fmCashcarryoverPojo = JSONArray.parseObject(json, FmCashcarryoverPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            fmCashcarryoverPojo.setLister(loginUser.getRealname());   // 制表
            fmCashcarryoverPojo.setListerid(loginUser.getUserid());    // 制表id   
            fmCashcarryoverPojo.setModifydate(new Date());   //修改时间
            fmCashcarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.fmCashcarryoverService.update(fmCashcarryoverPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除出纳账单", notes = "删除出纳账单", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Fm_CashCarryover.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmCashcarryoverService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增出纳账单Item", notes = "新增出纳账单Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Fm_CashCarryover.Add")
    public R<FmCashcarryoveritemPojo> createItem(@RequestBody String json) {
        try {
            FmCashcarryoveritemPojo fmCashcarryoveritemPojo = JSONArray.parseObject(json, FmCashcarryoveritemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            fmCashcarryoveritemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.fmCashcarryoveritemService.insert(fmCashcarryoveritemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除出纳账单Item", notes = "删除出纳账单Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Fm_CashCarryover.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmCashcarryoveritemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Fm_CashCarryover.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        FmCashcarryoverPojo fmCashcarryoverPojo = this.fmCashcarryoverService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(fmCashcarryoverPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = fmCashcarryoverPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    FmCashcarryoveritemPojo fmCashcarryoveritemPojo = new FmCashcarryoveritemPojo();
                    fmCashcarryoverPojo.getItem().add(fmCashcarryoveritemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(fmCashcarryoverPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

