package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.service.MatAccessService;
import inks.service.sa.som.service.MatInventoryService;
import inks.service.sa.som.service.MatRequisitionService;
import inks.service.sa.som.service.MatRequisitionitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 物料申领(MatRequisition)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-13 13:21:22
 */
@RestController
@RequestMapping("D04M08B1")
@Api(tags = "D04M08B1:领料单据")
public class D04M08B1Controller extends MatRequisitionController {

    @Resource
    private MatRequisitionService matRequisitionService;
    @Resource
    private MatInventoryService matInventoryService;

    @Resource
    private MatRequisitionitemService matRequisitionitemService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private MatAccessService matAccessService;

    @Resource
    private SaBillcodeService saBillcodeService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpFilter = "";
            // 加入场景   Eric 20221124
            qpFilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpFilter);
            return R.ok(this.matRequisitionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matRequisitionService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matRequisitionService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionitemdetailPojo>> getOnlinePageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpFilter = "";// " and Mat_Requisition.BillType IN ('生产领料')";
            qpFilter += " and Mat_RequisitionItem.FinishQty<Mat_RequisitionItem.Quantity ";
            qpFilter += " and Mat_RequisitionItem.Closed=0 and  Mat_RequisitionItem.DisannulMark=0";
            // 加入场景   Eric 20221124
            qpFilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpFilter);
            return R.ok(this.matRequisitionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionPojo>> getOnlineBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpFilter = "";// " and Mat_Requisition.BillType IN ('生产领料')";

            qpFilter += " and Mat_Requisition.FinishCount<Mat_Requisition.ItemCount";
            // 加入场景   Eric 20221124
            qpFilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpFilter);
            return R.ok(this.matRequisitionService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionPojo>> getOnlinePageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpFilter = "";// "" and Mat_Requisition.BillType IN ('生产领料')";
            qpFilter += " and Mat_Requisition.FinishCount<Mat_Requisition.ItemCount";
            // 加入场景   Eric 20221124
            qpFilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpFilter);
            return R.ok(this.matRequisitionService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOutOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionPojo>> getOutOnlinePageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpFilter = " and Mat_Requisition.BillType IN ('生产领料','领料单')";
            qpFilter += " and Mat_Requisition.FinishCount<Mat_Requisition.ItemCount";
            // 加入场景   Eric 20221124
            qpFilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpFilter);
            return R.ok(this.matRequisitionService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getInOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.List")
    public R<PageInfo<MatRequisitionPojo>> getInOnlinePageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Requisition.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpFilter = " and Mat_Requisition.BillType IN ('生产退料','退料单')";
            qpFilter += " and Mat_Requisition.FinishCount<Mat_Requisition.ItemCount";
            // 加入场景   Eric 20221124
            qpFilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpFilter);
            return R.ok(this.matRequisitionService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增物料申领", notes = "新增物料申领", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.Add")
    public R<MatRequisitionPojo> create(@RequestBody String json) {
        try {
            MatRequisitionPojo matRequisitionPojo = JSONArray.parseObject(json, MatRequisitionPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D04M08B1", loginUser.getTenantid(),"Mat_Requisition");
            matRequisitionPojo.setRefno(refNo);
            matRequisitionPojo.setCreateby(loginUser.getRealname());   // 创建者
            matRequisitionPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matRequisitionPojo.setCreatedate(new Date());   // 创建时间
            matRequisitionPojo.setLister(loginUser.getRealname());   // 制表
            matRequisitionPojo.setListerid(loginUser.getUserid());    // 制表id            
            matRequisitionPojo.setModifydate(new Date());   //修改时间
            matRequisitionPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.matRequisitionService.insert(matRequisitionPojo, loginUser.getToken()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return R<MatRequisitionPojo>
     * @Description: 新增物料申领ByWIP中的WorkUid
     * @param[1] json  接收格式：{ "workuid": "BM-2023-06-0131_1", "storeout": 1, "qty": "100" }
     * @time 2023/6/1 9:20
     */
    @ApiOperation(value = " 新增物料申领ByWIP中的WorkUid", notes = "新增物料申领ByWIP中的WorkUid", produces = "application/json")
    @RequestMapping(value = "/createByWip", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Requisition.Add")
    public R<MatRequisitionPojo> createByWip(@RequestBody String json) {
        try {
            QuickWipqtyPojo quickWipqtyPojo = JSONArray.parseObject(json, QuickWipqtyPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String tenantid = loginUser.getTenantid();
            // 查询Wip
            Map<String, Object> mapwip = this.matRequisitionService.getWipByWorkUid(quickWipqtyPojo.getWorkuid(), tenantid);
            if (mapwip == null) return R.fail("未找到对应WIP：" + quickWipqtyPojo.getWorkuid());
            // 查询原始订单
            String matcode;
            int matused;
            Double matQty;
            String machitemid = "";
            String machuid = "";
            String workitemid = "";
            String workuid = "";
            // 以是否有wrokitemid为准，有先处理原else部，无处理 machitemid代码， 交换一下
            // 原因：客户有订单加生产加工单，在加工单中会出现machitemid；再转WIP时两个itemid都会保存；如果先检查machitemid，永远执行不到workitemid;
            if (!"".equals(mapwip.get("workitemid").toString())) {
                workitemid = mapwip.get("workitemid").toString();
                workuid = mapwip.get("workuid").toString();
                Map<String, Object> mapmach = this.matRequisitionService.getWsItem(workitemid, tenantid);
                if (mapmach == null) return R.fail("未找到对应加工单项目：" + workuid);
                matcode = mapmach.get("matcode").toString();
                matused = Integer.parseInt(mapmach.get("matused").toString());
                // 是否有手动输入领用数量,没有则取生产加工单中全部的数量
                matQty = quickWipqtyPojo.getQty() == null ? Double.parseDouble(mapmach.get("quantity").toString()) : quickWipqtyPojo.getQty();
            } else {
                machitemid = mapwip.get("machitemid").toString();
                machuid = mapwip.get("machuid").toString();
                Map<String, Object> mapmach = this.matRequisitionService.getMachItem(machitemid, tenantid);
                if (mapmach == null) return R.fail("未找到对应销售订单项目：" + machuid);
                matcode = mapmach.get("matcode").toString();
                matused = Integer.parseInt(mapmach.get("matused").toString());
                // 是否有手动输入领用数量,没有则取订单中全部的数量
                matQty = quickWipqtyPojo.getQty() == null ? Double.parseDouble(mapmach.get("quantity").toString()) : quickWipqtyPojo.getQty();
            }
            if ("".equals(matcode)) {
                return R.fail("未找可领用信息：" + matcode);
            }
            if (matused == 1) {
                return R.fail("物料已被领用：" + matcode);
            }
            // 查询Goods
            Map<String, Object> mapgoods = this.matRequisitionService.getGoodsEntityByGoodsUid(matcode, tenantid);
            if (mapgoods == null) return R.fail("未找到对应货品档案：" + matcode);
            String storeid = mapgoods.get("storeid").toString();
            if (storeid == null || storeid.isEmpty()) return R.fail("请先设置好货品默认仓库：" + matcode);
            //查询指定仓库指定货品
            MatInventoryPojo matInventoryPojo = matInventoryService.getEntityByStoreGoods(mapgoods.get("id").toString(), storeid, tenantid);
            if (matInventoryPojo == null || matInventoryPojo.getQuantity() < matQty)
                return R.fail("指定仓库的货品库存量不足：" + matcode);
            MatRequisitionPojo matRequisitionPojo = new MatRequisitionPojo();
            matRequisitionPojo.setGroupid(mapwip.get("workshopid").toString());
            matRequisitionPojo.setBilltitle("【" + quickWipqtyPojo.getWorkuid() + "】生成领料单");
            matRequisitionPojo.setBilltype("领料单");

            MatRequisitionitemPojo matRequisitionitemPojo = new MatRequisitionitemPojo();
            matRequisitionitemPojo.setGoodsid(mapgoods.get("id").toString());
            matRequisitionitemPojo.setQuantity(matQty);
            matRequisitionitemPojo.setMachitemid(machitemid);
            matRequisitionitemPojo.setMachuid(machuid);
            matRequisitionitemPojo.setWorkitemid(workitemid);
            matRequisitionitemPojo.setWorkuid(workuid);
            matRequisitionitemPojo.setCiteuid(quickWipqtyPojo.getWorkuid());
            matRequisitionitemPojo.setCiteitemid(mapwip.get("id").toString());

            List<MatRequisitionitemPojo> lstitem = new ArrayList<>();
            lstitem.add(matRequisitionitemPojo);
            matRequisitionPojo.setItem(lstitem);


            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D04M08B1", loginUser.getTenantid(),"Mat_Requisition");
            matRequisitionPojo.setRefno(refNo);
            matRequisitionPojo.setCreateby(loginUser.getRealname());   // 创建者
            matRequisitionPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matRequisitionPojo.setCreatedate(new Date());   // 创建时间
            matRequisitionPojo.setLister(loginUser.getRealname());   // 制表
            matRequisitionPojo.setListerid(loginUser.getUserid());    // 制表id
            matRequisitionPojo.setModifydate(new Date());   //修改时间
            matRequisitionPojo.setTenantid(tenantid);   //租户id
            matRequisitionPojo.setAssessor(loginUser.getRealname());   // 创建者
            matRequisitionPojo.setAssessorid(loginUser.getUserid());  // 创建者id
            matRequisitionPojo.setAssessdate(new Date());   // 创建时间
            MatRequisitionPojo rpojo = this.matRequisitionService.insert(matRequisitionPojo, loginUser.getToken());
            if (quickWipqtyPojo.getStoreout() != null && quickWipqtyPojo.getStoreout() == 1) {
                MatAccessPojo matAccessPojo = new MatAccessPojo();
                matAccessPojo.setStoreid(storeid);
                matAccessPojo.setStorecode(mapgoods.get("storecode").toString());
                matAccessPojo.setStorename(mapgoods.get("storename").toString());
                matAccessPojo.setBilltitle("【" + rpojo.getRefno() + "】领料单自动转入");
                matAccessPojo.setDirection("出库单");
                matAccessPojo.setBilltype("领料出库");
                matAccessPojo.setGroupid(mapwip.get("workshopid").toString());

                MatAccessitemPojo matAccessitemPojo = new MatAccessitemPojo();
                matAccessitemPojo.setGoodsid(matRequisitionitemPojo.getGoodsid());
                matAccessitemPojo.setQuantity(matRequisitionitemPojo.getQuantity());
                matAccessitemPojo.setCiteuid(rpojo.getRefno());
                matAccessitemPojo.setCiteitemid(rpojo.getItem().get(0).getId());
                matAccessitemPojo.setMachuid(matRequisitionitemPojo.getMachuid());
                matAccessitemPojo.setMachitemid(matRequisitionitemPojo.getMachitemid());

                List<MatAccessitemPojo> lstaccitem = new ArrayList<>();
                lstaccitem.add(matAccessitemPojo);
                matAccessPojo.setItem(lstaccitem);

                //生成单据编码
                String refNo2 = saBillcodeService.getSerialNo("D04M08B1", loginUser.getTenantid(),"Mat_Requisition");
                matAccessPojo.setRefno(refNo2);
                matAccessPojo.setCreateby(loginUser.getRealname());   // 创建者
                matAccessPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                matAccessPojo.setCreatedate(new Date());   // 创建时间
                matAccessPojo.setLister(loginUser.getRealname());   // 制表
                matAccessPojo.setListerid(loginUser.getUserid());    // 制表id
                matAccessPojo.setModifydate(new Date());   //修改时间
                matAccessPojo.setTenantid(tenantid);   //租户id
                this.matAccessService.insert(matAccessPojo);
            }
            rpojo = this.matRequisitionService.getBillEntity(rpojo.getId(), rpojo.getTenantid());
            return R.ok(rpojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

