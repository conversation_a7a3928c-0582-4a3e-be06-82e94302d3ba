package inks.service.sa.som.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.MatSkuPojo;
import inks.service.sa.som.service.MatSkuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 货品SKU(Mat_Sku)表控制层
 *
 * <AUTHOR>
 * @since 2022-05-12 13:25:57
 */
@RestController
@RequestMapping("D04M15B1")
@Api(tags = "D04M15B1:货品Sku")
public class D04M15B1Controller extends MatSkuController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(MatSkuController.class);

    @Resource
    private MatSkuService matSkuService;

    @Resource
    private SaRedisService saRedisService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取货品SKU详细信息", notes = "获取货品SKU详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByGoodsAttr", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Sku.List")
    public R<MatSkuPojo> getEntityByGoodsAttr(@RequestBody String json, String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matSkuService.getEntityByAtte(key, json, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
