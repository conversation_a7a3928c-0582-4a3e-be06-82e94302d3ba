package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.BusDeductionPojo;
import inks.service.sa.som.domain.pojo.BusDeductionitemPojo;
import inks.service.sa.som.domain.pojo.BusDeductionitemdetailPojo;
import inks.service.sa.som.service.BusDeductionService;
import inks.service.sa.som.service.BusDeductionitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;

/**
 * 销售扣款(BusDeduction)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:31:47
 */
@RestController
@RequestMapping("D01M09B1")
@Api(tags = "D01M09B1:销售扣款")
public class D01M09B1Controller extends BusDeductionController {

    @Resource
    private BusDeductionService busDeductionService;


    @Resource
    private BusDeductionitemService busDeductionitemService;

    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaBillcodeService saBillcodeService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deduction.List")
    public R<PageInfo<BusDeductionitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deduction.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += " and Bus_DeductionItem.InvoQty<Bus_DeductionItem.Quantity";
            qpfilter += " and Bus_DeductionItem.DisannulMark=0 and Bus_DeductionItem.InvoClosed=0 ";  // 未关闭、未注销
            if (groupid != null) {
                qpfilter += " and Bus_Deduction.Groupid='" + groupid + "'";
            }
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDeductionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deduction.List")
    public R<PageInfo<BusDeductionPojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deduction.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += " and Bus_Deduction.FinishCount<Bus_Deduction.ItemCount";
            if (groupid != null) {
                qpfilter += " and Bus_Deduction.Groupid='" + groupid + "'";
            }
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDeductionService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "批量云打印单据", notes = "json={KEY,KEY},ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printBatchWebBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deduction.Print")
    public R<String> printBatchWebBill(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<String> lstptJson = new ArrayList<>();
            String ptRefNoMain = "";

            for (String key : lstkeys) {
                //=========获取单据表头信息========
                BusDeductionPojo busDeductionPojo = this.busDeductionService.getEntity(key, loginUser.getTenantid());
                if (busDeductionPojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }

                // 获取单据表头.表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(busDeductionPojo);

                // 获取单据表头.加入公司信息
                inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
                //=========获取单据Item信息========
                List<BusDeductionitemPojo> lstitem = this.busDeductionitemService.getList(key, loginUser.getTenantid());
                // 单据Item. 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = attrcostListToMaps(lstitem);

                // === 整理Map.row=====
                Map<String, Object> maprow = new LinkedHashMap<>();
                maprow.put("row", lst);
                // === 整理report=xml+grparam=====
                Map<String, Object> mapreport = new LinkedHashMap<>();
                mapreport.put("xml", maprow);
                mapreport.put("_grparam", map);
                // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                Map<String, Object> mapdata = new LinkedHashMap<>();
                mapdata.put("report", mapreport);
                // ====Map转Json ==== 注 时间转String 格式；
                String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
                lstptJson.add(ptJson);
                ptRefNoMain += busDeductionPojo.getRefno() + ",";

                // 刷入打印Num++
//                BusDeductionPojo billPrintPojo = new BusDeductionPojo();
//                billPrintPojo.setId(busDeductionPojo.getId());
//                billPrintPojo.setPrintcount(busDeductionPojo.getPrintcount() + 1);
//                billPrintPojo.setTenantid(busDeductionPojo.getTenantid());
//                this.busDeductionService.update(billPrintPojo);
            }
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else {
                mapPrint.put("code", "batchprint");
            }
            String[] lstRefno = ptRefNoMain.split(",");
            mapPrint.put("msg", "销售扣款：" + lstRefno[0] + "~" + lstRefno[lstRefno.length - 1]);    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setKeyValue("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            mapPrint.put("ptid", ptid);   // 报表id
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "批量云打印单据", notes = "json={KEY,KEY},ptid打印模版", produces = "application/json")
    @RequestMapping(value = "/printBatchBill", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deduction.Print")
    public void printBatchBill(@RequestBody String json, String ptid) throws IOException, JRException {
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            String content;
            if (reportsPojo != null) {
                content = reportsPojo.getRptdata();
            } else {
                throw new BaseBusinessException("未找到报表");
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            //数据填充
            JasperPrint printAll = new JasperPrint();
            for (int a = 0; a < lstkeys.size(); a++) {
                String key = lstkeys.get(a);
                //=========获取单据表头信息========
                BusDeductionPojo busDeductionPojo = this.busDeductionService.getBillEntity(key, loginUser.getTenantid());
                if (busDeductionPojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }

                //表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(busDeductionPojo);
                // 加入公司信息
                inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
                // 判定是否需要追行
                if (reportsPojo.getPagerow() > 0) {
                    int index = 0;
                    // 取行余数
                    index = busDeductionPojo.getItem().size() % reportsPojo.getPagerow();
                    if (index > 0) {
                        // 补全空白行
                        for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                            BusDeductionitemPojo busDeductionitemPojo = new BusDeductionitemPojo();
                            busDeductionPojo.getItem().add(busDeductionitemPojo);
                        }
                    }
                }

                // 带属性List转为Map  EricRen 20220427
                List<Map<String, Object>> lst = attrcostListToMaps(busDeductionPojo.getItem());
                //item转数据源
                JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);

                // 刷入打印Num++
//                BusMachiningPojo billPrintPojo = new BusMachiningPojo();
//                billPrintPojo.setId(busMachiningPojo.getId());
//                billPrintPojo.setPrintcount(busMachiningPojo.getPrintcount() + 1);
//                billPrintPojo.setTenantid(busMachiningPojo.getTenantid());
//                this.busMachiningService.update(billPrintPojo);

                //报表生成
                InputStream stream = new ByteArrayInputStream(content.getBytes());
                //编译报表
                JasperReport jasperReport = JasperCompileManager.compileReport(stream);

                //数据填充
                JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
                if (a == 0) {
                    printAll = print;
                } else {
                    List<JRPrintPage> pages = print.getPages();
                    for (JRPrintPage page : pages) {
                        printAll.addPage(page);
                    }
                }
            }
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(printAll, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}
