package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.BusReceiptPojo;
import inks.service.sa.som.service.BusReceiptService;
import inks.service.sa.som.service.BusReceiptitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 收款单据(BusReceipt)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:34:45
 */
@RestController
@RequestMapping("D01M08B1")
@Api(tags = "D01M08B1:收款单")
public class D01M08B1Controller extends BusReceiptController {


    @Resource
    private BusReceiptService busReceiptService;


    @Resource
    private BusReceiptitemService busReceiptitemService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineDocPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Receipt.List")
    public R<PageInfo<BusReceiptPojo>> getOnlineDocPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Receipt.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = " and Bus_Receipt.FmDocMark != 1";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.busReceiptService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
