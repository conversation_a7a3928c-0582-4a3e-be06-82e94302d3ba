package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaConfigService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.BuyOrderPojo;
import inks.service.sa.som.domain.pojo.BuyOrderitemdetailPojo;
import inks.service.sa.som.service.BuyOrderService;
import inks.service.sa.som.service.BuyOrderitemService;
import inks.service.sa.som.service.BuyPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 采购合同(BuyOrder)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 14:59:21
 */
@RestController
@RequestMapping("D03M02B1")
@Api(tags = "D03M02B1:采购合同:MRP需求")
public class D03M02B1Controller extends BuyOrderController {
    @Resource
    private SaConfigService saConfigService;
    @Resource
    private BuyPlanService buyPlanService;


    @Resource
    private BuyOrderService buyOrderService;


    @Resource
    private BuyOrderitemService buyOrderitemService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private SaBillcodeService saBillcodeService;


    @ApiOperation(value = "按条件分页查询Online", notes = "按条件分页查询Online", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<PageInfo<BuyOrderitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Order.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Buy_OrderItem.FinishQty<Buy_OrderItem.Quantity";
            qpfilter += " and Buy_OrderItem.DisannulMark=0 and Buy_OrderItem.Closed=0";
            if (groupid != null) {
                qpfilter += " and Buy_Order.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyOrderService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查完成的明细含未审核,即取反/getOnlinePageList", notes = "按条件分页查询Online", produces = "application/json")
    @RequestMapping(value = "/getFinishPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<PageInfo<BuyOrderitemdetailPojo>> getFinishPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Order.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and (Buy_OrderItem.FinishQty>=Buy_OrderItem.Quantity";// 完成条件3者其一 注意括号包裹
            qpfilter += " or Buy_OrderItem.DisannulMark=1 or Buy_OrderItem.Closed=1)";// 关闭、注销的item也算完成
            if (groupid != null) {
                qpfilter += " and Buy_Order.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyOrderService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询Online(只是在getOnlinePageList基础上加了个必须是已审核的条件)", notes = "按条件分页查询Online", produces = "application/json")
    @RequestMapping(value = "/getOnlineFinishPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<PageInfo<BuyOrderitemdetailPojo>> getOnlineFinishPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Order.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Buy_OrderItem.FinishQty<Buy_OrderItem.Quantity";
            qpfilter += " and Buy_OrderItem.DisannulMark=0 and Buy_OrderItem.Closed=0";
            qpfilter += " and Buy_Order.Assessorid<>''";
            if (groupid != null) {
                qpfilter += " and Buy_Order.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyOrderService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询Retrun", notes = "按条件分页查询Retrun", produces = "application/json")
    @RequestMapping(value = "/getRetrunFinishPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<PageInfo<BuyOrderitemdetailPojo>> getRetrunFinishPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Order.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Buy_OrderItem.FinishQty>0";
            qpfilter += " and Buy_OrderItem.DisannulMark=0 ";
            qpfilter += " and Buy_Order.Assessorid<>''";
            if (groupid != null) {
                qpfilter += " and Buy_Order.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyOrderService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<PageInfo<BuyOrderPojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Buy_Order.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Buy_Order.FinishCount+Buy_Order.DisannulCount<Buy_Order.ItemCount";
            if (groupid != null) {
                qpfilter += " and Buy_Order.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyOrderService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询(只是在getOnlinePageTh基础上加了个必须是已审核的条件)", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineFinishPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<PageInfo<BuyOrderPojo>> getOnlineFinishPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("Buy_Order.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Buy_Order.FinishCount+Buy_Order.DisannulCount<Buy_Order.ItemCount";
            qpfilter += " and Buy_Order.Assessorid<>''";
            if (groupid != null) {
                qpfilter += " and Buy_Order.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyOrderService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "单价来源module.buy.pricesource: goods货品 quot报价 lastgd货品最后一次 lastwg本供应商最后一次 返回select TaxPrice, Price, ItemTaxrate", produces = "application/json")
    @RequestMapping(value = "/getPriceByGoodsidAndSource", method = RequestMethod.POST)
    public R getPriceByGoodsidAndSource(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            List<Map<String, Object>> lst = JSON.parseObject(json, new TypeReference<List<Map<String, Object>>>() {
            });
            String pricesource = saConfigService.getConfigValue("module.buy.pricesource", "");
            if (StringUtils.isBlank(pricesource)) {
                throw new RuntimeException("module.buy.pricesource单价来源未配置");
            }
            for (Map<String, Object> map : lst) {
                Map<String, Object> priceFromGoodsidAndSource = buyPlanService.getPriceByGoodsidAndSource(
                        map.get("goodsid").toString(),
                        map.get("groupid").toString(),
                        pricesource,
                        loginUser.getTenantid()
                );
                // 追加到map中  select TaxPrice, Price, ItemTaxrate
                map.put("taxprice", priceFromGoodsidAndSource.get("TaxPrice"));
                map.put("price", priceFromGoodsidAndSource.get("Price"));
                map.put("itemtaxrate", priceFromGoodsidAndSource.get("ItemTaxrate"));
            }
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
