package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.OperLog;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.WkMrpPojo;
import inks.service.sa.som.domain.pojo.WkMrpitemPojo;
import inks.service.sa.som.domain.pojo.WkMrpitemdetailPojo;
import inks.service.sa.som.domain.pojo.WkMrpobjPojo;
import inks.service.sa.som.service.WkMrpService;
import inks.service.sa.som.service.WkMrpitemService;
import inks.service.sa.som.service.WkMrpobjService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * MRP运算(WkMrp)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-14 21:00:33
 */

public class WkMrpController {
    @Resource
    private SaBillcodeService saBillcodeService;
    /**
     * 服务对象
     */
    @Resource
    private WkMrpService wkMrpService;

    /**
     * 服务对象Item
     */
    @Resource
    private WkMrpitemService wkMrpitemService;
    /**
     * 服务对象Obj
     */
    @Resource
    private WkMrpobjService wkMrpobjService;

    @Resource
    private SaRedisService saRedisService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取MRP运算详细信息", notes = "获取MRP运算详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Mrp.List")
    public R<WkMrpPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkMrpService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询 attrcode:厂制/委制/外购/客供", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.List")
    public R<PageInfo<WkMrpitemdetailPojo>> getPageList(@RequestBody String json, String attrcode) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Mrp.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (StringUtils.isNotBlank(attrcode)) {
                qpfilter += " and Wk_MrpItem.AttrCode='" + attrcode + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkMrpService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取MRP运算详细信息", notes = "获取MRP运算详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Mrp.List")
    public R<WkMrpPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkMrpService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.List")
    public R<PageInfo<WkMrpPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Mrp.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkMrpService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.List")
    public R<PageInfo<WkMrpPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Mrp.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.wkMrpService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "getOnlinePageTh", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.List")
    public R<PageInfo<WkMrpPojo>> getOnlinePageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Wk_Mrp.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Wk_Mrp.ItemCount<=Wk_Mrp.FinishCount";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.wkMrpService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增MRP运算", notes = "新增MRP运算", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.Add")
    public R<WkMrpPojo> create(@RequestBody String json) {
        try {
            WkMrpPojo wkMrpPojo = JSONArray.parseObject(json, WkMrpPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D05M11B1", loginUser.getTenantid(),"Wk_Mrp");
            wkMrpPojo.setCreateby(loginUser.getRealname());   // 创建者
            wkMrpPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            wkMrpPojo.setCreatedate(new Date());   // 创建时间
            wkMrpPojo.setLister(loginUser.getRealname());   // 制表
            wkMrpPojo.setListerid(loginUser.getUserid());    // 制表id            
            wkMrpPojo.setModifydate(new Date());   //修改时间
            wkMrpPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.wkMrpService.insert(wkMrpPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改MRP运算", notes = "修改MRP运算", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.Edit")
    public R<WkMrpPojo> update(@RequestBody String json) {
        try {
            WkMrpPojo wkMrpPojo = JSONArray.parseObject(json, WkMrpPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            wkMrpPojo.setLister(loginUser.getRealname());   // 制表
            wkMrpPojo.setListerid(loginUser.getUserid());    // 制表id   
            wkMrpPojo.setModifydate(new Date());   //修改时间
            wkMrpPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.wkMrpService.update(wkMrpPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除MRP运算", notes = "删除MRP运算", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Mrp.Delete")
    @OperLog(title = "删除MRP运算")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String refno = this.wkMrpService.delete(key, loginUser.getTenantid());
            return R.ok(1, "id:" + key + "  refno:" + refno);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增MRP运算Item", notes = "新增MRP运算Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.Add")
    public R<WkMrpitemPojo> createItem(@RequestBody String json) {
        try {
            WkMrpitemPojo wkMrpitemPojo = JSONArray.parseObject(json, WkMrpitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            wkMrpitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkMrpitemService.insert(wkMrpitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 修改MrpItemd的AttrCode", notes = "json格式为{\n" +
            "itemids:[\"aa\",\"bb\"],\n" +
            "attrcode:\"委制\"\n" +
            "}", produces = "application/json")
    @RequestMapping(value = "/updateItemAttrCode", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.Add")
    public R<String> updateItemAttrCode(@RequestBody String json) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // 解析 JSON 字符串
            JSONObject jsonObject = JSON.parseObject(json);
            // 获取 itemids 数组
            List<String> itemIds = jsonObject.getJSONArray("itemids").toJavaList(String.class);
            if (CollectionUtils.isEmpty(itemIds)) {
                return R.fail("itemids 不能为空");
            }
            // 获取 attrcode 字符串
            String attrCode = jsonObject.getString("attrcode");

            return R.ok(this.wkMrpitemService.updateItemAttrCode(itemIds, attrCode, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 修改MRP运算Item", notes = "修改MRP运算Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.Add")
    public R<WkMrpitemPojo> updateItem(@RequestBody String json) {
        try {
            WkMrpitemPojo wkMrpitemPojo = JSONArray.parseObject(json, WkMrpitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            wkMrpitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.wkMrpitemService.update(wkMrpitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除MRP运算Item", notes = "删除MRP运算Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Mrp.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.wkMrpitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Mrp.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        WkMrpPojo wkMrpPojo = this.wkMrpService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(wkMrpPojo);

        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = wkMrpPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    WkMrpitemPojo wkMrpitemPojo = new WkMrpitemPojo();
                    wkMrpPojo.getItem().add(wkMrpitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(wkMrpPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Wk_Mrp.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //获取单据信息
            WkMrpPojo wkMrpPojo = this.wkMrpService.getEntity(key, loginUser.getTenantid());

            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(wkMrpPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
//            //=========获取单据Item信息========
//            List<WkMrpitemPojo> lstitem = this.wkMrpitemService.getList(key, loginUser.getTenantid());
//            // 单据Item. 带属性List转为Map  EricRen 20220427
//            List<Map<String, Object>> lst = attrListToMaps(lstitem);
//
//            // === 整理Map.row=====
//            Map<String, Object> maprow = new LinkedHashMap<>();
//            maprow.put("row", lst);
//            // === 整理report=xml+grparam=====
//            Map<String, Object> mapreport = new LinkedHashMap<>();
//            mapreport.put("xml", maprow);
//            mapreport.put("_grparam", map);
//            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
//            Map<String, Object> mapdata = new LinkedHashMap<>();
//            mapdata.put("report", mapreport);

            //=========获取单据Item信息========
            List<WkMrpitemPojo> lstitem = this.wkMrpitemService.getList(key, loginUser.getTenantid());
            //=========获取单据cash信息========
            List<WkMrpobjPojo> lstobj = this.wkMrpobjService.getList(key, loginUser.getTenantid());
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("Obj", attrListToMaps(lstobj));
            mapreport.put("Item", attrListToMaps(lstitem));
            mapreport.put("Master", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("xml", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "MRP运算单" + wkMrpPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            mapPrint.put("data", ptJson);   //  打印数据
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "云打印报表ByObjids,同一mrp单据下勾选obj", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBillByObjids", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Wk_Mrp.Print")
    public R<String> printWebBillByObjids(@RequestBody String json, String ptid, String sn, Integer cmd) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            //获取单据信息 objids
            Set<String> objids = JSON.parseObject(json, new TypeReference<Set<String>>() {
            });
            //=========1.获取单据Obj信息========
            List<WkMrpobjPojo> lstobj = wkMrpobjService.getListInids(objids, tid);
            // ========2.主表
            WkMrpPojo wkMrpPojo = this.wkMrpService.getEntity(lstobj.get(0).getPid(), tid);

            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(wkMrpPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            //=========3.获取单据Item信息========
            List<WkMrpitemPojo> lstitem = this.wkMrpitemService.getListInObjids(objids, tid);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("Obj", attrListToMaps(lstobj));
            mapreport.put("Item", attrListToMaps(lstitem));
            mapreport.put("Master", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("xml", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "MRP运算单" + wkMrpPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            mapPrint.put("data", ptJson);   //  打印数据
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

