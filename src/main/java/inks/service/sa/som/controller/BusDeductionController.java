package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.BusDeductionPojo;
import inks.service.sa.som.domain.pojo.BusDeductionitemPojo;
import inks.service.sa.som.domain.pojo.BusDeductionitemdetailPojo;
import inks.service.sa.som.service.BusDeductionService;
import inks.service.sa.som.service.BusDeductionitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;
import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;

/**
 * 销售扣款(BusDeduction)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:31:47
 */
public class BusDeductionController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(BusDeductionController.class);

    @Resource
    private BusDeductionService busDeductionService;

    @Resource
    private BusDeductionitemService busDeductionitemService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private SaBillcodeService saBillcodeService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取销售扣款详细信息", notes = "获取销售扣款详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deduction.List")
    public R<BusDeductionPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busDeductionService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deduction.List")
    public R<PageInfo<BusDeductionitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deduction.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.busDeductionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取销售扣款详细信息", notes = "获取销售扣款详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deduction.List")
    public R<BusDeductionPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busDeductionService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deduction.List")
    public R<PageInfo<BusDeductionPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deduction.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.busDeductionService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deduction.List")
    public R<PageInfo<BusDeductionPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deduction.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.busDeductionService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增销售扣款", notes = "新增销售扣款", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deduction.Add")
    public R<BusDeductionPojo> create(@RequestBody String json) {
        try {
            BusDeductionPojo busDeductionPojo = JSONArray.parseObject(json, BusDeductionPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D01M09B1", loginUser.getTenantid(), "Bus_Deduction");
            busDeductionPojo.setRefno(refNo);
            busDeductionPojo.setCreateby(loginUser.getRealname());   // 创建者
            busDeductionPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busDeductionPojo.setCreatedate(new Date());   // 创建时间
            busDeductionPojo.setLister(loginUser.getRealname());   // 制表
            busDeductionPojo.setListerid(loginUser.getUserid());    // 制表id            
            busDeductionPojo.setModifydate(new Date());   //修改时间
            busDeductionPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busDeductionService.insert(busDeductionPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改销售扣款", notes = "修改销售扣款", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deduction.Edit")
    public R<BusDeductionPojo> update(@RequestBody String json) {
        try {
            BusDeductionPojo busDeductionPojo = JSONArray.parseObject(json, BusDeductionPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            busDeductionPojo.setLister(loginUser.getRealname());   // 制表
            busDeductionPojo.setListerid(loginUser.getUserid());    // 制表id   
            busDeductionPojo.setModifydate(new Date());   //修改时间
            busDeductionPojo.setAssessor(""); //审核员
            busDeductionPojo.setAssessorid(""); //审核员
            busDeductionPojo.setAssessdate(new Date()); //审核时间
            busDeductionPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busDeductionService.update(busDeductionPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除销售扣款", notes = "删除销售扣款", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deduction.Delete")
    //@OperLog(title = "删除销售扣款")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busDeductionService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增销售扣款Item", notes = "新增销售扣款Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deduction.Add")
    public R<BusDeductionitemPojo> createItem(@RequestBody String json) {
        try {
            BusDeductionitemPojo busDeductionitemPojo = JSONArray.parseObject(json, BusDeductionitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            busDeductionitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.busDeductionitemService.insert(busDeductionitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除销售扣款Item", notes = "删除销售扣款Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deduction.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busDeductionitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核销售扣款", notes = "审核销售扣款", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deduction.Approval")
    public R<BusDeductionPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            BusDeductionPojo busDeductionPojo = this.busDeductionService.getEntity(key, loginUser.getTenantid());
            if (busDeductionPojo.getAssessor().equals("")) {
                busDeductionPojo.setAssessor(loginUser.getRealname()); //审核员
                busDeductionPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                busDeductionPojo.setAssessor(""); //审核员
                busDeductionPojo.setAssessorid(""); //审核员
            }
            busDeductionPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.busDeductionService.approval(busDeductionPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    @ApiOperation(value = "发出第三方审批", notes = "发出第三方审批wex/ding", produces = "application/json")
//    @RequestMapping(value = "/sendapprovel", method = RequestMethod.GET)
//    public R<ApprrecPojo> sendapprovel(String key, String apprid, String type) {
//        try {
//            if (type == null) type = "wxe";  // 默认走企业微信
//            String verifyKey = CacheConstants.APPR_CODES_KEY + apprid;
//            //获取token
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            //创建VM数据对象
//            VelocityContext context = new VelocityContext();
//            //从redis中获取模板对象
//            // Object obj = saRedisService.getValue(verifyKey);
//            ApprrecPojo apprrecPojo = saRedisService.getValue(verifyKey);
//            ApprovePojo approvePojo = new ApprovePojo();
//            //获得第三方账号
//            R rja = systemFeignService.getJustauthByUserid(loginUser.getUserid(), type, loginUser.getTenantid());
//            JustauthPojo justauthPojo = new JustauthPojo();
//            if (rja.getCode() == 200) {
//                org.springframework.beans.BeanUtils.copyProperties(rja.getData(), justauthPojo);
//            } else {
//                return R.fail("获得第三方账号出错" + rja.getMsg().toString());
//            }
//            approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
//            approvePojo.setUserid(justauthPojo.getAuthuuid());
//            approvePojo.setModelcode(apprrecPojo.getTemplateid());
//            approvePojo.setObject(this.busDeductionService.getBillEntity(key, loginUser.getTenantid()));
//            context.put("approvePojo", approvePojo);
//            String str = apprrecPojo.getDatatemp();
//            // 初始化并取得Velocity引擎
//            VelocityEngine ve = new VelocityEngine();
//            ve.init();
//            // 转换输出
//            StringWriter writer = new StringWriter();
//            ve.evaluate(context, writer, "", str); // 关键方法
//            //写回String
//            str = writer.toString();
//            //新建审批记录
//            apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
//            apprrecPojo.setDatatemp(str);
//            apprrecPojo.setApprname("订单审批");
//            apprrecPojo.setResultcode("");
//            apprrecPojo.setBillid(key);    // 单据ID
//            apprrecPojo.setUserid("");
//            apprrecPojo.setApprtype("");
//            apprrecPojo.setCreateby(loginUser.getRealname());
//            apprrecPojo.setCreatebyid(loginUser.getUserid());
//            apprrecPojo.setCreatedate(new Date());
//            apprrecPojo.setLister(loginUser.getRealname());
//            apprrecPojo.setListerid(loginUser.getUserid());
//            apprrecPojo.setModifydate(new Date());
//            apprrecPojo.setTenantid(loginUser.getTenantid());
//            //将企业微信审批信息存入redis
//            String CachKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
//            saRedisService.setKeyValue(CachKey, apprrecPojo, (long) (60 * 12), TimeUnit.MINUTES);
//            if ("wxe".equals(type)) {
//                R r = this.utilsFeignService.wxeapprovel(apprrecPojo.getId(), loginUser.getTenantid());
//                if (r.getCode() != 200) {
//                    return R.fail("发起审批失败" + r.toString());
//                }
//            } else {
//                R r = this.utilsFeignService.dingapprovel(apprrecPojo.getId(), loginUser.getTenantid());
//                if (r.getCode() != 200) {
//                    return R.fail("发起审批失败" + r.toString());
//                }
//            }
//            return R.ok(apprrecPojo);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }


//    @ApiOperation(value = "审批回调修改状态", notes = "审批回调修改状态", produces = "application/json")
//    @RequestMapping(value = "/justapprovel", method = RequestMethod.GET)
//    public R<BusDeductionPojo> justapprovel(String key, String type) {
//        try {
//            System.out.println("审核通过,写入审核信息");
//            //1.读取审批记录
//            String verifyKey = CacheConstants.APPR_CODES_KEY + key;
//            ApprrecPojo apprrecPojo = saRedisService.getValue(verifyKey);
//            //2. 获得单据数据
//            BusDeductionPojo busDeductionPojo = this.busDeductionService.getEntity(apprrecPojo.getBillid(), apprrecPojo.getTenantid());
//            //3. 写入审核批
//            //获得第三方账号
//            if (type == null) type = "wxe";
//            R rja = systemFeignService.getJustauthByUuid(apprrecPojo.getCallbackuuid(), type, apprrecPojo.getTenantid());
//            JustauthPojo justauthPojo = new JustauthPojo();
//            if (rja.getCode() == 200) {
//                org.springframework.beans.BeanUtils.copyProperties(rja.getData(), justauthPojo);
//            } else {
//                System.out.println("写入审核:获得第三方账号出错：" + rja.getMsg());
//                return R.fail("获得第三方账号出错" + rja.getMsg());
//            }
//            busDeductionPojo.setAssessorid(justauthPojo.getUserid());
//            busDeductionPojo.setAssessor(justauthPojo.getRealname()); //审核员
//            busDeductionPojo.setAssessdate(new Date()); //审核时间
//            return R.ok(this.busDeductionService.approval(busDeductionPojo));
//        } catch (Exception e) {
//            System.out.println("写入审核失败：" + e.getMessage());
//            return R.fail(e.getMessage());
//        }
//    }


    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deduction.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BusDeductionPojo busDeductionPojo = this.busDeductionService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busDeductionPojo);
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);

        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = busDeductionPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusDeductionitemPojo busDeductionitemPojo = new BusDeductionitemPojo();
                    busDeductionPojo.getItem().add(busDeductionitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(busDeductionPojo.getItem());
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);

        // 刷入打印Num++
//        BusDeductionPojo billPrintPojo = new BusDeductionPojo();
//        billPrintPojo.setId(busDeductionPojo.getId());
//        billPrintPojo.setPrintcount(busDeductionPojo.getPrintcount()+1);
//        billPrintPojo.setTenantid(busDeductionPojo.getTenantid());
//        this.busDeductionService.update(billPrintPojo);

        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deduction.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            BusDeductionPojo busDeductionPojo = this.busDeductionService.getEntity(key, loginUser.getTenantid());
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(busDeductionPojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<BusDeductionitemPojo> lstitem = this.busDeductionitemService.getList(key, loginUser.getTenantid());
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, String> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "销售扣款" + busDeductionPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setKeyValue("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("token", loginUser.getToken());
            // 刷入打印Num++
            BusDeductionPojo billPrintPojo = new BusDeductionPojo();
            billPrintPojo.setId(busDeductionPojo.getId());
            billPrintPojo.setPrintcount(busDeductionPojo.getPrintcount() + 1);
            billPrintPojo.setTenantid(busDeductionPojo.getTenantid());
            this.busDeductionService.updatePrintcount(billPrintPojo);
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印BusDeduction明细报表(分页PageList)", notes = "打印明细报表", produces = "application/json")
    @RequestMapping(value = "/printPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deduction.Print")
    public void printPageList(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Bus_Deduction.CreateDate");
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Bus_Deduction.Groupid='" + groupid + "'";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BusDeductionitemdetailPojo> lst = this.busDeductionService.getPageList(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusDeductionitemdetailPojo itemdetailPojo = new BusDeductionitemdetailPojo();
                    lst.add(itemdetailPojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "打印BusDeduction单据报表(分页PageTh)", notes = "打印PageTh报表", produces = "application/json")
    @RequestMapping(value = "/printPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deduction.Print")
    public void printPageTh(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Bus_Deduction.CreateDate");
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Bus_Deduction.Groupid='" + groupid + "'";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BusDeductionPojo> lst = this.busDeductionService.getPageTh(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusDeductionPojo pojo = new BusDeductionPojo();
                    lst.add(pojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "云打印BusDeduction明细报表(分页PageList)", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deduction.Print")
    public R<String> printWebPageList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deduction.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Deduction.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BusDeductionitemdetailPojo> lstitem = this.busDeductionService.getPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "BusDeduction明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setKeyValue("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "批量云打印报表(List<BusDeductionPojo>不带item)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),groupid不为null则只获取第一条客户信息", produces = "application/json")
    @RequestMapping(value = "/printWebPageTh", method = RequestMethod.POST)
    //    @PreAuthorize(hasPermi = "Bus_Deduction.Print")
    public R<String> printWebPageTh(@RequestBody String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deduction.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Deduction.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BusDeductionPojo> lstTh = this.busDeductionService.getPageTh(queryParam).getList();
            // 获取单据表头.表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (groupid != null && lstTh.size() > 0) {
                map.put("groupname", lstTh.get(0).getGroupname());
                map.put("abbreviate", lstTh.get(0).getAbbreviate());
                map.put("groupuid", lstTh.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstTh);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "wip批量打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setKeyValue("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "一页两联打印", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBillMulti", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deduction.Print")
    public R<String> printWebBillMulti(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            BusDeductionPojo busDeductionPojo = this.busDeductionService.getEntity(key, loginUser.getTenantid());
            // 检查是否审核后方可打印单据,改为 feign Eric 20230203
            //R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
            //if (r.getCode() == 200) {
            //    Map<String, String> tencfg = r.getData(); //
            //    String printapproved = tencfg.get("system.bill.printapproved");
            //    if (printapproved != null && printapproved.equals("true") && busDeductionPojo.getAssessor().equals("")) {
            //        throw new BaseBusinessException("请先审核单据");
            //    }
            //} else {
            //    throw new BaseBusinessException("获得打印权限出错" + r.getMsg());
            //}

            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(busDeductionPojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            QueryParam queryParam = new QueryParam();
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setFilterstr(" and Bus_DeductionItem.Pid='" + key + "'");
            queryParam.setOrderBy("Bus_DeductionItem.RowNum");
            queryParam.setPageNum(1);
            queryParam.setPageSize(1000);
            List<BusDeductionitemdetailPojo> lstitem = this.busDeductionService.getPageList(queryParam).getList();
            List<Map<String, Object>> lstMap = attrcostListToMaps(lstitem);
            // 一式两份
            List<Map<String, Object>> lst = lstMap.stream()
                    .map(item -> {
                        Map<String, Object> hashMap = new HashMap<>(item);
                        hashMap.put("ToWho", 1);
                        hashMap.put("PageNo", 1);
                        return hashMap;
                    })
                    .collect(Collectors.toList());
            List<Map<String, Object>> lstCopy = lstMap.stream()
                    .map(item -> {
                        Map<String, Object> hashMap = new HashMap<>(item);
                        hashMap.put("ToWho", 2);
                        hashMap.put("PageNo", 1);
                        return hashMap;
                    })
                    .collect(Collectors.toList());
            lst.addAll(lstCopy);
            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "BusDeduction" + busDeductionPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限
            // 刷入打印Num++
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

