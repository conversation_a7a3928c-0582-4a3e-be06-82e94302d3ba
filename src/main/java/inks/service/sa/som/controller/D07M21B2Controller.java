package inks.service.sa.som.controller;

import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.service.FmCashaccountService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 出纳账户(Fm_CashAccount)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 16:58:08
 */
@RestController
@RequestMapping("D07M21B2")
@Api(tags = "D07M21B2:出纳账户")
public class D07M21B2Controller extends FmCashaccountController {


    @Resource
    private FmCashaccountService fmCashaccountService;


    @Resource
    private SaRedisService saRedisService;

//
//    /**
//     * 分页查询
//     *
//     * @param json 筛选条件
//     * @return 查询结果
//     */
//    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
//    @RequestMapping(value = "/getRelationList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Fm_CashAccount.List")
//    public R<List<FmCashaccountPojo>> getRelationList(@RequestBody String json, String cashid) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
//                queryParam.setOrderBy("Fm_CashAccount.CreateDate");
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.fmCashaccountService.getPageList(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

}
