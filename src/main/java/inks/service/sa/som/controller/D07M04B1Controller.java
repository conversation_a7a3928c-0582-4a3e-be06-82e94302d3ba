package inks.service.sa.som.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.FmCashcarryoverPojo;
import inks.service.sa.som.domain.pojo.FmCashcarryoverrecPojo;
import inks.service.sa.som.service.FmCashcarryoverService;
import inks.service.sa.som.service.FmCashcarryoverrecService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * 出纳结账(Fm_CashCarryoverRec)表控制层
 *
 * <AUTHOR>
 * @since 2022-09-26 19:41:47
 */
@RestController
@RequestMapping("D07M04B1")
@Api(tags = "D07M04B1:出纳结账")
public class D07M04B1Controller extends FmCashcarryoverrecController {


    @Resource
    private FmCashcarryoverrecService fmCashcarryoverrecService;

    @Resource
    private FmCashcarryoverService fmCashcarryoverService;


    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaBillcodeService saBillcodeService;

    /**
     * 新增数据
     *
     * @return 新增结果
     */
    @ApiOperation(value = "开账", notes = "开账", produces = "application/json")
    @RequestMapping(value = "/open", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_AccountRec.Add")
    public R<FmCashcarryoverrecPojo> open(Integer year, Integer month) {
        try {
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));


            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            FmCashcarryoverrecPojo fmCashcarryoverrecPojo = this.fmCashcarryoverrecService.getEntityByMax(loginUser.getTenantid());
            if (fmCashcarryoverrecPojo != null) {
                return R.fail("已有开账记录,禁止重复开账");
            }
            fmCashcarryoverrecPojo = new FmCashcarryoverrecPojo();
            fmCashcarryoverrecPojo.setCarryyear(year);
            fmCashcarryoverrecPojo.setCarrymonth(month);
            fmCashcarryoverrecPojo.setRownum(Integer.parseInt(strRowNum));
            fmCashcarryoverrecPojo.setStartdate(DateUtils.parseDate(year + "-" + month + "-1 00:00:00"));
            fmCashcarryoverrecPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(fmCashcarryoverrecPojo.getStartdate(), 1), -1));
            fmCashcarryoverrecPojo.setCreateby(loginUser.getRealName());   // 创建者
            fmCashcarryoverrecPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            fmCashcarryoverrecPojo.setCreatedate(new Date());   // 创建时间
            fmCashcarryoverrecPojo.setLister(loginUser.getRealname());   // 制表
            fmCashcarryoverrecPojo.setListerid(loginUser.getUserid());    // 制表id
            fmCashcarryoverrecPojo.setModifydate(new Date());   //修改时间
            fmCashcarryoverrecPojo.setTenantid(loginUser.getTenantid());   //租户id
            fmCashcarryoverrecPojo.setTenantname(loginUser.getTenantinfo().getTenantname());

            if (DateUtils.getTimestamp(fmCashcarryoverrecPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            return R.ok(this.fmCashcarryoverrecService.insert(fmCashcarryoverrecPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = " 获取结转记录最新记录", notes = "获取结转记录最新记录", produces = "application/json")
    @RequestMapping(value = "/getEntityByMax", method = RequestMethod.GET)
    public R<FmCashcarryoverrecPojo> getEntityByMax() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmCashcarryoverrecService.getEntityByMax(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    /**
//     * 新增数据
//     *
//     * @return 新增结果
//     */
//    @ApiOperation(value = "批量生成账单，被转异步", notes = "批量生成账单，被转异步", produces = "application/json")
//    @RequestMapping(value = "/batchCreate", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Bus_Account.Add")
//    public R<Integer> batchCreate(Integer year, Integer month) {
//        try {
//            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
//            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));
//            String strRowNum = year.toString();
//            if (month < 10) strRowNum = strRowNum + "0";
//            strRowNum = strRowNum + month;
//            // 获得用户数据
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            FmCashcarryoverrecPojo fmCashcarryoverrecPojo = this.fmCashcarryoverrecService.getEntityByMax(loginUser.getTenantid());
//            if (fmCashcarryoverrecPojo == null) {
//                R.fail("请先开账");
//            }
//            FmCashcarryoverPojo fmCashcarryoverPojo = new FmCashcarryoverPojo();
//            fmCashcarryoverPojo.setStartdate(DateUtils.addSeconds(fmCashcarryoverrecPojo.getEnddate(), 1));
//            fmCashcarryoverPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(fmCashcarryoverPojo.getStartdate(), 1), -1));
//            //生成单据编码
//            R r = systemFeignService.getBillCode("D01M12B1", loginUser.getToken());
//            if (r.getCode() == 200)
//                fmCashcarryoverPojo.setRefno(r.getData().toString());
//            else {
//                return R.fail("单据编码读取出错" + r.toString());
//            }
//            fmCashcarryoverPojo.setBilltype("销售账单");
//            fmCashcarryoverPojo.setCarryyear(year);
//            fmCashcarryoverPojo.setCarrymonth(month);
//            fmCashcarryoverPojo.setRownum(Integer.parseInt(strRowNum));
//            fmCashcarryoverPojo.setCreateby(loginUser.getRealName());   // 创建者
//            fmCashcarryoverPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
//            fmCashcarryoverPojo.setCreatedate(new Date());   // 创建时间
//            fmCashcarryoverPojo.setLister(loginUser.getRealname());   // 制表
//            fmCashcarryoverPojo.setListerid(loginUser.getUserid());    // 制表id
//            fmCashcarryoverPojo.setModifydate(new Date());   //修改时间
//            fmCashcarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
//            fmCashcarryoverPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
//            if (DateUtils.getTimestamp(fmCashcarryoverPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
//                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
//            }
//            return R.ok(this.fmCashcarryoverService.batchCreate(fmCashcarryoverPojo));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

    //---------------------------做异步任务-------------------------
    @ApiOperation(value = "Start初期化全部销售账单", notes = "初期化全部销售账单", produces = "application/json")
    @RequestMapping(value = "/batchCreateStart", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Account.Add")
    public R<String> batchCreateStart(Integer year, Integer month) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            if (year == null) year = Integer.parseInt(DateUtils.parseDateToStr("yyyy", new Date()));
            if (month == null) month = Integer.parseInt(DateUtils.parseDateToStr("MM", new Date()));
            String strRowNum = year.toString();
            if (month < 10) strRowNum = strRowNum + "0";
            strRowNum = strRowNum + month;
            FmCashcarryoverrecPojo fmCashcarryoverrecPojo = this.fmCashcarryoverrecService.getEntityByMax(loginUser.getTenantid());
            if (fmCashcarryoverrecPojo == null) {
                R.fail("请先开账");
            }
            FmCashcarryoverPojo fmCashcarryoverPojo = new FmCashcarryoverPojo();
            fmCashcarryoverPojo.setStartdate(DateUtils.addSeconds(fmCashcarryoverrecPojo.getEnddate(), 1));
            fmCashcarryoverPojo.setEnddate(DateUtils.addSeconds(DateUtils.addMonths(fmCashcarryoverPojo.getStartdate(), 1), -1));
            //sa生成单据编码
            String refNo = saBillcodeService.getSerialNo("D07M03B1", loginUser.getTenantid(),"Fm_CashCarryoverRec");
            fmCashcarryoverPojo.setRefno(refNo);
            fmCashcarryoverPojo.setBilltype("出纳账单");
            fmCashcarryoverPojo.setCarryyear(year);
            fmCashcarryoverPojo.setCarrymonth(month);
            fmCashcarryoverPojo.setRownum(Integer.parseInt(strRowNum));
            fmCashcarryoverPojo.setCreateby(loginUser.getRealName());   // 创建者
            fmCashcarryoverPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            fmCashcarryoverPojo.setCreatedate(new Date());   // 创建时间
            fmCashcarryoverPojo.setLister(loginUser.getRealname());   // 制表
            fmCashcarryoverPojo.setListerid(loginUser.getUserid());    // 制表id
            fmCashcarryoverPojo.setModifydate(new Date());   //修改时间
            fmCashcarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            fmCashcarryoverPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
            if (DateUtils.getTimestamp(fmCashcarryoverPojo.getEnddate()) > DateUtils.getTimestamp(new Date())) {
                return R.fail(year + "年" + month + "月,尚末结现束,请往前调一个月重试");
            }
            // -----开始异步  批量生产账单---
            // uuid作为Redis的hkey
            String uuid = UUID.randomUUID().toString();
            this.fmCashcarryoverService.batchCreateStart(fmCashcarryoverPojo, uuid);
            return R.ok(uuid);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "State初期化全部销售账单", notes = "取客户应收款列表By销售单", produces = "application/json")
    @RequestMapping(value = "/batchCreateState", method = RequestMethod.GET)
    public R<Map<String, Object>> batchCreateState(@RequestParam String key) {
        Map<String, Object> state = this.fmCashcarryoverService.batchCreateState(key);
        return R.ok(state);
    }


//    /**
//     * 新增数据
//     *
//     * @param json 实体
//     * @return 新增结果
//     */
//    @ApiOperation(value = "初期化全部应收账单", notes = "初期化全部应收账单", produces = "application/json")
//    @RequestMapping(value = "/batchInit", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Fm_Cashcarryover.Add")
//    public R<Integer> batchInit(@RequestBody String json) {
//        try {
//            FmCashcarryoverPojo fmCashcarryoverPojo = JSONArray.parseObject(json, FmCashcarryoverPojo.class);
//            // 获得用户数据
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            //生成单据编码
//            R r = systemFeignService.getBillCode("D01M12B1", loginUser.getToken());
//            if (r.getCode() == 200)
//                fmCashcarryoverPojo.setRefno(r.getData().toString());
//            else {
//                return R.fail("单据编码读取出错" + r.toString());
//            }
//            fmCashcarryoverPojo.setBilltype("期初建账");
//            fmCashcarryoverPojo.setCreateby(loginUser.getRealName());   // 创建者
//            fmCashcarryoverPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
//            fmCashcarryoverPojo.setCreatedate(new Date());   // 创建时间
//            fmCashcarryoverPojo.setLister(loginUser.getRealname());   // 制表
//            fmCashcarryoverPojo.setListerid(loginUser.getUserid());    // 制表id
//            fmCashcarryoverPojo.setModifydate(new Date());   //修改时间
//            fmCashcarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
//            return R.ok(this.fmCashcarryoverService.batchInit(fmCashcarryoverPojo));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    /**
//     * 新增数据
//     *
//     * @param json 实体
//     * @return 新增结果
//     */
//    @ApiOperation(value = "新建全部应收账单", notes = "新建全部应收账单", produces = "application/json")
//    @RequestMapping(value = "/batchCreate", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Fm_Cashcarryover.Add")
//    public R<Integer> batchCreate(@RequestBody String json) {
//        try {
//            FmCashcarryoverPojo fmCashcarryoverPojo = JSONArray.parseObject(json, FmCashcarryoverPojo.class);
//            // 获得用户数据
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            //生成单据编码
//            R r = systemFeignService.getBillCode("D01M12B1", loginUser.getToken());
//            if (r.getCode() == 200)
//                fmCashcarryoverPojo.setRefno(r.getData().toString());
//            else {
//                return R.fail("单据编码读取出错" + r.toString());
//            }
//            fmCashcarryoverPojo.setBilltype("应收账单");
//            fmCashcarryoverPojo.setCreateby(loginUser.getRealName());   // 创建者
//            fmCashcarryoverPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
//            fmCashcarryoverPojo.setCreatedate(new Date());   // 创建时间
//            fmCashcarryoverPojo.setLister(loginUser.getRealname());   // 制表
//            fmCashcarryoverPojo.setListerid(loginUser.getUserid());    // 制表id
//            fmCashcarryoverPojo.setModifydate(new Date());   //修改时间
//            fmCashcarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
//            return R.ok(this.fmCashcarryoverService.batchCreate(fmCashcarryoverPojo));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
}
