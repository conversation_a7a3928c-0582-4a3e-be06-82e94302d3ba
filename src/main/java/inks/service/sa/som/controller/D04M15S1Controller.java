package inks.service.sa.som.controller;


import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.MatSkumodelPojo;
import inks.service.sa.som.service.MatSkumodelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * SKU模版(Mat_SkuModel)表控制层
 *
 * <AUTHOR>
 * @since 2022-06-10 08:49:00
 */
@RestController
@RequestMapping("D04M15S1")
@Api(tags = "D04M15S1:SKU模版")
public class D04M15S1Controller extends MatSkumodelController {

    @Resource
    private MatSkumodelService matSkumodelService;


    @Resource
    private SaRedisService saRedisService;


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取SKU模版详细信息 QuickCode", notes = "获取SKU模版详细信息 QuickCode", produces = "application/json")
    @RequestMapping(value = "/getEntityByCode", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_SkuModel.List")
    public R<MatSkumodelPojo> getEntityByCode(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matSkumodelService.getEntityByCode(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
