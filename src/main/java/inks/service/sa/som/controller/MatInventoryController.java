package inks.service.sa.som.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.MatInventoryPojo;
import inks.service.sa.som.service.MatInventoryService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 库存信息(Mat_Inventory)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-12 14:23:41
 */

public class MatInventoryController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(MatInventoryController.class);

    @Resource
    private MatInventoryService matInventoryService;


    @Resource
    private SaRedisService saRedisService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取库存信息详细信息", notes = "获取库存信息详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<MatInventoryPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matInventoryService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<PageInfo<MatInventoryPojo>> getPageList(@RequestBody String json, String store) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Inventory.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (store != null) {
                qpfilter += " and Mat_Inventory.Storeid='" + store + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInventoryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增库存信息", notes = "新增库存信息", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Inventory.Add")
    public R<MatInventoryPojo> create(@RequestBody String json) {
        try {
            MatInventoryPojo matInventoryPojo = JSONArray.parseObject(json, MatInventoryPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            matInventoryPojo.setCreateby(loginUser.getRealname());   // 创建者
            matInventoryPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matInventoryPojo.setCreatedate(new Date());   // 创建时间
            matInventoryPojo.setLister(loginUser.getRealname());   // 制表
            matInventoryPojo.setListerid(loginUser.getUserid());    // 制表id  
            matInventoryPojo.setModifydate(new Date());   //修改时间
            matInventoryPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.matInventoryService.insert(matInventoryPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改库存信息", notes = "修改库存信息", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Inventory.Edit")
    public R<MatInventoryPojo> update(@RequestBody String json) {
        try {
            MatInventoryPojo matInventoryPojo = JSONArray.parseObject(json, MatInventoryPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            matInventoryPojo.setLister(loginUser.getRealname());   // 制表
            matInventoryPojo.setListerid(loginUser.getUserid());    // 制表id  
            matInventoryPojo.setTenantid(loginUser.getTenantid());   //租户id
            matInventoryPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.matInventoryService.update(matInventoryPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除库存信息", notes = "删除库存信息", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Inventory.Delete")
    //@OperLog(title = "删除库存信息")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matInventoryService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Inventory.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        MatInventoryPojo matInventoryPojo = this.matInventoryService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(matInventoryPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取XML内容
        String xml = saRedisService.getValue("report_codes:" + ptid);
        String content = "";
        if (xml != null || !"".equals(xml) || xml != "") {
            content = xml;
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "导入数据", notes = "导入数据", produces = "application/json")
    @RequestMapping(value = "/importExecl", method = RequestMethod.POST)
    public R<List<MatInventoryPojo>> importExecl(String groupid, MultipartFile file, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //去掉标题和表头
            ImportParams importParams = POIUtil.createImportParams(0, 1);
            //将表格数据写入List
            List<MatInventoryPojo> list = POIUtil.importExcel(file.getInputStream(), MatInventoryPojo.class, importParams);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "模板导出", notes = "模板导出", produces = "application/json")
    @RequestMapping(value = "/exportModel", method = RequestMethod.GET)
    public void exportModel(HttpServletRequest request, HttpServletResponse response) {
        List<MatInventoryPojo> list = new ArrayList<>();
        //创建表格
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("库存信息", ""),
                MatInventoryPojo.class, list);
        try {
            //下载模板
            POIUtil.downloadWorkbook(workbook, request, response, "库存信息模板");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "识别编码", notes = "识别编码", produces = "application/json")
    @RequestMapping(value = "/getEntityBynsp", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<MatInventoryPojo> getEntityBynsp(@RequestBody String json) {
        try {
            MatInventoryPojo matInventoryPojo = JSONArray.parseObject(json, MatInventoryPojo.class);
            if (matInventoryPojo.getGoodsuid() == null) matInventoryPojo.setGoodsuid("");
            if (matInventoryPojo.getGoodsname() == null) matInventoryPojo.setGoodsname("");
            if (matInventoryPojo.getGoodsspec() == null) matInventoryPojo.setGoodsspec("");
            if (matInventoryPojo.getPartid() == null) matInventoryPojo.setPartid("");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatInventoryPojo matInventory = matInventoryService.getEntityByNameSpecPart(matInventoryPojo.getGoodsname(), matInventoryPojo.getGoodsspec(), matInventoryPojo.getPartid(), loginUser.getTenantid());
            return R.ok(matInventory);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

