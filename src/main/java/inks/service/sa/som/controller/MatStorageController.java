package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.MatStoragePojo;
import inks.service.sa.som.service.MatStorageService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 仓库管理(Mat_Storage)表控制层
 *
 * <AUTHOR>
 * @since 2022-11-30 13:33:57
 */

public class MatStorageController {

    @Resource
    private MatStorageService matStorageService;


    @Resource
    private SaRedisService saRedisService;


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取仓库管理详细信息", notes = "获取仓库管理详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Storage.List")
    public R<MatStoragePojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matStorageService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Storage.List")
    public R<PageInfo<MatStoragePojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Storage.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.matStorageService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增仓库管理", notes = "新增仓库管理", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Storage.Add")
    public R<MatStoragePojo> create(@RequestBody String json) {
        try {
            MatStoragePojo matStoragePojo = JSONArray.parseObject(json, MatStoragePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // 检查是否存在相同的仓库编码或仓库名称
            if (this.matStorageService.checkCodeOrName(matStoragePojo.getStorecode(), matStoragePojo.getStorename(), loginUser.getTenantid())) {
                return R.fail("仓库编码或仓库名称已存在");
            }
            matStoragePojo.setCreateby(loginUser.getRealName());   // 创建者
            matStoragePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matStoragePojo.setCreatedate(new Date());   // 创建时间
            matStoragePojo.setLister(loginUser.getRealname());   // 制表
            matStoragePojo.setListerid(loginUser.getUserid());    // 制表id  
            matStoragePojo.setModifydate(new Date());   //修改时间
            matStoragePojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.matStorageService.insert(matStoragePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改仓库管理", notes = "修改仓库管理", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Storage.Edit")
    public R<MatStoragePojo> update(@RequestBody String json) {
        try {
            MatStoragePojo matStoragePojo = JSONArray.parseObject(json, MatStoragePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            matStoragePojo.setLister(loginUser.getRealname());   // 制表
            matStoragePojo.setListerid(loginUser.getUserid());    // 制表id  
            matStoragePojo.setTenantid(loginUser.getTenantid());   //租户id
            matStoragePojo.setModifydate(new Date());   //修改时间
            return R.ok(this.matStorageService.update(matStoragePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除仓库管理", notes = "删除仓库管理", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Storage.Delete")
    //@OperLog(title = "删除仓库消息")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //检查引用
            List<String> lstcite = this.matStorageService.getItemCiteBillName(key, loginUser.getTenantid());
            if (lstcite.size() > 0) {
                return R.fail( "禁止删除,被以下单据引用:" + lstcite);
            }
            return R.ok(this.matStorageService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

