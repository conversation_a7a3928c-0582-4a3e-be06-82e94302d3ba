package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.BuyDeductionPojo;
import inks.service.sa.som.domain.pojo.BuyDeductionitemdetailPojo;
import inks.service.sa.som.service.BuyDeductionService;
import inks.service.sa.som.service.BuyDeductionitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 采购扣款(BuyDeduction)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:00:59
 */
@RestController
@RequestMapping("D03M04B1")
@Api(tags = "D03M04B1:采购扣款")
public class D03M04B1Controller extends BuyDeductionController {

    @Resource
    private BuyDeductionService buyDeductionService;


    @Resource
    private BuyDeductionitemService buyDeductionitemService;

    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaBillcodeService saBillcodeService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getInvoRemPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Deduction.List")
    public R<PageInfo<BuyDeductionitemdetailPojo>> getInvoRemPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Deduction.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Buy_DeductionItem.DisannulMark=0 and Buy_DeductionItem.Closed=0 and Buy_DeductionItem.InveQty<Buy_DeductionItem.Quantity";
            if (groupid != null) {
                qpfilter += " and Buy_Deduction.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyDeductionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Deduction.List")
    public R<PageInfo<BuyDeductionitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Deduction.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += " and Buy_DeductionItem.InvoQty<Buy_DeductionItem.Quantity";
            qpfilter += " and Buy_DeductionItem.DisannulMark=0 and Buy_DeductionItem.InvoClosed=0 ";  // 未关闭、未注销
            if (groupid != null) {
                qpfilter += " and Buy_Deduction.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyDeductionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Deduction.List")
    public R<PageInfo<BuyDeductionPojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Deduction.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += " and Buy_Deduction.FinishCount<Buy_Deduction.ItemCount";
            if (groupid != null) {
                qpfilter += " and Buy_Deduction.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyDeductionService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
