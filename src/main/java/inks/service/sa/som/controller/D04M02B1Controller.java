package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.MatAccessEntity;
import inks.service.sa.som.domain.MatTransferEntity;
import inks.service.sa.som.domain.pojo.MatAccessPojo;
import inks.service.sa.som.domain.pojo.MatTransferPojo;
import inks.service.sa.som.service.MatTransferService;
import inks.service.sa.som.service.MatTransferitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 调拨单据(MatTransfer)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-13 20:50:24
 */
@RestController
@RequestMapping("D04M02B1")
@Api(tags = "D04M02B1:调拨单据")
public class D04M02B1Controller extends MatTransferController {


    @Resource
    private MatTransferService matTransferService;


    @Resource
    private SaBillcodeService saBillcodeService;

    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 红冲调拨单据", notes = "新增调拨单据", produces = "application/json")
    @RequestMapping(value = "/createRed", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Transfer.Add")
    public R<MatTransferPojo> createRed(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatTransferPojo matTransferPojo = this.matTransferService.getBillEntity(key, loginUser.getTenantid());
            //新建原始单
            MatTransferEntity matTransferEntityOrg = new MatTransferEntity();
            matTransferEntityOrg.setId(matTransferPojo.getId());
            matTransferEntityOrg.setTenantid(matTransferPojo.getTenantid());

            // 原单RefNo
            String refnoOrg = matTransferPojo.getRefno();
            //生成单据编码  红冲单的编码
            String refNoRed = saBillcodeService.getSerialNo("D04M02B1", loginUser.getTenantid(),"Mat_Transfer");
            matTransferEntityOrg.setReturnuid(refNoRed);//原单设置红冲单号

            matTransferPojo.setRefno(refNoRed);
            matTransferPojo.setOrguid(refnoOrg);//红冲单设置原单号

            matTransferPojo.setCreateby(loginUser.getRealname());   // 创建者
            matTransferPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matTransferPojo.setCreatedate(new Date());   // 创建时间
            matTransferPojo.setLister(loginUser.getRealname());   // 制表
            matTransferPojo.setListerid(loginUser.getUserid());    // 制表id
            matTransferPojo.setModifydate(new Date());   //修改时间
            matTransferPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.matTransferService.insertRed(matTransferPojo, matTransferEntityOrg));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    @ApiOperation(value = "打印列表", notes = "打印列表", produces = "application/json")
//    @RequestMapping(value = "/printWsLabel", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Mat_Access.Print")
//    public R printWsLabel(String key, String sn) {
//        // 获得用户数据
//        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//
//        QueryParam queryParam = new QueryParam();
//        queryParam.setOrderBy("Mat_Transfer.CreateDate");
//        queryParam.setTenantid(loginUser.getTenantid());
//        queryParam.setFilterstr(" and Mat_TransferItem.and Mat_TransferItem.id='" + key + "'");
//        //获取列表信息
//        List<MatTransferitemdetailPojo> lst = this.matTransferService.getPageList(queryParam).getList();
//        //表头转MAP(空)
//        Map<String, Object> map = new IdentityHashMap<>();
//
//        map.put("code", "print");
//        map.put("msg", "打印");
//        map.put("data", JSONObject.toJSONString(lst));
//        String message = JSONObject.toJSONString(map);
//
//        jobFeignService.sending(sn, message);
//        System.out.println("发出审批:D04M01.wslabelprintersn:" + sn);
//        return R.ok("打印完成");
//    }
}
