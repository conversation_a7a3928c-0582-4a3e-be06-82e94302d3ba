package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.BusOrdercostPojo;
import inks.service.sa.som.domain.pojo.BusOrdercostitemPojo;
import inks.service.sa.som.domain.pojo.BusOrdercostitemdetailPojo;
import inks.service.sa.som.service.BusOrdercostService;
import inks.service.sa.som.service.BusOrdercostitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 订单成本(BusOrdercost)表控制层
 *
 * <AUTHOR>
 * @since 2022-02-04 19:33:37
 */
@RestController
@RequestMapping("D01M02B1")
@Api(tags = "D01M02B1:核价单")
public class D01M02B1Controller extends BusOrdercostController {

    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(BusOrdercostController.class);

    @Resource
    private BusOrdercostService busOrdercostService;

    @Resource
    private BusOrdercostitemService busOrdercostitemService;

    @Resource
    private SaRedisService saRedisService;


//    @Resource
//    private BusCostpartgroupService busCostpartgroupService;

    @Resource
    private SaBillcodeService saBillcodeService;

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_OrderCost.List")
    public R<PageInfo<BusOrdercostPojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_OrderCost.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += "and Bus_OrderCost.FinishCount<Bus_OrderCost.ItemCount";
            if (groupid != null) {
                qpfilter += " and Bus_OrderCost.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.busOrdercostService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_OrderCost.List")
    public R<PageInfo<BusOrdercostitemdetailPojo>> getOnlinePageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_OrderCost.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += "and Bus_OrderCostItem.MachMark<>'1'";
            qpfilter += " and Bus_OrderCostItem.DisannulMark=0 and Bus_OrderCostItem.Closed=0 ";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.busOrdercostService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 作废单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "作废核价订单", notes = "作废销售订单,?type=1作废，0为反作废", produces = "application/json")
    @RequestMapping(value = "/disannul", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_OrderCost.Edit")
    public R<BusOrdercostPojo> disannul(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<BusOrdercostitemPojo> lst = JSONArray.parseArray(json, BusOrdercostitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //  BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(lst.get(0).getPid(), loginUser.getTenantid());

            return R.ok(this.busOrdercostService.disannul(lst, type, loginUser));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 作废单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "中止核价订单", notes = "中止销售订单,?type=1中止，0为反作废", produces = "application/json")
    @RequestMapping(value = "/closed", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_OrderCost.Edit")
    public R<BusOrdercostPojo> closed(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<BusOrdercostitemPojo> lst = JSONArray.parseArray(json, BusOrdercostitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.busOrdercostService.closed(lst, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_OrderCost.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BusOrdercostPojo busOrdercostPojo = this.busOrdercostService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(busOrdercostPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = busOrdercostPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusOrdercostitemPojo busOrdercostitemPojo = new BusOrdercostitemPojo();
                    busOrdercostPojo.getItem().add(busOrdercostitemPojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(busOrdercostPojo.getItem());
//        this.busCostpartgroupService.splitGroupjson(lst,loginUser.getTenantid());
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
        // 刷入打印Num++
        BusOrdercostPojo billPrintPojo = new BusOrdercostPojo();
        billPrintPojo.setId(busOrdercostPojo.getId());
        billPrintPojo.setPrintcount(busOrdercostPojo.getPrintcount() + 1);
        billPrintPojo.setTenantid(busOrdercostPojo.getTenantid());
        this.busOrdercostService.update(billPrintPojo);

        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_OrderCost.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            BusOrdercostPojo busOrdercostPojo = this.busOrdercostService.getEntity(key, loginUser.getTenantid());
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(busOrdercostPojo);
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<BusOrdercostitemPojo> lstitem = this.busOrdercostitemService.getList(key, loginUser.getTenantid());
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);
//            this.busCostpartgroupService.splitGroupjson(lst,loginUser.getTenantid());
            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "核算单" + busOrdercostPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            mapPrint.put("data", ptJson);   //  打印数据
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限
            // 刷入打印Num++
            BusOrdercostPojo billPrintPojo = new BusOrdercostPojo();
            billPrintPojo.setId(busOrdercostPojo.getId());
            billPrintPojo.setPrintcount(busOrdercostPojo.getPrintcount() + 1);
            billPrintPojo.setTenantid(busOrdercostPojo.getTenantid());
            this.busOrdercostService.update(billPrintPojo);
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
