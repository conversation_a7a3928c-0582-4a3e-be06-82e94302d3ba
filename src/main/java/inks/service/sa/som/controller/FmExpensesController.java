package inks.service.sa.som.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmExpensesPojo;
import inks.service.sa.som.domain.pojo.FmExpensesitemPojo;
import inks.service.sa.som.domain.pojo.FmExpensesitemdetailPojo;
import inks.service.sa.som.service.FmExpensesService;
import inks.service.sa.som.service.FmExpensesitemService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaBillcodeService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 费用报销单(Fm_Expenses)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-26 15:43:16
 */
//@RestController
//@RequestMapping("fmExpenses")
public class FmExpensesController {

    @Resource
    private FmExpensesService fmExpensesService;
    @Resource
    private FmExpensesitemService fmExpensesitemService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaBillcodeService saBillcodeService;
    
    private final static Logger logger = LoggerFactory.getLogger(FmExpensesController.class);
    

    @ApiOperation(value=" 获取费用报销单详细信息", notes="获取费用报销单详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Fm_Expenses.List")
    public R<FmExpensesPojo> getEntity(String key) {
      try {
           // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmExpensesService.getEntity(key));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_Expenses.List")
    public R<PageInfo<FmExpensesitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Fm_Expenses.CreateDate");
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.fmExpensesService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value=" 获取费用报销单详细信息", notes="获取费用报销单详细信息", produces="application/json")
    @RequestMapping(value="/getBillEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Fm_Expenses.List")
    public R<FmExpensesPojo> getBillEntity(String key) {
      try {
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmExpensesService.getBillEntity(key));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getBillList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_Expenses.List")
    public R<PageInfo<FmExpensesPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
           if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Fm_Expenses.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.fmExpensesService.getBillList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageTh",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_Expenses.List")
    public R<PageInfo<FmExpensesPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Fm_Expenses.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.fmExpensesService.getPageTh(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value=" 新增费用报销单", notes="新增费用报销单", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_Expenses.Add")
    public R<FmExpensesPojo> create(@RequestBody String json) {
        try {
       FmExpensesPojo fmExpensesPojo = JSONArray.parseObject(json,FmExpensesPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("DxxMxxB1", loginUser.getTenantid(), "Fm_Expenses");
            fmExpensesPojo.setRefno(refNo);
            fmExpensesPojo.setCreateby(loginUser.getRealName());   // 创建者
            fmExpensesPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            fmExpensesPojo.setCreatedate(new Date());   // 创建时间
            fmExpensesPojo.setLister(loginUser.getRealname());   // 制表
            fmExpensesPojo.setListerid(loginUser.getUserid());    // 制表id            
            fmExpensesPojo.setModifydate(new Date());   //修改时间
        return R.ok(this.fmExpensesService.insert(fmExpensesPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="修改费用报销单", notes="修改费用报销单", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_Expenses.Edit")
    public R<FmExpensesPojo> update(@RequestBody String json) {
        try {
         FmExpensesPojo fmExpensesPojo = JSONArray.parseObject(json,FmExpensesPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
           fmExpensesPojo.setAssessor(""); //审核员
           fmExpensesPojo.setAssessorid(""); //审核员
           fmExpensesPojo.setAssessdate(new Date()); //审核时间
            fmExpensesPojo.setLister(loginUser.getRealname());   // 制表
            fmExpensesPojo.setListerid(loginUser.getUserid());    // 制表id   
            fmExpensesPojo.setModifydate(new Date());   //修改时间
        return R.ok(this.fmExpensesService.update(fmExpensesPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除费用报销单", notes="删除费用报销单", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Fm_Expenses.Delete")
    public R<Integer> delete(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmExpensesService.delete(key));
   }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value=" 新增费用报销单Item", notes="新增费用报销单Item", produces="application/json") 
    @RequestMapping(value="/createItem",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_Expenses.Add")
    public R<FmExpensesitemPojo> createItem(@RequestBody String json) {
       try {
     FmExpensesitemPojo fmExpensesitemPojo = JSONArray.parseObject(json,FmExpensesitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.fmExpensesitemService.insert(fmExpensesitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value=" 修改费用报销单Item", notes="修改费用报销单Item", produces="application/json") 
    @RequestMapping(value="/updateItem",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Fm_Expenses.Edit")
    public R<FmExpensesitemPojo> updateItem(@RequestBody String json) {
       try {
     FmExpensesitemPojo fmExpensesitemPojo = JSONArray.parseObject(json,FmExpensesitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.fmExpensesitemService.update(fmExpensesitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }    

    @ApiOperation(value="删除费用报销单Item", notes="删除费用报销单Item", produces="application/json")   
    @RequestMapping(value="/deleteItem",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Fm_Expenses.Delete")
    public R<Integer> deleteItem(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmExpensesitemService.delete(key));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    


    @ApiOperation(value = "审核费用报销单", notes = "审核费用报销单", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Fm_Expenses.Approval")
    public R<FmExpensesPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            FmExpensesPojo fmExpensesPojo = this.fmExpensesService.getEntity(key);
            if (fmExpensesPojo.getAssessor().equals(""))
            {
                fmExpensesPojo.setAssessor(loginUser.getRealname()); //审核员
                fmExpensesPojo.setAssessorid(loginUser.getUserid()); //审核员id
                }
            else
            {
                fmExpensesPojo.setAssessor(""); //审核员
                fmExpensesPojo.setAssessorid(""); //审核员
                }
            fmExpensesPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.fmExpensesService.approval(fmExpensesPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Fm_Expenses.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        FmExpensesPojo fmExpensesPojo = this.fmExpensesService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(fmExpensesPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
          content = reportsPojo.getRptdata();
        } else {
          throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
       if(reportsPojo.getPagerow()>0){
       int index=0;
      // 取行余数
      index =fmExpensesPojo.getItem().size()%reportsPojo.getPagerow();
      if(index>0){
        // 补全空白行
        for(int i=0;i<reportsPojo.getPagerow()-index;i++){
            FmExpensesitemPojo fmExpensesitemPojo = new FmExpensesitemPojo();
            fmExpensesPojo.getItem().add(fmExpensesitemPojo);
          }
      }
     }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(fmExpensesPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

