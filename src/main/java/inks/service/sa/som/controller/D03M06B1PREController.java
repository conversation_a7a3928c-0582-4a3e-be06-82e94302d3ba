package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.BuyPrepaymentsPojo;
import inks.service.sa.som.service.BuyPrepaymentsService;
import inks.service.sa.som.service.BuyPrepaymentsitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 预付款(BuyPrepayments)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:02:13
 */
@RestController
@RequestMapping("D03M06B1PRE")
@Api(tags = "D03M06B1PRE:采购预付")
public class D03M06B1PREController extends BuyPrepaymentsController {

    @Resource
    private BuyPrepaymentsService buyPrepaymentsService;


    @Resource
    private BuyPrepaymentsitemService buyPrepaymentsitemService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询 ?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Prepayments.List")
    public R<PageInfo<BuyPrepaymentsPojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Prepayments.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Buy_Prepayments.OutAmount<Buy_Prepayments.BillAmount";
            if (groupid != null) {
                qpfilter += " and Buy_Prepayments.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyPrepaymentsService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询 ?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineDocPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Prepayments.List")
    public R<PageInfo<BuyPrepaymentsPojo>> getOnlineDocPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Prepayments.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Buy_Prepayments.FmDocMark != 1";
            if (groupid != null) {
                qpfilter += " and Buy_Prepayments.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.buyPrepaymentsService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
