package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AmountUtils;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.domain.pojo.SaJustauthPojo;
import inks.sa.common.core.feign.SaUtsFeignClient;
import inks.sa.common.core.mapper.SaConfigMapper;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaJustauthService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.som.domain.pojo.BusMachiningPojo;
import inks.service.sa.som.domain.pojo.BuyOrderPojo;
import inks.service.sa.som.domain.pojo.BuyOrderitemPojo;
import inks.service.sa.som.domain.pojo.BuyOrderitemdetailPojo;
import inks.service.sa.som.mapper.BusMachiningMapper;
import inks.service.sa.som.mapper.BuyOrderMapper;
import inks.service.sa.som.service.BuyOrderService;
import inks.service.sa.som.service.BuyOrderitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 采购合同(BuyOrder)表控制层
 *
 * <AUTHOR>
 * @since 2022-05-06 20:33:27
 */

public class BuyOrderController {
    @Resource
    private SaConfigMapper saConfigMapper;
    @Resource
    private BusMachiningMapper busMachiningMapper;
    @Resource
    private SaJustauthService saJustauthService;
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(BuyOrderController.class);

    @Resource
    private BuyOrderService buyOrderService;

    @Resource
    private BuyOrderitemService buyOrderitemService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private SaBillcodeService saBillcodeService;
    @Resource
    private SaUtsFeignClient saUtsFeignClient;
    @Resource
    private BuyOrderMapper buyOrderMapper;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取采购合同详细信息", notes = "获取采购合同详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<BuyOrderPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyOrderService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<PageInfo<BuyOrderitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Order.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.buyOrderService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取采购合同详细信息", notes = "获取采购合同详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<BuyOrderPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyOrderService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<PageInfo<BuyOrderPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Order.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.buyOrderService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.List")
    public R<PageInfo<BuyOrderPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Order.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.buyOrderService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增采购合同", notes = "新增采购合同", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.Add")
    public R<BuyOrderPojo> create(@RequestBody String json, @RequestParam(defaultValue = "1") Integer warn) {
        BuyOrderPojo buyOrderPojo = JSONArray.parseObject(json, BuyOrderPojo.class);
        // item转json,校验各项金额相差不能大于1
        String lessThanOne = AmountUtils.lstLessThanOne(JSONObject.toJSONString(buyOrderPojo.getItem()));
        PrintColor.red("=========lessThanOne校验各项金额相差不能大于1:" + lessThanOne);
        if (lessThanOne != null) return R.fail(lessThanOne);
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //生成单据编码
        String refNo = saBillcodeService.getSerialNo("D03M02B1", loginUser.getTenantid(), "Buy_Order");
        buyOrderPojo.setRefno(refNo);
        buyOrderPojo.setCreateby(loginUser.getRealName());   // 创建者
        buyOrderPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        buyOrderPojo.setCreatedate(new Date());   // 创建时间
        buyOrderPojo.setLister(loginUser.getRealname());   // 制表
        buyOrderPojo.setListerid(loginUser.getUserid());    // 制表id
        buyOrderPojo.setModifydate(new Date());   //修改时间
        buyOrderPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.buyOrderService.insert(buyOrderPojo, warn));
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改采购合同", notes = "修改采购合同", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.Edit")
    public R<BuyOrderPojo> update(@RequestBody String json, @RequestParam(defaultValue = "1") Integer warn) {
        BuyOrderPojo buyOrderPojo = JSONArray.parseObject(json, BuyOrderPojo.class);
        // item转json,校验各项金额相差不能大于1
        String lessThanOne = AmountUtils.lstLessThanOne(JSONObject.toJSONString(buyOrderPojo.getItem()));
        PrintColor.red("=========lessThanOne校验各项金额相差不能大于1:" + lessThanOne);
        if (lessThanOne != null) return R.fail(lessThanOne);
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        buyOrderPojo.setLister(loginUser.getRealname());   // 制表
        buyOrderPojo.setListerid(loginUser.getUserid());    // 制表id
        buyOrderPojo.setModifydate(new Date());   //修改时间
        buyOrderPojo.setAssessor(""); //审核员
        buyOrderPojo.setAssessorid(""); //审核员
        buyOrderPojo.setAssessdate(new Date()); //审核时间
        buyOrderPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.buyOrderService.update(buyOrderPojo, warn));
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除采购合同", notes = "删除采购合同", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Order.Delete")
    //@OperLog(title = "删除采购合同")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyOrderService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增采购合同Item", notes = "新增采购合同Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.Add")
    public R<BuyOrderitemPojo> createItem(@RequestBody String json) {
        try {
            BuyOrderitemPojo buyOrderitemPojo = JSONArray.parseObject(json, BuyOrderitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            buyOrderitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.buyOrderitemService.insert(buyOrderitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除采购合同Item", notes = "删除采购合同Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Order.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyOrderitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核采购合同", notes = "审核采购合同", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Order.Approval")
    public R<BuyOrderPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            BuyOrderPojo buyOrderPojo = buyOrderService.getBillEntity(key, tid);
//            // 判断是否启用货品进价校验 采购货品的含税单价大于货品设置的含税进价,禁止审批
//            R<String> configValue = this.systemFeignService.getConfigValue("module.buy.pricelimit", tid, loginUser.getToken());
//            if (configValue.getCode() == 200 && "goods".equals(configValue.getData())) {
//                List<String> invalidGoodsNames = buyOrderPojo.getItem().stream()
//                        .filter(item -> item.getTaxprice().compareTo(item.getIntaxprice() == null ? 0 : item.getIntaxprice()) > 0)
//                        .map(BuyOrderitemPojo::getGoodsname)
//                        .collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(invalidGoodsNames)) {
//                    return R.fail("禁止审批,以下采购货品的含税单价大于货品设置的含税进价: " + String.join("、", invalidGoodsNames));
//                }
//            }
            //订单采购预收率
            //	module.buy.machorderdeporate 百分之1~100 ；0 不限
            //业务场景： 一个销售订单整单转到采购订单，需要验证销售订单预收率（预收款/billTaxAmount）;操作员保存后，有财务人员（放行权）审核；
            String OkMsg = "";
            if ("销售订单".equals(buyOrderPojo.getBilltype())) {
                // 1. 配置：1-100；0 表示不限
                String cfgStr = saConfigMapper.getCfgValueByCfgKey("module.buy.machorderdeporate");
                BigDecimal cfg = StringUtils.isBlank(cfgStr) ? BigDecimal.ZERO : new BigDecimal(cfgStr.trim());
                // 2. 查询销售订单金额与预收款 取第一个Machitemid即可
                BusMachiningPojo mach = busMachiningMapper.getEntityByitemid(buyOrderPojo.getItem().get(0).getMachitemid(), buyOrderPojo.getTenantid());
                if (mach == null) {
                    throw new BaseBusinessException("未找到对应销售订单");
                }
                BigDecimal billAmt = BigDecimal.valueOf(mach.getBilltaxamount());
                BigDecimal advaAmt = BigDecimal.valueOf(mach.getAdvaamount());
                // 3. 校验
                if (cfg.compareTo(BigDecimal.ZERO) > 0 && billAmt.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal actualRate = advaAmt.divide(billAmt, 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100));
                    if (actualRate.compareTo(cfg) < 0) {
                        // 检查用户是否有"Buy_Order.PassAdmin"权限
                        if (loginUser.getPermissions().contains("Buy_Order.PassAdmin")) {
                            OkMsg = "销售订单预收率不足，当前 " + actualRate.setScale(2, RoundingMode.HALF_UP) + "%，要求≥" + cfg + "% ,特批放行";
                        } else {
                            throw new BaseBusinessException("销售订单预收率不足，当前 " + actualRate.setScale(2, RoundingMode.HALF_UP) + "%，要求≥" + cfg + "%");
                        }
                    }
                }
            }
            if (buyOrderPojo.getAssessor().isEmpty()) {
                buyOrderPojo.setAssessor(loginUser.getRealname()); //审核员
                buyOrderPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                buyOrderPojo.setAssessor(""); //审核员
                buyOrderPojo.setAssessorid(""); //审核员
            }
            buyOrderPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.buyOrderService.approval(buyOrderPojo), OkMsg);
        } catch (
                Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "发出第三方审批", notes = "发出第三方审批wex/ding", produces = "application/json")
    @RequestMapping(value = "/sendapprovel", method = RequestMethod.GET)
    public R<ApprrecPojo> sendapprovel(String key, String apprid, String type) {
        try {
            if (type == null) type = "wxe";  // 默认走企业微信
            String verifyKey = CacheConstants.APPR_CODES_KEY + apprid;
            //获取token
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //创建VM数据对象
            VelocityContext context = new VelocityContext();
            //从redis中获取模板对象
            // Object obj = saRedisService.getCacheObject(verifyKey);
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
            ApprovePojo approvePojo = new ApprovePojo();
            //获得第三方账号
            SaJustauthPojo justauthByUserid = saJustauthService.getJustauthByUserid(loginUser.getUserid(), type, null);
            if (justauthByUserid == null) {
                PrintColor.red("D03M02B1/sendapprovel未找到第三方账号");
                return R.fail("D03M02B1/sendapprovel未找到第三方账号");
            }
            JustauthPojo justauthPojo = new JustauthPojo();
            org.springframework.beans.BeanUtils.copyProperties(justauthByUserid, justauthPojo);
            approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
            approvePojo.setUserid(justauthPojo.getAuthuuid());
            approvePojo.setModelcode(apprrecPojo.getTemplateid());
            BuyOrderPojo billEntity = this.buyOrderService.getBillEntity(key, loginUser.getTenantid());
            approvePojo.setObject(billEntity);


            // 发起oms审批,先判断是否正在审批 (最下面发起oms审批成功后需设置OaFlowMark=1)
            if (billEntity.getOaflowmark() != null && billEntity.getOaflowmark() == 1) {
                return R.fail("该单据已发起OA审批");
            }
            //获得第三方账号
            SaJustauthPojo justAuth = saJustauthService.getJustauthByUserid(loginUser.getUserid(), type, null);
            if (justAuth == null) {
                PrintColor.red("/dingapprovel 获得第三方账号出错");
                return R.fail("/dingapprovel 获得第三方账号出错");
            }
            org.springframework.beans.BeanUtils.copyProperties(justAuth, justauthPojo);

            approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
            approvePojo.setUserid(justauthPojo.getAuthuuid());
            approvePojo.setModelcode(apprrecPojo.getTemplateid());
            context.put("approvePojo", approvePojo);
            String str = apprrecPojo.getDatatemp();
            // 初始化并取得Velocity引擎
            VelocityEngine ve = new VelocityEngine();
            ve.init();
            // 转换输出
            StringWriter writer = new StringWriter();
            ve.evaluate(context, writer, "", str); // 关键方法
            //写回String
            str = writer.toString();
            apprrecPojo.setDatatemp(str);


            //新建审批记录
            apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
            apprrecPojo.setDatatemp(str);
            apprrecPojo.setApprname("订单审批");
            apprrecPojo.setResultcode("");
            apprrecPojo.setBillid(key);    // 单据ID
            apprrecPojo.setUserid("");
            apprrecPojo.setApprtype("");
            apprrecPojo.setCreateby(loginUser.getRealname());
            apprrecPojo.setCreatebyid(loginUser.getUserid());
            apprrecPojo.setCreatedate(new Date());
            apprrecPojo.setLister(loginUser.getRealname());
            apprrecPojo.setListerid(loginUser.getUserid());
            apprrecPojo.setModifydate(new Date());
            apprrecPojo.setTenantid(loginUser.getTenantid());
            //将企业微信审批信息存入redis
            String CachKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
            saRedisService.setKeyValue(CachKey, apprrecPojo, 60 * 12, TimeUnit.MINUTES);
            if ("wxe".equals(type)) {
                R r = this.saUtsFeignClient.wxeapprovel(apprrecPojo.getId(), loginUser.getTenantid());
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r);
                }
            } else {
                R r = this.saUtsFeignClient.dingapprovel(apprrecPojo.getId(), loginUser.getTenantid());
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r);
                }
            }
            // 发起审批成功,需设置OaFlowMark=1 并更新单据
            billEntity.setOaflowmark(1);
            buyOrderMapper.updateOaflowmark(billEntity);
            return R.ok(apprrecPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "审批回调修改状态", notes = "审批回调修改状态", produces = "application/json")
    @RequestMapping(value = "/justapprovel", method = RequestMethod.GET)
    public R<BuyOrderPojo> justapprovel(String key, String type, String approved) {
        try {
            PrintColor.red("/justapprovel 审批回调修改状态 approved:" + approved);
            //1.读取审批记录
            String verifyKey = CacheConstants.APPR_CODES_KEY + key;
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
            //2. 获得单据数据
            BuyOrderPojo billPojo = this.buyOrderService.getEntity(apprrecPojo.getBillid(), apprrecPojo.getTenantid());
            //3. 写入审核批
            //获得第三方账号
            if (type == null) type = "wxe";

            // oms审批即将完成,需设置OaFlowMark=0
            billPojo.setOaflowmark(0);
            buyOrderMapper.updateOaflowmark(billPojo);//只更新oaflowmark
            // 若oms审批点击拒绝(或由发起人取消发起),则设置完OaFlowMark=0直接结束方法
            if ("false".equals(approved)) {
                return R.ok();
            }

            SaJustauthPojo justauthByUuid = saJustauthService.getJustauthByUuid(apprrecPojo.getCallbackuuid(), type, null);
            if (justauthByUuid == null) {
                PrintColor.red("D03M02B1/justapprovel未找到第三方账号");
                return R.fail("D03M02B1/justapprovel未找到第三方账号");
            }

            JustauthPojo justauthPojo = new JustauthPojo();
            org.springframework.beans.BeanUtils.copyProperties(justauthByUuid, justauthPojo);
            billPojo.setAssessorid(justauthPojo.getUserid());
            billPojo.setAssessor(justauthPojo.getRealname()); //审核员
            billPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.buyOrderService.approval(billPojo));
        } catch (Exception e) {
            System.out.println("写入审核失败：" + e.getMessage());
            return R.fail(e.getMessage());
        }
    }


    /**
     * 作废单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "作废采购合同", notes = "作废采购合同,?type=1作废，0为反作废", produces = "application/json")
    @RequestMapping(value = "/disannul", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Edit")
    public R<BuyOrderPojo> disannul(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<BuyOrderitemPojo> lst = JSONArray.parseArray(json, BuyOrderitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyOrderService.disannul(lst, type, loginUser));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 作废单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "中止采购合同", notes = "中止采购合同,?type=1中止，0为反作废", produces = "application/json")
    @RequestMapping(value = "/closed", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Edit")
    public R<BuyOrderPojo> closed(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<BuyOrderitemPojo> lst = JSONArray.parseArray(json, BuyOrderitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.buyOrderService.closed(lst, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Order.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BuyOrderPojo buyOrderPojo = this.buyOrderService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(buyOrderPojo);
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = buyOrderPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BuyOrderitemPojo buyOrderitemPojo = new BuyOrderitemPojo();
                    buyOrderPojo.getItem().add(buyOrderitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(buyOrderPojo.getItem());

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "打印BuyOrder明细报表(分页PageList)", notes = "打印明细报表", produces = "application/json")
    @RequestMapping(value = "/printPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.Print")
    public void printPageList(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Buy_Order.BillDate");
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Buy_Order.Groupid='" + groupid + "'";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BuyOrderitemdetailPojo> lst = this.buyOrderService.getPageList(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BuyOrderitemdetailPojo itemdetailPojo = new BuyOrderitemdetailPojo();
                    lst.add(itemdetailPojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "打印BuyOrder单据报表(分页PageTh)", notes = "打印PageTh报表", produces = "application/json")
    @RequestMapping(value = "/printPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.Print")
    public void printPageTh(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Buy_Order.BillDate");
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Buy_Order.Groupid='" + groupid + "'";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BuyOrderPojo> lst = this.buyOrderService.getPageTh(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BuyOrderPojo pojo = new BuyOrderPojo();
                    lst.add(pojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Order.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            //=========获取单据表头信息========
            BuyOrderPojo buyOrderPojo = this.buyOrderService.getEntity(key, tid);


            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(buyOrderPojo);
            // 表头追加App_Workgroup供应商信息
            Map<String, Object> workgroupInfo = this.buyOrderService.getWorkgroupInfo(buyOrderPojo.getGroupid(), tid);
            map.putAll(workgroupInfo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<BuyOrderitemPojo> lstitem = this.buyOrderitemService.getList(key, tid);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "采购合同" + buyOrderPojo.getRefno());
            mapPrint.put("data", ptJson);
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "一式两份打印", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBillMulti", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Order.Print")
    public R<String> printWebBillMulti(String key, String ptid, String sn, Integer cmd) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            BuyOrderPojo buyOrderPojo = this.buyOrderService.getEntity(key, loginUser.getTenantid());


            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(buyOrderPojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            QueryParam queryParam = new QueryParam();
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setFilterstr(" and Buy_OrderItem.Pid='" + key + "'");
            queryParam.setOrderBy("Buy_OrderItem.RowNum");
            queryParam.setPageNum(1);
            queryParam.setPageSize(1000);
            List<BuyOrderitemdetailPojo> lstitem = this.buyOrderService.getPageList(queryParam).getList();
            List<BuyOrderitemdetailPojo> lstCopy = new ArrayList<>();
            for (BuyOrderitemdetailPojo map2 : lstitem) {
                BuyOrderitemdetailPojo newPojo = new BuyOrderitemdetailPojo();
                BeanUtils.copyProperties(map2, newPojo);
                lstCopy.add(newPojo);
            }
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);
            List<Map<String, Object>> lstCopyMap = attrListToMaps(lstCopy);
            // 给第一个 lst 中的每个 map 添加字段 ToWho=1，PageNo=1
            for (Map<String, Object> m : lst) {
                m.put("ToWho", 1);
                m.put("PageNo", 1);
            }

            // 给第二个 lstCopy 中的每个 map 添加字段 ToWho=2，PageNo=1
            for (Map<String, Object> m : lstCopyMap) {
                m.put("ToWho", 2);
                m.put("PageNo", 1);
            }
            // 合并两个 list
            lst.addAll(lstCopyMap);

            PrintColor.red("lst.size() = " + lst.size());
            PrintColor.red("lst: " + lst);


            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "采购合同" + buyOrderPojo.getRefno());
            mapPrint.put("data", ptJson);
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表,合并Item", notes = "打印报表,合并Item", produces = "application/json")
    @RequestMapping(value = "/printBillMergeGoods", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Buy_Order.Print")
    public void printBillMergeGoods(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        BuyOrderPojo buyOrderPojo = this.buyOrderService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(buyOrderPojo);
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //开始合并Item
        List<BuyOrderitemPojo> lstMerge = new ArrayList<>();
        for (BuyOrderitemPojo item : buyOrderPojo.getItem()) {
            int merge = 0;
            for (int i = 0; i < lstMerge.size(); i++) {
                if (item.getGoodsid().equals(lstMerge.get(i).getGoodsid())) {
                    BigDecimal mergeAmt = BigDecimal.valueOf(lstMerge.get(i).getAmount());
                    BigDecimal itemAmt = BigDecimal.valueOf(item.getAmount());
                    lstMerge.get(i).setAmount(mergeAmt.add(itemAmt).doubleValue());
                    BigDecimal mergeTAmt = BigDecimal.valueOf(lstMerge.get(i).getTaxamount());
                    BigDecimal itemTAmt = BigDecimal.valueOf(item.getTaxamount());
                    lstMerge.get(i).setTaxamount(mergeTAmt.add(itemTAmt).doubleValue());
                    BigDecimal mergeQty = BigDecimal.valueOf(lstMerge.get(i).getQuantity());
                    BigDecimal itemQty = BigDecimal.valueOf(item.getQuantity());
                    lstMerge.get(i).setQuantity(mergeQty.add(itemQty).doubleValue());
                    merge = 1;
                }
            }
            //如果没有合并
            if (merge == 0) {
                BuyOrderitemPojo itemPojo = new BuyOrderitemPojo();
                BeanUtils.copyProperties(item, itemPojo);
                lstMerge.add(itemPojo);
            }
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = buyOrderPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BuyOrderitemPojo buyOrderitemPojo = new BuyOrderitemPojo();
                    lstMerge.add(buyOrderitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(lstMerge);

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBillMergeGoods", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public R<String> printWebBillMergeGoods(String key, String ptid, String sn, Integer cmd) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            BuyOrderPojo buyOrderPojo = this.buyOrderService.getEntity(key, loginUser.getTenantid());

            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(buyOrderPojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<BuyOrderitemPojo> lstitem = this.buyOrderitemService.getList(key, loginUser.getTenantid());

            //开始合并Item
            List<BuyOrderitemPojo> lstMerge = new ArrayList<>();
            for (BuyOrderitemPojo item : lstitem) {
                int merge = 0;
                for (int i = 0; i < lstMerge.size(); i++) {
                    if (item.getGoodsid().equals(lstMerge.get(i).getGoodsid())) {
                        BigDecimal mergeAmt = BigDecimal.valueOf(lstMerge.get(i).getAmount());
                        BigDecimal itemAmt = BigDecimal.valueOf(item.getAmount());
                        lstMerge.get(i).setAmount(mergeAmt.add(itemAmt).doubleValue());
                        BigDecimal mergeTAmt = BigDecimal.valueOf(lstMerge.get(i).getTaxamount());
                        BigDecimal itemTAmt = BigDecimal.valueOf(item.getTaxamount());
                        lstMerge.get(i).setTaxamount(mergeTAmt.add(itemTAmt).doubleValue());
                        BigDecimal mergeQty = BigDecimal.valueOf(lstMerge.get(i).getQuantity());
                        BigDecimal itemQty = BigDecimal.valueOf(item.getQuantity());
                        lstMerge.get(i).setQuantity(mergeQty.add(itemQty).doubleValue());
                        merge = 1;
                    }
                }
                //如果没有合并
                if (merge == 0) {
                    BuyOrderitemPojo itemPojo = new BuyOrderitemPojo();
                    BeanUtils.copyProperties(item, itemPojo);
                    lstMerge.add(itemPojo);
                }
            }

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstMerge);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "采购合同" + buyOrderPojo.getRefno());
            mapPrint.put("data", ptJson);
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "云打印BuyOrder明细报表(分页PageList)", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Buy_Order.Print")
    public R<String> printWebPageList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Order.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Buy_Order.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BuyOrderitemdetailPojo> lstitem = this.buyOrderService.getPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "BuyOrder明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setKeyValue("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "批量云打印报表(List<BuyOrderPojo>不带item)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),groupid不为null则只获取第一条客户信息", produces = "application/json")
    @RequestMapping(value = "/printWebPageTh", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Buy_Order.Print")
    public R<String> printWebPageTh(@RequestBody String json, String ptid, String groupid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Buy_Order.BillDate");
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Buy_Order.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BuyOrderPojo> lstTh = this.buyOrderService.getPageTh(queryParam).getList();
            // 获取单据表头.表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (groupid != null && lstTh.size() > 0) {
                map.put("groupname", lstTh.get(0).getGroupname());
                map.put("abbreviate", lstTh.get(0).getAbbreviate());
                map.put("groupuid", lstTh.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstTh);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "wip批量打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setKeyValue("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

