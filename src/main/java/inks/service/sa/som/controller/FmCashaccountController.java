package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.FmCashaccountPojo;
import inks.service.sa.som.service.FmCashaccountService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 出纳账户(Fm_CashAccount)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-15 16:58:08
 */

public class FmCashaccountController {

    @Resource
    private FmCashaccountService fmCashaccountService;


    @Resource
    private SaRedisService saRedisService;


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取出纳账户详细信息", notes = "获取出纳账户详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Fm_CashAccount.List")
    public R<FmCashaccountPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.fmCashaccountService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Fm_CashAccount.List")
    public R<PageInfo<FmCashaccountPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Fm_CashAccount.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.fmCashaccountService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增出纳账户", notes = "新增出纳账户", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Fm_CashAccount.Add")
    public R<FmCashaccountPojo> create(@RequestBody String json) {
        try {
            FmCashaccountPojo fmCashaccountPojo = JSONArray.parseObject(json, FmCashaccountPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            fmCashaccountPojo.setCreateby(loginUser.getRealname());   // 创建者
            fmCashaccountPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            fmCashaccountPojo.setCreatedate(new Date());   // 创建时间
            fmCashaccountPojo.setLister(loginUser.getRealname());   // 制表
            fmCashaccountPojo.setListerid(loginUser.getUserid());    // 制表id  
            fmCashaccountPojo.setModifydate(new Date());   //修改时间
            fmCashaccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.fmCashaccountService.insert(fmCashaccountPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改出纳账户", notes = "修改出纳账户", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Fm_CashAccount.Edit")
    public R<FmCashaccountPojo> update(@RequestBody String json) {
        try {
            FmCashaccountPojo fmCashaccountPojo = JSONArray.parseObject(json, FmCashaccountPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            fmCashaccountPojo.setLister(loginUser.getRealname());   // 制表
            fmCashaccountPojo.setListerid(loginUser.getUserid());    // 制表id  
            fmCashaccountPojo.setTenantid(loginUser.getTenantid());   //租户id
            fmCashaccountPojo.setModifydate(new Date());   //修改时间
//            fmCashaccountPojo.setAssessor(""); // 审核员
//            fmCashaccountPojo.setAssessorid(""); // 审核员id
//            fmCashaccountPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.fmCashaccountService.update(fmCashaccountPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除出纳账户", notes = "删除出纳账户", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Fm_CashAccount.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //检查引用
            List<String> lstcite = this.fmCashaccountService.getCiteBillName(key, loginUser.getTenantid());
            if (lstcite.size() > 0) {
                return R.fail( lstcite.toString());
            }
            return R.ok(this.fmCashaccountService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Fm_CashAccount.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        FmCashaccountPojo fmCashaccountPojo = this.fmCashaccountService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(fmCashaccountPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

