package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.MatCarryoverPojo;
import inks.service.sa.som.domain.pojo.MatCarryoveritemPojo;
import inks.service.sa.som.domain.pojo.MatCarryoverrecPojo;
import inks.service.sa.som.service.MatCarryoverService;
import inks.service.sa.som.service.MatCarryoveritemService;
import inks.service.sa.som.service.MatCarryoverrecService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 仓库结转(MatCarryover)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-13 20:55:29
 */
@RestController
@RequestMapping("D04M06B1")
@Api(tags = "D04M06B1:仓库结转")
public class D04M06B1Controller extends MatCarryoverController {


    @Resource
    private MatCarryoverService matCarryoverService;
    @Resource
    private SaBillcodeService saBillcodeService;

    @Resource
    private MatCarryoveritemService matCarryoveritemService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private MatCarryoverrecService matCarryoverrecService;


    @ApiOperation(value = " 新增仓库结转", notes = "新增仓库结转", produces = "application/json")
    @RequestMapping(value = "/createCarry", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Carryover.Add")
    public R<MatCarryoverPojo> createCarry(@RequestBody String json) {
        try {
            MatCarryoverPojo matCarryoverPojo = JSONArray.parseObject(json, MatCarryoverPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D04M06B1", loginUser.getTenantid(),"Mat_Carryover");
            matCarryoverPojo.setRefno(refNo);
            matCarryoverPojo.setCreateby(loginUser.getRealName());   // 创建者
            matCarryoverPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matCarryoverPojo.setCreatedate(new Date());   // 创建时间
            matCarryoverPojo.setLister(loginUser.getRealname());   // 制表
            matCarryoverPojo.setListerid(loginUser.getUserid());    // 制表id
            matCarryoverPojo.setModifydate(new Date());   //修改时间
            matCarryoverPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.matCarryoverService.createCarry(matCarryoverPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询Item", notes = "按条件分页查询Item", produces = "application/json")
    @RequestMapping(value = "/getItemPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Carryover.List")
    public R<PageInfo<MatCarryoveritemPojo>> getItemPageList(@RequestBody String json, String key) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_CarryoverItem.RowNum");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setFilterstr(" and Mat_CarryoverItem.Pid='" + key + "'");
            return R.ok(this.matCarryoveritemService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getInitPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Carryover.List")
    public R<PageInfo<MatCarryoverPojo>> getInitPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Carryover.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and Mat_Carryover.BillType='期初建账'");
            return R.ok(this.matCarryoverService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Carryover.List")
    public R<PageInfo<MatCarryoverPojo>> getPageTh(@RequestBody String json, String storeid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Carryover.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            if (storeid != null) {
                queryParam.setFilterstr(" and Mat_Carryover.storeid='" + storeid + "'");
            }
            return R.ok(this.matCarryoverService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageThByMonth", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Carryover.List")
    public R<PageInfo<MatCarryoverPojo>> getPageThByMonth(@RequestBody String json, Integer year, Integer month) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            queryParam.setOrderBy("Mat_Carryover.StoreCode");
            queryParam.setOrderType(0);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and Mat_Carryover.carryyear=" + year + " and Mat_Carryover.carrymonth=" + month);
            return R.ok(this.matCarryoverService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "获取仓库账单详细信息", notes = "获取仓库账单详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByNew", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Carryover.List")
    public R<MatCarryoverPojo> getBillEntityByNew(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatCarryoverPojo busAccountPojo = new MatCarryoverPojo();
            MatCarryoverrecPojo busAccountrecPojo = this.matCarryoverrecService.getEntityByMax(loginUser.getTenantid());
            busAccountPojo.setTenantid(loginUser.getTenantid());
            busAccountPojo.setStoreid(key);
            if (busAccountrecPojo == null) {
                busAccountPojo.setStartdate(DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-01-01 00:00:00", new Date())));
            } else {
                busAccountPojo.setStartdate(DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1));
            }
            busAccountPojo.setEnddate(new Date());
            List<MatCarryoveritemPojo> lst = this.matCarryoverService.pullItemList(busAccountPojo);
            busAccountPojo.setItem(lst);
            return R.ok(busAccountPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "获取结转记录最新记录", notes = "获取结转记录最新记录, key为 Storeid", produces = "application/json")
    @RequestMapping(value = "/getMaxBillEntityByStore", method = RequestMethod.GET)
    public R<MatCarryoverPojo> getMaxBillEntityByStore(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.matCarryoverService.getMaxBillEntityByStore(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "跨期拉取采购对账item", notes = "跨期拉取采购对账item,包含已对账和未对账", produces = "application/json")
    @RequestMapping(value = "/getMatCarryoverList", method = RequestMethod.POST)
    public R<List<MatCarryoveritemPojo>> getMatCarryoverList(@RequestBody String json, String storeid) {
        try {
            List<MatCarryoveritemPojo> lstRt = new ArrayList<>();
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // 当前结算到哪一时间
            MatCarryoverrecPojo matCarryoverrecPojo = this.matCarryoverrecService.getEntityByMax(loginUser.getTenantid());
            if (matCarryoverrecPojo != null) {
                queryParam.getDateRange().setEndDate(matCarryoverrecPojo.getEnddate());
                queryParam.setFilterstr(" and Buy_Account.Groupid='" + storeid + "'");
                List<MatCarryoveritemPojo> lstAccoitem = this.matCarryoverService.getMultItemList(queryParam);
                if (lstAccoitem != null) {
                    for (MatCarryoveritemPojo item : lstAccoitem) {
                        MatCarryoveritemPojo rtitem = new MatCarryoveritemPojo();
                        BeanUtils.copyProperties(item, rtitem);
                        lstRt.add(rtitem);
                    }
                }
            }

            MatCarryoverPojo matCarryoverPojo = new MatCarryoverPojo();
            matCarryoverPojo.setTenantid(loginUser.getTenantid());
            matCarryoverPojo.setStoreid(storeid);
            if (matCarryoverrecPojo != null) {
                matCarryoverPojo.setStartdate(DateUtils.addSeconds(matCarryoverrecPojo.getEnddate(), 1));
            }
            matCarryoverPojo.setEnddate(queryParam.getDateRange().getEndDate());
            List<MatCarryoveritemPojo> lstpullItem = this.matCarryoverService.pullItemList(matCarryoverPojo);

            if (lstRt.size() > 0) {
                if (lstpullItem != null) {
                    for (MatCarryoveritemPojo item : lstpullItem) {
                        MatCarryoveritemPojo rtitem = new MatCarryoveritemPojo();
                        BeanUtils.copyProperties(item, rtitem);
                        lstRt.add(rtitem);
                    }
                }
                return R.ok(lstRt);
            } else {
                return R.ok(lstpullItem);
            }

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param json 主键
     * @return 单条数据
     */
    @ApiOperation(value = "获取供应商实时应付款报表", notes = "获取供应商实时应付款报表", produces = "application/json")
    @RequestMapping(value = "/getNowPageList", method = RequestMethod.POST)
    public R<PageInfo<MatCarryoverPojo>> getNowPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("GroupUid");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            MatCarryoverrecPojo busAccountrecPojo = this.matCarryoverrecService.getEntityByMax(loginUser.getTenantid());
            Date startDate = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy", new Date()) + "-01-01");
            Date endDate = new Date();
            if (busAccountrecPojo != null) {
                startDate = DateUtils.addSeconds(busAccountrecPojo.getEnddate(), 1);
            }
            DateRange dateRange = new DateRange("", startDate, endDate);
            queryParam.setDateRange(dateRange);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.matCarryoverService.getNowPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
