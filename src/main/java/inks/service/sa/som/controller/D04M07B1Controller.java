package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.MatInvenotePojo;
import inks.service.sa.som.domain.pojo.MatInvenoteitemPojo;
import inks.service.sa.som.service.MatInvenoteService;
import inks.service.sa.som.service.MatInvenoteitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 仓库盘点(MatInvenote)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-13 20:54:21
 */
@RestController
@RequestMapping("D04M07B1")
@Api(tags = "D04M07B1:仓库盘点")
public class D04M07B1Controller extends MatInvenoteController {


    @Resource
    private MatInvenoteService matInvenoteService;


    @Resource
    private MatInvenoteitemService matInvenoteitemService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private SaBillcodeService saBillcodeService;


    @ApiOperation(value = "后端新增仓库盘点表", notes = "后端新增仓库盘点表", produces = "application/json")
    @RequestMapping(value = "/createByNote", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_InveNote.Add")
    public R<MatInvenotePojo> createByNote(@RequestBody String json) {
        try {
            MatInvenotePojo matInvenotePojo = JSONArray.parseObject(json, MatInvenotePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D04M07B1", loginUser.getTenantid(), "Mat_InveNote");
            matInvenotePojo.setRefno(refNo);
            matInvenotePojo.setCreateby(loginUser.getRealName());   // 创建者
            matInvenotePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            matInvenotePojo.setCreatedate(new Date());   // 创建时间
            matInvenotePojo.setLister(loginUser.getRealname());   // 制表
            matInvenotePojo.setListerid(loginUser.getUserid());    // 制表id
            matInvenotePojo.setModifydate(new Date());   //修改时间
            matInvenotePojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.matInvenoteService.createByNote(matInvenotePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询Item online默认0; 1留下账面数量不等于盘点数量；盘盈和盘亏不为0的行", notes = "按条件分页查询Item", produces = "application/json")
    @RequestMapping(value = "/getItemPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_InveNote.List")
    public R<PageInfo<MatInvenoteitemPojo>> getItemPageList(@RequestBody String json, String key, @RequestParam(defaultValue = "0") int online) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_InveNoteItem.RowNum");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Mat_InveNoteItem.Pid='" + key + "'";
            //留下账面数量不等于盘点数量；盘盈和盘亏不为0的行
            if (online == 1) {
                qpfilter += " and Mat_InveNoteItem.CurrQty<>Mat_InveNoteItem.Quantity";
                qpfilter += " and Mat_InveNoteItem.CurrQty<>0 and Mat_InveNoteItem.OverFlowQty<>0";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInvenoteitemService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param key 实体
     * @return 新增结果
     */
    @ApiOperation(value = "生成盘点出入表", notes = "生成盘点出入表", produces = "application/json")
    @RequestMapping(value = "/createAccess", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Mat_Access.Add")
    public R<String> createAccess(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            return R.ok(this.matInvenoteService.createAccess(key, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表 key=billid,ptid打印模版,{ids:xxx}", produces = "application/json")
    @RequestMapping(value = "/printItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Print")
    public void printItem(String key, String ptid, @RequestBody String json) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        Map<String, Object> mapids = JSONArray.parseObject(json, Map.class);
        //获取单据信息
        MatInvenotePojo matInvenotePojo = this.matInvenoteService.getEntity(key, loginUser.getTenantid());
        matInvenotePojo.setItem(this.matInvenoteService.getItemListByIds(mapids.get("ids").toString(), key, loginUser.getTenantid()));

        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(matInvenotePojo);
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = matInvenotePojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    MatInvenoteitemPojo matInvenoteitemPojo = new MatInvenoteitemPojo();
                    matInvenotePojo.getItem().add(matInvenoteitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(matInvenotePojo.getItem());

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);

        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}
