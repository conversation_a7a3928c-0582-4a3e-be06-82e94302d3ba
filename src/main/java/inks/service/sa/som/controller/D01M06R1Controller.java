package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.BusDelieryPojo;
import inks.service.sa.som.domain.pojo.BusDelieryitemdetailPojo;
import inks.service.sa.som.service.BusDelieryService;
import inks.service.sa.som.service.BusMachiningitemService;
import inks.service.sa.som.service.D01M06R1Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 销售单(BusDeliery)报表
 *
 * <AUTHOR>
 * @since 2021-11-13 10:19:45
 */
@RestController
@RequestMapping("D01M06R1")
@Api(tags = "D01M06R1:销售发货报表")
public class D01M06R1Controller extends BusDelieryController {


    @Resource
    private D01M06R1Service d01M06R1Service;


    @Resource
    private BusMachiningitemService busMachiningitemService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private BusDelieryService busDelieryService;


    @ApiOperation(value = "按条件分页查询结余明细含未审核", notes = "按条件分页查询结余明细含未审核?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_DelieryItem.FinishQty<Bus_DelieryItem.Quantity+Bus_DelieryItem.FreeQty";
            qpfilter += " and Bus_DelieryItem.DisannulMark=0 and Bus_DelieryItem.FinishClosed=0 ";  // 未关闭、未注销
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询待出库明细", notes = "按条件分页查询待出库明细?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineOutPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getOnlineOutPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_DelieryItem.FinishQty<Bus_DelieryItem.Quantity+Bus_DelieryItem.FreeQty";
            qpfilter += " and Bus_DelieryItem.DisannulMark=0 and Bus_DelieryItem.FinishClosed=0 and Bus_DelieryItem.VirtualItem=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Deliery.Assessorid<>''";  // 已审核
            qpfilter += " and Bus_Deliery.BillType IN ('发出商品','其他发货','返工补发')";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询待入库明细", notes = "按条件分页查询待入库明细?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineInPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getOnlineInPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_DelieryItem.FinishQty<Bus_DelieryItem.Quantity+Bus_DelieryItem.FreeQty";
            qpfilter += " and Bus_DelieryItem.DisannulMark=0 and Bus_DelieryItem.FinishClosed=0 and Bus_DelieryItem.VirtualItem=0";  // 未关闭、未注销
            qpfilter += " and Bus_Deliery.Assessorid<>''";  // 已审核
            qpfilter += " and Bus_Deliery.BillType IN ('订单退货','其他退货','退货返工')";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询待开票明细", notes = "按条件分页查询待开票明细?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlineInvoPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getOnlineInvoPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_DelieryItem.InvoQty<Bus_DelieryItem.Quantity";
            qpfilter += " and Bus_DelieryItem.DisannulMark=0 and Bus_DelieryItem.InvoClosed=0 ";  // 未关闭、未注销
            qpfilter += " and Bus_Deliery.Assessorid<>''";  // 已审核
            qpfilter += " and Bus_Deliery.BillReceived=0 ";  // 未直接付款过
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询待拣货明细", notes = "按条件分页查询待拣货明细?groupid", produces = "application/json")
    @RequestMapping(value = "/getOnlinePickPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getOnlinePickPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_DelieryItem.PickQty<Bus_DelieryItem.Quantity+Bus_DelieryItem.FreeQty";
            qpfilter += " and Bus_DelieryItem.DisannulMark=0 and Bus_DelieryItem.FinishClosed=0 and Bus_DelieryItem.VirtualItem=0";  // 未关闭、未注销
            qpfilter += " and Bus_Deliery.Assessorid<>''";  // 已审核
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryPojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Deliery.FinishCount<Bus_Deliery.ItemCount";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineRecePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryPojo>> getOnlineRecePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Deliery.InvoCount=0 and ABS(Bus_Deliery.BillReceived)<ABS(Bus_Deliery.BillTaxAmount)";
            qpfilter += " and Bus_Deliery.Assessorid<>''";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 获取销售单客户金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = " 获取销售单客户金额排名", notes = "获取销售单客户金额排名", produces = "application/json")
    @RequestMapping(value = "/getSumPageListByGroup", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getSumPageListByGroup(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.BillDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumPageListByGroup(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 获取销售单客户金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = " 获取销售单客户金额排名", notes = "获取销售单客户金额排名", produces = "application/json")
    @RequestMapping(value = "/getSumPageListByGoods", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getSumPageListByGoods(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.BillDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumPageListByGoods(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 获取销售单客户金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = " 获取发货单客户金额排名", notes = "获取发货单客户金额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupMax", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<List<ChartPojo>> getSumAmtByGroupMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumAmtByGroupMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return R<List < Map < Object>>>
     * @Description (按省份 / 地级市)获取送货单客户金额排名 如果不传入省份名，则统计每个省份的客户订单金额排名；如果传入省份名，则统计每个地级市的客户订单金额排名
     * <AUTHOR>
     * @param[1] json
     * @param[2] Province
     * @time 2023/5/27 11:22
     */
    @ApiOperation(value = " (按客户省份/地级市)获取送货单客户金额排名", notes = "(按省份/地级市)获取送货单客户金额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupProvince", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<List<Map<String, Object>>> getSumAmtByGroupProvince(@RequestBody String json, String province) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumAmtByGroupProvince(queryParam, province));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description (按客户省份/地级市)获取货品销售(送货单金额)排行榜
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = " (按客户省份/地级市)获取货品销售(送货单金额)排行榜 ", notes = "(按客户省份/地级市)获取货品销售(送货单金额)排行榜 ", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGoodsMax", method = RequestMethod.POST)
    public R<List<ChartPojo>> getSumAmtByGoodsMax(@RequestBody String json, String province, String city) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumAmtByGoodsMax(queryParam, province, city));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * @return R<List < Map < Object>>>
     * @Description (按省份 / 地级市)获取销售订单客户金额排名 如果不传入省份名，则统计每个省份的客户订单金额排名；如果传入省份名，则统计每个地级市的客户订单金额排名
     * <AUTHOR>
     * @param[1] json
     * @param[2] Province
     * @time 2023/5/27 11:22
     */
    @ApiOperation(value = " (按客户省份/地级市)获取销售订单客户金额排名", notes = "(按省份/地级市)获取送货单客户金额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupProvinceMach", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<Map<String, Object>>> getSumAmtByGroupProvinceMach(@RequestBody String json, String province) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumAmtByGroupProvinceMach(queryParam, province));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " (按国家)获取销售订单客户金额排名", notes = "(按省份/地级市)获取送货单客户金额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtGroupByCountry", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<Map<String, Object>>> getSumAmtGroupByCountry(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumAmtGroupByCountry(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * @return R<List < Map < Object>>>
     * @Description (按省份 / 地级市)获取收款单, 预收款单客户金额排名 如果不传入省份名，则统计每个省份的客户订单金额排名；如果传入省份名，则统计每个地级市的客户订单金额排名
     * <AUTHOR>
     * @param[1] json
     * @param[2] Province
     * @time 2023/5/27 11:22
     */
    @ApiOperation(value = " (按客户省份/地级市)获取[收款单+预收款单]客户金额排名", notes = "(按省份/地级市)获取收款单,预收款单客户金额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupProvinceCollection", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<List<Map<String, Object>>> getSumAmtByGroupProvinceCollection(@RequestBody String json, String province) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumAmtByGroupProvinceCollection(queryParam, province));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /*
     *
     * <AUTHOR>
     * @description 销售趋势图年
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "销售趋势图年", notes = "销售趋势图年", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByYear", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByYear(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumAmtByYear(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图月
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "销售趋势图月", notes = "销售趋势图月", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByMonth", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByMonth(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumAmtByMonth(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图周
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "销售趋势图周", notes = "销售趋势图周", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByDay", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByDay(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumAmtByDay(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 本月销售额
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "本月销售额", notes = "本月销售额", produces = "application/json")
    @RequestMapping(value = "/getTagSumAmtQtyByMonth", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<ChartPojo> getTagSumAmtQtyByMonth(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getTagSumAmtQtyByMonth(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售饼状图年
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "销售饼状图年", notes = "销售饼状图年", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByYearMax", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByYearMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumAmtByYearMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售饼状图月
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "销售饼状图月", notes = "销售饼状图月", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByMonthMax", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByMonthMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumAmtByMonthMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售饼状图周
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "销售饼状图周", notes = "销售饼状图周", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByDayMax", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByDayMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01M06R1Service.getSumAmtByDayMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryPojo>> getPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.d01M06R1Service.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.d01M06R1Service.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListBySalesman", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getPageListBySalesman(@RequestBody String json, String groupid, String seller) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            if (StringUtils.isNotBlank(seller)) {
                qpfilter += " and Bus_Deliery.Groupid in (select distinct id from App_Workgroup where Seller='" + seller + "' and Tenantid='" + loginUser.getTenantid() + "')";
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.d01M06R1Service.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param ptid 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印销售对账明细报表", notes = "打印销售对账明细报表", produces = "application/json")
    @RequestMapping(value = "/printAccountList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public void printAccountList(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Bus_Deliery.BillDate");
        queryParam.setTenantid(loginUser.getTenantid());
        String qpfilter = " and Bus_Deliery.Groupid='" + groupid + "'";
        queryParam.setFilterstr(qpfilter);
        List<BusDelieryitemdetailPojo> lstDetail = this.d01M06R1Service.getPageList(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        map.put("startdate", queryParam.getDateRange().getStartDate());
        map.put("enddate", queryParam.getDateRange().getEndDate());
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        map.put("lister", loginUser.getRealname());
        if (lstDetail.size() > 0) {
            map.put("groupuid", lstDetail.get(0).getGroupuid());
            map.put("groupname", lstDetail.get(0).getGroupname());
            map.put("abbreviate", lstDetail.get(0).getAbbreviate());
        }
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lstDetail.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusDelieryitemdetailPojo busDelieryitemPojo = new BusDelieryitemdetailPojo();
                    lstDetail.add(busDelieryitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(lstDetail);

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param ptid 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印销售对账明细报表", notes = "打印销售对账明细报表", produces = "application/json")
    @RequestMapping(value = "/printAccountTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public void printAccountTh(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Bus_Deliery.BillDate");
        queryParam.setTenantid(loginUser.getTenantid());
        String qpfilter = " and Bus_Deliery.Groupid='" + groupid + "'";
        // 加入场景   Eric ********
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        List<BusDelieryPojo> lstDetail = this.d01M06R1Service.getPageTh(queryParam).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        map.put("startdate", queryParam.getDateRange().getStartDate());
        map.put("enddate", queryParam.getDateRange().getEndDate());
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        map.put("lister", loginUser.getRealname());
        if (lstDetail.size() > 0) {
            map.put("groupuid", lstDetail.get(0).getGroupuid());
            map.put("groupname", lstDetail.get(0).getGroupname());
            map.put("abbreviate", lstDetail.get(0).getAbbreviate());
        }
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lstDetail.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusDelieryPojo busDelieryPojo = new BusDelieryPojo();
                    lstDetail.add(busDelieryPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> lst = attrListToMaps(lstDetail);

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印销售订单明细报表", notes = "打印销售订单明细报表", produces = "application/json")
    @RequestMapping(value = "/printPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Print")
    public void printPageList(@RequestBody String json, String groupid, String ptid, HttpServletRequest request) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Bus_Deliery.BillDate");

        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
        }

        // 加入场景   Eric ********
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<BusDelieryitemdetailPojo> lst = this.busDelieryService.getPageList(queryParam).getList();

        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }

        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    BusDelieryitemdetailPojo wkWipnoteitemPojo = new BusDelieryitemdetailPojo();
                    lst.add(wkWipnoteitemPojo);
                }
            }
        }

        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 打印单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "分页云打印发货明细报表", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    public R<String> printWebPageList(@RequestBody(required = false) String json, String ptid, String groupid, String sn, Integer cmd, Integer redis, Integer online, HttpServletRequest request) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());


            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.BillDate");

            String qpfilter = "";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            if (online != null && online == 1) {
                qpfilter += " and Bus_DelieryItem.FinishQty<Bus_DelieryItem.Quantity+Bus_DelieryItem.FreeQty";
                qpfilter += " and Bus_DelieryItem.DisannulMark=0 and Bus_DelieryItem.FinishClosed=0 ";  // 未关闭、未注销
            }
            // 加入场景   Eric ********
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<BusDelieryitemdetailPojo> lstitem = this.busDelieryService.getPageList(queryParam).getList();

            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (queryParam.getDateRange() != null) {
                map.put("startdate", queryParam.getDateRange().getStartDate());
                map.put("enddate", queryParam.getDateRange().getEndDate());
            }
            if (groupid != null && lstitem.size() > 0) {
                map.put("groupname", lstitem.get(0).getGroupname());
                map.put("abbreviate", lstitem.get(0).getAbbreviate());
                map.put("groupuid", lstitem.get(0).getGroupuid());
            } else {
                map.put("groupname", "");
                map.put("abbreviate", "");
                map.put("groupuid", "");
            }
            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstitem);
//            this.busDelieryService.splitGroupjson(lst,loginUser.getTenantid());
            for (Map<String, Object> map2 : lst) {
                if (map2.get("costgroupjson") != null && !map2.get("costgroupjson").toString().isEmpty()) {
                    List<Map<String, Object>> listObjectSec = JSONArray.parseObject(map2.get("costgroupjson").toString(), List.class);
                    for (Map<String, Object> mapList : listObjectSec) {
                        if (mapList.get("key") != null && mapList.get("value") != null)
                            map2.put(mapList.get("key").toString(), mapList.get("value").toString());
                    }
                }
            }

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "订单明细：" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getStartDate()) + "~" + DateUtils.parseDateToStr("yyyy-MM-dd", queryParam.getDateRange().getEndDate()));   // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setKeyValue("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
