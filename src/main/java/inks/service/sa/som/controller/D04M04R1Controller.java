package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.MatGoodsPojo;
import inks.service.sa.som.mapper.MatInventoryMapper;
import inks.service.sa.som.service.MatInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 库存信息(Mat_Inventory)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-12 14:23:41
 */
@RestController
@RequestMapping("D04M04R1")
@Api(tags = "D04M04R1:物料订单占用")
public class D04M04R1Controller extends MatInventoryController {
    @Resource
    private MatInventoryService matInventoryService;
    @Resource
    private MatInventoryMapper matInventoryMapper;
    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "分页查询物料订单占用(通过物料Code获取订单占用数量)", notes = "物料订单占用", produces = "application/json")
    @RequestMapping(value = "/getMatShiftPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<PageInfo<MatGoodsPojo>> getMatShiftPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            // Goods过滤到有效且物料
            String qpfilter = "  and Mat_Goods.GoodsState='物料' and Mat_Goods.enabledmark=1";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInventoryService.getMatShiftPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询物料订单占用(通过物料Goodsid获取订单占用数量)", notes = "物料订单占用", produces = "application/json")
    @RequestMapping(value = "/getMatShiftPageListB", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<PageInfo<MatGoodsPojo>> getMatShiftPageListB(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Mat_Goods.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            // Goods过滤到有效且物料
            String qpfilter = " and Mat_Goods.GoodsState='物料' and Mat_Goods.enabledmark=1";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.matInventoryService.getMatShiftPageListB(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "(分页)查询库存信息表中每个货品的总数量,count数", notes = "(分页)查询库存信息表中每个货品的总数量,count数", produces = "application/json")
    @RequestMapping(value = "/getSumPageListByGoods", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<PageInfo<Map<String, Object>>> getSumPageListByGoods(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.matInventoryService.getSumPageListByGoods(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
