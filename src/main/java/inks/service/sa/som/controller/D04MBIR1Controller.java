package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.*;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.MatInventoryPojo;
import inks.service.sa.som.mapper.D04MBIR1Mapper;
import inks.service.sa.som.service.D04MBIR1Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("D04MBIR1")
@Api(tags = "D04MBIR1:仓库大屏")
public class D04MBIR1Controller {
    @Resource
    private D04MBIR1Service d04MBIR1Service;

    /**
     * 服务对象
     */
    @Resource
    private D04MBIR1Mapper d04MBIR1Mapper;

    /**
     * 引用Token服务
     */
    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "库存品金额排行", notes = "库存品金额排行,pagesize为返回行数", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGoodsMax", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<List<MatInventoryPojo>> getSumAmtByGoodsMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d04MBIR1Service.getSumAmtByGoodsMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "库存汇总：金额、数量、记录数", notes = "金额value、数量valueb、记录数valuec", produces = "application/json")
    @RequestMapping(value = "/getSumAmtQtyByStore", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<List<ChartPojo>> getSumAmtQtyByStore(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null)  //未定义时间
                queryParam.setDateRange(new DateRange("Mat_Inventory.CreateDate", DateUtils.addMonths(new Date(), -1), new Date()));
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d04MBIR1Service.getSumAmtQtyByStore(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "出入库趋势图月", notes = "出入库趋势图月", produces = "application/json")
    @RequestMapping(value = "/getSumQtyByMonth", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<List<Map<String, Object>>> getSumQtyByMonth(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            //if (queryParam.getDateRange() == null)  //未定义时间
            //    queryParam.setDateRange(new DateRange("Mat_Access.BillDate", DateUtils.addMonths(new Date(), -12), new Date()));
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d04MBIR1Service.getSumQtyByMonth(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "各项出库图月", notes = "出入库趋势图月", produces = "application/json")
    @RequestMapping(value = "/getSumOutQtyOneMonth", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<List<Map<String, Object>>> getSumOutQtyOneMonth(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            List<Map<String, Object>> sumOutQtyOneMonth = this.d04MBIR1Service.getSumOutQtyOneMonth(queryParam);
            List<Map<String, Object>> newList = new ArrayList<>();
            Map<String, Object> originalMap = sumOutQtyOneMonth.get(0);

            for (Map.Entry<String, Object> entry : originalMap.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (!key.equals("billmonth")) {
                    Map<String, Object> newMap = new HashMap<>();
                    newMap.put("name", key);
                    newMap.put("value", String.valueOf(value));
                    newList.add(newMap);
                }
            }
            return R.ok(newList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "各项入库图月", notes = "出入库趋势图月", produces = "application/json")
    @RequestMapping(value = "/getSumInQtyOneMonth", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<List<Map<String, Object>>> getSumInQtyOneMonth(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            List<Map<String, Object>> sumInQtyOneMonth = this.d04MBIR1Service.getSumInQtyOneMonth(queryParam);
            List<Map<String, Object>> newList = new ArrayList<>();
            Map<String, Object> originalMap = sumInQtyOneMonth.get(0);
            for (Map.Entry<String, Object> entry : originalMap.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (!key.equals("billmonth")) {
                    Map<String, Object> newMap = new HashMap<>();
                    newMap.put("name", key);
                    newMap.put("value", String.valueOf(value));
                    newList.add(newMap);
                }
            }
            return R.ok(newList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    //    1.车间生产产值汇总表
//    要求：按照生产车间统计每日、每月、年度车间生产的产值汇总
//    返回数据：车间名称、编码、金额汇总
    @ApiOperation(value = "车间生产产值汇总表:返回车间名称name、车间编码code、未税金额value,含税金额valueb (统计出入库单类型为生产入库的,根据出入库item关联的machid查询销售订单单价相乘即为金额,不传时间范围默认统计近30天)", notes = "金额value、数量valueb、记录数valuec", produces = "application/json")
    @RequestMapping(value = "/getWorkshopProdValueByDay", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<List<Map<String, Object>>> getWorkshopProductionValue(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null) {  //未定义时间 默认时间范围: 一个月前到现在
                queryParam.setDateRange(new DateRange("Mat_Inventory.CreateDate", DateUtils.addMonths(new Date(), -1), new Date()));
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //构建传入的日期范围内的数据，如传入 "StartDate": "2023-03-01 00:00:00","EndDate": "2023-03-31 23:59:59"
            //则构建一个从2023-03-01到2023-03-31的数据，31行HrPwrecgroupPojo空对象，Billdate从3.1到3.31
            Date startDate = queryParam.getDateRange().getStartDate();
            Date endDate = queryParam.getDateRange().getEndDate();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            // 使用 SimpleDateFormat 格式化日期，以便进行日期比较
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            // 0.创建一个List用于收集所有车间的数据
//            List<List<Map<String, Object>>> allWorkshopData = new ArrayList<>();
            List<Map<String, Object>> finalData = new ArrayList<>();
            // 1. 先查出所有车间 (循环车间构建30天的数据)
            List<Map<String, String>> allWorkshop = d04MBIR1Mapper.getAllWorkshop(loginUser.getTenantid());//车间id,GroupUid,GroupName
            for (Map<String, String> workshop : allWorkshop) {
                Map<String, Object> oneWorkshopMap = new HashMap<>();
                oneWorkshopMap.put("name", workshop.get("GroupName"));//车间信息
                oneWorkshopMap.put("code", workshop.get("GroupUid"));//车间编码
                // 2.构建传入的日期范围内的数据list30Day
                calendar.setTime(startDate);
                List<Map<String, Object>> list30Day = new ArrayList<>();
                while (!calendar.getTime().after(endDate)) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("date", dateFormat.format(calendar.getTime()));//每天日期
                    map.put("value", 0);// 未税金额
                    map.put("valueb", 0);// 含税金额
                    list30Day.add(map);
                    calendar.add(Calendar.DATE, 1);
                }
                oneWorkshopMap.put("data", list30Day);//车间信息(30天的数据List)

                // DB查出每天车间生产产值汇总表                              getWorkshopProdValueByDay: 返回date, value, valueb
                List<Map<String, Object>> mapsMachQty = d04MBIR1Service.getWorkshopProdValueByDay(startDate, endDate, workshop.get("id"), loginUser.getTenantid());
                // 3.循环30天的数据，如果DB查出的数据中有这一天的数据，则将DB查出的数据赋值给这一天的数据
                for (Map<String, Object> map : list30Day) {
                    for (Map<String, Object> mapsMachQtyMap : mapsMachQty) {
                        if (map.get("date").equals(mapsMachQtyMap.get("date").toString())) {
                            map.put("value", mapsMachQtyMap.get("value"));// 未税金额
                            map.put("valueb", mapsMachQtyMap.get("valueb"));// 含税金额
                        }
                    }
                }
                finalData.add(oneWorkshopMap);
            }
            return R.ok(finalData);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "车间生产产值汇总表(统计2023-01到2023-12十二个月):返回车间名称name、车间编码code、未税金额value,含税金额valueb (统计出入库单类型为生产入库的,根据出入库item关联的machid查询销售订单单价相乘即为金额)", notes = "金额value、数量valueb、记录数valuec", produces = "application/json")
    @RequestMapping(value = "/getWorkshopProdValueByMonth", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Mat_Inventory.List")
    public R<List<Map<String, Object>>> getWorkshopProdValueByMonth(@RequestBody String json) {
        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            if (queryParam.getDateRange() == null) {  //未定义时间 默认时间范围: 一个月前到现在
//                queryParam.setDateRange(new DateRange("Mat_Inventory.CreateDate", DateUtils.addMonths(new Date(), -1), new Date()));
//            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            Date currentDate = new Date();  // 获取当前日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentDate);
            int currentYear = calendar.get(Calendar.YEAR);  // 获取当前年份
            // 使用 SimpleDateFormat 格式化日期，以便进行日期比较
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
            // 0.创建一个List用于收集所有车间的数据
            List<Map<String, Object>> finalData = new ArrayList<>();
            // 1. 先查出所有车间 (循环车间构建12个月的数据)
            List<Map<String, String>> allWorkshop = d04MBIR1Mapper.getAllWorkshop(loginUser.getTenantid());//车间id,GroupUid,GroupName
            for (Map<String, String> workshop : allWorkshop) {
                Map<String, Object> oneWorkshopMap = new HashMap<>();
                oneWorkshopMap.put("name", workshop.get("GroupName"));//车间信息
                oneWorkshopMap.put("code", workshop.get("GroupUid"));//车间编码
                // 2.构建传入的日期范围内的数据list30Day
                List<Map<String, Object>> list12Month = new ArrayList<>();
                for (int month = 1; month <= 12; month++) {
                    calendar.set(Calendar.YEAR, currentYear);
                    calendar.set(Calendar.MONTH, month - 1);  // 月份从0开始，所以需要减1
                    calendar.set(Calendar.DATE, 1);  // 设为每月的1号
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("date", dateFormat.format(calendar.getTime()));//每天日期
                    map.put("value", 0);// 未税金额
                    map.put("valueb", 0);// 含税金额
                    list12Month.add(map);
                }
                oneWorkshopMap.put("data", list12Month);//车间信息(12个月的数据List)

                // DB查出每天车间生产产值汇总表                              getWorkshopProdValueByMonth: 返回date, value, valueb
                List<Map<String, Object>> mapsMachQty = d04MBIR1Service.getWorkshopProdValueByMonth(workshop.get("id"), loginUser.getTenantid());
                // 3.循环12个月的数据，如果DB查出的数据中有这一天的数据，则将DB查出的数据赋值给这一天的数据
                for (Map<String, Object> map : list12Month) {
                    for (Map<String, Object> mapsMachQtyMap : mapsMachQty) {
                        if (map.get("date").equals(mapsMachQtyMap.get("date").toString())) {
                            map.put("value", mapsMachQtyMap.get("value"));// 未税金额 出现0E-8是因为SQL查询COALESCE(null,0) null值转换为0E-8
                            map.put("valueb", mapsMachQtyMap.get("valueb"));// 含税金额
                        }
                    }
                }
                finalData.add(oneWorkshopMap);
            }
            return R.ok(finalData);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "统计车间年度每个月产值 入参：2024", notes = "", produces = "application/json")
    @RequestMapping(value = "/getWorkshopProdValueIn12Month", method = RequestMethod.GET)
    public R<List<Map<String, Object>>> getWorkshopProdValueIn12Month(Integer year) {
        try {
            // year不传默认当前年
            if (year == null) {
                Calendar calendar = Calendar.getInstance();
                year = calendar.get(Calendar.YEAR);  // 获取当前年份
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.d04MBIR1Mapper.getWorkshopProdValueIn12Month(year, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "统计车间年度每个月【领料单明细的finishcost成本金额】 入参：2024", notes = "", produces = "application/json")
    @RequestMapping(value = "/getWorkshopReqFinishCostIn12Month", method = RequestMethod.GET)
    public R<List<Map<String, Object>>> getWorkshopReqFinishCostIn12Month(Integer year) {
        try {
            // year不传默认当前年
            if (year == null) {
                Calendar calendar = Calendar.getInstance();
                year = calendar.get(Calendar.YEAR);  // 获取当前年份
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.d04MBIR1Mapper.getWorkshopReqFinishCostIn12Month(year, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "统计车间年度每个月【其他收入、费用支出、相加】 入参：2024", notes = "", produces = "application/json")
    @RequestMapping(value = "/getWorkshopFmIn12Month", method = RequestMethod.GET)
    public R<List<Map<String, Object>>> getWorkshopFmIn12Month(Integer year) {
        try {
            // year不传默认当前年
            if (year == null) {
                Calendar calendar = Calendar.getInstance();
                year = calendar.get(Calendar.YEAR);  // 获取当前年份
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.d04MBIR1Mapper.getWorkshopFmIn12Month(year, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
