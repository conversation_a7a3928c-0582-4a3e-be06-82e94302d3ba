package inks.service.sa.som.controller; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/3
 * @param 销售大屏
 */

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.pojo.AppWorkgroupPojo;
import inks.service.sa.som.mapper.D01MBIR1Mapper;
import inks.service.sa.som.service.AppWorkgroupService;
import inks.service.sa.som.service.D01MBIR1Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

import static org.apache.commons.lang3.StringUtils.*;

@RestController
@RequestMapping("D01MBIR1")
@Api(tags = "D01MBIR1:销售大屏报表")
public class D01MBIR1Controller {
    @Resource
    private D01MBIR1Service d01MBIR1Service;
    @Resource
    private D01MBIR1Mapper d01MBIR1Mapper;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    private AppWorkgroupService appWorkgroupService;

    //构建今年12个月的字符串List 格式："2023-01"，"2023-02"，"2023-03"，"2023-04"，"2023-05"，"2023-06"，"2023-07"，"2023-08"，"2023-09"，"2023-10"，"2023-11"，"2023-12"
    private static List<String> getThisYearMonths() {
        List<String> monthList = new ArrayList<>();

        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);

        // 构建12个月的字符串列表
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        for (int i = 0; i < 12; i++) {
            calendar.set(Calendar.MONTH, i);
            calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为每月的第一天
            String monthString = dateFormat.format(calendar.getTime());
            monthList.add(monthString);
        }

        return monthList;
    }

    /**
     * 通过主键查询单条数据
     *
     * @param json 主键
     * @return 单条数据
     */
    @ApiOperation(value = "获取客户发货单额排名", notes = "获取客户发货单额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupMax", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByGroupMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.d01MBIR1Service.getSumAmtByGroupMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取客户销售单额排名", notes = "获取客户销售额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupMaxMach", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByGroupMaxMach(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.d01MBIR1Service.getSumAmtByGroupMaxMach(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "(GroupUid首字母分组)获取客户发货单额排名", notes = "获取客户发货单额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupMaxFirstUid", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByGroupMaxFirstUid(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.d01MBIR1Mapper.getSumAmtByGroupMaxFirstUid(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "(GroupUid首字母分组)获取客户销售单额排名", notes = "获取客户销售额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupMaxMachFirstUid", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByGroupMaxMachFirstUid(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.d01MBIR1Mapper.getSumAmtByGroupMaxMachFirstUid(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "(输入一个GroupUid首字母)获取客户发货单额排名(最近6个月每月的数据)firstuid:GroupUid首字母", notes = "获取客户发货单额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupMaxFirstUid6Month", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByGroupMaxFirstUid6Month(@RequestBody String json, String firstuid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.d01MBIR1Mapper.getSumAmtByGroupMaxFirstUid6Month(queryParam, firstuid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "(输入一个GroupUid首字母)获取客户销售单额排名(最近6个月每月的数据)firstuid:GroupUid首字母", notes = "获取客户销售额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupMaxMachFirstUid6Month", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByGroupMaxMachFirstUid6Month(@RequestBody String json, String firstuid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.d01MBIR1Mapper.getSumAmtByGroupMaxMachFirstUid6Month(queryParam, firstuid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    //租户首次注册后，权限code、角色、打印模板，数据从默认同步到tid，          前端做个mrp一样的进度窗口；
    @ApiOperation(value = "获取各个业务员发货单额排名(拼接今年12个月每月的数据) Integer recentMonths统计最近几个月的默认recentMonths=12", notes = "获取客户销售额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtBySalesman12Month", method = RequestMethod.POST)
    public R<List<ChartPojo>> getSumAmtBySalesman12Month(@RequestBody String json, Integer recentMonths) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            // 获取各个客户发货单额排名(最近12个月每月的数据)
            List<ChartPojo> lst12MonthDB = this.d01MBIR1Mapper.getSumAmtBySalesman12Month(queryParam, recentMonths == null ? 12 : recentMonths);

            // 处理查询结果，确保每个月的所有的销售人员都包含在结果中，不存在的销售人员的value以0值填充
            List<String> allSalesmenName = d01MBIR1Mapper.getAllSalesmenNameDeli(queryParam.getTenantid()); // 获取所有销售人员的列表
            lst12MonthDB = fillMissingSalesmenByMonth(lst12MonthDB, allSalesmenName);
            return R.ok(lst12MonthDB);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取各个业务员销售单额排名(拼接今年近12个月每月的数据) Integer recentMonths统计最近几个月的默认recentMonths=12", notes = "获取客户销售额排名", produces = "application/json")
    @RequestMapping(value = "/getSumAmtBySalesmanMach12Month", method = RequestMethod.POST)
    public R<List<ChartPojo>> getSumAmtBySalesmanMach12Month(@RequestBody String json, Integer recentMonths) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);

            // 获取各个客户销售单额排名(最近12个月每月的数据)
            List<ChartPojo> lst12MonthDB = this.d01MBIR1Mapper.getSumAmtBySalesmanMach12Month(queryParam, recentMonths == null ? 12 : recentMonths);

            // 处理查询结果，确保每个月的所有的销售人员都包含在结果中，不存在的销售人员的value以0值填充
            List<String> allSalesmenName = d01MBIR1Mapper.getAllSalesmenNameMach(queryParam.getTenantid()); // 获取所有销售人员的列表
            lst12MonthDB = fillMissingSalesmenByMonth(lst12MonthDB, allSalesmenName);
            return R.ok(lst12MonthDB);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // 处理查询结果，遍历每个月,确保每个月的销售人员都包含在结果中，不存在的销售人员以0值填充
    private List<ChartPojo> fillMissingSalesmenByMonth(List<ChartPojo> result, List<String> specifiedSalesmen) {
        List<ChartPojo> filledResult = new ArrayList<>();
        // 获取所有月份的列表
        List<String> thisYearMonths = getThisYearMonths();
//        Set<String> allMonths = new HashSet<>();
//        for (ChartPojo item : result) {
//            allMonths.add(item.getName());
//        }
        // 遍历每个月，确保每个月都返回指定的销售人员的数据，不存在的销售人员以0值填充
        for (String month : thisYearMonths) {
            for (String salesman : specifiedSalesmen) {
                boolean found = false;
                for (ChartPojo item : result) {
                    if (item.getName().equals(month) && item.getCode().equals(salesman)) {
                        filledResult.add(item);
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    // 不存在的销售人员以0值填充
                    ChartPojo emptyItem = new ChartPojo();
                    emptyItem.setName(month); //"2023-12"
                    emptyItem.setCode(salesman); //"业务员a"
                    emptyItem.setValue(0.0);
                    emptyItem.setValueb(0.0);
                    emptyItem.setValuec(0.0);
                    filledResult.add(emptyItem);
                }
            }
        }
        return filledResult;
    }

    @ApiOperation(value = "按货品编码前两位(C1,C2)分组统计销售订单含税金value，未税金额valueb,标准金额valuec", notes = "", produces = "application/json")
    @RequestMapping(value = "/getSumMachAmtGroupByGoodsCode", method = RequestMethod.POST)
    public R<List<ChartPojo>> getSumMachAmtGroupByGoodsCode(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.d01MBIR1Mapper.getSumMachAmtGroupByGoodsUid(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "查询每个客户的订单数量，订单金额，收款金额，结余，发货数量，发货金额", notes = "按订单金额降序", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGroupMachAndDeliAndReceipt", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<Map<String, Object>>> getSumAmtByGroupMachAndDeliAndReceipt(@RequestBody String json, String groupname) {
        try {

            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            //            如果json不包含PageSize,则给PageSize赋值10000
//            if (!json.contains("PageSize")) {
//                queryParam.setPageSize(10000);
//            }
//            直接给PageSize赋值10000(不要分页)
            queryParam.setPageSize(10000);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getSumAmtByGroupMachAndDeliAndReceipt(queryParam, groupname));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * @return R<List < ChartPojo>>
     * @Description 获取发货单和销售订单货品金额排名, 返回的value是发货单货品金额，valueb是销售订单货品金额
     * <AUTHOR>
     * @param[1] json
     * @time 2023/7/4 13:37
     */
    @ApiOperation(value = " 获取发货单和销售订单货品金额排名(返回value是发货单货品金额，valueb是销售订单货品金额,name货品名,code货品编码)", notes = "value是发货单货品金额，valueb是销售订单货品金额", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGoodsMax", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByGoodsMax(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getSumAmtByGoodsMax(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取单独的销售订单货品金额排名", notes = "", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByGoodsMaxMach", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByGoodsMaxMach(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getSumAmtByGoodsMaxMach(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description
     * @date 2021/12/30
     * @param * @param null
     * @return 业务员订单金额占比
     */
    @ApiOperation(value = "业务员发货单金额占比", notes = "业务员订单金额占比", produces = "application/json")
    @RequestMapping(value = "/getSumAmtBySalesman", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtBySalesman(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getSumAmtBySalesman(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "业务员销售订单金额占比", notes = "业务员订单金额占比", produces = "application/json")
    @RequestMapping(value = "/getSumAmtBySalesmanMach", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtBySalesmanMach(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getSumAmtBySalesmanMach(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图年
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "销售趋势图年", notes = "销售趋势图年", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByYear", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByYear(@RequestBody String json, Integer trend) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getSumAmtByYear(queryParam, trend));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图月
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "发货趋势图月", notes = "发货趋势图月", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByMonth", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByMonth(@RequestBody String json, Integer trend) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setPageNum(1);
            queryParam.setPageSize(10000);
            return R.ok(this.d01MBIR1Service.getSumAmtByMonth(queryParam, trend));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "销售趋势图月", notes = "销售趋势图月", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByMonthMach", method = RequestMethod.POST)
    public R<List<ChartPojo>> getSumAmtByMonthMach(@RequestBody String json, Integer trend) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setPageNum(1);
            queryParam.setPageSize(10000);
            return R.ok(this.d01MBIR1Service.getSumAmtByMonthMach(queryParam, trend));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /*
     *
     * <AUTHOR>
     * @description 销售趋势图周
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "销售趋势图周", notes = "销售趋势图周", produces = "application/json")
    @RequestMapping(value = "/getSumAmtByDay", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Bus_Machining.List")
    public R<List<ChartPojo>> getSumAmtByDay(@RequestBody String json, Integer trend) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getSumAmtByDay(queryParam, trend));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /*
     *
     * <AUTHOR>
     * @description 本月销售额
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "标签：根据日期范围汇总发货单额、货品数量、记录数", notes = "发货单额(value)、货品数量(valueb)、明细数(valuec)", produces = "application/json")
    @RequestMapping(value = "/getTagSumAmtQtyByDate", method = RequestMethod.POST)
    public R<ChartPojo> getTagSumAmtQtyByDate(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getTagSumAmtQtyByDate(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "标签：根据日期范围汇总销售单额、货品数量、记录数", notes = "销售额(value)、货品数量(valueb)、明细数(valuec)", produces = "application/json")
    @RequestMapping(value = "/getTagSumAmtQtyByDateMach", method = RequestMethod.POST)
    public R<ChartPojo> getTagSumAmtQtyByDateMach(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getTagSumAmtQtyByDateMach(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 本月销售额
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    @ApiOperation(value = "标签：当前客户总数", notes = "当前客户总数", produces = "application/json")
    @RequestMapping(value = "/getTagGroupCount", method = RequestMethod.GET)
    public R<ChartPojo> getTagGroupCount() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            QueryParam queryParam = new QueryParam();
            queryParam.setPageNum(1);
            queryParam.setPageSize(1);
            queryParam.setOrderBy("CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            queryParam.setFilterstr(" and GroupType='客户'");
            PageInfo<AppWorkgroupPojo> lst = this.appWorkgroupService.getPageList(queryParam);
            ChartPojo chartPojo = new ChartPojo();
            chartPojo.setName("客户总数");
            chartPojo.setValue(lst.getTotal() + 0.0);
            return R.ok(chartPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * @return
     * @author: nanno
     * @description: 订单完成率 返回总订单数、完成订单数
     * @createTime: 2023/3/8 16:31
     * @params * @Param: null
     */
    @ApiOperation(value = "订单完成率", notes = "订单完成率", produces = "application/json")
    @RequestMapping(value = "/getMachFinishRate", method = RequestMethod.POST)
    public R<ChartPojo> getMachFinishRate(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getMachFinishRate(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return
     * @author: nanno
     * @description: 发货完成率 返回总发货单数、完成数
     * @createTime: 2023/3/8 16:31
     * @params * @Param: null
     */
    @ApiOperation(value = "发货完成率", notes = "发货完成率", produces = "application/json")
    @RequestMapping(value = "/getDeliFinishRate", method = RequestMethod.POST)
    public R<ChartPojo> getDeliFinishRate(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getDeliFinishRate(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return
     * @author: nanno
     * @description: 收款完成率 返回总发货单数、完成数
     * @createTime: 2023/3/8 16:31
     * @params * @Param: null
     */
    @ApiOperation(value = "收款完成率", notes = "收款完成率", produces = "application/json")
    @RequestMapping(value = "/getReceiptFinishRate", method = RequestMethod.POST)
    public R<ChartPojo> getReceiptFinishRate(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getReceiptFinishRate(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "group By国家:客户分布", notes = "group By国家:客户分布", produces = "application/json")
    @RequestMapping(value = "/getGroupDistribution", method = RequestMethod.GET)
    public R<List<ChartPojo>> getGroupDistribution() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.d01MBIR1Service.getGroupDistribution(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    // 按客户分组,统计订单AttributeJson字段里面的总重量spuzongzhongliang,退铜屑重量sputuitongxie,退夹头重量sputuijiatou
//    SUM(IF(spu.spukey = 'spuzongzhongliang', spu.spuvalue, 0)) AS 总重量,--value
//    SUM(IF(spu.spukey = 'sputuitongxie', spu.spuvalue, 0)) AS 退铜屑重量, --valueb
//    SUM(IF(spu.spukey = 'sputuijiatou', spu.spuvalue, 0)) AS 退夹头重量   --valuec
    @ApiOperation(value = "统计订单AttributeJson字段: 按客户分组,返回name客户名,code客户编码,value总重量(spuzongzhongliang),valueb退铜屑重量(sputuitongxiezongzhong),valuec退夹头重量(sputuijiatouzongzhong)", notes = "", produces = "application/json")
    @RequestMapping(value = "/getSpuWeightGroupByGroup", method = RequestMethod.POST)
    public R<PageInfo<ChartPojo>> getSpuWeightGroupByGroup(@RequestBody String json, String groupid) {
        try {
            // 获得用户数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            if (isNotBlank(groupid)) {
                qpfilter = "And App_Workgroup.id='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.d01MBIR1Service.getSpuWeightGroupByGroup(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "统计订单AttributeJson字段: 按材质(AttributeJson.spucaizhi)分组,统计总重量spuzongzhongliang", notes = "", produces = "application/json")
    @RequestMapping(value = "/getSpuWeightGroupByCaiZhi", method = RequestMethod.POST)
    public R<List<ChartPojo>> getSpuWeightGroupByCaiZhi(@RequestBody String json) {
        try {
            // 获得用户数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getSpuWeightGroupByCaiZhi(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "统计订单AttributeJson字段: 按铸造工艺(AttributeJson.spuzhuzaogongyi)分组,统计总重量spuzongzhongliang", notes = "", produces = "application/json")
    @RequestMapping(value = "/getSpuWeightGroupByGongYi", method = RequestMethod.POST)
    public R<List<ChartPojo>> getSpuWeightGroupByGongYi(@RequestBody String json) {
        try {
            // 获得用户数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getSpuWeightGroupByGongYi(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "统计订单AttributeJson字段: 按6个规格+goodsid为一个整体(AttributeJson.'spuff', 'spufd', 'spufdn', 'spul', 'spuh', 'spub')分组,统计总重量spuzongzhongliang", notes = "", produces = "application/json")
    @RequestMapping(value = "/getSpuWeightGroupByGuiGe", method = RequestMethod.POST)
    public R<PageInfo<Map<String, Object>>> getSpuWeightGroupByGuiGe(@RequestBody String json) {
        try {
            // 获得用户数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.d01MBIR1Service.getSpuWeightGroupByGuiGe(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    @ApiOperation(value = "收款完成率", notes = "收款完成率", produces = "application/json")
//    @RequestMapping(value = "/getReceiptFinishRate", method = RequestMethod.POST)
//    public R<ChartPojo> getReceiptFinishRate(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d01MBIR1Service.getReceiptFinishRate(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }


//    /*
//     *
//     * <AUTHOR>
//     * @description 本月销售额
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @ApiOperation(value = "本月销售额", notes = "本月销售额", produces = "application/json")
//    @RequestMapping(value = "/getTagSumAmtQtyByMonth", method = RequestMethod.POST)
////    @PreAuthorize(hasPermi = "Bus_Machining.List")
//    public R<ChartPojo> getTagSumAmtQtyByMonth(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d01MBIR1Service.getTagSumAmtQtyByMonth(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 销售饼状图年
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @ApiOperation(value = "销售饼状图年", notes = "销售饼状图年", produces = "application/json")
//    @RequestMapping(value = "/getSumAmtByYearMax", method = RequestMethod.POST)
////    @PreAuthorize(hasPermi = "Bus_Machining.List")
//    public R<List<ChartPojo>> getSumAmtByYearMax(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d01MBIR1Service.getSumAmtByYearMax(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 销售饼状图月
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @ApiOperation(value = "销售饼状图月", notes = "销售饼状图月", produces = "application/json")
//    @RequestMapping(value = "/getSumAmtByMonthMax", method = RequestMethod.POST)
////    @PreAuthorize(hasPermi = "Bus_Machining.List")
//    public R<List<ChartPojo>> getSumAmtByMonthMax(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d01MBIR1Service.getSumAmtByMonthMax(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 销售饼状图周
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @ApiOperation(value = "销售饼状图周", notes = "销售饼状图周", produces = "application/json")
//    @RequestMapping(value = "/getSumAmtByDayMax", method = RequestMethod.POST)
////    @PreAuthorize(hasPermi = "Bus_Machining.List")
//    public R<List<ChartPojo>> getSumAmtByDayMax(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d01MBIR1Service.getSumAmtByDayMax(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "根据当前月查询本月开票", notes = "根据当前月查询本月开票", produces = "application/json")
//    @RequestMapping(value = "/getTagSumAmtByMonth", method = RequestMethod.GET)
////    @PreAuthorize(hasPermi = "Bus_Invoice.List")
//    public R<List<ChartPojo>> getTagSumAmtByMonth() {
//        try {
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            return R.ok(this.d01MBIR1Service.getTagSumAmtByMonth(loginUser.getTenantid()));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 订单逾期
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @ApiOperation(value = "订单逾期", notes = "订单逾期", produces = "application/json")
//    @RequestMapping(value = "/getPageList", method = RequestMethod.GET)
////    @PreAuthorize(hasPermi = "Bus_Machining.List")
//    public R<ChartPojo> getPageList() {
//        try {
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            return R.ok(this.d01MBIR1Service.getPageList(loginUser.getTenantid()));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 热销产品
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @ApiOperation(value = "热销产品", notes = "热销产品", produces = "application/json")
//    @RequestMapping(value = "/getSumByGoodsMax", method = RequestMethod.POST)
////    @PreAuthorize(hasPermi = "Bus_Machining.List")
//    public R<List<ChartPojo>> getSumByGoodsMax(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            return R.ok(this.d01MBIR1Service.getSumByGoodsMax(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }


//    /*
//     *
//     * <AUTHOR>
//     * @description 查询在线订单数
//     * @date 2021/12/30
//     * @param * @param null
//     * @return
//     */
//    @ApiOperation(value = "查询在线订单数", notes = "查询在线订单数", produces = "application/json")
//    @RequestMapping(value = "/getListSize", method = RequestMethod.GET)
//    public R<Integer> getCountMachItemOnline() {
//        try {
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            return R.ok(this.d01MBIR1Service.getCountMachItemOnline(loginUser.getTenantid()));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
}
