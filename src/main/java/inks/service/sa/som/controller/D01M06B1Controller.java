package inks.service.sa.som.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.AmountUtils;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.som.domain.pojo.BusDelieryPojo;
import inks.service.sa.som.domain.pojo.BusDelieryitemPojo;
import inks.service.sa.som.domain.pojo.BusDelieryitemdetailPojo;
import inks.service.sa.som.mapper.BusDelieryitemMapper;
import inks.service.sa.som.service.BusDelieryService;
import inks.service.sa.som.service.BusDelieryitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 发出商品(BusDeliery)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-13 14:07:43
 */
@RestController
@RequestMapping("D01M06B1")
@Api(tags = "D01M06B1:订单发货")
public class D01M06B1Controller extends BusDelieryController {

    @Resource
    private BusDelieryService busDelieryService;

    @Resource
    private BusDelieryitemService busDelieryitemService;
    @Resource
    private BusDelieryitemMapper busDelieryitemMapper;

    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaBillcodeService saBillcodeService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getPageList(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_Deliery.BillType IN ('发出商品','订单退货')";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryitemdetailPojo>> getOnlinePageList(@RequestBody String json, String groupid,@RequestParam(defaultValue = "1") Integer type) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Bus_Deliery.BillType IN ('发出商品','订单退货')";
            qpfilter += " and Bus_DelieryItem.FinishQty<Bus_DelieryItem.Quantity+Bus_DelieryItem.FreeQty";
            qpfilter += " and Bus_DelieryItem.DisannulMark=0 and Bus_DelieryItem.FinishClosed=0 and Bus_DelieryItem.VirtualItem=0";  // 未关闭、未注销且不是虚拟品
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            PageInfo<BusDelieryitemdetailPojo> pageList = this.busDelieryService.getPageList(queryParam);

            // 如果type=0,则保证金额，数量均返回正数
            if (type == 0) {
            List<BusDelieryitemdetailPojo> list = pageList.getList();
                for (BusDelieryitemdetailPojo item : list) {
                    if (item.getQuantity() != null) {
                        item.setQuantity(Math.abs(item.getQuantity()));
                    }
                    if (item.getAmount() != null) {
                        item.setAmount(Math.abs(item.getAmount()));
                    }
                    if (item.getTaxamount() != null) {
                        item.setTaxamount(Math.abs(item.getTaxamount()));
                    }
                    if (item.getTaxtotal() != null) {
                        item.setTaxtotal(Math.abs(item.getTaxtotal()));
                    }
                }
                pageList.setList(list);
            }

            return R.ok(pageList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryPojo>> getPageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Deliery.BillType IN ('发出商品','订单退货')";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.List")
    public R<PageInfo<BusDelieryPojo>> getOnlinePageTh(@RequestBody String json, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Bus_Deliery.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = " and Bus_Deliery.BillType IN ('发出商品','订单退货')";
            qpfilter += " and Bus_Deliery.FinishCount<Bus_Deliery.ItemCount";
            if (groupid != null) {
                qpfilter += " and Bus_Deliery.Groupid='" + groupid + "'";
            }
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.busDelieryService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增发出商品", notes = "新增发出商品", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Deliery.Add")
    public R<BusDelieryPojo> create(@RequestBody String json) {
        try {
            BusDelieryPojo busDelieryPojo = JSONArray.parseObject(json, BusDelieryPojo.class);
            // item转json,校验各项金额相差不能大于1
            String lessThanOne = AmountUtils.lstLessThanOne(JSONObject.toJSONString(busDelieryPojo.getItem()));
            PrintColor.red("=========lessThanOne校验各项金额相差不能大于1:" + lessThanOne);
            if (lessThanOne != null) return R.fail(lessThanOne);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("D01M06B1", loginUser.getTenantid(),"Bus_Deliery");
            busDelieryPojo.setRefno(refNo);
            busDelieryPojo.setCreateby(loginUser.getRealname());   // 创建者
            busDelieryPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            busDelieryPojo.setCreatedate(new Date());   // 创建时间
            busDelieryPojo.setLister(loginUser.getRealname());   // 制表
            busDelieryPojo.setListerid(loginUser.getUserid());    // 制表id
            busDelieryPojo.setModifydate(new Date());   //修改时间
            busDelieryPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.busDelieryService.insert(busDelieryPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除发出商品", notes = "删除发出商品", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "Bus_Deliery.Delete")
    //@OperLog(title = "删除发出商品")
    public R<BusDelieryPojo> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            //检查引用
            List<BusDelieryitemPojo> lst = this.busDelieryitemService.getList(key, loginUser.getTenantid());
            for (BusDelieryitemPojo item : lst) {
                List<String> lstcite = this.busDelieryService.getItemCiteBillName(item.getId(), item.getPid(), loginUser.getTenantid());
                if (lstcite.size() > 0) {
                    return R.fail( "禁止删除,被以下单据引用:" + lstcite);
                }
            }
            BusDelieryPojo busDelieryPojo = busDelieryService.getBillEntity(key, tid);
            this.busDelieryService.delete(key, loginUser.getTenantid());
            return R.ok(busDelieryPojo, busDelieryPojo.getRefno() + "删除成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    // 用来一次性刷新租户下所有发货单子表的AttributeStr
//    @RequestMapping(value = "/updateAttrStr", method = RequestMethod.GET)
//    public int updateAttrStr() {
////        String gongShi="${spuchang}*${spukuan}*${spuhou}*${spuwaijing}*${spunajing}*${spugaodu}";
//        String gongShi="${spuff}*${spufd}*${spufdn}*${spul}*${spuh}*${spub}";//立一
////        String tid="ceb5ae04-ea5f-4ba8-81af-0c8ec1ad112c";
//        String tid="b2db6f59-659a-48c6-95be-85a20f0032e2"; //立一
////        String tid="b842c7ca-a02b-4dc6-af43-e4d3e84af592";
////        String tid="12138";
//        // id,AttributeJson
//        List<Map<String,String>> lst = this.busDelieryitemMapper.getAllByTid(tid);
//        int count=0;
//        for (Map<String, String> map : lst) {
//            String id = map.get("id");
//            String attributejson = map.get("AttributeJson");
//            System.out.println("id = " + id);
//            System.out.println("attributejson = " + attributejson);
//
//            if (StringUtils.isNotBlank(attributejson)) {
//                String attrStr = BeanUtils.calculateAttrStr(attributejson, gongShi);
//                int i = this.busDelieryitemMapper.upateAttrStr(id, attrStr, tid);
//                count+=i;
//            }
//        }
//        System.out.println("共刷新: " + count);
//        return count;
//    }


}

