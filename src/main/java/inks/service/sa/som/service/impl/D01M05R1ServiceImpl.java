package inks.service.sa.som.service.impl;

import inks.common.core.domain.ChartPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.mapper.D01M05R1Mapper;
import inks.service.sa.som.service.D01M05R1Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
@Service
public class D01M05R1ServiceImpl implements D01M05R1Service {
    @Resource
    private D01M05R1Mapper d01M05R1Mapper;
     /*
      *
      * <AUTHOR>
      * @description 根据当前月查询本月开票
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    @Override
    public List<ChartPojo> getTagSumAmtByMonth(String tid) {
        try {
            return d01M05R1Mapper.getTagSumAmtByMonth(tid);
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
