package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyVouchercashPojo;

import java.util.List;
/**
 * 现金项目(BuyVouchercash)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-11 15:13:07
 */
public interface BuyVouchercashService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyVouchercashPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyVouchercashPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyVouchercashPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param buyVouchercashPojo 实例对象
     * @return 实例对象
     */
    BuyVouchercashPojo insert(BuyVouchercashPojo buyVouchercashPojo);

    /**
     * 修改数据
     *
     * @param buyVouchercashpojo 实例对象
     * @return 实例对象
     */
    BuyVouchercashPojo update(BuyVouchercashPojo buyVouchercashpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param buyVouchercashpojo 实例对象
     * @return 实例对象
     */
    BuyVouchercashPojo clearNull(BuyVouchercashPojo buyVouchercashpojo);
}
