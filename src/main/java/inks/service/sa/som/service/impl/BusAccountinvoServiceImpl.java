package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BusAccountinvoEntity;
import inks.service.sa.som.domain.pojo.BusAccountinvoPojo;
import inks.service.sa.som.mapper.BusAccountinvoMapper;
import inks.service.sa.som.service.BusAccountinvoService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 销售单to发票(BusAccountinvo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-16 11:27:03
 */
@Service("busAccountinvoService")
public class BusAccountinvoServiceImpl implements BusAccountinvoService {
    @Resource
    private BusAccountinvoMapper busAccountinvoMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusAccountinvoPojo getEntity(String key,String tid) {
        return this.busAccountinvoMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusAccountinvoPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountinvoPojo> lst = busAccountinvoMapper.getPageList(queryParam);
            PageInfo<BusAccountinvoPojo> pageInfo = new PageInfo<BusAccountinvoPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusAccountinvoPojo> getList(String Pid,String tid) { 
        try {
            List<BusAccountinvoPojo> lst = busAccountinvoMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busAccountinvoPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusAccountinvoPojo insert(BusAccountinvoPojo busAccountinvoPojo) {
        //初始化item的NULL
        BusAccountinvoPojo itempojo =this.clearNull(busAccountinvoPojo);
        BusAccountinvoEntity busAccountinvoEntity = new BusAccountinvoEntity(); 
        BeanUtils.copyProperties(itempojo,busAccountinvoEntity);
          //生成雪花id
          busAccountinvoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busAccountinvoEntity.setRevision(1);  //乐观锁      
          this.busAccountinvoMapper.insert(busAccountinvoEntity);
        return this.getEntity(busAccountinvoEntity.getId(),busAccountinvoEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busAccountinvoPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusAccountinvoPojo update(BusAccountinvoPojo busAccountinvoPojo) {
        BusAccountinvoEntity busAccountinvoEntity = new BusAccountinvoEntity(); 
        BeanUtils.copyProperties(busAccountinvoPojo,busAccountinvoEntity);
        this.busAccountinvoMapper.update(busAccountinvoEntity);
        return this.getEntity(busAccountinvoEntity.getId(),busAccountinvoEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busAccountinvoMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busAccountinvoPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusAccountinvoPojo clearNull(BusAccountinvoPojo busAccountinvoPojo){
     //初始化NULL字段
     if(busAccountinvoPojo.getPid()==null) busAccountinvoPojo.setPid("");
     if(busAccountinvoPojo.getDirection()==null) busAccountinvoPojo.setDirection("");
     if(busAccountinvoPojo.getModulecode()==null) busAccountinvoPojo.setModulecode("");
     if(busAccountinvoPojo.getBilltype()==null) busAccountinvoPojo.setBilltype("");
     if(busAccountinvoPojo.getBilldate()==null) busAccountinvoPojo.setBilldate(new Date());
     if(busAccountinvoPojo.getBilltitle()==null) busAccountinvoPojo.setBilltitle("");
     if(busAccountinvoPojo.getBilluid()==null) busAccountinvoPojo.setBilluid("");
     if(busAccountinvoPojo.getBillid()==null) busAccountinvoPojo.setBillid("");
     if(busAccountinvoPojo.getOpenamount()==null) busAccountinvoPojo.setOpenamount(0D);
     if(busAccountinvoPojo.getInamount()==null) busAccountinvoPojo.setInamount(0D);
     if(busAccountinvoPojo.getOutamount()==null) busAccountinvoPojo.setOutamount(0D);
     if(busAccountinvoPojo.getCloseamount()==null) busAccountinvoPojo.setCloseamount(0D);
     if(busAccountinvoPojo.getRownum()==null) busAccountinvoPojo.setRownum(0);
     if(busAccountinvoPojo.getRemark()==null) busAccountinvoPojo.setRemark("");
     if(busAccountinvoPojo.getCustom1()==null) busAccountinvoPojo.setCustom1("");
     if(busAccountinvoPojo.getCustom2()==null) busAccountinvoPojo.setCustom2("");
     if(busAccountinvoPojo.getCustom3()==null) busAccountinvoPojo.setCustom3("");
     if(busAccountinvoPojo.getCustom4()==null) busAccountinvoPojo.setCustom4("");
     if(busAccountinvoPojo.getCustom5()==null) busAccountinvoPojo.setCustom5("");
     if(busAccountinvoPojo.getCustom6()==null) busAccountinvoPojo.setCustom6("");
     if(busAccountinvoPojo.getCustom7()==null) busAccountinvoPojo.setCustom7("");
     if(busAccountinvoPojo.getCustom8()==null) busAccountinvoPojo.setCustom8("");
     if(busAccountinvoPojo.getCustom9()==null) busAccountinvoPojo.setCustom9("");
     if(busAccountinvoPojo.getCustom10()==null) busAccountinvoPojo.setCustom10("");
     if(busAccountinvoPojo.getTenantid()==null) busAccountinvoPojo.setTenantid("");
     if(busAccountinvoPojo.getRevision()==null) busAccountinvoPojo.setRevision(0);
     return busAccountinvoPojo;
     }
}
