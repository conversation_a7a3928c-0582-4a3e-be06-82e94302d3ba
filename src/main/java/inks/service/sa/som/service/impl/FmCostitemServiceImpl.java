package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.FmCostitemEntity;
import inks.service.sa.som.domain.pojo.FmCostitemPojo;
import inks.service.sa.som.mapper.FmCostitemMapper;
import inks.service.sa.som.service.FmCostitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
/**
 * 费用明细(FmCostitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-29 15:25:30
 */
@Service("fmCostitemService")
public class FmCostitemServiceImpl implements FmCostitemService {
    @Resource
    private FmCostitemMapper fmCostitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmCostitemPojo getEntity(String key,String tid) {
        return this.fmCostitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmCostitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmCostitemPojo> lst = fmCostitemMapper.getPageList(queryParam);
            PageInfo<FmCostitemPojo> pageInfo = new PageInfo<FmCostitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<FmCostitemPojo> getList(String Pid,String tid) { 
        try {
            List<FmCostitemPojo> lst = fmCostitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param fmCostitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmCostitemPojo insert(FmCostitemPojo fmCostitemPojo) {
        //初始化item的NULL
        FmCostitemPojo itempojo =this.clearNull(fmCostitemPojo);
        FmCostitemEntity fmCostitemEntity = new FmCostitemEntity(); 
        BeanUtils.copyProperties(itempojo,fmCostitemEntity);
        
          fmCostitemEntity.setId(UUID.randomUUID().toString());
          fmCostitemEntity.setRevision(1);  //乐观锁      
          this.fmCostitemMapper.insert(fmCostitemEntity);
        return this.getEntity(fmCostitemEntity.getId(),fmCostitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param fmCostitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmCostitemPojo update(FmCostitemPojo fmCostitemPojo) {
        FmCostitemEntity fmCostitemEntity = new FmCostitemEntity(); 
        BeanUtils.copyProperties(fmCostitemPojo,fmCostitemEntity);
        this.fmCostitemMapper.update(fmCostitemEntity);
        return this.getEntity(fmCostitemEntity.getId(),fmCostitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.fmCostitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param fmCostitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public FmCostitemPojo clearNull(FmCostitemPojo fmCostitemPojo){
     //初始化NULL字段
     if(fmCostitemPojo.getPid()==null) fmCostitemPojo.setPid("");
     if(fmCostitemPojo.getCosttypeid()==null) fmCostitemPojo.setCosttypeid("");
     if(fmCostitemPojo.getItemname()==null) fmCostitemPojo.setItemname("");
     if(fmCostitemPojo.getItemdepict()==null) fmCostitemPojo.setItemdepict("");
     if(fmCostitemPojo.getAmount()==null) fmCostitemPojo.setAmount(0D);
     if(fmCostitemPojo.getRemark()==null) fmCostitemPojo.setRemark("");
     if(fmCostitemPojo.getRownum()==null) fmCostitemPojo.setRownum(0);
     if(fmCostitemPojo.getCustom1()==null) fmCostitemPojo.setCustom1("");
     if(fmCostitemPojo.getCustom2()==null) fmCostitemPojo.setCustom2("");
     if(fmCostitemPojo.getCustom3()==null) fmCostitemPojo.setCustom3("");
     if(fmCostitemPojo.getCustom4()==null) fmCostitemPojo.setCustom4("");
     if(fmCostitemPojo.getCustom5()==null) fmCostitemPojo.setCustom5("");
     if(fmCostitemPojo.getCustom6()==null) fmCostitemPojo.setCustom6("");
     if(fmCostitemPojo.getCustom7()==null) fmCostitemPojo.setCustom7("");
     if(fmCostitemPojo.getCustom8()==null) fmCostitemPojo.setCustom8("");
     if(fmCostitemPojo.getCustom9()==null) fmCostitemPojo.setCustom9("");
     if(fmCostitemPojo.getCustom10()==null) fmCostitemPojo.setCustom10("");
     if(fmCostitemPojo.getTenantid()==null) fmCostitemPojo.setTenantid("");
     if(fmCostitemPojo.getRevision()==null) fmCostitemPojo.setRevision(0);
     return fmCostitemPojo;
     }
}
