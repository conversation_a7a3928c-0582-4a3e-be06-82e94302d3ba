package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.FmCashcarryoverEntity;
import inks.service.sa.som.domain.FmCashcarryoveritemEntity;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.FmCashaccountMapper;
import inks.service.sa.som.mapper.FmCashcarryoverMapper;
import inks.service.sa.som.mapper.FmCashcarryoveritemMapper;
import inks.service.sa.som.service.FmCashcarryoverService;
import inks.service.sa.som.service.FmCashcarryoveritemService;
import inks.service.sa.som.service.FmCashcarryoverrecService;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 出纳账单(FmCashcarryover)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-09-26 19:41:23
 */
@Service("fmCashcarryoverService")
public class FmCashcarryoverServiceImpl implements FmCashcarryoverService {
    @Resource
    private FmCashcarryoverMapper fmCashcarryoverMapper;

    @Resource
    private FmCashcarryoveritemMapper fmCashcarryoveritemMapper;

    
    @Resource
    private FmCashcarryoveritemService fmCashcarryoveritemService;

    @Resource
    private FmCashaccountMapper fmCashaccountMapper;

  
    @Resource
    private FmCashcarryoverrecService fmCashcarryoverrecService;

    @Resource
    private SaRedisService saRedisService;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmCashcarryoverPojo getEntity(String key, String tid) {
        return this.fmCashcarryoverMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmCashcarryoveritemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmCashcarryoveritemdetailPojo> lst = fmCashcarryoverMapper.getPageList(queryParam);
            PageInfo<FmCashcarryoveritemdetailPojo> pageInfo = new PageInfo<FmCashcarryoveritemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmCashcarryoverPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            FmCashcarryoverPojo fmCashcarryoverPojo = this.fmCashcarryoverMapper.getEntity(key, tid);
            //读取子表
            fmCashcarryoverPojo.setItem(fmCashcarryoveritemMapper.getList(fmCashcarryoverPojo.getId(), fmCashcarryoverPojo.getTenantid()));
            return fmCashcarryoverPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmCashcarryoverPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmCashcarryoverPojo> lst = fmCashcarryoverMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(fmCashcarryoveritemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<FmCashcarryoverPojo> pageInfo = new PageInfo<FmCashcarryoverPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmCashcarryoverPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmCashcarryoverPojo> lst = fmCashcarryoverMapper.getPageTh(queryParam);
            PageInfo<FmCashcarryoverPojo> pageInfo = new PageInfo<FmCashcarryoverPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param fmCashcarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public FmCashcarryoverPojo insert(FmCashcarryoverPojo fmCashcarryoverPojo) {
//初始化NULL字段
        if (fmCashcarryoverPojo.getRefno() == null) fmCashcarryoverPojo.setRefno("");
        if (fmCashcarryoverPojo.getBilltype() == null) fmCashcarryoverPojo.setBilltype("");
        if (fmCashcarryoverPojo.getBilldate() == null) fmCashcarryoverPojo.setBilldate(new Date());
        if (fmCashcarryoverPojo.getBilltitle() == null) fmCashcarryoverPojo.setBilltitle("");
        if (fmCashcarryoverPojo.getCashaccid() == null) fmCashcarryoverPojo.setCashaccid("");
        if (fmCashcarryoverPojo.getCashaccname() == null) fmCashcarryoverPojo.setCashaccname("");
        if (fmCashcarryoverPojo.getCarryyear() == null) fmCashcarryoverPojo.setCarryyear(0);
        if (fmCashcarryoverPojo.getCarrymonth() == null) fmCashcarryoverPojo.setCarrymonth(0);
        if (fmCashcarryoverPojo.getStartdate() == null) fmCashcarryoverPojo.setStartdate(new Date());
        if (fmCashcarryoverPojo.getEnddate() == null) fmCashcarryoverPojo.setEnddate(new Date());
        if (fmCashcarryoverPojo.getOperator() == null) fmCashcarryoverPojo.setOperator("");
        if (fmCashcarryoverPojo.getOperatorid() == null) fmCashcarryoverPojo.setOperatorid("");
        if (fmCashcarryoverPojo.getRownum() == null) fmCashcarryoverPojo.setRownum(0);
        if (fmCashcarryoverPojo.getSummary() == null) fmCashcarryoverPojo.setSummary("");
        if (fmCashcarryoverPojo.getCreateby() == null) fmCashcarryoverPojo.setCreateby("");
        if (fmCashcarryoverPojo.getCreatebyid() == null) fmCashcarryoverPojo.setCreatebyid("");
        if (fmCashcarryoverPojo.getCreatedate() == null) fmCashcarryoverPojo.setCreatedate(new Date());
        if (fmCashcarryoverPojo.getLister() == null) fmCashcarryoverPojo.setLister("");
        if (fmCashcarryoverPojo.getListerid() == null) fmCashcarryoverPojo.setListerid("");
        if (fmCashcarryoverPojo.getModifydate() == null) fmCashcarryoverPojo.setModifydate(new Date());
        if (fmCashcarryoverPojo.getBillopenamount() == null) fmCashcarryoverPojo.setBillopenamount(0D);
        if (fmCashcarryoverPojo.getBillinamount() == null) fmCashcarryoverPojo.setBillinamount(0D);
        if (fmCashcarryoverPojo.getBilloutamount() == null) fmCashcarryoverPojo.setBilloutamount(0D);
        if (fmCashcarryoverPojo.getBillcloseamount() == null) fmCashcarryoverPojo.setBillcloseamount(0D);
        if (fmCashcarryoverPojo.getItemcount() == null) fmCashcarryoverPojo.setItemcount(0);
        if (fmCashcarryoverPojo.getPrintcount() == null) fmCashcarryoverPojo.setPrintcount(0);
        if (fmCashcarryoverPojo.getCustom1() == null) fmCashcarryoverPojo.setCustom1("");
        if (fmCashcarryoverPojo.getCustom2() == null) fmCashcarryoverPojo.setCustom2("");
        if (fmCashcarryoverPojo.getCustom3() == null) fmCashcarryoverPojo.setCustom3("");
        if (fmCashcarryoverPojo.getCustom4() == null) fmCashcarryoverPojo.setCustom4("");
        if (fmCashcarryoverPojo.getCustom5() == null) fmCashcarryoverPojo.setCustom5("");
        if (fmCashcarryoverPojo.getCustom6() == null) fmCashcarryoverPojo.setCustom6("");
        if (fmCashcarryoverPojo.getCustom7() == null) fmCashcarryoverPojo.setCustom7("");
        if (fmCashcarryoverPojo.getCustom8() == null) fmCashcarryoverPojo.setCustom8("");
        if (fmCashcarryoverPojo.getCustom9() == null) fmCashcarryoverPojo.setCustom9("");
        if (fmCashcarryoverPojo.getCustom10() == null) fmCashcarryoverPojo.setCustom10("");
        if (fmCashcarryoverPojo.getDeptid() == null) fmCashcarryoverPojo.setDeptid("");
        if (fmCashcarryoverPojo.getTenantid() == null) fmCashcarryoverPojo.setTenantid("");
        if (fmCashcarryoverPojo.getTenantname() == null) fmCashcarryoverPojo.setTenantname("");
        if (fmCashcarryoverPojo.getRevision() == null) fmCashcarryoverPojo.setRevision(0);
        //生成id
        String id = UUID.randomUUID().toString();
        FmCashcarryoverEntity fmCashcarryoverEntity = new FmCashcarryoverEntity();
        BeanUtils.copyProperties(fmCashcarryoverPojo, fmCashcarryoverEntity);
        //设置id和新建日期
        fmCashcarryoverEntity.setId(id);
        fmCashcarryoverEntity.setRevision(1);  //乐观锁
        //插入主表
        this.fmCashcarryoverMapper.insert(fmCashcarryoverEntity);
        //Item子表处理
        List<FmCashcarryoveritemPojo> lst = fmCashcarryoverPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                FmCashcarryoveritemPojo itemPojo = this.fmCashcarryoveritemService.clearNull(lst.get(i));
                FmCashcarryoveritemEntity fmCashcarryoveritemEntity = new FmCashcarryoveritemEntity();
                BeanUtils.copyProperties(itemPojo, fmCashcarryoveritemEntity);
                //设置id和Pid
                fmCashcarryoveritemEntity.setId(UUID.randomUUID().toString());
                fmCashcarryoveritemEntity.setPid(id);
                fmCashcarryoveritemEntity.setTenantid(fmCashcarryoverPojo.getTenantid());
                fmCashcarryoveritemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.fmCashcarryoveritemMapper.insert(fmCashcarryoveritemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(fmCashcarryoverEntity.getId(), fmCashcarryoverEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param fmCashcarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public FmCashcarryoverPojo update(FmCashcarryoverPojo fmCashcarryoverPojo) {
        //主表更改
        FmCashcarryoverEntity fmCashcarryoverEntity = new FmCashcarryoverEntity();
        BeanUtils.copyProperties(fmCashcarryoverPojo, fmCashcarryoverEntity);
        this.fmCashcarryoverMapper.update(fmCashcarryoverEntity);
        if (fmCashcarryoverPojo.getItem() != null) {
            //Item子表处理
            List<FmCashcarryoveritemPojo> lst = fmCashcarryoverPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = fmCashcarryoverMapper.getDelItemIds(fmCashcarryoverPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.fmCashcarryoveritemMapper.delete(lstDelIds.get(i), fmCashcarryoverEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    FmCashcarryoveritemEntity fmCashcarryoveritemEntity = new FmCashcarryoveritemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        FmCashcarryoveritemPojo itemPojo = this.fmCashcarryoveritemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, fmCashcarryoveritemEntity);
                        //设置id和Pid
                        fmCashcarryoveritemEntity.setId(UUID.randomUUID().toString());  // item id
                        fmCashcarryoveritemEntity.setPid(fmCashcarryoverEntity.getId());  // 主表 id
                        fmCashcarryoveritemEntity.setTenantid(fmCashcarryoverPojo.getTenantid());   // 租户id
                        fmCashcarryoveritemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.fmCashcarryoveritemMapper.insert(fmCashcarryoveritemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), fmCashcarryoveritemEntity);
                        fmCashcarryoveritemEntity.setTenantid(fmCashcarryoverPojo.getTenantid());
                        this.fmCashcarryoveritemMapper.update(fmCashcarryoveritemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(fmCashcarryoverEntity.getId(), fmCashcarryoverEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        FmCashcarryoverPojo fmCashcarryoverPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<FmCashcarryoveritemPojo> lst = fmCashcarryoverPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.fmCashcarryoveritemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.fmCashcarryoverMapper.delete(key, tid);
    }

    /**
     * 现金银行明细
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmCashcarryoveritemPojo> getBillItemList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmCashcarryoveritemPojo> lst =this.fmCashcarryoverMapper.getBillItemList(queryParam);
            PageInfo<FmCashcarryoveritemPojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }


    // 批量生成账单
    @Override
    @Transactional
    public int batchCreate(FmCashcarryoverPojo fmCashcarryoverPojo) {
        try {
            int num = 0;
            String tid = fmCashcarryoverPojo.getTenantid();
            // 获取所有账户
            List<String> lstcaIds = this.fmCashaccountMapper.getAccountIds(tid);

            // item改为BigDec
            BigDecimal decrecopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecoutAmt = BigDecimal.valueOf(0);
            BigDecimal decreccloseAmt = BigDecimal.valueOf(0);

            // 遍历计算每个客户账单
            for (String caid : lstcaIds) {
                FmCashcarryoverPojo newPojo = new FmCashcarryoverPojo();
                BeanUtils.copyProperties(fmCashcarryoverPojo, newPojo);
                newPojo.setCashaccid(caid);
                // 1.送货单to收款
                newPojo.setItem(pullItemList(newPojo));
                // 改为BigDec
                BigDecimal decopenAmt = BigDecimal.valueOf(0);
                BigDecimal decinAmt = BigDecimal.valueOf(0);
                BigDecimal decoutAmt = BigDecimal.valueOf(0);
                BigDecimal deccloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getItem().size() > 0) {
                    decopenAmt = decopenAmt.add(BigDecimal.valueOf(newPojo.getItem().get(0).getOpenamount()));
                    deccloseAmt = deccloseAmt.add(BigDecimal.valueOf(newPojo.getItem().get(newPojo.getItem().size() - 1).getCloseamount()));
                }
                for (FmCashcarryoveritemPojo itemPojo : newPojo.getItem()) {
                    decinAmt = decinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decoutAmt = decoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setBillopenamount(decopenAmt.doubleValue());
                newPojo.setBillinamount(decinAmt.doubleValue());
                newPojo.setBilloutamount(decoutAmt.doubleValue());
                newPojo.setBillcloseamount(deccloseAmt.doubleValue());

                insert(newPojo);
                // 当前客户item累加
                decrecopenAmt = decrecopenAmt.add(decopenAmt);
                decrecinAmt = decrecinAmt.add(decinAmt);
                decrecoutAmt = decrecoutAmt.add(decoutAmt);
                decreccloseAmt = decreccloseAmt.add(deccloseAmt);
                // 下一位客户
                num++;
            }
            FmCashcarryoverrecPojo fmCashcarryoverrecPojo = new FmCashcarryoverrecPojo();
            BeanUtils.copyProperties(fmCashcarryoverPojo, fmCashcarryoverrecPojo);
            // 设置item该月累加账单
            fmCashcarryoverrecPojo.setBillopenamount(decrecopenAmt.doubleValue());
            fmCashcarryoverrecPojo.setBillinamount(decrecinAmt.doubleValue());
            fmCashcarryoverrecPojo.setBilloutamount(decrecoutAmt.doubleValue());
            fmCashcarryoverrecPojo.setBillcloseamount(decreccloseAmt.doubleValue());
            this.fmCashcarryoverrecService.insert(fmCashcarryoverrecPojo);
            return num;
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }
    }

    // 批量生成账单
    @Override
    @Transactional
    public int batchInit(FmCashcarryoverPojo fmCashcarryoverPojo) {
        try {
            int num = 0;
            String tid = fmCashcarryoverPojo.getTenantid();
            // 获取该租户下所有账户号
            List<String> lstcaIds = this.fmCashaccountMapper.getAccountIds(tid);
            for (String caid : lstcaIds) {
                FmCashcarryoverPojo  dbPojo = getMaxEntityByCash(caid, tid);
                // 是否已有初始化
                if (dbPojo == null) {
                    FmCashcarryoverPojo  newPojo = new FmCashcarryoverPojo ();
                    BeanUtils.copyProperties(fmCashcarryoverPojo, newPojo);
                    newPojo.setCashaccid(caid);
                    insert(newPojo);
                    num++;
                }
            }
            return num;
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }
    }

    /**
     * 通过出纳账号id查询最新单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmCashcarryoverPojo getMaxEntityByCash(String key, String tid) {
        return this.fmCashcarryoverMapper.getMaxEntityByCash(key, tid);
    }

    /**
     * 新增数据
     *
     * @param fmCashcarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    public List<FmCashcarryoveritemPojo> pullItemList(FmCashcarryoverPojo fmCashcarryoverPojo) {

        // 查询当前客户之前的销售账单
       FmCashcarryoverPojo fmCashcarryoverMaxPojo = this.fmCashcarryoverMapper.getMaxEntityByCash(fmCashcarryoverPojo.getCashaccid(), fmCashcarryoverPojo.getTenantid());
        // 初始化时间
        Date dtStart = DateUtils.parseDate("2022-01-01");
        Double openAmount = 0D;
        if (fmCashcarryoverMaxPojo != null) {
//            throw new RuntimeException("请先初始化客户账单");
            dtStart = fmCashcarryoverMaxPojo.getEnddate();
            openAmount = fmCashcarryoverMaxPojo.getBillcloseamount();
        }
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(fmCashcarryoverPojo.getEnddate()) + " 23:59:59");

        QueryParam queryParam = new QueryParam();
        String strFilter = " and Cashaccid='" + fmCashcarryoverPojo.getCashaccid() + "'";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(2000);
        queryParam.setTenantid(fmCashcarryoverPojo.getTenantid());
        List<FmCashcarryoveritemPojo> lstitem = this.fmCashcarryoverMapper.pullItemList(queryParam);
        if (lstitem.size() == 0) {
            //  throw new RuntimeException("未找到销售与收款信息");
            FmCashcarryoveritemPojo newitem = new  FmCashcarryoveritemPojo();
            newitem.setOpenamount(0D);
            newitem.setInamount(0D);
            newitem.setOutamount(0D);
            newitem.setCloseamount(0D);
            newitem.setBilldate(dtEnd);
            lstitem.add(newitem);
        }


        // 改为BigDec
        BigDecimal decinAmt = BigDecimal.valueOf(0);
        BigDecimal decoutAmt = BigDecimal.valueOf(0);
        BigDecimal itemopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal itemcloseAmt = BigDecimal.valueOf(0);

        for (int i = 0; i < lstitem.size(); i++) {
            lstitem.get(i).setRownum(i);
            BigDecimal iteminAmt = BigDecimal.valueOf(lstitem.get(i).getInamount());
            BigDecimal itemoutAmt = BigDecimal.valueOf(lstitem.get(i).getOutamount());
            decinAmt = decinAmt.add(iteminAmt);
            decoutAmt = decoutAmt.add(itemoutAmt);
            // 计算行期末
            itemcloseAmt = itemopenAmt.add(iteminAmt).subtract(itemoutAmt);
            lstitem.get(i).setCloseamount(itemcloseAmt.doubleValue());
            // 更新行期初，为下一行计算用
            itemopenAmt = BigDecimal.valueOf(itemcloseAmt.doubleValue());
        }

        // 填入 初始，初末
        lstitem.get(0).setOpenamount(openAmount);

        BigDecimal decopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal deccloseAmt = BigDecimal.valueOf(0);
        deccloseAmt = deccloseAmt.add(decopenAmt).add(decinAmt).subtract(decoutAmt);

        lstitem.get(lstitem.size() - 1).setCloseamount(deccloseAmt.doubleValue());

        //返回Bill实例
        return lstitem;

    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public List<FmCashcarryoveritemPojo> getMultItemList(QueryParam queryParam) {
        return this.fmCashcarryoverMapper.getMultItemList(queryParam);
    }

    /**
     * 分页查询实时报表
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmCashcarryoverPojo> getNowPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmCashcarryoverPojo> lst = this.fmCashcarryoverMapper.getNowPageList(queryParam);
            PageInfo<FmCashcarryoverPojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //销售结转redis状态key
    private final String BUSBATCHCREATE_CODE = "busbatchcreate_codes:";

    @Async
    @Override
    @Transactional
    public void batchCreateStart(FmCashcarryoverPojo fmCashcarryoverPojo, String uuid) {
        try {
            //设置当前计算任务进度
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100");
            missionMsg.put("msg", "任务开始处理");
            missionMsg.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.saRedisService.setCacheMapValue(BUSBATCHCREATE_CODE, uuid, missionMsg);

            int num = 0;
            String tid = fmCashcarryoverPojo.getTenantid();
            // 获取所有客户
            List<FmCashaccountPojo> lstca = this.fmCashaccountMapper.getList(tid);

            // item改为BigDec
            BigDecimal decrecopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecoutAmt = BigDecimal.valueOf(0);
            BigDecimal decreccloseAmt = BigDecimal.valueOf(0);

            // 遍历计算每个客户账单
            for (FmCashaccountPojo ca : lstca) {
                FmCashcarryoverPojo newPojo = new FmCashcarryoverPojo();
                BeanUtils.copyProperties(fmCashcarryoverPojo, newPojo);
                newPojo.setCashaccid(ca.getId());
                newPojo.setCashaccname(ca.getAccountname());
                // 1.送货单to收款
                newPojo.setItem(pullItemList(newPojo));
                // 改为BigDec
                BigDecimal decopenAmt = BigDecimal.valueOf(0);
                BigDecimal decinAmt = BigDecimal.valueOf(0);
                BigDecimal decoutAmt = BigDecimal.valueOf(0);
                BigDecimal deccloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getItem().size() > 0) {
                    decopenAmt = decopenAmt.add(BigDecimal.valueOf(newPojo.getItem().get(0).getOpenamount()));
                    deccloseAmt = deccloseAmt.add(BigDecimal.valueOf(newPojo.getItem().get(newPojo.getItem().size() - 1).getCloseamount()));
                }
                for (FmCashcarryoveritemPojo itemPojo : newPojo.getItem()) {
                    decinAmt = decinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decoutAmt = decoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setBillopenamount(decopenAmt.doubleValue());
                newPojo.setBillinamount(decinAmt.doubleValue());
                newPojo.setBilloutamount(decoutAmt.doubleValue());
                newPojo.setBillcloseamount(deccloseAmt.doubleValue());

                insert(newPojo);
                // 当前客户item累加
                decrecopenAmt = decrecopenAmt.add(decopenAmt);
                decrecinAmt = decrecinAmt.add(decinAmt);
                decrecoutAmt = decrecoutAmt.add(decoutAmt);
                decreccloseAmt = decreccloseAmt.add(deccloseAmt);
                // 下一位账号
                num++;
            }
            FmCashcarryoverrecPojo fmCashcarryoverrecPojo = new FmCashcarryoverrecPojo();
            BeanUtils.copyProperties(fmCashcarryoverPojo, fmCashcarryoverrecPojo);
            // 设置item该月累加账单
            fmCashcarryoverrecPojo.setBillopenamount(decrecopenAmt.doubleValue());
            fmCashcarryoverrecPojo.setBillinamount(decrecinAmt.doubleValue());
            fmCashcarryoverrecPojo.setBilloutamount(decrecoutAmt.doubleValue());
            fmCashcarryoverrecPojo.setBillcloseamount(decreccloseAmt.doubleValue());
            this.fmCashcarryoverrecService.insert(fmCashcarryoverrecPojo);

            missionMsg.put("code", "200");//任务完成代码
            missionMsg.put("msg", "任务处理完成");
            missionMsg.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.saRedisService.setCacheMapValue(BUSBATCHCREATE_CODE, uuid, missionMsg);
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }
    }

    @Override
    public Map<String, Object> batchCreateState(String key) {
        return this.saRedisService.getCacheMapValue(BUSBATCHCREATE_CODE, key);
    }
}
