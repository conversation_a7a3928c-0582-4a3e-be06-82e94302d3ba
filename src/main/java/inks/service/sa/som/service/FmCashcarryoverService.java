package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmCashcarryoverPojo;
import inks.service.sa.som.domain.pojo.FmCashcarryoveritemPojo;
import inks.service.sa.som.domain.pojo.FmCashcarryoveritemdetailPojo;

import java.util.List;
import java.util.Map;

/**
 * 出纳账单(FmCashcarryover)表服务接口
 *
 * <AUTHOR>
 * @since 2022-09-26 19:41:23
 */
public interface FmCashcarryoverService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCashcarryoverPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmCashcarryoveritemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCashcarryoverPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmCashcarryoverPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmCashcarryoverPojo> getPageTh(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param fmCashcarryoverPojo 实例对象
     * @return 实例对象
     */
    FmCashcarryoverPojo insert(FmCashcarryoverPojo fmCashcarryoverPojo);

    /**
     * 修改数据
     *
     * @param fmCashcarryoverpojo 实例对象
     * @return 实例对象
     */
    FmCashcarryoverPojo update(FmCashcarryoverPojo fmCashcarryoverpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    /**
     * 现金银行明细
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmCashcarryoveritemPojo> getBillItemList(QueryParam queryParam);


    // 批量生产账单
    int batchCreate(FmCashcarryoverPojo fmCashcarryoverPojo);

    // 批量生产账单
    int batchInit(FmCashcarryoverPojo fmCashcarryoverPojo);
    /**
     * 通过出纳账号id查询最新单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
      FmCashcarryoverPojo getMaxEntityByCash(String key, String tid);

    List<FmCashcarryoveritemPojo> pullItemList(FmCashcarryoverPojo fmCashcarryoverPojo);
    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    List<FmCashcarryoveritemPojo> getMultItemList(QueryParam queryParam);


    /**
     * 分页查询实时报表
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmCashcarryoverPojo> getNowPageList(QueryParam queryParam);


    void batchCreateStart(FmCashcarryoverPojo busAccountPojo, String uuid);

    Map<String, Object> batchCreateState(String key);


    
}
