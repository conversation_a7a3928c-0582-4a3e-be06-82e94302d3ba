package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.FmPayapplyEntity;
import inks.service.sa.som.domain.FmPayapplycashEntity;
import inks.service.sa.som.domain.FmPayapplyitemEntity;
import inks.service.sa.som.domain.pojo.FmPayapplyPojo;
import inks.service.sa.som.domain.pojo.FmPayapplycashPojo;
import inks.service.sa.som.domain.pojo.FmPayapplyitemPojo;
import inks.service.sa.som.domain.pojo.FmPayapplyitemdetailPojo;
import inks.service.sa.som.mapper.FmPayapplyMapper;
import inks.service.sa.som.mapper.FmPayapplycashMapper;
import inks.service.sa.som.mapper.FmPayapplyitemMapper;
import inks.service.sa.som.service.FmPayapplyService;
import inks.service.sa.som.service.FmPayapplycashService;
import inks.service.sa.som.service.FmPayapplyitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 往来核销(FmPayapply)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-13 21:30:02
 */
@Service("fmPayapplyService")
public class FmPayapplyServiceImpl implements FmPayapplyService {
    @Resource
    private FmPayapplyMapper fmPayapplyMapper;

    @Resource
    private FmPayapplyitemMapper fmPayapplyitemMapper;

    
    @Resource
    private FmPayapplyitemService fmPayapplyitemService;

    /**
     * 服务对象cash
     */
    @Resource
    private FmPayapplycashService fmPayapplycashService;

    @Resource
    private FmPayapplycashMapper fmPayapplycashMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmPayapplyPojo getEntity(String key, String tid) {
        return this.fmPayapplyMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmPayapplyitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmPayapplyitemdetailPojo> lst = fmPayapplyMapper.getPageList(queryParam);
            PageInfo<FmPayapplyitemdetailPojo> pageInfo = new PageInfo<FmPayapplyitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmPayapplyPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            FmPayapplyPojo fmPayapplyPojo = this.fmPayapplyMapper.getEntity(key, tid);
            //读取子表
            fmPayapplyPojo.setItem(fmPayapplyitemMapper.getList(fmPayapplyPojo.getId(), fmPayapplyPojo.getTenantid()));
            //读取子表
            fmPayapplyPojo.setCash(fmPayapplycashMapper.getList(fmPayapplyPojo.getId(), fmPayapplyPojo.getTenantid()));
            return fmPayapplyPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmPayapplyPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmPayapplyPojo> lst = fmPayapplyMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(fmPayapplyitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setCash(fmPayapplycashMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<FmPayapplyPojo> pageInfo = new PageInfo<FmPayapplyPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmPayapplyPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmPayapplyPojo> lst = fmPayapplyMapper.getPageTh(queryParam);
            PageInfo<FmPayapplyPojo> pageInfo = new PageInfo<FmPayapplyPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param fmPayapplyPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public FmPayapplyPojo insert(FmPayapplyPojo fmPayapplyPojo) {
//初始化NULL字段
        if (fmPayapplyPojo.getRefno() == null) fmPayapplyPojo.setRefno("");
        if (fmPayapplyPojo.getBilltype() == null) fmPayapplyPojo.setBilltype("");
        if (fmPayapplyPojo.getBilldate() == null) fmPayapplyPojo.setBilldate(new Date());
        if (fmPayapplyPojo.getBilltitle() == null) fmPayapplyPojo.setBilltitle("");
        if (fmPayapplyPojo.getPaygroupid() == null) fmPayapplyPojo.setPaygroupid("");
        if (fmPayapplyPojo.getApplygroupid() == null) fmPayapplyPojo.setApplygroupid("");
        if (fmPayapplyPojo.getPayamount() == null) fmPayapplyPojo.setPayamount(0D);
        if (fmPayapplyPojo.getApplied() == null) fmPayapplyPojo.setApplied(0D);
        if (fmPayapplyPojo.getOperator() == null) fmPayapplyPojo.setOperator("");
        if (fmPayapplyPojo.getSummary() == null) fmPayapplyPojo.setSummary("");
        if (fmPayapplyPojo.getCreateby() == null) fmPayapplyPojo.setCreateby("");
        if (fmPayapplyPojo.getCreatebyid() == null) fmPayapplyPojo.setCreatebyid("");
        if (fmPayapplyPojo.getCreatedate() == null) fmPayapplyPojo.setCreatedate(new Date());
        if (fmPayapplyPojo.getLister() == null) fmPayapplyPojo.setLister("");
        if (fmPayapplyPojo.getListerid() == null) fmPayapplyPojo.setListerid("");
        if (fmPayapplyPojo.getModifydate() == null) fmPayapplyPojo.setModifydate(new Date());
        if (fmPayapplyPojo.getCustom1() == null) fmPayapplyPojo.setCustom1("");
        if (fmPayapplyPojo.getCustom2() == null) fmPayapplyPojo.setCustom2("");
        if (fmPayapplyPojo.getCustom3() == null) fmPayapplyPojo.setCustom3("");
        if (fmPayapplyPojo.getCustom4() == null) fmPayapplyPojo.setCustom4("");
        if (fmPayapplyPojo.getCustom5() == null) fmPayapplyPojo.setCustom5("");
        if (fmPayapplyPojo.getCustom6() == null) fmPayapplyPojo.setCustom6("");
        if (fmPayapplyPojo.getCustom7() == null) fmPayapplyPojo.setCustom7("");
        if (fmPayapplyPojo.getCustom8() == null) fmPayapplyPojo.setCustom8("");
        if (fmPayapplyPojo.getCustom9() == null) fmPayapplyPojo.setCustom9("");
        if (fmPayapplyPojo.getCustom10() == null) fmPayapplyPojo.setCustom10("");
        if (fmPayapplyPojo.getTenantid() == null) fmPayapplyPojo.setTenantid("");
        if (fmPayapplyPojo.getTenantname() == null) fmPayapplyPojo.setTenantname("");
        if (fmPayapplyPojo.getRevision() == null) fmPayapplyPojo.setRevision(0);
        //生成id
        String id = UUID.randomUUID().toString();
        FmPayapplyEntity fmPayapplyEntity = new FmPayapplyEntity();
        BeanUtils.copyProperties(fmPayapplyPojo, fmPayapplyEntity);
        //设置id和新建日期
        fmPayapplyEntity.setId(id);
        fmPayapplyEntity.setRevision(1);  //乐观锁
        //插入主表
        this.fmPayapplyMapper.insert(fmPayapplyEntity);
        //Item子表处理
        List<FmPayapplyitemPojo> lst = fmPayapplyPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                FmPayapplyitemPojo itemPojo = this.fmPayapplyitemService.clearNull(lst.get(i));
                FmPayapplyitemEntity fmPayapplyitemEntity = new FmPayapplyitemEntity();
                BeanUtils.copyProperties(itemPojo, fmPayapplyitemEntity);
                //设置id和Pid
                fmPayapplyitemEntity.setId(UUID.randomUUID().toString());
                fmPayapplyitemEntity.setPid(id);
                fmPayapplyitemEntity.setTenantid(fmPayapplyPojo.getTenantid());
                fmPayapplyitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.fmPayapplyitemMapper.insert(fmPayapplyitemEntity);
                // 同步关联发票
                if (fmPayapplyEntity.getBilltype().equals("预收冲应收")) {
                    // 同步销售发票,销售订单的收款,预收款,总收款等(主表Receipted共收,FirstAmt预收,LastAmt收款,子表AvgFirstAmt, AvgLastAmt)
                    syncFirstAndLastAmt(fmPayapplyitemEntity.getInvobillid(), fmPayapplyitemEntity.getInvobillcode(), fmPayapplyPojo.getTenantid());
                    // 销售发票有没有超数
                    Double overAmount = this.fmPayapplyMapper.getSaleInvoAmount(fmPayapplyitemEntity.getInvobillid(), fmPayapplyPojo.getTenantid());
                    if (overAmount != null && overAmount > 0) {
                        int Rowno = i + 1;
                        throw new RuntimeException(Rowno + "行," + fmPayapplyitemEntity.getInvobillcode() + "发票核销超额:" + overAmount);
                    }
                } else {
                    this.fmPayapplyMapper.updateBuyInvoFinish(fmPayapplyitemEntity.getInvobillid(), fmPayapplyitemEntity.getInvobillcode(), fmPayapplyPojo.getTenantid());
                    // 采购发票有没有超数
                    Double overAmount = this.fmPayapplyMapper.getBuyInvoAmount(fmPayapplyitemEntity.getInvobillid(), fmPayapplyPojo.getTenantid());
                    if (overAmount != null && overAmount > 0) {
                        int Rowno = i + 1;
                        throw new RuntimeException(Rowno + "行," + fmPayapplyitemEntity.getInvobillcode() + "发票核销超额:" + overAmount);
                    }
                }
            }
        }
        //Cash子表处理
        List<FmPayapplycashPojo> lstcash = fmPayapplyPojo.getCash();
        if (lstcash != null) {
            //循环每个item子表
            for (int i = 0; i < lstcash.size(); i++) {
                //初始化cash的NULL
                FmPayapplycashPojo cashPojo = this.fmPayapplycashService.clearNull(lstcash.get(i));
                FmPayapplycashEntity fmPayapplycashEntity = new FmPayapplycashEntity();
                BeanUtils.copyProperties(cashPojo, fmPayapplycashEntity);
                //设置id和Pid
                fmPayapplycashEntity.setId(UUID.randomUUID().toString());
                fmPayapplycashEntity.setPid(id);
                fmPayapplycashEntity.setTenantid(fmPayapplyPojo.getTenantid());
                fmPayapplycashEntity.setRevision(1);  //乐观锁
                //插入子表
                this.fmPayapplycashMapper.insert(fmPayapplycashEntity);
                // 同步关联预收预付
                if (fmPayapplyEntity.getBilltype().equals("预收冲应收")) {
                    this.fmPayapplyMapper.updateSaleDepoFinish(fmPayapplycashEntity.getPaybillid(), fmPayapplyPojo.getTenantid());
                    // 预收有没有超数
                    Double overAmount = this.fmPayapplyMapper.getSaleDepoAmount(fmPayapplycashEntity.getPaybillid(), fmPayapplyPojo.getTenantid());
                    if (overAmount != null && overAmount > 0) {
                        int Rowno = i + 1;
                        throw new RuntimeException(Rowno + "行," + fmPayapplycashEntity.getPaybillcode() + "预收核销超额:" + overAmount.toString());
                    }
                } else {
                    this.fmPayapplyMapper.updateBuyPrepFinish(fmPayapplycashEntity.getPaybillid(), fmPayapplycashEntity.getPaybillcode(), fmPayapplyPojo.getTenantid());
                    // 预付有没有超数
                    Double overAmount = this.fmPayapplyMapper.getBuyPrepAmount(fmPayapplycashEntity.getPaybillid(), fmPayapplyPojo.getTenantid());
                    if (overAmount != null && overAmount > 0) {
                        int Rowno = i + 1;
                        throw new RuntimeException(Rowno + "行," + fmPayapplycashEntity.getPaybillcode() + "预付核销超额:" + overAmount.toString());
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(fmPayapplyEntity.getId(), fmPayapplyEntity.getTenantid());

    }

    // sale，fm服务都也有这个方法，必须同时修改！！！！！！！
    private void syncFirstAndLastAmt(String invobillid, String invobillcode, String tid) {
        // 1更新Bus_Invoice的Receipted共收,FirstAmt预收,LastAmt收款 (FirstAmt+LastAmt=Receipted)
        this.fmPayapplyMapper.updateBusInvoFinish(invobillid, invobillcode,tid);
        // 2更新Bus_InvoiceItem的AvgFirstAmt, AvgLastAmt
        this.fmPayapplyMapper.updateBusInvoItemAvgAmt(invobillid, invobillcode,tid);
        // 3更新关联销售订单Bus_MachiningItem的AvgFirstAmt, AvgLastAmt
        this.fmPayapplyMapper.updateBusMachingItemAvgAmt(invobillid, invobillcode,tid);
        // 4更新关联销售订单Bus_Machining的FirstAmt, LastAmt
        this.fmPayapplyMapper.updateBusMachingFirstLastAmt(invobillid, invobillcode,tid);
    }

    /**
     * 修改数据
     *
     * @param fmPayapplyPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public FmPayapplyPojo update(FmPayapplyPojo fmPayapplyPojo) {
        //主表更改
        FmPayapplyEntity fmPayapplyEntity = new FmPayapplyEntity();
        BeanUtils.copyProperties(fmPayapplyPojo, fmPayapplyEntity);
        this.fmPayapplyMapper.update(fmPayapplyEntity);
        if (fmPayapplyPojo.getItem() != null) {
            //Item子表处理
            List<FmPayapplyitemPojo> lst = fmPayapplyPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = fmPayapplyMapper.getDelItemIds(fmPayapplyPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    FmPayapplyitemPojo delPojo = this.fmPayapplyitemMapper.getEntity(lstDelIds.get(i), fmPayapplyEntity.getTenantid());
                    this.fmPayapplyitemMapper.delete(lstDelIds.get(i), fmPayapplyEntity.getTenantid());
                    // 同步关联发票
                    if (fmPayapplyEntity.getBilltype().equals("预收冲应收")) {
                        // 同步销售发票,销售订单的收款,预收款,总收款等(主表Receipted共收,FirstAmt预收,LastAmt收款,子表AvgFirstAmt, AvgLastAmt)
                        syncFirstAndLastAmt(delPojo.getInvobillid(), delPojo.getInvobillcode(), fmPayapplyPojo.getTenantid());
//                    this.fmPayapplyMapper.updateBusInvoFinish(delPojo.getInvobillid(), delPojo.getInvobillcode(), fmPayapplyPojo.getTenantid());
                    } else {
                        this.fmPayapplyMapper.updateBuyInvoFinish(delPojo.getInvobillid(), delPojo.getInvobillcode(), fmPayapplyPojo.getTenantid());
                    }
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    FmPayapplyitemEntity fmPayapplyitemEntity = new FmPayapplyitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        FmPayapplyitemPojo itemPojo = this.fmPayapplyitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, fmPayapplyitemEntity);
                        //设置id和Pid
                        fmPayapplyitemEntity.setId(UUID.randomUUID().toString());  // item id
                        fmPayapplyitemEntity.setPid(fmPayapplyEntity.getId());  // 主表 id
                        fmPayapplyitemEntity.setTenantid(fmPayapplyPojo.getTenantid());   // 租户id
                        fmPayapplyitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.fmPayapplyitemMapper.insert(fmPayapplyitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), fmPayapplyitemEntity);
                        fmPayapplyitemEntity.setTenantid(fmPayapplyPojo.getTenantid());
                        this.fmPayapplyitemMapper.update(fmPayapplyitemEntity);
                    }

                    // 同步关联发票
                    if (fmPayapplyEntity.getBilltype().equals("预收冲应收")) {
                        // 同步销售发票,销售订单的收款,预收款,总收款等(主表Receipted共收,FirstAmt预收,LastAmt收款,子表AvgFirstAmt, AvgLastAmt)
                        syncFirstAndLastAmt(fmPayapplyitemEntity.getInvobillid(), fmPayapplyitemEntity.getInvobillcode(), fmPayapplyPojo.getTenantid());
//                        this.fmPayapplyMapper.updateBusInvoFinish(fmPayapplyitemEntity.getInvobillid(), fmPayapplyitemEntity.getInvobillcode(), fmPayapplyPojo.getTenantid());
                        // 销售发票有没有超数
                        Double overAmount = this.fmPayapplyMapper.getSaleInvoAmount(fmPayapplyitemEntity.getInvobillid(), fmPayapplyPojo.getTenantid());
                        if (overAmount != null && overAmount > 0) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行," + fmPayapplyitemEntity.getInvobillcode() + "发票核销超额:" + overAmount);
                        }
                    } else {
                        this.fmPayapplyMapper.updateBuyInvoFinish(fmPayapplyitemEntity.getInvobillid(), fmPayapplyitemEntity.getInvobillcode(), fmPayapplyPojo.getTenantid());
                        // 销售发票有没有超数
                        Double overAmount = this.fmPayapplyMapper.getBuyInvoAmount(fmPayapplyitemEntity.getInvobillid(), fmPayapplyPojo.getTenantid());
                        if (overAmount != null && overAmount > 0) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行," + fmPayapplyitemEntity.getInvobillcode() + "发票核销超额:" + overAmount);
                        }
                    }
                }
            }
        }


        if (fmPayapplyPojo.getCash() != null) {
            //Cash子表处理
            List<FmPayapplycashPojo> lstcash = fmPayapplyPojo.getCash();
            //获取被删除的Cash
            List<String> lstcashDelIds = fmPayapplyMapper.getDelCashIds(fmPayapplyPojo);
            if (lstcashDelIds != null) {
                //循环每个删除cash子表
                for (int i = 0; i < lstcashDelIds.size(); i++) {
                    FmPayapplycashPojo delPojo = this.fmPayapplycashMapper.getEntity(lstcashDelIds.get(i), fmPayapplyEntity.getTenantid());
                    this.fmPayapplycashMapper.delete(lstcashDelIds.get(i), fmPayapplyEntity.getTenantid());
                    // 同步关联预收预付
                    if (fmPayapplyEntity.getBilltype().equals("预收冲应收")) {
                        this.fmPayapplyMapper.updateSaleDepoFinish(delPojo.getPaybillid(), fmPayapplyPojo.getTenantid());
                    } else {
                        this.fmPayapplyMapper.updateBuyPrepFinish(delPojo.getPaybillid(), delPojo.getPaybillcode(), fmPayapplyPojo.getTenantid());
                    }
                }
            }
            if (lstcash != null) {
                //循环每个cash子表
                for (int i = 0; i < lstcash.size(); i++) {
                    FmPayapplycashEntity fmPayapplycashEntity = new FmPayapplycashEntity();
                    if ("".equals(lstcash.get(i).getId()) || lstcash.get(i).getId() == null) {
                        //初始化cash的NULL
                        FmPayapplycashPojo cashPojo = this.fmPayapplycashService.clearNull(lstcash.get(i));
                        BeanUtils.copyProperties(cashPojo, fmPayapplycashEntity);
                        //设置id和Pid
                        fmPayapplycashEntity.setId(UUID.randomUUID().toString());  // cash id
                        fmPayapplycashEntity.setPid(fmPayapplyEntity.getId());  // 主表 id
                        fmPayapplycashEntity.setTenantid(fmPayapplyPojo.getTenantid());   // 租户id
                        fmPayapplycashEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.fmPayapplycashMapper.insert(fmPayapplycashEntity);
                    } else {
                        BeanUtils.copyProperties(lstcash.get(i), fmPayapplycashEntity);
                        fmPayapplycashEntity.setTenantid(fmPayapplyPojo.getTenantid());
                        this.fmPayapplycashMapper.update(fmPayapplycashEntity);
                    }
                    // 同步关联预收预付
                    if (fmPayapplyEntity.getBilltype().equals("预收冲应收")) {
                        this.fmPayapplyMapper.updateSaleDepoFinish(fmPayapplycashEntity.getPaybillid(), fmPayapplyPojo.getTenantid());
                        // 预收有没有超数
                        Double overAmount = this.fmPayapplyMapper.getSaleDepoAmount(fmPayapplycashEntity.getPaybillid(), fmPayapplyPojo.getTenantid());
                        if (overAmount != null && overAmount > 0) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行," + fmPayapplycashEntity.getPaybillcode() + "预收核销超额:" + overAmount.toString());
                        }
                    } else {
                        this.fmPayapplyMapper.updateBuyPrepFinish(fmPayapplycashEntity.getPaybillid(), fmPayapplycashEntity.getPaybillcode(), fmPayapplyPojo.getTenantid());
                        // 预付有没有超数
                        Double overAmount = this.fmPayapplyMapper.getBuyPrepAmount(fmPayapplycashEntity.getPaybillid(), fmPayapplyPojo.getTenantid());
                        if (overAmount != null && overAmount > 0) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行," + fmPayapplycashEntity.getPaybillcode() + "预付核销超额:" + overAmount.toString());
                        }
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(fmPayapplyEntity.getId(), fmPayapplyEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        FmPayapplyPojo fmPayapplyPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<FmPayapplyitemPojo> lst = fmPayapplyPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                FmPayapplyitemPojo delPojo = this.fmPayapplyitemMapper.getEntity(lst.get(i).getId(), tid);
                this.fmPayapplyitemMapper.delete(lst.get(i).getId(), tid);
                // 同步关联发票
                if (fmPayapplyPojo.getBilltype().equals("预收冲应收")) {
                    // 同步销售发票,销售订单的收款,预收款,总收款等(主表Receipted共收,FirstAmt预收,LastAmt收款,子表AvgFirstAmt, AvgLastAmt)
                    syncFirstAndLastAmt(delPojo.getInvobillid(), delPojo.getInvobillcode(), fmPayapplyPojo.getTenantid());
//                    this.fmPayapplyMapper.updateBusInvoFinish(delPojo.getInvobillid(), delPojo.getInvobillcode(), fmPayapplyPojo.getTenantid());
                } else {
                    this.fmPayapplyMapper.updateBuyInvoFinish(delPojo.getInvobillid(), delPojo.getInvobillcode(), fmPayapplyPojo.getTenantid());
                }
            }
        }

        //Cash子表处理
        List<FmPayapplycashPojo> lstcash = fmPayapplyPojo.getCash();
        if (lstcash != null) {
            //循环每个删除cash子表
            for (int i = 0; i < lstcash.size(); i++) {
                FmPayapplycashPojo delPojo = this.fmPayapplycashMapper.getEntity(lstcash.get(i).getId(), tid);
                this.fmPayapplycashMapper.delete(lstcash.get(i).getId(), tid);
                // 同步关联预收预付
                if (fmPayapplyPojo.getBilltype().equals("预收冲应收")) {
                    this.fmPayapplyMapper.updateSaleDepoFinish(delPojo.getPaybillid(), fmPayapplyPojo.getTenantid());
                } else {
                    this.fmPayapplyMapper.updateBuyPrepFinish(delPojo.getPaybillid(), delPojo.getPaybillcode(), fmPayapplyPojo.getTenantid());
                }
            }
        }
        return this.fmPayapplyMapper.delete(key, tid);
    }


}
