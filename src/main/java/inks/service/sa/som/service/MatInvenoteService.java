package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatInvenotePojo;
import inks.service.sa.som.domain.pojo.MatInvenoteitemPojo;
import inks.service.sa.som.domain.pojo.MatInvenoteitemdetailPojo;

import java.util.List;

/**
 * 仓库盘点(MatInvenote)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-14 19:24:21
 */
public interface MatInvenoteService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatInvenotePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatInvenoteitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatInvenotePojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatInvenotePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatInvenotePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matInvenotePojo 实例对象
     * @return 实例对象
     */
    MatInvenotePojo insert(MatInvenotePojo matInvenotePojo);

    /**
     * 修改数据
     *
     * @param matInvenotepojo 实例对象
     * @return 实例对象
     */
    MatInvenotePojo update(MatInvenotePojo matInvenotepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param matInvenotePojo 实例对象
     * @return 实例对象
     */
    MatInvenotePojo approval(MatInvenotePojo matInvenotePojo);

    /**
     * 新增数据
     *
     * @param matInvenotePojo 实例对象
     * @return 实例对象
     */
    MatInvenotePojo createByNote(MatInvenotePojo matInvenotePojo);

    // 建立盘点出入库
    String createAccess (String key,  LoginUser loginUser);

    List<MatInvenoteitemPojo> getItemListByIds(String ids, String pid, String tenantid);
}
