package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.StreamUtils;
import inks.service.sa.som.domain.BusDelieryEntity;
import inks.service.sa.som.domain.BusDelieryitemEntity;
import inks.service.sa.som.domain.constant.MyConstant;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.*;
import inks.service.sa.som.service.BusDelieryService;
import inks.service.sa.som.service.BusDelieryitemService;
import inks.service.sa.som.service.MatGoodsService;
import inks.sa.common.core.service.SaRedisService;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;
import static org.apache.commons.lang3.StringUtils.*;

/**
 * 发出商品(BusDeliery)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-25 15:16:12
 */
@Service("busDelieryService")
public class BusDelieryServiceImpl implements BusDelieryService {
    @Resource
    private BusDelieryMapper busDelieryMapper;

    @Resource
    private BusDelieryitemMapper busDelieryitemMapper;

    @Resource
    private BusMachiningitemMapper busMachiningitemMapper;
    @Resource
    private BusMachiningMapper busMachiningMapper;
    @Resource
    private MatGoodsService matGoodsService;
    
    @Resource
    private BusDelieryitemService busDelieryitemService;

    @Resource
    private BusAccountrecMapper busAccountrecMapper;

   
    @Resource
    private SaRedisService saRedisService;


    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private SyncMapper syncMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDelieryPojo getEntity(String key, String tid) {
        return this.busDelieryMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDelieryitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDelieryitemdetailPojo> lst = busDelieryMapper.getPageList(queryParam);
            PageInfo<BusDelieryitemdetailPojo> pageInfo = new PageInfo<BusDelieryitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDelieryPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusDelieryPojo busDelieryPojo = this.busDelieryMapper.getEntity(key, tid);
            //读取子表
            busDelieryPojo.setItem(busDelieryitemMapper.getList(busDelieryPojo.getId(), busDelieryPojo.getTenantid()));
            return busDelieryPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDelieryPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDelieryPojo> lst = busDelieryMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busDelieryitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusDelieryPojo> pageInfo = new PageInfo<BusDelieryPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDelieryPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDelieryPojo> lst = busDelieryMapper.getPageTh(queryParam);
            PageInfo<BusDelieryPojo> pageInfo = new PageInfo<BusDelieryPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busDelieryPojo 实例对象
     * @return 实例对象
     */
    @Override
    // @Transactional
    public BusDelieryPojo insert(BusDelieryPojo busDelieryPojo) {
//初始化NULL字段
        if (busDelieryPojo.getRefno() == null) busDelieryPojo.setRefno("");
        String billType = busDelieryPojo.getBilltype();
        if (billType == null) busDelieryPojo.setBilltype("");
        if (busDelieryPojo.getBilltitle() == null) busDelieryPojo.setBilltitle("");
        if (busDelieryPojo.getBilldate() == null) busDelieryPojo.setBilldate(new Date());
        if (busDelieryPojo.getGroupid() == null) busDelieryPojo.setGroupid("");
        if (busDelieryPojo.getTelephone() == null) busDelieryPojo.setTelephone("");
        if (busDelieryPojo.getLinkman() == null) busDelieryPojo.setLinkman("");
        if (busDelieryPojo.getDeliadd() == null) busDelieryPojo.setDeliadd("");
        if (busDelieryPojo.getTaxrate() == null) busDelieryPojo.setTaxrate(0);
        if (busDelieryPojo.getTransport() == null) busDelieryPojo.setTransport("");
        if (busDelieryPojo.getSalesman() == null) busDelieryPojo.setSalesman("");
        if (busDelieryPojo.getSalesmanid() == null) busDelieryPojo.setSalesmanid("");
        if (busDelieryPojo.getOperator() == null) busDelieryPojo.setOperator("");
        if (busDelieryPojo.getOperatorid() == null) busDelieryPojo.setOperatorid("");
        if (busDelieryPojo.getSummary() == null) busDelieryPojo.setSummary("");
        if (busDelieryPojo.getCreateby() == null) busDelieryPojo.setCreateby("");
        if (busDelieryPojo.getCreatebyid() == null) busDelieryPojo.setCreatebyid("");
        if (busDelieryPojo.getCreatedate() == null) busDelieryPojo.setCreatedate(new Date());
        if (busDelieryPojo.getLister() == null) busDelieryPojo.setLister("");
        if (busDelieryPojo.getListerid() == null) busDelieryPojo.setListerid("");
        if (busDelieryPojo.getModifydate() == null) busDelieryPojo.setModifydate(new Date());
        if (busDelieryPojo.getAssessor() == null) busDelieryPojo.setAssessor("");
        if (busDelieryPojo.getAssessorid() == null) busDelieryPojo.setAssessorid("");
        if (busDelieryPojo.getAssessdate() == null) busDelieryPojo.setAssessdate(new Date());
        if (busDelieryPojo.getDisannulcount() == null) busDelieryPojo.setDisannulcount(0);
        if (busDelieryPojo.getBillstatecode() == null) busDelieryPojo.setBillstatecode("");
        if (busDelieryPojo.getBillstatedate() == null) busDelieryPojo.setBillstatedate(new Date());
        if (busDelieryPojo.getBilltaxamount() == null) busDelieryPojo.setBilltaxamount(0D);
        if (busDelieryPojo.getBilltaxtotal() == null) busDelieryPojo.setBilltaxtotal(0D);
        if (busDelieryPojo.getBillamount() == null) busDelieryPojo.setBillamount(0D);
        if (busDelieryPojo.getBillreceived() == null) busDelieryPojo.setBillreceived(0D);
        if (busDelieryPojo.getItemcount() == null) busDelieryPojo.setItemcount(busDelieryPojo.getItem().size());
        if (busDelieryPojo.getPickcount() == null) busDelieryPojo.setPickcount(0);
        if (busDelieryPojo.getFinishcount() == null) busDelieryPojo.setFinishcount(0);
        if (busDelieryPojo.getInvocount() == null) busDelieryPojo.setInvocount(0);
        if (busDelieryPojo.getPrintcount() == null) busDelieryPojo.setPrintcount(0);
        if (busDelieryPojo.getOaflowmark() == null) busDelieryPojo.setOaflowmark(0);
        if (busDelieryPojo.getCustom1() == null) busDelieryPojo.setCustom1("");
        if (busDelieryPojo.getCustom2() == null) busDelieryPojo.setCustom2("");
        if (busDelieryPojo.getCustom3() == null) busDelieryPojo.setCustom3("");
        if (busDelieryPojo.getCustom4() == null) busDelieryPojo.setCustom4("");
        if (busDelieryPojo.getCustom5() == null) busDelieryPojo.setCustom5("");
        if (busDelieryPojo.getCustom6() == null) busDelieryPojo.setCustom6("");
        if (busDelieryPojo.getCustom7() == null) busDelieryPojo.setCustom7("");
        if (busDelieryPojo.getCustom8() == null) busDelieryPojo.setCustom8("");
        if (busDelieryPojo.getCustom9() == null) busDelieryPojo.setCustom9("");
        if (busDelieryPojo.getCustom10() == null) busDelieryPojo.setCustom10("");
        String tid = busDelieryPojo.getTenantid();
        if (tid == null) busDelieryPojo.setTenantid("");
        if (busDelieryPojo.getTenantname() == null) busDelieryPojo.setTenantname("");
        if (busDelieryPojo.getRevision() == null) busDelieryPojo.setRevision(0);
        // 结账检查
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(tid);
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busDelieryPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getEnddate() + ",禁止新建结账前单据");
        }

        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusDelieryEntity busDelieryEntity = new BusDelieryEntity();
        BeanUtils.copyProperties(busDelieryPojo, busDelieryEntity);
        //设置id和新建日期
        busDelieryEntity.setId(id);
        busDelieryEntity.setRevision(1);  //乐观锁
        List<BusDelieryitemPojo> lst = busDelieryPojo.getItem();
//        // 获取attributestr公式
//        Map<String, Object> tencfg = this.saRedisService.getValue("tenant_config:" + busDelieryPojo.getTenantid());
        String attrstrExpr = null;
//        if (tencfg != null && tencfg.containsKey("system.bill.attributestr")) {
//            attrstrExpr = tencfg.get("system.bill.attributestr").toString();
//        }
        // 数量检查
        if ("发出商品".equals(billType) && busDelieryPojo.getItem() != null) {
            // 更新订单已发货 Eric20211213
            for (int i = 0; i < lst.size(); i++) {
                if (!"".equals(lst.get(i).getCiteitemid()) && !"".equals(lst.get(i).getCiteuid())) {
                    // 超数检查
                    BusMachiningitemPojo machiningitemPojo = this.busMachiningitemMapper.getEntity(lst.get(i).getCiteitemid(), tid);
                    if (machiningitemPojo != null) {
                        BigDecimal finishQty = BigDecimal.valueOf(machiningitemPojo.getFinishqty());
                        BigDecimal quantity = BigDecimal.valueOf(lst.get(i).getQuantity());
                        BigDecimal totalQty = finishQty.add(quantity);
                        // 默认对比MachiningItem.Quantity, 如果有有最大发货数MaxQty,就对比最大发货数
                        Double qtyFinal = machiningitemPojo.getQuantity();
                        if (machiningitemPojo.getMaxqty() != 0) {
                            qtyFinal = machiningitemPojo.getMaxqty();
                        }
                        //BigDecimal不能直接比较大小，需要使用compareTo方法(前者大于后者返回1，等于返回0，小于返回-1)
                        if (totalQty.compareTo(BigDecimal.valueOf(qtyFinal)) > 0) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,发货总数:" + totalQty + "超出订单数:" + qtyFinal);
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getCiteuid());
                    }
                }
            }
        }

        transactionTemplate.execute((status) -> {

            if (lst != null) {
                //循环每个item子表
                for (BusDelieryitemPojo busDelieryitemPojo : lst) {
                    //初始化item的NULL
                    BusDelieryitemPojo itemPojo = this.busDelieryitemService.clearNull(busDelieryitemPojo);
                    BusDelieryitemEntity busDelieryitemEntity = new BusDelieryitemEntity();
                    BeanUtils.copyProperties(itemPojo, busDelieryitemEntity);
                    //设置id和Pid
                    busDelieryitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                    busDelieryitemEntity.setPid(id);
                    busDelieryitemEntity.setTenantid(tid);
                    busDelieryitemEntity.setRevision(1);  //乐观锁
//                    // 计算子表AttributeStr(通过AttributeJson和system服务的计算公式)
//                    if (isNotBlank(attrstrExpr) && isNotBlank(busDelieryitemEntity.getAttributejson())) {
//                        busDelieryitemEntity.setAttributestr(inks.common.core.utils.bean.BeanUtils.calculateAttrStr(busDelieryitemEntity.getAttributejson(), attrstrExpr));
//                    }
                    //插入子表
                    this.busDelieryitemMapper.insert(busDelieryitemEntity);
                }
            }
            //插入主表
            this.busDelieryMapper.insert(busDelieryEntity);

            if (lst != null) {
                for (BusDelieryitemPojo busDelieryitemPojo : lst) {

                    if ("发出商品".equals(billType) || "订单退货".equals(billType)) {            //更新订单已发货
                        if (!"".equals(busDelieryitemPojo.getCiteitemid()) && !"".equals(busDelieryitemPojo.getCiteuid())) {
                            this.busDelieryMapper.updateMachDeliFinish(busDelieryitemPojo.getCiteitemid(), busDelieryitemPojo.getCiteuid(), tid);
                            // 同步销售订单主表的最终成交金额FinishAmount FinishTaxAmount
                            this.busMachiningMapper.updateFinishAmount(busDelieryitemPojo.getCiteitemid(), tid);
                        }
                    }
                }
            }
            // 更新完工款数
            this.busDelieryMapper.updateFinishCount(busDelieryEntity.getId(), busDelieryEntity.getTenantid());

            //循环每个item子表
            if (lst != null) {
                for (BusDelieryitemPojo busDelieryitemPojo : lst) {
                    if ("发出商品".equals(billType) || "订单退货".equals(billType)) {            //更新订单已发货
                        if (!"".equals(busDelieryitemPojo.getCiteitemid()) && !"".equals(busDelieryitemPojo.getCiteuid())) {
                            this.busDelieryMapper.updateMachFinishCount(busDelieryitemPojo.getCiteitemid(), busDelieryitemPojo.getCiteuid(), tid);
                        }
                    }
                }
            }

            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BusDelieryitemPojo::getGoodsid).collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                syncMapper.updateGoodsBusRemQty(goodsid, tid);
            });
            return Boolean.TRUE;
        });


        //返回Bill实例
        return this.getBillEntity(busDelieryEntity.getId(), busDelieryEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busDelieryPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDelieryPojo update(BusDelieryPojo busDelieryPojo) {
        String tid = busDelieryPojo.getTenantid();
        String billType = busDelieryPojo.getBilltype();
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(tid);
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busDelieryPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getEnddate() + ",禁止修改结账前单据");
        }
        BusDelieryPojo orgPojo = this.busDelieryMapper.getEntity(busDelieryPojo.getId(), tid);
        if (busAccountrecPojo != null && DateUtils.getTimestamp(orgPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getEnddate() + ",禁止修改结账前单据");
        }
        //主表更改
        BusDelieryEntity busDelieryEntity = new BusDelieryEntity();
        BeanUtils.copyProperties(busDelieryPojo, busDelieryEntity);
        busDelieryEntity.setItemcount(busDelieryPojo.getItem().size());
        // 获取attributestr公式
//        Map<String, Object> tencfg = this.saRedisService.getValue("tenant_config:" + busDelieryPojo.getTenantid());
        String attrstrExpr = null;
//        if (tencfg != null && tencfg.containsKey("system.bill.attributestr")) {
//            attrstrExpr = tencfg.get("system.bill.attributestr").toString();
//        }
        // 数量检查
        if (billType.equals("发出商品") && busDelieryPojo.getItem() != null) {
            List<BusDelieryitemPojo> lst = busDelieryPojo.getItem();
            //更新订单已发货 Eric20211213
            for (int i = 0; i < lst.size(); i++) {
                BigDecimal orgQty = BigDecimal.ZERO;
                if (lst.get(i).getId() != null && !"".equals(lst.get(i).getId())) {
                    // 原始记录
                    BusDelieryitemPojo busDelieryitemPojo = this.busDelieryitemMapper.getEntity(lst.get(i).getId(), busDelieryEntity.getTenantid());
                    if (busDelieryitemPojo != null) {
                        orgQty = BigDecimal.valueOf(busDelieryitemPojo.getQuantity());
                    }
                }
                if (!"".equals(lst.get(i).getCiteitemid()) && !"".equals(lst.get(i).getCiteuid())) {
                    // 超数检查
                    BusMachiningitemPojo machiningitemPojo = this.busMachiningitemMapper.getEntity(lst.get(i).getCiteitemid(), tid);
                    if (machiningitemPojo != null) {
                        BigDecimal finishQty = BigDecimal.valueOf(machiningitemPojo.getFinishqty());
                        BigDecimal quantity = BigDecimal.valueOf(lst.get(i).getQuantity());
                        BigDecimal totalQty = finishQty.subtract(orgQty).add(quantity);
                        // 默认对比MachiningItem.Quantity, 如果有有最大发货数MaxQty,就对比最大发货数
                        Double qtyFinal = machiningitemPojo.getQuantity();
                        if (machiningitemPojo.getMaxqty() != 0) {
                            qtyFinal = machiningitemPojo.getMaxqty();
                        }
                        //BigDecimal不能直接比较大小，需要使用compareTo方法(前者大于后者返回1，等于返回0，小于返回-1)
                        if (totalQty.compareTo(BigDecimal.valueOf(qtyFinal)) > 0) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,发货总数:" + totalQty + "超出订单数:" + qtyFinal);
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getCiteuid());
                    }
                }
            }
        }
        //Item子表处理
        List<BusDelieryitemPojo> lst = busDelieryPojo.getItem();
        if (lst == null) throw new RuntimeException("单据内容不能为空");
        //获取被删除的Item
        List<BusDelieryitemPojo> lstDel = new ArrayList<>();
        transactionTemplate.execute((status) -> {
            if (busDelieryPojo.getItem() != null) {
                List<String> lstDelIds = busDelieryMapper.getDelItemIds(busDelieryPojo);
                if (lstDelIds != null) {
                    //循环每个删除item子表
                    for (String lstDelId : lstDelIds) {
                        //读取待删除项目
                        BusDelieryitemPojo busDelieryitemPojo = this.busDelieryitemMapper.getEntity(lstDelId, busDelieryEntity.getTenantid());
                        lstDel.add(busDelieryitemPojo);
                        List<String> lstcite = getItemCiteBillName(lstDelId, busDelieryitemPojo.getPid(), busDelieryEntity.getTenantid());
                        if (!lstcite.isEmpty()) {
                            throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite.toString());
                        }
                        //删除项目
                        this.busDelieryitemMapper.delete(lstDelId, busDelieryEntity.getTenantid());
                        //更新订单已发货 Eric20211213
                        this.busDelieryMapper.updateMachDeliFinish(busDelieryitemPojo.getCiteitemid(), busDelieryitemPojo.getCiteuid(), tid);
                        // 同步销售订单主表的最终成交金额FinishAmount FinishTaxAmount
                        this.busMachiningMapper.updateFinishAmount(busDelieryitemPojo.getCiteitemid(), tid);
                    }
                }
                //循环每个item子表
                for (BusDelieryitemPojo busDelieryitemPojo : lst) {
                    BusDelieryitemEntity busDelieryitemEntity = new BusDelieryitemEntity();
//                    // 先计算子表AttributeStr(通过AttributeJson和system服务的计算公式)
//                    if (isNotBlank(attrstrExpr) && isNotBlank(busDelieryitemEntity.getAttributejson())) {
//                        busDelieryitemPojo.setAttributestr(inks.common.core.utils.bean.BeanUtils.calculateAttrStr(busDelieryitemPojo.getAttributejson(), attrstrExpr));
//                    }
                    if ("".equals(busDelieryitemPojo.getId()) || busDelieryitemPojo.getId() == null) {
                        //初始化item的NULL
                        BusDelieryitemPojo itemPojo = this.busDelieryitemService.clearNull(busDelieryitemPojo);
                        BeanUtils.copyProperties(itemPojo, busDelieryitemEntity);
                        //设置id和Pid
                        busDelieryitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busDelieryitemEntity.setPid(busDelieryEntity.getId());  // 主表 id
                        busDelieryitemEntity.setTenantid(tid);   // 租户id
                        busDelieryitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busDelieryitemMapper.insert(busDelieryitemEntity);

                    } else {
                        BeanUtils.copyProperties(busDelieryitemPojo, busDelieryitemEntity);
                        busDelieryitemEntity.setTenantid(tid);
                        this.busDelieryitemMapper.update(busDelieryitemEntity);
                    }


                }
            }
            this.busDelieryMapper.update(busDelieryEntity);
            if ("发出商品".equals(billType) || "订单退货".equals(billType)) {
                //更新订单已发货 Eric20211213
                for (BusDelieryitemPojo busDelieryitemPojo : lst) {
                    if (!"".equals(busDelieryitemPojo.getCiteitemid()) && !"".equals(busDelieryitemPojo.getCiteuid())) {
                        this.busDelieryMapper.updateMachDeliFinish(busDelieryitemPojo.getCiteitemid(), busDelieryitemPojo.getCiteuid(), tid);
                        // 同步销售订单主表的最终成交金额FinishAmount FinishTaxAmount
                        this.busMachiningMapper.updateFinishAmount(busDelieryitemPojo.getCiteitemid(), tid);
                    }
                }
            }
            // 更新完工款数
            this.busDelieryMapper.updateFinishCount(busDelieryEntity.getId(), busDelieryEntity.getTenantid());
            //循环每个删除item子表
            for (int i = 0; i < lstDel.size(); i++) {
                if ("发出商品".equals(billType) || "订单退货".equals(billType)
                        && !"".equals(lst.get(i).getCiteitemid()) && !"".equals(lst.get(i).getCiteuid())) {
                    this.busDelieryMapper.updateMachFinishCount(lstDel.get(i).getCiteitemid(), lstDel.get(i).getCiteuid(), tid);
                }
            }

            //循环每个item子表
            for (BusDelieryitemPojo busDelieryitemPojo : lst) {
                if ("发出商品".equals(billType) || "订单退货".equals(billType)
                        && !"".equals(busDelieryitemPojo.getCiteitemid()) && !"".equals(busDelieryitemPojo.getCiteuid())) {
                    this.busDelieryMapper.updateMachFinishCount(busDelieryitemPojo.getCiteitemid(), busDelieryitemPojo.getCiteuid(), tid);
                }
            }
            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BusDelieryitemPojo::getGoodsid).collect(Collectors.toSet());
            Set<String> goodsidLstDelSet = lstDel.stream().map(BusDelieryitemPojo::getGoodsid).collect(Collectors.toSet());
            goodsidLstSet.addAll(goodsidLstDelSet);
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                syncMapper.updateGoodsBusRemQty(goodsid, tid);
            });

            return Boolean.TRUE;
        });


        //返回Bill实例
        return this.getBillEntity(busDelieryEntity.getId(), busDelieryEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        BusDelieryPojo busDelieryPojo = this.getBillEntity(key, tid);
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(busDelieryPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busDelieryPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getEnddate() + ",禁止删除结账前单据");
        }
        //Item子表处理
        List<BusDelieryitemPojo> lst = busDelieryPojo.getItem();
        Integer delNum = 0;
        delNum = transactionTemplate.execute((status) -> {
            if (lst != null) {
                //循环每个删除item子表
                for (BusDelieryitemPojo busDelieryitemPojo : lst) {
                    // 检查是否被引用
                    List<String> lstcite = getItemCiteBillName(busDelieryitemPojo.getId(), busDelieryitemPojo.getPid(), tid);
                    if (!lstcite.isEmpty()) {
                        throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                    }
                    this.busDelieryitemMapper.delete(busDelieryitemPojo.getId(), tid);
                    //更新订单已发货 Eric20211213
                    if (!busDelieryitemPojo.getCiteitemid().isEmpty() && !busDelieryitemPojo.getCiteuid().isEmpty()) {
                        this.busDelieryMapper.updateMachDeliFinish(busDelieryitemPojo.getCiteitemid(), busDelieryitemPojo.getCiteuid(), busDelieryPojo.getTenantid());
                        // 同步销售订单主表的最终成交金额FinishAmount FinishTaxAmount
                        this.busMachiningMapper.updateFinishAmount(busDelieryitemPojo.getCiteitemid(), tid);
                    }
                }
            }
            return this.busDelieryMapper.delete(key, tid);
        });

        if (lst != null) {
            //循环每个删除item子表
            for (BusDelieryitemPojo busDelieryitemPojo : lst) {
                //更新订单已发货 Eric20211213
                if (!busDelieryitemPojo.getCiteitemid().isEmpty() && !busDelieryitemPojo.getCiteuid().isEmpty()) {
                    this.busDelieryMapper.updateMachFinishCount(busDelieryitemPojo.getCiteitemid(), busDelieryitemPojo.getCiteuid(), busDelieryPojo.getTenantid());
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BusDelieryitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            syncMapper.updateGoodsBusRemQty(goodsid, tid);
        });
        return delNum;
    }


    /**
     * 审核数据
     *
     * @param busDelieryPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDelieryPojo approval(BusDelieryPojo busDelieryPojo) {
        //主表更改
        BusDelieryEntity busDelieryEntity = new BusDelieryEntity();
        BeanUtils.copyProperties(busDelieryPojo, busDelieryEntity);
        this.busDelieryMapper.approval(busDelieryEntity);
        //返回Bill实例
        return this.getBillEntity(busDelieryEntity.getId(), busDelieryEntity.getTenantid());
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDelieryPojo disannul(List<BusDelieryitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            BusDelieryitemPojo Pojo = lst.get(i);
            BusDelieryitemPojo dbPojo = this.busDelieryitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getFinishclosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getInvoqty() > 0 || dbPojo.getPickqty() > 0 || dbPojo.getFinishqty() > 0) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已被引用,禁止作废操作");
                    }
                    BusDelieryitemEntity entity = new BusDelieryitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    entity.setDisannuldate(new Date());
                    entity.setDisannullister(loginUser.getRealname());
                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(loginUser.getTenantid());
                    this.busDelieryitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BusDelieryitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            syncMapper.updateGoodsBusRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.busDelieryMapper.updateDisannulCountAndAmount(Pid, tid);
            //主表更改
            BusDelieryEntity busDelieryEntity = new BusDelieryEntity();
            busDelieryEntity.setId(Pid);
            busDelieryEntity.setLister(loginUser.getRealname());
            busDelieryEntity.setListerid(loginUser.getUserid());
            busDelieryEntity.setModifydate(new Date());
            busDelieryEntity.setTenantid(loginUser.getTenantid());
            this.busDelieryMapper.update(busDelieryEntity);
            //返回Bill实例
            return this.getBillEntity(busDelieryEntity.getId(), busDelieryEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDelieryPojo closed(List<BusDelieryitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "中止" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            BusDelieryitemPojo Pojo = lst.get(i);
            BusDelieryitemPojo dbPojo = this.busDelieryitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getFinishclosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    BusDelieryitemEntity entity = new BusDelieryitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setFinishclosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.busDelieryitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BusDelieryitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            syncMapper.updateGoodsBusRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.busDelieryMapper.updateFinishCount(Pid, tid);
            //主表更改
            BusDelieryEntity busDelieryEntity = new BusDelieryEntity();
            busDelieryEntity.setId(Pid);
            busDelieryEntity.setLister(loginUser.getRealname());
            busDelieryEntity.setListerid(loginUser.getUserid());
            busDelieryEntity.setModifydate(new Date());
            busDelieryEntity.setTenantid(loginUser.getTenantid());
            this.busDelieryMapper.update(busDelieryEntity);
            //返回Bill实例
            return this.getBillEntity(busDelieryEntity.getId(), busDelieryEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    // 查询Item是否被引用
    public List<String> getItemCiteBillName(String key, String pid, String tid) {
        return this.busDelieryMapper.getItemCiteBillName(key, pid, tid);
    }

    private final String PRINTBATCH_STATE = "printbatch_codes:";


    @Override
    @Async
    // 开始批量打印
    public void printBatchBillStart(List<String> ids, String uuid, String printapproved, ReportsPojo reportsPojo, LoginUser loginUser) {

        try {
            //设置当前计算任务进度
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100"); //开始处理代码
            missionMsg.put("msg", "任务开始处理");
            missionMsg.put("totalCount", ids.size());
            missionMsg.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.saRedisService.setCacheObject(MyConstant.PRINTBATCH_STATE + uuid, missionMsg, 600);
            //数据填充
            JasperPrint printAll = new JasperPrint();
            List<JasperPrint> lstPrint = new ArrayList<>();
            String content = reportsPojo.getRptdata();
            int successCount = 0;
            int failCount = 0;
            for (int a = 0; a < ids.size(); a++) {
                String key = ids.get(a);
                //获取单据信息
                BusDelieryPojo busDelieryPojo = this.getBillEntity(key, loginUser.getTenantid());
                if (busDelieryPojo == null) {
                    failCount++;
                } else if (printapproved != null && printapproved.equals("true") && busDelieryPojo.getAssessor().equals("")) {
                    failCount++;
                } else {
                    //表头转MAP
                    Map<String, Object> map = inks.common.core.utils.bean.BeanUtils.beanToMap(busDelieryPojo);
                    // 加入公司信息
                    inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
                    // 判定是否需要追行
                    if (reportsPojo.getPagerow() > 0) {
                        int index = 0;
                        // 取行余数
                        index = busDelieryPojo.getItem().size() % reportsPojo.getPagerow();
                        if (index > 0) {
                            // 补全空白行
                            for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                                BusDelieryitemPojo busDelieryitemPojo = new BusDelieryitemPojo();
                                busDelieryPojo.getItem().add(busDelieryitemPojo);
                            }
                        }
                    }
                    // 带属性List转为Map  EricRen 20220427
                    List<Map<String, Object>> lst = attrcostListToMaps(busDelieryPojo.getItem());
                    //item转数据源
                    JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
                    //报表生成
                    InputStream stream = new ByteArrayInputStream(content.getBytes());
                    //编译报表
                    JasperReport jasperReport = JasperCompileManager.compileReport(stream);
                    //数据填充
                    JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
                    if (a == 0) {
                        printAll = print;
                    } else {
                        List<JRPrintPage> pages = print.getPages();
                        for (JRPrintPage page : pages) {
                            printAll.addPage(page);
                        }
                    }
                    lstPrint.add(print);
                }
                successCount++;
                missionMsg.put("code", "150"); //任务处理中代码
                missionMsg.put("msg", "任务处理中");
                missionMsg.put("totalCount", ids.size());
                missionMsg.put("successCount", successCount);
                this.saRedisService.setCacheObject(MyConstant.PRINTBATCH_STATE + uuid, missionMsg, 600);
            }

//            //输出文件
//            String pdfPath = "D:\\test.pdf";
//            JasperExportManager.exportReportToPdfFile(printAll,pdfPath);


            byte[] base64File = StreamUtils.toByteArray(printAll);

            String cachekey = "report_pages:" + uuid;
            this.saRedisService.setKeyValue(cachekey, base64File, 60L, TimeUnit.MINUTES);

            missionMsg.put("code", "200");//任务完成代码
            missionMsg.put("msg", "任务处理完成");
            missionMsg.put("successCount", successCount);
            missionMsg.put("failCount", failCount);
            missionMsg.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.saRedisService.setCacheObject(MyConstant.PRINTBATCH_STATE + uuid, missionMsg, 600);

        } catch (Exception ignored) {

        }
    }



    @Override
    public List<BusDelieryitemPojo> getItemListByIds(String ids, String pid, String tid) {
        return this.busDelieryMapper.getItemListByIds(ids, pid, tid);
    }
}
