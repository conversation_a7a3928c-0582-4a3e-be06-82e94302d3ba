package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BusOrdercostEntity;
import inks.service.sa.som.domain.BusOrdercostitemEntity;
import inks.service.sa.som.domain.pojo.BusOrdercostPojo;
import inks.service.sa.som.domain.pojo.BusOrdercostitemPojo;
import inks.service.sa.som.domain.pojo.BusOrdercostitemdetailPojo;
import inks.service.sa.som.mapper.BusOrdercostMapper;
import inks.service.sa.som.mapper.BusOrdercostitemMapper;
import inks.service.sa.som.service.BusOrdercostService;
import inks.service.sa.som.service.BusOrdercostitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 订单成本(BusOrdercost)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-30 08:20:51
 */
@Service("busOrdercostService")
public class BusOrdercostServiceImpl implements BusOrdercostService {
    @Resource
    private BusOrdercostMapper busOrdercostMapper;

    @Resource
    private BusOrdercostitemMapper busOrdercostitemMapper;

    
    @Resource
    private BusOrdercostitemService busOrdercostitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusOrdercostPojo getEntity(String key, String tid) {
        return this.busOrdercostMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusOrdercostitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusOrdercostitemdetailPojo> lst = busOrdercostMapper.getPageList(queryParam);
            PageInfo<BusOrdercostitemdetailPojo> pageInfo = new PageInfo<BusOrdercostitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusOrdercostPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusOrdercostPojo busOrdercostPojo = this.busOrdercostMapper.getEntity(key, tid);
            //读取子表
            busOrdercostPojo.setItem(busOrdercostitemMapper.getList(busOrdercostPojo.getId(), busOrdercostPojo.getTenantid()));
            return busOrdercostPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusOrdercostPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusOrdercostPojo> lst = busOrdercostMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busOrdercostitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusOrdercostPojo> pageInfo = new PageInfo<BusOrdercostPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusOrdercostPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusOrdercostPojo> lst = busOrdercostMapper.getPageTh(queryParam);
            PageInfo<BusOrdercostPojo> pageInfo = new PageInfo<BusOrdercostPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busOrdercostPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusOrdercostPojo insert(BusOrdercostPojo busOrdercostPojo) {
//初始化NULL字段
        if (busOrdercostPojo.getRefno() == null) busOrdercostPojo.setRefno("");
        if (busOrdercostPojo.getBilltype() == null) busOrdercostPojo.setBilltype("");
        if (busOrdercostPojo.getBilltitle() == null) busOrdercostPojo.setBilltitle("");
        if (busOrdercostPojo.getBilldate() == null) busOrdercostPojo.setBilldate(new Date());
        if (busOrdercostPojo.getProjectid() == null) busOrdercostPojo.setProjectid("");
        if (busOrdercostPojo.getProjname() == null) busOrdercostPojo.setProjname("");
        if (busOrdercostPojo.getProjcode() == null) busOrdercostPojo.setProjcode("");
        if (busOrdercostPojo.getProbability() == null) busOrdercostPojo.setProbability("");
        if (busOrdercostPojo.getGroupid() == null) busOrdercostPojo.setGroupid("");
        if (busOrdercostPojo.getTradercode() == null) busOrdercostPojo.setTradercode("");
        if (busOrdercostPojo.getTradername() == null) busOrdercostPojo.setTradername("");
        if (busOrdercostPojo.getCustaddress() == null) busOrdercostPojo.setCustaddress("");
        if (busOrdercostPojo.getLinkman() == null) busOrdercostPojo.setLinkman("");
        if (busOrdercostPojo.getCusttel() == null) busOrdercostPojo.setCusttel("");
        if (busOrdercostPojo.getCustfax() == null) busOrdercostPojo.setCustfax("");
        if (busOrdercostPojo.getPeriods() == null) busOrdercostPojo.setPeriods("");
        if (busOrdercostPojo.getValiditydate() == null) busOrdercostPojo.setValiditydate("");
        if (busOrdercostPojo.getCurrency() == null) busOrdercostPojo.setCurrency("");
        if (busOrdercostPojo.getDelivery() == null) busOrdercostPojo.setDelivery("");
        if (busOrdercostPojo.getPayment() == null) busOrdercostPojo.setPayment("");
        if (busOrdercostPojo.getBillclause() == null) busOrdercostPojo.setBillclause("");
        if (busOrdercostPojo.getOperator() == null) busOrdercostPojo.setOperator("");
        if (busOrdercostPojo.getBilltaxamount() == null) busOrdercostPojo.setBilltaxamount(0D);
        if (busOrdercostPojo.getBillamount() == null) busOrdercostPojo.setBillamount(0D);
        if (busOrdercostPojo.getBilltaxtotal() == null) busOrdercostPojo.setBilltaxtotal(0D);
        if (busOrdercostPojo.getSummary() == null) busOrdercostPojo.setSummary("");
        if (busOrdercostPojo.getCreateby() == null) busOrdercostPojo.setCreateby("");
        if (busOrdercostPojo.getCreatebyid() == null) busOrdercostPojo.setCreatebyid("");
        if (busOrdercostPojo.getCreatedate() == null) busOrdercostPojo.setCreatedate(new Date());
        if (busOrdercostPojo.getLister() == null) busOrdercostPojo.setLister("");
        if (busOrdercostPojo.getListerid() == null) busOrdercostPojo.setListerid("");
        if (busOrdercostPojo.getModifydate() == null) busOrdercostPojo.setModifydate(new Date());
        if (busOrdercostPojo.getSubmitterid() == null) busOrdercostPojo.setSubmitterid("");
        if (busOrdercostPojo.getSubmitter() == null) busOrdercostPojo.setSubmitter("");
        if (busOrdercostPojo.getSubmitdate() == null) busOrdercostPojo.setSubmitdate(new Date());
        if (busOrdercostPojo.getAssessor() == null) busOrdercostPojo.setAssessor("");
        if (busOrdercostPojo.getAssessorid() == null) busOrdercostPojo.setAssessorid("");
        if (busOrdercostPojo.getAssessdate() == null) busOrdercostPojo.setAssessdate(new Date());
        if (busOrdercostPojo.getItemcount() == null) busOrdercostPojo.setItemcount(busOrdercostPojo.getItem().size());
        if (busOrdercostPojo.getFinishcount() == null) busOrdercostPojo.setFinishcount(0);
        if (busOrdercostPojo.getDisannulcount() == null) busOrdercostPojo.setDisannulcount(0);
        if (busOrdercostPojo.getPrintcount() == null) busOrdercostPojo.setPrintcount(0);
        if (busOrdercostPojo.getSalesman() == null) busOrdercostPojo.setSalesman("");
        if (busOrdercostPojo.getSalesmanid() == null) busOrdercostPojo.setSalesmanid("");
        if (busOrdercostPojo.getCustom1() == null) busOrdercostPojo.setCustom1("");
        if (busOrdercostPojo.getCustom2() == null) busOrdercostPojo.setCustom2("");
        if (busOrdercostPojo.getCustom3() == null) busOrdercostPojo.setCustom3("");
        if (busOrdercostPojo.getCustom4() == null) busOrdercostPojo.setCustom4("");
        if (busOrdercostPojo.getCustom5() == null) busOrdercostPojo.setCustom5("");
        if (busOrdercostPojo.getCustom6() == null) busOrdercostPojo.setCustom6("");
        if (busOrdercostPojo.getCustom7() == null) busOrdercostPojo.setCustom7("");
        if (busOrdercostPojo.getCustom8() == null) busOrdercostPojo.setCustom8("");
        if (busOrdercostPojo.getCustom9() == null) busOrdercostPojo.setCustom9("");
        if (busOrdercostPojo.getCustom10() == null) busOrdercostPojo.setCustom10("");
        if (busOrdercostPojo.getDeptid() == null) busOrdercostPojo.setDeptid("");
        if (busOrdercostPojo.getTenantid() == null) busOrdercostPojo.setTenantid("");
        if (busOrdercostPojo.getTenantname() == null) busOrdercostPojo.setTenantname("");
        if (busOrdercostPojo.getRevision() == null) busOrdercostPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusOrdercostEntity busOrdercostEntity = new BusOrdercostEntity();
        BeanUtils.copyProperties(busOrdercostPojo, busOrdercostEntity);
        //设置id和新建日期
        busOrdercostEntity.setId(id);
        busOrdercostEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busOrdercostMapper.insert(busOrdercostEntity);
        //Item子表处理
        List<BusOrdercostitemPojo> lst = busOrdercostPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                BusOrdercostitemPojo itemPojo = this.busOrdercostitemService.clearNull(lst.get(i));
                BusOrdercostitemEntity busOrdercostitemEntity = new BusOrdercostitemEntity();
                BeanUtils.copyProperties(itemPojo, busOrdercostitemEntity);
                //设置id和Pid
                busOrdercostitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busOrdercostitemEntity.setPid(id);
                busOrdercostitemEntity.setTenantid(busOrdercostPojo.getTenantid());
                busOrdercostitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busOrdercostitemMapper.insert(busOrdercostitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(busOrdercostEntity.getId(), busOrdercostEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busOrdercostPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusOrdercostPojo update(BusOrdercostPojo busOrdercostPojo) {
        //主表更改
        BusOrdercostEntity busOrdercostEntity = new BusOrdercostEntity();
        if (busOrdercostPojo.getItem() != null) busOrdercostPojo.setItemcount(busOrdercostPojo.getItem().size());
        BeanUtils.copyProperties(busOrdercostPojo, busOrdercostEntity);
        this.busOrdercostMapper.update(busOrdercostEntity);
        if (busOrdercostPojo.getItem() != null) {
            //Item子表处理
            List<BusOrdercostitemPojo> lst = busOrdercostPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = busOrdercostMapper.getDelItemIds(busOrdercostPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.busOrdercostitemMapper.delete(lstDelIds.get(i), busOrdercostEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    BusOrdercostitemEntity busOrdercostitemEntity = new BusOrdercostitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        BusOrdercostitemPojo itemPojo = this.busOrdercostitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, busOrdercostitemEntity);
                        //设置id和Pid
                        busOrdercostitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busOrdercostitemEntity.setPid(busOrdercostEntity.getId());  // 主表 id
                        busOrdercostitemEntity.setTenantid(busOrdercostPojo.getTenantid());   // 租户id
                        busOrdercostitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busOrdercostitemMapper.insert(busOrdercostitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), busOrdercostitemEntity);
                        busOrdercostitemEntity.setTenantid(busOrdercostPojo.getTenantid());
                        this.busOrdercostitemMapper.update(busOrdercostitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(busOrdercostEntity.getId(), busOrdercostEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BusOrdercostPojo busOrdercostPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BusOrdercostitemPojo> lst = busOrdercostPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.busOrdercostitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.busOrdercostMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param busOrdercostPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusOrdercostPojo approval(BusOrdercostPojo busOrdercostPojo) {
        //主表更改
        BusOrdercostEntity busOrdercostEntity = new BusOrdercostEntity();
        BeanUtils.copyProperties(busOrdercostPojo, busOrdercostEntity);
        this.busOrdercostMapper.approval(busOrdercostEntity);
        //返回Bill实例
        return this.getBillEntity(busOrdercostEntity.getId(), busOrdercostEntity.getTenantid());
    }
    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusOrdercostPojo disannul(List<BusOrdercostitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            BusOrdercostitemPojo Pojo = lst.get(i);
            BusOrdercostitemPojo dbPojo = this.busOrdercostitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (dbPojo.getDisannulmark() != type) {
                    if (Pid.equals("")) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getMachmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已转订单,禁止作废操作");
                    }
                    BusOrdercostitemEntity entity = new BusOrdercostitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
//                    entity.setDisannuldate(new Date());
//                    entity.setDisannullister(loginUser.getRealname());
//                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(loginUser.getTenantid());
                    this.busOrdercostitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.busOrdercostMapper.updateDisannulCount(Pid, tid);
            //主表更改
            BusOrdercostEntity busOrdercostEntity = new BusOrdercostEntity();
            busOrdercostEntity.setId(Pid);
            busOrdercostEntity.setLister(loginUser.getRealname());
            busOrdercostEntity.setListerid(loginUser.getUserid());
            busOrdercostEntity.setModifydate(new Date());
            busOrdercostEntity.setTenantid(loginUser.getTenantid());
            this.busOrdercostMapper.update(busOrdercostEntity);
            //返回Bill实例
            return this.getBillEntity(busOrdercostEntity.getId(), busOrdercostEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusOrdercostPojo closed(List<BusOrdercostitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            BusOrdercostitemPojo Pojo = lst.get(i);
            BusOrdercostitemPojo dbPojo = this.busOrdercostitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (dbPojo.getClosed() != type) {
                    if (Pid.equals("")) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    BusOrdercostitemEntity entity = new BusOrdercostitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.busOrdercostitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.busOrdercostMapper.updateFinishCount(Pid, tid);
            //主表更改
            BusOrdercostEntity busOrdercostEntity = new BusOrdercostEntity();
            busOrdercostEntity.setId(Pid);
            busOrdercostEntity.setLister(loginUser.getRealname());
            busOrdercostEntity.setListerid(loginUser.getUserid());
            busOrdercostEntity.setModifydate(new Date());
            busOrdercostEntity.setTenantid(loginUser.getTenantid());
            this.busOrdercostMapper.update(busOrdercostEntity);
            //返回Bill实例
            return this.getBillEntity(busOrdercostEntity.getId(), busOrdercostEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    public List<String> getItemCiteBillName(String key, String pid, String tid) {
        return  this.busOrdercostMapper.getItemCiteBillName(key, pid, tid);
    }
}
