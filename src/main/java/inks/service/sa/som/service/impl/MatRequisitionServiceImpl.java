package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatRequisitionEntity;
import inks.service.sa.som.domain.MatRequisitionitemEntity;
import inks.service.sa.som.domain.pojo.MatRequisitionPojo;
import inks.service.sa.som.domain.pojo.MatRequisitionitemPojo;
import inks.service.sa.som.domain.pojo.MatRequisitionitemdetailPojo;
import inks.service.sa.som.mapper.MatRequisitionMapper;
import inks.service.sa.som.mapper.MatRequisitionitemMapper;
import inks.service.sa.som.mapper.SyncMapper;
import inks.service.sa.som.service.MatGoodsService;
import inks.service.sa.som.service.MatRequisitionService;
import inks.service.sa.som.service.MatRequisitionitemService;
import inks.sa.common.core.service.SaBillcodeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 物料申领(MatRequisition)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-25 13:06:04
 */
@Service("matRequisitionService")
public class MatRequisitionServiceImpl implements MatRequisitionService {
    @Resource
    private MatRequisitionMapper matRequisitionMapper;

    @Resource
    private MatRequisitionitemMapper matRequisitionitemMapper;

    
    @Resource
    private MatRequisitionitemService matRequisitionitemService;



    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private MatGoodsService matGoodsService;
    @Resource
    private SyncMapper syncMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatRequisitionPojo getEntity(String key, String tid) {
        return this.matRequisitionMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatRequisitionitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatRequisitionitemdetailPojo> lst = matRequisitionMapper.getPageList(queryParam);
            PageInfo<MatRequisitionitemdetailPojo> pageInfo = new PageInfo<MatRequisitionitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatRequisitionPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            MatRequisitionPojo matRequisitionPojo = this.matRequisitionMapper.getEntity(key, tid);
            //读取子表
            matRequisitionPojo.setItem(matRequisitionitemMapper.getList(matRequisitionPojo.getId(), matRequisitionPojo.getTenantid()));
            return matRequisitionPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatRequisitionPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatRequisitionPojo> lst = matRequisitionMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(matRequisitionitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<MatRequisitionPojo> pageInfo = new PageInfo<MatRequisitionPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatRequisitionPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatRequisitionPojo> lst = matRequisitionMapper.getPageTh(queryParam);
            PageInfo<MatRequisitionPojo> pageInfo = new PageInfo<MatRequisitionPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matRequisitionPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public MatRequisitionPojo insert(MatRequisitionPojo matRequisitionPojo, String token) {
//初始化NULL字段
        if (matRequisitionPojo.getRefno() == null) matRequisitionPojo.setRefno("");
        if (matRequisitionPojo.getBilltype() == null) matRequisitionPojo.setBilltype("");
        if (matRequisitionPojo.getBilldate() == null) matRequisitionPojo.setBilldate(new Date());
        if (matRequisitionPojo.getBilltitle() == null) matRequisitionPojo.setBilltitle("");
        if (matRequisitionPojo.getGroupid() == null) matRequisitionPojo.setGroupid("");
        if (matRequisitionPojo.getBranthname() == null) matRequisitionPojo.setBranthname("");
        if (matRequisitionPojo.getOperator() == null) matRequisitionPojo.setOperator("");
        if (matRequisitionPojo.getOperatorid() == null) matRequisitionPojo.setOperatorid("");
        if (matRequisitionPojo.getSummary() == null) matRequisitionPojo.setSummary("");
        if (matRequisitionPojo.getCreateby() == null) matRequisitionPojo.setCreateby("");
        if (matRequisitionPojo.getCreatebyid() == null) matRequisitionPojo.setCreatebyid("");
        if (matRequisitionPojo.getCreatedate() == null) matRequisitionPojo.setCreatedate(new Date());
        if (matRequisitionPojo.getLister() == null) matRequisitionPojo.setLister("");
        if (matRequisitionPojo.getListerid() == null) matRequisitionPojo.setListerid("");
        if (matRequisitionPojo.getModifydate() == null) matRequisitionPojo.setModifydate(new Date());
        if (matRequisitionPojo.getAssessor() == null) matRequisitionPojo.setAssessor("");
        if (matRequisitionPojo.getAssessorid() == null) matRequisitionPojo.setAssessorid("");
        if (matRequisitionPojo.getAssessdate() == null) matRequisitionPojo.setAssessdate(new Date());
        if (matRequisitionPojo.getBillstatecode() == null) matRequisitionPojo.setBillstatecode("");
        if (matRequisitionPojo.getBillstatedate() == null) matRequisitionPojo.setBillstatedate(new Date());
        if (matRequisitionPojo.getModulecode() == null) matRequisitionPojo.setModulecode("");
        if (matRequisitionPojo.getCiteuid() == null) matRequisitionPojo.setCiteuid("");
        if (matRequisitionPojo.getCiteid() == null) matRequisitionPojo.setCiteid("");
        if (matRequisitionPojo.getDisannulcount() == null) matRequisitionPojo.setDisannulcount(0);
        if (matRequisitionPojo.getItemcount() == null)
            matRequisitionPojo.setItemcount(matRequisitionPojo.getItem().size());
        if (matRequisitionPojo.getPickcount() == null) matRequisitionPojo.setPickcount(0);
        if (matRequisitionPojo.getFinishcount() == null) matRequisitionPojo.setFinishcount(0);
        if (matRequisitionPojo.getPrintcount() == null) matRequisitionPojo.setPrintcount(0);
        if (matRequisitionPojo.getProjectid() == null) matRequisitionPojo.setProjectid("");
        if (matRequisitionPojo.getProjcode() == null) matRequisitionPojo.setProjcode("");
        if (matRequisitionPojo.getProjname() == null) matRequisitionPojo.setProjname("");
        if (matRequisitionPojo.getOaflowmark() == null) matRequisitionPojo.setOaflowmark(0);
        if (matRequisitionPojo.getCustom1() == null) matRequisitionPojo.setCustom1("");
        if (matRequisitionPojo.getCustom2() == null) matRequisitionPojo.setCustom2("");
        if (matRequisitionPojo.getCustom3() == null) matRequisitionPojo.setCustom3("");
        if (matRequisitionPojo.getCustom4() == null) matRequisitionPojo.setCustom4("");
        if (matRequisitionPojo.getCustom5() == null) matRequisitionPojo.setCustom5("");
        if (matRequisitionPojo.getCustom6() == null) matRequisitionPojo.setCustom6("");
        if (matRequisitionPojo.getCustom7() == null) matRequisitionPojo.setCustom7("");
        if (matRequisitionPojo.getCustom8() == null) matRequisitionPojo.setCustom8("");
        if (matRequisitionPojo.getCustom9() == null) matRequisitionPojo.setCustom9("");
        if (matRequisitionPojo.getCustom10() == null) matRequisitionPojo.setCustom10("");
        String tid = matRequisitionPojo.getTenantid();
        if (tid == null) matRequisitionPojo.setTenantid("");
        if (matRequisitionPojo.getTenantname() == null) matRequisitionPojo.setTenantname("");
        if (matRequisitionPojo.getRevision() == null) matRequisitionPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        MatRequisitionEntity matRequisitionEntity = new MatRequisitionEntity();
        BeanUtils.copyProperties(matRequisitionPojo, matRequisitionEntity);
        //设置id和新建日期
        matRequisitionEntity.setId(id);
        matRequisitionEntity.setRevision(1);  //乐观锁

        //Item子表处理
        List<MatRequisitionitemPojo> lst = matRequisitionPojo.getItem();
        // 读取指定系统参数 生产加工单是否允许超数申领
//        String requoverflow = systemFeignService.getConfigValue("module.manu.requoverflow", tid, token).getData();
//        if (!"true".equals(requoverflow)) {
            if (lst != null) {
                for (int i = 0; i < lst.size(); i++) {
                    MatRequisitionitemPojo ReqItemEntity = lst.get(i);
                    if (ReqItemEntity.getWkbilltype() != null && ReqItemEntity.getWkbilltype() > 0) {
                        // B1 同步加工单、委外单物料
                        if (!"".equals(ReqItemEntity.getWorkitemmatid())) {
                            double matQuantity = 0;
                            double matFinishQty = 0;
                            // 生产加工单
                            if (ReqItemEntity.getWkbilltype() == 1) {
//                        select Quantity,FinishQty from Wk_WorksheetMat
                                Map<String, Object> sheetMatInfo = matRequisitionMapper.getSheetMatInfo(ReqItemEntity.getWorkitemmatid(), matRequisitionEntity.getTenantid());

                                if (!sheetMatInfo.isEmpty()) {
                                    matQuantity = Double.parseDouble(sheetMatInfo.get("Quantity").toString());
                                    matFinishQty = Double.parseDouble(sheetMatInfo.get("FinishQty").toString());
                                } else {
                                    throw new RuntimeException("关联单据丢失");
                                }
                            } else if (ReqItemEntity.getWkbilltype() == 2) {
                                // 委外加工单
//                        select Quantity,FinishQty from Wk_SubcontractMat
                                Map<String, Object> sheetMatInfo = matRequisitionMapper.getSubMatInfo(ReqItemEntity.getWorkitemmatid(), matRequisitionEntity.getTenantid());
                                if (!sheetMatInfo.isEmpty()) {
                                    matQuantity = Double.parseDouble(sheetMatInfo.get("Quantity").toString());
                                    matFinishQty = Double.parseDouble(sheetMatInfo.get("FinishQty").toString());
                                } else {
                                    throw new RuntimeException("关联单据丢失");
                                }
                            }
                            if (matFinishQty + ReqItemEntity.getQuantity() > matQuantity) {
                                int Rowno = i + 1;
                                throw new RuntimeException(Rowno + "行,领料总数:" + (matFinishQty + ReqItemEntity.getQuantity()) + "超出需求数:" + matQuantity);
                            }
                        }
                    }
                }
            }
//        }

//        transactionTemplate.execute((status) -> {
        //插入主表
        this.matRequisitionMapper.insert(matRequisitionEntity);

        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                MatRequisitionitemPojo itemPojo = this.matRequisitionitemService.clearNull(lst.get(i));
                MatRequisitionitemEntity matRequisitionitemEntity = new MatRequisitionitemEntity();
                BeanUtils.copyProperties(itemPojo, matRequisitionitemEntity);
                //设置id和Pid
                matRequisitionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matRequisitionitemEntity.setPid(id);
                matRequisitionitemEntity.setTenantid(tid);
                matRequisitionitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matRequisitionitemMapper.insert(matRequisitionitemEntity);
                // 同步单据数量
                if ("".equals(matRequisitionitemEntity.getWorkitemmatid())) {
                    // P1 同步主料状态
                    if (!"".equals(matRequisitionitemEntity.getMachitemid()) || !"".equals(matRequisitionitemEntity.getWorkitemid())) {
                        this.matRequisitionMapper.updateWipMatUsed(matRequisitionitemEntity.getMachitemid(), tid);
                        // 销售订单转WIP
                        if (!"".equals(matRequisitionitemEntity.getMachitemid())) {
                            this.matRequisitionMapper.updateMachMatUsed(matRequisitionitemEntity.getMachitemid(), tid);
                            if (matRequisitionitemEntity.getSourcetype() == 2) { //sourcetype==2 同步订单子表的物料领料数MatUseQty
                                this.matRequisitionMapper.updateMachMatUseQty(matRequisitionitemEntity.getMachitemid(), tid);
                            }
                        }

                        // 生产加工单转WIP
                        if (!"".equals(matRequisitionitemEntity.getWorkitemid()))
                            this.matRequisitionMapper.updateWsMatUsed(matRequisitionitemEntity.getWorkitemid(), tid);
                    }
                } else {
                    // B1 同步加工单、委外单物料
                    if (!"".equals(matRequisitionitemEntity.getWorkitemmatid()) && matRequisitionitemEntity.getWkbilltype() > 0) {
                        // 生产加工单
                        if (matRequisitionitemEntity.getWkbilltype() == 1) {
                            this.matRequisitionMapper.updateWkWsMatFinishCount(matRequisitionitemEntity.getWorkitemmatid(), matRequisitionEntity.getTenantid());
                        } else if (matRequisitionitemEntity.getWkbilltype() == 2) {
                            // 委外加工单
                            this.matRequisitionMapper.updateWkScMatFinishCount(matRequisitionitemEntity.getWorkitemmatid(), matRequisitionEntity.getTenantid());
                        }
                    }
                }
                // 如果是MRP需求
                if (isNotBlank(itemPojo.getMrpitemid())) {
                    this.matRequisitionMapper.updateMrpMatReqQty(itemPojo.getMrpitemid(), tid);
//                    WkMrpitemPojo wkMrpitemPojo = this.matCustsuppMapper.getMrpItemEntity(requisitionItem.getMrpitemid(), tid);
//                    if (wkMrpitemPojo != null && wkMrpitemPojo.getBuyorderqty() + wkMrpitemPojo.getBuyplanqty() > wkMrpitemPojo.getNeedqty()) {
//                        double wkqty = wkMrpitemPojo.getBuyorderqty() + wkMrpitemPojo.getBuyplanqty();
//                        throw new RuntimeException(buyOrderitemPojo.getGoodsuid() + ":采购总数" + wkqty + "大于需求数" + wkMrpitemPojo.getNeedqty());
//                    }
//                     刷新MRP完工数
//                    this.matRequisitionMapper.updateMrpFinishCount(requisitionItem.getMrpitemid(), tid);
                }
            }
        }
//            return Boolean.TRUE;
//        });
        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(MatRequisitionitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            syncMapper.updateGoodsRequRemQty(goodsid, tid);
        });

        //返回Bill实例
        return this.getBillEntity(matRequisitionEntity.getId(), matRequisitionEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matRequisitionPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatRequisitionPojo update(MatRequisitionPojo matRequisitionPojo) {
        //主表更改
        MatRequisitionEntity matRequisitionEntity = new MatRequisitionEntity();
        BeanUtils.copyProperties(matRequisitionPojo, matRequisitionEntity);
        matRequisitionEntity.setItemcount(matRequisitionPojo.getItem().size());
        String tid = matRequisitionEntity.getTenantid();
        //Item子表处理
        List<MatRequisitionitemPojo> lst = matRequisitionPojo.getItem();
        List<MatRequisitionitemPojo> lstDel = new ArrayList<>();

//        if (lst != null) {
//            for (int i = 0; i < lst.size(); i++) {
//                MatRequisitionitemPojo ReqItemEntity = lst.get(i);
//                // B1 同步加工单、委外单物料
//                if (!"".equals(ReqItemEntity.getWorkitemmatid()) && ReqItemEntity.getWkbilltype() > 0) {
//                    double matQuantity = 0;
//                    double matFinishQty = 0;
//                    // 生产加工单
//                    if (ReqItemEntity.getWkbilltype() == 1) {
////                        select Quantity,FinishQty from Wk_WorksheetMat
//                        Map<String, Object> sheetMatInfo = matRequisitionMapper.getSheetMatInfo(ReqItemEntity.getWorkitemmatid(), matRequisitionEntity.getTenantid());
//
//                        if (!sheetMatInfo.isEmpty()) {
//                            matQuantity = Double.parseDouble(sheetMatInfo.get("Quantity").toString());
//                            matFinishQty = Double.parseDouble(sheetMatInfo.get("FinishQty").toString());
//                        } else {
//                            throw new RuntimeException("关联单据丢失");
//                        }
//                    } else if (ReqItemEntity.getWkbilltype() == 2) {
//                        // 委外加工单
////                        select Quantity,FinishQty from Wk_SubcontractMat
//                        Map<String, Object> sheetMatInfo = matRequisitionMapper.getSubMatInfo(ReqItemEntity.getWorkitemmatid(), matRequisitionEntity.getTenantid());
//                        if (!sheetMatInfo.isEmpty()) {
//                            matQuantity = Double.parseDouble(sheetMatInfo.get("Quantity").toString());
//                            matFinishQty = Double.parseDouble(sheetMatInfo.get("FinishQty").toString());
//                        } else {
//                            throw new RuntimeException("关联单据丢失");
//                        }
//                    }
//                    if (matFinishQty + ReqItemEntity.getQuantity() > matQuantity) {
//                        int Rowno = i + 1;
//                        throw new RuntimeException(Rowno + "行,领料总数:" + (matFinishQty + ReqItemEntity.getQuantity()) + "超出需求数:" + matQuantity);
//                    }
//                }
//            }
//        }

        transactionTemplate.execute((status) -> {
            this.matRequisitionMapper.update(matRequisitionEntity);
            if (matRequisitionPojo.getItem() != null) {
                //获取被删除的Item
                List<String> lstDelIds = matRequisitionMapper.getDelItemIds(matRequisitionPojo);
                if (lstDelIds != null) {
                    //循环每个删除item子表
                    for (int i = 0; i < lstDelIds.size(); i++) {
                        this.matRequisitionitemMapper.delete(lstDelIds.get(i), tid);
                        MatRequisitionitemPojo dbPojo = this.matRequisitionitemMapper.getEntity(lstDelIds.get(i), tid);
                        lstDel.add(dbPojo);
                        // 同步单据数量
                        if ("".equals(dbPojo.getWorkitemmatid())) {
                            // P1 同步主料状态
                            if (!"".equals(dbPojo.getMachitemid()) || !"".equals(dbPojo.getWorkitemid())) {
                                this.matRequisitionMapper.updateUnWipMatUsed(dbPojo.getMachitemid(), matRequisitionPojo.getTenantid());
                                if (!"".equals(dbPojo.getMachitemid()))
                                    this.matRequisitionMapper.updateUnMachMatUsed(dbPojo.getMachitemid(), matRequisitionPojo.getTenantid());
                                if (!"".equals(dbPojo.getWorkitemid()))
                                    this.matRequisitionMapper.updateUnWsMatUsed(dbPojo.getWorkitemid(), matRequisitionPojo.getTenantid());
                            }
                        } else {
                            // B1 同步加工单、委外单物料
                            if (!"".equals(dbPojo.getWorkitemid()) && dbPojo.getWkbilltype() > 0) {
                                if (dbPojo.getWkbilltype() == 1) {
                                    this.matRequisitionMapper.updateWkWsMatFinishCount(dbPojo.getWorkitemmatid(), tid);
                                } else if (dbPojo.getWkbilltype() == 2) {
                                    this.matRequisitionMapper.updateWkScMatFinishCount(dbPojo.getWorkitemmatid(), tid);
                                }
                            }
                        }
                        // 如果是MRP需求
                        if (isNotBlank(dbPojo.getMrpitemid())) {
                            this.matRequisitionMapper.updateMrpMatReqQty(dbPojo.getMrpitemid(), tid);
                        }
                    }
                }
                if (lst != null) {
                    //循环每个item子表
                    for (MatRequisitionitemPojo matRequisitionitemPojo : lst) {
                        MatRequisitionitemEntity matRequisitionitemEntity = new MatRequisitionitemEntity();
                        if ("".equals(matRequisitionitemPojo.getId()) || matRequisitionitemPojo.getId() == null) {
                            //初始化item的NULL
                            MatRequisitionitemPojo itemPojo = this.matRequisitionitemService.clearNull(matRequisitionitemPojo);
                            BeanUtils.copyProperties(itemPojo, matRequisitionitemEntity);
                            //设置id和Pid
                            matRequisitionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                            matRequisitionitemEntity.setPid(matRequisitionEntity.getId());  // 主表 id
                            matRequisitionitemEntity.setTenantid(matRequisitionPojo.getTenantid());   // 租户id
                            matRequisitionitemEntity.setRevision(1);  // 乐观锁
                            //插入子表
                            this.matRequisitionitemMapper.insert(matRequisitionitemEntity);

                        } else {
                            BeanUtils.copyProperties(matRequisitionitemPojo, matRequisitionitemEntity);
                            matRequisitionitemEntity.setTenantid(matRequisitionPojo.getTenantid());
                            this.matRequisitionitemMapper.update(matRequisitionitemEntity);
                        }

                        // 同步单据数量
                        if ("".equals(matRequisitionitemEntity.getWorkitemmatid())) {
                            // P1 同步主料状态
                            if (!"".equals(matRequisitionitemEntity.getMachitemid()) || !"".equals(matRequisitionitemEntity.getWorkitemid())) {
                                this.matRequisitionMapper.updateWipMatUsed(matRequisitionitemEntity.getMachitemid(), matRequisitionPojo.getTenantid());
                                // 销售订单转WIP
                                if (!"".equals(matRequisitionitemEntity.getMachitemid()))
                                    this.matRequisitionMapper.updateMachMatUsed(matRequisitionitemEntity.getMachitemid(), matRequisitionPojo.getTenantid());
                                // 生产加工单转WIP
                                if (!"".equals(matRequisitionitemEntity.getWorkitemid()))
                                    this.matRequisitionMapper.updateWsMatUsed(matRequisitionitemEntity.getWorkitemid(), matRequisitionPojo.getTenantid());
                            }
                        } else {
                            // B1 同步加工单、委外单物料
                            if (!"".equals(matRequisitionitemEntity.getWorkitemmatid()) && matRequisitionitemEntity.getWkbilltype() > 0) {
                                // 生产加工单
                                if (matRequisitionitemEntity.getWkbilltype() == 1) {
                                    this.matRequisitionMapper.updateWkWsMatFinishCount(matRequisitionitemEntity.getWorkitemmatid(), tid);
                                } else if (matRequisitionitemEntity.getWkbilltype() == 2) {
                                    // 委外加工单
                                    this.matRequisitionMapper.updateWkScMatFinishCount(matRequisitionitemEntity.getWorkitemmatid(), tid);
                                }
                            }
                        }
                        // 如果是MRP需求
                        if (isNotBlank(matRequisitionitemPojo.getMrpitemid())) {
                            this.matRequisitionMapper.updateMrpMatReqQty(matRequisitionitemPojo.getMrpitemid(), tid);
                        }

                    }
                }
            }
            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(MatRequisitionitemPojo::getGoodsid).collect(Collectors.toSet());
            Set<String> goodsidLstDelSet = lstDel.stream().map(MatRequisitionitemPojo::getGoodsid).collect(Collectors.toSet());
            goodsidLstSet.addAll(goodsidLstDelSet);
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                syncMapper.updateGoodsRequRemQty(goodsid, tid);
            });
            return Boolean.TRUE;
        });

        //返回Bill实例
        return this.getBillEntity(matRequisitionEntity.getId(), tid);
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        MatRequisitionPojo matRequisitionPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<MatRequisitionitemPojo> lst = matRequisitionPojo.getItem();
        Integer delNum = 0;
        delNum = transactionTemplate.execute((status) -> {
            if (lst != null) {
                //循环每个删除item子表
                for (int i = 0; i < lst.size(); i++) {
                    this.matRequisitionitemMapper.delete(lst.get(i).getId(), tid);
                    // 同步单据数量
                    if ("".equals(lst.get(i).getWorkitemmatid())) {
                        // P1 同步主料状态
                        if (!"".equals(lst.get(i).getMachitemid()) || !"".equals(lst.get(i).getWorkitemid())) {
                            this.matRequisitionMapper.updateUnWipMatUsed(lst.get(i).getMachitemid(), matRequisitionPojo.getTenantid());
                            // 销售订单转WIP
                            if (!"".equals(lst.get(i).getMachitemid()))
                                this.matRequisitionMapper.updateUnMachMatUsed(lst.get(i).getMachitemid(), matRequisitionPojo.getTenantid());
                            // 生产加工单转WIP
                            if (!"".equals(lst.get(i).getWorkitemid()))
                                this.matRequisitionMapper.updateUnWsMatUsed(lst.get(i).getWorkitemid(), matRequisitionPojo.getTenantid());
                        }
                    } else {
                        // B1 同步加工单、委外单物料
                        if (!"".equals(lst.get(i).getWorkitemmatid()) && lst.get(i).getWkbilltype() > 0) {
                            if (lst.get(i).getWkbilltype() == 1) {
                                // 生产加工单物料
                                this.matRequisitionMapper.updateWkWsMatFinishCount(lst.get(i).getWorkitemmatid(), matRequisitionPojo.getTenantid());
                            } else if (lst.get(i).getWkbilltype() == 2) {
                                // 委外加工单物料
                                this.matRequisitionMapper.updateWkScMatFinishCount(lst.get(i).getWorkitemmatid(), matRequisitionPojo.getTenantid());
                            }
                        }
                    }
                    // 如果是MRP需求
                    if (isNotBlank(lst.get(i).getMrpitemid())) {
                        this.matRequisitionMapper.updateMrpMatReqQty(lst.get(i).getMrpitemid(), tid);
                    }
                }
            }
            Integer _num = this.matRequisitionMapper.delete(key, tid);
            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(MatRequisitionitemPojo::getGoodsid).collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                syncMapper.updateGoodsRequRemQty(goodsid, tid);
            });
            return _num;
        });

        return delNum;
    }


    /**
     * 审核数据
     *
     * @param matRequisitionPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatRequisitionPojo approval(MatRequisitionPojo matRequisitionPojo) {
        //主表更改
        MatRequisitionEntity matRequisitionEntity = new MatRequisitionEntity();
        BeanUtils.copyProperties(matRequisitionPojo, matRequisitionEntity);
        this.matRequisitionMapper.approval(matRequisitionEntity);
        //返回Bill实例
        return this.getBillEntity(matRequisitionEntity.getId(), matRequisitionEntity.getTenantid());
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatRequisitionPojo disannul(List<MatRequisitionitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "启用";
        for (int i = 0; i < lst.size(); i++) {
            MatRequisitionitemPojo Pojo = lst.get(i);
            MatRequisitionitemPojo dbPojo = this.matRequisitionitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if ("".equals(Pid)) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getBackqty() > 0 || dbPojo.getPickqty() > 0 || dbPojo.getFinishqty() > 0) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已被引用,禁止作废操作");
                    }
                    MatRequisitionitemEntity entity = new MatRequisitionitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
//                    entity.setDisannuldate(new Date());
//                    entity.setDisannullister(loginUser.getRealname());
//                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(loginUser.getTenantid());
                    this.matRequisitionitemMapper.update(entity);
                    // 如果是MRP需求
                    if (isNotBlank(dbPojo.getMrpitemid())) {
                        this.matRequisitionMapper.updateMrpMatReqQty(dbPojo.getMrpitemid(), tid);
                    }
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(MatRequisitionitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            syncMapper.updateGoodsRequRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.matRequisitionMapper.updateDisannulCount(Pid, tid);
            //主表更改
            MatRequisitionEntity matRequisitionEntity = new MatRequisitionEntity();
            matRequisitionEntity.setId(Pid);
            matRequisitionEntity.setLister(loginUser.getRealname());
            matRequisitionEntity.setListerid(loginUser.getUserid());
            matRequisitionEntity.setModifydate(new Date());
            matRequisitionEntity.setTenantid(loginUser.getTenantid());
            this.matRequisitionMapper.update(matRequisitionEntity);
            //返回Bill实例
            return this.getBillEntity(matRequisitionEntity.getId(), matRequisitionEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 中止数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatRequisitionPojo closed(List<MatRequisitionitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "启用";
        for (int i = 0; i < lst.size(); i++) {
            MatRequisitionitemPojo Pojo = lst.get(i);
            MatRequisitionitemPojo dbPojo = this.matRequisitionitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if ("".equals(Pid)) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }

                    MatRequisitionitemEntity entity = new MatRequisitionitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.matRequisitionitemMapper.update(entity);
                    // 如果是MRP需求
                    if (isNotBlank(dbPojo.getMrpitemid())) {
                        this.matRequisitionMapper.updateMrpMatReqQty(dbPojo.getMrpitemid(), tid);
                    }
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(MatRequisitionitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            syncMapper.updateGoodsRequRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.matRequisitionMapper.updateFinishCount(Pid, tid);
            //主表更改
            MatRequisitionEntity matRequisitionEntity = new MatRequisitionEntity();
            matRequisitionEntity.setId(Pid);
            matRequisitionEntity.setLister(loginUser.getRealname());
            matRequisitionEntity.setListerid(loginUser.getUserid());
            matRequisitionEntity.setModifydate(new Date());
            matRequisitionEntity.setTenantid(loginUser.getTenantid());
            this.matRequisitionMapper.update(matRequisitionEntity);
            //返回Bill实例
            return this.getBillEntity(matRequisitionEntity.getId(), matRequisitionEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    @Override
    public Map<String, Object> getWipByWorkUid(String key, String tid) {
        return this.matRequisitionMapper.getWipByWorkUid(key, tid);
    }


    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    @Override
    public Map<String, Object> getMachItem(String key, String tid) {
        return this.matRequisitionMapper.getMachItem(key, tid);
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    @Override
    public Map<String, Object> getWsItem(String key, String tid) {
        return this.matRequisitionMapper.getWsItem(key, tid);
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    @Override
    public Map<String, Object> getGoodsEntityByGoodsUid(String key, String tid) {
        return this.matRequisitionMapper.getGoodsEntityByGoodsUid(key, tid);
    }

    /*
     *
     * <AUTHOR>
     * @description 获取销售单客户金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    public PageInfo<MatRequisitionitemdetailPojo> getSumPageListByGroup(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatRequisitionitemdetailPojo> lst = this.matRequisitionMapper.getSumPageListByGroup(queryParam);
            PageInfo<MatRequisitionitemdetailPojo> pageInfo = new PageInfo<MatRequisitionitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 获取销售单客户金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    public PageInfo<MatRequisitionitemdetailPojo> getSumPageListByGoods(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatRequisitionitemdetailPojo> lst = this.matRequisitionMapper.getSumPageListByGoods(queryParam);
            PageInfo<MatRequisitionitemdetailPojo> pageInfo = new PageInfo<MatRequisitionitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /*
     *
     * <AUTHOR>
     * @description 获取销售单客户金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    public PageInfo<MatRequisitionitemdetailPojo> getMachPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatRequisitionitemdetailPojo> lst = this.matRequisitionMapper.getMachPageList(queryParam);
            PageInfo<MatRequisitionitemdetailPojo> pageInfo = new PageInfo<MatRequisitionitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    // 查询Item是否被引用
    public Integer getItemCiteBillName(String key, String pid, String tid) {
        return this.matRequisitionMapper.getItemCiteBillName(key, pid, tid);
    }

    @Override
    public void updatePrintcount(MatRequisitionPojo billPrintPojo) {
        this.matRequisitionMapper.updatePrintcount(billPrintPojo);
    }
}
