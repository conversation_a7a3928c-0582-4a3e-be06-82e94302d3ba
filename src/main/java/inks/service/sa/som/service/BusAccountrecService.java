package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BusAccountrecPojo;

/**
 * 结转记录(BusAccountrec)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-25 08:28:53
 */
public interface BusAccountrecService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountrecPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusAccountrecPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busAccountrecPojo 实例对象
     * @return 实例对象
     */
    BusAccountrecPojo insert(BusAccountrecPojo busAccountrecPojo);

    /**
     * 修改数据
     *
     * @param busAccountrecpojo 实例对象
     * @return 实例对象
     */
    BusAccountrecPojo update(BusAccountrecPojo busAccountrecpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    BusAccountrecPojo getEntityByMax(String tid);
}
