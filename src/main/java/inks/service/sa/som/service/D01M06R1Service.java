package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BusDelieryPojo;
import inks.service.sa.som.domain.pojo.BusDelieryitemdetailPojo;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 销售单(BusDeliery)报表表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-13 10:19:46
 */
public interface D01M06R1Service {

    /*
     *
     * <AUTHOR>
     * @description 销售单金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    PageInfo<BusDelieryitemdetailPojo> getSumPageListByGroup(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 货品销售金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    PageInfo<BusDelieryitemdetailPojo> getSumPageListByGoods(QueryParam queryParam);
     /*
      *
      * <AUTHOR>
      * @description 销售单金额排名
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByGroupMax(QueryParam queryParam);
     /*
      *
      * <AUTHOR>
      * @description 货品销售金额排名
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByGoodsMax(QueryParam queryParam,String province,String city);
     /*
      *
      * <AUTHOR>
      * @description 销售趋势图年度
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByYear(QueryParam queryParam);
     /*
      *
      * <AUTHOR>
      * @description 销售趋势图月度
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByMonth(QueryParam queryParam) throws ParseException;
     /*
      *
      * <AUTHOR>
      * @description 销售趋势图周
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByDay(QueryParam queryParam);
     /*
      *
      * <AUTHOR>
      * @description 本月销售额
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    ChartPojo getTagSumAmtQtyByMonth(QueryParam queryParam);
     /*
      *
      * <AUTHOR>
      * @description 销售饼状图年
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByYearMax(QueryParam queryParam);
     /*
      *
      * <AUTHOR>
      * @description 销售饼状图月
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByMonthMax(QueryParam queryParam);
     /*
      *
      * <AUTHOR>
      * @description 销售饼状图周
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByDayMax(QueryParam queryParam);


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDelieryitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDelieryPojo> getPageTh(QueryParam queryParam);

    List<Map<String,Object>> getSumAmtByGroupProvince(QueryParam queryParam, String province);

    List<Map<String,Object>> getSumAmtByGroupProvinceMach(QueryParam queryParam, String province);

    List<Map<String,Object>> getSumAmtByGroupProvinceCollection(QueryParam queryParam, String province);

    List<Map<String, Object>> getSumAmtGroupByCountry(QueryParam queryParam);
}

