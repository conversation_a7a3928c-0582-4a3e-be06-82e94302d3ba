package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BusDepositPojo;
import inks.service.sa.som.domain.pojo.BusDepositcashdetailPojo;
import inks.service.sa.som.domain.pojo.BusDeposititemdetailPojo;

/**
 * 预收款(BusDeposit)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-15 15:31:25
 */
public interface BusDepositService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDepositPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDeposititemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDepositPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDepositPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDepositPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busDepositPojo 实例对象
     * @return 实例对象
     */
    BusDepositPojo insert(BusDepositPojo busDepositPojo);

    /**
     * 修改数据
     *
     * @param busDepositpojo 实例对象
     * @return 实例对象
     */
    BusDepositPojo update(BusDepositPojo busDepositpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param busDepositPojo 实例对象
     * @return 实例对象
     */
    BusDepositPojo approval(BusDepositPojo busDepositPojo);

    PageInfo<BusDepositcashdetailPojo> getCashPageList(QueryParam queryParam);

}
