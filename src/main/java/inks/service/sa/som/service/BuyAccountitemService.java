package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyAccountitemPojo;

import java.util.List;
/**
 * 账单明细(BuyAccountitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-09 08:29:10
 */
public interface BuyAccountitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyAccountitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyAccountitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyAccountitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param buyAccountitemPojo 实例对象
     * @return 实例对象
     */
    BuyAccountitemPojo insert(BuyAccountitemPojo buyAccountitemPojo);

    /**
     * 修改数据
     *
     * @param buyAccountitempojo 实例对象
     * @return 实例对象
     */
    BuyAccountitemPojo update(BuyAccountitemPojo buyAccountitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param buyAccountitempojo 实例对象
     * @return 实例对象
     */
    BuyAccountitemPojo clearNull(BuyAccountitemPojo buyAccountitempojo);
}
