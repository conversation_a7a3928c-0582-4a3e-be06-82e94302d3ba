package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.FmCosttypeEntity;
import inks.service.sa.som.domain.pojo.FmCosttypePojo;
import inks.service.sa.som.mapper.FmCosttypeMapper;
import inks.service.sa.som.service.FmCosttypeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 费用科目(FmCosttype)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-29 15:21:43
 */
@Service("fmCosttypeService")
public class FmCosttypeServiceImpl implements FmCosttypeService {
    @Resource
    private FmCosttypeMapper fmCosttypeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmCosttypePojo getEntity(String key, String tid) {
        return this.fmCosttypeMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmCosttypePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmCosttypePojo> lst = fmCosttypeMapper.getPageList(queryParam);
            PageInfo<FmCosttypePojo> pageInfo = new PageInfo<FmCosttypePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param fmCosttypePojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmCosttypePojo insert(FmCosttypePojo fmCosttypePojo) {
    //初始化NULL字段
     if(fmCosttypePojo.getParentid()==null) fmCosttypePojo.setParentid("");
     if(fmCosttypePojo.getDirection()==null) fmCosttypePojo.setDirection("");
     if(fmCosttypePojo.getCostcode()==null) fmCosttypePojo.setCostcode("");
     if(fmCosttypePojo.getCostname()==null) fmCosttypePojo.setCostname("");
     if(fmCosttypePojo.getRownum()==null) fmCosttypePojo.setRownum(0);
     if(fmCosttypePojo.getRemark()==null) fmCosttypePojo.setRemark("");
     if(fmCosttypePojo.getEnabledmark()==null) fmCosttypePojo.setEnabledmark(0);
     if(fmCosttypePojo.getDeletemark()==null) fmCosttypePojo.setDeletemark(0);
     if(fmCosttypePojo.getDeletelisterid()==null) fmCosttypePojo.setDeletelisterid("");
     if(fmCosttypePojo.getDeletelister()==null) fmCosttypePojo.setDeletelister("");
     if(fmCosttypePojo.getDeletedate()==null) fmCosttypePojo.setDeletedate(new Date());
     if(fmCosttypePojo.getCreateby()==null) fmCosttypePojo.setCreateby("");
     if(fmCosttypePojo.getCreatebyid()==null) fmCosttypePojo.setCreatebyid("");
     if(fmCosttypePojo.getCreatedate()==null) fmCosttypePojo.setCreatedate(new Date());
     if(fmCosttypePojo.getLister()==null) fmCosttypePojo.setLister("");
     if(fmCosttypePojo.getListerid()==null) fmCosttypePojo.setListerid("");
     if(fmCosttypePojo.getModifydate()==null) fmCosttypePojo.setModifydate(new Date());
     if(fmCosttypePojo.getCustom1()==null) fmCosttypePojo.setCustom1("");
     if(fmCosttypePojo.getCustom2()==null) fmCosttypePojo.setCustom2("");
     if(fmCosttypePojo.getCustom3()==null) fmCosttypePojo.setCustom3("");
     if(fmCosttypePojo.getCustom4()==null) fmCosttypePojo.setCustom4("");
     if(fmCosttypePojo.getCustom5()==null) fmCosttypePojo.setCustom5("");
     if(fmCosttypePojo.getCustom6()==null) fmCosttypePojo.setCustom6("");
     if(fmCosttypePojo.getCustom7()==null) fmCosttypePojo.setCustom7("");
     if(fmCosttypePojo.getCustom8()==null) fmCosttypePojo.setCustom8("");
     if(fmCosttypePojo.getCustom9()==null) fmCosttypePojo.setCustom9("");
     if(fmCosttypePojo.getCustom10()==null) fmCosttypePojo.setCustom10("");
     if(fmCosttypePojo.getTenantid()==null) fmCosttypePojo.setTenantid("");
     if(fmCosttypePojo.getTenantname()==null) fmCosttypePojo.setTenantname("");
     if(fmCosttypePojo.getRevision()==null) fmCosttypePojo.setRevision(0);
        FmCosttypeEntity fmCosttypeEntity = new FmCosttypeEntity(); 
        BeanUtils.copyProperties(fmCosttypePojo,fmCosttypeEntity);
        
          fmCosttypeEntity.setId(UUID.randomUUID().toString());
          fmCosttypeEntity.setRevision(1);  //乐观锁
          this.fmCosttypeMapper.insert(fmCosttypeEntity);
        return this.getEntity(fmCosttypeEntity.getId(),fmCosttypeEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param fmCosttypePojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmCosttypePojo update(FmCosttypePojo fmCosttypePojo) {
        FmCosttypeEntity fmCosttypeEntity = new FmCosttypeEntity(); 
        BeanUtils.copyProperties(fmCosttypePojo,fmCosttypeEntity);
        this.fmCosttypeMapper.update(fmCosttypeEntity);
        return this.getEntity(fmCosttypeEntity.getId(),fmCosttypeEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        // 查询费用科目是否被引用
        List<String> citeBillName = fmCosttypeMapper.getCiteBillName(key, tid);
        if (!citeBillName.isEmpty()) {
            throw new BaseBusinessException("费用科目被以下单据引用：" + citeBillName);
        }
        return this.fmCosttypeMapper.delete(key,tid) ;
    }
    
                                                                                                                                                               
}
