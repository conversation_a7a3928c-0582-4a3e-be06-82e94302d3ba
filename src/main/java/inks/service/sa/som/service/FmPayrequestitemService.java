package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmPayrequestitemPojo;

import java.util.List;
/**
 * 付款申请单(FmPayrequestitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-07 13:31:44
 */
public interface FmPayrequestitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmPayrequestitemPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmPayrequestitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<FmPayrequestitemPojo> getList(String Pid);  
    
    /**
     * 新增数据
     *
     * @param fmPayrequestitemPojo 实例对象
     * @return 实例对象
     */
    FmPayrequestitemPojo insert(FmPayrequestitemPojo fmPayrequestitemPojo);

    /**
     * 修改数据
     *
     * @param fmPayrequestitempojo 实例对象
     * @return 实例对象
     */
    FmPayrequestitemPojo update(FmPayrequestitemPojo fmPayrequestitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

     /**
     * 修改数据
     *
     * @param fmPayrequestitempojo 实例对象
     * @return 实例对象
     */
    FmPayrequestitemPojo clearNull(FmPayrequestitemPojo fmPayrequestitempojo);
}
