package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyDeductionitemEntity;
import inks.service.sa.som.domain.pojo.BuyDeductionitemPojo;
import inks.service.sa.som.mapper.BuyDeductionitemMapper;
import inks.service.sa.som.service.BuyDeductionitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 采购扣款Item(BuyDeductionitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 20:36:08
 */
@Service("buyDeductionitemService")
public class BuyDeductionitemServiceImpl implements BuyDeductionitemService {
    @Resource
    private BuyDeductionitemMapper buyDeductionitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyDeductionitemPojo getEntity(String key,String tid) {
        return this.buyDeductionitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyDeductionitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyDeductionitemPojo> lst = buyDeductionitemMapper.getPageList(queryParam);
            PageInfo<BuyDeductionitemPojo> pageInfo = new PageInfo<BuyDeductionitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BuyDeductionitemPojo> getList(String Pid,String tid) { 
        try {
            List<BuyDeductionitemPojo> lst = buyDeductionitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param buyDeductionitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyDeductionitemPojo insert(BuyDeductionitemPojo buyDeductionitemPojo) {
        //初始化item的NULL
        BuyDeductionitemPojo itempojo =this.clearNull(buyDeductionitemPojo);
        BuyDeductionitemEntity buyDeductionitemEntity = new BuyDeductionitemEntity(); 
        BeanUtils.copyProperties(itempojo,buyDeductionitemEntity);
        
          buyDeductionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          buyDeductionitemEntity.setRevision(1);  //乐观锁      
          this.buyDeductionitemMapper.insert(buyDeductionitemEntity);
        return this.getEntity(buyDeductionitemEntity.getId(),buyDeductionitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param buyDeductionitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyDeductionitemPojo update(BuyDeductionitemPojo buyDeductionitemPojo) {
        BuyDeductionitemEntity buyDeductionitemEntity = new BuyDeductionitemEntity(); 
        BeanUtils.copyProperties(buyDeductionitemPojo,buyDeductionitemEntity);
        this.buyDeductionitemMapper.update(buyDeductionitemEntity);
        return this.getEntity(buyDeductionitemEntity.getId(),buyDeductionitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.buyDeductionitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param buyDeductionitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BuyDeductionitemPojo clearNull(BuyDeductionitemPojo buyDeductionitemPojo){
     //初始化NULL字段
     if(buyDeductionitemPojo.getPid()==null) buyDeductionitemPojo.setPid("");
     if(buyDeductionitemPojo.getGoodsid()==null) buyDeductionitemPojo.setGoodsid("");
     if(buyDeductionitemPojo.getItemcode()==null) buyDeductionitemPojo.setItemcode("");
     if(buyDeductionitemPojo.getItemname()==null) buyDeductionitemPojo.setItemname("");
     if(buyDeductionitemPojo.getItemspec()==null) buyDeductionitemPojo.setItemspec("");
     if(buyDeductionitemPojo.getItemunit()==null) buyDeductionitemPojo.setItemunit("");
     if(buyDeductionitemPojo.getQuantity()==null) buyDeductionitemPojo.setQuantity(0D);
     if(buyDeductionitemPojo.getTaxprice()==null) buyDeductionitemPojo.setTaxprice(0D);
     if(buyDeductionitemPojo.getTaxamount()==null) buyDeductionitemPojo.setTaxamount(0D);
     if(buyDeductionitemPojo.getTaxtotal()==null) buyDeductionitemPojo.setTaxtotal(0D);
     if(buyDeductionitemPojo.getItemtaxrate()==null) buyDeductionitemPojo.setItemtaxrate(0);
     if(buyDeductionitemPojo.getPrice()==null) buyDeductionitemPojo.setPrice(0D);
     if(buyDeductionitemPojo.getAmount()==null) buyDeductionitemPojo.setAmount(0D);
     if(buyDeductionitemPojo.getRemark()==null) buyDeductionitemPojo.setRemark("");
     if(buyDeductionitemPojo.getCiteuid()==null) buyDeductionitemPojo.setCiteuid("");
     if(buyDeductionitemPojo.getCiteitemid()==null) buyDeductionitemPojo.setCiteitemid("");
     if(buyDeductionitemPojo.getOrderuid()==null) buyDeductionitemPojo.setOrderuid("");
     if(buyDeductionitemPojo.getOrderitemid()==null) buyDeductionitemPojo.setOrderitemid("");
     if(buyDeductionitemPojo.getCustpo()==null) buyDeductionitemPojo.setCustpo("");
     if(buyDeductionitemPojo.getRownum()==null) buyDeductionitemPojo.setRownum(0);
     if(buyDeductionitemPojo.getInvoqty()==null) buyDeductionitemPojo.setInvoqty(0D);
     if(buyDeductionitemPojo.getInvoclosed()==null) buyDeductionitemPojo.setInvoclosed(0);
     if(buyDeductionitemPojo.getDisannulmark()==null) buyDeductionitemPojo.setDisannulmark(0);
     if(buyDeductionitemPojo.getDisannullisterid()==null) buyDeductionitemPojo.setDisannullisterid("");
     if(buyDeductionitemPojo.getDisannullister()==null) buyDeductionitemPojo.setDisannullister("");
     if(buyDeductionitemPojo.getDisannuldate()==null) buyDeductionitemPojo.setDisannuldate(new Date());
     if(buyDeductionitemPojo.getCustom1()==null) buyDeductionitemPojo.setCustom1("");
     if(buyDeductionitemPojo.getCustom2()==null) buyDeductionitemPojo.setCustom2("");
     if(buyDeductionitemPojo.getCustom3()==null) buyDeductionitemPojo.setCustom3("");
     if(buyDeductionitemPojo.getCustom4()==null) buyDeductionitemPojo.setCustom4("");
     if(buyDeductionitemPojo.getCustom5()==null) buyDeductionitemPojo.setCustom5("");
     if(buyDeductionitemPojo.getCustom6()==null) buyDeductionitemPojo.setCustom6("");
     if(buyDeductionitemPojo.getCustom7()==null) buyDeductionitemPojo.setCustom7("");
     if(buyDeductionitemPojo.getCustom8()==null) buyDeductionitemPojo.setCustom8("");
     if(buyDeductionitemPojo.getCustom9()==null) buyDeductionitemPojo.setCustom9("");
     if(buyDeductionitemPojo.getCustom10()==null) buyDeductionitemPojo.setCustom10("");
     if(buyDeductionitemPojo.getTenantid()==null) buyDeductionitemPojo.setTenantid("");
     if(buyDeductionitemPojo.getRevision()==null) buyDeductionitemPojo.setRevision(0);
     return buyDeductionitemPojo;
     }
}
