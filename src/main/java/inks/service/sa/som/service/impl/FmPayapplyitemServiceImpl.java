package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.FmPayapplyitemEntity;
import inks.service.sa.som.domain.pojo.FmPayapplyitemPojo;
import inks.service.sa.som.mapper.FmPayapplyitemMapper;
import inks.service.sa.som.service.FmPayapplyitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
/**
 * 往来核销项目(FmPayapplyitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-13 21:30:35
 */
@Service("fmPayapplyitemService")
public class FmPayapplyitemServiceImpl implements FmPayapplyitemService {
    @Resource
    private FmPayapplyitemMapper fmPayapplyitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmPayapplyitemPojo getEntity(String key,String tid) {
        return this.fmPayapplyitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmPayapplyitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmPayapplyitemPojo> lst = fmPayapplyitemMapper.getPageList(queryParam);
            PageInfo<FmPayapplyitemPojo> pageInfo = new PageInfo<FmPayapplyitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<FmPayapplyitemPojo> getList(String Pid,String tid) { 
        try {
            List<FmPayapplyitemPojo> lst = fmPayapplyitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param fmPayapplyitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmPayapplyitemPojo insert(FmPayapplyitemPojo fmPayapplyitemPojo) {
        //初始化item的NULL
        FmPayapplyitemPojo itempojo =this.clearNull(fmPayapplyitemPojo);
        FmPayapplyitemEntity fmPayapplyitemEntity = new FmPayapplyitemEntity(); 
        BeanUtils.copyProperties(itempojo,fmPayapplyitemEntity);
        
          fmPayapplyitemEntity.setId(UUID.randomUUID().toString());
          fmPayapplyitemEntity.setRevision(1);  //乐观锁      
          this.fmPayapplyitemMapper.insert(fmPayapplyitemEntity);
        return this.getEntity(fmPayapplyitemEntity.getId(),fmPayapplyitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param fmPayapplyitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmPayapplyitemPojo update(FmPayapplyitemPojo fmPayapplyitemPojo) {
        FmPayapplyitemEntity fmPayapplyitemEntity = new FmPayapplyitemEntity(); 
        BeanUtils.copyProperties(fmPayapplyitemPojo,fmPayapplyitemEntity);
        this.fmPayapplyitemMapper.update(fmPayapplyitemEntity);
        return this.getEntity(fmPayapplyitemEntity.getId(),fmPayapplyitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.fmPayapplyitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param fmPayapplyitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public FmPayapplyitemPojo clearNull(FmPayapplyitemPojo fmPayapplyitemPojo){
     //初始化NULL字段
     if(fmPayapplyitemPojo.getPid()==null) fmPayapplyitemPojo.setPid("");
     if(fmPayapplyitemPojo.getInvobillid()==null) fmPayapplyitemPojo.setInvobillid("");
     if(fmPayapplyitemPojo.getInvobillcode()==null) fmPayapplyitemPojo.setInvobillcode("");
     if(fmPayapplyitemPojo.getInvocode()==null) fmPayapplyitemPojo.setInvocode("");
     if(fmPayapplyitemPojo.getInvoamount()==null) fmPayapplyitemPojo.setInvoamount(0D);
     if(fmPayapplyitemPojo.getAmount()==null) fmPayapplyitemPojo.setAmount(0D);
     if(fmPayapplyitemPojo.getRownum()==null) fmPayapplyitemPojo.setRownum(0);
     if(fmPayapplyitemPojo.getRemark()==null) fmPayapplyitemPojo.setRemark("");
     if(fmPayapplyitemPojo.getCustom1()==null) fmPayapplyitemPojo.setCustom1("");
     if(fmPayapplyitemPojo.getCustom2()==null) fmPayapplyitemPojo.setCustom2("");
     if(fmPayapplyitemPojo.getCustom3()==null) fmPayapplyitemPojo.setCustom3("");
     if(fmPayapplyitemPojo.getCustom4()==null) fmPayapplyitemPojo.setCustom4("");
     if(fmPayapplyitemPojo.getCustom5()==null) fmPayapplyitemPojo.setCustom5("");
     if(fmPayapplyitemPojo.getCustom6()==null) fmPayapplyitemPojo.setCustom6("");
     if(fmPayapplyitemPojo.getCustom7()==null) fmPayapplyitemPojo.setCustom7("");
     if(fmPayapplyitemPojo.getCustom8()==null) fmPayapplyitemPojo.setCustom8("");
     if(fmPayapplyitemPojo.getCustom9()==null) fmPayapplyitemPojo.setCustom9("");
     if(fmPayapplyitemPojo.getCustom10()==null) fmPayapplyitemPojo.setCustom10("");
     if(fmPayapplyitemPojo.getTenantid()==null) fmPayapplyitemPojo.setTenantid("");
     if(fmPayapplyitemPojo.getRevision()==null) fmPayapplyitemPojo.setRevision(0);
     return fmPayapplyitemPojo;
     }
}
