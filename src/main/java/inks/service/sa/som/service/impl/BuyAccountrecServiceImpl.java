package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyAccountrecEntity;
import inks.service.sa.som.domain.pojo.BuyAccountrecPojo;
import inks.service.sa.som.mapper.BuyAccountMapper;
import inks.service.sa.som.mapper.BuyAccountitemMapper;
import inks.service.sa.som.mapper.BuyAccountrecMapper;
import inks.service.sa.som.service.BuyAccountrecService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 采购结账(BuyAccountrec)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-28 20:24:37
 */
@Service("buyAccountrecService")
public class BuyAccountrecServiceImpl implements BuyAccountrecService {
    @Resource
    private BuyAccountrecMapper buyAccountrecMapper;

    @Resource
    private BuyAccountMapper buyAccountMapper;
    @Resource
    private BuyAccountitemMapper buyAccountitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyAccountrecPojo getEntity(String key, String tid) {
        return this.buyAccountrecMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyAccountrecPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyAccountrecPojo> lst = buyAccountrecMapper.getPageList(queryParam);
            PageInfo<BuyAccountrecPojo> pageInfo = new PageInfo<BuyAccountrecPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param buyAccountrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyAccountrecPojo insert(BuyAccountrecPojo buyAccountrecPojo) {
        //初始化NULL字段
        if (buyAccountrecPojo.getCarryyear() == null) buyAccountrecPojo.setCarryyear(0);
        if (buyAccountrecPojo.getCarrymonth() == null) buyAccountrecPojo.setCarrymonth(0);
        if (buyAccountrecPojo.getStartdate() == null) buyAccountrecPojo.setStartdate(new Date());
        if (buyAccountrecPojo.getEnddate() == null) buyAccountrecPojo.setEnddate(new Date());
        if (buyAccountrecPojo.getOperator() == null) buyAccountrecPojo.setOperator("");
        if (buyAccountrecPojo.getOperatorid() == null) buyAccountrecPojo.setOperatorid("");
        if (buyAccountrecPojo.getRownum() == null) buyAccountrecPojo.setRownum(0);
        if (buyAccountrecPojo.getRemark() == null) buyAccountrecPojo.setRemark("");
        if (buyAccountrecPojo.getCreateby() == null) buyAccountrecPojo.setCreateby("");
        if (buyAccountrecPojo.getCreatebyid() == null) buyAccountrecPojo.setCreatebyid("");
        if (buyAccountrecPojo.getCreatedate() == null) buyAccountrecPojo.setCreatedate(new Date());
        if (buyAccountrecPojo.getLister() == null) buyAccountrecPojo.setLister("");
        if (buyAccountrecPojo.getListerid() == null) buyAccountrecPojo.setListerid("");
        if (buyAccountrecPojo.getModifydate() == null) buyAccountrecPojo.setModifydate(new Date());
        if (buyAccountrecPojo.getBillopenamount() == null) buyAccountrecPojo.setBillopenamount(0D);
        if (buyAccountrecPojo.getBillinamount() == null) buyAccountrecPojo.setBillinamount(0D);
        if (buyAccountrecPojo.getBilloutamount() == null) buyAccountrecPojo.setBilloutamount(0D);
        if (buyAccountrecPojo.getBillcloseamount() == null) buyAccountrecPojo.setBillcloseamount(0D);
        if (buyAccountrecPojo.getInvoopenamount() == null) buyAccountrecPojo.setInvoopenamount(0D);
        if (buyAccountrecPojo.getInvoinamount() == null) buyAccountrecPojo.setInvoinamount(0D);
        if (buyAccountrecPojo.getInvooutamount() == null) buyAccountrecPojo.setInvooutamount(0D);
        if (buyAccountrecPojo.getInvocloseamount() == null) buyAccountrecPojo.setInvocloseamount(0D);
        if (buyAccountrecPojo.getArapopenamount() == null) buyAccountrecPojo.setArapopenamount(0D);
        if (buyAccountrecPojo.getArapinamount() == null) buyAccountrecPojo.setArapinamount(0D);
        if (buyAccountrecPojo.getArapoutamount() == null) buyAccountrecPojo.setArapoutamount(0D);
        if (buyAccountrecPojo.getArapcloseamount() == null) buyAccountrecPojo.setArapcloseamount(0D);
        if (buyAccountrecPojo.getPrintcount() == null) buyAccountrecPojo.setPrintcount(0);
        if (buyAccountrecPojo.getCustom1() == null) buyAccountrecPojo.setCustom1("");
        if (buyAccountrecPojo.getCustom2() == null) buyAccountrecPojo.setCustom2("");
        if (buyAccountrecPojo.getCustom3() == null) buyAccountrecPojo.setCustom3("");
        if (buyAccountrecPojo.getCustom4() == null) buyAccountrecPojo.setCustom4("");
        if (buyAccountrecPojo.getCustom5() == null) buyAccountrecPojo.setCustom5("");
        if (buyAccountrecPojo.getCustom6() == null) buyAccountrecPojo.setCustom6("");
        if (buyAccountrecPojo.getCustom7() == null) buyAccountrecPojo.setCustom7("");
        if (buyAccountrecPojo.getCustom8() == null) buyAccountrecPojo.setCustom8("");
        if (buyAccountrecPojo.getCustom9() == null) buyAccountrecPojo.setCustom9("");
        if (buyAccountrecPojo.getCustom10() == null) buyAccountrecPojo.setCustom10("");
        if (buyAccountrecPojo.getTenantid() == null) buyAccountrecPojo.setTenantid("");
        if (buyAccountrecPojo.getTenantname() == null) buyAccountrecPojo.setTenantname("");
        if (buyAccountrecPojo.getRevision() == null) buyAccountrecPojo.setRevision(0);
        BuyAccountrecEntity buyAccountrecEntity = new BuyAccountrecEntity();
        BeanUtils.copyProperties(buyAccountrecPojo, buyAccountrecEntity);

        buyAccountrecEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        buyAccountrecEntity.setRevision(1);  //乐观锁
        this.buyAccountrecMapper.insert(buyAccountrecEntity);
        return this.getEntity(buyAccountrecEntity.getId(), buyAccountrecEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param buyAccountrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyAccountrecPojo update(BuyAccountrecPojo buyAccountrecPojo) {
        BuyAccountrecEntity buyAccountrecEntity = new BuyAccountrecEntity();
        BeanUtils.copyProperties(buyAccountrecPojo, buyAccountrecEntity);
        this.buyAccountrecMapper.update(buyAccountrecEntity);
        return this.getEntity(buyAccountrecEntity.getId(), buyAccountrecEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BuyAccountrecPojo delPojo = this.buyAccountrecMapper.getEntity(key, tid);
        // 查询需要删除的主表id集合
        List<String> deleteIds = buyAccountMapper.getDeleteIds();
        this.buyAccountMapper.deleteByMonth(delPojo.getCarryyear(), delPojo.getCarrymonth(), tid);
        // 删除关联的子表
        if (CollectionUtils.isNotEmpty(deleteIds)) {
            buyAccountitemMapper.deleteByPid(deleteIds, tid);
        }
        return this.buyAccountrecMapper.delete(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public BuyAccountrecPojo getEntityByMax(String tid) {
        return this.buyAccountrecMapper.getEntityByMax(tid);
    }

}
