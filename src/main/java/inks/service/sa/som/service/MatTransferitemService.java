package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatTransferitemPojo;

import java.util.List;
/**
 * 调拨项目(MatTransferitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-15 14:23:16
 */
public interface MatTransferitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatTransferitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatTransferitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatTransferitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param matTransferitemPojo 实例对象
     * @return 实例对象
     */
    MatTransferitemPojo insert(MatTransferitemPojo matTransferitemPojo);

    /**
     * 修改数据
     *
     * @param matTransferitempojo 实例对象
     * @return 实例对象
     */
    MatTransferitemPojo update(MatTransferitemPojo matTransferitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param matTransferitempojo 实例对象
     * @return 实例对象
     */
    MatTransferitemPojo clearNull(MatTransferitemPojo matTransferitempojo);
}
