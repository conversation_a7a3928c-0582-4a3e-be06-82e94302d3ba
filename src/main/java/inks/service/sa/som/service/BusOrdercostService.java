package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BusOrdercostPojo;
import inks.service.sa.som.domain.pojo.BusOrdercostitemPojo;
import inks.service.sa.som.domain.pojo.BusOrdercostitemdetailPojo;

import java.util.List;

/**
 * 订单成本(BusOrdercost)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-30 08:20:50
 */
public interface BusOrdercostService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusOrdercostPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusOrdercostitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusOrdercostPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusOrdercostPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusOrdercostPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busOrdercostPojo 实例对象
     * @return 实例对象
     */
    BusOrdercostPojo insert(BusOrdercostPojo busOrdercostPojo);

    /**
     * 修改数据
     *
     * @param busOrdercostpojo 实例对象
     * @return 实例对象
     */
    BusOrdercostPojo update(BusOrdercostPojo busOrdercostpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param busOrdercostPojo 实例对象
     * @return 实例对象
     */
    BusOrdercostPojo approval(BusOrdercostPojo busOrdercostPojo);


    BusOrdercostPojo closed(List<BusOrdercostitemPojo> lst, Integer type, LoginUser loginUser);

    BusOrdercostPojo disannul(List<BusOrdercostitemPojo> lst, Integer type, LoginUser loginUser);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(String key, String pid, String tid);
}
