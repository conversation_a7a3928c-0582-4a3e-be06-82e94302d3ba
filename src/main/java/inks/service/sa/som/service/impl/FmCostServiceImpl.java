package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.FmCostEntity;
import inks.service.sa.som.domain.FmCostitemEntity;
import inks.service.sa.som.domain.pojo.FmCostPojo;
import inks.service.sa.som.domain.pojo.FmCostitemPojo;
import inks.service.sa.som.domain.pojo.FmCostitemdetailPojo;
import inks.service.sa.som.mapper.FmCostMapper;
import inks.service.sa.som.mapper.FmCostitemMapper;
import inks.service.sa.som.service.FmCostService;
import inks.service.sa.som.service.FmCostitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 费用开支(FmCost)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-29 15:25:08
 */
@Service("fmCostService")
public class FmCostServiceImpl implements FmCostService {
    @Resource
    private FmCostMapper fmCostMapper;

    @Resource
    private FmCostitemMapper fmCostitemMapper;

    
    @Resource
    private FmCostitemService fmCostitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmCostPojo getEntity(String key, String tid) {
        return this.fmCostMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmCostitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmCostitemdetailPojo> lst = fmCostMapper.getPageList(queryParam);
            PageInfo<FmCostitemdetailPojo> pageInfo = new PageInfo<FmCostitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmCostPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            FmCostPojo fmCostPojo = this.fmCostMapper.getEntity(key, tid);
            //读取子表
            fmCostPojo.setItem(fmCostitemMapper.getList(fmCostPojo.getId(), fmCostPojo.getTenantid()));
            return fmCostPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmCostPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmCostPojo> lst = fmCostMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(fmCostitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<FmCostPojo> pageInfo = new PageInfo<FmCostPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmCostPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmCostPojo> lst = fmCostMapper.getPageTh(queryParam);
            PageInfo<FmCostPojo> pageInfo = new PageInfo<FmCostPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param fmCostPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public FmCostPojo insert(FmCostPojo fmCostPojo) {
//初始化NULL字段
        if (fmCostPojo.getRefno() == null) fmCostPojo.setRefno("");
        if (fmCostPojo.getBilltype() == null) fmCostPojo.setBilltype("");
        if (fmCostPojo.getBilldate() == null) fmCostPojo.setBilldate(new Date());
        if (fmCostPojo.getProjectid() == null) fmCostPojo.setProjectid("");
        if (fmCostPojo.getProjcode() == null) fmCostPojo.setProjcode("");
        if (fmCostPojo.getProjname() == null) fmCostPojo.setProjname("");
        if (fmCostPojo.getBilltitle() == null) fmCostPojo.setBilltitle("");
        if (fmCostPojo.getGroupid() == null) fmCostPojo.setGroupid("");
        if (fmCostPojo.getChequenum() == null) fmCostPojo.setChequenum("");
        if (fmCostPojo.getMoneyid() == null) fmCostPojo.setMoneyid("");
        if (fmCostPojo.getAmount() == null) fmCostPojo.setAmount(0D);
        if (fmCostPojo.getCashaccid() == null) fmCostPojo.setCashaccid("");
        if (fmCostPojo.getOperator() == null) fmCostPojo.setOperator("");
        if (fmCostPojo.getProjectcode() == null) fmCostPojo.setProjectcode("");
        if (fmCostPojo.getSummary() == null) fmCostPojo.setSummary("");
        if (fmCostPojo.getCreateby() == null) fmCostPojo.setCreateby("");
        if (fmCostPojo.getCreatebyid() == null) fmCostPojo.setCreatebyid("");
        if (fmCostPojo.getCreatedate() == null) fmCostPojo.setCreatedate(new Date());
        if (fmCostPojo.getLister() == null) fmCostPojo.setLister("");
        if (fmCostPojo.getListerid() == null) fmCostPojo.setListerid("");
        if (fmCostPojo.getModifydate() == null) fmCostPojo.setModifydate(new Date());
        if (fmCostPojo.getModulecode() == null) fmCostPojo.setModulecode("");
        if (fmCostPojo.getCiteuid() == null) fmCostPojo.setCiteuid("");
        if (fmCostPojo.getCiteid() == null) fmCostPojo.setCiteid("");
        if (fmCostPojo.getBenefited() == null) fmCostPojo.setBenefited("");
        if (fmCostPojo.getCustom1() == null) fmCostPojo.setCustom1("");
        if (fmCostPojo.getCustom2() == null) fmCostPojo.setCustom2("");
        if (fmCostPojo.getCustom3() == null) fmCostPojo.setCustom3("");
        if (fmCostPojo.getCustom4() == null) fmCostPojo.setCustom4("");
        if (fmCostPojo.getCustom5() == null) fmCostPojo.setCustom5("");
        if (fmCostPojo.getCustom6() == null) fmCostPojo.setCustom6("");
        if (fmCostPojo.getCustom7() == null) fmCostPojo.setCustom7("");
        if (fmCostPojo.getCustom8() == null) fmCostPojo.setCustom8("");
        if (fmCostPojo.getCustom9() == null) fmCostPojo.setCustom9("");
        if (fmCostPojo.getCustom10() == null) fmCostPojo.setCustom10("");
        if (fmCostPojo.getTenantid() == null) fmCostPojo.setTenantid("");
        if (fmCostPojo.getTenantname() == null) fmCostPojo.setTenantname("");
        if (fmCostPojo.getRevision() == null) fmCostPojo.setRevision(0);
        //生成id
        String id = UUID.randomUUID().toString();
        FmCostEntity fmCostEntity = new FmCostEntity();
        BeanUtils.copyProperties(fmCostPojo, fmCostEntity);
        //设置id和新建日期
        fmCostEntity.setId(id);
        fmCostEntity.setRevision(1);  //乐观锁
        //插入主表
        this.fmCostMapper.insert(fmCostEntity);
        //Item子表处理
        List<FmCostitemPojo> lst = fmCostPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                FmCostitemPojo itemPojo = this.fmCostitemService.clearNull(lst.get(i));
                FmCostitemEntity fmCostitemEntity = new FmCostitemEntity();
                BeanUtils.copyProperties(itemPojo, fmCostitemEntity);
                //设置id和Pid
                fmCostitemEntity.setId(UUID.randomUUID().toString());
                fmCostitemEntity.setPid(id);
                fmCostitemEntity.setTenantid(fmCostPojo.getTenantid());
                fmCostitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.fmCostitemMapper.insert(fmCostitemEntity);
            }
        }
        // 同步出纳账户
        this.fmCostMapper.updateCashAmount(fmCostPojo.getCashaccid(), fmCostPojo.getAmount(), fmCostPojo.getTenantid());
        //返回Bill实例
        return this.getBillEntity(fmCostEntity.getId(), fmCostEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param fmCostPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public FmCostPojo update(FmCostPojo fmCostPojo) {
        //主表更改
        FmCostEntity fmCostEntity = new FmCostEntity();
        BeanUtils.copyProperties(fmCostPojo, fmCostEntity);
        FmCostPojo dbPojo = this.fmCostMapper.getEntity(fmCostPojo.getId(), fmCostPojo.getTenantid());
        // 同步出纳账户(充回)
        this.fmCostMapper.updateCashAmount(fmCostPojo.getCashaccid(), 0 - dbPojo.getAmount(), fmCostPojo.getTenantid());
        this.fmCostMapper.update(fmCostEntity);
        // 同步出纳账户
        this.fmCostMapper.updateCashAmount(fmCostPojo.getCashaccid(), fmCostPojo.getAmount(), fmCostPojo.getTenantid());
        if (fmCostPojo.getItem() != null) {
            //Item子表处理
            List<FmCostitemPojo> lst = fmCostPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = fmCostMapper.getDelItemIds(fmCostPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.fmCostitemMapper.delete(lstDelIds.get(i), fmCostEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    FmCostitemEntity fmCostitemEntity = new FmCostitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        FmCostitemPojo itemPojo = this.fmCostitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, fmCostitemEntity);
                        //设置id和Pid
                        fmCostitemEntity.setId(UUID.randomUUID().toString());  // item id
                        fmCostitemEntity.setPid(fmCostEntity.getId());  // 主表 id
                        fmCostitemEntity.setTenantid(fmCostPojo.getTenantid());   // 租户id
                        fmCostitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.fmCostitemMapper.insert(fmCostitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), fmCostitemEntity);
                        fmCostitemEntity.setTenantid(fmCostPojo.getTenantid());
                        this.fmCostitemMapper.update(fmCostitemEntity);
                    }
                }
            }
        }


        //返回Bill实例
        return this.getBillEntity(fmCostEntity.getId(), fmCostEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        FmCostPojo fmCostPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<FmCostitemPojo> lst = fmCostPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.fmCostitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        this.fmCostMapper.delete(key, tid);
        // 同步出纳账户(充回)
        this.fmCostMapper.updateCashAmount(fmCostPojo.getCashaccid(), 0 - fmCostPojo.getAmount(), fmCostPojo.getTenantid());
        return 1;
    }


}
