package inks.service.sa.som.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.pojo.FmDepotransfermachPojo;
import inks.service.sa.som.domain.FmDepotransfermachEntity;
import inks.service.sa.som.mapper.FmDepotransfermachMapper;
import inks.service.sa.som.service.FmDepotransfermachService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 销售订单子表(FmDepotransfermach)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-14 17:21:36
 */
@Service("fmDepotransfermachService")
public class FmDepotransfermachServiceImpl implements FmDepotransfermachService {
    @Resource
    private FmDepotransfermachMapper fmDepotransfermachMapper;

    @Override
    public FmDepotransfermachPojo getEntity(String key) {
        return this.fmDepotransfermachMapper.getEntity(key);
    }

    @Override
    public PageInfo<FmDepotransfermachPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmDepotransfermachPojo> lst = fmDepotransfermachMapper.getPageList(queryParam);
            PageInfo<FmDepotransfermachPojo> pageInfo = new PageInfo<FmDepotransfermachPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<FmDepotransfermachPojo> getList(String Pid) { 
        try {
            List<FmDepotransfermachPojo> lst = fmDepotransfermachMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public FmDepotransfermachPojo insert(FmDepotransfermachPojo fmDepotransfermachPojo) {
        //初始化item的NULL
        FmDepotransfermachPojo itempojo =this.clearNull(fmDepotransfermachPojo);
        FmDepotransfermachEntity fmDepotransfermachEntity = new FmDepotransfermachEntity(); 
        BeanUtils.copyProperties(itempojo,fmDepotransfermachEntity);
         //生成雪花id
          fmDepotransfermachEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          fmDepotransfermachEntity.setRevision(1);  //乐观锁      
          this.fmDepotransfermachMapper.insert(fmDepotransfermachEntity);
        return this.getEntity(fmDepotransfermachEntity.getId());
  
    }

    @Override
    public FmDepotransfermachPojo update(FmDepotransfermachPojo fmDepotransfermachPojo) {
        FmDepotransfermachEntity fmDepotransfermachEntity = new FmDepotransfermachEntity(); 
        BeanUtils.copyProperties(fmDepotransfermachPojo,fmDepotransfermachEntity);
        this.fmDepotransfermachMapper.update(fmDepotransfermachEntity);
        return this.getEntity(fmDepotransfermachEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.fmDepotransfermachMapper.delete(key) ;
    }

     @Override
     public FmDepotransfermachPojo clearNull(FmDepotransfermachPojo fmDepotransfermachPojo){
     //初始化NULL字段
     if(fmDepotransfermachPojo.getPid()==null) fmDepotransfermachPojo.setPid("");
     if(fmDepotransfermachPojo.getMachbillid()==null) fmDepotransfermachPojo.setMachbillid("");
     if(fmDepotransfermachPojo.getMachrefno()==null) fmDepotransfermachPojo.setMachrefno("");
     if(fmDepotransfermachPojo.getMachgroupid()==null) fmDepotransfermachPojo.setMachgroupid("");
     if(fmDepotransfermachPojo.getMachamount()==null) fmDepotransfermachPojo.setMachamount(0D);
     if(fmDepotransfermachPojo.getAmount()==null) fmDepotransfermachPojo.setAmount(0D);
     if(fmDepotransfermachPojo.getRownum()==null) fmDepotransfermachPojo.setRownum(0);
     if(fmDepotransfermachPojo.getRemark()==null) fmDepotransfermachPojo.setRemark("");
     if(fmDepotransfermachPojo.getCustom1()==null) fmDepotransfermachPojo.setCustom1("");
     if(fmDepotransfermachPojo.getCustom2()==null) fmDepotransfermachPojo.setCustom2("");
     if(fmDepotransfermachPojo.getCustom3()==null) fmDepotransfermachPojo.setCustom3("");
     if(fmDepotransfermachPojo.getCustom4()==null) fmDepotransfermachPojo.setCustom4("");
     if(fmDepotransfermachPojo.getCustom5()==null) fmDepotransfermachPojo.setCustom5("");
     if(fmDepotransfermachPojo.getCustom6()==null) fmDepotransfermachPojo.setCustom6("");
     if(fmDepotransfermachPojo.getCustom7()==null) fmDepotransfermachPojo.setCustom7("");
     if(fmDepotransfermachPojo.getCustom8()==null) fmDepotransfermachPojo.setCustom8("");
     if(fmDepotransfermachPojo.getCustom9()==null) fmDepotransfermachPojo.setCustom9("");
     if(fmDepotransfermachPojo.getCustom10()==null) fmDepotransfermachPojo.setCustom10("");
     if(fmDepotransfermachPojo.getTenantid()==null) fmDepotransfermachPojo.setTenantid("");
     if(fmDepotransfermachPojo.getRevision()==null) fmDepotransfermachPojo.setRevision(0);
     return fmDepotransfermachPojo;
     }
}
