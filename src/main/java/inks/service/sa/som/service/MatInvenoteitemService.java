package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatInvenoteitemPojo;

import java.util.List;
/**
 * 盘点项目(MatInvenoteitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-14 21:02:45
 */
public interface MatInvenoteitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatInvenoteitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatInvenoteitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatInvenoteitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param matInvenoteitemPojo 实例对象
     * @return 实例对象
     */
    MatInvenoteitemPojo insert(MatInvenoteitemPojo matInvenoteitemPojo);

    /**
     * 修改数据
     *
     * @param matInvenoteitempojo 实例对象
     * @return 实例对象
     */
    MatInvenoteitemPojo update(MatInvenoteitemPojo matInvenoteitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param matInvenoteitempojo 实例对象
     * @return 实例对象
     */
    MatInvenoteitemPojo clearNull(MatInvenoteitemPojo matInvenoteitempojo);
}
