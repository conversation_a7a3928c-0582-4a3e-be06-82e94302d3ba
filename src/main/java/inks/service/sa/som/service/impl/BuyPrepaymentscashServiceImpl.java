package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyPrepaymentscashEntity;
import inks.service.sa.som.domain.pojo.BuyPrepaymentscashPojo;
import inks.service.sa.som.mapper.BuyPrepaymentscashMapper;
import inks.service.sa.som.service.BuyPrepaymentscashService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 现金项目(BuyPrepaymentscash)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-11 15:13:40
 */
@Service("buyPrepaymentscashService")
public class BuyPrepaymentscashServiceImpl implements BuyPrepaymentscashService {
    @Resource
    private BuyPrepaymentscashMapper buyPrepaymentscashMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyPrepaymentscashPojo getEntity(String key,String tid) {
        return this.buyPrepaymentscashMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyPrepaymentscashPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyPrepaymentscashPojo> lst = buyPrepaymentscashMapper.getPageList(queryParam);
            PageInfo<BuyPrepaymentscashPojo> pageInfo = new PageInfo<BuyPrepaymentscashPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BuyPrepaymentscashPojo> getList(String Pid,String tid) { 
        try {
            List<BuyPrepaymentscashPojo> lst = buyPrepaymentscashMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param buyPrepaymentscashPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyPrepaymentscashPojo insert(BuyPrepaymentscashPojo buyPrepaymentscashPojo) {
        //初始化item的NULL
        BuyPrepaymentscashPojo itempojo =this.clearNull(buyPrepaymentscashPojo);
        BuyPrepaymentscashEntity buyPrepaymentscashEntity = new BuyPrepaymentscashEntity(); 
        BeanUtils.copyProperties(itempojo,buyPrepaymentscashEntity);
        
          buyPrepaymentscashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          buyPrepaymentscashEntity.setRevision(1);  //乐观锁      
          this.buyPrepaymentscashMapper.insert(buyPrepaymentscashEntity);
        return this.getEntity(buyPrepaymentscashEntity.getId(),buyPrepaymentscashEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param buyPrepaymentscashPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyPrepaymentscashPojo update(BuyPrepaymentscashPojo buyPrepaymentscashPojo) {
        BuyPrepaymentscashEntity buyPrepaymentscashEntity = new BuyPrepaymentscashEntity(); 
        BeanUtils.copyProperties(buyPrepaymentscashPojo,buyPrepaymentscashEntity);
        this.buyPrepaymentscashMapper.update(buyPrepaymentscashEntity);
        return this.getEntity(buyPrepaymentscashEntity.getId(),buyPrepaymentscashEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.buyPrepaymentscashMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param buyPrepaymentscashPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BuyPrepaymentscashPojo clearNull(BuyPrepaymentscashPojo buyPrepaymentscashPojo){
     //初始化NULL字段
     if(buyPrepaymentscashPojo.getPid()==null) buyPrepaymentscashPojo.setPid("");
     if(buyPrepaymentscashPojo.getCashaccid()==null) buyPrepaymentscashPojo.setCashaccid("");
     if(buyPrepaymentscashPojo.getCashaccname()==null) buyPrepaymentscashPojo.setCashaccname("");
     if(buyPrepaymentscashPojo.getAmount()==null) buyPrepaymentscashPojo.setAmount(0D);
     if(buyPrepaymentscashPojo.getRownum()==null) buyPrepaymentscashPojo.setRownum(0);
     if(buyPrepaymentscashPojo.getRemark()==null) buyPrepaymentscashPojo.setRemark("");
     if(buyPrepaymentscashPojo.getCustom1()==null) buyPrepaymentscashPojo.setCustom1("");
     if(buyPrepaymentscashPojo.getCustom2()==null) buyPrepaymentscashPojo.setCustom2("");
     if(buyPrepaymentscashPojo.getCustom3()==null) buyPrepaymentscashPojo.setCustom3("");
     if(buyPrepaymentscashPojo.getCustom4()==null) buyPrepaymentscashPojo.setCustom4("");
     if(buyPrepaymentscashPojo.getCustom5()==null) buyPrepaymentscashPojo.setCustom5("");
     if(buyPrepaymentscashPojo.getCustom6()==null) buyPrepaymentscashPojo.setCustom6("");
     if(buyPrepaymentscashPojo.getCustom7()==null) buyPrepaymentscashPojo.setCustom7("");
     if(buyPrepaymentscashPojo.getCustom8()==null) buyPrepaymentscashPojo.setCustom8("");
     if(buyPrepaymentscashPojo.getCustom9()==null) buyPrepaymentscashPojo.setCustom9("");
     if(buyPrepaymentscashPojo.getCustom10()==null) buyPrepaymentscashPojo.setCustom10("");
     if(buyPrepaymentscashPojo.getTenantid()==null) buyPrepaymentscashPojo.setTenantid("");
     if(buyPrepaymentscashPojo.getRevision()==null) buyPrepaymentscashPojo.setRevision(0);
     return buyPrepaymentscashPojo;
     }
}
