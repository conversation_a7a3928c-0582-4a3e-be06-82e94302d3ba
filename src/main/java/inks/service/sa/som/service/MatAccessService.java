package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatAccessEntity;
import inks.service.sa.som.domain.pojo.MatAccessPojo;
import inks.service.sa.som.domain.pojo.MatAccessitemdetailPojo;

import java.text.ParseException;
import java.util.Map;

/**
 * 出入库主表(MatAccess)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-12 14:44:33
 */
public interface MatAccessService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatAccessPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatAccessitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatAccessPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatAccessPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatAccessPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matAccessPojo 实例对象
     * @return 实例对象
     */
    MatAccessPojo insert(MatAccessPojo matAccessPojo);

    /**
     * 修改数据
     *
     * @param matAccesspojo 实例对象
     * @return 实例对象
     */
    MatAccessPojo update(MatAccessPojo matAccesspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    /**
     * 新增数据
     *
     * @param matAccessPojo 实例对象
     * @return 实例对象
     */
    MatAccessPojo insertRed(MatAccessPojo matAccessPojo, MatAccessEntity matAccessEntityOrg);


//    /**
//     * Wip过数入库
//     *
//     * @return 实例对象
//     */
//    MatAccessPojo createByWip(QuickWipqtyPojo quickWipqtyPojo);

    // 填充库存id
    MatAccessPojo fillInvoid(MatAccessPojo matAccessPojo);

    //刷新领料单成本
    int updateRequMatCostAmt(String key, String tid);

    //刷新销售订单成本
    int updateMachMatCostAmt(String key, String tid);

    String saveInit(MatAccessPojo matAccessPojo);

    MatAccessPojo saveOnly(String redisKey);

    void updatePrintcount(MatAccessPojo billPrintPojo);

    PageInfo<Map<String, Object>> getSumPageListByGoods(QueryParam queryParam);

    Boolean quickInStoreByBuyFinishing(String finishid, String storeid, MatAccessPojo matAccessPojo) throws ParseException;

    Boolean quickInStoreByMatRequisition(String requisitionid, String storeid,String billtitle ,MatAccessPojo matAccessPojo) throws ParseException;
}
