package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.MatBomdetailPojo;
import inks.common.core.domain.MrpBomdetailPojo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.export.MatBomlayerPojo;
import inks.service.sa.som.domain.pojo.MatBomPojo;
import inks.service.sa.som.domain.pojo.MatBomitemdetailPojo;

import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 物料Bom(MatBom)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-11 14:43:01
 */
public interface MatBomService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatBomitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatBomPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatBomPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matBomPojo 实例对象
     * @return 实例对象
     */
    MatBomPojo insert(MatBomPojo matBomPojo);

    /**
     * 修改数据
     *
     * @param matBompojo 实例对象
     * @return 实例对象
     */
    MatBomPojo update(MatBomPojo matBompojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param matBomPojo 实例对象
     * @return 实例对象
     */
    MatBomPojo approval(MatBomPojo matBomPojo);

//    /**
//     * 通过ID查询单条数据
//     *
//     * @param key 主键
//     * @return 实例对象
//     */
//    MatBomtreePojo getTreeEntity(String key, String tid);

//    /**
//     * 通过ID查询单条数据
//     *
//     * @param key 主键
//     * @return 实例对象
//     */
//    List<MatBomdetailPojo> getBomDetailByGoodsid(String key, Double qty, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomPojo getEntityByGoodsid(String key, String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomPojo getBillEntityByGoodsid(String key, Double qty, Integer need, String tid);

//    /**
//     * 通过ID查询单条数据
//     *
//     * @param key 主键
//     * @return 实例对象
//     */
//    List<MatBomorderitemdetailPojo> getListByItemGoodsid(String key, String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<MatBomdetailPojo> getBomDetail(String key, Double qty, String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<MrpBomdetailPojo> getMrpBomDetail(String key, Double qty, String tid);


    MatBomlayerPojo createBomByLayer(MatBomlayerPojo matBomlayerPojo);

    MatBomlayerPojo getItemAllByLayer(String key, String tenantid, Double qty, Integer need);

    void getItemAllByLayerStart(String hKey, String bomid, String tenantid, Double qty, Integer need);

    /**
     * 通过Bom.ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBomlayerPojo getBillEntityByLayer(String key, String tenantid);

    String findBomidByGoodsid(String key, String tid);

    // 递归方法
    void getUnAssessBomListRecursive(String matBomId, List<MatBomPojo> unAssessBomList, AtomicInteger count, String tid);

    int approvalBatch(List<String> keys, LoginUser loginUser, Date date);
}




