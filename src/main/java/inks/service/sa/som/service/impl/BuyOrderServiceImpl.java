package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.exception.WarnException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.mapper.SaConfigMapper;
import inks.service.sa.som.domain.BuyOrderEntity;
import inks.service.sa.som.domain.BuyOrderitemEntity;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.*;
import inks.service.sa.som.service.BuyOrderService;
import inks.service.sa.som.service.BuyOrderitemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 采购合同(BuyOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 20:33:30
 */
@Service("buyOrderService")
public class BuyOrderServiceImpl implements BuyOrderService {
    @Resource
    private BusMachiningMapper busMachiningMapper;
    @Resource
    private BuyOrderMapper buyOrderMapper;

    @Resource
    private BuyOrderitemMapper buyOrderitemMapper;


    @Resource
    private BuyOrderitemService buyOrderitemService;


    @Resource
    private BuyPlanitemMapper buyPlanitemMapper;

    @Resource
    private BuyPlanMapper buyPlanMapper;


    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private SyncMapper syncMapper;
    @Resource
    private SaConfigMapper saConfigMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyOrderPojo getEntity(String key, String tid) {
        return this.buyOrderMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyOrderitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyOrderitemdetailPojo> lst = buyOrderMapper.getPageList(queryParam);
            PageInfo<BuyOrderitemdetailPojo> pageInfo = new PageInfo<BuyOrderitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyOrderPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BuyOrderPojo buyOrderPojo = this.buyOrderMapper.getEntity(key, tid);
            //读取子表
            buyOrderPojo.setItem(buyOrderitemMapper.getList(buyOrderPojo.getId(), buyOrderPojo.getTenantid()));
            return buyOrderPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyOrderPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyOrderPojo> lst = buyOrderMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (BuyOrderPojo buyOrderPojo : lst) {
                buyOrderPojo.setItem(buyOrderitemMapper.getList(buyOrderPojo.getId(), buyOrderPojo.getTenantid()));
            }
            PageInfo<BuyOrderPojo> pageInfo = new PageInfo<BuyOrderPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyOrderPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyOrderPojo> lst = buyOrderMapper.getPageTh(queryParam);
            PageInfo<BuyOrderPojo> pageInfo = new PageInfo<BuyOrderPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param buyOrderPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyOrderPojo insert(BuyOrderPojo buyOrderPojo, Integer warn) {
        //初始化NULL字段
        if (buyOrderPojo.getRefno() == null) buyOrderPojo.setRefno("");
        if (buyOrderPojo.getBilltype() == null) buyOrderPojo.setBilltype("");
        if (buyOrderPojo.getBilltitle() == null) buyOrderPojo.setBilltitle("");
        if (buyOrderPojo.getBilldate() == null) buyOrderPojo.setBilldate(new Date());
        if (buyOrderPojo.getGroupid() == null) buyOrderPojo.setGroupid("");
        if (buyOrderPojo.getPayment() == null) buyOrderPojo.setPayment("");
        if (buyOrderPojo.getOrderno() == null) buyOrderPojo.setOrderno("");
        if (buyOrderPojo.getArrivaladd() == null) buyOrderPojo.setArrivaladd("");
        if (buyOrderPojo.getTransport() == null) buyOrderPojo.setTransport("");
        if (buyOrderPojo.getLinkman() == null) buyOrderPojo.setLinkman("");
        if (buyOrderPojo.getLinktel() == null) buyOrderPojo.setLinktel("");
        if (buyOrderPojo.getTaxrate() == null) buyOrderPojo.setTaxrate(0);
        if (buyOrderPojo.getOperator() == null) buyOrderPojo.setOperator("");
        if (buyOrderPojo.getPrepayments() == null) buyOrderPojo.setPrepayments(0D);
        if (buyOrderPojo.getSummary() == null) buyOrderPojo.setSummary("");
        if (buyOrderPojo.getCreateby() == null) buyOrderPojo.setCreateby("");
        if (buyOrderPojo.getCreatebyid() == null) buyOrderPojo.setCreatebyid("");
        if (buyOrderPojo.getCreatedate() == null) buyOrderPojo.setCreatedate(new Date());
        if (buyOrderPojo.getLister() == null) buyOrderPojo.setLister("");
        if (buyOrderPojo.getListerid() == null) buyOrderPojo.setListerid("");
        if (buyOrderPojo.getModifydate() == null) buyOrderPojo.setModifydate(new Date());
        if (buyOrderPojo.getAssessor() == null) buyOrderPojo.setAssessor("");
        if (buyOrderPojo.getAssessorid() == null) buyOrderPojo.setAssessorid("");
        if (buyOrderPojo.getAssessdate() == null) buyOrderPojo.setAssessdate(new Date());
        if (buyOrderPojo.getBilltaxamount() == null) buyOrderPojo.setBilltaxamount(0D);
        if (buyOrderPojo.getBilltaxtotal() == null) buyOrderPojo.setBilltaxtotal(0D);
        if (buyOrderPojo.getBillamount() == null) buyOrderPojo.setBillamount(0D);
        if (buyOrderPojo.getBillstatecode() == null) buyOrderPojo.setBillstatecode("");
        if (buyOrderPojo.getBillstatedate() == null) buyOrderPojo.setBillstatedate(new Date());
        if (buyOrderPojo.getBillplandate() == null) buyOrderPojo.setBillplandate(new Date());
        if (buyOrderPojo.getItemcount() == null) buyOrderPojo.setItemcount(buyOrderPojo.getItem().size());
        if (buyOrderPojo.getFinishcount() == null) buyOrderPojo.setFinishcount(0);
        if (buyOrderPojo.getDisannulcount() == null) buyOrderPojo.setDisannulcount(0);
        if (buyOrderPojo.getPrintcount() == null) buyOrderPojo.setPrintcount(0);
        if (buyOrderPojo.getOaflowmark() == null) buyOrderPojo.setOaflowmark(0);
        if (buyOrderPojo.getInvoamt() == null) buyOrderPojo.setInvoamt(0D);
        if (buyOrderPojo.getInvocount() == null) buyOrderPojo.setInvocount(0);
        if (buyOrderPojo.getFirstamt() == null) buyOrderPojo.setFirstamt(0D);
        if (buyOrderPojo.getLastamt() == null) buyOrderPojo.setLastamt(0D);
        if (buyOrderPojo.getProjectid() == null) buyOrderPojo.setProjectid("");
        if (buyOrderPojo.getProjcode() == null) buyOrderPojo.setProjcode("");
        if (buyOrderPojo.getProjname() == null) buyOrderPojo.setProjname("");
        if (buyOrderPojo.getCustom1() == null) buyOrderPojo.setCustom1("");
        if (buyOrderPojo.getCustom2() == null) buyOrderPojo.setCustom2("");
        if (buyOrderPojo.getCustom3() == null) buyOrderPojo.setCustom3("");
        if (buyOrderPojo.getCustom4() == null) buyOrderPojo.setCustom4("");
        if (buyOrderPojo.getCustom5() == null) buyOrderPojo.setCustom5("");
        if (buyOrderPojo.getCustom6() == null) buyOrderPojo.setCustom6("");
        if (buyOrderPojo.getCustom7() == null) buyOrderPojo.setCustom7("");
        if (buyOrderPojo.getCustom8() == null) buyOrderPojo.setCustom8("");
        if (buyOrderPojo.getCustom9() == null) buyOrderPojo.setCustom9("");
        if (buyOrderPojo.getCustom10() == null) buyOrderPojo.setCustom10("");
        if (buyOrderPojo.getDeptid() == null) buyOrderPojo.setDeptid("");
        if (buyOrderPojo.getTenantid() == null) buyOrderPojo.setTenantid("");
        if (buyOrderPojo.getTenantname() == null) buyOrderPojo.setTenantname("");
        if (buyOrderPojo.getRevision() == null) buyOrderPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BuyOrderEntity buyOrderEntity = new BuyOrderEntity();
        BeanUtils.copyProperties(buyOrderPojo, buyOrderEntity);
        //设置id和新建日期
        buyOrderEntity.setId(id);
        buyOrderEntity.setRevision(1);  //乐观锁

        //Item子表处理
        List<BuyOrderitemPojo> lst = buyOrderPojo.getItem();
        if (lst != null) {
            // 同步采购计划检查
            if (buyOrderEntity.getBilltype().equals("采购计划")) {
                for (int i = 0; i < lst.size(); i++) {
                    if (lst.get(i).getCiteitemid() != null && !"".equals(lst.get(i).getCiteitemid()))// 超数检查
                    {
                        BuyPlanitemPojo buyPlanitemPojo = this.buyPlanitemMapper.getEntity(lst.get(i).getCiteitemid(), buyOrderEntity.getTenantid());
                        if (buyPlanitemPojo != null) {
                            if (buyPlanitemPojo.getBuyqty() + lst.get(i).getQuantity() > buyPlanitemPojo.getQuantity()) {
                                int Rowno = i + 1;
                                throw new RuntimeException(Rowno + "行,采购总数:" + (buyPlanitemPojo.getBuyqty() + lst.get(i).getQuantity()) + "超出计划数:" + buyPlanitemPojo.getQuantity());
                            }
                        } else {
                            throw new RuntimeException("关联单据丢失:" + lst.get(i).getCiteuid());
                        }
                    }
                }
            }
            //订单采购预收率
            //	module.buy.machorderdeporate 百分之1~100 ；0 不限
            //业务场景： 一个销售订单整单转到采购订单，需要验证销售订单预收率（预收款/billTaxAmount）;操作员保存后，有财务人员（放行权）审核；
            if (warn == 1 && "销售订单".equals(buyOrderEntity.getBilltype())) {
                // 1. 配置：1-100；0 表示不限
                String cfgStr = saConfigMapper.getCfgValueByCfgKey("module.buy.machorderdeporate");
                BigDecimal cfg = StringUtils.isBlank(cfgStr) ? BigDecimal.ZERO : new BigDecimal(cfgStr.trim());
                // 2. 查询销售订单金额与预收款 取第一个Machitemid即可
                BusMachiningPojo mach = busMachiningMapper.getEntityByitemid(lst.get(0).getMachitemid(), buyOrderPojo.getTenantid());
                if (mach == null) {
                    throw new BaseBusinessException("未找到对应销售订单");
                }
                BigDecimal billAmt = BigDecimal.valueOf(mach.getBilltaxamount());
                BigDecimal advaAmt = BigDecimal.valueOf(mach.getAdvaamount());
                // 3. 校验
                if (cfg.compareTo(BigDecimal.ZERO) > 0 && billAmt.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal actualRate = advaAmt.divide(billAmt, 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100));
                    if (actualRate.compareTo(cfg) < 0) {
                        throw new WarnException("销售订单预收率不足，当前 " + actualRate.setScale(2, RoundingMode.HALF_UP) + "%，要求≥" + cfg + "%");
                    }
                }
            }
        }
        transactionTemplate.execute((status) -> {
            //插入主表
            this.buyOrderMapper.insert(buyOrderEntity);
            if (lst != null) {
                //循环每个item子表
                for (BuyOrderitemPojo buyOrderitemPojo : lst) {
                    //初始化item的NULL
                    BuyOrderitemPojo itemPojo = this.buyOrderitemService.clearNull(buyOrderitemPojo);
                    BuyOrderitemEntity buyOrderitemEntity = new BuyOrderitemEntity();
                    BeanUtils.copyProperties(itemPojo, buyOrderitemEntity);
                    //设置id和Pid
                    buyOrderitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                    buyOrderitemEntity.setPid(id);
                    buyOrderitemEntity.setTenantid(buyOrderPojo.getTenantid());
                    buyOrderitemEntity.setRevision(1);  //乐观锁
                    //插入子表
                    this.buyOrderitemMapper.insert(buyOrderitemEntity);
                    // 同步采购计划
                    if (buyOrderEntity.getBilltype().equals("采购计划") &&
                            buyOrderitemEntity.getCiteitemid() != null && !"".equals(buyOrderitemEntity.getCiteitemid())) {
                        this.buyOrderMapper.updatePlanItemBuyQtyAndFinishQty(buyOrderitemEntity.getCiteitemid(), buyOrderEntity.getTenantid());
                        this.buyOrderMapper.updatePlanBuyCountAndFinishCount(buyOrderitemEntity.getCiteitemid(), buyOrderEntity.getTenantid());
                    }

                    // 如果是MRP需求
                    if ("MRP需求".equals(buyOrderPojo.getBilltype())) {
                        this.buyOrderMapper.updateMrpBuyOrderFinish(buyOrderitemPojo.getMrpitemid(), buyOrderitemPojo.getMrpuid(), buyOrderPojo.getTenantid());
                        WkMrpitemPojo wkMrpitemPojo = this.buyPlanMapper.getMrpItemEntity(buyOrderitemPojo.getMrpitemid(), buyOrderPojo.getTenantid());
                        if (wkMrpitemPojo != null && wkMrpitemPojo.getBuyorderqty() + wkMrpitemPojo.getBuyplanqty() > wkMrpitemPojo.getNeedqty()) {
                            Double wkqty = wkMrpitemPojo.getBuyorderqty() + wkMrpitemPojo.getBuyplanqty();
                            throw new RuntimeException(buyOrderitemPojo.getGoodsuid() + ":采购总数" + wkqty + "大于需求数" + wkMrpitemPojo.getNeedqty());
                        }
                        // 刷新MRP 采购相关完工数(采购订单、采购计划)
                        this.buyPlanMapper.updateMrpBuyFinishCount(buyOrderitemPojo.getMrpitemid(), buyOrderPojo.getTenantid());
                        // 刷新MRP完工数
                        this.buyPlanMapper.updateMrpFinishCount(buyOrderitemPojo.getMrpitemid(), buyOrderPojo.getTenantid());
                    }

                    // 同步货品数量 MQ生产者  nanno 20230222
                    //MqBaseParamPojo mqBaseParamPojo = new MqBaseParamPojo(lst.get(i).getGoodsid(), "BuyRemQty", buyOrderPojo.getTenantid());
                    //this.rabbitTemplate.convertAndSend("updateGoodsQty", JSON.toJSONString(mqBaseParamPojo));
                    this.syncMapper.updateGoodsBuyRemQty(buyOrderitemPojo.getGoodsid(), buyOrderPojo.getTenantid());

                }

            }
            if (lst != null) {
                //循环每个item子表 同步单据关联 采购计划、销售订单、订单物料
                syncOrderCite(lst, buyOrderPojo.getBilltype(), buyOrderPojo.getTenantid()); //同步单据引用
            }
            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BuyOrderitemPojo::getGoodsid).collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                syncMapper.updateGoodsBuyRemQty(goodsid, buyOrderPojo.getTenantid());
            });


            return Boolean.TRUE;
        });

        //返回Bill实例
        return this.getBillEntity(buyOrderEntity.getId(), buyOrderEntity.getTenantid());

    }


    // 同步单据关联 采购计划、销售订单、订单物料
    private void syncOrderCite(List<BuyOrderitemPojo> lst, String billType, String tid) {
        for (BuyOrderitemPojo buyOrderitemPojo : lst) {
            // 同步采购计划
            if (billType.equals("采购计划") && isNotBlank(buyOrderitemPojo.getCiteitemid())) {
                this.buyOrderMapper.updatePlanBuyCountAndFinishCount(buyOrderitemPojo.getCiteitemid(), tid);
            }
            // 同步销售订单的采购数量: BuyQuantity
            if (billType.equals("销售订单") && isNotBlank(buyOrderitemPojo.getMachitemid())) {
                this.buyOrderMapper.updateMachingBuyQuantity(buyOrderitemPojo.getMachitemid(), tid);
            }
            // 同步销售订单("订单物料")的采购数量: MatBuyQty
            if (billType.equals("订单物料")) {
                if (buyOrderitemPojo.getSourcetype() == 3) {//0为其他，1为计划，2为订单货品，3为订单物料
                    this.buyOrderMapper.updateMachingMatBuyQty(buyOrderitemPojo.getMachitemid(), tid);
                }
            }
        }
    }

    /**
     * 修改数据
     *
     * @param buyOrderPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyOrderPojo update(BuyOrderPojo buyOrderPojo, Integer warn) {
        String billType = buyOrderPojo.getBilltype();
        String tid = buyOrderPojo.getTenantid();
        //主表更改
        BuyOrderEntity buyOrderEntity = new BuyOrderEntity();
        BeanUtils.copyProperties(buyOrderPojo, buyOrderEntity);
        //更新ItemCount
        buyOrderEntity.setItemcount(buyOrderPojo.getItem().size());
        //Item子表处理
        List<BuyOrderitemPojo> lst = buyOrderPojo.getItem();
        List<BuyOrderitemPojo> lstDel = new ArrayList<>();
//        // 计算汇总  Eric 20220615
//        Double billTaxAmount = 0D;
//        Double billAmount = 0D;
//        Double billTaxTotal = 0D;
//        if (lst != null) {
//            for (BuyOrderitemPojo item : lst) {
//                billTaxAmount += item.getTaxamount();
//                billTaxTotal += item.getTaxtotal();
//                billAmount += item.getAmount();
//            }
//            buyOrderEntity.setItemcount(lst.size());
//        }
//        buyOrderEntity.setBilltaxamount(billTaxAmount);
//        buyOrderEntity.setBilltaxtotal(billTaxTotal);
//        buyOrderEntity.setBillamount(billAmount);

        if (lst != null) {
            // 同步采购计划检查
            if (buyOrderEntity.getBilltype().equals("采购计划")) {
                for (int i = 0; i < lst.size(); i++) {
                    if (lst.get(i).getCiteitemid() != null && !"".equals(lst.get(i).getCiteitemid()))// 超数检查
                    {
                        Double OrgQty = 0D;
                        if (lst.get(i).getId() != null && !"".equals(lst.get(i).getId())) {
                            BuyOrderitemPojo buyOrderitemPojo = this.buyOrderitemMapper.getEntity(lst.get(i).getId(), buyOrderEntity.getTenantid());
                            if (buyOrderitemPojo != null) OrgQty = buyOrderitemPojo.getQuantity();
                        }
                        BuyPlanitemPojo buyPlanitemPojo = this.buyPlanitemMapper.getEntity(lst.get(i).getCiteitemid(), buyOrderEntity.getTenantid());
                        if (buyPlanitemPojo != null) {
                            if (buyPlanitemPojo.getBuyqty() - OrgQty + lst.get(i).getQuantity() > buyPlanitemPojo.getQuantity()) {
                                int Rowno = i + 1;
                                throw new RuntimeException(Rowno + "行,采购总数:" + (buyPlanitemPojo.getBuyqty() - OrgQty + lst.get(i).getQuantity()) + "超出计划数:" + buyPlanitemPojo.getQuantity());
                            }
                        } else {
                            throw new RuntimeException("关联单据丢失:" + lst.get(i).getCiteuid());
                        }
                    }
                }
            }
            //订单采购预收率
            //	module.buy.machorderdeporate 百分之1~100 ；0 不限
            //业务场景： 一个销售订单整单转到采购订单，需要验证销售订单预收率（预收款/billTaxAmount）;操作员保存后，有财务人员（放行权）审核；
            if (warn == 1 && "销售订单".equals(buyOrderEntity.getBilltype())) {
                // 1. 配置：1-100；0 表示不限
                String cfgStr = saConfigMapper.getCfgValueByCfgKey("module.buy.machorderdeporate");
                BigDecimal cfg = StringUtils.isBlank(cfgStr) ? BigDecimal.ZERO : new BigDecimal(cfgStr.trim());
                // 2. 查询销售订单金额与预收款 取第一个Machitemid即可
                BusMachiningPojo mach = busMachiningMapper.getEntityByitemid(lst.get(0).getMachitemid(), buyOrderPojo.getTenantid());
                if (mach == null) {
                    throw new BaseBusinessException("未找到对应销售订单");
                }
                BigDecimal billAmt = BigDecimal.valueOf(mach.getBilltaxamount());
                BigDecimal advaAmt = BigDecimal.valueOf(mach.getAdvaamount());
                // 3. 校验
                if (cfg.compareTo(BigDecimal.ZERO) > 0 && billAmt.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal actualRate = advaAmt.divide(billAmt, 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100));
                    if (actualRate.compareTo(cfg) < 0) {
                        throw new WarnException("销售订单预收率不足，当前 " + actualRate.setScale(2, RoundingMode.HALF_UP) + "%，要求≥" + cfg + "%");
                    }
                }
            }
        }
        // 开始事务
        transactionTemplate.execute((status) -> {
            this.buyOrderMapper.update(buyOrderEntity);

            if (buyOrderPojo.getItem() != null) {
                //获取被删除的Item
                List<String> lstDelIds = buyOrderMapper.getDelItemIds(buyOrderPojo);
                if (lstDelIds != null) {
                    //循环每个删除item子表
                    for (String lstDelId : lstDelIds) {
                        BuyOrderitemPojo delPojo = this.buyOrderitemMapper.getEntity(lstDelId, buyOrderEntity.getTenantid());
                        lstDel.add(delPojo);
                        // 加上引用检查
                        List<String> lstcite = getItemCiteBillName(delPojo.getId(), delPojo.getPid(), delPojo.getTenantid());
                        if (!lstcite.isEmpty()) {
                            throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                        }
                        this.buyOrderitemMapper.delete(lstDelId, buyOrderEntity.getTenantid());
                        // 同步采购计划
                        if (billType.equals("采购计划")) {
                            this.buyOrderMapper.updatePlanItemBuyQtyAndFinishQty(delPojo.getCiteitemid(), buyOrderEntity.getTenantid());
                            this.buyOrderMapper.updatePlanBuyCountAndFinishCount(delPojo.getCiteitemid(), buyOrderEntity.getTenantid());
                        }
                        // 如果是MRP需求
                        if ("MRP需求".equals(billType)) {
                            this.buyOrderMapper.updateMrpBuyOrderFinish(delPojo.getMrpitemid(), delPojo.getMrpuid(), tid);
                            // 刷新MRP 采购相关完工数(采购订单、采购计划)
                            this.buyPlanMapper.updateMrpBuyFinishCount(delPojo.getMrpitemid(), tid);
                            // 刷新MRP完工数
                            this.buyPlanMapper.updateMrpFinishCount(delPojo.getMrpitemid(), tid);
                        }

                    }
                }
                if (lst != null) {
                    //循环每个item子表
                    for (BuyOrderitemPojo buyOrderitemPojo : lst) {
                        BuyOrderitemEntity buyOrderitemEntity = new BuyOrderitemEntity();
                        if ("".equals(buyOrderitemPojo.getId()) || buyOrderitemPojo.getId() == null) {
                            //初始化item的NULL
                            BuyOrderitemPojo itemPojo = this.buyOrderitemService.clearNull(buyOrderitemPojo);
                            BeanUtils.copyProperties(itemPojo, buyOrderitemEntity);
                            //设置id和Pid
                            buyOrderitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                            buyOrderitemEntity.setPid(buyOrderEntity.getId());  // 主表 id
                            buyOrderitemEntity.setTenantid(tid);   // 租户id
                            buyOrderitemEntity.setRevision(1);  // 乐观锁
                            //插入子表
                            this.buyOrderitemMapper.insert(buyOrderitemEntity);
                        } else {
                            BeanUtils.copyProperties(buyOrderitemPojo, buyOrderitemEntity);
                            buyOrderitemEntity.setTenantid(tid);
                            this.buyOrderitemMapper.update(buyOrderitemEntity);
                        }
                        // 同步采购计划
                        if (buyOrderEntity.getBilltype().equals("采购计划") &&
                                buyOrderitemEntity.getCiteitemid() != null && !"".equals(buyOrderitemEntity.getCiteitemid())) {
                            this.buyOrderMapper.updatePlanItemBuyQtyAndFinishQty(buyOrderitemEntity.getCiteitemid(), buyOrderEntity.getTenantid());
                            this.buyOrderMapper.updatePlanBuyCountAndFinishCount(buyOrderitemEntity.getCiteitemid(), buyOrderEntity.getTenantid());

                        }

                        // 如果是MRP需求
                        if ("MRP需求".equals(billType)) {
                            this.buyOrderMapper.updateMrpBuyOrderFinish(buyOrderitemPojo.getMrpitemid(), buyOrderitemPojo.getMrpuid(), tid);
                            WkMrpitemPojo wkMrpitemPojo = this.buyPlanMapper.getMrpItemEntity(buyOrderitemPojo.getMrpitemid(), tid);
                            if (wkMrpitemPojo != null && wkMrpitemPojo.getBuyorderqty() + wkMrpitemPojo.getBuyplanqty() > wkMrpitemPojo.getNeedqty()) {
                                Double wkqty = wkMrpitemPojo.getBuyorderqty() + wkMrpitemPojo.getBuyplanqty();
                                throw new RuntimeException(buyOrderitemPojo.getGoodsuid() + ":采购总数" + wkqty + "大于需求数" + wkMrpitemPojo.getNeedqty());
                            }
                            // 刷新MRP 采购相关完工数(采购订单、采购计划)
                            this.buyPlanMapper.updateMrpBuyFinishCount(buyOrderitemPojo.getMrpitemid(), tid);
                            // 刷新MRP完工数
                            this.buyPlanMapper.updateMrpFinishCount(buyOrderitemPojo.getMrpitemid(), tid);
                        }
                    }
                }
            }

            if (!lstDel.isEmpty()) {
                //同步单据关联 采购计划、销售订单、订单物料
                syncOrderCite(lstDel, billType, tid);
            }
            if (lst != null) {
                syncOrderCite(lst, billType, tid);
            }

            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BuyOrderitemPojo::getGoodsid).collect(Collectors.toSet());
            Set<String> goodsidLstDelSet = lstDel.stream().map(BuyOrderitemPojo::getGoodsid).collect(Collectors.toSet());
            goodsidLstSet.addAll(goodsidLstDelSet);
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                syncMapper.updateGoodsBuyRemQty(goodsid, tid);
            });
            return Boolean.TRUE;
        });


        //返回Bill实例
        return this.getBillEntity(buyOrderEntity.getId(), buyOrderEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        BuyOrderPojo buyOrderPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BuyOrderitemPojo> lst = buyOrderPojo.getItem();
        Integer delCount = 0;
        delCount = transactionTemplate.execute((status) -> {
            if (lst != null) {
                //循环每个删除item子表
                for (BuyOrderitemPojo buyOrderitemPojo : lst) {
                    // 加上引用检查
                    List<String> lstcite = getItemCiteBillName(buyOrderitemPojo.getId(), buyOrderitemPojo.getPid(), tid);
                    if (!lstcite.isEmpty()) {
                        throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                    }

                    this.buyOrderitemMapper.delete(buyOrderitemPojo.getId(), tid);
                    // 同步采购计划
                    if (buyOrderPojo.getBilltype().equals("采购计划") && !buyOrderitemPojo.getCiteitemid().isEmpty()) {
                        this.buyOrderMapper.updatePlanItemBuyQtyAndFinishQty(buyOrderitemPojo.getCiteitemid(), tid);
                        this.buyOrderMapper.updatePlanBuyCountAndFinishCount(buyOrderitemPojo.getCiteitemid(), tid);
                    }

                    // 如果是MRP需求
                    if ("MRP需求".equals(buyOrderPojo.getBilltype())) {
                        this.buyOrderMapper.updateMrpBuyOrderFinish(buyOrderitemPojo.getMrpitemid(), buyOrderitemPojo.getMrpuid(), tid);
                        // 刷新MRP 采购相关完工数(采购订单、采购计划)
                        this.buyPlanMapper.updateMrpBuyFinishCount(buyOrderitemPojo.getMrpitemid(), tid);
                        // 刷新MRP完工数
                        this.buyPlanMapper.updateMrpFinishCount(buyOrderitemPojo.getMrpitemid(), tid);
                    }

                }
            }
            Integer num = this.buyOrderMapper.delete(key, tid);
            if (lst != null) {
                //循环每个删除item子表 同步单据关联 采购计划、销售订单、订单物料
                syncOrderCite(lst, buyOrderPojo.getBilltype(), tid);
            }

            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BuyOrderitemPojo::getGoodsid).collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                syncMapper.updateGoodsBuyRemQty(goodsid, tid);
            });
            return num;
        });

        return delCount;
    }


    /**
     * 审核数据
     *
     * @param buyOrderPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyOrderPojo approval(BuyOrderPojo buyOrderPojo) {
        //主表更改
        BuyOrderEntity buyOrderEntity = new BuyOrderEntity();
        BeanUtils.copyProperties(buyOrderPojo, buyOrderEntity);
        this.buyOrderMapper.approval(buyOrderEntity);
        //返回Bill实例
        return this.getBillEntity(buyOrderEntity.getId(), buyOrderEntity.getTenantid());
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyOrderPojo disannul(List<BuyOrderitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            BuyOrderitemPojo Pojo = lst.get(i);
            BuyOrderitemPojo dbPojo = this.buyOrderitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getFinishqty() > 0) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已被引用,禁止作废操作");
                    }
                    BuyOrderitemEntity entity = new BuyOrderitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    entity.setDisannuldate(new Date());
                    entity.setDisannullister(loginUser.getRealname());
                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(loginUser.getTenantid());
                    this.buyOrderitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BuyOrderitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            syncMapper.updateGoodsBuyRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.buyOrderMapper.updateDisannulCount(Pid, tid);
            //主表更改
            BuyOrderEntity buyOrderEntity = new BuyOrderEntity();
            buyOrderEntity.setId(Pid);
            buyOrderEntity.setLister(loginUser.getRealname());
            buyOrderEntity.setListerid(loginUser.getUserid());
            buyOrderEntity.setModifydate(new Date());
            buyOrderEntity.setTenantid(loginUser.getTenantid());
            this.buyOrderMapper.update(buyOrderEntity);
            //返回Bill实例
            return this.getBillEntity(buyOrderEntity.getId(), buyOrderEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyOrderPojo closed(List<BuyOrderitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            BuyOrderitemPojo Pojo = lst.get(i);
            BuyOrderitemPojo dbPojo = this.buyOrderitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    BuyOrderitemEntity entity = new BuyOrderitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.buyOrderitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BuyOrderitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            syncMapper.updateGoodsBuyRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.buyOrderMapper.updateFinishCount(Pid, tid);
            //主表更改
            BuyOrderEntity buyOrderEntity = new BuyOrderEntity();
            buyOrderEntity.setId(Pid);
            buyOrderEntity.setLister(loginUser.getRealname());
            buyOrderEntity.setListerid(loginUser.getUserid());
            buyOrderEntity.setModifydate(new Date());
            buyOrderEntity.setTenantid(loginUser.getTenantid());
            this.buyOrderMapper.update(buyOrderEntity);
            //返回Bill实例
            return this.getBillEntity(buyOrderEntity.getId(), buyOrderEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    // 查询Item是否被引用
    public List<String> getItemCiteBillName(String key, String pid, String tid) {
        return this.buyOrderMapper.getItemCiteBillName(key, pid, tid);
    }

    @Override
    public Map<String, Object> getWorkgroupInfo(String groupid, String tid) {
        return this.buyOrderMapper.getWorkgroupInfo(groupid, tid);
    }
}
