package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BusReceiptPojo;
import inks.service.sa.som.domain.pojo.BusReceiptitemdetailPojo;

/**
 * 收款单据(BusReceipt)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-15 15:34:47
 */
public interface BusReceiptService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusReceiptPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusReceiptitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusReceiptPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusReceiptPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusReceiptPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busReceiptPojo 实例对象
     * @return 实例对象
     */
    BusReceiptPojo insert(BusReceiptPojo busReceiptPojo);

    /**
     * 修改数据
     *
     * @param busReceiptpojo 实例对象
     * @return 实例对象
     */
    BusReceiptPojo update(BusReceiptPojo busReceiptpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param busReceiptPojo 实例对象
     * @return 实例对象
     */
    BusReceiptPojo approval(BusReceiptPojo busReceiptPojo);
}
