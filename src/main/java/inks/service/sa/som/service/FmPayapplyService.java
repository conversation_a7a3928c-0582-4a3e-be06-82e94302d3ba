package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmPayapplyPojo;
import inks.service.sa.som.domain.pojo.FmPayapplyitemdetailPojo;

/**
 * 往来核销(FmPayapply)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-13 21:30:01
 */
public interface FmPayapplyService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmPayapplyPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmPayapplyitemdetailPojo> getPageList(QueryParam queryParam);

 /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmPayapplyPojo getBillEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmPayapplyPojo> getBillList(QueryParam queryParam);
    
        /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmPayapplyPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param fmPayapplyPojo 实例对象
     * @return 实例对象
     */
    FmPayapplyPojo insert(FmPayapplyPojo fmPayapplyPojo);

    /**
     * 修改数据
     *
     * @param fmPayapplypojo 实例对象
     * @return 实例对象
     */
    FmPayapplyPojo update(FmPayapplyPojo fmPayapplypojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

                                                                                                                                                      }
