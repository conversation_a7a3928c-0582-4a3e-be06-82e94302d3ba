package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatCombinitemPojo;

import java.util.List;
/**
 * 组合项目(MatCombinitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-19 11:09:45
 */
public interface MatCombinitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCombinitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatCombinitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatCombinitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param matCombinitemPojo 实例对象
     * @return 实例对象
     */
    MatCombinitemPojo insert(MatCombinitemPojo matCombinitemPojo);

    /**
     * 修改数据
     *
     * @param matCombinitempojo 实例对象
     * @return 实例对象
     */
    MatCombinitemPojo update(MatCombinitemPojo matCombinitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param matCombinitempojo 实例对象
     * @return 实例对象
     */
    MatCombinitemPojo clearNull(MatCombinitemPojo matCombinitempojo);
}
