package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmPayapplyitemPojo;

import java.util.List;
/**
 * 往来核销项目(FmPayapplyitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-13 21:30:35
 */
public interface FmPayapplyitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmPayapplyitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmPayapplyitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<FmPayapplyitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param fmPayapplyitemPojo 实例对象
     * @return 实例对象
     */
    FmPayapplyitemPojo insert(FmPayapplyitemPojo fmPayapplyitemPojo);

    /**
     * 修改数据
     *
     * @param fmPayapplyitempojo 实例对象
     * @return 实例对象
     */
    FmPayapplyitemPojo update(FmPayapplyitemPojo fmPayapplyitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param fmPayapplyitempojo 实例对象
     * @return 实例对象
     */
    FmPayapplyitemPojo clearNull(FmPayapplyitemPojo fmPayapplyitempojo);
}
