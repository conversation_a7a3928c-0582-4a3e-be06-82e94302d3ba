package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.sa.som.domain.MatCarryoverEntity;
import inks.service.sa.som.domain.MatCarryoveritemEntity;
import inks.service.sa.som.domain.constant.MyConstant;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.MatCarryoverMapper;
import inks.service.sa.som.mapper.MatCarryoveritemMapper;
import inks.service.sa.som.mapper.MatStorageMapper;
import inks.service.sa.som.service.MatCarryoverService;
import inks.service.sa.som.service.MatCarryoveritemService;
import inks.service.sa.som.service.MatCarryoverrecService;
import inks.sa.common.core.service.SaRedisService;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 仓库结转(MatCarryover)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-06 11:24:00
 */
@Service("matCarryoverService")
public class MatCarryoverServiceImpl implements MatCarryoverService {
    @Resource
    private MatCarryoverMapper matCarryoverMapper;

    @Resource
    private MatCarryoveritemMapper matCarryoveritemMapper;

    
    @Resource
    private MatCarryoveritemService matCarryoveritemService;
    @Resource
    private MatStorageMapper matStorageMapper;
    @Resource
    private SaRedisService saRedisService;
  
    @Resource
    private MatCarryoverrecService matCarryoverrecService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatCarryoverPojo getEntity(String key, String tid) {
        return this.matCarryoverMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatCarryoveritemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatCarryoveritemdetailPojo> lst = matCarryoverMapper.getPageList(queryParam);
            PageInfo<MatCarryoveritemdetailPojo> pageInfo = new PageInfo<MatCarryoveritemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatCarryoverPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            MatCarryoverPojo matCarryoverPojo = this.matCarryoverMapper.getEntity(key, tid);
            //读取子表
            matCarryoverPojo.setItem(matCarryoveritemMapper.getList(matCarryoverPojo.getId(), matCarryoverPojo.getTenantid()));
            return matCarryoverPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatCarryoverPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatCarryoverPojo> lst = matCarryoverMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(matCarryoveritemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<MatCarryoverPojo> pageInfo = new PageInfo<MatCarryoverPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatCarryoverPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatCarryoverPojo> lst = matCarryoverMapper.getPageTh(queryParam);
            PageInfo<MatCarryoverPojo> pageInfo = new PageInfo<MatCarryoverPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matCarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatCarryoverPojo insert(MatCarryoverPojo matCarryoverPojo) {
//初始化NULL字段
        if (matCarryoverPojo.getRefno() == null) matCarryoverPojo.setRefno("");
        if (matCarryoverPojo.getBilltype() == null) matCarryoverPojo.setBilltype("");
        if (matCarryoverPojo.getBilldate() == null) matCarryoverPojo.setBilldate(new Date());
        if (matCarryoverPojo.getProjectid() == null) matCarryoverPojo.setProjectid("");
        if (matCarryoverPojo.getProjcode() == null) matCarryoverPojo.setProjcode("");
        if (matCarryoverPojo.getProjname() == null) matCarryoverPojo.setProjname("");
        if (matCarryoverPojo.getBilltitle() == null) matCarryoverPojo.setBilltitle("");
        if (matCarryoverPojo.getStoreid() == null) matCarryoverPojo.setStoreid("");
        if (matCarryoverPojo.getStorecode() == null) matCarryoverPojo.setStorecode("");
        if (matCarryoverPojo.getStorename() == null) matCarryoverPojo.setStorename("");
        if (matCarryoverPojo.getCarryyear() == null) matCarryoverPojo.setCarryyear(0);
        if (matCarryoverPojo.getCarrymonth() == null) matCarryoverPojo.setCarrymonth(0);
        if (matCarryoverPojo.getStartdate() == null) matCarryoverPojo.setStartdate(new Date());
        if (matCarryoverPojo.getEnddate() == null) matCarryoverPojo.setEnddate(new Date());
        if (matCarryoverPojo.getOperator() == null) matCarryoverPojo.setOperator("");
        if (matCarryoverPojo.getOperatorid() == null) matCarryoverPojo.setOperatorid("");
        if (matCarryoverPojo.getRownum() == null) matCarryoverPojo.setRownum(0);
        if (matCarryoverPojo.getSummary() == null) matCarryoverPojo.setSummary("");
        if (matCarryoverPojo.getCreateby() == null) matCarryoverPojo.setCreateby("");
        if (matCarryoverPojo.getCreatebyid() == null) matCarryoverPojo.setCreatebyid("");
        if (matCarryoverPojo.getCreatedate() == null) matCarryoverPojo.setCreatedate(new Date());
        if (matCarryoverPojo.getLister() == null) matCarryoverPojo.setLister("");
        if (matCarryoverPojo.getListerid() == null) matCarryoverPojo.setListerid("");
        if (matCarryoverPojo.getModifydate() == null) matCarryoverPojo.setModifydate(new Date());
        if (matCarryoverPojo.getBillopenamount() == null) matCarryoverPojo.setBillopenamount(0D);
        if (matCarryoverPojo.getBillinamount() == null) matCarryoverPojo.setBillinamount(0D);
        if (matCarryoverPojo.getBilloutamount() == null) matCarryoverPojo.setBilloutamount(0D);
        if (matCarryoverPojo.getBillcloseamount() == null) matCarryoverPojo.setBillcloseamount(0D);
        if (matCarryoverPojo.getItemcount() == null) matCarryoverPojo.setItemcount(matCarryoverPojo.getItem().size());
        if (matCarryoverPojo.getPrintcount() == null) matCarryoverPojo.setPrintcount(0);
        if (matCarryoverPojo.getCustom1() == null) matCarryoverPojo.setCustom1("");
        if (matCarryoverPojo.getCustom2() == null) matCarryoverPojo.setCustom2("");
        if (matCarryoverPojo.getCustom3() == null) matCarryoverPojo.setCustom3("");
        if (matCarryoverPojo.getCustom4() == null) matCarryoverPojo.setCustom4("");
        if (matCarryoverPojo.getCustom5() == null) matCarryoverPojo.setCustom5("");
        if (matCarryoverPojo.getCustom6() == null) matCarryoverPojo.setCustom6("");
        if (matCarryoverPojo.getCustom7() == null) matCarryoverPojo.setCustom7("");
        if (matCarryoverPojo.getCustom8() == null) matCarryoverPojo.setCustom8("");
        if (matCarryoverPojo.getCustom9() == null) matCarryoverPojo.setCustom9("");
        if (matCarryoverPojo.getCustom10() == null) matCarryoverPojo.setCustom10("");
        if (matCarryoverPojo.getTenantid() == null) matCarryoverPojo.setTenantid("");
        if (matCarryoverPojo.getTenantname() == null) matCarryoverPojo.setTenantname("");
        if (matCarryoverPojo.getRevision() == null) matCarryoverPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        MatCarryoverEntity matCarryoverEntity = new MatCarryoverEntity();
        BeanUtils.copyProperties(matCarryoverPojo, matCarryoverEntity);
        //设置id和新建日期
        matCarryoverEntity.setId(id);
        matCarryoverEntity.setRevision(1);  //乐观锁
        //插入主表
        this.matCarryoverMapper.insert(matCarryoverEntity);
        //Item子表处理
        List<MatCarryoveritemPojo> lst = matCarryoverPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                MatCarryoveritemPojo itemPojo = this.matCarryoveritemService.clearNull(lst.get(i));
                MatCarryoveritemEntity matCarryoveritemEntity = new MatCarryoveritemEntity();
                BeanUtils.copyProperties(itemPojo, matCarryoveritemEntity);
                //设置id和Pid
                matCarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matCarryoveritemEntity.setPid(id);
                matCarryoveritemEntity.setTenantid(matCarryoverPojo.getTenantid());
                matCarryoveritemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matCarryoveritemMapper.insert(matCarryoveritemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(matCarryoverEntity.getId(), matCarryoverEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matCarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatCarryoverPojo update(MatCarryoverPojo matCarryoverPojo) {
        //主表更改
        MatCarryoverEntity matCarryoverEntity = new MatCarryoverEntity();
        matCarryoverPojo.setItemcount(matCarryoverPojo.getItem().size());
        BeanUtils.copyProperties(matCarryoverPojo, matCarryoverEntity);
        this.matCarryoverMapper.update(matCarryoverEntity);
        if (matCarryoverPojo.getItem() != null) {
            //Item子表处理
            List<MatCarryoveritemPojo> lst = matCarryoverPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = matCarryoverMapper.getDelItemIds(matCarryoverPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.matCarryoveritemMapper.delete(lstDelIds.get(i), matCarryoverEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    MatCarryoveritemEntity matCarryoveritemEntity = new MatCarryoveritemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        MatCarryoveritemPojo itemPojo = this.matCarryoveritemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, matCarryoveritemEntity);
                        //设置id和Pid
                        matCarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        matCarryoveritemEntity.setPid(matCarryoverEntity.getId());  // 主表 id
                        matCarryoveritemEntity.setTenantid(matCarryoverPojo.getTenantid());   // 租户id
                        matCarryoveritemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.matCarryoveritemMapper.insert(matCarryoveritemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), matCarryoveritemEntity);
                        matCarryoveritemEntity.setTenantid(matCarryoverPojo.getTenantid());
                        this.matCarryoveritemMapper.update(matCarryoveritemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(matCarryoverEntity.getId(), matCarryoverEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        MatCarryoverPojo matCarryoverPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<MatCarryoveritemPojo> lst = matCarryoverPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.matCarryoveritemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.matCarryoverMapper.delete(key, tid);
    }

    /**
     * 新增数据
     *
     * @param matCarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatCarryoverPojo createCarry(MatCarryoverPojo matCarryoverPojo) {

        //查看仓库信息
        MatStoragePojo StoPojo = this.matStorageMapper.getEntity(matCarryoverPojo.getStoreid(), matCarryoverPojo.getTenantid());
        if (StoPojo == null) {
            throw new RuntimeException("未找到对应仓库信息");
        }
        // 查询当前客户之前的销售账单
        QueryParam queryParam = new QueryParam();
        queryParam.setFilterstr(" and Mat_Carryover.Storeid='" + StoPojo.getId() + "'");
        queryParam.setOrderBy("Mat_Carryover.EndDate");
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(1);
        queryParam.setTenantid(matCarryoverPojo.getTenantid());
        List<MatCarryoverPojo> lstCarry = this.matCarryoverMapper.getPageTh(queryParam);
        if (lstCarry.size() == 0) {
            throw new RuntimeException("请先初始化仓库账单");
        }

        // 查询上个结转表内容
        List<MatCarryoveritemPojo> lstOpen = this.matCarryoveritemMapper.getList(lstCarry.get(0).getId(), matCarryoverPojo.getTenantid());

        // =============本期出入库汇总 =====================
        // 初始化时间
        Date dtStart = lstCarry.get(0).getEnddate();
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(matCarryoverPojo.getEnddate()) + " 23:59:59");
        matCarryoverPojo.setStartdate(lstCarry.get(0).getEnddate());

        queryParam = new QueryParam();
        queryParam.setFilterstr(" and Mat_Access.Storeid='" + StoPojo.getId() + "'");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setTenantid(matCarryoverPojo.getTenantid());
        List<MatCarryoveritemPojo> lstAcce = this.matCarryoverMapper.getGoodsAcceList(queryParam);

        //  ==========开始拼接==============
        int rowNum = 1;
        List<MatCarryoveritemPojo> lstNew = new ArrayList<>();
        for (MatCarryoveritemPojo openPojo : lstOpen) {
            MatCarryoveritemPojo newPojo = new MatCarryoveritemPojo();
            newPojo.setGoodsid(openPojo.getGoodsid());
            newPojo.setOpenqty(openPojo.getCloseqty());
            newPojo.setOpenamount(openPojo.getCloseamount());
            if (lstAcce.size() > 0) {
                for (MatCarryoveritemPojo accePojo : lstAcce) {
                    if (openPojo.getGoodsid().equals(accePojo.getGoodsid())) {
                        newPojo.setInqty(accePojo.getInqty());
                        newPojo.setInamount(accePojo.getInamount());
                        newPojo.setOutqty(accePojo.getOutqty());
                        newPojo.setOutamount(accePojo.getOutamount());
                        lstAcce.remove(accePojo);
                        break;
                    }
                }
            } else {
                newPojo.setInqty(0D);
                newPojo.setInamount(0D);
                newPojo.setOutqty(0D);
                newPojo.setOutamount(0D);
            }
            // 三项非空，添加到列表
            if (newPojo.getOpenqty() != 0D || newPojo.getInqty() != 0D || newPojo.getOutqty() != 0D) {
                newPojo.setCloseqty(newPojo.getOpenqty() + newPojo.getInqty() - newPojo.getOutqty());
                newPojo.setCloseamount(newPojo.getOpenamount() + newPojo.getInamount() - newPojo.getOutamount());
                newPojo.setRownum(rowNum);
                lstNew.add(newPojo);
                rowNum++;
            }
        }

        // =========本期新增================
        if (lstAcce.size() > 0) {
            for (MatCarryoveritemPojo accePojo : lstAcce) {
                MatCarryoveritemPojo newPojo = new MatCarryoveritemPojo();
                newPojo.setGoodsid(accePojo.getGoodsid());
                newPojo.setOpenqty(0D);
                newPojo.setOpenamount(0D);
                newPojo.setInqty(accePojo.getInqty());
                newPojo.setInamount(accePojo.getInamount());
                newPojo.setOutqty(accePojo.getOutqty());
                newPojo.setOutamount(accePojo.getOutamount());
                /// 添加到列表
                newPojo.setCloseqty(newPojo.getOpenqty() + newPojo.getInqty() - newPojo.getOutqty());
                newPojo.setCloseamount(newPojo.getOpenamount() + newPojo.getInamount() - newPojo.getOutamount());
                newPojo.setRownum(rowNum);
                lstNew.add(newPojo);
                rowNum++;
            }
        }
        //  =======插入数据========
        matCarryoverPojo.setItem(lstNew);
        matCarryoverPojo.setItemcount(lstNew.size());
        matCarryoverPojo = insert(matCarryoverPojo);

        //返回Bill实例
        return this.getEntity(matCarryoverPojo.getId(), matCarryoverPojo.getTenantid());

    }


    // 批量生成账单
    @Override
    @Transactional
    public int batchCreate(MatCarryoverPojo matCarryoverPojo) {
        try {
            int num = 0;
            String tid = matCarryoverPojo.getTenantid();
            List<MatStoragePojo> lstStoIds = this.matCarryoverMapper.getStoreList(tid);
            Double recopenAmt = 0D;
            Double recinAmt = 0D;
            Double recoutAmt = 0D;
            Double reccloseAmt = 0D;
            for (MatStoragePojo stoPojo : lstStoIds) {
                MatCarryoverPojo newPojo = new MatCarryoverPojo();
                BeanUtils.copyProperties(matCarryoverPojo, newPojo);
                newPojo.setStoreid(stoPojo.getId());
                newPojo.setStorecode(stoPojo.getStorecode());
                newPojo.setStorename(stoPojo.getStorename());
                newPojo.setItem(pullItemList(newPojo));
                Double openAmount = 0D;
                Double inAmount = 0D;
                Double outAmount = 0D;
                Double closeAmount = 0D;
                for (MatCarryoveritemPojo itemPojo : newPojo.getItem()) {
                    openAmount += itemPojo.getOpenamount() != null ? itemPojo.getOpenamount() : 0D;
                    inAmount += itemPojo.getInamount() != null ? itemPojo.getInamount() : 0D;
                    outAmount += itemPojo.getOutamount() != null ? itemPojo.getOutamount() : 0D;
                    closeAmount += itemPojo.getCloseamount() != null ? itemPojo.getCloseamount() : 0D;
                }
                newPojo.setBillopenamount(openAmount);
                newPojo.setBillinamount(inAmount);
                newPojo.setBilloutamount(outAmount);
                newPojo.setBillcloseamount(closeAmount);
                insert(newPojo);
                recopenAmt += openAmount;
                recinAmt += inAmount;
                recoutAmt += outAmount;
                reccloseAmt += closeAmount;
                num++;
            }
            MatCarryoverrecPojo matCarryoverrecPojo = new MatCarryoverrecPojo();
            BeanUtils.copyProperties(matCarryoverPojo, matCarryoverrecPojo);
            matCarryoverrecPojo.setBillopenamount(recopenAmt);
            matCarryoverrecPojo.setBillinamount(recinAmt);
            matCarryoverrecPojo.setBilloutamount(recoutAmt);
            matCarryoverrecPojo.setBillcloseamount(reccloseAmt);
            this.matCarryoverrecService.insert(matCarryoverrecPojo);
            return num;
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }

    }

    /**
     * 新增数据
     *
     * @param matCarryoverPojo 实例对象
     * @return 实例对象
     */
    @Override
    public List<MatCarryoveritemPojo> pullItemList(MatCarryoverPojo matCarryoverPojo) {
        // 查询当前客户之前的销售账单
        List<MatCarryoveritemPojo> lstOpen = new ArrayList<>();
        MatCarryoverPojo matCarryoverMaxPojo = this.matCarryoverMapper.getMaxEntityByStore(matCarryoverPojo.getStoreid(), matCarryoverPojo.getTenantid());

        // 初始化时间
        Date dtStart = DateUtils.parseDate("2022-01-01");
        if (matCarryoverMaxPojo != null) {
            dtStart = matCarryoverMaxPojo.getEnddate();
            // 查询上个结转表内容
            lstOpen = this.matCarryoveritemMapper.getList(matCarryoverMaxPojo.getId(), matCarryoverPojo.getTenantid());
        }
        // =============本期出入库汇总 =====================
        // 初始化时间
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(matCarryoverPojo.getEnddate()) + " 23:59:59");
        matCarryoverPojo.setStartdate(dtStart);

        QueryParam queryParam = new QueryParam();
        queryParam.setFilterstr(" and Mat_Access.Storeid='" + matCarryoverPojo.getStoreid() + "'");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setTenantid(matCarryoverPojo.getTenantid());
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(3000);
        List<MatCarryoveritemPojo> lstAcce = this.matCarryoverMapper.getGoodsAcceList(queryParam);

        //  ==========开始拼接==============
        int rowNum = 1;
        List<MatCarryoveritemPojo> lstitem = new ArrayList<>();
        for (MatCarryoveritemPojo openPojo : lstOpen) {
            MatCarryoveritemPojo newPojo = new MatCarryoveritemPojo();
            newPojo.setGoodsid(openPojo.getGoodsid());
            newPojo.setGoodsuid(openPojo.getGoodsuid());
            newPojo.setGoodsname(openPojo.getGoodsname());
            newPojo.setGoodsspec(openPojo.getGoodsspec());
            newPojo.setGoodsunit(openPojo.getGoodsunit());
            newPojo.setOpenqty(openPojo.getCloseqty() != null ? openPojo.getCloseqty() : 0D);
            newPojo.setOpenamount(openPojo.getCloseamount() != null ? openPojo.getCloseamount() : 0D);
            newPojo.setInqty(0D);
            newPojo.setInamount(0D);
            newPojo.setOutqty(0D);
            newPojo.setOutamount(0D);
            if (lstAcce.size() > 0) {
                for (MatCarryoveritemPojo accePojo : lstAcce) {
                    if (openPojo.getGoodsid().equals(accePojo.getGoodsid())) {
                        newPojo.setInqty(accePojo.getInqty() != null ? accePojo.getInqty() : 0D);
                        newPojo.setInamount(accePojo.getInamount() != null ? accePojo.getInamount() : 0D);
                        newPojo.setOutqty(accePojo.getOutqty() != null ? accePojo.getOutqty() : 0D);
                        newPojo.setOutamount(accePojo.getOutamount() != null ? accePojo.getOutamount() : 0D);
                        lstAcce.remove(accePojo);
                        break;
                    }
                }
            }
            // 三项非空，添加到列表
            if (newPojo.getOpenqty() != 0D || newPojo.getInqty() != 0D || newPojo.getOutqty() != 0D) {
                newPojo.setCloseqty(newPojo.getOpenqty() + newPojo.getInqty() - newPojo.getOutqty());
                newPojo.setCloseamount(newPojo.getOpenamount() + newPojo.getInamount() - newPojo.getOutamount());
                newPojo.setRownum(rowNum);
                lstitem.add(newPojo);
                rowNum++;
            }
        }

        // =========本期新增================
        if (lstAcce.size() > 0) {
            for (MatCarryoveritemPojo accePojo : lstAcce) {
                MatCarryoveritemPojo newPojo = new MatCarryoveritemPojo();
                newPojo.setGoodsid(accePojo.getGoodsid());
                newPojo.setGoodsuid(accePojo.getGoodsuid());
                newPojo.setGoodsname(accePojo.getGoodsname());
                newPojo.setGoodsspec(accePojo.getGoodsspec());
                newPojo.setGoodsunit(accePojo.getGoodsunit());
                newPojo.setOpenqty(0D);
                newPojo.setOpenamount(0D);
                newPojo.setInqty(accePojo.getInqty() != null ? accePojo.getInqty() : 0D);
                newPojo.setInamount(accePojo.getInamount() != null ? accePojo.getInamount() : 0D);
                newPojo.setOutqty(accePojo.getOutqty() != null ? accePojo.getOutqty() : 0D);
                newPojo.setOutamount(accePojo.getOutamount() != null ? accePojo.getOutamount() : 0D);
                /// 添加到列表
                newPojo.setCloseqty(newPojo.getOpenqty() + newPojo.getInqty() - newPojo.getOutqty());
                newPojo.setCloseamount(newPojo.getOpenamount() + newPojo.getInamount() - newPojo.getOutamount());
                newPojo.setRownum(rowNum);
                lstitem.add(newPojo);
                rowNum++;
            }
        }
        //返回Bill实例
        return lstitem;

    }

    /**
     * 通过GroupID查询最新单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatCarryoverPojo getMaxBillEntityByStore(String key, String tid) {
        MatCarryoverPojo busAccountPojo = this.matCarryoverMapper.getMaxEntityByStore(key, tid);
        //读取子表
        busAccountPojo.setItem(this.matCarryoveritemMapper.getList(busAccountPojo.getId(), busAccountPojo.getTenantid()));
        return busAccountPojo;
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */

    @Override
    public List<MatCarryoveritemPojo> getMultItemList(QueryParam queryParam) {
        return this.matCarryoverMapper.getMultItemList(queryParam);
    }


    // 获取供应商实时应付款报表
    @Override
    public PageInfo<MatCarryoverPojo> getNowPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatCarryoverPojo> lst = this.matCarryoverMapper.getNowPageList(queryParam);
            PageInfo<MatCarryoverPojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Async
    @Override
    @Transactional
    public void batchCreateStart(MatCarryoverPojo matCarryoverPojo, String uuid) {
        try {
            //设置当前计算任务进度
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100");
            missionMsg.put("msg", "任务开始处理");
            missionMsg.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.saRedisService.setCacheObject(MyConstant.STOREBATCHCREATE_CODE + uuid, missionMsg, 600);
            int num = 0;
            String tid = matCarryoverPojo.getTenantid();
            List<MatStoragePojo> lstStoIds = this.matCarryoverMapper.getStoreList(tid);
            Double recopenAmt = 0D;
            Double recinAmt = 0D;
            Double recoutAmt = 0D;
            Double reccloseAmt = 0D;
            for (MatStoragePojo stoPojo : lstStoIds) {
                MatCarryoverPojo newPojo = new MatCarryoverPojo();
                BeanUtils.copyProperties(matCarryoverPojo, newPojo);
                newPojo.setStoreid(stoPojo.getId());
                newPojo.setStorecode(stoPojo.getStorecode());
                newPojo.setStorename(stoPojo.getStorename());
                newPojo.setItem(pullItemList(newPojo));
                Double openAmount = 0D;
                Double inAmount = 0D;
                Double outAmount = 0D;
                Double closeAmount = 0D;
                for (MatCarryoveritemPojo itemPojo : newPojo.getItem()) {
                    openAmount += itemPojo.getOpenamount() != null ? itemPojo.getOpenamount() : 0D;
                    inAmount += itemPojo.getInamount() != null ? itemPojo.getInamount() : 0D;
                    outAmount += itemPojo.getOutamount() != null ? itemPojo.getOutamount() : 0D;
                    closeAmount += itemPojo.getCloseamount() != null ? itemPojo.getCloseamount() : 0D;
                }
                newPojo.setBillopenamount(openAmount);
                newPojo.setBillinamount(inAmount);
                newPojo.setBilloutamount(outAmount);
                newPojo.setBillcloseamount(closeAmount);
                insert(newPojo);
                recopenAmt += openAmount;
                recinAmt += inAmount;
                recoutAmt += outAmount;
                reccloseAmt += closeAmount;
                num++;
            }
            MatCarryoverrecPojo matCarryoverrecPojo = new MatCarryoverrecPojo();
            BeanUtils.copyProperties(matCarryoverPojo, matCarryoverrecPojo);
            matCarryoverrecPojo.setBillopenamount(recopenAmt);
            matCarryoverrecPojo.setBillinamount(recinAmt);
            matCarryoverrecPojo.setBilloutamount(recoutAmt);
            matCarryoverrecPojo.setBillcloseamount(reccloseAmt);
            this.matCarryoverrecService.insert(matCarryoverrecPojo);
            missionMsg.put("code", "200");//任务完成代码
            missionMsg.put("msg", "任务处理完成");
            missionMsg.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.saRedisService.setCacheObject(MyConstant.STOREBATCHCREATE_CODE + uuid, missionMsg, 600);
            //return num;
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }
    }


}
