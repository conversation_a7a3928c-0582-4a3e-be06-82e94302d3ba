package inks.service.sa.som.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmDepotransferitemPojo;
import inks.service.sa.som.domain.FmDepotransferitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 预收核转Item(FmDepotransferitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-14 17:21:36
 */
public interface FmDepotransferitemService {


    FmDepotransferitemPojo getEntity(String key);

    PageInfo<FmDepotransferitemPojo> getPageList(QueryParam queryParam);

    List<FmDepotransferitemPojo> getList(String Pid);  

    FmDepotransferitemPojo insert(FmDepotransferitemPojo fmDepotransferitemPojo);

    FmDepotransferitemPojo update(FmDepotransferitemPojo fmDepotransferitempojo);

    int delete(String key);

    FmDepotransferitemPojo clearNull(FmDepotransferitemPojo fmDepotransferitempojo);
}
