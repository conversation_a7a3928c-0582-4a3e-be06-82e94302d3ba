package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmCostPojo;
import inks.service.sa.som.domain.pojo.FmCostitemdetailPojo;

/**
 * 费用开支(FmCost)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-29 15:25:08
 */
public interface FmCostService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCostPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmCostitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCostPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmCostPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmCostPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param fmCostPojo 实例对象
     * @return 实例对象
     */
    FmCostPojo insert(FmCostPojo fmCostPojo);

    /**
     * 修改数据
     *
     * @param fmCostpojo 实例对象
     * @return 实例对象
     */
    FmCostPojo update(FmCostPojo fmCostpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

}
