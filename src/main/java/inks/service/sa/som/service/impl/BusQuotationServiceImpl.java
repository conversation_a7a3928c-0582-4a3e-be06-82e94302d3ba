package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BusQuotationEntity;
import inks.service.sa.som.domain.BusQuotationitemEntity;
import inks.service.sa.som.domain.pojo.BusQuotationPojo;
import inks.service.sa.som.domain.pojo.BusQuotationitemPojo;
import inks.service.sa.som.domain.pojo.BusQuotationitemdetailPojo;
import inks.service.sa.som.mapper.BusQuotationMapper;
import inks.service.sa.som.mapper.BusQuotationitemMapper;
import inks.service.sa.som.service.BusQuotationService;
import inks.service.sa.som.service.BusQuotationitemService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 报价单(BusQuotation)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-06 08:21:09
 */
@Service("busQuotationService")
public class BusQuotationServiceImpl implements BusQuotationService {
    @Resource
    private BusQuotationMapper busQuotationMapper;

    @Resource
    private BusQuotationitemMapper busQuotationitemMapper;

    
    @Resource
    private BusQuotationitemService busQuotationitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusQuotationPojo getEntity(String key, String tid) {
        return this.busQuotationMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusQuotationitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusQuotationitemdetailPojo> lst = busQuotationMapper.getPageList(queryParam);
            PageInfo<BusQuotationitemdetailPojo> pageInfo = new PageInfo<BusQuotationitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusQuotationPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusQuotationPojo busQuotationPojo = this.busQuotationMapper.getEntity(key, tid);
            //读取子表
            busQuotationPojo.setItem(busQuotationitemMapper.getList(busQuotationPojo.getId(), busQuotationPojo.getTenantid()));
            return busQuotationPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusQuotationPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusQuotationPojo> lst = busQuotationMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busQuotationitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusQuotationPojo> pageInfo = new PageInfo<BusQuotationPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusQuotationPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusQuotationPojo> lst = busQuotationMapper.getPageTh(queryParam);
            PageInfo<BusQuotationPojo> pageInfo = new PageInfo<BusQuotationPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busQuotationPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusQuotationPojo insert(BusQuotationPojo busQuotationPojo) {
        String tid = busQuotationPojo.getTenantid();
//初始化NULL字段
        if (busQuotationPojo.getRefno() == null) busQuotationPojo.setRefno("");
        if (busQuotationPojo.getBilltype() == null) busQuotationPojo.setBilltype("");
        if (busQuotationPojo.getBilltitle() == null) busQuotationPojo.setBilltitle("");
        if (busQuotationPojo.getBilldate() == null) busQuotationPojo.setBilldate(new Date());
        if (busQuotationPojo.getProjectid() == null) busQuotationPojo.setProjectid("");
        if (busQuotationPojo.getProjname() == null) busQuotationPojo.setProjname("");
        if (busQuotationPojo.getProjcode() == null) busQuotationPojo.setProjcode("");
        if (busQuotationPojo.getProbability() == null) busQuotationPojo.setProbability("");
        if (busQuotationPojo.getGroupid() == null) busQuotationPojo.setGroupid("");
        if (busQuotationPojo.getTradercode() == null) busQuotationPojo.setTradercode("");
        if (busQuotationPojo.getTradername() == null) busQuotationPojo.setTradername("");
        if (busQuotationPojo.getCustaddress() == null) busQuotationPojo.setCustaddress("");
        if (busQuotationPojo.getLinkman() == null) busQuotationPojo.setLinkman("");
        if (busQuotationPojo.getTel() == null) busQuotationPojo.setTel("");
        if (busQuotationPojo.getFax() == null) busQuotationPojo.setFax("");
        if (busQuotationPojo.getPeriods() == null) busQuotationPojo.setPeriods("");
        if (busQuotationPojo.getValiditydate() == null) busQuotationPojo.setValiditydate("");
        if (busQuotationPojo.getCurrency() == null) busQuotationPojo.setCurrency("");
        if (busQuotationPojo.getDelivery() == null) busQuotationPojo.setDelivery("");
        if (busQuotationPojo.getPayment() == null) busQuotationPojo.setPayment("");
        if (busQuotationPojo.getSeller() == null) busQuotationPojo.setSeller("");
        if (busQuotationPojo.getSummary() == null) busQuotationPojo.setSummary("");
        if (busQuotationPojo.getBillclause() == null) busQuotationPojo.setBillclause("");
        if (busQuotationPojo.getBilltaxamount() == null) busQuotationPojo.setBilltaxamount(0D);
        if (busQuotationPojo.getBillamount() == null) busQuotationPojo.setBillamount(0D);
        if (busQuotationPojo.getBilltaxtotal() == null) busQuotationPojo.setBilltaxtotal(0D);
        if (busQuotationPojo.getCreateby() == null) busQuotationPojo.setCreateby("");
        if (busQuotationPojo.getCreatebyid() == null) busQuotationPojo.setCreatebyid("");
        if (busQuotationPojo.getCreatedate() == null) busQuotationPojo.setCreatedate(new Date());
        if (busQuotationPojo.getLister() == null) busQuotationPojo.setLister("");
        if (busQuotationPojo.getListerid() == null) busQuotationPojo.setListerid("");
        if (busQuotationPojo.getModifydate() == null) busQuotationPojo.setModifydate(new Date());
        if (busQuotationPojo.getSubmitterid() == null) busQuotationPojo.setSubmitterid("");
        if (busQuotationPojo.getSubmitter() == null) busQuotationPojo.setSubmitter("");
        if (busQuotationPojo.getSubmitdate() == null) busQuotationPojo.setSubmitdate(new Date());
        if (busQuotationPojo.getAssessor() == null) busQuotationPojo.setAssessor("");
        if (busQuotationPojo.getAssessorid() == null) busQuotationPojo.setAssessorid("");
        if (busQuotationPojo.getAssessdate() == null) busQuotationPojo.setAssessdate(new Date());
        if (busQuotationPojo.getItemcount() == null) busQuotationPojo.setItemcount(busQuotationPojo.getItem().size());
        if (busQuotationPojo.getFinishcount() == null) busQuotationPojo.setFinishcount(0);
        if (busQuotationPojo.getDisannulcount() == null) busQuotationPojo.setDisannulcount(0);
        if (busQuotationPojo.getPrintcount() == null) busQuotationPojo.setPrintcount(0);
        if (busQuotationPojo.getCustom1() == null) busQuotationPojo.setCustom1("");
        if (busQuotationPojo.getCustom2() == null) busQuotationPojo.setCustom2("");
        if (busQuotationPojo.getCustom3() == null) busQuotationPojo.setCustom3("");
        if (busQuotationPojo.getCustom4() == null) busQuotationPojo.setCustom4("");
        if (busQuotationPojo.getCustom5() == null) busQuotationPojo.setCustom5("");
        if (busQuotationPojo.getCustom6() == null) busQuotationPojo.setCustom6("");
        if (busQuotationPojo.getCustom7() == null) busQuotationPojo.setCustom7("");
        if (busQuotationPojo.getCustom8() == null) busQuotationPojo.setCustom8("");
        if (busQuotationPojo.getCustom9() == null) busQuotationPojo.setCustom9("");
        if (busQuotationPojo.getCustom10() == null) busQuotationPojo.setCustom10("");
        if (busQuotationPojo.getDeptid() == null) busQuotationPojo.setDeptid("");
        if (tid == null) busQuotationPojo.setTenantid("");
        if (busQuotationPojo.getTenantname() == null) busQuotationPojo.setTenantname("");
        if (busQuotationPojo.getRevision() == null) busQuotationPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusQuotationEntity busQuotationEntity = new BusQuotationEntity();
        BeanUtils.copyProperties(busQuotationPojo, busQuotationEntity);
        //设置id和新建日期
        busQuotationEntity.setId(id);
        busQuotationEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busQuotationMapper.insert(busQuotationEntity);
        //Item子表处理
        List<BusQuotationitemPojo> lst = busQuotationPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (BusQuotationitemPojo busQuotationitemPojo : lst) {
                //初始化item的NULL
                BusQuotationitemPojo itemPojo = this.busQuotationitemService.clearNull(busQuotationitemPojo);
                BusQuotationitemEntity busQuotationitemEntity = new BusQuotationitemEntity();
                BeanUtils.copyProperties(itemPojo, busQuotationitemEntity);
                //设置id和Pid
                busQuotationitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busQuotationitemEntity.setPid(id);
                busQuotationitemEntity.setTenantid(tid);
                busQuotationitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busQuotationitemMapper.insert(busQuotationitemEntity);

                // 同步意向订单
                String intendeditemid = busQuotationitemEntity.getIntendeditemid();
                if (StringUtils.isNotBlank(intendeditemid)) {
                    //Bus_IntendedIte.FinishMark  -- 1转报价2转核价3转订单
                    this.busQuotationMapper.updateIntendedItemFinishMark(intendeditemid, 1, tid);
                    this.busQuotationMapper.updateIntendedFinishCount(intendeditemid, tid);
                }

            }
        }
        //返回Bill实例
        return this.getBillEntity(busQuotationEntity.getId(), busQuotationEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busQuotationPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusQuotationPojo update(BusQuotationPojo busQuotationPojo) {
        String tid = busQuotationPojo.getTenantid();
        //主表更改
        BusQuotationEntity busQuotationEntity = new BusQuotationEntity();
        busQuotationPojo.setItemcount(busQuotationPojo.getItem().size());
        BeanUtils.copyProperties(busQuotationPojo, busQuotationEntity);
        this.busQuotationMapper.update(busQuotationEntity);
        if (busQuotationPojo.getItem() != null) {
            //Item子表处理
            List<BusQuotationitemPojo> lst = busQuotationPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = busQuotationMapper.getDelItemIds(busQuotationPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    BusQuotationitemPojo busQuotationitemPojo = this.busQuotationitemMapper.getEntity(lstDelId, tid);
                    this.busQuotationitemMapper.delete(lstDelId, tid);
                    // 同步意向订单
                    String intendeditemid = busQuotationitemPojo.getIntendeditemid();
                    if (StringUtils.isNotBlank(intendeditemid)) {
                        //Bus_IntendedIte.FinishMark  -- 1转报价2转核价3转订单
                        this.busQuotationMapper.updateIntendedItemFinishMark(intendeditemid, 1, tid);
                        this.busQuotationMapper.updateIntendedFinishCount(intendeditemid, tid);
                    }
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    BusQuotationitemEntity busQuotationitemEntity = new BusQuotationitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        BusQuotationitemPojo itemPojo = this.busQuotationitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, busQuotationitemEntity);
                        //设置id和Pid
                        busQuotationitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busQuotationitemEntity.setPid(busQuotationEntity.getId());  // 主表 id
                        busQuotationitemEntity.setTenantid(tid);   // 租户id
                        busQuotationitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busQuotationitemMapper.insert(busQuotationitemEntity);
                        // 同步意向订单
                        String intendeditemid = busQuotationitemEntity.getIntendeditemid();
                        if (StringUtils.isNotBlank(intendeditemid)) {
                            //Bus_IntendedIte.FinishMark  -- 1转报价2转核价3转订单
                            this.busQuotationMapper.updateIntendedItemFinishMark(intendeditemid, 1, tid);
                            this.busQuotationMapper.updateIntendedFinishCount(intendeditemid, tid);
                        }
                    } else {
                        BeanUtils.copyProperties(lst.get(i), busQuotationitemEntity);
                        busQuotationitemEntity.setTenantid(tid);
                        this.busQuotationitemMapper.update(busQuotationitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(busQuotationEntity.getId(), busQuotationEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BusQuotationPojo busQuotationPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BusQuotationitemPojo> lst = busQuotationPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (BusQuotationitemPojo busQuotationitemPojo : lst) {
                // 检查引用单 :销售单
                List<String> lstcite = this.busQuotationitemMapper.getItemCiteBillName(busQuotationitemPojo.getId(), tid);
                if (CollectionUtils.isNotEmpty(lstcite)) {
                    throw new BaseBusinessException("禁止删除,被以下单据引用:" + lstcite);
                }
                this.busQuotationitemMapper.delete(busQuotationitemPojo.getId(), tid);
                // 同步意向订单
                String intendeditemid = busQuotationitemPojo.getIntendeditemid();
                if (StringUtils.isNotBlank(intendeditemid)) {
                    //Bus_IntendedIte.FinishMark  -- 1转报价2转核价3转订单
                    this.busQuotationMapper.updateIntendedItemFinishMark(intendeditemid, 1, tid);
                    this.busQuotationMapper.updateIntendedFinishCount(intendeditemid, tid);
                }
            }
        }
        return this.busQuotationMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param busQuotationPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusQuotationPojo approval(BusQuotationPojo busQuotationPojo) {
        //主表更改
        BusQuotationEntity busQuotationEntity = new BusQuotationEntity();
        BeanUtils.copyProperties(busQuotationPojo, busQuotationEntity);
        this.busQuotationMapper.approval(busQuotationEntity);
        //返回Bill实例
        return this.getBillEntity(busQuotationEntity.getId(), busQuotationEntity.getTenantid());
    }

}
