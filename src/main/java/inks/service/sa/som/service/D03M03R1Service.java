package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BusDelieryitemdetailPojo;
import inks.service.sa.som.domain.pojo.BuyFinishingitemdetailPojo;

import java.util.List;

public interface D03M03R1Service {
    List<ChartPojo> getSumAmtByYear(QueryParam queryParam);
    List<ChartPojo> getSumAmtByMonth(QueryParam queryParam);
    List<ChartPojo> getSumAmtByDay(QueryParam queryParam);

    List<ChartPojo> getSumAmtByYearMax(QueryParam queryParam);
    List<ChartPojo> getSumAmtByMonthMax(QueryParam queryParam);
    List<ChartPojo> getSumAmtByDayMax(QueryParam queryParam);

    PageInfo<BuyFinishingitemdetailPojo> getSumPageListByGoods(QueryParam queryParam);

    PageInfo<BuyFinishingitemdetailPojo> getSumPageListByGroup(QueryParam queryParam);
}
