package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.sa.som.domain.BuyDeductionEntity;
import inks.service.sa.som.domain.BuyDeductionitemEntity;
import inks.service.sa.som.domain.pojo.BuyAccountrecPojo;
import inks.service.sa.som.domain.pojo.BuyDeductionPojo;
import inks.service.sa.som.domain.pojo.BuyDeductionitemPojo;
import inks.service.sa.som.domain.pojo.BuyDeductionitemdetailPojo;
import inks.service.sa.som.mapper.BuyAccountrecMapper;
import inks.service.sa.som.mapper.BuyDeductionMapper;
import inks.service.sa.som.mapper.BuyDeductionitemMapper;
import inks.service.sa.som.service.BuyDeductionService;
import inks.service.sa.som.service.BuyDeductionitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 采购扣款(BuyDeduction)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 20:35:56
 */
@Service("buyDeductionService")
public class BuyDeductionServiceImpl implements BuyDeductionService {
    @Resource
    private BuyDeductionMapper buyDeductionMapper;

    @Resource
    private BuyDeductionitemMapper buyDeductionitemMapper;

    
    @Resource
    private BuyDeductionitemService buyDeductionitemService;

    @Resource
    private BuyAccountrecMapper buyAccountrecMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyDeductionPojo getEntity(String key, String tid) {
        return this.buyDeductionMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyDeductionitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyDeductionitemdetailPojo> lst = buyDeductionMapper.getPageList(queryParam);
            PageInfo<BuyDeductionitemdetailPojo> pageInfo = new PageInfo<BuyDeductionitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyDeductionPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BuyDeductionPojo buyDeductionPojo = this.buyDeductionMapper.getEntity(key, tid);
            //读取子表
            buyDeductionPojo.setItem(buyDeductionitemMapper.getList(buyDeductionPojo.getId(), buyDeductionPojo.getTenantid()));
            return buyDeductionPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyDeductionPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyDeductionPojo> lst = buyDeductionMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(buyDeductionitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BuyDeductionPojo> pageInfo = new PageInfo<BuyDeductionPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyDeductionPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyDeductionPojo> lst = buyDeductionMapper.getPageTh(queryParam);
            PageInfo<BuyDeductionPojo> pageInfo = new PageInfo<BuyDeductionPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param buyDeductionPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyDeductionPojo insert(BuyDeductionPojo buyDeductionPojo) {
//初始化NULL字段
        if (buyDeductionPojo.getRefno() == null) buyDeductionPojo.setRefno("");
        if (buyDeductionPojo.getBilltype() == null) buyDeductionPojo.setBilltype("");
        if (buyDeductionPojo.getBilltitle() == null) buyDeductionPojo.setBilltitle("");
        if (buyDeductionPojo.getBilldate() == null) buyDeductionPojo.setBilldate(new Date());
        if (buyDeductionPojo.getProjectid() == null) buyDeductionPojo.setProjectid("");
        if (buyDeductionPojo.getProjcode() == null) buyDeductionPojo.setProjcode("");
        if (buyDeductionPojo.getProjname() == null) buyDeductionPojo.setProjname("");
        if (buyDeductionPojo.getGroupid() == null) buyDeductionPojo.setGroupid("");
        if (buyDeductionPojo.getOperator() == null) buyDeductionPojo.setOperator("");
        if (buyDeductionPojo.getTaxrate() == null) buyDeductionPojo.setTaxrate(0);
        if (buyDeductionPojo.getBilltaxamount() == null) buyDeductionPojo.setBilltaxamount(0D);
        if (buyDeductionPojo.getBillamount() == null) buyDeductionPojo.setBillamount(0D);
        if (buyDeductionPojo.getBilltaxtotal() == null) buyDeductionPojo.setBilltaxtotal(0D);
        if (buyDeductionPojo.getSummary() == null) buyDeductionPojo.setSummary("");
        if (buyDeductionPojo.getCreateby() == null) buyDeductionPojo.setCreateby("");
        if (buyDeductionPojo.getCreatebyid() == null) buyDeductionPojo.setCreatebyid("");
        if (buyDeductionPojo.getCreatedate() == null) buyDeductionPojo.setCreatedate(new Date());
        if (buyDeductionPojo.getLister() == null) buyDeductionPojo.setLister("");
        if (buyDeductionPojo.getListerid() == null) buyDeductionPojo.setListerid("");
        if (buyDeductionPojo.getModifydate() == null) buyDeductionPojo.setModifydate(new Date());
        if (buyDeductionPojo.getAssessor() == null) buyDeductionPojo.setAssessor("");
        if (buyDeductionPojo.getAssessorid() == null) buyDeductionPojo.setAssessorid("");
        if (buyDeductionPojo.getAssessdate() == null) buyDeductionPojo.setAssessdate(new Date());
        if (buyDeductionPojo.getItemcount() == null) buyDeductionPojo.setItemcount(buyDeductionPojo.getItem().size());
        if (buyDeductionPojo.getDisannulcount() == null) buyDeductionPojo.setDisannulcount(0);
        if (buyDeductionPojo.getFinishcount() == null) buyDeductionPojo.setFinishcount(0);
        if (buyDeductionPojo.getPrintcount() == null) buyDeductionPojo.setPrintcount(0);
        if (buyDeductionPojo.getCustom1() == null) buyDeductionPojo.setCustom1("");
        if (buyDeductionPojo.getCustom2() == null) buyDeductionPojo.setCustom2("");
        if (buyDeductionPojo.getCustom3() == null) buyDeductionPojo.setCustom3("");
        if (buyDeductionPojo.getCustom4() == null) buyDeductionPojo.setCustom4("");
        if (buyDeductionPojo.getCustom5() == null) buyDeductionPojo.setCustom5("");
        if (buyDeductionPojo.getCustom6() == null) buyDeductionPojo.setCustom6("");
        if (buyDeductionPojo.getCustom7() == null) buyDeductionPojo.setCustom7("");
        if (buyDeductionPojo.getCustom8() == null) buyDeductionPojo.setCustom8("");
        if (buyDeductionPojo.getCustom9() == null) buyDeductionPojo.setCustom9("");
        if (buyDeductionPojo.getCustom10() == null) buyDeductionPojo.setCustom10("");
        if (buyDeductionPojo.getDeptid() == null) buyDeductionPojo.setDeptid("");
        if (buyDeductionPojo.getTenantid() == null) buyDeductionPojo.setTenantid("");
        if (buyDeductionPojo.getTenantname() == null) buyDeductionPojo.setTenantname("");
        if (buyDeductionPojo.getRevision() == null) buyDeductionPojo.setRevision(0);

        // 结账检查
        BuyAccountrecPojo buyAccountrecPojo = this.buyAccountrecMapper.getEntityByMax(buyDeductionPojo.getTenantid());
        if (buyAccountrecPojo != null && DateUtils.getTimestamp(buyDeductionPojo.getBilldate()) <= DateUtils.getTimestamp(buyAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + buyAccountrecPojo.getEnddate() + ",禁止新建结账前单据");
        }

        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BuyDeductionEntity buyDeductionEntity = new BuyDeductionEntity();
        BeanUtils.copyProperties(buyDeductionPojo, buyDeductionEntity);
        //设置id和新建日期
        buyDeductionEntity.setId(id);
        buyDeductionEntity.setRevision(1);  //乐观锁
        //插入主表
        this.buyDeductionMapper.insert(buyDeductionEntity);
        //Item子表处理
        List<BuyDeductionitemPojo> lst = buyDeductionPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                BuyDeductionitemPojo itemPojo = this.buyDeductionitemService.clearNull(lst.get(i));
                BuyDeductionitemEntity buyDeductionitemEntity = new BuyDeductionitemEntity();
                BeanUtils.copyProperties(itemPojo, buyDeductionitemEntity);
                //设置id和Pid
                buyDeductionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                buyDeductionitemEntity.setPid(id);
                buyDeductionitemEntity.setTenantid(buyDeductionPojo.getTenantid());
                buyDeductionitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.buyDeductionitemMapper.insert(buyDeductionitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(buyDeductionEntity.getId(), buyDeductionEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param buyDeductionPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyDeductionPojo update(BuyDeductionPojo buyDeductionPojo) {
        // 检查结账
        BuyAccountrecPojo buyAccountrecPojo = this.buyAccountrecMapper.getEntityByMax(buyDeductionPojo.getTenantid());
        if (buyAccountrecPojo != null && DateUtils.getTimestamp(buyDeductionPojo.getBilldate()) <= DateUtils.getTimestamp(buyAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + buyAccountrecPojo.getEnddate() + ",禁止修改结账前单据");
        }
        BuyDeductionPojo orgPojo = this.buyDeductionMapper.getEntity(buyDeductionPojo.getId(), buyDeductionPojo.getTenantid());
        if (buyAccountrecPojo != null && DateUtils.getTimestamp(orgPojo.getBilldate()) <= DateUtils.getTimestamp(buyAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + buyAccountrecPojo.getEnddate() + ",禁止修改结账前单据");
        }
        //主表更改
        BuyDeductionEntity buyDeductionEntity = new BuyDeductionEntity();
        BeanUtils.copyProperties(buyDeductionPojo, buyDeductionEntity);
        this.buyDeductionMapper.update(buyDeductionEntity);
        if (buyDeductionPojo.getItem() != null) {
            //Item子表处理
            List<BuyDeductionitemPojo> lst = buyDeductionPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = buyDeductionMapper.getDelItemIds(buyDeductionPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    BuyDeductionitemPojo delPojo = this.buyDeductionitemMapper.getEntity(lstDelIds.get(i), buyDeductionEntity.getTenantid());
                    // 加上引用检查
                    List<String> lstcite = getItemCiteBillName(delPojo.getId(),delPojo.getPid(),delPojo.getTenantid());
                    if (lstcite.size() > 0) {
                        throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                    }
                    this.buyDeductionitemMapper.delete(lstDelIds.get(i), buyDeductionEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    BuyDeductionitemEntity buyDeductionitemEntity = new BuyDeductionitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        BuyDeductionitemPojo itemPojo = this.buyDeductionitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, buyDeductionitemEntity);
                        //设置id和Pid
                        buyDeductionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        buyDeductionitemEntity.setPid(buyDeductionEntity.getId());  // 主表 id
                        buyDeductionitemEntity.setTenantid(buyDeductionPojo.getTenantid());   // 租户id
                        buyDeductionitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.buyDeductionitemMapper.insert(buyDeductionitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), buyDeductionitemEntity);
                        buyDeductionitemEntity.setTenantid(buyDeductionPojo.getTenantid());
                        this.buyDeductionitemMapper.update(buyDeductionitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(buyDeductionEntity.getId(), buyDeductionEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BuyDeductionPojo buyDeductionPojo = this.getBillEntity(key, tid);
        // 检查结账
        BuyAccountrecPojo busAccountrecPojo = this.buyAccountrecMapper.getEntityByMax(buyDeductionPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(buyDeductionPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getEnddate() + ",禁止删除结账前单据");
        }
        //Item子表处理
        List<BuyDeductionitemPojo> lst = buyDeductionPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                // 加上引用检查
                List<String> lstcite = getItemCiteBillName(lst.get(i).getId(), lst.get(i).getPid(), tid);
                if (lstcite.size() > 0) {
                    throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                }
                this.buyDeductionitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.buyDeductionMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param buyDeductionPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyDeductionPojo approval(BuyDeductionPojo buyDeductionPojo) {
        //主表更改
        BuyDeductionEntity buyDeductionEntity = new BuyDeductionEntity();
        BeanUtils.copyProperties(buyDeductionPojo, buyDeductionEntity);
        this.buyDeductionMapper.approval(buyDeductionEntity);
        //返回Bill实例
        return this.getBillEntity(buyDeductionEntity.getId(), buyDeductionEntity.getTenantid());
    }

    @Override
    // 查询Item是否被引用
    public List<String> getItemCiteBillName(String key, String pid, String tid) {
        return this.buyDeductionMapper.getItemCiteBillName(key, pid, tid);
    }

    @Override
    public void updatePrintcount(BuyDeductionPojo billPrintPojo) {
        this.buyDeductionMapper.updatePrintcount(billPrintPojo);
    }
}
