package inks.service.sa.som.service.impl;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.mapper.D03M05R1Mapper;
import inks.service.sa.som.service.D03M05R1Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class D03M05R1ServiceImpl implements D03M05R1Service {
    @Resource
    private D03M05R1Mapper d03M05R1Mapper;
    @Override
    public List<ChartPojo> getItemCountByMonth(QueryParam queryParam) {
        try {
            return d03M05R1Mapper.getItemCountByMonth(queryParam);
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<ChartPojo> sumChartMaxByGroup(QueryParam queryParam) {
        try {
            return d03M05R1Mapper.sumChartMaxByGroup(queryParam);
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
