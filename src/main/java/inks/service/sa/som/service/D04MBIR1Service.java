package inks.service.sa.som.service;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatInventoryPojo;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface D04MBIR1Service {


    /*
    库存品金额排行
     */
    List<MatInventoryPojo> getSumAmtByGoodsMax(QueryParam queryParam);


    List<ChartPojo> getSumAmtQtyByStore(QueryParam queryParam);

    List<Map<String,Object>> getSumQtyByMonth(QueryParam queryParam);

    List<Map<String,Object>> getSumOutQtyOneMonth(QueryParam queryParam);

    List<Map<String,Object>> getSumInQtyOneMonth(QueryParam queryParam);


    List<Map<String, Object>> getWorkshopProdValueByDay(Date startDate, Date endDate,String workshopid, String tenantid);

    List<Map<String, Object>> getWorkshopProdValueByMonth(String workshopid, String tenantid);
}
