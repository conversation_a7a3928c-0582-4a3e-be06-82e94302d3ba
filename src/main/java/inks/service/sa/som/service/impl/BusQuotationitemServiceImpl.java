package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BusQuotationitemEntity;
import inks.service.sa.som.domain.pojo.BusQuotationitemPojo;
import inks.service.sa.som.mapper.BusQuotationitemMapper;
import inks.service.sa.som.service.BusQuotationitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 报价项目(BusQuotationitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-06 08:21:26
 */
@Service("busQuotationitemService")
public class BusQuotationitemServiceImpl implements BusQuotationitemService {
    @Resource
    private BusQuotationitemMapper busQuotationitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusQuotationitemPojo getEntity(String key,String tid) {
        return this.busQuotationitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusQuotationitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusQuotationitemPojo> lst = busQuotationitemMapper.getPageList(queryParam);
            PageInfo<BusQuotationitemPojo> pageInfo = new PageInfo<BusQuotationitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusQuotationitemPojo> getList(String Pid,String tid) {
        try {
            List<BusQuotationitemPojo> lst = busQuotationitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param busQuotationitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusQuotationitemPojo insert(BusQuotationitemPojo busQuotationitemPojo) {
        //初始化item的NULL
        BusQuotationitemPojo itempojo =this.clearNull(busQuotationitemPojo);
        BusQuotationitemEntity busQuotationitemEntity = new BusQuotationitemEntity();
        BeanUtils.copyProperties(itempojo,busQuotationitemEntity);

          busQuotationitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busQuotationitemEntity.setRevision(1);  //乐观锁
          this.busQuotationitemMapper.insert(busQuotationitemEntity);
        return this.getEntity(busQuotationitemEntity.getId(),busQuotationitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busQuotationitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusQuotationitemPojo update(BusQuotationitemPojo busQuotationitemPojo) {
        BusQuotationitemEntity busQuotationitemEntity = new BusQuotationitemEntity();
        BeanUtils.copyProperties(busQuotationitemPojo,busQuotationitemEntity);
        this.busQuotationitemMapper.update(busQuotationitemEntity);
        return this.getEntity(busQuotationitemEntity.getId(),busQuotationitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busQuotationitemMapper.delete(key,tid) ;
    }

     /**
     * 修改数据
     *
     * @param busQuotationitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusQuotationitemPojo clearNull(BusQuotationitemPojo busQuotationitemPojo){
         //初始化NULL字段
         if(busQuotationitemPojo.getPid()==null) busQuotationitemPojo.setPid("");
         if(busQuotationitemPojo.getGoodsid()==null) busQuotationitemPojo.setGoodsid("");
         if(busQuotationitemPojo.getItemtype()==null) busQuotationitemPojo.setItemtype("");
         if(busQuotationitemPojo.getItemname()==null) busQuotationitemPojo.setItemname("");
         if(busQuotationitemPojo.getItemspec()==null) busQuotationitemPojo.setItemspec("");
         if(busQuotationitemPojo.getItemunit()==null) busQuotationitemPojo.setItemunit("");
         if(busQuotationitemPojo.getQuantity()==null) busQuotationitemPojo.setQuantity(0D);
         if(busQuotationitemPojo.getPrice()==null) busQuotationitemPojo.setPrice(0D);
         if(busQuotationitemPojo.getAmount()==null) busQuotationitemPojo.setAmount(0D);
         if(busQuotationitemPojo.getItemtaxrate()==null) busQuotationitemPojo.setItemtaxrate(0);
         if(busQuotationitemPojo.getTaxprice()==null) busQuotationitemPojo.setTaxprice(0D);
         if(busQuotationitemPojo.getTaxamount()==null) busQuotationitemPojo.setTaxamount(0D);
         if(busQuotationitemPojo.getRemark()==null) busQuotationitemPojo.setRemark("");
         if(busQuotationitemPojo.getRownum()==null) busQuotationitemPojo.setRownum(0);
         if(busQuotationitemPojo.getAttributejson()==null) busQuotationitemPojo.setAttributejson("");
         if(busQuotationitemPojo.getDisannulmark()==null) busQuotationitemPojo.setDisannulmark(0);
         if(busQuotationitemPojo.getDisannullisterid()==null) busQuotationitemPojo.setDisannullisterid("");
         if(busQuotationitemPojo.getDisannullister()==null) busQuotationitemPojo.setDisannullister("");
         if(busQuotationitemPojo.getDisannuldate()==null) busQuotationitemPojo.setDisannuldate(new Date());
         if(busQuotationitemPojo.getClosed()==null) busQuotationitemPojo.setClosed(0);
         if(busQuotationitemPojo.getMachmark()==null) busQuotationitemPojo.setMachmark(0);
         if(busQuotationitemPojo.getVirtualitem()==null) busQuotationitemPojo.setVirtualitem(0);
         if(busQuotationitemPojo.getIntendeduid()==null) busQuotationitemPojo.setIntendeduid("");
         if(busQuotationitemPojo.getIntendeditemid()==null) busQuotationitemPojo.setIntendeditemid("");
         if(busQuotationitemPojo.getCustom1()==null) busQuotationitemPojo.setCustom1("");
         if(busQuotationitemPojo.getCustom2()==null) busQuotationitemPojo.setCustom2("");
         if(busQuotationitemPojo.getCustom3()==null) busQuotationitemPojo.setCustom3("");
         if(busQuotationitemPojo.getCustom4()==null) busQuotationitemPojo.setCustom4("");
         if(busQuotationitemPojo.getCustom5()==null) busQuotationitemPojo.setCustom5("");
         if(busQuotationitemPojo.getCustom6()==null) busQuotationitemPojo.setCustom6("");
         if(busQuotationitemPojo.getCustom7()==null) busQuotationitemPojo.setCustom7("");
         if(busQuotationitemPojo.getCustom8()==null) busQuotationitemPojo.setCustom8("");
         if(busQuotationitemPojo.getCustom9()==null) busQuotationitemPojo.setCustom9("");
         if(busQuotationitemPojo.getCustom10()==null) busQuotationitemPojo.setCustom10("");
         if(busQuotationitemPojo.getTenantid()==null) busQuotationitemPojo.setTenantid("");
         if(busQuotationitemPojo.getRevision()==null) busQuotationitemPojo.setRevision(0);
         return busQuotationitemPojo;
     }
}
