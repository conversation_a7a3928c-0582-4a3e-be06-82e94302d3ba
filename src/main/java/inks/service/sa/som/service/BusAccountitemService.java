package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BusAccountitemPojo;

import java.util.List;
/**
 * 账单明细(BusAccountitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-03 14:28:20
 */
public interface BusAccountitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusAccountitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusAccountitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busAccountitemPojo 实例对象
     * @return 实例对象
     */
    BusAccountitemPojo insert(BusAccountitemPojo busAccountitemPojo);

    /**
     * 修改数据
     *
     * @param busAccountitempojo 实例对象
     * @return 实例对象
     */
    BusAccountitemPojo update(BusAccountitemPojo busAccountitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busAccountitempojo 实例对象
     * @return 实例对象
     */
    BusAccountitemPojo clearNull(BusAccountitemPojo busAccountitempojo);
}
