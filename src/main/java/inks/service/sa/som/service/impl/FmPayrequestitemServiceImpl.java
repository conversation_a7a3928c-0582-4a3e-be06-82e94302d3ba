package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.FmPayrequestitemEntity;
import inks.service.sa.som.domain.pojo.FmPayrequestitemPojo;
import inks.service.sa.som.mapper.FmPayrequestitemMapper;
import inks.service.sa.som.service.FmPayrequestitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 付款申请单(FmPayrequestitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-07 13:31:45
 */
@Service("fmPayrequestitemService")
public class FmPayrequestitemServiceImpl implements FmPayrequestitemService {
    @Resource
    private FmPayrequestitemMapper fmPayrequestitemMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmPayrequestitemPojo getEntity(String key) {
        return this.fmPayrequestitemMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmPayrequestitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmPayrequestitemPojo> lst = fmPayrequestitemMapper.getPageList(queryParam);
            PageInfo<FmPayrequestitemPojo> pageInfo = new PageInfo<FmPayrequestitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<FmPayrequestitemPojo> getList(String Pid) { 
        try {
            List<FmPayrequestitemPojo> lst = fmPayrequestitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param fmPayrequestitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmPayrequestitemPojo insert(FmPayrequestitemPojo fmPayrequestitemPojo) {
        //初始化item的NULL
        FmPayrequestitemPojo itempojo =this.clearNull(fmPayrequestitemPojo);
        FmPayrequestitemEntity fmPayrequestitemEntity = new FmPayrequestitemEntity(); 
        BeanUtils.copyProperties(itempojo,fmPayrequestitemEntity);
         //生成雪花id
          fmPayrequestitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          fmPayrequestitemEntity.setRevision(1);  //乐观锁      
          this.fmPayrequestitemMapper.insert(fmPayrequestitemEntity);
        return this.getEntity(fmPayrequestitemEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param fmPayrequestitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmPayrequestitemPojo update(FmPayrequestitemPojo fmPayrequestitemPojo) {
        FmPayrequestitemEntity fmPayrequestitemEntity = new FmPayrequestitemEntity(); 
        BeanUtils.copyProperties(fmPayrequestitemPojo,fmPayrequestitemEntity);
        this.fmPayrequestitemMapper.update(fmPayrequestitemEntity);
        return this.getEntity(fmPayrequestitemEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.fmPayrequestitemMapper.delete(key) ;
    }
    
     /**
     * 修改数据
     *
     * @param fmPayrequestitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public FmPayrequestitemPojo clearNull(FmPayrequestitemPojo fmPayrequestitemPojo){
     //初始化NULL字段
     if(fmPayrequestitemPojo.getPid()==null) fmPayrequestitemPojo.setPid("");
     if(fmPayrequestitemPojo.getInvoid()==null) fmPayrequestitemPojo.setInvoid("");
     if(fmPayrequestitemPojo.getInvobillcode()==null) fmPayrequestitemPojo.setInvobillcode("");
     if(fmPayrequestitemPojo.getInvocode()==null) fmPayrequestitemPojo.setInvocode("");
     if(fmPayrequestitemPojo.getInvoamount()==null) fmPayrequestitemPojo.setInvoamount(0D);
     if(fmPayrequestitemPojo.getAmount()==null) fmPayrequestitemPojo.setAmount(0D);
     if(fmPayrequestitemPojo.getRownum()==null) fmPayrequestitemPojo.setRownum(0);
     if(fmPayrequestitemPojo.getRemark()==null) fmPayrequestitemPojo.setRemark("");
     if(fmPayrequestitemPojo.getCustom1()==null) fmPayrequestitemPojo.setCustom1("");
     if(fmPayrequestitemPojo.getCustom2()==null) fmPayrequestitemPojo.setCustom2("");
     if(fmPayrequestitemPojo.getCustom3()==null) fmPayrequestitemPojo.setCustom3("");
     if(fmPayrequestitemPojo.getCustom4()==null) fmPayrequestitemPojo.setCustom4("");
     if(fmPayrequestitemPojo.getCustom5()==null) fmPayrequestitemPojo.setCustom5("");
     if(fmPayrequestitemPojo.getCustom6()==null) fmPayrequestitemPojo.setCustom6("");
     if(fmPayrequestitemPojo.getCustom7()==null) fmPayrequestitemPojo.setCustom7("");
     if(fmPayrequestitemPojo.getCustom8()==null) fmPayrequestitemPojo.setCustom8("");
     if(fmPayrequestitemPojo.getCustom9()==null) fmPayrequestitemPojo.setCustom9("");
     if(fmPayrequestitemPojo.getCustom10()==null) fmPayrequestitemPojo.setCustom10("");
     if(fmPayrequestitemPojo.getTenantid()==null) fmPayrequestitemPojo.setTenantid("");
     if(fmPayrequestitemPojo.getRevision()==null) fmPayrequestitemPojo.setRevision(0);
     return fmPayrequestitemPojo;
     }
}
