package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyOrderPojo;
import inks.service.sa.som.domain.pojo.BuyOrderitemPojo;
import inks.service.sa.som.domain.pojo.BuyOrderitemdetailPojo;

import java.util.List;
import java.util.Map;

/**
 * 采购合同(BuyOrder)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-06 20:33:30
 */
public interface BuyOrderService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyOrderPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyOrderitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyOrderPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyOrderPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyOrderPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyOrderPojo 实例对象
     * @return 实例对象
     */
    BuyOrderPojo insert(BuyOrderPojo buyOrderPojo, Integer warn);

    /**
     * 修改数据
     *
     * @param buyOrderpojo 实例对象
     * @return 实例对象
     */
    BuyOrderPojo update(BuyOrderPojo buyOrderpojo, Integer warn);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param buyOrderPojo 实例对象
     * @return 实例对象
     */
    BuyOrderPojo approval(BuyOrderPojo buyOrderPojo);

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    BuyOrderPojo disannul(List<BuyOrderitemPojo> lst, Integer type, LoginUser loginUser);

    /**
     * 中止数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    BuyOrderPojo closed(List<BuyOrderitemPojo> lst, Integer type, LoginUser loginUser);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(String key, String pid, String tid);

    Map<String, Object> getWorkgroupInfo(String groupid, String tid);
}
