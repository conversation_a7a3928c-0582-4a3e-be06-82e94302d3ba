package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyAccountinvoEntity;
import inks.service.sa.som.domain.pojo.BuyAccountinvoPojo;
import inks.service.sa.som.mapper.BuyAccountinvoMapper;
import inks.service.sa.som.service.BuyAccountinvoService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 收货单to发票(BuyAccountinvo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-17 10:52:03
 */
@Service("buyAccountinvoService")
public class BuyAccountinvoServiceImpl implements BuyAccountinvoService {
    @Resource
    private BuyAccountinvoMapper buyAccountinvoMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyAccountinvoPojo getEntity(String key,String tid) {
        return this.buyAccountinvoMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyAccountinvoPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyAccountinvoPojo> lst = buyAccountinvoMapper.getPageList(queryParam);
            PageInfo<BuyAccountinvoPojo> pageInfo = new PageInfo<BuyAccountinvoPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BuyAccountinvoPojo> getList(String Pid,String tid) { 
        try {
            List<BuyAccountinvoPojo> lst = buyAccountinvoMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param buyAccountinvoPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyAccountinvoPojo insert(BuyAccountinvoPojo buyAccountinvoPojo) {
        //初始化item的NULL
        BuyAccountinvoPojo itempojo =this.clearNull(buyAccountinvoPojo);
        BuyAccountinvoEntity buyAccountinvoEntity = new BuyAccountinvoEntity(); 
        BeanUtils.copyProperties(itempojo,buyAccountinvoEntity);
          //生成雪花id
          buyAccountinvoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          buyAccountinvoEntity.setRevision(1);  //乐观锁      
          this.buyAccountinvoMapper.insert(buyAccountinvoEntity);
        return this.getEntity(buyAccountinvoEntity.getId(),buyAccountinvoEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param buyAccountinvoPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyAccountinvoPojo update(BuyAccountinvoPojo buyAccountinvoPojo) {
        BuyAccountinvoEntity buyAccountinvoEntity = new BuyAccountinvoEntity(); 
        BeanUtils.copyProperties(buyAccountinvoPojo,buyAccountinvoEntity);
        this.buyAccountinvoMapper.update(buyAccountinvoEntity);
        return this.getEntity(buyAccountinvoEntity.getId(),buyAccountinvoEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.buyAccountinvoMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param buyAccountinvoPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BuyAccountinvoPojo clearNull(BuyAccountinvoPojo buyAccountinvoPojo){
     //初始化NULL字段
     if(buyAccountinvoPojo.getPid()==null) buyAccountinvoPojo.setPid("");
     if(buyAccountinvoPojo.getDirection()==null) buyAccountinvoPojo.setDirection("");
     if(buyAccountinvoPojo.getModulecode()==null) buyAccountinvoPojo.setModulecode("");
     if(buyAccountinvoPojo.getBilltype()==null) buyAccountinvoPojo.setBilltype("");
     if(buyAccountinvoPojo.getBilldate()==null) buyAccountinvoPojo.setBilldate(new Date());
     if(buyAccountinvoPojo.getBilltitle()==null) buyAccountinvoPojo.setBilltitle("");
     if(buyAccountinvoPojo.getBilluid()==null) buyAccountinvoPojo.setBilluid("");
     if(buyAccountinvoPojo.getBillid()==null) buyAccountinvoPojo.setBillid("");
     if(buyAccountinvoPojo.getOpenamount()==null) buyAccountinvoPojo.setOpenamount(0D);
     if(buyAccountinvoPojo.getInamount()==null) buyAccountinvoPojo.setInamount(0D);
     if(buyAccountinvoPojo.getOutamount()==null) buyAccountinvoPojo.setOutamount(0D);
     if(buyAccountinvoPojo.getCloseamount()==null) buyAccountinvoPojo.setCloseamount(0D);
     if(buyAccountinvoPojo.getRownum()==null) buyAccountinvoPojo.setRownum(0);
     if(buyAccountinvoPojo.getRemark()==null) buyAccountinvoPojo.setRemark("");
     if(buyAccountinvoPojo.getCustom1()==null) buyAccountinvoPojo.setCustom1("");
     if(buyAccountinvoPojo.getCustom2()==null) buyAccountinvoPojo.setCustom2("");
     if(buyAccountinvoPojo.getCustom3()==null) buyAccountinvoPojo.setCustom3("");
     if(buyAccountinvoPojo.getCustom4()==null) buyAccountinvoPojo.setCustom4("");
     if(buyAccountinvoPojo.getCustom5()==null) buyAccountinvoPojo.setCustom5("");
     if(buyAccountinvoPojo.getCustom6()==null) buyAccountinvoPojo.setCustom6("");
     if(buyAccountinvoPojo.getCustom7()==null) buyAccountinvoPojo.setCustom7("");
     if(buyAccountinvoPojo.getCustom8()==null) buyAccountinvoPojo.setCustom8("");
     if(buyAccountinvoPojo.getCustom9()==null) buyAccountinvoPojo.setCustom9("");
     if(buyAccountinvoPojo.getCustom10()==null) buyAccountinvoPojo.setCustom10("");
     if(buyAccountinvoPojo.getTenantid()==null) buyAccountinvoPojo.setTenantid("");
     if(buyAccountinvoPojo.getRevision()==null) buyAccountinvoPojo.setRevision(0);
     return buyAccountinvoPojo;
     }
}
