package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyOrderitemEntity;
import inks.service.sa.som.domain.pojo.BuyOrderitemPojo;
import inks.service.sa.som.mapper.BuyOrderitemMapper;
import inks.service.sa.som.service.BuyOrderitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 采购单子表(BuyOrderitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 20:33:55
 */
@Service("buyOrderitemService")
public class BuyOrderitemServiceImpl implements BuyOrderitemService {
    @Resource
    private BuyOrderitemMapper buyOrderitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyOrderitemPojo getEntity(String key, String tid) {
        return this.buyOrderitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyOrderitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyOrderitemPojo> lst = buyOrderitemMapper.getPageList(queryParam);
            PageInfo<BuyOrderitemPojo> pageInfo = new PageInfo<BuyOrderitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BuyOrderitemPojo> getList(String Pid, String tid) {
        try {
            List<BuyOrderitemPojo> lst = buyOrderitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param buyOrderitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyOrderitemPojo insert(BuyOrderitemPojo buyOrderitemPojo) {
        //初始化item的NULL
        BuyOrderitemPojo itempojo = this.clearNull(buyOrderitemPojo);
        BuyOrderitemEntity buyOrderitemEntity = new BuyOrderitemEntity();
        BeanUtils.copyProperties(itempojo, buyOrderitemEntity);

        buyOrderitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        buyOrderitemEntity.setRevision(1);  //乐观锁
        this.buyOrderitemMapper.insert(buyOrderitemEntity);
        return this.getEntity(buyOrderitemEntity.getId(), buyOrderitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param buyOrderitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyOrderitemPojo update(BuyOrderitemPojo buyOrderitemPojo) {
        BuyOrderitemEntity buyOrderitemEntity = new BuyOrderitemEntity();
        BeanUtils.copyProperties(buyOrderitemPojo, buyOrderitemEntity);
        this.buyOrderitemMapper.update(buyOrderitemEntity);
        return this.getEntity(buyOrderitemEntity.getId(), buyOrderitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.buyOrderitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param buyOrderitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyOrderitemPojo clearNull(BuyOrderitemPojo buyOrderitemPojo) {
        //初始化NULL字段
        if (buyOrderitemPojo.getPid() == null) buyOrderitemPojo.setPid("");
        if (buyOrderitemPojo.getGoodsid() == null) buyOrderitemPojo.setGoodsid("");
        if (buyOrderitemPojo.getItemcode() == null) buyOrderitemPojo.setItemcode("");
        if (buyOrderitemPojo.getItemname() == null) buyOrderitemPojo.setItemname("");
        if (buyOrderitemPojo.getItemspec() == null) buyOrderitemPojo.setItemspec("");
        if (buyOrderitemPojo.getItemunit() == null) buyOrderitemPojo.setItemunit("");
        if (buyOrderitemPojo.getQuantity() == null) buyOrderitemPojo.setQuantity(0D);
        if (buyOrderitemPojo.getTaxprice() == null) buyOrderitemPojo.setTaxprice(0D);
        if (buyOrderitemPojo.getTaxamount() == null) buyOrderitemPojo.setTaxamount(0D);
        if (buyOrderitemPojo.getItemtaxrate() == null) buyOrderitemPojo.setItemtaxrate(0);
        if (buyOrderitemPojo.getTaxtotal() == null) buyOrderitemPojo.setTaxtotal(0D);
        if (buyOrderitemPojo.getPrice() == null) buyOrderitemPojo.setPrice(0D);
        if (buyOrderitemPojo.getAmount() == null) buyOrderitemPojo.setAmount(0D);
        if (buyOrderitemPojo.getPlandate() == null) buyOrderitemPojo.setPlandate(new Date());
        if (buyOrderitemPojo.getMaxqty() == null) buyOrderitemPojo.setMaxqty(0D);
        if (buyOrderitemPojo.getFinishqty() == null) buyOrderitemPojo.setFinishqty(0D);
        if (buyOrderitemPojo.getRemark() == null) buyOrderitemPojo.setRemark("");
        if (buyOrderitemPojo.getCiteuid() == null) buyOrderitemPojo.setCiteuid("");
        if (buyOrderitemPojo.getCiteitemid() == null) buyOrderitemPojo.setCiteitemid("");
        if (buyOrderitemPojo.getStatecode() == null) buyOrderitemPojo.setStatecode("");
        if (buyOrderitemPojo.getStatedate() == null) buyOrderitemPojo.setStatedate(new Date());
        if (buyOrderitemPojo.getClosed() == null) buyOrderitemPojo.setClosed(0);
        if (buyOrderitemPojo.getRownum() == null) buyOrderitemPojo.setRownum(0);
        if (buyOrderitemPojo.getOrggoodsid() == null) buyOrderitemPojo.setOrggoodsid("");
        if (buyOrderitemPojo.getSubrate() == null) buyOrderitemPojo.setSubrate(0D);
        if (buyOrderitemPojo.getMachuid() == null) buyOrderitemPojo.setMachuid("");
        if (buyOrderitemPojo.getMachitemid() == null) buyOrderitemPojo.setMachitemid("");
        if (buyOrderitemPojo.getMachbatch() == null) buyOrderitemPojo.setMachbatch("");
        if (buyOrderitemPojo.getMachgroupid() == null) buyOrderitemPojo.setMachgroupid("");
        if (buyOrderitemPojo.getMainplanuid() == null) buyOrderitemPojo.setMainplanuid("");
        if (buyOrderitemPojo.getMainplanitemid() == null) buyOrderitemPojo.setMainplanitemid("");
        if (buyOrderitemPojo.getMrpuid() == null) buyOrderitemPojo.setMrpuid("");
     if(buyOrderitemPojo.getMrpitemid()==null) buyOrderitemPojo.setMrpitemid("");
     if(buyOrderitemPojo.getMrpobjgoodsid()==null) buyOrderitemPojo.setMrpobjgoodsid("");
        if (buyOrderitemPojo.getVirtualitem() == null) buyOrderitemPojo.setVirtualitem(0);
        if (buyOrderitemPojo.getCustomer() == null) buyOrderitemPojo.setCustomer("");
        if (buyOrderitemPojo.getCustpo() == null) buyOrderitemPojo.setCustpo("");
        if (buyOrderitemPojo.getBatchno() == null) buyOrderitemPojo.setBatchno("");
        if (buyOrderitemPojo.getDisannulmark() == null) buyOrderitemPojo.setDisannulmark(0);
        if (buyOrderitemPojo.getDisannullisterid() == null) buyOrderitemPojo.setDisannullisterid("");
        if (buyOrderitemPojo.getDisannullister() == null) buyOrderitemPojo.setDisannullister("");
        if (buyOrderitemPojo.getDisannuldate() == null) buyOrderitemPojo.setDisannuldate(new Date());
        if (buyOrderitemPojo.getAttributejson() == null) buyOrderitemPojo.setAttributejson("");
        if (buyOrderitemPojo.getSourcetype() == null) buyOrderitemPojo.setSourcetype(0);
        if (buyOrderitemPojo.getStdprice() == null) buyOrderitemPojo.setStdprice(0D);
        if (buyOrderitemPojo.getStdamount() == null) buyOrderitemPojo.setStdamount(0D);
        if (buyOrderitemPojo.getRebate() == null) buyOrderitemPojo.setRebate(0);
        if (buyOrderitemPojo.getPassedqty() == null) buyOrderitemPojo.setPassedqty(0D);
        if (buyOrderitemPojo.getInstoreqty() == null) buyOrderitemPojo.setInstoreqty(0D);
        if (buyOrderitemPojo.getInvoqty() == null) buyOrderitemPojo.setInvoqty(0D);
        if (buyOrderitemPojo.getInvoclosed() == null) buyOrderitemPojo.setInvoclosed(0);
        if (buyOrderitemPojo.getAvginvoamt() == null) buyOrderitemPojo.setAvginvoamt(0D);
        if (buyOrderitemPojo.getAvgfirstamt() == null) buyOrderitemPojo.setAvgfirstamt(0D);
     if(buyOrderitemPojo.getAvglastamt()==null) buyOrderitemPojo.setAvglastamt(0D);
     if(buyOrderitemPojo.getInvoamt()==null) buyOrderitemPojo.setInvoamt(0D);
        if (buyOrderitemPojo.getCustom1() == null) buyOrderitemPojo.setCustom1("");
        if (buyOrderitemPojo.getCustom2() == null) buyOrderitemPojo.setCustom2("");
        if (buyOrderitemPojo.getCustom3() == null) buyOrderitemPojo.setCustom3("");
        if (buyOrderitemPojo.getCustom4() == null) buyOrderitemPojo.setCustom4("");
        if (buyOrderitemPojo.getCustom5() == null) buyOrderitemPojo.setCustom5("");
        if (buyOrderitemPojo.getCustom6() == null) buyOrderitemPojo.setCustom6("");
        if (buyOrderitemPojo.getCustom7() == null) buyOrderitemPojo.setCustom7("");
        if (buyOrderitemPojo.getCustom8() == null) buyOrderitemPojo.setCustom8("");
        if (buyOrderitemPojo.getCustom9() == null) buyOrderitemPojo.setCustom9("");
        if (buyOrderitemPojo.getCustom10() == null) buyOrderitemPojo.setCustom10("");
        if (buyOrderitemPojo.getCustom11() == null) buyOrderitemPojo.setCustom11("");
        if (buyOrderitemPojo.getCustom12() == null) buyOrderitemPojo.setCustom12("");
        if (buyOrderitemPojo.getCustom13() == null) buyOrderitemPojo.setCustom13("");
        if (buyOrderitemPojo.getCustom14() == null) buyOrderitemPojo.setCustom14("");
        if (buyOrderitemPojo.getCustom15() == null) buyOrderitemPojo.setCustom15("");
        if (buyOrderitemPojo.getCustom16() == null) buyOrderitemPojo.setCustom16("");
        if (buyOrderitemPojo.getCustom17() == null) buyOrderitemPojo.setCustom17("");
        if (buyOrderitemPojo.getCustom18() == null) buyOrderitemPojo.setCustom18("");
        if (buyOrderitemPojo.getTenantid() == null) buyOrderitemPojo.setTenantid("");
        if (buyOrderitemPojo.getRevision() == null) buyOrderitemPojo.setRevision(0);
        return buyOrderitemPojo;
    }
}
