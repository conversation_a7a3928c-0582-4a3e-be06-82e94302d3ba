package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatAttributePojo;

import java.util.List;

/**
 * SPU属性表(MatAttribute)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-14 14:45:49
 */
public interface MatAttributeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatAttributePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatAttributePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matAttributePojo 实例对象
     * @return 实例对象
     */
    MatAttributePojo insert(MatAttributePojo matAttributePojo);

    /**
     * 修改数据
     *
     * @param matAttributepojo 实例对象
     * @return 实例对象
     */
    MatAttributePojo update(MatAttributePojo matAttributepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    /**
     *  显示列
     *
     * @return 查询结果
     */
    List<MatAttributePojo> getListByShow(String useDomain,String tid);
}
