package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyInvoiceEntity;
import inks.service.sa.som.domain.BuyInvoiceitemEntity;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.BuyDeductionitemMapper;
import inks.service.sa.som.mapper.BuyFinishingitemMapper;
import inks.service.sa.som.mapper.BuyInvoiceMapper;
import inks.service.sa.som.mapper.BuyInvoiceitemMapper;
import inks.service.sa.som.service.BuyInvoiceService;
import inks.service.sa.som.service.BuyInvoiceitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 采购开票(BuyInvoice)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-15 15:01:35
 */
@Service("buyInvoiceService")
public class BuyInvoiceServiceImpl implements BuyInvoiceService {
    @Resource
    private BuyInvoiceMapper buyInvoiceMapper;

    @Resource
    private BuyInvoiceitemMapper buyInvoiceitemMapper;

    
    @Resource
    private BuyInvoiceitemService buyInvoiceitemService;
    @Resource
    private BuyFinishingitemMapper buyFinishingitemMapper;

    @Resource
    private BuyDeductionitemMapper buyDeductionitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyInvoicePojo getEntity(String key, String tid) {
        return this.buyInvoiceMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyInvoiceitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyInvoiceitemdetailPojo> lst = buyInvoiceMapper.getPageList(queryParam);
            PageInfo<BuyInvoiceitemdetailPojo> pageInfo = new PageInfo<BuyInvoiceitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyInvoicePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BuyInvoicePojo buyInvoicePojo = this.buyInvoiceMapper.getEntity(key, tid);
            //读取子表
            buyInvoicePojo.setItem(buyInvoiceitemMapper.getList(buyInvoicePojo.getId(), buyInvoicePojo.getTenantid()));
            return buyInvoicePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyInvoicePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyInvoicePojo> lst = buyInvoiceMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(buyInvoiceitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BuyInvoicePojo> pageInfo = new PageInfo<BuyInvoicePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyInvoicePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyInvoicePojo> lst = buyInvoiceMapper.getPageTh(queryParam);
            PageInfo<BuyInvoicePojo> pageInfo = new PageInfo<BuyInvoicePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param buyInvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyInvoicePojo insert(BuyInvoicePojo buyInvoicePojo) {
//初始化NULL字段
        if (buyInvoicePojo.getRefno() == null) buyInvoicePojo.setRefno("");
        if (buyInvoicePojo.getBilltype() == null) buyInvoicePojo.setBilltype("");
        if (buyInvoicePojo.getBilltitle() == null) buyInvoicePojo.setBilltitle("");
        if (buyInvoicePojo.getBilldate() == null) buyInvoicePojo.setBilldate(new Date());
        if (buyInvoicePojo.getProjectid() == null) buyInvoicePojo.setProjectid("");
        if (buyInvoicePojo.getProjcode() == null) buyInvoicePojo.setProjcode("");
        if (buyInvoicePojo.getProjname() == null) buyInvoicePojo.setProjname("");
        if (buyInvoicePojo.getGroupid() == null) buyInvoicePojo.setGroupid("");
        if (buyInvoicePojo.getTaxrate() == null) buyInvoicePojo.setTaxrate(0);
        if (buyInvoicePojo.getTaxamount() == null) buyInvoicePojo.setTaxamount(0D);
        if (buyInvoicePojo.getTaxtotal() == null) buyInvoicePojo.setTaxtotal(0D);
        if (buyInvoicePojo.getAmount() == null) buyInvoicePojo.setAmount(0D);
        if (buyInvoicePojo.getInvodate() == null) buyInvoicePojo.setInvodate(new Date());
        if (buyInvoicePojo.getInvocode() == null) buyInvoicePojo.setInvocode("");
        if (buyInvoicePojo.getAimdate() == null) buyInvoicePojo.setAimdate(new Date());
        if (buyInvoicePojo.getPaid() == null) buyInvoicePojo.setPaid(0D);
        if (buyInvoicePojo.getSummary() == null) buyInvoicePojo.setSummary("");
        if (buyInvoicePojo.getCreateby() == null) buyInvoicePojo.setCreateby("");
        if (buyInvoicePojo.getCreatebyid() == null) buyInvoicePojo.setCreatebyid("");
        if (buyInvoicePojo.getCreatedate() == null) buyInvoicePojo.setCreatedate(new Date());
        if (buyInvoicePojo.getLister() == null) buyInvoicePojo.setLister("");
        if (buyInvoicePojo.getListerid() == null) buyInvoicePojo.setListerid("");
        if (buyInvoicePojo.getModifydate() == null) buyInvoicePojo.setModifydate(new Date());
        if (buyInvoicePojo.getAssessor() == null) buyInvoicePojo.setAssessor("");
        if (buyInvoicePojo.getAssessorid() == null) buyInvoicePojo.setAssessorid("");
        if (buyInvoicePojo.getAssessdate() == null) buyInvoicePojo.setAssessdate(new Date());
        if (buyInvoicePojo.getStatecode() == null) buyInvoicePojo.setStatecode("");
        if (buyInvoicePojo.getStatedate() == null) buyInvoicePojo.setStatedate(new Date());
        if (buyInvoicePojo.getClosed() == null) buyInvoicePojo.setClosed(0);
        if (buyInvoicePojo.getDisannulmark() == null) buyInvoicePojo.setDisannulmark(0);
        if (buyInvoicePojo.getFmdocmark() == null) buyInvoicePojo.setFmdocmark(0);
        if (buyInvoicePojo.getFmdoccode() == null) buyInvoicePojo.setFmdoccode("");
        if (buyInvoicePojo.getOperator() == null) buyInvoicePojo.setOperator("");
        if (buyInvoicePojo.getFirstamt() == null) buyInvoicePojo.setFirstamt(0D);
        if (buyInvoicePojo.getLastamt() == null) buyInvoicePojo.setLastamt(0D);
        if (buyInvoicePojo.getCustom1() == null) buyInvoicePojo.setCustom1("");
        if (buyInvoicePojo.getCustom2() == null) buyInvoicePojo.setCustom2("");
        if (buyInvoicePojo.getCustom3() == null) buyInvoicePojo.setCustom3("");
        if (buyInvoicePojo.getCustom4() == null) buyInvoicePojo.setCustom4("");
        if (buyInvoicePojo.getCustom5() == null) buyInvoicePojo.setCustom5("");
        if (buyInvoicePojo.getCustom6() == null) buyInvoicePojo.setCustom6("");
        if (buyInvoicePojo.getCustom7() == null) buyInvoicePojo.setCustom7("");
        if (buyInvoicePojo.getCustom8() == null) buyInvoicePojo.setCustom8("");
        if (buyInvoicePojo.getCustom9() == null) buyInvoicePojo.setCustom9("");
        if (buyInvoicePojo.getCustom10() == null) buyInvoicePojo.setCustom10("");
        if (buyInvoicePojo.getTenantid() == null) buyInvoicePojo.setTenantid("");
        if (buyInvoicePojo.getRevision() == null) buyInvoicePojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BuyInvoiceEntity buyInvoiceEntity = new BuyInvoiceEntity();
        BeanUtils.copyProperties(buyInvoicePojo, buyInvoiceEntity);
        //设置id和新建日期
        buyInvoiceEntity.setId(id);
        buyInvoiceEntity.setRevision(1);  //乐观锁
        //插入主表
        this.buyInvoiceMapper.insert(buyInvoiceEntity);
        //Item子表处理
        List<BuyInvoiceitemPojo> lst = buyInvoicePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                BuyInvoiceitemPojo itemPojo = this.buyInvoiceitemService.clearNull(lst.get(i));
                BuyInvoiceitemEntity buyInvoiceitemEntity = new BuyInvoiceitemEntity();
                BeanUtils.copyProperties(itemPojo, buyInvoiceitemEntity);
                //设置id和Pid
                buyInvoiceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                buyInvoiceitemEntity.setPid(id);
                buyInvoiceitemEntity.setTenantid(buyInvoicePojo.getTenantid());
                buyInvoiceitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.buyInvoiceitemMapper.insert(buyInvoiceitemEntity);


                if (lst.get(i).getFinishtype().equals("采购验收") || lst.get(i).getFinishtype().equals("采购退货") ||
                        lst.get(i).getFinishtype().equals("其他收货") || lst.get(i).getFinishtype().equals("其他退货")) {
                    this.buyInvoiceMapper.updateFiniInvoFinish(lst.get(i).getFinishitemid(), lst.get(i).getFinishuid(), buyInvoicePojo.getTenantid());
                    // 超数检查
                    BuyFinishingitemPojo buyFinishingitemPojo = this.buyFinishingitemMapper.getEntity(lst.get(i).getFinishitemid(), buyInvoicePojo.getTenantid());
                    if (buyFinishingitemPojo != null) {
                        if (buyFinishingitemPojo.getInvoqty() > buyFinishingitemPojo.getQuantity()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,开票总数:" + buyFinishingitemPojo.getInvoqty() + "超出收货数:" + buyFinishingitemPojo.getQuantity());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getFinishuid());
                    }
                    this.buyInvoiceMapper.updateFiniInvoCount(lst.get(i).getFinishitemid(), lst.get(i).getFinishuid(), buyInvoicePojo.getTenantid());
                    if (isNotBlank(lst.get(i).getOrderitemid())&& isNotBlank(lst.get(i).getOrderuid())) {
                        // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
                        syncOrderAboutInvo(lst.get(i).getOrderitemid(), lst.get(i).getOrderuid(), buyInvoicePojo.getTenantid());
                    }
                } else if (lst.get(i).getFinishtype().equals("采购扣款") || lst.get(i).getFinishtype().equals("其他扣款")) {
                    this.buyInvoiceMapper.updateDeduInvoFinish(lst.get(i).getFinishitemid(), lst.get(i).getFinishuid(), buyInvoicePojo.getTenantid());
                    // 超数检查
                    BuyDeductionitemPojo buyDeductionitemPojo = this.buyDeductionitemMapper.getEntity(lst.get(i).getFinishitemid(), buyInvoicePojo.getTenantid());
                    if (buyDeductionitemPojo != null) {
                        if (buyDeductionitemPojo.getInvoqty() > buyDeductionitemPojo.getQuantity()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,开票总数:" + buyDeductionitemPojo.getInvoqty() + "超出扣款数:" + buyDeductionitemPojo.getQuantity());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getFinishuid());
                    }
                    this.buyInvoiceMapper.updateDeduInvoCount(lst.get(i).getFinishitemid(), lst.get(i).getFinishuid(), buyInvoicePojo.getTenantid());
                } else if (lst.get(i).getFinishtype().equals("委制验收") || lst.get(i).getFinishtype().equals("委制退货")) {
                    this.buyInvoiceMapper.updateScCompleteItemInvoFinish(lst.get(i).getFinishitemid(), buyInvoicePojo.getTenantid());
                    // 超数检查
                    Map<String, Object> scCompleteItemMap = this.buyInvoiceMapper.getScCompleteItemEntity(lst.get(i).getFinishitemid(), buyInvoicePojo.getTenantid());
                    if (scCompleteItemMap != null) {
                        //InvoQty,Quantity 转为double
                        double invoQty = Double.parseDouble(scCompleteItemMap.get("InvoQty").toString());
                        double quantity = Double.parseDouble(scCompleteItemMap.get("Quantity").toString());
                        if (invoQty > quantity) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,开票总数:" + invoQty + "超出扣款数:" + quantity);
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getFinishuid());
                    }
                    this.buyInvoiceMapper.updateScCompleteInvoCount(lst.get(i).getFinishitemid(), buyInvoicePojo.getTenantid());
                }

            }
        }
        //返回Bill实例
        return this.getBillEntity(buyInvoiceEntity.getId(), buyInvoiceEntity.getTenantid());

    }

    private void syncOrderAboutInvo(String orderitemid, String orderuid, String tenantid) {
        String orerId = buyInvoiceMapper.getOrerId(orderitemid, tenantid);
        this.buyInvoiceMapper.updateOrderItemInvoFinish(orderitemid, orderuid, tenantid);
        this.buyInvoiceMapper.updateOrderInvoCount(orderitemid, orderuid, tenantid);
        this.buyInvoiceMapper.updateOrderItemAvgInvoAmt(orerId, tenantid);
    }

    /**
     * 修改数据
     *
     * @param buyInvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyInvoicePojo update(BuyInvoicePojo buyInvoicePojo) {
        //主表更改
        BuyInvoiceEntity buyInvoiceEntity = new BuyInvoiceEntity();
        BeanUtils.copyProperties(buyInvoicePojo, buyInvoiceEntity);
        BuyInvoicePojo dbPojo = this.buyInvoiceMapper.getEntity(buyInvoiceEntity.getId(), buyInvoiceEntity.getTenantid());
        if (buyInvoiceEntity.getAmount() != null && buyInvoiceEntity.getAmount() != dbPojo.getAmount()) {
            String citeBillName = getCiteBillName(buyInvoiceEntity.getId(), buyInvoiceEntity.getTenantid());
            if (isNotBlank(citeBillName)) {
                throw new RuntimeException("发票已被引用,禁止修改:" + citeBillName);
            }
        }
        this.buyInvoiceMapper.update(buyInvoiceEntity);
        //Item子表处理
        List<BuyInvoiceitemPojo> lst = buyInvoicePojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = buyInvoiceMapper.getDelItemIds(buyInvoicePojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                // 加上引用检测  目前只校验"采购付款"
                String citeBillName = getCiteBillName(buyInvoiceEntity.getId(), buyInvoiceEntity.getTenantid());
                if (isNotBlank(citeBillName)) {
                    throw new RuntimeException("发票已被引用,禁止修改:" + citeBillName);
                }

                BuyInvoiceitemPojo delPojo = this.buyInvoiceitemMapper.getEntity(lstDelIds.get(i), buyInvoiceEntity.getTenantid());
                this.buyInvoiceitemMapper.delete(lstDelIds.get(i), buyInvoiceEntity.getTenantid());
                if (delPojo.getFinishtype().equals("采购验收") || delPojo.getFinishtype().equals("采购退货") ||
                        delPojo.getFinishtype().equals("其他收货") || delPojo.getFinishtype().equals("其他退货")) {
                    this.buyInvoiceMapper.updateFiniInvoFinish(delPojo.getFinishitemid(), delPojo.getFinishuid(), buyInvoicePojo.getTenantid());
                    this.buyInvoiceMapper.updateFiniInvoCount(delPojo.getFinishitemid(), delPojo.getFinishuid(), buyInvoicePojo.getTenantid());
                    if (isNotBlank(lst.get(i).getOrderitemid())&& isNotBlank(lst.get(i).getOrderuid())) {
                        // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
                        syncOrderAboutInvo(lst.get(i).getOrderitemid(), lst.get(i).getOrderuid(), buyInvoicePojo.getTenantid());
                    }
                } else if (delPojo.getFinishtype().equals("采购扣款") || delPojo.getFinishtype().equals("其他扣款")) {
                    this.buyInvoiceMapper.updateDeduInvoFinish(delPojo.getFinishitemid(), delPojo.getFinishuid(), buyInvoicePojo.getTenantid());
                    this.buyInvoiceMapper.updateDeduInvoCount(delPojo.getFinishitemid(), delPojo.getFinishuid(), buyInvoicePojo.getTenantid());
                } else if (delPojo.getFinishtype().equals("委制验收") || delPojo.getFinishtype().equals("委制退货")) {
                    this.buyInvoiceMapper.updateScCompleteItemInvoFinish(delPojo.getFinishitemid(), buyInvoicePojo.getTenantid());
                    this.buyInvoiceMapper.updateScCompleteInvoCount(delPojo.getFinishitemid(), buyInvoicePojo.getTenantid());
                }
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                BuyInvoiceitemEntity buyInvoiceitemEntity = new BuyInvoiceitemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    BuyInvoiceitemPojo itemPojo = this.buyInvoiceitemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, buyInvoiceitemEntity);
                    //设置id和Pid
                    buyInvoiceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    buyInvoiceitemEntity.setPid(buyInvoiceEntity.getId());  // 主表 id
                    buyInvoiceitemEntity.setTenantid(buyInvoicePojo.getTenantid());   // 租户id
                    buyInvoiceitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.buyInvoiceitemMapper.insert(buyInvoiceitemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), buyInvoiceitemEntity);
                    buyInvoiceitemEntity.setTenantid(buyInvoicePojo.getTenantid());
                    this.buyInvoiceitemMapper.update(buyInvoiceitemEntity);
                }

                if (lst.get(i).getFinishtype().equals("采购验收") || lst.get(i).getFinishtype().equals("采购退货") ||
                        lst.get(i).getFinishtype().equals("其他收货") || lst.get(i).getFinishtype().equals("其他退货")) {
                    this.buyInvoiceMapper.updateFiniInvoFinish(lst.get(i).getFinishitemid(), lst.get(i).getFinishuid(), buyInvoicePojo.getTenantid());
                    // 超数检查
                    BuyFinishingitemPojo buyFinishingitemPojo = this.buyFinishingitemMapper.getEntity(lst.get(i).getFinishitemid(), buyInvoicePojo.getTenantid());
                    if (buyFinishingitemPojo != null) {
                        if (buyFinishingitemPojo.getInvoqty() > buyFinishingitemPojo.getQuantity()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,开票总数:" + buyFinishingitemPojo.getInvoqty() + "超出收货数:" + buyFinishingitemPojo.getQuantity());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getFinishuid());
                    }
                    this.buyInvoiceMapper.updateFiniInvoCount(lst.get(i).getFinishitemid(), lst.get(i).getFinishuid(), buyInvoicePojo.getTenantid());
                    if (isNotBlank(lst.get(i).getOrderitemid())&& isNotBlank(lst.get(i).getOrderuid())) {
                        // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
                        syncOrderAboutInvo(lst.get(i).getOrderitemid(), lst.get(i).getOrderuid(), buyInvoicePojo.getTenantid());
                    }
                } else if (lst.get(i).getFinishtype().equals("采购扣款") || lst.get(i).getFinishtype().equals("其他扣款")) {
                    this.buyInvoiceMapper.updateDeduInvoFinish(lst.get(i).getFinishitemid(), lst.get(i).getFinishuid(), buyInvoicePojo.getTenantid());
                    // 超数检查
                    BuyDeductionitemPojo buyDeductionitemPojo = this.buyDeductionitemMapper.getEntity(lst.get(i).getFinishitemid(), buyInvoicePojo.getTenantid());
                    if (buyDeductionitemPojo != null) {
                        if (buyDeductionitemPojo.getInvoqty() > buyDeductionitemPojo.getQuantity()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,开票总数:" + buyDeductionitemPojo.getInvoqty() + "超出扣款数:" + buyDeductionitemPojo.getQuantity());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getFinishuid());
                    }
                    this.buyInvoiceMapper.updateDeduInvoCount(lst.get(i).getFinishitemid(), lst.get(i).getFinishuid(), buyInvoicePojo.getTenantid());
                } else if (lst.get(i).getFinishtype().equals("委制验收") || lst.get(i).getFinishtype().equals("委制退货")) {
                    this.buyInvoiceMapper.updateScCompleteItemInvoFinish(lst.get(i).getFinishitemid(), buyInvoicePojo.getTenantid());
                    // 超数检查
                    Map<String, Object> scCompleteItemMap = this.buyInvoiceMapper.getScCompleteItemEntity(lst.get(i).getFinishitemid(), buyInvoicePojo.getTenantid());
                    if (scCompleteItemMap != null) {
                        //InvoQty,Quantity 转为double
                        double invoQty = Double.parseDouble(scCompleteItemMap.get("InvoQty").toString());
                        double quantity = Double.parseDouble(scCompleteItemMap.get("Quantity").toString());
                        if (invoQty > quantity) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,开票总数:" + invoQty + "超出扣款数:" + quantity);
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getFinishuid());
                    }
                    this.buyInvoiceMapper.updateScCompleteInvoCount(lst.get(i).getFinishitemid(), buyInvoicePojo.getTenantid());
                }

            }
        }
        //返回Bill实例
        return this.getBillEntity(buyInvoiceEntity.getId(), buyInvoiceEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BuyInvoicePojo buyInvoicePojo = this.getBillEntity(key, tid);
        // 目前只校验"采购付款",若增加额外校验 需要修改逻辑
        String citeBillName = getCiteBillName(buyInvoicePojo.getId(), buyInvoicePojo.getTenantid());
        if (isNotBlank(citeBillName)) {
            throw new RuntimeException("发票已被引用,禁止修改:" + citeBillName);
        }
        //Item子表处理
        List<BuyInvoiceitemPojo> lst = buyInvoicePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                BuyInvoiceitemPojo delPojo = this.buyInvoiceitemMapper.getEntity(lst.get(i).getId(), tid);
                this.buyInvoiceitemMapper.delete(lst.get(i).getId(), tid);
                if (delPojo.getFinishtype().equals("采购验收") || delPojo.getFinishtype().equals("采购退货") ||
                        delPojo.getFinishtype().equals("其他收货") || delPojo.getFinishtype().equals("其他退货")) {
                    this.buyInvoiceMapper.updateFiniInvoFinish(delPojo.getFinishitemid(), delPojo.getFinishuid(), buyInvoicePojo.getTenantid());
                    this.buyInvoiceMapper.updateFiniInvoCount(delPojo.getFinishitemid(), delPojo.getFinishuid(), buyInvoicePojo.getTenantid());
                    if (isNotBlank(lst.get(i).getOrderitemid())&& isNotBlank(lst.get(i).getOrderuid())) {
                        // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
                        syncOrderAboutInvo(lst.get(i).getOrderitemid(), lst.get(i).getOrderuid(), buyInvoicePojo.getTenantid());
                    }
                } else if (delPojo.getFinishtype().equals("采购扣款") || delPojo.getFinishtype().equals("其他扣款")) {
                    this.buyInvoiceMapper.updateDeduInvoFinish(delPojo.getFinishitemid(), delPojo.getFinishuid(), buyInvoicePojo.getTenantid());
                    this.buyInvoiceMapper.updateDeduInvoCount(delPojo.getFinishitemid(), delPojo.getFinishuid(), buyInvoicePojo.getTenantid());
                } else if (delPojo.getFinishtype().equals("委制验收") || delPojo.getFinishtype().equals("委制退货")) {
                    this.buyInvoiceMapper.updateScCompleteItemInvoFinish(delPojo.getFinishitemid(), buyInvoicePojo.getTenantid());
                    this.buyInvoiceMapper.updateScCompleteInvoCount(delPojo.getFinishitemid(), buyInvoicePojo.getTenantid());
                }
            }
        }
        return this.buyInvoiceMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param buyInvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyInvoicePojo approval(BuyInvoicePojo buyInvoicePojo) {
        //主表更改
        BuyInvoiceEntity buyInvoiceEntity = new BuyInvoiceEntity();
        BeanUtils.copyProperties(buyInvoicePojo, buyInvoiceEntity);
        this.buyInvoiceMapper.approval(buyInvoiceEntity);
        //返回Bill实例
        return this.getBillEntity(buyInvoiceEntity.getId(), buyInvoiceEntity.getTenantid());
    }

    @Override
    // 查询Item是否被引用
    public String getCiteBillName(String key, String tid) {
        // 目前只校验"采购付款",若增加额外校验 需要修改逻辑
        return this.buyInvoiceMapper.getCiteBillName(key, tid);
    }


}
