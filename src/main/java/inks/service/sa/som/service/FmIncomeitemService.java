package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmIncomeitemPojo;

import java.util.List;
/**
 * 收入明细(FmIncomeitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-29 15:52:47
 */
public interface FmIncomeitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmIncomeitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmIncomeitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<FmIncomeitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param fmIncomeitemPojo 实例对象
     * @return 实例对象
     */
    FmIncomeitemPojo insert(FmIncomeitemPojo fmIncomeitemPojo);

    /**
     * 修改数据
     *
     * @param fmIncomeitempojo 实例对象
     * @return 实例对象
     */
    FmIncomeitemPojo update(FmIncomeitemPojo fmIncomeitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param fmIncomeitempojo 实例对象
     * @return 实例对象
     */
    FmIncomeitemPojo clearNull(FmIncomeitemPojo fmIncomeitempojo);
}
