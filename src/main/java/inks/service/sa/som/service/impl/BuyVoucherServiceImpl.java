package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.sa.som.domain.BuyVoucherEntity;
import inks.service.sa.som.domain.BuyVouchercashEntity;
import inks.service.sa.som.domain.BuyVoucheritemEntity;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.*;
import inks.service.sa.som.service.BuyVoucherService;
import inks.service.sa.som.service.BuyVouchercashService;
import inks.service.sa.som.service.BuyVoucheritemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 采购付款(BuyVoucher)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-15 15:02:31
 */
@Service("buyVoucherService")
public class BuyVoucherServiceImpl implements BuyVoucherService {
    @Resource
    private BuyVoucherMapper buyVoucherMapper;

    @Resource
    private BuyVoucheritemMapper buyVoucheritemMapper;

    
    @Resource
    private BuyVoucheritemService buyVoucheritemService;

    @Resource
    private BuyVouchercashMapper buyVouchercashMapper;

    
    @Resource
    private BuyVouchercashService buyVouchercashService;

    @Resource
    private BuyInvoiceMapper buyInvoiceMapper;

    @Resource
    private BuyFinishingMapper buyFinishingMapper;

    @Resource
    private BuyAccountrecMapper buyAccountrecMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyVoucherPojo getEntity(String key, String tid) {
        return this.buyVoucherMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyVoucheritemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyVoucheritemdetailPojo> lst = buyVoucherMapper.getPageList(queryParam);
            PageInfo<BuyVoucheritemdetailPojo> pageInfo = new PageInfo<BuyVoucheritemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyVoucherPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BuyVoucherPojo buyVoucherPojo = this.buyVoucherMapper.getEntity(key, tid);
            //读取子表
            buyVoucherPojo.setItem(buyVoucheritemMapper.getList(buyVoucherPojo.getId(), buyVoucherPojo.getTenantid()));
            //读取Cash
            buyVoucherPojo.setCash(buyVouchercashMapper.getList(buyVoucherPojo.getId(), buyVoucherPojo.getTenantid()));
            return buyVoucherPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyVoucherPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyVoucherPojo> lst = buyVoucherMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(buyVoucheritemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setCash(buyVouchercashMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BuyVoucherPojo> pageInfo = new PageInfo<BuyVoucherPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyVoucherPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyVoucherPojo> lst = buyVoucherMapper.getPageTh(queryParam);
            PageInfo<BuyVoucherPojo> pageInfo = new PageInfo<BuyVoucherPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param buyVoucherPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyVoucherPojo insert(BuyVoucherPojo buyVoucherPojo) {
        String tid = buyVoucherPojo.getTenantid();
//初始化NULL字段
        if (buyVoucherPojo.getRefno() == null) buyVoucherPojo.setRefno("");
        if (buyVoucherPojo.getBilltype() == null) buyVoucherPojo.setBilltype("");
        if (buyVoucherPojo.getBilltitle() == null) buyVoucherPojo.setBilltitle("");
        if (buyVoucherPojo.getBilldate() == null) buyVoucherPojo.setBilldate(new Date());
        if (buyVoucherPojo.getProjectid() == null) buyVoucherPojo.setProjectid("");
        if (buyVoucherPojo.getProjcode() == null) buyVoucherPojo.setProjcode("");
        if (buyVoucherPojo.getProjname() == null) buyVoucherPojo.setProjname("");
        if (buyVoucherPojo.getGroupid() == null) buyVoucherPojo.setGroupid("");
        if (buyVoucherPojo.getBillamount() == null) buyVoucherPojo.setBillamount(0D);
        if (buyVoucherPojo.getOperator() == null) buyVoucherPojo.setOperator("");
        if (buyVoucherPojo.getCitecode() == null) buyVoucherPojo.setCitecode("");
        if (buyVoucherPojo.getReturnuid() == null) buyVoucherPojo.setReturnuid("");
        if (buyVoucherPojo.getOrguid() == null) buyVoucherPojo.setOrguid("");
        if (buyVoucherPojo.getSummary() == null) buyVoucherPojo.setSummary("");
        if (buyVoucherPojo.getCreateby() == null) buyVoucherPojo.setCreateby("");
        if (buyVoucherPojo.getCreatebyid() == null) buyVoucherPojo.setCreatebyid("");
        if (buyVoucherPojo.getCreatedate() == null) buyVoucherPojo.setCreatedate(new Date());
        if (buyVoucherPojo.getLister() == null) buyVoucherPojo.setLister("");
        if (buyVoucherPojo.getListerid() == null) buyVoucherPojo.setListerid("");
        if (buyVoucherPojo.getModifydate() == null) buyVoucherPojo.setModifydate(new Date());
        if (buyVoucherPojo.getAssessor() == null) buyVoucherPojo.setAssessor("");
        if (buyVoucherPojo.getAssessorid() == null) buyVoucherPojo.setAssessorid("");
        if (buyVoucherPojo.getAssessdate() == null) buyVoucherPojo.setAssessdate(new Date());
        if (buyVoucherPojo.getFmdocmark()==null) buyVoucherPojo.setFmdocmark(0);
        if (buyVoucherPojo.getFmdoccode()==null) buyVoucherPojo.setFmdoccode("");
        if (buyVoucherPojo.getCustom1() == null) buyVoucherPojo.setCustom1("");
        if (buyVoucherPojo.getCustom2() == null) buyVoucherPojo.setCustom2("");
        if (buyVoucherPojo.getCustom3() == null) buyVoucherPojo.setCustom3("");
        if (buyVoucherPojo.getCustom4() == null) buyVoucherPojo.setCustom4("");
        if (buyVoucherPojo.getCustom5() == null) buyVoucherPojo.setCustom5("");
        if (buyVoucherPojo.getCustom6() == null) buyVoucherPojo.setCustom6("");
        if (buyVoucherPojo.getCustom7() == null) buyVoucherPojo.setCustom7("");
        if (buyVoucherPojo.getCustom8() == null) buyVoucherPojo.setCustom8("");
        if (buyVoucherPojo.getCustom9() == null) buyVoucherPojo.setCustom9("");
        if (buyVoucherPojo.getCustom10() == null) buyVoucherPojo.setCustom10("");
        if (tid == null) buyVoucherPojo.setTenantid("");
        if (buyVoucherPojo.getRevision() == null) buyVoucherPojo.setRevision(0);

        // 结账检查
        BuyAccountrecPojo buyAccountrecPojo = this.buyAccountrecMapper.getEntityByMax(tid);
        if (buyAccountrecPojo != null && DateUtils.getTimestamp(buyVoucherPojo.getBilldate()) <= DateUtils.getTimestamp(buyAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + buyAccountrecPojo.getEnddate() + ",禁止新建结账前单据");
        }

        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BuyVoucherEntity buyVoucherEntity = new BuyVoucherEntity();
        BeanUtils.copyProperties(buyVoucherPojo, buyVoucherEntity);
        String billType = buyVoucherEntity.getBilltype();
        //设置id和新建日期
        buyVoucherEntity.setId(id);
        buyVoucherEntity.setRevision(1);  //乐观锁
        //插入主表
        this.buyVoucherMapper.insert(buyVoucherEntity);

        // 如果是红冲单 同步原始单据的红冲标志
        if (billType.contains("红冲")) {
            this.buyVoucherMapper.updateOrgReturn(buyVoucherPojo.getOrguid(),buyVoucherPojo.getRefno(), buyVoucherEntity.getTenantid());
        }


        //Item子表处理
        List<BuyVoucheritemPojo> lst = buyVoucherPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                BuyVoucheritemPojo itemPojo = this.buyVoucheritemService.clearNull(lst.get(i));
                BuyVoucheritemEntity buyVoucheritemEntity = new BuyVoucheritemEntity();
                BeanUtils.copyProperties(itemPojo, buyVoucheritemEntity);
                //设置id和Pid
                buyVoucheritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                buyVoucheritemEntity.setPid(id);
                buyVoucheritemEntity.setTenantid(tid);
                buyVoucheritemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.buyVoucheritemMapper.insert(buyVoucheritemEntity);
                // 同步发票金额
                if (billType.equals("采购付款")|| billType.equals("付款红冲")) {
                    // 同步采购发票,采购订单的收款,预收款,总收款等(主表Paid共收,FirstAmt预收,LastAmt收款,子表AvgFirstAmt, AvgLastAmt)
                    syncFirstAndLastAmt(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), tid);
//                    this.buyVoucherMapper.updateBuyInvoFinish(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), tid);
                    // 超数检查
                    BuyInvoicePojo buyInvoicePojo = this.buyInvoiceMapper.getEntity(lst.get(i).getInvoid(), tid);
                    if (buyInvoicePojo != null) {
                        if (buyInvoicePojo.getPaid() > buyInvoicePojo.getTaxamount()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,付款总额:" + buyInvoicePojo.getPaid() + "超出应付额:" + buyInvoicePojo.getTaxamount());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getInvobillcode());
                    }
                } else if (billType.equals("单据付款")) {
                    this.buyVoucherMapper.updateFiniFinish(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), tid);
                    // 超数检查
                    BuyFinishingPojo buyFinishingPojo = this.buyFinishingMapper.getEntity(lst.get(i).getInvoid(), tid);
                    if (buyFinishingPojo != null) {
                        if (buyFinishingPojo.getBillpaid() > buyFinishingPojo.getBilltaxamount()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,付款总额:" + buyFinishingPojo.getBillpaid() + "超出单据应付额:" + buyFinishingPojo.getBilltaxamount());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getInvobillcode());
                    }
                }
            }
        }

        //Cash子表处理
        List<BuyVouchercashPojo> lstcash = buyVoucherPojo.getCash();
        if (lstcash != null) {
            //循环每个item子表
            for (int i = 0; i < lstcash.size(); i++) {
                //初始化item的NULL
                BuyVouchercashPojo cashPojo = this.buyVouchercashService.clearNull(lstcash.get(i));
                BuyVouchercashEntity buyVouchercashEntity = new BuyVouchercashEntity();
                BeanUtils.copyProperties(cashPojo, buyVouchercashEntity);
                //设置id和Pid
                buyVouchercashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                buyVouchercashEntity.setPid(id);
                buyVouchercashEntity.setTenantid(tid);
                buyVouchercashEntity.setRevision(1);  //乐观锁
                //插入子表
                this.buyVouchercashMapper.insert(buyVouchercashEntity);

                this.buyVoucherMapper.updateCashAmount(buyVouchercashEntity.getCashaccid(), 0 - buyVouchercashEntity.getAmount(), tid);
            }
        }

        //返回Bill实例
        return this.getBillEntity(buyVoucherEntity.getId(), buyVoucherEntity.getTenantid());

    }



    /**
     * 修改数据
     *
     * @param buyVoucherPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyVoucherPojo update(BuyVoucherPojo buyVoucherPojo) {
        String tid = buyVoucherPojo.getTenantid();
        // 检查结账
        BuyAccountrecPojo buyAccountrecPojo = this.buyAccountrecMapper.getEntityByMax(tid);
        if (buyAccountrecPojo != null && DateUtils.getTimestamp(buyVoucherPojo.getBilldate()) <= DateUtils.getTimestamp(buyAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + buyAccountrecPojo.getEnddate() + ",禁止修改结账前单据");
        }
        BuyVoucherPojo orgPojo = this.buyVoucherMapper.getEntity(buyVoucherPojo.getId(), tid);
        if (buyAccountrecPojo != null && DateUtils.getTimestamp(orgPojo.getBilldate()) <= DateUtils.getTimestamp(buyAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + buyAccountrecPojo.getEnddate() + ",禁止修改结账前单据");
        }
        //主表更改
        BuyVoucherEntity buyVoucherEntity = new BuyVoucherEntity();
        BeanUtils.copyProperties(buyVoucherPojo, buyVoucherEntity);
        this.buyVoucherMapper.update(buyVoucherEntity);
        //Item子表处理
        List<BuyVoucheritemPojo> lst = buyVoucherPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = buyVoucherMapper.getDelItemIds(buyVoucherPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                BuyVoucheritemPojo delPojo = this.buyVoucheritemService.getEntity(lstDelIds.get(i), buyVoucherEntity.getTenantid());
                this.buyVoucheritemMapper.delete(lstDelIds.get(i), buyVoucherEntity.getTenantid());
                if (buyVoucherEntity.getBilltype().equals("采购付款")) {
                    // 同步采购发票,采购订单的收款,预收款,总收款等(主表Paid共收,FirstAmt预收,LastAmt收款,子表AvgFirstAmt, AvgLastAmt)
                    syncFirstAndLastAmt(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), tid);
//                    this.buyVoucherMapper.updateBuyInvoFinish(delPojo.getInvoid(), delPojo.getInvobillcode(), buyVoucherPojo.getTenantid());
                } else if (buyVoucherEntity.getBilltype().equals("单据付款")) {
                    this.buyVoucherMapper.updateFiniFinish(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), tid);
                }
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                BuyVoucheritemEntity buyVoucheritemEntity = new BuyVoucheritemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    BuyVoucheritemPojo itemPojo = this.buyVoucheritemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, buyVoucheritemEntity);
                    //设置id和Pid
                    buyVoucheritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    buyVoucheritemEntity.setPid(buyVoucherEntity.getId());  // 主表 id
                    buyVoucheritemEntity.setTenantid(tid);   // 租户id
                    buyVoucheritemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.buyVoucheritemMapper.insert(buyVoucheritemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), buyVoucheritemEntity);
                    buyVoucheritemEntity.setTenantid(tid);
                    this.buyVoucheritemMapper.update(buyVoucheritemEntity);
                }
                if (buyVoucherEntity.getBilltype().equals("采购付款")||buyVoucherEntity.getBilltype().equals("付款红冲")) {
                    // 同步采购发票,采购订单的收款,预收款,总收款等(主表Paid共收,FirstAmt预收,LastAmt收款,子表AvgFirstAmt, AvgLastAmt)
                    syncFirstAndLastAmt(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), tid);
//                    // 同步发票金额
//                    this.buyVoucherMapper.updateBuyInvoFinish(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), buyVoucherPojo.getTenantid());
                    // 超数检查
                    BuyInvoicePojo buyInvoicePojo = this.buyInvoiceMapper.getEntity(lst.get(i).getInvoid(), tid);
                    if (buyInvoicePojo != null) {
                        if (buyInvoicePojo.getPaid() > buyInvoicePojo.getTaxamount()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,付款总额:" + buyInvoicePojo.getPaid() + "超出应付额:" + buyInvoicePojo.getTaxamount());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getInvobillcode());
                    }
                } else if (buyVoucherEntity.getBilltype().equals("单据付款")) {
                    this.buyVoucherMapper.updateFiniFinish(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), tid);
                    // 超数检查
                    BuyFinishingPojo buyFinishingPojo = this.buyFinishingMapper.getEntity(lst.get(i).getInvoid(), tid);
                    if (buyFinishingPojo != null) {
                        if (buyFinishingPojo.getBillpaid() > buyFinishingPojo.getBilltaxamount()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,付款总额:" + buyFinishingPojo.getBillpaid() + "超出单据应付额:" + buyFinishingPojo.getBilltaxamount());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getInvobillcode());
                    }
                }
            }
        }

        //Cash子表处理
        List<BuyVouchercashPojo> lstcash = buyVoucherPojo.getCash();
        //获取被删除的Item
        List<String> lstcashDelIds = buyVoucherMapper.getDelCashIds(buyVoucherPojo);
        if (lstcashDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstcashDelIds.size(); i++) {
                BuyVouchercashPojo delPojo = this.buyVouchercashMapper.getEntity(lstcashDelIds.get(i), buyVoucherEntity.getTenantid());
                this.buyVouchercashMapper.delete(lstcashDelIds.get(i), buyVoucherEntity.getTenantid());
                this.buyVoucherMapper.updateCashAmount(delPojo.getCashaccid(), delPojo.getAmount(), tid);
            }
        }
        if (lstcash != null) {
            //循环每个item子表
            for (int i = 0; i < lstcash.size(); i++) {
                BuyVouchercashEntity buyVouchercashEntity = new BuyVouchercashEntity();
                if (lstcash.get(i).getId() == "" || lstcash.get(i).getId() == null) {
                    //初始化item的NULL
                    BuyVouchercashPojo cashPojo = this.buyVouchercashService.clearNull(lstcash.get(i));
                    BeanUtils.copyProperties(cashPojo, buyVouchercashEntity);
                    //设置id和Pid
                    buyVouchercashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    buyVouchercashEntity.setPid(buyVoucherEntity.getId());  // 主表 id
                    buyVouchercashEntity.setTenantid(tid);   // 租户id
                    buyVouchercashEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.buyVouchercashMapper.insert(buyVouchercashEntity);
                    this.buyVoucherMapper.updateCashAmount(buyVouchercashEntity.getCashaccid(), 0 - buyVouchercashEntity.getAmount(), tid);
                } else {
                    //初始化item的NULL
                    BuyVouchercashPojo cashPojo = this.buyVouchercashService.clearNull(lstcash.get(i));
                    BeanUtils.copyProperties(cashPojo, buyVouchercashEntity);
                    buyVouchercashEntity.setTenantid(tid);
                    BuyVouchercashPojo buyVouchercashPojo = this.buyVouchercashMapper.getEntity(buyVouchercashEntity.getId(), buyVoucherEntity.getTenantid());
                    this.buyVoucherMapper.updateCashAmount(buyVouchercashPojo.getCashaccid(), buyVouchercashPojo.getAmount(), tid);
                    this.buyVouchercashMapper.update(buyVouchercashEntity);
                    this.buyVoucherMapper.updateCashAmount(buyVouchercashEntity.getCashaccid(), 0 - buyVouchercashEntity.getAmount(), tid);
                }
            }
        }

        //返回Bill实例
        return this.getBillEntity(buyVoucherEntity.getId(), buyVoucherEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BuyVoucherPojo buyVoucherPojo = this.getBillEntity(key, tid);
        // 检查结账
        BuyAccountrecPojo busAccountrecPojo = this.buyAccountrecMapper.getEntityByMax(buyVoucherPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(buyVoucherPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getEnddate() + ",禁止删除结账前单据");
        }
        // 如果是红冲单 同步取消原始单据的红冲标志
        if (buyVoucherPojo.getBilltype().contains("红冲")) {
            this.buyVoucherMapper.updateOrgReturn(buyVoucherPojo.getOrguid(),"", tid);
        }
        //Item子表处理
        List<BuyVoucheritemPojo> lst = buyVoucherPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.buyVoucheritemMapper.delete(lst.get(i).getId(), tid);
                if (buyVoucherPojo.getBilltype().equals("采购付款")||buyVoucherPojo.getBilltype().equals("付款红冲")) {
                    // 同步采购发票,采购订单的收款,预收款,总收款等(主表Paid共收,FirstAmt预收,LastAmt收款,子表AvgFirstAmt, AvgLastAmt)
                    syncFirstAndLastAmt(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), tid);
//                    // 同步发票金额
//                    this.buyVoucherMapper.updateBuyInvoFinish(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), buyVoucherPojo.getTenantid());
                } else if (buyVoucherPojo.getBilltype().equals("单据付款")) {
                    this.buyVoucherMapper.updateFiniFinish(lst.get(i).getInvoid(), lst.get(i).getInvobillcode(), buyVoucherPojo.getTenantid());
                }
            }
        }

        //Cash子表处理
        List<BuyVouchercashPojo> lstcash = buyVoucherPojo.getCash();
        if (lstcash != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstcash.size(); i++) {
                BuyVouchercashPojo delPojo = this.buyVouchercashMapper.getEntity(lstcash.get(i).getId(), buyVoucherPojo.getTenantid());
                this.buyVouchercashMapper.delete(lstcash.get(i).getId(), tid);
                this.buyVoucherMapper.updateCashAmount(delPojo.getCashaccid(), delPojo.getAmount(), buyVoucherPojo.getTenantid());
            }
        }
        return this.buyVoucherMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param buyVoucherPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyVoucherPojo approval(BuyVoucherPojo buyVoucherPojo) {
        //主表更改
        BuyVoucherEntity buyVoucherEntity = new BuyVoucherEntity();
        BeanUtils.copyProperties(buyVoucherPojo, buyVoucherEntity);
        this.buyVoucherMapper.approval(buyVoucherEntity);
        //返回Bill实例
        return this.getBillEntity(buyVoucherEntity.getId(), buyVoucherEntity.getTenantid());
    }


    // 同步采购发票,采购订单的收款,预收款,总收款等(主表Paid共收,FirstAmt预收,LastAmt收款,子表AvgFirstAmt, AvgLastAmt)
    private void syncFirstAndLastAmt(String invobillid, String invobillcode, String tid) {
        // 1更新Buy_Invoice的Receipted共收,FirstAmt预收,LastAmt收款 (FirstAmt+LastAmt=Receipted)
        this.buyVoucherMapper.updateBuyInvoFinish(invobillid, invobillcode,tid);
        // 2更新Buy_InvoiceItem的AvgFirstAmt, AvgLastAmt
        this.buyVoucherMapper.updateBuyInvoItemAvgAmt(invobillid, invobillcode,tid);
        // 3更新关联销售订单Buy_OrderItem的AvgFirstAmt, AvgLastAmt
        this.buyVoucherMapper.updateBuyOrderItemAvgAmt(invobillid, invobillcode,tid);
        // 4更新关联销售订单Buy_Order的FirstAmt, LastAmt
        this.buyVoucherMapper.updateBuyOrderFirstLastAmt(invobillid, invobillcode,tid);
    }

}
