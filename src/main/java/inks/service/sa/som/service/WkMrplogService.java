package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.WkMrplogPojo;

import java.util.List;

/**
 * 操作日志(WkMrplog)表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-22 12:35:01
 */
public interface WkMrplogService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMrplogPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<WkMrplogPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkMrplogPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param wkMrplogPojo 实例对象
     * @return 实例对象
     */
    WkMrplogPojo insert(WkMrplogPojo wkMrplogPojo);

    /**
     * 修改数据
     *
     * @param wkMrplogpojo 实例对象
     * @return 实例对象
     */
    WkMrplogPojo update(WkMrplogPojo wkMrplogpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param wkMrplogpojo 实例对象
     * @return 实例对象
     */
    WkMrplogPojo clearNull(WkMrplogPojo wkMrplogpojo);
}
