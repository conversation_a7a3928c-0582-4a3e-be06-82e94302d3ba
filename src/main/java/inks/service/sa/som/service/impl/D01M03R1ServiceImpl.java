package inks.service.sa.som.service.impl;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.mapper.D01M03R1Mapper;
import inks.service.sa.som.service.D01M03R1Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 销售订单(BusMachining)报表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-13 10:19:46
 */
@Service("d01m03r1Service")
public class D01M03R1ServiceImpl implements D01M03R1Service {
    @Resource
    private D01M03R1Mapper d01M03R1Mapper;
     /*
      *
      * <AUTHOR>
      * @description 获取客户订单金额排名
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    public List<ChartPojo> getSumAmtByGroupMax(QueryParam queryParam) {
        try {
            List<ChartPojo> lst = d01M03R1Mapper.getSumAmtByGroupMax(queryParam);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
     /*
      *
      * <AUTHOR>
      * @description 订单逾期
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    @Override
    public ChartPojo getPageList(String tid) {
        try {
            return d01M03R1Mapper.getPageList(tid);
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }

    }
     /*
      *
      * <AUTHOR>
      * @description 热销产品
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    @Override
    public List<ChartPojo> getSumAmtByGoodsMax(QueryParam queryParam) {
        try {
            return d01M03R1Mapper.getSumAmtByGoodsMax(queryParam);
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
     /*
      *
      * <AUTHOR>
      * @description 业务员订单金额占比
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    @Override
    public List<ChartPojo> getSumAmtBySalesman(QueryParam queryParam) {
        try {
            return d01M03R1Mapper.getSumAmtBySalesman(queryParam);
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
