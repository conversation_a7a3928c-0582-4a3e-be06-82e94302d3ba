package inks.service.sa.som.service.impl;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatInvenoteEntity;
import inks.service.sa.som.domain.MatInvenoteitemEntity;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.MatInvenoteMapper;
import inks.service.sa.som.mapper.MatInvenoteitemMapper;
import inks.service.sa.som.mapper.MatInventoryMapper;
import inks.service.sa.som.service.MatAccessService;
import inks.service.sa.som.service.MatInvenoteService;
import inks.service.sa.som.service.MatInvenoteitemService;
import inks.sa.common.core.service.SaBillcodeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 仓库盘点(MatInvenote)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-14 19:24:21
 */
@Service("matInvenoteService")
public class MatInvenoteServiceImpl implements MatInvenoteService {


    @Resource
    private MatInvenoteMapper matInvenoteMapper;

    @Resource
    private MatInvenoteitemMapper matInvenoteitemMapper;
    @Resource
    private SaBillcodeService saBillcodeService;
    
    @Resource
    private MatInvenoteitemService matInvenoteitemService;

    @Resource
    private MatInventoryMapper matInventoryMapper;

  
  

  
    @Resource
    private MatAccessService matAccessService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatInvenotePojo getEntity(String key, String tid) {
        return this.matInvenoteMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatInvenoteitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatInvenoteitemdetailPojo> lst = matInvenoteMapper.getPageList(queryParam);
            PageInfo<MatInvenoteitemdetailPojo> pageInfo = new PageInfo<MatInvenoteitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatInvenotePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            MatInvenotePojo matInvenotePojo = this.matInvenoteMapper.getEntity(key, tid);
            //读取子表
            matInvenotePojo.setItem(matInvenoteitemMapper.getList(matInvenotePojo.getId(), matInvenotePojo.getTenantid()));
            return matInvenotePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatInvenotePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatInvenotePojo> lst = matInvenoteMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(matInvenoteitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<MatInvenotePojo> pageInfo = new PageInfo<MatInvenotePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatInvenotePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatInvenotePojo> lst = matInvenoteMapper.getPageTh(queryParam);
            PageInfo<MatInvenotePojo> pageInfo = new PageInfo<MatInvenotePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matInvenotePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatInvenotePojo insert(MatInvenotePojo matInvenotePojo) {
//初始化NULL字段
        if (matInvenotePojo.getRefno() == null) matInvenotePojo.setRefno("");
        if (matInvenotePojo.getBilldate() == null) matInvenotePojo.setBilldate(new Date());
        if (matInvenotePojo.getBilltype() == null) matInvenotePojo.setBilltype("");
        if (matInvenotePojo.getProjectid() == null) matInvenotePojo.setProjectid("");
        if (matInvenotePojo.getProjcode() == null) matInvenotePojo.setProjcode("");
        if (matInvenotePojo.getProjname() == null) matInvenotePojo.setProjname("");
        if (matInvenotePojo.getInveyear() == null) matInvenotePojo.setInveyear(0);
        if (matInvenotePojo.getInvemonth() == null) matInvenotePojo.setInvemonth(0);
        if (matInvenotePojo.getStartdate() == null) matInvenotePojo.setStartdate(new Date());
        if (matInvenotePojo.getEnddate() == null) matInvenotePojo.setEnddate(new Date());
        if (matInvenotePojo.getStoreid() == null) matInvenotePojo.setStoreid("");
        if (matInvenotePojo.getStorecode() == null) matInvenotePojo.setStorecode("");
        if (matInvenotePojo.getStorename() == null) matInvenotePojo.setStorename("");
        if (matInvenotePojo.getSummary() == null) matInvenotePojo.setSummary("");
        if (matInvenotePojo.getCreateby() == null) matInvenotePojo.setCreateby("");
        if (matInvenotePojo.getCreatebyid() == null) matInvenotePojo.setCreatebyid("");
        if (matInvenotePojo.getCreatedate() == null) matInvenotePojo.setCreatedate(new Date());
        if (matInvenotePojo.getLister() == null) matInvenotePojo.setLister("");
        if (matInvenotePojo.getListerid() == null) matInvenotePojo.setListerid("");
        if (matInvenotePojo.getModifydate() == null) matInvenotePojo.setModifydate(new Date());
        if (matInvenotePojo.getAssessor() == null) matInvenotePojo.setAssessor("");
        if (matInvenotePojo.getAssessorid() == null) matInvenotePojo.setAssessorid("");
        if (matInvenotePojo.getAssessdate() == null) matInvenotePojo.setAssessdate(new Date());
        if (matInvenotePojo.getStatecode() == null) matInvenotePojo.setStatecode("");
        if (matInvenotePojo.getStatedate() == null) matInvenotePojo.setStatedate(new Date());
        if (matInvenotePojo.getAcceuidin() == null) matInvenotePojo.setAcceuidin("");
        if (matInvenotePojo.getAcceuidout() == null) matInvenotePojo.setAcceuidout("");
        if (matInvenotePojo.getCustom1() == null) matInvenotePojo.setCustom1("");
        if (matInvenotePojo.getCustom2() == null) matInvenotePojo.setCustom2("");
        if (matInvenotePojo.getCustom3() == null) matInvenotePojo.setCustom3("");
        if (matInvenotePojo.getCustom4() == null) matInvenotePojo.setCustom4("");
        if (matInvenotePojo.getCustom5() == null) matInvenotePojo.setCustom5("");
        if (matInvenotePojo.getCustom6() == null) matInvenotePojo.setCustom6("");
        if (matInvenotePojo.getCustom7() == null) matInvenotePojo.setCustom7("");
        if (matInvenotePojo.getCustom8() == null) matInvenotePojo.setCustom8("");
        if (matInvenotePojo.getCustom9() == null) matInvenotePojo.setCustom9("");
        if (matInvenotePojo.getCustom10() == null) matInvenotePojo.setCustom10("");
        if (matInvenotePojo.getTenantid() == null) matInvenotePojo.setTenantid("");
        if (matInvenotePojo.getTenantidname() == null) matInvenotePojo.setTenantidname("");
        if (matInvenotePojo.getRevision() == null) matInvenotePojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        MatInvenoteEntity matInvenoteEntity = new MatInvenoteEntity();
        BeanUtils.copyProperties(matInvenotePojo, matInvenoteEntity);
        //设置id和新建日期
        matInvenoteEntity.setId(id);
        matInvenoteEntity.setRevision(1);  //乐观锁
        //插入主表
        this.matInvenoteMapper.insert(matInvenoteEntity);
        //Item子表处理
        List<MatInvenoteitemPojo> lst = matInvenotePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                MatInvenoteitemPojo itemPojo = this.matInvenoteitemService.clearNull(lst.get(i));
                MatInvenoteitemEntity matInvenoteitemEntity = new MatInvenoteitemEntity();
                BeanUtils.copyProperties(itemPojo, matInvenoteitemEntity);
                //设置id和Pid
                matInvenoteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matInvenoteitemEntity.setPid(id);
                matInvenoteitemEntity.setTenantid(matInvenotePojo.getTenantid());
                matInvenoteitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matInvenoteitemMapper.insert(matInvenoteitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(matInvenoteEntity.getId(), matInvenoteEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matInvenotePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatInvenotePojo update(MatInvenotePojo matInvenotePojo) {
        //主表更改
        MatInvenoteEntity matInvenoteEntity = new MatInvenoteEntity();
        BeanUtils.copyProperties(matInvenotePojo, matInvenoteEntity);
        this.matInvenoteMapper.update(matInvenoteEntity);
        if (matInvenotePojo.getItem() != null) {
            //Item子表处理
            List<MatInvenoteitemPojo> lst = matInvenotePojo.getItem();
//            //获取被删除的Item
//            List<String> lstDelIds = matInvenoteMapper.getDelItemIds(matInvenotePojo);
//            if (lstDelIds != null) {
//                //循环每个删除item子表
//                for (int i = 0; i < lstDelIds.size(); i++) {
//                    this.matInvenoteitemMapper.delete(lstDelIds.get(i), matInvenoteEntity.getTenantid());
//                }
//            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    MatInvenoteitemEntity matInvenoteitemEntity = new MatInvenoteitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        MatInvenoteitemPojo itemPojo = this.matInvenoteitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, matInvenoteitemEntity);
                        //设置id和Pid
                        matInvenoteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        matInvenoteitemEntity.setPid(matInvenoteEntity.getId());  // 主表 id
                        matInvenoteitemEntity.setTenantid(matInvenotePojo.getTenantid());   // 租户id
                        matInvenoteitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.matInvenoteitemMapper.insert(matInvenoteitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), matInvenoteitemEntity);
                        matInvenoteitemEntity.setTenantid(matInvenotePojo.getTenantid());
                        this.matInvenoteitemMapper.update(matInvenoteitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(matInvenoteEntity.getId(), matInvenoteEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        MatInvenotePojo matInvenotePojo = this.getBillEntity(key, tid);
        if (!"".equals(matInvenotePojo.getAcceuidin()) || !"".equals((matInvenotePojo.getAcceuidout()))) {
            throw new RuntimeException("盘点表已生成盈亏,禁止删除");
        }
        //Item子表处理
        List<MatInvenoteitemPojo> lst = matInvenotePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.matInvenoteitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.matInvenoteMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param matInvenotePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatInvenotePojo approval(MatInvenotePojo matInvenotePojo) {
        //主表更改
        MatInvenoteEntity matInvenoteEntity = new MatInvenoteEntity();
        BeanUtils.copyProperties(matInvenotePojo, matInvenoteEntity);
        this.matInvenoteMapper.approval(matInvenoteEntity);
        //返回Bill实例
        return this.getBillEntity(matInvenoteEntity.getId(), matInvenoteEntity.getTenantid());
    }


    /**
     * 新增数据
     *
     * @param matInvenotePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatInvenotePojo createByNote(MatInvenotePojo matInvenotePojo) {
        //初始化NULL字段
        if (matInvenotePojo.getRefno() == null) matInvenotePojo.setRefno("");
        if (matInvenotePojo.getBilldate() == null) matInvenotePojo.setBilldate(new Date());
        if (matInvenotePojo.getBilltype() == null) matInvenotePojo.setBilltype("");
        if (matInvenotePojo.getInveyear() == null) matInvenotePojo.setInveyear(0);
        if (matInvenotePojo.getInvemonth() == null) matInvenotePojo.setInvemonth(0);
        if (matInvenotePojo.getStartdate() == null) matInvenotePojo.setStartdate(new Date());
        if (matInvenotePojo.getEnddate() == null) matInvenotePojo.setEnddate(new Date());
        if (matInvenotePojo.getStoreid() == null) matInvenotePojo.setStoreid("");
        if (matInvenotePojo.getStorecode() == null) matInvenotePojo.setStorecode("");
        if (matInvenotePojo.getStorename() == null) matInvenotePojo.setStorename("");
        if (matInvenotePojo.getSummary() == null) matInvenotePojo.setSummary("");
        if (matInvenotePojo.getCreateby() == null) matInvenotePojo.setCreateby("");
        if (matInvenotePojo.getCreatebyid() == null) matInvenotePojo.setCreatebyid("");
        if (matInvenotePojo.getCreatedate() == null) matInvenotePojo.setCreatedate(new Date());
        if (matInvenotePojo.getLister() == null) matInvenotePojo.setLister("");
        if (matInvenotePojo.getListerid() == null) matInvenotePojo.setListerid("");
        if (matInvenotePojo.getModifydate() == null) matInvenotePojo.setModifydate(new Date());
        if (matInvenotePojo.getAssessor() == null) matInvenotePojo.setAssessor("");
        if (matInvenotePojo.getAssessorid() == null) matInvenotePojo.setAssessorid("");
        if (matInvenotePojo.getAssessdate() == null) matInvenotePojo.setAssessdate(new Date());
        if (matInvenotePojo.getStatecode() == null) matInvenotePojo.setStatecode("");
        if (matInvenotePojo.getStatedate() == null) matInvenotePojo.setStatedate(new Date());
        if (matInvenotePojo.getAcceuidin() == null) matInvenotePojo.setAcceuidin("");
        if (matInvenotePojo.getAcceuidout() == null) matInvenotePojo.setAcceuidout("");
        if (matInvenotePojo.getCustom1() == null) matInvenotePojo.setCustom1("");
        if (matInvenotePojo.getCustom2() == null) matInvenotePojo.setCustom2("");
        if (matInvenotePojo.getCustom3() == null) matInvenotePojo.setCustom3("");
        if (matInvenotePojo.getCustom4() == null) matInvenotePojo.setCustom4("");
        if (matInvenotePojo.getCustom5() == null) matInvenotePojo.setCustom5("");
        if (matInvenotePojo.getCustom6() == null) matInvenotePojo.setCustom6("");
        if (matInvenotePojo.getCustom7() == null) matInvenotePojo.setCustom7("");
        if (matInvenotePojo.getCustom8() == null) matInvenotePojo.setCustom8("");
        if (matInvenotePojo.getCustom9() == null) matInvenotePojo.setCustom9("");
        if (matInvenotePojo.getCustom10() == null) matInvenotePojo.setCustom10("");
        if (matInvenotePojo.getTenantid() == null) matInvenotePojo.setTenantid("");
        if (matInvenotePojo.getTenantidname() == null) matInvenotePojo.setTenantidname("");
        if (matInvenotePojo.getRevision() == null) matInvenotePojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        MatInvenoteEntity matInvenoteEntity = new MatInvenoteEntity();
        BeanUtils.copyProperties(matInvenotePojo, matInvenoteEntity);
        //设置id和新建日期
        matInvenoteEntity.setId(id);
        matInvenoteEntity.setRevision(1);  //乐观锁
        //插入主表
        this.matInvenoteMapper.insert(matInvenoteEntity);

        // 读取Mat_Inventory表 库存信息
        List<MatInventoryPojo> lst = matInventoryMapper.getOnlineListByStore(matInvenoteEntity.getStoreid(), matInvenoteEntity.getTenantid());

        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                MatInvenoteitemPojo itemPojo = new MatInvenoteitemPojo();
                BeanUtils.copyProperties(lst.get(i), itemPojo);
                itemPojo = this.matInvenoteitemService.clearNull(itemPojo);
                MatInvenoteitemEntity matInvenoteitemEntity = new MatInvenoteitemEntity();
                BeanUtils.copyProperties(itemPojo, matInvenoteitemEntity);
                matInvenoteitemEntity.setCurrqty(matInvenoteitemEntity.getQuantity());
                matInvenoteitemEntity.setCurramt(matInvenoteitemEntity.getAmount());
                matInvenoteitemEntity.setPrice(0D);
                if (lst.get(i).getAmount() != 0 && lst.get(i).getQuantity() != 0) {
                    matInvenoteitemEntity.setPrice(lst.get(i).getAmount() / lst.get(i).getQuantity());
                }
                //设置id和Pid
                matInvenoteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matInvenoteitemEntity.setPid(id);
                matInvenoteitemEntity.setTenantid(matInvenotePojo.getTenantid());
                matInvenoteitemEntity.setRevision(1);  //乐观锁
                //设置库存id
                matInvenoteitemEntity.setInveid(lst.get(i).getId());
                //插入子表
                this.matInvenoteitemMapper.insert(matInvenoteitemEntity);
            }
        }
        //返回实例
        return this.getEntity(matInvenoteEntity.getId(), matInvenoteEntity.getTenantid());
    }

    // 建立盘点出入库
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public String createAccess(String key, LoginUser loginUser) {
        MatInvenotePojo matInvenotePojo = getEntity(key, loginUser.getTenantid());
        if (!"".equals(matInvenotePojo.getAcceuidin()) || !"".equals((matInvenotePojo.getAcceuidout()))) {
            throw new RuntimeException("盘点表已生成盈亏,请勿重复生成");
        }
        String Refno = "";
        List<MatInvenoteitemPojo> lstitem = this.matInvenoteitemMapper.getListByAlter(key, loginUser.getTenantid());
        if (lstitem.size() == 0) {
            throw new RuntimeException("未找到可操作数据");
        }
        //生成单据编码
        Refno = saBillcodeService.getSerialNo("D04M01B9", loginUser.getTenantid(),"Mat_InveNote");


        List<MatAccessitemPojo> lstin = new ArrayList<>();
        List<MatAccessitemPojo> lstout = new ArrayList<>();
        for (MatInvenoteitemPojo pojo : lstitem) {
            MatAccessitemPojo accitem = new MatAccessitemPojo();
            accitem.setId(inksSnowflake.getSnowflake().nextIdStr());
            accitem.setGoodsid(pojo.getGoodsid());
            accitem.setGoodsuid(pojo.getGoodsuid());
            accitem.setLocation(pojo.getLocation());
            accitem.setBatchno(pojo.getBatchno());
            accitem.setSkuid(pojo.getSkuid());
            accitem.setPacksn(pojo.getPacksn());
            accitem.setInveid(pojo.getInveid());
            accitem.setTenantid(pojo.getTenantid());
            accitem.setCiteitemid(pojo.getId());
            accitem.setCiteuid(matInvenotePojo.getRefno());
            if (pojo.getCurrqty() > pojo.getQuantity()) {
                accitem.setQuantity(pojo.getCurrqty() - pojo.getQuantity());
                accitem.setAmount(pojo.getCurramt() - pojo.getAmount());
                accitem.setPrice(accitem.getAmount() / accitem.getQuantity());
                lstin.add(accitem);
            } else {
                accitem.setQuantity(pojo.getQuantity() - pojo.getCurrqty());
                accitem.setAmount(pojo.getAmount() - pojo.getCurramt());
                accitem.setPrice(accitem.getAmount() / accitem.getQuantity());
                lstout.add(accitem);
            }
        }


        MatAccessPojo matAccessPojo = new MatAccessPojo();
        matAccessPojo.setRefno(Refno);
        matAccessPojo.setStoreid(matInvenotePojo.getStoreid());
        matAccessPojo.setStorecode(matInvenotePojo.getStorecode());
        matAccessPojo.setStorename(matInvenotePojo.getStorename());
        matAccessPojo.setCreateby(loginUser.getRealname());   // 创建者
        matAccessPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        matAccessPojo.setCreatedate(new Date());   // 创建时间
        matAccessPojo.setLister(loginUser.getRealname());   // 制表
        matAccessPojo.setListerid(loginUser.getUserid());    // 制表id
        matAccessPojo.setModifydate(new Date());   //修改时间
        matAccessPojo.setTenantid(loginUser.getTenantid());   //租户id
        // 加入分页生成数 Eric 20230623
        int pageSize = 20;
        // 盘盈入库
        if (lstin.size() > 0) {
            matAccessPojo.setBilltype("盘盈入库");
            matAccessPojo.setDirection("入库单");
            do {
                List<MatAccessitemPojo> items = new ArrayList<>();
                for (int i = 0; i < pageSize; i++) {
                    if (lstin.size() == 0) break;
                    MatAccessitemPojo item = new MatAccessitemPojo();
                    BeanUtils.copyProperties(lstin.get(0), item);
                    items.add(item);
                    lstin.remove(lstin.get(0));
                }
                matAccessPojo.setItem(items);

                this.matAccessService.insert(matAccessPojo);
            } while (lstin.size() > 0);
            matInvenotePojo.setAcceuidin(Refno);
        }

        // 盘亏出库
        if (lstout.size() > 0) {
            MatAccessPojo matAccessoutPojo = new MatAccessPojo();
            BeanUtils.copyProperties(matAccessPojo, matAccessoutPojo);
            matAccessoutPojo.setBilltype("盘亏出库");
            matAccessoutPojo.setDirection("出库单");
            do {
                List<MatAccessitemPojo> items = new ArrayList<>();
                for (int i = 0; i < pageSize; i++) {
                    if (lstout.size() == 0) break;
                    MatAccessitemPojo item = new MatAccessitemPojo();
                    BeanUtils.copyProperties(lstout.get(0), item);
                    items.add(item);
                    lstout.remove(lstout.get(0));
                }
                matAccessoutPojo.setItem(items);
                this.matAccessService.insert(matAccessoutPojo);
            } while (lstout.size() > 0);
            matInvenotePojo.setAcceuidout(Refno);
        }
        // 更新盘点表状态；
        if (!"".equals(Refno)) {
            update(matInvenotePojo);
        }
        return Refno;
    }

    @Override
    public List<MatInvenoteitemPojo> getItemListByIds(String ids, String pid, String tenantid) {
        return this.matInvenoteitemMapper.getItemListByIds(ids, pid, tenantid);
    }
}
