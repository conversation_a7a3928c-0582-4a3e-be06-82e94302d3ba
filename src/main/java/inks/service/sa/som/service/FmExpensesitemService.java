package inks.service.sa.som.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmExpensesitemPojo;
import inks.service.sa.som.domain.FmExpensesitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 费用报销单明细表(FmExpensesitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-26 15:43:25
 */
public interface FmExpensesitemService {


    FmExpensesitemPojo getEntity(String key);

    PageInfo<FmExpensesitemPojo> getPageList(QueryParam queryParam);

    List<FmExpensesitemPojo> getList(String Pid);  

    FmExpensesitemPojo insert(FmExpensesitemPojo fmExpensesitemPojo);

    FmExpensesitemPojo update(FmExpensesitemPojo fmExpensesitempojo);

    int delete(String key);

    FmExpensesitemPojo clearNull(FmExpensesitemPojo fmExpensesitempojo);
}
