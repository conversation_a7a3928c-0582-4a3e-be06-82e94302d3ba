package inks.service.sa.som.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.pojo.BusDelieryitemdetailPojo;
import inks.service.sa.som.domain.pojo.BuyFinishingitemdetailPojo;
import inks.service.sa.som.mapper.D03M03R1Mapper;
import inks.service.sa.som.service.D03M03R1Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
public class D03M03R1Serviceimpl implements D03M03R1Service {
    @Resource
    private D03M03R1Mapper d03M03R1Mapper;
    @Override
    public List<ChartPojo> getSumAmtByYear(QueryParam queryParam) {
        try {
            //将 SearchPojo对象转换成json对象
            JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
            List<String> list = new ArrayList<>();
            //从SearchPojo 对象中取出时间
            list.add(resultStr.getString("StartDate")+"-01");
            list.add(resultStr.getString("StartDate")+"-02");
            list.add(resultStr.getString("StartDate")+"-03");
            list.add(resultStr.getString("StartDate")+"-04");
            list.add(resultStr.getString("StartDate")+"-05");
            list.add(resultStr.getString("StartDate")+"-06");
            list.add(resultStr.getString("StartDate")+"-07");
            list.add(resultStr.getString("StartDate")+"-08");
            list.add(resultStr.getString("StartDate")+"-09");
            list.add(resultStr.getString("StartDate")+"-10");
            list.add(resultStr.getString("StartDate")+"-11");
            list.add(resultStr.getString("StartDate")+"-12");
            //统计书
            Double sum = 0.0;
            List<ChartPojo> chartPojoList = d03M03R1Mapper.getSumAmtByYear(queryParam);
            List<ChartPojo> pojoList = new ArrayList<>();
            //循环月list
            for(int i=0;i< list.size();i++){
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(list.get(i));
                for(int j=0;j< chartPojoList.size();j++){
                    if(chartPojoList.get(j)!=null){
                        //判断如果月等于数据库返回月那么值就等于数据库返回值 否则为0
                        if(chartPojoList.get(j).getName().equals(list.get(i))){
                            //因为是趋势图 所以采用累加的形式
                            sum+=chartPojoList.get(j).getValue();
                            chartPojo.setValue(sum);
                            break;
                        }
                        else{
                            sum+=0.0;
                            chartPojo.setValue(sum);
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<ChartPojo> getSumAmtByMonth(QueryParam queryParam) {
        try {
            //将object对象转行成json对象
            JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
            List<String> dateList = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new SimpleDateFormat("yyyy-MM").parse(resultStr.getString("StartDate")));
            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");
            String nowDate = dateSdf.format(new Date());
            // 到下个月不在累计
            while (calendar.get(Calendar.MONTH) + 1 == Integer.parseInt(resultStr.getString("StartDate").split("-")[1])) {
                // 至本年月日,不在计算
                if (dateSdf.format(calendar.getTime()).equals(nowDate)) {
                    dateList.add(dateSdf.format(calendar.getTime()));
                    calendar.add(Calendar.DATE, 1);
                    break;
                }
                dateList.add(dateSdf.format(calendar.getTime()));
                calendar.add(Calendar.DATE, 1);
            }
            List<ChartPojo> chartPojoList = d03M03R1Mapper.getSumAmtByMonth(queryParam);
            Double sum = 0.0;
            List<ChartPojo> pojoList = new ArrayList<>();
            for(int i=0;i< dateList.size();i++){
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(dateList.get(i));
                for(int j=0;j< chartPojoList.size();j++){
                    if(chartPojoList.get(j)!=null){
                        if(chartPojoList.get(j).getName().equals(dateList.get(i))){
                            sum+=chartPojoList.get(j).getValue();
                            chartPojo.setValue(sum);
                            break;
                        }
                        else{
                            sum+=0.0;
                            chartPojo.setValue(sum);
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<ChartPojo> getSumAmtByDay(QueryParam queryParam) {
        try {
            List<String> list = new ArrayList<>();
            for(int i=7-1;i>=0;i--){
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - i);
                Date today = calendar.getTime();
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                list.add(format.format(today));
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            queryParam.getDateRange().setStartDate(simpleDateFormat.parse(list.get(0)));
            queryParam.getDateRange().setEndDate(simpleDateFormat.parse(list.get(list.size()-1)));
            List<ChartPojo> chartPojoList = d03M03R1Mapper.getSumAmtByDay(queryParam);
            Double sum = 0.0;
            List<ChartPojo> pojoList = new ArrayList<>();
            for(int i=0;i< list.size();i++){
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(list.get(i));
                for(int j=0;j< chartPojoList.size();j++){
                    if(chartPojoList.get(j)!=null){
                        if(chartPojoList.get(j).getName().equals(list.get(i))){
                            sum+=chartPojoList.get(j).getValue();
                            chartPojo.setValue(sum);
                            break;
                        }
                        else{
                            sum+=0.0;
                            chartPojo.setValue(sum);
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<ChartPojo> getSumAmtByYearMax(QueryParam queryParam) {
        try {
            //将 SearchPojo对象转换成json对象
            JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
            List<String> list = new ArrayList<>();
            //从SearchPojo 对象中取出时间
            list.add(resultStr.getString("StartDate")+"-01");
            list.add(resultStr.getString("StartDate")+"-02");
            list.add(resultStr.getString("StartDate")+"-03");
            list.add(resultStr.getString("StartDate")+"-04");
            list.add(resultStr.getString("StartDate")+"-05");
            list.add(resultStr.getString("StartDate")+"-06");
            list.add(resultStr.getString("StartDate")+"-07");
            list.add(resultStr.getString("StartDate")+"-08");
            list.add(resultStr.getString("StartDate")+"-09");
            list.add(resultStr.getString("StartDate")+"-10");
            list.add(resultStr.getString("StartDate")+"-11");
            list.add(resultStr.getString("StartDate")+"-12");
            List<ChartPojo> chartPojoList = d03M03R1Mapper.getSumAmtByYear(queryParam);
            List<ChartPojo> pojoList = new ArrayList<>();
            //循环月list
            for(int i=0;i< list.size();i++){
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(list.get(i));
                for(int j=0;j< chartPojoList.size();j++){
                    if(chartPojoList.get(j)!=null){
                        //判断如果月等于数据库返回月那么值就等于数据库返回值 否则为0
                        if(chartPojoList.get(j).getName().equals(list.get(i))){
                            //因为是趋势图 所以采用累加的形式
                            chartPojo.setValue(chartPojoList.get(j).getValue());
                            break;
                        }
                        else{
                            chartPojo.setValue(0.0);
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<ChartPojo> getSumAmtByMonthMax(QueryParam queryParam) {
        try {
            //将object对象转行成json对象
            JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
            List<String> dateList = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new SimpleDateFormat("yyyy-MM").parse(resultStr.getString("StartDate")));
            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");
            String nowDate = dateSdf.format(new Date());
            // 到下个月不在累计
            while (calendar.get(Calendar.MONTH) + 1 == Integer.parseInt(resultStr.getString("StartDate").split("-")[1])) {
                // 至本年月日,不在计算
                if (dateSdf.format(calendar.getTime()).equals(nowDate)) {
                    dateList.add(dateSdf.format(calendar.getTime()));
                    calendar.add(Calendar.DATE, 1);
                    break;
                }
                dateList.add(dateSdf.format(calendar.getTime()));
                calendar.add(Calendar.DATE, 1);
            }
            List<ChartPojo> chartPojoList = d03M03R1Mapper.getSumAmtByMonth(queryParam);
            List<ChartPojo> pojoList = new ArrayList<>();
            for(int i=0;i< dateList.size();i++){
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(dateList.get(i));
                for(int j=0;j< chartPojoList.size();j++){
                    if(chartPojoList.get(j)!=null){
                        if(chartPojoList.get(j).getName().equals(dateList.get(i))){
                            chartPojo.setValue(chartPojoList.get(j).getValue());
                            break;
                        }
                        else{
                            chartPojo.setValue(0.0);
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<ChartPojo> getSumAmtByDayMax(QueryParam queryParam) {
        try {
            List<String> list = new ArrayList<>();
            for(int i=7-1;i>=0;i--){
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - i);
                Date today = calendar.getTime();
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                list.add(format.format(today));
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            queryParam.getDateRange().setStartDate(simpleDateFormat.parse(list.get(0)));
            queryParam.getDateRange().setEndDate(simpleDateFormat.parse(list.get(list.size()-1)));
            List<ChartPojo> chartPojoList = d03M03R1Mapper.getSumAmtByDay(queryParam);
            List<ChartPojo> pojoList = new ArrayList<>();
            for (String s : list) {
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(s);
                for (ChartPojo pojo : chartPojoList) {
                    if (pojo != null) {
                        if (pojo.getName().equals(s)) {
                            chartPojo.setValue(pojo.getValue());
                            break;
                        } else {
                            chartPojo.setValue(0.0);
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<BuyFinishingitemdetailPojo> getSumPageListByGoods(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyFinishingitemdetailPojo> lst = this.d03M03R1Mapper.getSumPageListByGoods(queryParam);
            PageInfo<BuyFinishingitemdetailPojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<BuyFinishingitemdetailPojo> getSumPageListByGroup(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyFinishingitemdetailPojo> lst = this.d03M03R1Mapper.getSumPageListByGroup(queryParam);
            PageInfo<BuyFinishingitemdetailPojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
