package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyVoucheritemEntity;
import inks.service.sa.som.domain.pojo.BuyVoucheritemPojo;
import inks.service.sa.som.mapper.BuyVoucheritemMapper;
import inks.service.sa.som.service.BuyVoucheritemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 相关发票(BuyVoucheritem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-11 15:13:15
 */
@Service("buyVoucheritemService")
public class BuyVoucheritemServiceImpl implements BuyVoucheritemService {
    @Resource
    private BuyVoucheritemMapper buyVoucheritemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyVoucheritemPojo getEntity(String key, String tid) {
        return this.buyVoucheritemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyVoucheritemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyVoucheritemPojo> lst = buyVoucheritemMapper.getPageList(queryParam);
            PageInfo<BuyVoucheritemPojo> pageInfo = new PageInfo<BuyVoucheritemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BuyVoucheritemPojo> getList(String Pid, String tid) {
        try {
            List<BuyVoucheritemPojo> lst = buyVoucheritemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param buyVoucheritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyVoucheritemPojo insert(BuyVoucheritemPojo buyVoucheritemPojo) {
        //初始化item的NULL
        BuyVoucheritemPojo itempojo = this.clearNull(buyVoucheritemPojo);
        BuyVoucheritemEntity buyVoucheritemEntity = new BuyVoucheritemEntity();
        BeanUtils.copyProperties(itempojo, buyVoucheritemEntity);

        buyVoucheritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        buyVoucheritemEntity.setRevision(1);  //乐观锁
        this.buyVoucheritemMapper.insert(buyVoucheritemEntity);
        return this.getEntity(buyVoucheritemEntity.getId(), buyVoucheritemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param buyVoucheritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyVoucheritemPojo update(BuyVoucheritemPojo buyVoucheritemPojo) {
        BuyVoucheritemEntity buyVoucheritemEntity = new BuyVoucheritemEntity();
        BeanUtils.copyProperties(buyVoucheritemPojo, buyVoucheritemEntity);
        this.buyVoucheritemMapper.update(buyVoucheritemEntity);
        return this.getEntity(buyVoucheritemEntity.getId(), buyVoucheritemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.buyVoucheritemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param buyVoucheritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyVoucheritemPojo clearNull(BuyVoucheritemPojo buyVoucheritemPojo) {
        //初始化NULL字段
        if (buyVoucheritemPojo.getPid() == null) buyVoucheritemPojo.setPid("");
        if (buyVoucheritemPojo.getInvoid() == null) buyVoucheritemPojo.setInvoid("");
        if (buyVoucheritemPojo.getInvobillcode() == null) buyVoucheritemPojo.setInvobillcode("");
        if (buyVoucheritemPojo.getInvocode() == null) buyVoucheritemPojo.setInvocode("");
        if (buyVoucheritemPojo.getInvoamount() == null) buyVoucheritemPojo.setInvoamount(0D);
        if (buyVoucheritemPojo.getAmount() == null) buyVoucheritemPojo.setAmount(0D);
        if (buyVoucheritemPojo.getRownum() == null) buyVoucheritemPojo.setRownum(0);
        if (buyVoucheritemPojo.getRemark() == null) buyVoucheritemPojo.setRemark("");
        if (buyVoucheritemPojo.getCustom1() == null) buyVoucheritemPojo.setCustom1("");
        if (buyVoucheritemPojo.getCustom2() == null) buyVoucheritemPojo.setCustom2("");
        if (buyVoucheritemPojo.getCustom3() == null) buyVoucheritemPojo.setCustom3("");
        if (buyVoucheritemPojo.getCustom4() == null) buyVoucheritemPojo.setCustom4("");
        if (buyVoucheritemPojo.getCustom5() == null) buyVoucheritemPojo.setCustom5("");
        if (buyVoucheritemPojo.getCustom6() == null) buyVoucheritemPojo.setCustom6("");
        if (buyVoucheritemPojo.getCustom7() == null) buyVoucheritemPojo.setCustom7("");
        if (buyVoucheritemPojo.getCustom8() == null) buyVoucheritemPojo.setCustom8("");
        if (buyVoucheritemPojo.getCustom9() == null) buyVoucheritemPojo.setCustom9("");
        if (buyVoucheritemPojo.getCustom10() == null) buyVoucheritemPojo.setCustom10("");
        if (buyVoucheritemPojo.getTenantid() == null) buyVoucheritemPojo.setTenantid("");
        if (buyVoucheritemPojo.getRevision() == null) buyVoucheritemPojo.setRevision(0);
        return buyVoucheritemPojo;
    }
}
