package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyPlanmergeEntity;
import inks.service.sa.som.domain.pojo.BuyPlanmergePojo;
import inks.service.sa.som.mapper.BuyPlanmergeMapper;
import inks.service.sa.som.service.BuyPlanmergeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 合并项目(BuyPlanmerge)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-18 12:58:12
 */
@Service("buyPlanmergeService")
public class BuyPlanmergeServiceImpl implements BuyPlanmergeService {
    @Resource
    private BuyPlanmergeMapper buyPlanmergeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyPlanmergePojo getEntity(String key, String tid) {
        return this.buyPlanmergeMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyPlanmergePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyPlanmergePojo> lst = buyPlanmergeMapper.getPageList(queryParam);
            PageInfo<BuyPlanmergePojo> pageInfo = new PageInfo<BuyPlanmergePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BuyPlanmergePojo> getList(String Pid, String tid) {
        try {
            List<BuyPlanmergePojo> lst = buyPlanmergeMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param buyPlanmergePojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyPlanmergePojo insert(BuyPlanmergePojo buyPlanmergePojo) {
        //初始化item的NULL
        BuyPlanmergePojo itempojo = this.clearNull(buyPlanmergePojo);
        BuyPlanmergeEntity buyPlanmergeEntity = new BuyPlanmergeEntity();
        BeanUtils.copyProperties(itempojo, buyPlanmergeEntity);
        //生成雪花id
        buyPlanmergeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        buyPlanmergeEntity.setRevision(1);  //乐观锁
        this.buyPlanmergeMapper.insert(buyPlanmergeEntity);
        return this.getEntity(buyPlanmergeEntity.getId(), buyPlanmergeEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param buyPlanmergePojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyPlanmergePojo update(BuyPlanmergePojo buyPlanmergePojo) {
        BuyPlanmergeEntity buyPlanmergeEntity = new BuyPlanmergeEntity();
        BeanUtils.copyProperties(buyPlanmergePojo, buyPlanmergeEntity);
        this.buyPlanmergeMapper.update(buyPlanmergeEntity);
        return this.getEntity(buyPlanmergeEntity.getId(), buyPlanmergeEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.buyPlanmergeMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param buyPlanmergePojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyPlanmergePojo clearNull(BuyPlanmergePojo buyPlanmergePojo) {
        //初始化NULL字段
        if (buyPlanmergePojo.getPid() == null) buyPlanmergePojo.setPid("");
        if (buyPlanmergePojo.getItemtype() == null) buyPlanmergePojo.setItemtype("");
        if (buyPlanmergePojo.getGoodsid() == null) buyPlanmergePojo.setGoodsid("");
        if (buyPlanmergePojo.getItemcode() == null) buyPlanmergePojo.setItemcode("");
        if (buyPlanmergePojo.getItemname() == null) buyPlanmergePojo.setItemname("");
        if (buyPlanmergePojo.getItemspec() == null) buyPlanmergePojo.setItemspec("");
        if (buyPlanmergePojo.getItemunit() == null) buyPlanmergePojo.setItemunit("");
        if (buyPlanmergePojo.getQuantity() == null) buyPlanmergePojo.setQuantity(0D);
        if (buyPlanmergePojo.getTaxprice() == null) buyPlanmergePojo.setTaxprice(0D);
        if (buyPlanmergePojo.getTaxamount() == null) buyPlanmergePojo.setTaxamount(0D);
        if (buyPlanmergePojo.getTaxtotal() == null) buyPlanmergePojo.setTaxtotal(0D);
        if (buyPlanmergePojo.getItemtaxrate() == null) buyPlanmergePojo.setItemtaxrate(0);
        if (buyPlanmergePojo.getPrice() == null) buyPlanmergePojo.setPrice(0D);
        if (buyPlanmergePojo.getAmount() == null) buyPlanmergePojo.setAmount(0D);
        if (buyPlanmergePojo.getPlandate() == null) buyPlanmergePojo.setPlandate(new Date());
        if (buyPlanmergePojo.getGroupid() == null) buyPlanmergePojo.setGroupid("");
        if (buyPlanmergePojo.getRemark() == null) buyPlanmergePojo.setRemark("");
        if (buyPlanmergePojo.getStatecode() == null) buyPlanmergePojo.setStatecode("");
        if (buyPlanmergePojo.getStatedate() == null) buyPlanmergePojo.setStatedate(new Date());
        if (buyPlanmergePojo.getClosed() == null) buyPlanmergePojo.setClosed(0);
        if (buyPlanmergePojo.getBuyqty() == null) buyPlanmergePojo.setBuyqty(0D);
        if (buyPlanmergePojo.getFinishqty() == null) buyPlanmergePojo.setFinishqty(0D);
        if (buyPlanmergePojo.getRownum() == null) buyPlanmergePojo.setRownum(0);
        if (buyPlanmergePojo.getCiteuid() == null) buyPlanmergePojo.setCiteuid("");
        if (buyPlanmergePojo.getCiteitemid() == null) buyPlanmergePojo.setCiteitemid("");
        if (buyPlanmergePojo.getMachuid() == null) buyPlanmergePojo.setMachuid("");
        if (buyPlanmergePojo.getMachitemid() == null) buyPlanmergePojo.setMachitemid("");
        if (buyPlanmergePojo.getMachgroupid() == null) buyPlanmergePojo.setMachgroupid("");
        if (buyPlanmergePojo.getMainplanuid() == null) buyPlanmergePojo.setMainplanuid("");
        if (buyPlanmergePojo.getMainplanitemid() == null) buyPlanmergePojo.setMainplanitemid("");
        if (buyPlanmergePojo.getMrpuid() == null) buyPlanmergePojo.setMrpuid("");
        if (buyPlanmergePojo.getCustomer() == null) buyPlanmergePojo.setCustomer("");
        if (buyPlanmergePojo.getCustpo() == null) buyPlanmergePojo.setCustpo("");
        if (buyPlanmergePojo.getBatchno() == null) buyPlanmergePojo.setBatchno("");
        if (buyPlanmergePojo.getAttributejson() == null) buyPlanmergePojo.setAttributejson("");
        if (buyPlanmergePojo.getSourcetype() == null) buyPlanmergePojo.setSourcetype(0);
        if (buyPlanmergePojo.getItemcount() == null) buyPlanmergePojo.setItemcount(0);
        if (buyPlanmergePojo.getCustom1() == null) buyPlanmergePojo.setCustom1("");
        if (buyPlanmergePojo.getCustom2() == null) buyPlanmergePojo.setCustom2("");
        if (buyPlanmergePojo.getCustom3() == null) buyPlanmergePojo.setCustom3("");
        if (buyPlanmergePojo.getCustom4() == null) buyPlanmergePojo.setCustom4("");
        if (buyPlanmergePojo.getCustom5() == null) buyPlanmergePojo.setCustom5("");
        if (buyPlanmergePojo.getCustom6() == null) buyPlanmergePojo.setCustom6("");
        if (buyPlanmergePojo.getCustom7() == null) buyPlanmergePojo.setCustom7("");
        if (buyPlanmergePojo.getCustom8() == null) buyPlanmergePojo.setCustom8("");
        if (buyPlanmergePojo.getCustom9() == null) buyPlanmergePojo.setCustom9("");
        if (buyPlanmergePojo.getCustom10() == null) buyPlanmergePojo.setCustom10("");
        if (buyPlanmergePojo.getTenantid() == null) buyPlanmergePojo.setTenantid("");
        if (buyPlanmergePojo.getRevision() == null) buyPlanmergePojo.setRevision(0);
        return buyPlanmergePojo;
    }
}
