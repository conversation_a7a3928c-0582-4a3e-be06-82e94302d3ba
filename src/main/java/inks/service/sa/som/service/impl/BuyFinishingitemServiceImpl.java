package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyFinishingitemEntity;
import inks.service.sa.som.domain.pojo.BuyFinishingitemPojo;
import inks.service.sa.som.mapper.BuyFinishingitemMapper;
import inks.service.sa.som.service.BuyFinishingitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 采购验收项目(BuyFinishingitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 20:34:36
 */
@Service("buyFinishingitemService")
public class BuyFinishingitemServiceImpl implements BuyFinishingitemService {
    @Resource
    private BuyFinishingitemMapper buyFinishingitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyFinishingitemPojo getEntity(String key, String tid) {
        return this.buyFinishingitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyFinishingitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyFinishingitemPojo> lst = buyFinishingitemMapper.getPageList(queryParam);
            PageInfo<BuyFinishingitemPojo> pageInfo = new PageInfo<BuyFinishingitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BuyFinishingitemPojo> getList(String Pid, String tid) {
        try {
            List<BuyFinishingitemPojo> lst = buyFinishingitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param buyFinishingitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyFinishingitemPojo insert(BuyFinishingitemPojo buyFinishingitemPojo) {
        //初始化item的NULL
        BuyFinishingitemPojo itempojo = this.clearNull(buyFinishingitemPojo);
        BuyFinishingitemEntity buyFinishingitemEntity = new BuyFinishingitemEntity();
        BeanUtils.copyProperties(itempojo, buyFinishingitemEntity);

        buyFinishingitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        buyFinishingitemEntity.setRevision(1);  //乐观锁
        this.buyFinishingitemMapper.insert(buyFinishingitemEntity);
        return this.getEntity(buyFinishingitemEntity.getId(), buyFinishingitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param buyFinishingitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyFinishingitemPojo update(BuyFinishingitemPojo buyFinishingitemPojo) {
        BuyFinishingitemEntity buyFinishingitemEntity = new BuyFinishingitemEntity();
        BeanUtils.copyProperties(buyFinishingitemPojo, buyFinishingitemEntity);
        this.buyFinishingitemMapper.update(buyFinishingitemEntity);
        return this.getEntity(buyFinishingitemEntity.getId(), buyFinishingitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.buyFinishingitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param buyFinishingitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyFinishingitemPojo clearNull(BuyFinishingitemPojo buyFinishingitemPojo) {
        //初始化NULL字段
        if (buyFinishingitemPojo.getPid() == null) buyFinishingitemPojo.setPid("");
        if (buyFinishingitemPojo.getGoodsid() == null) buyFinishingitemPojo.setGoodsid("");
        if (buyFinishingitemPojo.getItemcode() == null) buyFinishingitemPojo.setItemcode("");
        if (buyFinishingitemPojo.getItemname() == null) buyFinishingitemPojo.setItemname("");
        if (buyFinishingitemPojo.getItemspec() == null) buyFinishingitemPojo.setItemspec("");
        if (buyFinishingitemPojo.getItemunit() == null) buyFinishingitemPojo.setItemunit("");
        if (buyFinishingitemPojo.getQuantity() == null) buyFinishingitemPojo.setQuantity(0D);
        if (buyFinishingitemPojo.getTaxprice() == null) buyFinishingitemPojo.setTaxprice(0D);
        if (buyFinishingitemPojo.getTaxamount() == null) buyFinishingitemPojo.setTaxamount(0D);
        if (buyFinishingitemPojo.getPrice() == null) buyFinishingitemPojo.setPrice(0D);
        if (buyFinishingitemPojo.getAmount() == null) buyFinishingitemPojo.setAmount(0D);
        if (buyFinishingitemPojo.getTaxtotal() == null) buyFinishingitemPojo.setTaxtotal(0D);
        if (buyFinishingitemPojo.getItemtaxrate() == null) buyFinishingitemPojo.setItemtaxrate(0);
        if (buyFinishingitemPojo.getRemark() == null) buyFinishingitemPojo.setRemark("");
        if (buyFinishingitemPojo.getOrderno() == null) buyFinishingitemPojo.setOrderno("");
        if (buyFinishingitemPojo.getOrderuid() == null) buyFinishingitemPojo.setOrderuid("");
        if (buyFinishingitemPojo.getOrderitemid() == null) buyFinishingitemPojo.setOrderitemid("");
        if (buyFinishingitemPojo.getStatecode() == null) buyFinishingitemPojo.setStatecode("");
        if (buyFinishingitemPojo.getStatedate() == null) buyFinishingitemPojo.setStatedate(new Date());
        if (buyFinishingitemPojo.getInspectid() == null) buyFinishingitemPojo.setInspectid("");
        if (buyFinishingitemPojo.getInspectuid() == null) buyFinishingitemPojo.setInspectuid("");
        if (buyFinishingitemPojo.getPassedqty() == null) buyFinishingitemPojo.setPassedqty(0D);
        if (buyFinishingitemPojo.getFinishqty() == null) buyFinishingitemPojo.setFinishqty(0D);
        if (buyFinishingitemPojo.getClosed() == null) buyFinishingitemPojo.setClosed(0);
        if (buyFinishingitemPojo.getRownum() == null) buyFinishingitemPojo.setRownum(0);
        if (buyFinishingitemPojo.getInvoqty() == null) buyFinishingitemPojo.setInvoqty(0D);
        if (buyFinishingitemPojo.getInvoclosed() == null) buyFinishingitemPojo.setInvoclosed(0);
        if (buyFinishingitemPojo.getVirtualitem() == null) buyFinishingitemPojo.setVirtualitem(0);
        if (buyFinishingitemPojo.getCustomer() == null) buyFinishingitemPojo.setCustomer("");
        if (buyFinishingitemPojo.getCustpo() == null) buyFinishingitemPojo.setCustpo("");
        if (buyFinishingitemPojo.getLocation() == null) buyFinishingitemPojo.setLocation("");
        if (buyFinishingitemPojo.getBatchno() == null) buyFinishingitemPojo.setBatchno("");
        if (buyFinishingitemPojo.getMachuid() == null) buyFinishingitemPojo.setMachuid("");
        if (buyFinishingitemPojo.getMachitemid() == null) buyFinishingitemPojo.setMachitemid("");
        if (buyFinishingitemPojo.getMachbatch() == null) buyFinishingitemPojo.setMachbatch("");
        if (buyFinishingitemPojo.getMachgroupid() == null) buyFinishingitemPojo.setMachgroupid("");
        if (buyFinishingitemPojo.getMainplanuid() == null) buyFinishingitemPojo.setMainplanuid("");
        if (buyFinishingitemPojo.getMainplanitemid() == null) buyFinishingitemPojo.setMainplanitemid("");
        if (buyFinishingitemPojo.getMrpuid() == null) buyFinishingitemPojo.setMrpuid("");
     if(buyFinishingitemPojo.getMrpitemid()==null) buyFinishingitemPojo.setMrpitemid("");
     if(buyFinishingitemPojo.getMrpobjgoodsid()==null) buyFinishingitemPojo.setMrpobjgoodsid("");
        if (buyFinishingitemPojo.getDeliqty() == null) buyFinishingitemPojo.setDeliqty(0D);
        if (buyFinishingitemPojo.getDisannulmark() == null) buyFinishingitemPojo.setDisannulmark(0);
        if (buyFinishingitemPojo.getDisannullisterid() == null) buyFinishingitemPojo.setDisannullisterid("");
        if (buyFinishingitemPojo.getDisannullister() == null) buyFinishingitemPojo.setDisannullister("");
        if (buyFinishingitemPojo.getDisannuldate() == null) buyFinishingitemPojo.setDisannuldate(new Date());
        if (buyFinishingitemPojo.getAttributejson() == null) buyFinishingitemPojo.setAttributejson("");
        if (buyFinishingitemPojo.getSourcetype() == null) buyFinishingitemPojo.setSourcetype(0);
        if (buyFinishingitemPojo.getCustom1() == null) buyFinishingitemPojo.setCustom1("");
        if (buyFinishingitemPojo.getCustom2() == null) buyFinishingitemPojo.setCustom2("");
        if (buyFinishingitemPojo.getCustom3() == null) buyFinishingitemPojo.setCustom3("");
        if (buyFinishingitemPojo.getCustom4() == null) buyFinishingitemPojo.setCustom4("");
        if (buyFinishingitemPojo.getCustom5() == null) buyFinishingitemPojo.setCustom5("");
        if (buyFinishingitemPojo.getCustom6() == null) buyFinishingitemPojo.setCustom6("");
        if (buyFinishingitemPojo.getCustom7() == null) buyFinishingitemPojo.setCustom7("");
        if (buyFinishingitemPojo.getCustom8() == null) buyFinishingitemPojo.setCustom8("");
        if (buyFinishingitemPojo.getCustom9() == null) buyFinishingitemPojo.setCustom9("");
        if (buyFinishingitemPojo.getCustom10() == null) buyFinishingitemPojo.setCustom10("");
        if (buyFinishingitemPojo.getCustom11() == null) buyFinishingitemPojo.setCustom11("");
        if (buyFinishingitemPojo.getCustom12() == null) buyFinishingitemPojo.setCustom12("");
        if (buyFinishingitemPojo.getCustom13() == null) buyFinishingitemPojo.setCustom13("");
        if (buyFinishingitemPojo.getCustom14() == null) buyFinishingitemPojo.setCustom14("");
        if (buyFinishingitemPojo.getCustom15() == null) buyFinishingitemPojo.setCustom15("");
        if (buyFinishingitemPojo.getCustom16() == null) buyFinishingitemPojo.setCustom16("");
        if (buyFinishingitemPojo.getCustom17() == null) buyFinishingitemPojo.setCustom17("");
        if (buyFinishingitemPojo.getCustom18() == null) buyFinishingitemPojo.setCustom18("");
        if (buyFinishingitemPojo.getTenantid() == null) buyFinishingitemPojo.setTenantid("");
        if (buyFinishingitemPojo.getRevision() == null) buyFinishingitemPojo.setRevision(0);
        return buyFinishingitemPojo;
    }
}
