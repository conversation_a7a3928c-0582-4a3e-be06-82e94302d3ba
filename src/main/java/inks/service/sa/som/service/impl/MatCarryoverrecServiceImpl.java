package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatCarryoverrecEntity;
import inks.service.sa.som.domain.pojo.MatCarryoverrecPojo;
import inks.service.sa.som.mapper.MatCarryoverMapper;
import inks.service.sa.som.mapper.MatCarryoverrecMapper;
import inks.service.sa.som.service.MatCarryoverrecService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 仓库结账(MatCarryoverrec)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-05 12:50:55
 */
@Service("matCarryoverrecService")
public class MatCarryoverrecServiceImpl implements MatCarryoverrecService {

    @Resource
    private MatCarryoverrecMapper matCarryoverrecMapper;

    @Resource
    private MatCarryoverMapper matCarryoverMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatCarryoverrecPojo getEntity(String key, String tid) {
        return this.matCarryoverrecMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatCarryoverrecPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatCarryoverrecPojo> lst = matCarryoverrecMapper.getPageList(queryParam);
            PageInfo<MatCarryoverrecPojo> pageInfo = new PageInfo<MatCarryoverrecPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param matCarryoverrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatCarryoverrecPojo insert(MatCarryoverrecPojo matCarryoverrecPojo) {
    //初始化NULL字段
     if(matCarryoverrecPojo.getCarryyear()==null) matCarryoverrecPojo.setCarryyear(0);
     if(matCarryoverrecPojo.getCarrymonth()==null) matCarryoverrecPojo.setCarrymonth(0);
     if(matCarryoverrecPojo.getStartdate()==null) matCarryoverrecPojo.setStartdate(new Date());
     if(matCarryoverrecPojo.getEnddate()==null) matCarryoverrecPojo.setEnddate(new Date());
     if(matCarryoverrecPojo.getOperator()==null) matCarryoverrecPojo.setOperator("");
     if(matCarryoverrecPojo.getOperatorid()==null) matCarryoverrecPojo.setOperatorid("");
     if(matCarryoverrecPojo.getRownum()==null) matCarryoverrecPojo.setRownum(0);
     if(matCarryoverrecPojo.getRemark()==null) matCarryoverrecPojo.setRemark("");
     if(matCarryoverrecPojo.getCreateby()==null) matCarryoverrecPojo.setCreateby("");
     if(matCarryoverrecPojo.getCreatebyid()==null) matCarryoverrecPojo.setCreatebyid("");
     if(matCarryoverrecPojo.getCreatedate()==null) matCarryoverrecPojo.setCreatedate(new Date());
     if(matCarryoverrecPojo.getLister()==null) matCarryoverrecPojo.setLister("");
     if(matCarryoverrecPojo.getListerid()==null) matCarryoverrecPojo.setListerid("");
     if(matCarryoverrecPojo.getModifydate()==null) matCarryoverrecPojo.setModifydate(new Date());
     if(matCarryoverrecPojo.getBillopenamount()==null) matCarryoverrecPojo.setBillopenamount(0D);
     if(matCarryoverrecPojo.getBillinamount()==null) matCarryoverrecPojo.setBillinamount(0D);
     if(matCarryoverrecPojo.getBilloutamount()==null) matCarryoverrecPojo.setBilloutamount(0D);
     if(matCarryoverrecPojo.getBillcloseamount()==null) matCarryoverrecPojo.setBillcloseamount(0D);
     if(matCarryoverrecPojo.getPrintcount()==null) matCarryoverrecPojo.setPrintcount(0);
     if(matCarryoverrecPojo.getCustom1()==null) matCarryoverrecPojo.setCustom1("");
     if(matCarryoverrecPojo.getCustom2()==null) matCarryoverrecPojo.setCustom2("");
     if(matCarryoverrecPojo.getCustom3()==null) matCarryoverrecPojo.setCustom3("");
     if(matCarryoverrecPojo.getCustom4()==null) matCarryoverrecPojo.setCustom4("");
     if(matCarryoverrecPojo.getCustom5()==null) matCarryoverrecPojo.setCustom5("");
     if(matCarryoverrecPojo.getCustom6()==null) matCarryoverrecPojo.setCustom6("");
     if(matCarryoverrecPojo.getCustom7()==null) matCarryoverrecPojo.setCustom7("");
     if(matCarryoverrecPojo.getCustom8()==null) matCarryoverrecPojo.setCustom8("");
     if(matCarryoverrecPojo.getCustom9()==null) matCarryoverrecPojo.setCustom9("");
     if(matCarryoverrecPojo.getCustom10()==null) matCarryoverrecPojo.setCustom10("");
     if(matCarryoverrecPojo.getTenantid()==null) matCarryoverrecPojo.setTenantid("");
     if(matCarryoverrecPojo.getTenantname()==null) matCarryoverrecPojo.setTenantname("");
     if(matCarryoverrecPojo.getRevision()==null) matCarryoverrecPojo.setRevision(0);
        MatCarryoverrecEntity matCarryoverrecEntity = new MatCarryoverrecEntity(); 
        BeanUtils.copyProperties(matCarryoverrecPojo,matCarryoverrecEntity);
        
          matCarryoverrecEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          matCarryoverrecEntity.setRevision(1);  //乐观锁
          this.matCarryoverrecMapper.insert(matCarryoverrecEntity);
        return this.getEntity(matCarryoverrecEntity.getId(),matCarryoverrecEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param matCarryoverrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatCarryoverrecPojo update(MatCarryoverrecPojo matCarryoverrecPojo) {
        MatCarryoverrecEntity matCarryoverrecEntity = new MatCarryoverrecEntity(); 
        BeanUtils.copyProperties(matCarryoverrecPojo,matCarryoverrecEntity);
        this.matCarryoverrecMapper.update(matCarryoverrecEntity);
        return this.getEntity(matCarryoverrecEntity.getId(),matCarryoverrecEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        MatCarryoverrecPojo delPojo= this.matCarryoverrecMapper.getEntity(key,tid);
        this.matCarryoverMapper.deleteByMonth(delPojo.getCarryyear(),delPojo.getCarrymonth(),tid);
        return this.matCarryoverrecMapper.delete(key,tid) ;
    }

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public MatCarryoverrecPojo getEntityByMax(String tid) {
        return this.matCarryoverrecMapper.getEntityByMax(tid);
    }

}
