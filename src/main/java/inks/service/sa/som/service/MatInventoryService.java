package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatGoodsInveQtyPojo;
import inks.service.sa.som.domain.pojo.MatGoodsPojo;
import inks.service.sa.som.domain.pojo.MatInventoryPojo;
import inks.service.sa.som.domain.pojo.MatInventoryQtyPojo;

import java.util.List;
import java.util.Map;

/**
 * 库存信息(MatInventory)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-12 14:23:43
 */
public interface MatInventoryService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatInventoryPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatInventoryPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matInventoryPojo 实例对象
     * @return 实例对象
     */
    MatInventoryPojo insert(MatInventoryPojo matInventoryPojo);

    /**
     * 修改数据
     *
     * @param matInventorypojo 实例对象
     * @return 实例对象
     */
    MatInventoryPojo update(MatInventoryPojo matInventorypojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 刷新货品总仓库数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateGoodsIvQty(String key, String tid);


    //获取EntityBySN
    MatInventoryPojo getEntityBySn(MatInventoryPojo matInventorypojo);

    MatInventoryPojo getEntityBySnNoSku(MatInventoryPojo matInventoryPojo);
    //获取EntityBySN
    MatInventoryPojo getEntityBySku(MatInventoryPojo matInventorypojo);

    //获取EntityBy批号
    MatInventoryPojo getEntityByBatch(MatInventoryPojo matInventorypojo);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatInventoryQtyPojo> getQtyPageList(QueryParam queryParam);


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatGoodsInveQtyPojo> getQtyPageListByGoods(QueryParam queryParam);


    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    List<MatInventoryPojo> getListByGoods(String key, String tid);


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatGoodsInveQtyPojo> getMachMatQtyPageListByGoods(QueryParam queryParam, String itemid);

    //按货品名称\规格\外部编码查询货品信息
    MatInventoryPojo getEntityByNameSpecPart(String name, String goodsspec, String partid, String tid);

    MatInventoryPojo getEntityByStoreGoods(String key, String storeid, String tid);

    PageInfo<MatGoodsPojo> getMatShiftPageList(QueryParam queryParam);

    PageInfo<MatGoodsPojo> getMatShiftPageListB(QueryParam queryParam);

    PageInfo<Map<String,Object>> getSumPageListByGoods(QueryParam queryParam);

    MatInventoryPojo getEntityByIf(MatInventoryPojo matInventoryPojo);

}
