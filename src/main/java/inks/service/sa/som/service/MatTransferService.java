package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatTransferEntity;
import inks.service.sa.som.domain.pojo.MatTransferPojo;
import inks.service.sa.som.domain.pojo.MatTransferitemdetailPojo;

/**
 * 调拨单据(MatTransfer)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-13 20:50:26
 */
public interface MatTransferService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatTransferPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatTransferitemdetailPojo> getPageList(QueryParam queryParam);

 /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatTransferPojo getBillEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatTransferPojo> getBillList(QueryParam queryParam);
    
        /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatTransferPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param matTransferPojo 实例对象
     * @return 实例对象
     */
    MatTransferPojo insert(MatTransferPojo matTransferPojo);

    /**
     * 修改数据
     *
     * @param matTransferpojo 实例对象
     * @return 实例对象
     */
    MatTransferPojo update(MatTransferPojo matTransferpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    MatTransferPojo insertRed(MatTransferPojo matTransferPojo, MatTransferEntity matTransferEntityOrg);
}
