package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.sa.som.domain.MatGoodsEntity;
import inks.service.sa.som.domain.pojo.MatGoodsBatchAttrPojo;
import inks.service.sa.som.domain.pojo.MatGoodsPojo;
import inks.service.sa.som.mapper.MatGoodsMapper;
import inks.service.sa.som.service.MatGoodsService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 货品信息(MatGoods)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-14 08:36:14
 */
@Service("matGoodsService")
public class MatGoodsServiceImpl implements MatGoodsService {
    @Resource
    private MatGoodsMapper matGoodsMapper;


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatGoodsPojo getEntity(String key, String tid) {
        MatGoodsPojo matGoodsPojo = this.matGoodsMapper.getEntity(key, tid);
//        List<MatGoodsunitPojo> lst = matGoodsunitMapper.getList(key, tid);
//        if (lst != null) matGoodsPojo.setUnit(lst);
        return matGoodsPojo;
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatGoodsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatGoodsPojo> lst = matGoodsMapper.getPageList(queryParam);
            PageInfo<MatGoodsPojo> pageInfo = new PageInfo<MatGoodsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matGoodsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatGoodsPojo insert(MatGoodsPojo matGoodsPojo) {
        //初始化NULL字段
        if (matGoodsPojo.getGoodsuid() == null) matGoodsPojo.setGoodsuid("");
        if (matGoodsPojo.getGoodsname() == null) matGoodsPojo.setGoodsname("");
        if (matGoodsPojo.getGoodsspec() == null) matGoodsPojo.setGoodsspec("");
        if (matGoodsPojo.getGoodsunit() == null) matGoodsPojo.setGoodsunit("");
        if (matGoodsPojo.getGoodsstate() == null) matGoodsPojo.setGoodsstate("");
        if (matGoodsPojo.getGoodspinyin() == null) matGoodsPojo.setGoodspinyin("");
        if (matGoodsPojo.getVersionnum() == null) matGoodsPojo.setVersionnum("");
        if (matGoodsPojo.getMaterial() == null) matGoodsPojo.setMaterial("");
        if (matGoodsPojo.getSurface() == null) matGoodsPojo.setSurface("");
        if (matGoodsPojo.getBarcode() == null) matGoodsPojo.setBarcode("");
        if (matGoodsPojo.getSafestock() == null) matGoodsPojo.setSafestock(0D);
        if (matGoodsPojo.getInprice() == null) matGoodsPojo.setInprice(0D);
        if (matGoodsPojo.getOutprice() == null) matGoodsPojo.setOutprice(0D);
        if (matGoodsPojo.getGroupid() == null) matGoodsPojo.setGroupid("");
        if (matGoodsPojo.getFileguid() == null) matGoodsPojo.setFileguid("");
        if (matGoodsPojo.getDrawing() == null) matGoodsPojo.setDrawing("");
        if (matGoodsPojo.getStoreid() == null) matGoodsPojo.setStoreid("");
        if (matGoodsPojo.getStorelistname() == null) matGoodsPojo.setStorelistname("");
        if (matGoodsPojo.getStorelistguid() == null) matGoodsPojo.setStorelistguid("");
        if (matGoodsPojo.getIvquantity() == null) matGoodsPojo.setIvquantity(0D);
        if (matGoodsPojo.getAgeprice() == null) matGoodsPojo.setAgeprice(0D);
        if (matGoodsPojo.getUidgroupguid() == null) matGoodsPojo.setUidgroupguid("");
        if (matGoodsPojo.getUidgroupcode() == null) matGoodsPojo.setUidgroupcode("");
        if (matGoodsPojo.getUidgroupname() == null) matGoodsPojo.setUidgroupname("");
        if (matGoodsPojo.getUidgroupnum() == null) matGoodsPojo.setUidgroupnum(0);
        if (matGoodsPojo.getPartid() == null) matGoodsPojo.setPartid("");
        if (matGoodsPojo.getPid() == null) matGoodsPojo.setPid("");
        if (matGoodsPojo.getPuid() == null) matGoodsPojo.setPuid("");
        if (matGoodsPojo.getEnabledmark() == null) matGoodsPojo.setEnabledmark(0);
        if (matGoodsPojo.getGoodsphoto1() == null) matGoodsPojo.setGoodsphoto1("");
        if (matGoodsPojo.getGoodsphoto2() == null) matGoodsPojo.setGoodsphoto2("");
        if (matGoodsPojo.getRemark() == null) matGoodsPojo.setRemark("");
        if (matGoodsPojo.getCreateby() == null) matGoodsPojo.setCreateby("");
        if (matGoodsPojo.getCreatebyid() == null) matGoodsPojo.setCreatebyid("");
        if (matGoodsPojo.getCreatedate() == null) matGoodsPojo.setCreatedate(new Date());
        if (matGoodsPojo.getLister() == null) matGoodsPojo.setLister("");
        if (matGoodsPojo.getListerid() == null) matGoodsPojo.setListerid("");
        if (matGoodsPojo.getModifydate() == null) matGoodsPojo.setModifydate(new Date());
        if (matGoodsPojo.getDeletemark() == null) matGoodsPojo.setDeletemark(0);
        if (matGoodsPojo.getDeletelister() == null) matGoodsPojo.setDeletelister("");
        if (matGoodsPojo.getDeletelisterid() == null) matGoodsPojo.setDeletelisterid("");
        if (matGoodsPojo.getDeletedate() == null) matGoodsPojo.setDeletedate(new Date());
        if (matGoodsPojo.getBatchmg() == null) matGoodsPojo.setBatchmg(0);
        if (matGoodsPojo.getBatchonly() == null) matGoodsPojo.setBatchonly(0);
        if (matGoodsPojo.getSkumark() == null) matGoodsPojo.setSkumark(0);
        if (matGoodsPojo.getPacksnmark() == null) matGoodsPojo.setPacksnmark(0);
        if (matGoodsPojo.getVirtualitem() == null) matGoodsPojo.setVirtualitem(0);
        if (matGoodsPojo.getBomid() == null) matGoodsPojo.setBomid("");
        if (matGoodsPojo.getQuickcode() == null) matGoodsPojo.setQuickcode("");
        if (matGoodsPojo.getBrandname() == null) matGoodsPojo.setBrandname("");
        if (matGoodsPojo.getBuyremqty() == null) matGoodsPojo.setBuyremqty(0D);
        if (matGoodsPojo.getWkwsremqty() == null) matGoodsPojo.setWkwsremqty(0D);
        if (matGoodsPojo.getWkscremqty() == null) matGoodsPojo.setWkscremqty(0D);
        if (matGoodsPojo.getBusremqty() == null) matGoodsPojo.setBusremqty(0D);
        if (matGoodsPojo.getMrpremqty() == null) matGoodsPojo.setMrpremqty(0D);
        if (matGoodsPojo.getRequremqty() == null) matGoodsPojo.setRequremqty(0D);
        if (matGoodsPojo.getOverflowqty() == null) matGoodsPojo.setOverflowqty(0D);
        if (matGoodsPojo.getTaxrate() == null) matGoodsPojo.setTaxrate(0);
        if (matGoodsPojo.getIntaxprice() == null) matGoodsPojo.setIntaxprice(0D);
        if (matGoodsPojo.getOuttaxprice() == null) matGoodsPojo.setOuttaxprice(0D);
        if (matGoodsPojo.getSpecid() == null) matGoodsPojo.setSpecid("");
        if(matGoodsPojo.getAliasname()==null) matGoodsPojo.setAliasname("");
        if (matGoodsPojo.getCustom1() == null) matGoodsPojo.setCustom1("");
        if (matGoodsPojo.getCustom2() == null) matGoodsPojo.setCustom2("");
        if (matGoodsPojo.getCustom3() == null) matGoodsPojo.setCustom3("");
        if (matGoodsPojo.getCustom4() == null) matGoodsPojo.setCustom4("");
        if (matGoodsPojo.getCustom5() == null) matGoodsPojo.setCustom5("");
        if (matGoodsPojo.getCustom6() == null) matGoodsPojo.setCustom6("");
        if (matGoodsPojo.getCustom7() == null) matGoodsPojo.setCustom7("");
        if (matGoodsPojo.getCustom8() == null) matGoodsPojo.setCustom8("");
        if (matGoodsPojo.getCustom9() == null) matGoodsPojo.setCustom9("");
        if (matGoodsPojo.getCustom10() == null) matGoodsPojo.setCustom10("");
        if (matGoodsPojo.getDeptid() == null) matGoodsPojo.setDeptid("");
        if (matGoodsPojo.getTenantid() == null) matGoodsPojo.setTenantid("");
        if (matGoodsPojo.getTenantname() == null) matGoodsPojo.setTenantname("");
        if (matGoodsPojo.getRevision() == null) matGoodsPojo.setRevision(0);
        if (matGoodsPojo.getAlertsqty() == null) matGoodsPojo.setAlertsqty(0D);
        if (matGoodsPojo.getIntqtymark() == null) matGoodsPojo.setIntqtymark(0);
        if (matGoodsPojo.getWeightqty() == null) matGoodsPojo.setWeightqty(0D);
        if (matGoodsPojo.getWeightunit() == null) matGoodsPojo.setWeightunit("");
        if (matGoodsPojo.getLengthqty() == null) matGoodsPojo.setLengthqty(0D);
        if (matGoodsPojo.getLengthunit() == null) matGoodsPojo.setLengthunit("");
        if (matGoodsPojo.getAreaqty() == null) matGoodsPojo.setAreaqty(0D);
        if (matGoodsPojo.getAreaunit() == null) matGoodsPojo.setAreaunit("");
        if (matGoodsPojo.getVolumeqty() == null) matGoodsPojo.setVolumeqty(0D);
        if (matGoodsPojo.getVolumeunit() == null) matGoodsPojo.setVolumeunit("");
        if (matGoodsPojo.getPackqty() == null) matGoodsPojo.setPackqty(0D);
        if (matGoodsPojo.getPackunit() == null) matGoodsPojo.setPackunit("");
        if (matGoodsPojo.getMatqtyunit() == null) matGoodsPojo.setMatqtyunit(0);
        MatGoodsEntity matGoodsEntity = new MatGoodsEntity();
        BeanUtils.copyProperties(matGoodsPojo, matGoodsEntity);
        String id = inksSnowflake.getSnowflake().nextIdStr();
        matGoodsEntity.setId(id);
        matGoodsEntity.setRevision(1);  //乐观锁
        MatGoodsPojo dbPojo = this.matGoodsMapper.getEntityByNameSpecPartBrandNameSurface(matGoodsPojo);
        if (dbPojo != null) {
            throw new RuntimeException("同名同规格的货品已有,请勿重建:" + DateUtils.parseDateToStr("yyyy-MM-dd", dbPojo.getCreatedate()));

        }
        Integer count = this.matGoodsMapper.checkGoodsUid("", matGoodsPojo.getGoodsuid(), matGoodsPojo.getTenantid());
        if (count > 0) {
            throw new RuntimeException("货品编码已存在,请修改编码后重试!");
        }
        this.matGoodsMapper.insert(matGoodsEntity);

//        // 如果有货品多单位信息,则保存货品多单位List
//        List<MatGoodsunitPojo> lstunit = matGoodsPojo.getUnit();
//        if (lstunit != null) {
//            //循环每个item子表
//            for (int i = 0; i < lstunit.size(); i++) {
//                //初始化item的NULL
//                MatGoodsunitPojo matGoodsunitPojo = this.matGoodsunitService.clearNull(lstunit.get(i));
//                MatGoodsunitEntity matGoodsunitEntity = new MatGoodsunitEntity();
//                BeanUtils.copyProperties(matGoodsunitPojo, matGoodsunitEntity);
//                //设置id和Pid
//                matGoodsunitEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
//                matGoodsunitEntity.setPid(id);
//                matGoodsunitEntity.setTenantid(matGoodsPojo.getTenantid());
//                matGoodsunitEntity.setRevision(1);  //乐观锁
//                //插入子表
//                this.matGoodsunitMapper.insert(matGoodsunitEntity);
//            }
//        }
        return this.getEntity(matGoodsEntity.getId(), matGoodsEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matGoodsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatGoodsPojo update(MatGoodsPojo matGoodsPojo) {
        MatGoodsEntity matGoodsEntity = new MatGoodsEntity();
        BeanUtils.copyProperties(matGoodsPojo, matGoodsEntity);
        MatGoodsPojo dbPojo = this.matGoodsMapper.getEntityByNameSpecPartBrandNameSurface(matGoodsPojo);
        if (dbPojo != null && !dbPojo.getId().equals(matGoodsPojo.getId())) {
            throw new RuntimeException("同名同规格的货品已有,请勿重建:" + DateUtils.parseDateToStr("yyyy-MM-dd", dbPojo.getCreatedate()));
        }
        Integer count = this.matGoodsMapper.checkGoodsUid(matGoodsPojo.getId(), matGoodsPojo.getGoodsuid(), matGoodsPojo.getTenantid());
        if (count > 0) {
            throw new RuntimeException("货品编码已存在,请修改编码后重试!");
        }
        this.matGoodsMapper.update(matGoodsEntity);
//        // 如果有货品多单位信息,则修改货品多单位List
//        if (matGoodsPojo.getUnit() != null && matGoodsPojo.getUnit().size() > 0) {
//            //Item子表处理
//            List<MatGoodsunitPojo> lst = matGoodsPojo.getUnit();
//            //获取被删除的Item
//            List<String> lstDelIds = matGoodsMapper.getDelUnitIds(matGoodsPojo);
//            if (lstDelIds != null) {
//                //循环每个删除item子表
//                for (int i = 0; i < lstDelIds.size(); i++) {
//                    this.matGoodsunitMapper.delete(lstDelIds.get(i), matGoodsEntity.getTenantid());
//                }
//            }
//            if (lst != null) {
//                //循环每个item子表
//                for (int i = 0; i < lst.size(); i++) {
//                    MatGoodsunitEntity matGoodsunitEntity = new MatGoodsunitEntity();
//                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
//                        //初始化item的NULL
//                        MatGoodsunitPojo itemPojo = this.matGoodsunitService.clearNull(lst.get(i));
//                        BeanUtils.copyProperties(itemPojo, matGoodsunitEntity);
//                        //设置id和Pid
//                        matGoodsunitEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
//                        matGoodsunitEntity.setPid(matGoodsEntity.getId());  // 主表 id
//                        matGoodsunitEntity.setTenantid(matGoodsEntity.getTenantid());   // 租户id
//                        matGoodsunitEntity.setRevision(1);  // 乐观锁
//                        //插入子表
//                        this.matGoodsunitMapper.insert(matGoodsunitEntity);
//                    } else {
//                        BeanUtils.copyProperties(lst.get(i), matGoodsunitEntity);
//                        matGoodsunitEntity.setTenantid(matGoodsEntity.getTenantid());
//                        this.matGoodsunitMapper.update(matGoodsunitEntity);
//                    }
//                }
//            }
//        }
        return this.getEntity(matGoodsEntity.getId(), matGoodsEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        MatGoodsPojo matGoodsPojo = this.getEntity(key, tid);
//        //货品单位unit子表处理
//        List<MatGoodsunitPojo> lst = matGoodsPojo.getUnit();
//        if (lst != null && lst.size() > 0) {
//            //循环每个删除item子表
//            for (int i = 0; i < lst.size(); i++) {
//                this.matGoodsunitMapper.delete(lst.get(i).getId(), tid);
//            }
//        }
        return this.matGoodsMapper.delete(key, tid);
    }

    //按货品料号和租户ID查询货品
    @Override
    public MatGoodsPojo getEntityByGoodsUid(String name, String tid) {
        try {
            return matGoodsMapper.getEntityByGoodsUid(name, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //按货品名称和租户ID查询货品
    @Override
    public MatGoodsPojo getEntityByName(String name, String tid) {
        try {
            return matGoodsMapper.getEntityByName(name, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //按货品名称\规格查询货品信息
    @Override
    public MatGoodsPojo getEntityByNameSpec(String name, String goodsspec, String tid) {
        try {
            return matGoodsMapper.getEntityByNameSpec(name, goodsspec, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //按货品名称\规格\外部编码查询货品信息
    @Override
    public MatGoodsPojo getEntityByNameSpecPart(String name, String goodsspec, String partid, String tid) {
        try {
            return matGoodsMapper.getEntityByNameSpecPart(name, goodsspec, partid, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //获取最大货品编码实例
    @Override
    public MatGoodsPojo getEntityByGroup(String groupid, String tid) {
        try {
            return matGoodsMapper.getEntityByGroup(groupid, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    // 查询货品是否被引用
    @Override
    public List<String> getCiteBillName(String key, String tid) {
        return this.matGoodsMapper.getCiteBillName(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatGoodsPojo getEntityByPartid(String key, String tid) {
        return this.matGoodsMapper.getEntityByPartid(key, tid);
    }


    //按快速码和租户ID查询货品
    @Override
    public MatGoodsPojo getEntityByQuickCode(String key, String tid) {
        try {
            return matGoodsMapper.getEntityByQuickCode(key, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 修改数据
     *
     * @param tid 实例对象
     * @return 实例对象
     */
    @Override
    public Integer updateIvQty(String tid) {
        try {
            return this.matGoodsMapper.updateIvQty(tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //刷新销售待出数
    @Override
    public int updateGoodsBusRemQty(String goodsid, String tid) {
        return this.matGoodsMapper.updateGoodsBusRemQty(goodsid, tid);
    }

    //刷新收货待入数

    @Override
    public int updateGoodsBuyRemQty(String key, String tid) {
        return this.matGoodsMapper.updateGoodsBuyRemQty(key, tid);
    }
    //刷新生产待入数

    @Override
    public int updateGoodsWkWsRemQty(String key, String tid) {
        return this.matGoodsMapper.updateGoodsWkWsRemQty(key, tid);
    }
    //刷新加工待入数

    @Override
    public int updateGoodsWkScRemQty(String key, String tid) {
        return this.matGoodsMapper.updateGoodsWkScRemQty(key, tid);
    }
    //刷新领料待出数

    @Override
    public int updateGoodsRequRemQty(String key, String tid) {
        return this.matGoodsMapper.updateGoodsRequRemQty(key, tid);
    }

    //刷新当前库存数和单价
    @Override
    public int updateGoodsIvQuantity(String key, String tid) {
        return this.matGoodsMapper.updateGoodsIvQuantity(key, tid);
    }

    @Override
    public String getGoodsstate(String goodsid, String tenantid) {
        return this.matGoodsMapper.getGoodsstate(goodsid, tenantid);
    }

    //"参数type  0：goods，1：order ;弹窗让客户选择 【默认厂商】 来源 goods,【末次订单】来源上一个采购订单；
    // key：货品ID，type：0：goods，1：order
    @Override
    public List<Map<String, Object>> getSupplierByGood(List<String> goodsidList, Integer type) {
        if (goodsidList == null || goodsidList.isEmpty()) {
            return Collections.emptyList();
        }

        switch (type) {
            case 0:
                return this.matGoodsMapper.getSupplierByGoods(goodsidList);
            case 1:
                return this.matGoodsMapper.getSupplierByLastOrders(goodsidList);
            default:
                return Collections.emptyList();
        }
    }

    @Override
    public MatGoodsBatchAttrPojo getGoodsBatchAttr(String goodsid, String tenantid) {
        return this.matGoodsMapper.getGoodsBatchAttr(goodsid, tenantid);
    }
}
