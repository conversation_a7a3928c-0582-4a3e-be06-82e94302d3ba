package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.BuyAccountEntity;
import inks.service.sa.som.domain.BuyAccountarapEntity;
import inks.service.sa.som.domain.BuyAccountinvoEntity;
import inks.service.sa.som.domain.BuyAccountitemEntity;
import inks.service.sa.som.domain.constant.MyConstant;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.*;
import inks.service.sa.som.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 应付账单(BuyAccount)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-09 08:28:58
 */
@Service("buyAccountService")
public class BuyAccountServiceImpl implements BuyAccountService {
    @Resource
    private BuyAccountMapper buyAccountMapper;

    @Resource
    private BuyAccountitemMapper buyAccountitemMapper;
    @Resource
    private BuyAccountinvoMapper buyAccountinvoMapper;
    @Resource
    private BuyAccountarapMapper buyAccountarapMapper;
    
    @Resource
    private BuyAccountitemService buyAccountitemService;
    @Resource
    private BuyAccountinvoService buyAccountinvoService;
    @Resource
    private BuyAccountarapService buyAccountarapService;

    @Resource
    private BuyAccountrecService buyAccountrecService;
    @Resource
    private SaRedisService saRedisService;

    @Resource
    private BuyInvoiceMapper buyInvoiceMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyAccountPojo getEntity(String key, String tid) {
        return this.buyAccountMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyAccountitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyAccountitemdetailPojo> lst = buyAccountMapper.getPageList(queryParam);
            PageInfo<BuyAccountitemdetailPojo> pageInfo = new PageInfo<BuyAccountitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyAccountPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BuyAccountPojo buyAccountPojo = this.buyAccountMapper.getEntity(key, tid);
            //读取子表
            buyAccountPojo.setItem(buyAccountitemMapper.getList(buyAccountPojo.getId(), buyAccountPojo.getTenantid()));
            // 读取invo
            buyAccountPojo.setInvo(buyAccountinvoMapper.getList(buyAccountPojo.getId(), buyAccountPojo.getTenantid()));
            // 读取arap
            buyAccountPojo.setArap(buyAccountarapMapper.getList(buyAccountPojo.getId(), buyAccountPojo.getTenantid()));
            return buyAccountPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyAccountPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyAccountPojo> lst = buyAccountMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(buyAccountitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setInvo(buyAccountinvoMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setArap(buyAccountarapMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BuyAccountPojo> pageInfo = new PageInfo<BuyAccountPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyAccountPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyAccountPojo> lst = buyAccountMapper.getPageTh(queryParam);
            PageInfo<BuyAccountPojo> pageInfo = new PageInfo<BuyAccountPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param buyAccountPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyAccountPojo insert(BuyAccountPojo buyAccountPojo) {
//初始化NULL字段
        if (buyAccountPojo.getRefno() == null) buyAccountPojo.setRefno("");
        if (buyAccountPojo.getBilltype() == null) buyAccountPojo.setBilltype("");
        if (buyAccountPojo.getBilldate() == null) buyAccountPojo.setBilldate(new Date());
        if (buyAccountPojo.getBilltitle() == null) buyAccountPojo.setBilltitle("");
        if (buyAccountPojo.getProjectid() == null) buyAccountPojo.setProjectid("");
        if (buyAccountPojo.getProjcode() == null) buyAccountPojo.setProjcode("");
        if (buyAccountPojo.getProjname() == null) buyAccountPojo.setProjname("");
        if (buyAccountPojo.getGroupid() == null) buyAccountPojo.setGroupid("");
        if (buyAccountPojo.getCarryyear() == null) buyAccountPojo.setCarryyear(0);
        if (buyAccountPojo.getCarrymonth() == null) buyAccountPojo.setCarrymonth(0);
        if (buyAccountPojo.getStartdate() == null) buyAccountPojo.setStartdate(new Date());
        if (buyAccountPojo.getEnddate() == null) buyAccountPojo.setEnddate(new Date());
        if (buyAccountPojo.getOperator() == null) buyAccountPojo.setOperator("");
        if (buyAccountPojo.getOperatorid() == null) buyAccountPojo.setOperatorid("");
        if (buyAccountPojo.getRownum() == null) buyAccountPojo.setRownum(0);
        if (buyAccountPojo.getSummary() == null) buyAccountPojo.setSummary("");
        if (buyAccountPojo.getCreateby() == null) buyAccountPojo.setCreateby("");
        if (buyAccountPojo.getCreatebyid() == null) buyAccountPojo.setCreatebyid("");
        if (buyAccountPojo.getCreatedate() == null) buyAccountPojo.setCreatedate(new Date());
        if (buyAccountPojo.getLister() == null) buyAccountPojo.setLister("");
        if (buyAccountPojo.getListerid() == null) buyAccountPojo.setListerid("");
        if (buyAccountPojo.getModifydate() == null) buyAccountPojo.setModifydate(new Date());
        if (buyAccountPojo.getBillopenamount() == null) buyAccountPojo.setBillopenamount(0D);
        if (buyAccountPojo.getBillinamount() == null) buyAccountPojo.setBillinamount(0D);
        if (buyAccountPojo.getBilloutamount() == null) buyAccountPojo.setBilloutamount(0D);
        if (buyAccountPojo.getBillcloseamount() == null) buyAccountPojo.setBillcloseamount(0D);
        if (buyAccountPojo.getInvoopenamount() == null) buyAccountPojo.setInvoopenamount(0D);
        if (buyAccountPojo.getInvoinamount() == null) buyAccountPojo.setInvoinamount(0D);
        if (buyAccountPojo.getInvooutamount() == null) buyAccountPojo.setInvooutamount(0D);
        if (buyAccountPojo.getInvocloseamount() == null) buyAccountPojo.setInvocloseamount(0D);
        if (buyAccountPojo.getArapopenamount() == null) buyAccountPojo.setArapopenamount(0D);
        if (buyAccountPojo.getArapinamount() == null) buyAccountPojo.setArapinamount(0D);
        if (buyAccountPojo.getArapoutamount() == null) buyAccountPojo.setArapoutamount(0D);
        if (buyAccountPojo.getArapcloseamount() == null) buyAccountPojo.setArapcloseamount(0D);
        if (buyAccountPojo.getPrintcount() == null) buyAccountPojo.setPrintcount(0);
        if (buyAccountPojo.getCustom1() == null) buyAccountPojo.setCustom1("");
        if (buyAccountPojo.getCustom2() == null) buyAccountPojo.setCustom2("");
        if (buyAccountPojo.getCustom3() == null) buyAccountPojo.setCustom3("");
        if (buyAccountPojo.getCustom4() == null) buyAccountPojo.setCustom4("");
        if (buyAccountPojo.getCustom5() == null) buyAccountPojo.setCustom5("");
        if (buyAccountPojo.getCustom6() == null) buyAccountPojo.setCustom6("");
        if (buyAccountPojo.getCustom7() == null) buyAccountPojo.setCustom7("");
        if (buyAccountPojo.getCustom8() == null) buyAccountPojo.setCustom8("");
        if (buyAccountPojo.getCustom9() == null) buyAccountPojo.setCustom9("");
        if (buyAccountPojo.getCustom10() == null) buyAccountPojo.setCustom10("");
        if (buyAccountPojo.getTenantid() == null) buyAccountPojo.setTenantid("");
        if (buyAccountPojo.getTenantname() == null) buyAccountPojo.setTenantname("");
        if (buyAccountPojo.getRevision() == null) buyAccountPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BuyAccountEntity buyAccountEntity = new BuyAccountEntity();
        BeanUtils.copyProperties(buyAccountPojo, buyAccountEntity);
        //设置id和新建日期
        buyAccountEntity.setId(id);
        buyAccountEntity.setRevision(1);  //乐观锁
        //插入主表
        this.buyAccountMapper.insert(buyAccountEntity);
        //Item子表处理
        List<BuyAccountitemPojo> lst = buyAccountPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                BuyAccountitemPojo itemPojo = this.buyAccountitemService.clearNull(lst.get(i));
                BuyAccountitemEntity buyAccountitemEntity = new BuyAccountitemEntity();
                BeanUtils.copyProperties(itemPojo, buyAccountitemEntity);
                //设置id和Pid
                buyAccountitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                buyAccountitemEntity.setPid(id);
                buyAccountitemEntity.setTenantid(buyAccountPojo.getTenantid());
                buyAccountitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.buyAccountitemMapper.insert(buyAccountitemEntity);
            }
        }

        //invo子表处理
        List<BuyAccountinvoPojo> lstInvo = buyAccountPojo.getInvo();
        if (lstInvo != null) {
            //循环每个item子表
            for (int i = 0; i < lstInvo.size(); i++) {
                //初始化item的NULL
                BuyAccountinvoPojo itemPojo = this.buyAccountinvoService.clearNull(lstInvo.get(i));
                BuyAccountinvoEntity buyAccountinvoEntity = new BuyAccountinvoEntity();
                BeanUtils.copyProperties(itemPojo, buyAccountinvoEntity);
                //设置id和Pid
                buyAccountinvoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                buyAccountinvoEntity.setPid(id);
                buyAccountinvoEntity.setTenantid(buyAccountPojo.getTenantid());
                buyAccountinvoEntity.setRevision(1);  //乐观锁
                //插入子表
                this.buyAccountinvoMapper.insert(buyAccountinvoEntity);
            }
        }
        // arap子表处理
        List<BuyAccountarapPojo> lstArap = buyAccountPojo.getArap();
        if (lstArap != null) {
            //循环每个item子表
            for (int i = 0; i < lstArap.size(); i++) {
                //初始化item的NULL
                BuyAccountarapPojo itemPojo = this.buyAccountarapService.clearNull(lstArap.get(i));
                BuyAccountarapEntity buyAccountarapEntity = new BuyAccountarapEntity();
                BeanUtils.copyProperties(itemPojo, buyAccountarapEntity);
                //设置id和Pid
                buyAccountarapEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                buyAccountarapEntity.setPid(id);
                buyAccountarapEntity.setTenantid(buyAccountPojo.getTenantid());
                buyAccountarapEntity.setRevision(1);  //乐观锁
                //插入子表
                this.buyAccountarapMapper.insert(buyAccountarapEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(buyAccountEntity.getId(), buyAccountEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param buyAccountPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyAccountPojo update(BuyAccountPojo buyAccountPojo) {
        //主表更改
        BuyAccountEntity buyAccountEntity = new BuyAccountEntity();
        BeanUtils.copyProperties(buyAccountPojo, buyAccountEntity);
        this.buyAccountMapper.update(buyAccountEntity);
        //Item子表处理
        if (buyAccountPojo.getItem() != null) {
            //Item子表处理
            List<BuyAccountitemPojo> lst = buyAccountPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = buyAccountMapper.getDelItemIds(buyAccountPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.buyAccountitemMapper.delete(lstDelIds.get(i), buyAccountEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    BuyAccountitemEntity buyAccountitemEntity = new BuyAccountitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        BuyAccountitemPojo itemPojo = this.buyAccountitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, buyAccountitemEntity);
                        //设置id和Pid
                        buyAccountitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        buyAccountitemEntity.setPid(buyAccountEntity.getId());  // 主表 id
                        buyAccountitemEntity.setTenantid(buyAccountPojo.getTenantid());   // 租户id
                        buyAccountitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.buyAccountitemMapper.insert(buyAccountitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), buyAccountitemEntity);
                        buyAccountitemEntity.setTenantid(buyAccountPojo.getTenantid());
                        this.buyAccountitemMapper.update(buyAccountitemEntity);
                    }
                }
            }
        }

        //invo子表处理
        if (buyAccountPojo.getInvo() != null) {
            //invo子表处理
            List<BuyAccountinvoPojo> lst = buyAccountPojo.getInvo();
            //获取被删除的invo
            List<String> lstDelIds = buyAccountMapper.getDelInvoIds(buyAccountPojo);
            if (lstDelIds != null) {
                //循环每个删除invo子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.buyAccountinvoMapper.delete(lstDelIds.get(i), buyAccountEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个invo子表
                for (int i = 0; i < lst.size(); i++) {
                    BuyAccountinvoEntity buyAccountinvoEntity = new BuyAccountinvoEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化invo的NULL
                        BuyAccountinvoPojo itemPojo = this.buyAccountinvoService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, buyAccountinvoEntity);
                        //设置id和Pid
                        buyAccountinvoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // invo id
                        buyAccountinvoEntity.setPid(buyAccountEntity.getId());  // 主表 id
                        buyAccountinvoEntity.setTenantid(buyAccountPojo.getTenantid());   // 租户id
                        buyAccountinvoEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.buyAccountinvoMapper.insert(buyAccountinvoEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), buyAccountinvoEntity);
                        buyAccountinvoEntity.setTenantid(buyAccountPojo.getTenantid());
                        this.buyAccountinvoMapper.update(buyAccountinvoEntity);
                    }
                }
            }
        }
        //arap子表处理
        if (buyAccountPojo.getArap() != null) {
            //arap子表处理
            List<BuyAccountarapPojo> lst = buyAccountPojo.getArap();
            //获取被删除的arap
            List<String> lstDelIds = buyAccountMapper.getDelArapIds(buyAccountPojo);
            if (lstDelIds != null) {
                //循环每个删除arap子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.buyAccountarapMapper.delete(lstDelIds.get(i), buyAccountEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个arap子表
                for (int i = 0; i < lst.size(); i++) {
                    BuyAccountarapEntity buyAccountarapEntity = new BuyAccountarapEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化arap的NULL
                        BuyAccountarapPojo itemPojo = this.buyAccountarapService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, buyAccountarapEntity);
                        //设置id和Pid
                        buyAccountarapEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // arap id
                        buyAccountarapEntity.setPid(buyAccountEntity.getId());  // 主表 id
                        buyAccountarapEntity.setTenantid(buyAccountPojo.getTenantid());   // 租户id
                        buyAccountarapEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.buyAccountarapMapper.insert(buyAccountarapEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), buyAccountarapEntity);
                        buyAccountarapEntity.setTenantid(buyAccountPojo.getTenantid());
                        this.buyAccountarapMapper.update(buyAccountarapEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(buyAccountEntity.getId(), buyAccountEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BuyAccountPojo buyAccountPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BuyAccountitemPojo> lst = buyAccountPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.buyAccountitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        //invo子表处理
        List<BuyAccountinvoPojo> lstInvo = buyAccountPojo.getInvo();
        if (lstInvo != null) {
            //循环每个删除invo子表
            for (int i = 0; i < lstInvo.size(); i++) {
                this.buyAccountinvoMapper.delete(lstInvo.get(i).getId(), tid);
            }
        }
        //arap子表处理
        List<BuyAccountarapPojo> lstArap = buyAccountPojo.getArap();
        if (lstArap != null) {
            //循环每个删除arap子表
            for (int i = 0; i < lstArap.size(); i++) {
                this.buyAccountarapMapper.delete(lstArap.get(i).getId(), tid);
            }
        }
        return this.buyAccountMapper.delete(key, tid);
    }

    /**
     * 新增数据
     *
     * @param buyAccountPojo 实例对象
     * @return 实例对象
     */
    @Override
    public List<BuyAccountitemPojo> pullItemList(BuyAccountPojo buyAccountPojo) {
        //生成id
//        AppWorkgroupPojo wgPojo = this.appWorkgroupService.getEntity(busAccountPojo.getGroupid(), busAccountPojo.getTenantid());
//        if (wgPojo == null) {
//            throw new RuntimeException("未找到对应客户信息");
//        }

        // 查询当前客户之前的销售账单
        BuyAccountPojo buyAccountMaxPojo = this.buyAccountMapper.getMaxEntityByGroup(buyAccountPojo.getGroupid(), buyAccountPojo.getTenantid());

        // 初始化时间
        Date dtStart = DateUtils.parseDate("2022-01-01");
        Double openAmount = 0D;
        if (buyAccountMaxPojo != null) {
//            throw new RuntimeException("请先初始化客户账单");
            dtStart = buyAccountMaxPojo.getEnddate();
            openAmount = buyAccountMaxPojo.getBillcloseamount();
        }
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(buyAccountPojo.getEnddate()) + " 23:59:59");

        QueryParam queryParam = new QueryParam();
        String strFilter = " and Groupid='" + buyAccountPojo.getGroupid() + "'";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(1000);
        queryParam.setTenantid(buyAccountPojo.getTenantid());
        List<BuyAccountitemPojo> lstitem = this.buyAccountMapper.pullItemList(queryParam);
        if (lstitem.size() == 0) {
//            throw new RuntimeException("未找到采购与付款信息");
            BuyAccountitemPojo newitem = new BuyAccountitemPojo();
            newitem.setOpenamount(0D);
            newitem.setInamount(0D);
            newitem.setOutamount(0D);
            newitem.setCloseamount(0D);
            lstitem.add(newitem);
        }

        // 改为BigDec
        BigDecimal decinAmt = BigDecimal.valueOf(0);
        BigDecimal decoutAmt = BigDecimal.valueOf(0);
        BigDecimal itemopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal itemcloseAmt = BigDecimal.valueOf(0);
        for (int i = 0; i < lstitem.size(); i++) {
            lstitem.get(i).setRownum(i);
            BigDecimal iteminAmt = BigDecimal.valueOf(lstitem.get(i).getInamount());
            BigDecimal itemoutAmt = BigDecimal.valueOf(lstitem.get(i).getOutamount());
            decinAmt = decinAmt.add(iteminAmt);
            decoutAmt = decoutAmt.add(itemoutAmt);
            // 计算行期末
            itemcloseAmt = itemopenAmt.add(iteminAmt).subtract(itemoutAmt);
            lstitem.get(i).setCloseamount(itemcloseAmt.doubleValue());
            // 更新行期初，为下一行计算用
            itemopenAmt = BigDecimal.valueOf(itemcloseAmt.doubleValue());
        }

        // 第一行填入初始，初末
        lstitem.get(0).setOpenamount(openAmount);

        BigDecimal decopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal deccloseAmt = BigDecimal.valueOf(0);
        deccloseAmt = deccloseAmt.add(decopenAmt).add(decinAmt).subtract(decoutAmt);
        // 最后一行 填入期末
        lstitem.get(lstitem.size() - 1).setCloseamount(deccloseAmt.doubleValue());

        //返回Bill实例
        return lstitem;
    }

    @Override
    public List<BuyAccountinvoPojo> pullInvoList(BuyAccountPojo buyAccountPojo) {
        // 查询当前客户之前的销售账单
        BuyAccountPojo buyAccountMaxPojo = this.buyAccountMapper.getMaxEntityByGroup(buyAccountPojo.getGroupid(), buyAccountPojo.getTenantid());
        // 初始化时间
        Date dtStart = DateUtils.parseDate("2022-01-01");
        Double openAmount = 0D;
        if (buyAccountMaxPojo != null) {
            dtStart = buyAccountMaxPojo.getEnddate();
            openAmount = buyAccountMaxPojo.getBillcloseamount();
        }
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(buyAccountPojo.getEnddate()) + " 23:59:59");

        QueryParam queryParam = new QueryParam();
        String strFilter = " and Groupid='" + buyAccountPojo.getGroupid() + "'";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(1000);
        queryParam.setTenantid(buyAccountPojo.getTenantid());
        List<BuyAccountinvoPojo> lstinvoitem = this.buyAccountMapper.pullInvoList(queryParam);
        if (lstinvoitem.size() == 0) {
            BuyAccountinvoPojo newitem = new BuyAccountinvoPojo();
            newitem.setOpenamount(0D);
            newitem.setInamount(0D);
            newitem.setOutamount(0D);
            newitem.setCloseamount(0D);
            lstinvoitem.add(newitem);
        }
        // 改为BigDec
        BigDecimal decinAmt = BigDecimal.valueOf(0);
        BigDecimal decoutAmt = BigDecimal.valueOf(0);
        BigDecimal itemopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal itemcloseAmt = BigDecimal.ZERO;
        for (int i = 0; i < lstinvoitem.size(); i++) {
            lstinvoitem.get(i).setRownum(i);
            BigDecimal iteminAmt = BigDecimal.valueOf(lstinvoitem.get(i).getInamount());
            BigDecimal itemoutAmt = BigDecimal.valueOf(lstinvoitem.get(i).getOutamount());
            decinAmt = decinAmt.add(iteminAmt);
            decoutAmt = decoutAmt.add(itemoutAmt);
            // 计算行期末
            itemcloseAmt = itemopenAmt.add(iteminAmt).subtract(itemoutAmt);
            lstinvoitem.get(i).setCloseamount(itemcloseAmt.doubleValue());
            // 更新行期初，为下一行计算用
            itemopenAmt = BigDecimal.valueOf(itemcloseAmt.doubleValue());
        }

        // 第一行填入 初始，初末
        lstinvoitem.get(0).setOpenamount(openAmount);

        BigDecimal decopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal deccloseAmt = BigDecimal.valueOf(0);
        deccloseAmt = deccloseAmt.add(decopenAmt).add(decinAmt).subtract(decoutAmt);
        // 最后一行 填入期末
        lstinvoitem.get(lstinvoitem.size() - 1).setCloseamount(deccloseAmt.doubleValue());
        //返回Bill实例
        return lstinvoitem;
    }

    @Override
    public List<BuyAccountarapPojo> pullArapList(BuyAccountPojo buyAccountPojo) {
        // 查询当前客户之前的销售账单
        BuyAccountPojo buyAccountMaxPojo = this.buyAccountMapper.getMaxEntityByGroup(buyAccountPojo.getGroupid(), buyAccountPojo.getTenantid());
        // 初始化时间
        Date dtStart = DateUtils.parseDate("2022-01-01");
        Double openAmount = 0D;
        if (buyAccountMaxPojo != null) {
            dtStart = buyAccountMaxPojo.getEnddate();
            openAmount = buyAccountMaxPojo.getBillcloseamount();
        }
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(buyAccountPojo.getEnddate()) + " 23:59:59");

        QueryParam queryParam = new QueryParam();
        String strFilter = " and Groupid='" + buyAccountPojo.getGroupid() + "'";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(1000);
        queryParam.setTenantid(buyAccountPojo.getTenantid());
        List<BuyAccountarapPojo> lstarapitem = this.buyAccountMapper.pullArapList(queryParam);
        if (lstarapitem.size() == 0) {
            BuyAccountarapPojo newitem = new BuyAccountarapPojo();
            newitem.setOpenamount(0D);
            newitem.setInamount(0D);
            newitem.setOutamount(0D);
            newitem.setCloseamount(0D);
            lstarapitem.add(newitem);
        }
        // 改为BigDec
        BigDecimal decinAmt = BigDecimal.valueOf(0);
        BigDecimal decoutAmt = BigDecimal.valueOf(0);
        BigDecimal itemopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal itemcloseAmt = BigDecimal.ZERO;
        for (int i = 0; i < lstarapitem.size(); i++) {
            lstarapitem.get(i).setRownum(i);
            BigDecimal iteminAmt = BigDecimal.valueOf(lstarapitem.get(i).getInamount());
            BigDecimal itemoutAmt = BigDecimal.valueOf(lstarapitem.get(i).getOutamount());
            decinAmt = decinAmt.add(iteminAmt);
            decoutAmt = decoutAmt.add(itemoutAmt);
            // 计算行期末
            itemcloseAmt = itemopenAmt.add(iteminAmt).subtract(itemoutAmt);
            lstarapitem.get(i).setCloseamount(itemcloseAmt.doubleValue());
            // 更新行期初，为下一行计算用
            itemopenAmt = BigDecimal.valueOf(itemcloseAmt.doubleValue());
        }

        // 第一行填入 初始，初末
        lstarapitem.get(0).setOpenamount(openAmount);

        BigDecimal decopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal deccloseAmt = BigDecimal.valueOf(0);
        deccloseAmt = deccloseAmt.add(decopenAmt).add(decinAmt).subtract(decoutAmt);
        // 最后一行 填入期末
        lstarapitem.get(lstarapitem.size() - 1).setCloseamount(deccloseAmt.doubleValue());
        //返回Bill实例
        return lstarapitem;
    }

    // 批量生成账单
    @Override
    @Transactional
    public int batchCreate(BuyAccountPojo buyAccountPojo) {
        try {
            int num = 0;
            String tid = buyAccountPojo.getTenantid();
            List<String> lstwgIds = this.buyAccountMapper.getSupplierIds(tid);
            // item改为BigDec
            BigDecimal decrecopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecoutAmt = BigDecimal.valueOf(0);
            BigDecimal decreccloseAmt = BigDecimal.valueOf(0);
            // invo改为BigDec
            BigDecimal decrecInvoopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecInvoinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecInvooutAmt = BigDecimal.valueOf(0);
            BigDecimal decrecInvocloseAmt = BigDecimal.valueOf(0);
            // arap改为BigDec
            BigDecimal decrecArapopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecArapinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecArapoutAmt = BigDecimal.valueOf(0);
            BigDecimal decrecArapcloseAmt = BigDecimal.valueOf(0);
            // 遍历计算每个客户账单
            for (String wgid : lstwgIds) {
                BuyAccountPojo newPojo = new BuyAccountPojo();
                BeanUtils.copyProperties(buyAccountPojo, newPojo);
                newPojo.setGroupid(wgid);
                // 1.收货单to收款
                newPojo.setItem(pullItemList(newPojo));
                // 改为BigDec
                BigDecimal decopenAmt = BigDecimal.valueOf(0);
                BigDecimal decinAmt = BigDecimal.valueOf(0);
                BigDecimal decoutAmt = BigDecimal.valueOf(0);
                BigDecimal deccloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getItem().size() > 0) {
                    decopenAmt = decopenAmt.add(BigDecimal.valueOf(newPojo.getItem().get(0).getOpenamount()));
                    deccloseAmt = deccloseAmt.add(BigDecimal.valueOf(newPojo.getItem().get(newPojo.getItem().size() - 1).getCloseamount()));
                }
                for (BuyAccountitemPojo itemPojo : newPojo.getItem()) {
                    decinAmt = decinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decoutAmt = decoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setBillopenamount(decopenAmt.doubleValue());
                newPojo.setBillinamount(decinAmt.doubleValue());
                newPojo.setBilloutamount(decoutAmt.doubleValue());
                newPojo.setBillcloseamount(deccloseAmt.doubleValue());

                // 2.收货单to发票
                newPojo.setInvo(pullInvoList(newPojo));
                // 改为BigDec
                BigDecimal decInvoopenAmt = BigDecimal.valueOf(0);
                BigDecimal decInvoinAmt = BigDecimal.valueOf(0);
                BigDecimal decInvooutAmt = BigDecimal.valueOf(0);
                BigDecimal decInvocloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getInvo().size() > 0) {
                    decInvoopenAmt = decInvoopenAmt.add(BigDecimal.valueOf(newPojo.getInvo().get(0).getOpenamount()));
                    decInvocloseAmt = decInvocloseAmt.add(BigDecimal.valueOf(newPojo.getInvo().get(newPojo.getInvo().size() - 1).getCloseamount()));
                }
                for (BuyAccountinvoPojo itemPojo : newPojo.getInvo()) {
                    decInvoinAmt = decInvoinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decInvooutAmt = decInvooutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setInvoopenamount(decInvoopenAmt.doubleValue());
                newPojo.setInvoinamount(decInvoinAmt.doubleValue());
                newPojo.setInvooutamount(decInvooutAmt.doubleValue());
                newPojo.setInvocloseamount(decInvocloseAmt.doubleValue());

                // 3.发票to收款
                newPojo.setArap(pullArapList(newPojo));
                // 改为BigDec
                BigDecimal decArapopenAmt = BigDecimal.valueOf(0);
                BigDecimal decArapinAmt = BigDecimal.valueOf(0);
                BigDecimal decArapoutAmt = BigDecimal.valueOf(0);
                BigDecimal decArapcloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getArap().size() > 0) {
                    decArapopenAmt = decArapopenAmt.add(BigDecimal.valueOf(newPojo.getArap().get(0).getOpenamount()));
                    decArapcloseAmt = decArapcloseAmt.add(BigDecimal.valueOf(newPojo.getArap().get(newPojo.getArap().size() - 1).getCloseamount()));
                }
                for (BuyAccountarapPojo itemPojo : newPojo.getArap()) {
                    decArapinAmt = decArapinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decArapoutAmt = decArapoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setArapopenamount(decArapopenAmt.doubleValue());
                newPojo.setArapinamount(decArapinAmt.doubleValue());
                newPojo.setArapoutamount(decArapoutAmt.doubleValue());
                newPojo.setArapcloseamount(decArapcloseAmt.doubleValue());

                insert(newPojo);
                // 当前客户item累加
                decrecopenAmt = decrecopenAmt.add(decopenAmt);
                decrecinAmt = decrecinAmt.add(decinAmt);
                decrecoutAmt = decrecoutAmt.add(decoutAmt);
                decreccloseAmt = decreccloseAmt.add(deccloseAmt);
                // 当前客户invo累加
                decrecInvoopenAmt = decrecInvoopenAmt.add(decInvoopenAmt);
                decrecInvoinAmt = decrecInvoinAmt.add(decInvoinAmt);
                decrecInvooutAmt = decrecInvooutAmt.add(decInvooutAmt);
                decrecInvocloseAmt = decrecInvocloseAmt.add(decInvocloseAmt);
                // 当前客户arap累加
                decrecArapopenAmt = decrecArapopenAmt.add(decArapopenAmt);
                decrecArapinAmt = decrecArapinAmt.add(decArapinAmt);
                decrecArapoutAmt = decrecArapoutAmt.add(decArapoutAmt);
                decrecArapcloseAmt = decrecArapcloseAmt.add(decArapcloseAmt);
                // 下一位客户
                num++;
            }
            BuyAccountrecPojo buyAccountrecPojo = new BuyAccountrecPojo();
            BeanUtils.copyProperties(buyAccountPojo, buyAccountrecPojo);
            // 设置item该月累加账单
            buyAccountrecPojo.setBillopenamount(decrecopenAmt.doubleValue());
            buyAccountrecPojo.setBillinamount(decrecinAmt.doubleValue());
            buyAccountrecPojo.setBilloutamount(decrecoutAmt.doubleValue());
            buyAccountrecPojo.setBillcloseamount(decreccloseAmt.doubleValue());
            // 设置invo该月累加账单
            buyAccountrecPojo.setInvoopenamount(decrecInvoopenAmt.doubleValue());
            buyAccountrecPojo.setInvoinamount(decrecInvoinAmt.doubleValue());
            buyAccountrecPojo.setInvooutamount(decrecInvooutAmt.doubleValue());
            buyAccountrecPojo.setInvocloseamount(decrecInvocloseAmt.doubleValue());
            // 设置arap该月累加账单
            buyAccountrecPojo.setArapopenamount(decrecArapopenAmt.doubleValue());
            buyAccountrecPojo.setArapinamount(decrecArapinAmt.doubleValue());
            buyAccountrecPojo.setArapoutamount(decrecArapoutAmt.doubleValue());
            buyAccountrecPojo.setArapcloseamount(decrecArapcloseAmt.doubleValue());
            this.buyAccountrecService.insert(buyAccountrecPojo);
            return num;
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }

    }

    /**
     * 通过GroupID查询最新单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyAccountPojo getMaxEntityByGroup(String key, String tid) {
        return this.buyAccountMapper.getMaxEntityByGroup(key, tid);
    }

    /**
     * 通过GroupID查询最新单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyAccountPojo getMaxBillEntityByGroup(String key, String tid) {
        BuyAccountPojo busAccountPojo = this.buyAccountMapper.getMaxEntityByGroup(key, tid);
        //读取子表
        busAccountPojo.setItem(buyAccountitemMapper.getList(busAccountPojo.getId(), busAccountPojo.getTenantid()));
        return busAccountPojo;
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */

    @Override
    public List<BuyAccountitemPojo> getMultItemList(QueryParam queryParam) {
        return this.buyAccountMapper.getMultItemList(queryParam);
    }


    // 获取供应商实时应付款报表
    @Override
    public PageInfo<BuyAccountPojo> getNowPageList(QueryParam queryParam, Integer online) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyAccountPojo> lst = this.buyAccountMapper.getNowPageList(queryParam,online);
            PageInfo<BuyAccountPojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public AppWgSupplierPojo getSupplierGeneral(String key, String tid) {
        AppWgSupplierPojo wgPojo = this.buyAccountMapper.getWorkGroupEntity(key, tid);
//        AppWgSupplierPojo wgPojo = new AppWgSupplierPojo();

        // 初始化时间
        Date dtStart = DateUtils.parseDate(DateUtils.dateTimeNow("yyyy-01-01 00:00:00"));
        Date dtEnd = new Date();
        // 查询当前客户之前的销售账单
        BuyAccountPojo buyAccountPojo = this.buyAccountMapper.getMaxEntityByGroup(key, tid);
        if (buyAccountPojo == null) {
            wgPojo.setAccountamount(0D);
            if (DateUtils.getTimestamp(wgPojo.getCreatedate()) < DateUtils.getTimestamp(dtStart)) {
                throw new RuntimeException("请先初始化客户账单");
            }
        } else {
            dtStart = buyAccountPojo.getEnddate(); //  lstAcc.get(0).getEnddate();
            // 写入 对账单金额
            wgPojo.setAccountamount(buyAccountPojo.getBillcloseamount());
        }

        QueryParam queryParam = new QueryParam();
        String strFilter = " and Groupid='" + key + "'";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(1000);
        queryParam.setTenantid(tid);
        List<BuyAccountitemPojo> lstitem = this.buyAccountMapper.pullItemList(queryParam);
        if (lstitem.isEmpty()) {
            wgPojo.setFreeamount(0D);
        } else {
            // RowNum 重排
            Double _inAmount = 0D;
            Double _outAmount = 0D;

            for (int i = 0; i < lstitem.size(); i++) {
                lstitem.get(i).setRownum(i);
                _inAmount += lstitem.get(i).getInamount();
                _outAmount += lstitem.get(i).getOutamount();
            }
            wgPojo.setFreeinamount(_inAmount);
            wgPojo.setFreeoutamount(_outAmount);
            wgPojo.setFreeamount(_inAmount - _outAmount);
        }
        wgPojo.setTotalamount(wgPojo.getAccountamount() + wgPojo.getFreeamount());

        // 逾期发票数
        queryParam = new QueryParam();
        strFilter = " and Groupid='" + key + "'";
        strFilter += " and Buy_Invoice.DisannulMark=0 and Buy_Invoice.Closed=0 and Buy_Invoice.Paid<Buy_Invoice.TaxAmount";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(1000);
        queryParam.setTenantid(tid);
        List<BuyInvoicePojo> lstInvo = this.buyInvoiceMapper.getPageTh(queryParam);
        wgPojo.setTotalinvoice(lstInvo.size());
        wgPojo.setOverdueinvoice(0);
        if (lstInvo.size() > 0) {
            for (BuyInvoicePojo invoPojo : lstInvo) {
                if (DateUtils.getTimestamp(invoPojo.getAimdate()) < DateUtils.getTimestamp(new Date())) {
                    wgPojo.setOverdueinvoice(wgPojo.getOverdueinvoice() + 1);
                }
            }
        }

        // 加入发票管理
//        queryParam = new QueryParam();
//        strFilter = " and id='" + wgPojo.getId() + "'";
//        queryParam.setOrderBy("CreateDate");
//        queryParam.setOrderType(0);
//        queryParam.setFilterstr(strFilter);
//        queryParam.setPageNum(1);
//        queryParam.setPageSize(1);
//        queryParam.setTenantid(tid);
//        List<AppWorkgroupPojo> lst = appWorkgroupMapper.getPageListByRece(queryParam);
//        if (lst.size()>0){
//            wgPojo.setInvoremamount(lst.get(0).getInvoremamount());
//            wgPojo.setSalefreeamount(lst.get(0).getSalefreeamount());
//            wgPojo.setDeporemamount(lst.get(0).getDeporemamount());
//            wgPojo.setSaletotalamount(lst.get(0).getInvoremamount()+lst.get(0).getSalefreeamount()-lst.get(0).getDeporemamount());
//        }
        return wgPojo;
    }




    @Async
    @Override
    @Transactional
    public void batchCreateStart(BuyAccountPojo buyAccountPojo, String uuid) {
        try {
            //设置当前计算任务进度
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100");
            missionMsg.put("msg", "任务开始处理");
            missionMsg.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.saRedisService.setCacheObject(MyConstant.BUYBATCHCREATE_CODE + uuid, missionMsg, 600);
            String tid = buyAccountPojo.getTenantid();
            List<String> lstwgIds = this.buyAccountMapper.getSupplierIds(tid);
            // 改为BigDec
            BigDecimal decrecopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecoutAmt = BigDecimal.valueOf(0);
            BigDecimal decreccloseAmt = BigDecimal.valueOf(0);
            // invo改为BigDec
            BigDecimal decrecInvoopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecInvoinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecInvooutAmt = BigDecimal.valueOf(0);
            BigDecimal decrecInvocloseAmt = BigDecimal.valueOf(0);
            // arap改为BigDec
            BigDecimal decrecArapopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecArapinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecArapoutAmt = BigDecimal.valueOf(0);
            BigDecimal decrecArapcloseAmt = BigDecimal.valueOf(0);
            for (String wgid : lstwgIds) {
                BuyAccountPojo newPojo = new BuyAccountPojo();
                BeanUtils.copyProperties(buyAccountPojo, newPojo);
                newPojo.setGroupid(wgid);
                newPojo.setItem(pullItemList(newPojo));
                // 改为BigDec
                BigDecimal decopenAmt = BigDecimal.valueOf(0);
                BigDecimal decinAmt = BigDecimal.valueOf(0);
                BigDecimal decoutAmt = BigDecimal.valueOf(0);
                BigDecimal deccloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getItem().size() > 0) {
                    decopenAmt = decopenAmt.add(BigDecimal.valueOf(newPojo.getItem().get(0).getOpenamount()));
                    deccloseAmt = deccloseAmt.add(BigDecimal.valueOf(newPojo.getItem().get(newPojo.getItem().size() - 1).getCloseamount()));
                }
                for (BuyAccountitemPojo itemPojo : newPojo.getItem()) {
                    decinAmt = decinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decoutAmt = decoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setBillopenamount(decopenAmt.doubleValue());
                newPojo.setBillinamount(decinAmt.doubleValue());
                newPojo.setBilloutamount(decoutAmt.doubleValue());
                newPojo.setBillcloseamount(deccloseAmt.doubleValue());

                // 2.收货单to发票
                newPojo.setInvo(pullInvoList(newPojo));
                // 改为BigDec
                BigDecimal decInvoopenAmt = BigDecimal.valueOf(0);
                BigDecimal decInvoinAmt = BigDecimal.valueOf(0);
                BigDecimal decInvooutAmt = BigDecimal.valueOf(0);
                BigDecimal decInvocloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getInvo().size() > 0) {
                    decInvoopenAmt = decInvoopenAmt.add(BigDecimal.valueOf(newPojo.getInvo().get(0).getOpenamount()));
                    decInvocloseAmt = decInvocloseAmt.add(BigDecimal.valueOf(newPojo.getInvo().get(newPojo.getInvo().size() - 1).getCloseamount()));
                }
                for (BuyAccountinvoPojo itemPojo : newPojo.getInvo()) {
                    decInvoinAmt = decInvoinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decInvooutAmt = decInvooutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setInvoopenamount(decInvoopenAmt.doubleValue());
                newPojo.setInvoinamount(decInvoinAmt.doubleValue());
                newPojo.setInvooutamount(decInvooutAmt.doubleValue());
                newPojo.setInvocloseamount(decInvocloseAmt.doubleValue());

                // 3.发票to收款
                newPojo.setArap(pullArapList(newPojo));
                // 改为BigDec
                BigDecimal decArapopenAmt = BigDecimal.valueOf(0);
                BigDecimal decArapinAmt = BigDecimal.valueOf(0);
                BigDecimal decArapoutAmt = BigDecimal.valueOf(0);
                BigDecimal decArapcloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getArap().size() > 0) {
                    decArapopenAmt = decArapopenAmt.add(BigDecimal.valueOf(newPojo.getArap().get(0).getOpenamount()));
                    decArapcloseAmt = decArapcloseAmt.add(BigDecimal.valueOf(newPojo.getArap().get(newPojo.getArap().size() - 1).getCloseamount()));
                }
                for (BuyAccountarapPojo itemPojo : newPojo.getArap()) {
                    decArapinAmt = decArapinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decArapoutAmt = decArapoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setArapopenamount(decArapopenAmt.doubleValue());
                newPojo.setArapinamount(decArapinAmt.doubleValue());
                newPojo.setArapoutamount(decArapoutAmt.doubleValue());
                newPojo.setArapcloseamount(decArapcloseAmt.doubleValue());
                insert(newPojo);
                decrecopenAmt = decrecopenAmt.add(decopenAmt);
                decrecinAmt = decrecinAmt.add(decinAmt);
                decrecoutAmt = decrecoutAmt.add(decoutAmt);
                decreccloseAmt = decreccloseAmt.add(deccloseAmt);
                // 当前客户invo累加
                decrecInvoopenAmt = decrecInvoopenAmt.add(decInvoopenAmt);
                decrecInvoinAmt = decrecInvoinAmt.add(decInvoinAmt);
                decrecInvooutAmt = decrecInvooutAmt.add(decInvooutAmt);
                decrecInvocloseAmt = decrecInvocloseAmt.add(decInvocloseAmt);
                // 当前客户arap累加
                decrecArapopenAmt = decrecArapopenAmt.add(decArapopenAmt);
                decrecArapinAmt = decrecArapinAmt.add(decArapinAmt);
                decrecArapoutAmt = decrecArapoutAmt.add(decArapoutAmt);
                decrecArapcloseAmt = decrecArapcloseAmt.add(decArapcloseAmt);
            }
            BuyAccountrecPojo buyAccountrecPojo = new BuyAccountrecPojo();
            BeanUtils.copyProperties(buyAccountPojo, buyAccountrecPojo);
            buyAccountrecPojo.setBillopenamount(decrecopenAmt.doubleValue());
            buyAccountrecPojo.setBillinamount(decrecinAmt.doubleValue());
            buyAccountrecPojo.setBilloutamount(decrecoutAmt.doubleValue());
            buyAccountrecPojo.setBillcloseamount(decreccloseAmt.doubleValue());
            // 设置invo该月累加账单
            buyAccountrecPojo.setInvoopenamount(decrecInvoopenAmt.doubleValue());
            buyAccountrecPojo.setInvoinamount(decrecInvoinAmt.doubleValue());
            buyAccountrecPojo.setInvooutamount(decrecInvooutAmt.doubleValue());
            buyAccountrecPojo.setInvocloseamount(decrecInvocloseAmt.doubleValue());
            // 设置arap该月累加账单
            buyAccountrecPojo.setArapopenamount(decrecArapopenAmt.doubleValue());
            buyAccountrecPojo.setArapinamount(decrecArapinAmt.doubleValue());
            buyAccountrecPojo.setArapoutamount(decrecArapoutAmt.doubleValue());
            buyAccountrecPojo.setArapcloseamount(decrecArapcloseAmt.doubleValue());
            this.buyAccountrecService.insert(buyAccountrecPojo);
            missionMsg.put("code", "200");//任务完成代码
            missionMsg.put("msg", "任务处理完成");
            missionMsg.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.saRedisService.setCacheObject(MyConstant.BUYBATCHCREATE_CODE + uuid, missionMsg, 600);
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }
    }




}
