package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatStoragePojo;

import java.util.List;

/**
 * 仓库管理(MatStorage)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-30 13:33:59
 */
public interface MatStorageService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatStoragePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatStoragePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matStoragePojo 实例对象
     * @return 实例对象
     */
    MatStoragePojo insert(MatStoragePojo matStoragePojo);

    /**
     * 修改数据
     *
     * @param matStoragepojo 实例对象
     * @return 实例对象
     */
    MatStoragePojo update(MatStoragePojo matStoragepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    List<MatStoragePojo> getMachList(String tid);

    // 查询仓库是否被引用
    List<String> getItemCiteBillName(String key, String tid);

    // 检查是否存在相同的仓库编码或仓库名称
    boolean checkCodeOrName(String storecode, String storename, String tid);
}
