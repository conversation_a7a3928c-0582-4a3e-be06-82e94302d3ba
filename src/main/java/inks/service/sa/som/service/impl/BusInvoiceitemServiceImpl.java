package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BusInvoiceitemEntity;
import inks.service.sa.som.domain.pojo.BusInvoiceitemPojo;
import inks.service.sa.som.mapper.BusInvoiceitemMapper;
import inks.service.sa.som.service.BusInvoiceitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 发票项目(BusInvoiceitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-11 10:00:36
 */
@Service("busInvoiceitemService")
public class BusInvoiceitemServiceImpl implements BusInvoiceitemService {
    @Resource
    private BusInvoiceitemMapper busInvoiceitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusInvoiceitemPojo getEntity(String key, String tid) {
        return this.busInvoiceitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusInvoiceitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusInvoiceitemPojo> lst = busInvoiceitemMapper.getPageList(queryParam);
            PageInfo<BusInvoiceitemPojo> pageInfo = new PageInfo<BusInvoiceitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusInvoiceitemPojo> getList(String Pid, String tid) {
        try {
            List<BusInvoiceitemPojo> lst = busInvoiceitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param busInvoiceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusInvoiceitemPojo insert(BusInvoiceitemPojo busInvoiceitemPojo) {
        //初始化item的NULL
        BusInvoiceitemPojo itempojo = this.clearNull(busInvoiceitemPojo);
        BusInvoiceitemEntity busInvoiceitemEntity = new BusInvoiceitemEntity();
        BeanUtils.copyProperties(itempojo, busInvoiceitemEntity);

        busInvoiceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        busInvoiceitemEntity.setRevision(1);  //乐观锁
        this.busInvoiceitemMapper.insert(busInvoiceitemEntity);
        return this.getEntity(busInvoiceitemEntity.getId(), busInvoiceitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busInvoiceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusInvoiceitemPojo update(BusInvoiceitemPojo busInvoiceitemPojo) {
        BusInvoiceitemEntity busInvoiceitemEntity = new BusInvoiceitemEntity();
        BeanUtils.copyProperties(busInvoiceitemPojo, busInvoiceitemEntity);
        this.busInvoiceitemMapper.update(busInvoiceitemEntity);
        return this.getEntity(busInvoiceitemEntity.getId(), busInvoiceitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.busInvoiceitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param busInvoiceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusInvoiceitemPojo clearNull(BusInvoiceitemPojo busInvoiceitemPojo) {
        //初始化NULL字段
        if (busInvoiceitemPojo.getPid() == null) busInvoiceitemPojo.setPid("");
        if (busInvoiceitemPojo.getDeliuid() == null) busInvoiceitemPojo.setDeliuid("");
        if (busInvoiceitemPojo.getDelidate() == null) busInvoiceitemPojo.setDelidate(new Date());
        if (busInvoiceitemPojo.getDelitype() == null) busInvoiceitemPojo.setDelitype("");
        if (busInvoiceitemPojo.getDeliitemid() == null) busInvoiceitemPojo.setDeliitemid("");
        if (busInvoiceitemPojo.getGoodsid() == null) busInvoiceitemPojo.setGoodsid("");
        if (busInvoiceitemPojo.getBillqty() == null) busInvoiceitemPojo.setBillqty(0D);
        if (busInvoiceitemPojo.getQuantity() == null) busInvoiceitemPojo.setQuantity(0D);
        if (busInvoiceitemPojo.getTaxprice() == null) busInvoiceitemPojo.setTaxprice(0D);
        if (busInvoiceitemPojo.getTaxamount() == null) busInvoiceitemPojo.setTaxamount(0D);
        if (busInvoiceitemPojo.getPrice() == null) busInvoiceitemPojo.setPrice(0D);
        if (busInvoiceitemPojo.getAmount() == null) busInvoiceitemPojo.setAmount(0D);
        if (busInvoiceitemPojo.getItemtaxrate() == null) busInvoiceitemPojo.setItemtaxrate(0);
        if (busInvoiceitemPojo.getTaxtotal() == null) busInvoiceitemPojo.setTaxtotal(0D);
        if (busInvoiceitemPojo.getRownum() == null) busInvoiceitemPojo.setRownum(0);
        if (busInvoiceitemPojo.getRemark() == null) busInvoiceitemPojo.setRemark("");
        if (busInvoiceitemPojo.getMachuid() == null) busInvoiceitemPojo.setMachuid("");
        if (busInvoiceitemPojo.getMachitemid() == null) busInvoiceitemPojo.setMachitemid("");
        if (busInvoiceitemPojo.getCustpo() == null) busInvoiceitemPojo.setCustpo("");
        if (busInvoiceitemPojo.getAvgfirstamt() == null) busInvoiceitemPojo.setAvgfirstamt(0D);
        if (busInvoiceitemPojo.getAvglastamt() == null) busInvoiceitemPojo.setAvglastamt(0D);
        if (busInvoiceitemPojo.getCustom1() == null) busInvoiceitemPojo.setCustom1("");
        if (busInvoiceitemPojo.getCustom2() == null) busInvoiceitemPojo.setCustom2("");
        if (busInvoiceitemPojo.getCustom3() == null) busInvoiceitemPojo.setCustom3("");
        if (busInvoiceitemPojo.getCustom4() == null) busInvoiceitemPojo.setCustom4("");
        if (busInvoiceitemPojo.getCustom5() == null) busInvoiceitemPojo.setCustom5("");
        if (busInvoiceitemPojo.getCustom6() == null) busInvoiceitemPojo.setCustom6("");
        if (busInvoiceitemPojo.getCustom7() == null) busInvoiceitemPojo.setCustom7("");
        if (busInvoiceitemPojo.getCustom8() == null) busInvoiceitemPojo.setCustom8("");
        if (busInvoiceitemPojo.getCustom9() == null) busInvoiceitemPojo.setCustom9("");
        if (busInvoiceitemPojo.getCustom10() == null) busInvoiceitemPojo.setCustom10("");
        if (busInvoiceitemPojo.getCustom11() == null) busInvoiceitemPojo.setCustom11("");
        if (busInvoiceitemPojo.getCustom12() == null) busInvoiceitemPojo.setCustom12("");
        if (busInvoiceitemPojo.getCustom13() == null) busInvoiceitemPojo.setCustom13("");
        if (busInvoiceitemPojo.getCustom14() == null) busInvoiceitemPojo.setCustom14("");
        if (busInvoiceitemPojo.getCustom15() == null) busInvoiceitemPojo.setCustom15("");
        if (busInvoiceitemPojo.getCustom16() == null) busInvoiceitemPojo.setCustom16("");
        if (busInvoiceitemPojo.getCustom17() == null) busInvoiceitemPojo.setCustom17("");
        if (busInvoiceitemPojo.getCustom18() == null) busInvoiceitemPojo.setCustom18("");
        if (busInvoiceitemPojo.getTenantid() == null) busInvoiceitemPojo.setTenantid("");
        if (busInvoiceitemPojo.getRevision() == null) busInvoiceitemPojo.setRevision(0);
        return busInvoiceitemPojo;
    }
}
