package inks.service.sa.som.service.impl;

import inks.common.core.domain.MatBomdetailPojo;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.mapper.GoodsBomMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 将openFeign调用的goodsFeignService.getBomDetailByGoodsid
 * 即@GetMapping({"/D91M02B1/getBomDetailByGoodsid"})
 * 移到本服务中
 *
 * @time 2024/4/24 上午11:22
 */

@Service
public class GoodsBomServiceImpl {

    @Resource
    private GoodsBomMapper goodsBomMapper;


    public List<MatBomdetailPojo> getBomDetailByGoodsid(String goodsid, Double qty, String mrpqtyupper, int mrpqtydec, String tid) {
        try {
            // 读取货品信息
//            MatGoodsPojo matGoodsPojo = this.goodsBomMapper.getEntity(goodsid, tid);
            String bomid = this.goodsBomMapper.getBomid(goodsid, tid);
            if (StringUtils.isBlank(bomid)) {
                R.fail("未找到相关标准BOM表");
            }
            List<MatBomdetailPojo> lst = getBomDetail(bomid, qty,mrpqtyupper, mrpqtydec, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    public List<MatBomdetailPojo> getBomDetail(String key, Double qty, String mrpqtyupper, int mrpqtydec, String tid) {
        try {

            List<MatBomdetailPojo> lst = new ArrayList<>();
            //读取主表
            MatBomdetailPojo matBomPojoDB = this.goodsBomMapper.getEntity(key, tid);
            if (matBomPojoDB == null) {
                //throw new BaseBusinessException("标准BOM表丢失");
                return null; // 不再报错，返回null 因为改为要将这条为关联bom的货品本身作为mrpitem计算插入了
            }
            // 加入boot级
            MatBomdetailPojo matBomdetailPojo = new MatBomdetailPojo();
            BeanUtils.copyProperties(matBomPojoDB, matBomdetailPojo);
            matBomdetailPojo.setTreeid(inksSnowflake.getSnowflake().nextIdStr());
            matBomdetailPojo.setBomqty(qty);
            matBomdetailPojo.setGoodsbomid(matBomPojoDB.getId());
            matBomdetailPojo.setLevelnum(1);
            lst.add(matBomdetailPojo);
            // 加入第二层，及子层
            getDetailitem(lst, matBomPojoDB.getId(), matBomdetailPojo.getTreeid(), qty, 2,mrpqtyupper, mrpqtydec, matBomPojoDB.getTenantid());
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /*
 bom明细加入子层
 lst 为总清单；
 key  Bomid;
 TreeParentid: 临时树主键
 qty 生产数量；
 tid 租户
 */
    private List<MatBomdetailPojo> getDetailitem(List<MatBomdetailPojo> lst, String key, String Treeid, Double qty, Integer level, String mrpqtyupper, int mrpqtydec, String tid) {

        //读取bom子表
        List<MatBomdetailPojo> lstdetail = this.goodsBomMapper.getDetailList(key, Treeid, tid);// 有查安全库存Mat_Goods.SafeStock
        if (!lstdetail.isEmpty()) {
            for (MatBomdetailPojo matBomdetailPojo : lstdetail) {
                // 临时树id
                matBomdetailPojo.setTreeid(inksSnowflake.getSnowflake().nextIdStr());
                // 物料数量
                double bomqty = matBomdetailPojo.getSubqty() / matBomdetailPojo.getMainqty() * qty;
                if (matBomdetailPojo.getLossrate() > 0) {
                    bomqty = bomqty * (1 + matBomdetailPojo.getLossrate() / 100);
                }

                // 根据 mrpqtydec 计算小数保留的倍率
                double scaleFactor = Math.pow(10, mrpqtydec);
                // 根据 mrpqtyupper 判断是向上进位还是四舍五入
                bomqty = "true".equalsIgnoreCase(mrpqtyupper)
                        ? Math.ceil(bomqty * scaleFactor) / scaleFactor   // 向上进位处理
                        : Math.round(bomqty * scaleFactor) / scaleFactor; // 四舍五入处理

                matBomdetailPojo.setBomqty(bomqty);
                matBomdetailPojo.setLevelnum(level);
                if (matBomdetailPojo.getGoodsbomid() == null) {
                    matBomdetailPojo.setGoodsbomid("");
                }
                lst.add(matBomdetailPojo);

                // 货品有Bomid，并在10层内
                if (matBomdetailPojo.getGoodsbomid() != null && !matBomdetailPojo.getGoodsbomid().isEmpty() && level < 10) {
                    getDetailitem(lst, matBomdetailPojo.getGoodsbomid(), matBomdetailPojo.getTreeid(), bomqty, level + 1, mrpqtyupper, mrpqtydec, tid);
                }
            }
        }

        return lstdetail;
    }

}
