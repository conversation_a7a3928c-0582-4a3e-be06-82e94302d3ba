package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyVouchercashEntity;
import inks.service.sa.som.domain.pojo.BuyVouchercashPojo;
import inks.service.sa.som.mapper.BuyVouchercashMapper;
import inks.service.sa.som.service.BuyVouchercashService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 现金项目(BuyVouchercash)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-11 15:13:07
 */
@Service("buyVouchercashService")
public class BuyVouchercashServiceImpl implements BuyVouchercashService {
    @Resource
    private BuyVouchercashMapper buyVouchercashMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyVouchercashPojo getEntity(String key,String tid) {
        return this.buyVouchercashMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyVouchercashPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyVouchercashPojo> lst = buyVouchercashMapper.getPageList(queryParam);
            PageInfo<BuyVouchercashPojo> pageInfo = new PageInfo<BuyVouchercashPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BuyVouchercashPojo> getList(String Pid,String tid) { 
        try {
            List<BuyVouchercashPojo> lst = buyVouchercashMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param buyVouchercashPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyVouchercashPojo insert(BuyVouchercashPojo buyVouchercashPojo) {
        //初始化item的NULL
        BuyVouchercashPojo itempojo =this.clearNull(buyVouchercashPojo);
        BuyVouchercashEntity buyVouchercashEntity = new BuyVouchercashEntity(); 
        BeanUtils.copyProperties(itempojo,buyVouchercashEntity);
        
          buyVouchercashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          buyVouchercashEntity.setRevision(1);  //乐观锁      
          this.buyVouchercashMapper.insert(buyVouchercashEntity);
        return this.getEntity(buyVouchercashEntity.getId(),buyVouchercashEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param buyVouchercashPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyVouchercashPojo update(BuyVouchercashPojo buyVouchercashPojo) {
        BuyVouchercashEntity buyVouchercashEntity = new BuyVouchercashEntity(); 
        BeanUtils.copyProperties(buyVouchercashPojo,buyVouchercashEntity);
        this.buyVouchercashMapper.update(buyVouchercashEntity);
        return this.getEntity(buyVouchercashEntity.getId(),buyVouchercashEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.buyVouchercashMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param buyVouchercashPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BuyVouchercashPojo clearNull(BuyVouchercashPojo buyVouchercashPojo){
     //初始化NULL字段
     if(buyVouchercashPojo.getPid()==null) buyVouchercashPojo.setPid("");
     if(buyVouchercashPojo.getCashaccid()==null) buyVouchercashPojo.setCashaccid("");
     if(buyVouchercashPojo.getCashaccname()==null) buyVouchercashPojo.setCashaccname("");
     if(buyVouchercashPojo.getAmount()==null) buyVouchercashPojo.setAmount(0D);
     if(buyVouchercashPojo.getRownum()==null) buyVouchercashPojo.setRownum(0);
     if(buyVouchercashPojo.getRemark()==null) buyVouchercashPojo.setRemark("");
     if(buyVouchercashPojo.getCustom1()==null) buyVouchercashPojo.setCustom1("");
     if(buyVouchercashPojo.getCustom2()==null) buyVouchercashPojo.setCustom2("");
     if(buyVouchercashPojo.getCustom3()==null) buyVouchercashPojo.setCustom3("");
     if(buyVouchercashPojo.getCustom4()==null) buyVouchercashPojo.setCustom4("");
     if(buyVouchercashPojo.getCustom5()==null) buyVouchercashPojo.setCustom5("");
     if(buyVouchercashPojo.getCustom6()==null) buyVouchercashPojo.setCustom6("");
     if(buyVouchercashPojo.getCustom7()==null) buyVouchercashPojo.setCustom7("");
     if(buyVouchercashPojo.getCustom8()==null) buyVouchercashPojo.setCustom8("");
     if(buyVouchercashPojo.getCustom9()==null) buyVouchercashPojo.setCustom9("");
     if(buyVouchercashPojo.getCustom10()==null) buyVouchercashPojo.setCustom10("");
     if(buyVouchercashPojo.getTenantid()==null) buyVouchercashPojo.setTenantid("");
     if(buyVouchercashPojo.getRevision()==null) buyVouchercashPojo.setRevision(0);
     return buyVouchercashPojo;
     }
}
