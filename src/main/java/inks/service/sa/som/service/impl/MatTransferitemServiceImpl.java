package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatTransferitemEntity;
import inks.service.sa.som.domain.pojo.MatTransferitemPojo;
import inks.service.sa.som.mapper.MatTransferitemMapper;
import inks.service.sa.som.service.MatTransferitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 调拨项目(MatTransferitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-15 14:23:17
 */
@Service("matTransferitemService")
public class MatTransferitemServiceImpl implements MatTransferitemService {
    @Resource
    private MatTransferitemMapper matTransferitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatTransferitemPojo getEntity(String key,String tid) {
        return this.matTransferitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatTransferitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatTransferitemPojo> lst = matTransferitemMapper.getPageList(queryParam);
            PageInfo<MatTransferitemPojo> pageInfo = new PageInfo<MatTransferitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatTransferitemPojo> getList(String Pid,String tid) { 
        try {
            List<MatTransferitemPojo> lst = matTransferitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param matTransferitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatTransferitemPojo insert(MatTransferitemPojo matTransferitemPojo) {
        //初始化item的NULL
        MatTransferitemPojo itempojo =this.clearNull(matTransferitemPojo);
        MatTransferitemEntity matTransferitemEntity = new MatTransferitemEntity(); 
        BeanUtils.copyProperties(itempojo,matTransferitemEntity);
        
          matTransferitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          matTransferitemEntity.setRevision(1);  //乐观锁      
          this.matTransferitemMapper.insert(matTransferitemEntity);
        return this.getEntity(matTransferitemEntity.getId(),matTransferitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param matTransferitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatTransferitemPojo update(MatTransferitemPojo matTransferitemPojo) {
        MatTransferitemEntity matTransferitemEntity = new MatTransferitemEntity(); 
        BeanUtils.copyProperties(matTransferitemPojo,matTransferitemEntity);
        this.matTransferitemMapper.update(matTransferitemEntity);
        return this.getEntity(matTransferitemEntity.getId(),matTransferitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.matTransferitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param matTransferitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public MatTransferitemPojo clearNull(MatTransferitemPojo matTransferitemPojo){
     //初始化NULL字段
     if(matTransferitemPojo.getPid()==null) matTransferitemPojo.setPid("");
     if(matTransferitemPojo.getInveid()==null) matTransferitemPojo.setInveid("");
     if(matTransferitemPojo.getGoodsid()==null) matTransferitemPojo.setGoodsid("");
     if(matTransferitemPojo.getItemcode()==null) matTransferitemPojo.setItemcode("");
     if(matTransferitemPojo.getItemname()==null) matTransferitemPojo.setItemname("");
     if(matTransferitemPojo.getItemspec()==null) matTransferitemPojo.setItemspec("");
     if(matTransferitemPojo.getItemunit()==null) matTransferitemPojo.setItemunit("");
     if(matTransferitemPojo.getBatchno()==null) matTransferitemPojo.setBatchno("");
     if(matTransferitemPojo.getPacksn()==null) matTransferitemPojo.setPacksn("");
     if(matTransferitemPojo.getExpidate()==null) matTransferitemPojo.setExpidate(new Date());
     if(matTransferitemPojo.getOutlocation()==null) matTransferitemPojo.setOutlocation("");
     if(matTransferitemPojo.getInlocation()==null) matTransferitemPojo.setInlocation("");
     if(matTransferitemPojo.getQuantity()==null) matTransferitemPojo.setQuantity(0D);
     if(matTransferitemPojo.getRemark()==null) matTransferitemPojo.setRemark("");
     if(matTransferitemPojo.getRownum()==null) matTransferitemPojo.setRownum(0);
     if(matTransferitemPojo.getCustom1()==null) matTransferitemPojo.setCustom1("");
     if(matTransferitemPojo.getCustom2()==null) matTransferitemPojo.setCustom2("");
     if(matTransferitemPojo.getCustom3()==null) matTransferitemPojo.setCustom3("");
     if(matTransferitemPojo.getCustom4()==null) matTransferitemPojo.setCustom4("");
     if(matTransferitemPojo.getCustom5()==null) matTransferitemPojo.setCustom5("");
     if(matTransferitemPojo.getCustom6()==null) matTransferitemPojo.setCustom6("");
     if(matTransferitemPojo.getCustom7()==null) matTransferitemPojo.setCustom7("");
     if(matTransferitemPojo.getCustom8()==null) matTransferitemPojo.setCustom8("");
     if(matTransferitemPojo.getCustom9()==null) matTransferitemPojo.setCustom9("");
     if(matTransferitemPojo.getCustom10()==null) matTransferitemPojo.setCustom10("");
     if(matTransferitemPojo.getTenantid()==null) matTransferitemPojo.setTenantid("");
     if(matTransferitemPojo.getRevision()==null) matTransferitemPojo.setRevision(0);
     return matTransferitemPojo;
     }
}
