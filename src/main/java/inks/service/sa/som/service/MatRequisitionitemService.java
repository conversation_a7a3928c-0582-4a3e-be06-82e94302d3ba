package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatRequisitionitemPojo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 申领项目(MatRequisitionitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-14 14:10:32
 */
public interface MatRequisitionitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatRequisitionitemPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatRequisitionitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatRequisitionitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param matRequisitionitemPojo 实例对象
     * @return 实例对象
     */
    MatRequisitionitemPojo insert(MatRequisitionitemPojo matRequisitionitemPojo);

    /**
     * 修改数据
     *
     * @param matRequisitionitempojo 实例对象
     * @return 实例对象
     */
    MatRequisitionitemPojo update(MatRequisitionitemPojo matRequisitionitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param matRequisitionitempojo 实例对象
     * @return 实例对象
     */
    MatRequisitionitemPojo clearNull(MatRequisitionitemPojo matRequisitionitempojo);


    /**
     * 查询 所有Item
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    List<MatRequisitionitemPojo> getListByWorkitemid(String key,String tid);

    List<Map<String, Object>> getMachListInMachitemids(Set<String> machitemids, String tid);
}
