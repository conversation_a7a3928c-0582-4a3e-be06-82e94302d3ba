package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyAccountarapEntity;
import inks.service.sa.som.domain.pojo.BuyAccountarapPojo;
import inks.service.sa.som.mapper.BuyAccountarapMapper;
import inks.service.sa.som.service.BuyAccountarapService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 发票to付款(BuyAccountarap)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-17 10:52:03
 */
@Service("buyAccountarapService")
public class BuyAccountarapServiceImpl implements BuyAccountarapService {
    @Resource
    private BuyAccountarapMapper buyAccountarapMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyAccountarapPojo getEntity(String key,String tid) {
        return this.buyAccountarapMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyAccountarapPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyAccountarapPojo> lst = buyAccountarapMapper.getPageList(queryParam);
            PageInfo<BuyAccountarapPojo> pageInfo = new PageInfo<BuyAccountarapPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BuyAccountarapPojo> getList(String Pid,String tid) { 
        try {
            List<BuyAccountarapPojo> lst = buyAccountarapMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param buyAccountarapPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyAccountarapPojo insert(BuyAccountarapPojo buyAccountarapPojo) {
        //初始化item的NULL
        BuyAccountarapPojo itempojo =this.clearNull(buyAccountarapPojo);
        BuyAccountarapEntity buyAccountarapEntity = new BuyAccountarapEntity(); 
        BeanUtils.copyProperties(itempojo,buyAccountarapEntity);
          //生成雪花id
          buyAccountarapEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          buyAccountarapEntity.setRevision(1);  //乐观锁      
          this.buyAccountarapMapper.insert(buyAccountarapEntity);
        return this.getEntity(buyAccountarapEntity.getId(),buyAccountarapEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param buyAccountarapPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyAccountarapPojo update(BuyAccountarapPojo buyAccountarapPojo) {
        BuyAccountarapEntity buyAccountarapEntity = new BuyAccountarapEntity(); 
        BeanUtils.copyProperties(buyAccountarapPojo,buyAccountarapEntity);
        this.buyAccountarapMapper.update(buyAccountarapEntity);
        return this.getEntity(buyAccountarapEntity.getId(),buyAccountarapEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.buyAccountarapMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param buyAccountarapPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BuyAccountarapPojo clearNull(BuyAccountarapPojo buyAccountarapPojo){
     //初始化NULL字段
     if(buyAccountarapPojo.getPid()==null) buyAccountarapPojo.setPid("");
     if(buyAccountarapPojo.getDirection()==null) buyAccountarapPojo.setDirection("");
     if(buyAccountarapPojo.getModulecode()==null) buyAccountarapPojo.setModulecode("");
     if(buyAccountarapPojo.getBilltype()==null) buyAccountarapPojo.setBilltype("");
     if(buyAccountarapPojo.getBilldate()==null) buyAccountarapPojo.setBilldate(new Date());
     if(buyAccountarapPojo.getBilltitle()==null) buyAccountarapPojo.setBilltitle("");
     if(buyAccountarapPojo.getBilluid()==null) buyAccountarapPojo.setBilluid("");
     if(buyAccountarapPojo.getBillid()==null) buyAccountarapPojo.setBillid("");
     if(buyAccountarapPojo.getOpenamount()==null) buyAccountarapPojo.setOpenamount(0D);
     if(buyAccountarapPojo.getInamount()==null) buyAccountarapPojo.setInamount(0D);
     if(buyAccountarapPojo.getOutamount()==null) buyAccountarapPojo.setOutamount(0D);
     if(buyAccountarapPojo.getCloseamount()==null) buyAccountarapPojo.setCloseamount(0D);
     if(buyAccountarapPojo.getRownum()==null) buyAccountarapPojo.setRownum(0);
     if(buyAccountarapPojo.getRemark()==null) buyAccountarapPojo.setRemark("");
     if(buyAccountarapPojo.getCustom1()==null) buyAccountarapPojo.setCustom1("");
     if(buyAccountarapPojo.getCustom2()==null) buyAccountarapPojo.setCustom2("");
     if(buyAccountarapPojo.getCustom3()==null) buyAccountarapPojo.setCustom3("");
     if(buyAccountarapPojo.getCustom4()==null) buyAccountarapPojo.setCustom4("");
     if(buyAccountarapPojo.getCustom5()==null) buyAccountarapPojo.setCustom5("");
     if(buyAccountarapPojo.getCustom6()==null) buyAccountarapPojo.setCustom6("");
     if(buyAccountarapPojo.getCustom7()==null) buyAccountarapPojo.setCustom7("");
     if(buyAccountarapPojo.getCustom8()==null) buyAccountarapPojo.setCustom8("");
     if(buyAccountarapPojo.getCustom9()==null) buyAccountarapPojo.setCustom9("");
     if(buyAccountarapPojo.getCustom10()==null) buyAccountarapPojo.setCustom10("");
     if(buyAccountarapPojo.getTenantid()==null) buyAccountarapPojo.setTenantid("");
     if(buyAccountarapPojo.getRevision()==null) buyAccountarapPojo.setRevision(0);
     return buyAccountarapPojo;
     }
}
