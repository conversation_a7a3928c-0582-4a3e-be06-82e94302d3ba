package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatAccessitemEntity;
import inks.service.sa.som.domain.pojo.MatAccessitemPojo;
import inks.service.sa.som.mapper.MatAccessitemMapper;
import inks.service.sa.som.service.MatAccessitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 出入库项目(MatAccessitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-12 13:02:17
 */
@Service("matAccessitemService")
public class MatAccessitemServiceImpl implements MatAccessitemService {
    @Resource
    private MatAccessitemMapper matAccessitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatAccessitemPojo getEntity(String key, String tid) {
        return this.matAccessitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatAccessitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatAccessitemPojo> lst = matAccessitemMapper.getPageList(queryParam);
            PageInfo<MatAccessitemPojo> pageInfo = new PageInfo<MatAccessitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatAccessitemPojo> getList(String Pid, String tid) {
        try {
            List<MatAccessitemPojo> lst = matAccessitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matAccessitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatAccessitemPojo insert(MatAccessitemPojo matAccessitemPojo) {
        //初始化item的NULL
        MatAccessitemPojo itempojo = this.clearNull(matAccessitemPojo);
        MatAccessitemEntity matAccessitemEntity = new MatAccessitemEntity();
        BeanUtils.copyProperties(itempojo, matAccessitemEntity);

        matAccessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matAccessitemEntity.setRevision(1);  //乐观锁
        this.matAccessitemMapper.insert(matAccessitemEntity);
        return this.getEntity(matAccessitemEntity.getId(), matAccessitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matAccessitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatAccessitemPojo update(MatAccessitemPojo matAccessitemPojo) {
        MatAccessitemEntity matAccessitemEntity = new MatAccessitemEntity();
        BeanUtils.copyProperties(matAccessitemPojo, matAccessitemEntity);
        this.matAccessitemMapper.update(matAccessitemEntity);
        return this.getEntity(matAccessitemEntity.getId(), matAccessitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matAccessitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param matAccessitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatAccessitemPojo clearNull(MatAccessitemPojo matAccessitemPojo) {
        //初始化NULL字段
        if (matAccessitemPojo.getPid() == null) matAccessitemPojo.setPid("");
        if (matAccessitemPojo.getGoodsid() == null) matAccessitemPojo.setGoodsid("");
        if (matAccessitemPojo.getQuantity() == null) matAccessitemPojo.setQuantity(0D);
        if (matAccessitemPojo.getPrice() == null) matAccessitemPojo.setPrice(0D);
        if (matAccessitemPojo.getAmount() == null) matAccessitemPojo.setAmount(0D);
        if (matAccessitemPojo.getTaxprice() == null) matAccessitemPojo.setTaxprice(0D);
        if (matAccessitemPojo.getTaxamount() == null) matAccessitemPojo.setTaxamount(0D);
        if (matAccessitemPojo.getItemtaxrate() == null) matAccessitemPojo.setItemtaxrate(0);
        if (matAccessitemPojo.getTaxtotal() == null) matAccessitemPojo.setTaxtotal(0D);
        if (matAccessitemPojo.getRemark() == null) matAccessitemPojo.setRemark("");
        if (matAccessitemPojo.getCiteuid() == null) matAccessitemPojo.setCiteuid("");
        if (matAccessitemPojo.getCiteitemid() == null) matAccessitemPojo.setCiteitemid("");
        if (matAccessitemPojo.getStatecode() == null) matAccessitemPojo.setStatecode("");
        if (matAccessitemPojo.getStatedate() == null) matAccessitemPojo.setStatedate(new Date());
        if (matAccessitemPojo.getRownum() == null) matAccessitemPojo.setRownum(0);
        if (matAccessitemPojo.getLocation() == null) matAccessitemPojo.setLocation("");
        if (matAccessitemPojo.getBatchno() == null) matAccessitemPojo.setBatchno("");
        if (matAccessitemPojo.getPacksn() == null) matAccessitemPojo.setPacksn("");
        if (matAccessitemPojo.getExpidate() == null) matAccessitemPojo.setExpidate(new Date());
        if (matAccessitemPojo.getCustomer() == null) matAccessitemPojo.setCustomer("");
        if (matAccessitemPojo.getCustpo() == null) matAccessitemPojo.setCustpo("");
        if (matAccessitemPojo.getMachuid() == null) matAccessitemPojo.setMachuid("");
        if (matAccessitemPojo.getMachitemid() == null) matAccessitemPojo.setMachitemid("");
        if (matAccessitemPojo.getMachgroupid() == null) matAccessitemPojo.setMachgroupid("");
        if (matAccessitemPojo.getMainplanuid() == null) matAccessitemPojo.setMainplanuid("");
        if (matAccessitemPojo.getMainplanitemid() == null) matAccessitemPojo.setMainplanitemid("");
        if (matAccessitemPojo.getMrpuid() == null) matAccessitemPojo.setMrpuid("");
        if (matAccessitemPojo.getMrpitemid() == null) matAccessitemPojo.setMrpitemid("");
        if (matAccessitemPojo.getInveid() == null) matAccessitemPojo.setInveid("");
        if (matAccessitemPojo.getSkuid() == null) matAccessitemPojo.setSkuid("");
        if (matAccessitemPojo.getAttributejson() == null) matAccessitemPojo.setAttributejson("");
        if (matAccessitemPojo.getWkqtyid() == null) matAccessitemPojo.setWkqtyid("");
        if (matAccessitemPojo.getLabelcodes() == null) matAccessitemPojo.setLabelcodes("");
        if (matAccessitemPojo.getLabelqty() == null) matAccessitemPojo.setLabelqty(0D);
        if (matAccessitemPojo.getSourcetype() == null) matAccessitemPojo.setSourcetype(0);
        if (matAccessitemPojo.getOrderuid() == null) matAccessitemPojo.setOrderuid("");
        if (matAccessitemPojo.getOrderitemid() == null) matAccessitemPojo.setOrderitemid("");
        if (matAccessitemPojo.getOrderno() == null) matAccessitemPojo.setOrderno("");
        if (matAccessitemPojo.getWorkuid() == null) matAccessitemPojo.setWorkuid("");
        if (matAccessitemPojo.getWorkitemid() == null) matAccessitemPojo.setWorkitemid("");
        if (matAccessitemPojo.getSubcuid() == null) matAccessitemPojo.setSubcuid("");
        if (matAccessitemPojo.getSubcitemid() == null) matAccessitemPojo.setSubcitemid("");
        if (matAccessitemPojo.getCustuid() == null) matAccessitemPojo.setCustuid("");
        if (matAccessitemPojo.getCuistitemid() == null) matAccessitemPojo.setCuistitemid("");
        if (matAccessitemPojo.getCustom1() == null) matAccessitemPojo.setCustom1("");
        if (matAccessitemPojo.getCustom2() == null) matAccessitemPojo.setCustom2("");
        if (matAccessitemPojo.getCustom3() == null) matAccessitemPojo.setCustom3("");
        if (matAccessitemPojo.getCustom4() == null) matAccessitemPojo.setCustom4("");
        if (matAccessitemPojo.getCustom5() == null) matAccessitemPojo.setCustom5("");
        if (matAccessitemPojo.getCustom6() == null) matAccessitemPojo.setCustom6("");
        if (matAccessitemPojo.getCustom7() == null) matAccessitemPojo.setCustom7("");
        if (matAccessitemPojo.getCustom8() == null) matAccessitemPojo.setCustom8("");
        if (matAccessitemPojo.getCustom9() == null) matAccessitemPojo.setCustom9("");
        if (matAccessitemPojo.getCustom10() == null) matAccessitemPojo.setCustom10("");
        if (matAccessitemPojo.getTenantid() == null) matAccessitemPojo.setTenantid("");
        if (matAccessitemPojo.getRevision() == null) matAccessitemPojo.setRevision(0);
        return matAccessitemPojo;
    }
}
