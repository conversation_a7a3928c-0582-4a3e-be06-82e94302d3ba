package inks.service.sa.som.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmDepotransfermachPojo;
import inks.service.sa.som.domain.FmDepotransfermachEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 销售订单子表(FmDepotransfermach)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-14 17:21:36
 */
public interface FmDepotransfermachService {


    FmDepotransfermachPojo getEntity(String key);

    PageInfo<FmDepotransfermachPojo> getPageList(QueryParam queryParam);

    List<FmDepotransfermachPojo> getList(String Pid);  

    FmDepotransfermachPojo insert(FmDepotransfermachPojo fmDepotransfermachPojo);

    FmDepotransfermachPojo update(FmDepotransfermachPojo fmDepotransfermachpojo);

    int delete(String key);

    FmDepotransfermachPojo clearNull(FmDepotransfermachPojo fmDepotransfermachpojo);
}
