package inks.service.sa.som.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.pojo.FmExpensesPojo;
import inks.service.sa.som.domain.pojo.FmExpensesitemPojo;
import inks.service.sa.som.domain.pojo.FmExpensesitemdetailPojo;
import inks.service.sa.som.domain.FmExpensesEntity;
import inks.service.sa.som.domain.FmExpensesitemEntity;
import inks.service.sa.som.mapper.FmExpensesMapper;
import inks.service.sa.som.service.FmExpensesService;
import inks.service.sa.som.service.FmExpensesitemService;
import inks.service.sa.som.mapper.FmExpensesitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 费用报销单(FmExpenses)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-26 15:43:17
 */
@Service("fmExpensesService")
public class FmExpensesServiceImpl implements FmExpensesService {
    @Resource
    private FmExpensesMapper fmExpensesMapper;
    
    @Resource
    private FmExpensesitemMapper fmExpensesitemMapper;
    

    @Resource
    private FmExpensesitemService fmExpensesitemService;
    

    @Override
    public FmExpensesPojo getEntity(String key) {
        return this.fmExpensesMapper.getEntity(key);
    }


    @Override
    public PageInfo<FmExpensesitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmExpensesitemdetailPojo> lst = fmExpensesMapper.getPageList(queryParam);
            PageInfo<FmExpensesitemdetailPojo> pageInfo = new PageInfo<FmExpensesitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public FmExpensesPojo getBillEntity(String key) {
       try {
        //读取主表
        FmExpensesPojo fmExpensesPojo = this.fmExpensesMapper.getEntity(key);
        //读取子表
        fmExpensesPojo.setItem(fmExpensesitemMapper.getList(fmExpensesPojo.getId()));
        return fmExpensesPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PageInfo<FmExpensesPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmExpensesPojo> lst = fmExpensesMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(fmExpensesitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<FmExpensesPojo> pageInfo = new PageInfo<FmExpensesPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<FmExpensesPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmExpensesPojo> lst = fmExpensesMapper.getPageTh(queryParam);
            PageInfo<FmExpensesPojo> pageInfo = new PageInfo<FmExpensesPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public FmExpensesPojo insert(FmExpensesPojo fmExpensesPojo) {
        //初始化NULL字段
        cleanNull(fmExpensesPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        FmExpensesEntity fmExpensesEntity = new FmExpensesEntity(); 
        BeanUtils.copyProperties(fmExpensesPojo,fmExpensesEntity);
        //设置id和新建日期
        fmExpensesEntity.setId(id);
        fmExpensesEntity.setRevision(1);  //乐观锁
        //插入主表
        this.fmExpensesMapper.insert(fmExpensesEntity);
        //Item子表处理
        List<FmExpensesitemPojo> lst = fmExpensesPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               //初始化item的NULL
               FmExpensesitemPojo itemPojo =this.fmExpensesitemService.clearNull(lst.get(i));
               FmExpensesitemEntity fmExpensesitemEntity = new FmExpensesitemEntity(); 
               BeanUtils.copyProperties(itemPojo,fmExpensesitemEntity);
               //设置id和Pid
               fmExpensesitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               fmExpensesitemEntity.setPid(id);
               fmExpensesitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.fmExpensesitemMapper.insert(fmExpensesitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(fmExpensesEntity.getId());
    }


    @Override
    @Transactional
    public FmExpensesPojo update(FmExpensesPojo fmExpensesPojo) {
        //主表更改
        FmExpensesEntity fmExpensesEntity = new FmExpensesEntity(); 
        BeanUtils.copyProperties(fmExpensesPojo,fmExpensesEntity);
        this.fmExpensesMapper.update(fmExpensesEntity);
        if (fmExpensesPojo.getItem() != null) {
        //Item子表处理
        List<FmExpensesitemPojo> lst = fmExpensesPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =fmExpensesMapper.getDelItemIds(fmExpensesPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.fmExpensesitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               FmExpensesitemEntity fmExpensesitemEntity = new FmExpensesitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               FmExpensesitemPojo itemPojo =this.fmExpensesitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,fmExpensesitemEntity);
               //设置id和Pid
               fmExpensesitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               fmExpensesitemEntity.setPid(fmExpensesEntity.getId());  // 主表 id
               fmExpensesitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.fmExpensesitemMapper.insert(fmExpensesitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),fmExpensesitemEntity);             
               this.fmExpensesitemMapper.update(fmExpensesitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(fmExpensesEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
       FmExpensesPojo fmExpensesPojo =  this.getBillEntity(key);
        //Item子表处理
        List<FmExpensesitemPojo> lst = fmExpensesPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(int i=0;i<lst.size();i++){
             this.fmExpensesitemMapper.delete(lst.get(i).getId());
            }
        }        
        return this.fmExpensesMapper.delete(key) ;
    }
    

    
    @Override
    @Transactional
    public FmExpensesPojo approval(FmExpensesPojo fmExpensesPojo) {
        //主表更改
        FmExpensesEntity fmExpensesEntity = new FmExpensesEntity();
        BeanUtils.copyProperties(fmExpensesPojo,fmExpensesEntity);
        this.fmExpensesMapper.approval(fmExpensesEntity);
        //返回Bill实例
        return this.getBillEntity(fmExpensesEntity.getId());
    }

    private static void cleanNull(FmExpensesPojo fmExpensesPojo) {
        if(fmExpensesPojo.getRefno()==null) fmExpensesPojo.setRefno("");
        if(fmExpensesPojo.getBilldate()==null) fmExpensesPojo.setBilldate(new Date());
        if(fmExpensesPojo.getBilltype()==null) fmExpensesPojo.setBilltype("");
        if(fmExpensesPojo.getBilltitle()==null) fmExpensesPojo.setBilltitle("");
        if(fmExpensesPojo.getDeptid()==null) fmExpensesPojo.setDeptid("");
        if(fmExpensesPojo.getDeptname()==null) fmExpensesPojo.setDeptname("");
        if(fmExpensesPojo.getBillamount()==null) fmExpensesPojo.setBillamount(0D);
        if(fmExpensesPojo.getAmountupper()==null) fmExpensesPojo.setAmountupper("");
        if(fmExpensesPojo.getAttachcount()==null) fmExpensesPojo.setAttachcount(0);
        if(fmExpensesPojo.getItemcount()==null) fmExpensesPojo.setItemcount(0);
        if(fmExpensesPojo.getAssessor()==null) fmExpensesPojo.setAssessor("");
        if(fmExpensesPojo.getAssessorid()==null) fmExpensesPojo.setAssessorid("");
        if(fmExpensesPojo.getAssessdate()==null) fmExpensesPojo.setAssessdate(new Date());
        if(fmExpensesPojo.getRownum()==null) fmExpensesPojo.setRownum(0);
        if(fmExpensesPojo.getSummary()==null) fmExpensesPojo.setSummary("");
        if(fmExpensesPojo.getCreateby()==null) fmExpensesPojo.setCreateby("");
        if(fmExpensesPojo.getCreatebyid()==null) fmExpensesPojo.setCreatebyid("");
        if(fmExpensesPojo.getCreatedate()==null) fmExpensesPojo.setCreatedate(new Date());
        if(fmExpensesPojo.getLister()==null) fmExpensesPojo.setLister("");
        if(fmExpensesPojo.getListerid()==null) fmExpensesPojo.setListerid("");
        if(fmExpensesPojo.getModifydate()==null) fmExpensesPojo.setModifydate(new Date());
        if(fmExpensesPojo.getCustom1()==null) fmExpensesPojo.setCustom1("");
        if(fmExpensesPojo.getCustom2()==null) fmExpensesPojo.setCustom2("");
        if(fmExpensesPojo.getCustom3()==null) fmExpensesPojo.setCustom3("");
        if(fmExpensesPojo.getCustom4()==null) fmExpensesPojo.setCustom4("");
        if(fmExpensesPojo.getCustom5()==null) fmExpensesPojo.setCustom5("");
        if(fmExpensesPojo.getCustom6()==null) fmExpensesPojo.setCustom6("");
        if(fmExpensesPojo.getCustom7()==null) fmExpensesPojo.setCustom7("");
        if(fmExpensesPojo.getCustom8()==null) fmExpensesPojo.setCustom8("");
        if(fmExpensesPojo.getCustom9()==null) fmExpensesPojo.setCustom9("");
        if(fmExpensesPojo.getCustom10()==null) fmExpensesPojo.setCustom10("");
        if(fmExpensesPojo.getTenantid()==null) fmExpensesPojo.setTenantid("");
        if(fmExpensesPojo.getRevision()==null) fmExpensesPojo.setRevision(0);
   }

}
