package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatAccessitemPojo;

import java.util.List;
/**
 * 出入库项目(MatAccessitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-12 14:38:15
 */
public interface MatAccessitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatAccessitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatAccessitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatAccessitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param matAccessitemPojo 实例对象
     * @return 实例对象
     */
    MatAccessitemPojo insert(MatAccessitemPojo matAccessitemPojo);

    /**
     * 修改数据
     *
     * @param matAccessitempojo 实例对象
     * @return 实例对象
     */
    MatAccessitemPojo update(MatAccessitemPojo matAccessitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param matAccessitempojo 实例对象
     * @return 实例对象
     */
    MatAccessitemPojo clearNull(MatAccessitemPojo matAccessitempojo);
}
