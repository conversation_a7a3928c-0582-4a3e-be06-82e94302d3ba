package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BusReceiptitemEntity;
import inks.service.sa.som.domain.pojo.BusReceiptitemPojo;
import inks.service.sa.som.mapper.BusReceiptitemMapper;
import inks.service.sa.som.service.BusReceiptitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 相关发票(BusReceiptitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-20 13:13:56
 */
@Service("busReceiptitemService")
public class BusReceiptitemServiceImpl implements BusReceiptitemService {
    @Resource
    private BusReceiptitemMapper busReceiptitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusReceiptitemPojo getEntity(String key, String tid) {
        return this.busReceiptitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusReceiptitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusReceiptitemPojo> lst = busReceiptitemMapper.getPageList(queryParam);
            PageInfo<BusReceiptitemPojo> pageInfo = new PageInfo<BusReceiptitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusReceiptitemPojo> getList(String Pid, String tid) {
        try {
            List<BusReceiptitemPojo> lst = busReceiptitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param busReceiptitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusReceiptitemPojo insert(BusReceiptitemPojo busReceiptitemPojo) {
        //初始化item的NULL
        BusReceiptitemPojo itempojo = this.clearNull(busReceiptitemPojo);
        BusReceiptitemEntity busReceiptitemEntity = new BusReceiptitemEntity();
        BeanUtils.copyProperties(itempojo, busReceiptitemEntity);

        busReceiptitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        busReceiptitemEntity.setRevision(1);  //乐观锁
        this.busReceiptitemMapper.insert(busReceiptitemEntity);
        return this.getEntity(busReceiptitemEntity.getId(), busReceiptitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busReceiptitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusReceiptitemPojo update(BusReceiptitemPojo busReceiptitemPojo) {
        BusReceiptitemEntity busReceiptitemEntity = new BusReceiptitemEntity();
        BeanUtils.copyProperties(busReceiptitemPojo, busReceiptitemEntity);
        this.busReceiptitemMapper.update(busReceiptitemEntity);
        return this.getEntity(busReceiptitemEntity.getId(), busReceiptitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.busReceiptitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param busReceiptitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusReceiptitemPojo clearNull(BusReceiptitemPojo busReceiptitemPojo) {
        //初始化NULL字段
        if (busReceiptitemPojo.getPid() == null) busReceiptitemPojo.setPid("");
        if (busReceiptitemPojo.getInvoid() == null) busReceiptitemPojo.setInvoid("");
        if (busReceiptitemPojo.getInvobillcode() == null) busReceiptitemPojo.setInvobillcode("");
        if (busReceiptitemPojo.getInvocode() == null) busReceiptitemPojo.setInvocode("");
        if (busReceiptitemPojo.getInvoamount() == null) busReceiptitemPojo.setInvoamount(0D);
        if (busReceiptitemPojo.getAmount() == null) busReceiptitemPojo.setAmount(0D);
        if (busReceiptitemPojo.getRownum() == null) busReceiptitemPojo.setRownum(0);
        if (busReceiptitemPojo.getRemark() == null) busReceiptitemPojo.setRemark("");
        if (busReceiptitemPojo.getCustom1() == null) busReceiptitemPojo.setCustom1("");
        if (busReceiptitemPojo.getCustom2() == null) busReceiptitemPojo.setCustom2("");
        if (busReceiptitemPojo.getCustom3() == null) busReceiptitemPojo.setCustom3("");
        if (busReceiptitemPojo.getCustom4() == null) busReceiptitemPojo.setCustom4("");
        if (busReceiptitemPojo.getCustom5() == null) busReceiptitemPojo.setCustom5("");
        if (busReceiptitemPojo.getCustom6() == null) busReceiptitemPojo.setCustom6("");
        if (busReceiptitemPojo.getCustom7() == null) busReceiptitemPojo.setCustom7("");
        if (busReceiptitemPojo.getCustom8() == null) busReceiptitemPojo.setCustom8("");
        if (busReceiptitemPojo.getCustom9() == null) busReceiptitemPojo.setCustom9("");
        if (busReceiptitemPojo.getCustom10() == null) busReceiptitemPojo.setCustom10("");
        if (busReceiptitemPojo.getTenantid() == null) busReceiptitemPojo.setTenantid("");
        if (busReceiptitemPojo.getRevision() == null) busReceiptitemPojo.setRevision(0);
        return busReceiptitemPojo;
    }
}
