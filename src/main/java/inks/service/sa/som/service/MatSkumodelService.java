package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatSkumodelPojo;

/**
 * SKU模版(MatSkumodel)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-10 08:49:02
 */
public interface MatSkumodelService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSkumodelPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSkumodelPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matSkumodelPojo 实例对象
     * @return 实例对象
     */
    MatSkumodelPojo insert(MatSkumodelPojo matSkumodelPojo);

    /**
     * 修改数据
     *
     * @param matSkumodelpojo 实例对象
     * @return 实例对象
     */
    MatSkumodelPojo update(MatSkumodelPojo matSkumodelpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSkumodelPojo getEntityByCode(String key, String tid);
}
