package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmPayapplycashPojo;

import java.util.List;
/**
 * 核销原始单据(FmPayapplycash)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-13 21:30:15
 */
public interface FmPayapplycashService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmPayapplycashPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmPayapplycashPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<FmPayapplycashPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param fmPayapplycashPojo 实例对象
     * @return 实例对象
     */
    FmPayapplycashPojo insert(FmPayapplycashPojo fmPayapplycashPojo);

    /**
     * 修改数据
     *
     * @param fmPayapplycashpojo 实例对象
     * @return 实例对象
     */
    FmPayapplycashPojo update(FmPayapplycashPojo fmPayapplycashpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param fmPayapplycashpojo 实例对象
     * @return 实例对象
     */
    FmPayapplycashPojo clearNull(FmPayapplycashPojo fmPayapplycashpojo);
}
