package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatCombinitemEntity;
import inks.service.sa.som.domain.pojo.MatCombinitemPojo;
import inks.service.sa.som.mapper.MatCombinitemMapper;
import inks.service.sa.som.service.MatCombinitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 组合项目(MatCombinitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-19 11:09:45
 */
@Service("matCombinitemService")
public class MatCombinitemServiceImpl implements MatCombinitemService {
    @Resource
    private MatCombinitemMapper matCombinitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatCombinitemPojo getEntity(String key,String tid) {
        return this.matCombinitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatCombinitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatCombinitemPojo> lst = matCombinitemMapper.getPageList(queryParam);
            PageInfo<MatCombinitemPojo> pageInfo = new PageInfo<MatCombinitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatCombinitemPojo> getList(String Pid,String tid) { 
        try {
            List<MatCombinitemPojo> lst = matCombinitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param matCombinitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatCombinitemPojo insert(MatCombinitemPojo matCombinitemPojo) {
        //初始化item的NULL
        MatCombinitemPojo itempojo =this.clearNull(matCombinitemPojo);
        MatCombinitemEntity matCombinitemEntity = new MatCombinitemEntity(); 
        BeanUtils.copyProperties(itempojo,matCombinitemEntity);
        
          matCombinitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          matCombinitemEntity.setRevision(1);  //乐观锁      
          this.matCombinitemMapper.insert(matCombinitemEntity);
        return this.getEntity(matCombinitemEntity.getId(),matCombinitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param matCombinitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatCombinitemPojo update(MatCombinitemPojo matCombinitemPojo) {
        MatCombinitemEntity matCombinitemEntity = new MatCombinitemEntity(); 
        BeanUtils.copyProperties(matCombinitemPojo,matCombinitemEntity);
        this.matCombinitemMapper.update(matCombinitemEntity);
        return this.getEntity(matCombinitemEntity.getId(),matCombinitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.matCombinitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param matCombinitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public MatCombinitemPojo clearNull(MatCombinitemPojo matCombinitemPojo){
     //初始化NULL字段
     if(matCombinitemPojo.getPid()==null) matCombinitemPojo.setPid("");
     if(matCombinitemPojo.getAccesstype()==null) matCombinitemPojo.setAccesstype(0);
     if(matCombinitemPojo.getGoodsid()==null) matCombinitemPojo.setGoodsid("");
     if(matCombinitemPojo.getItemcode()==null) matCombinitemPojo.setItemcode("");
     if(matCombinitemPojo.getItemname()==null) matCombinitemPojo.setItemname("");
     if(matCombinitemPojo.getItemspec()==null) matCombinitemPojo.setItemspec("");
     if(matCombinitemPojo.getItemunit()==null) matCombinitemPojo.setItemunit("");
     if(matCombinitemPojo.getQuantity()==null) matCombinitemPojo.setQuantity(0D);
     if(matCombinitemPojo.getPrice()==null) matCombinitemPojo.setPrice(0D);
     if(matCombinitemPojo.getAmount()==null) matCombinitemPojo.setAmount(0D);
     if(matCombinitemPojo.getRemark()==null) matCombinitemPojo.setRemark("");
     if(matCombinitemPojo.getRownum()==null) matCombinitemPojo.setRownum(0);
     if(matCombinitemPojo.getLocation()==null) matCombinitemPojo.setLocation("");
     if(matCombinitemPojo.getBatchno()==null) matCombinitemPojo.setBatchno("");
     if(matCombinitemPojo.getPacksn()==null) matCombinitemPojo.setPacksn("");
     if(matCombinitemPojo.getExpidate()==null) matCombinitemPojo.setExpidate(new Date());
     if(matCombinitemPojo.getInveid()==null) matCombinitemPojo.setInveid("");
     if(matCombinitemPojo.getCustom1()==null) matCombinitemPojo.setCustom1("");
     if(matCombinitemPojo.getCustom2()==null) matCombinitemPojo.setCustom2("");
     if(matCombinitemPojo.getCustom3()==null) matCombinitemPojo.setCustom3("");
     if(matCombinitemPojo.getCustom4()==null) matCombinitemPojo.setCustom4("");
     if(matCombinitemPojo.getCustom5()==null) matCombinitemPojo.setCustom5("");
     if(matCombinitemPojo.getCustom6()==null) matCombinitemPojo.setCustom6("");
     if(matCombinitemPojo.getCustom7()==null) matCombinitemPojo.setCustom7("");
     if(matCombinitemPojo.getCustom8()==null) matCombinitemPojo.setCustom8("");
     if(matCombinitemPojo.getCustom9()==null) matCombinitemPojo.setCustom9("");
     if(matCombinitemPojo.getCustom10()==null) matCombinitemPojo.setCustom10("");
     if(matCombinitemPojo.getTenantid()==null) matCombinitemPojo.setTenantid("");
     if(matCombinitemPojo.getTenantname()==null) matCombinitemPojo.setTenantname("");
     if(matCombinitemPojo.getRevision()==null) matCombinitemPojo.setRevision(0);
     return matCombinitemPojo;
     }
}
