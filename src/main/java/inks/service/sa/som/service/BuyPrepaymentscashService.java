package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyPrepaymentscashPojo;

import java.util.List;
/**
 * 现金项目(BuyPrepaymentscash)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-11 15:13:34
 */
public interface BuyPrepaymentscashService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyPrepaymentscashPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyPrepaymentscashPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyPrepaymentscashPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param buyPrepaymentscashPojo 实例对象
     * @return 实例对象
     */
    BuyPrepaymentscashPojo insert(BuyPrepaymentscashPojo buyPrepaymentscashPojo);

    /**
     * 修改数据
     *
     * @param buyPrepaymentscashpojo 实例对象
     * @return 实例对象
     */
    BuyPrepaymentscashPojo update(BuyPrepaymentscashPojo buyPrepaymentscashpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param buyPrepaymentscashpojo 实例对象
     * @return 实例对象
     */
    BuyPrepaymentscashPojo clearNull(BuyPrepaymentscashPojo buyPrepaymentscashpojo);
}
