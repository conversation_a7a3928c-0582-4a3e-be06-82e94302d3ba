package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BusInvoicePojo;
import inks.service.sa.som.domain.pojo.BusInvoiceitemPojo;
import inks.service.sa.som.domain.pojo.BusInvoiceitemdetailPojo;

import java.util.List;

/**
 * 销售开票(BusInvoice)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-15 15:30:29
 */
public interface BusInvoiceService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusInvoicePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusInvoiceitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusInvoicePojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusInvoicePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusInvoicePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busInvoicePojo 实例对象
     * @return 实例对象
     */
    BusInvoicePojo insert(BusInvoicePojo busInvoicePojo);

    /**
     * 修改数据
     *
     * @param busInvoicepojo 实例对象
     * @return 实例对象
     */
    BusInvoicePojo update(BusInvoicePojo busInvoicepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param busInvoicePojo 实例对象
     * @return 实例对象
     */
    BusInvoicePojo approval(BusInvoicePojo busInvoicePojo);

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    BusInvoicePojo disannul(String key, Integer type, LoginUser loginUser);

    /**
     * 中止数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    BusInvoicePojo closed(String key,Integer type, LoginUser loginUser);


    // 查询Item是否被引用
    List<String> getCiteBillName(String key, String tid);

    // 拉取某个客户所有待对账项目，送货+扣款
    List<BusInvoiceitemPojo> pullItem(QueryParam queryParam,String groupid);
}
