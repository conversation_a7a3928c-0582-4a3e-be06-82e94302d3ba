package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyPrepaymentsitemEntity;
import inks.service.sa.som.domain.pojo.BuyPrepaymentsitemPojo;
import inks.service.sa.som.mapper.BuyPrepaymentsitemMapper;
import inks.service.sa.som.service.BuyPrepaymentsitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 预付款项目(BuyPrepaymentsitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-11 15:13:48
 */
@Service("buyPrepaymentsitemService")
public class BuyPrepaymentsitemServiceImpl implements BuyPrepaymentsitemService {
    @Resource
    private BuyPrepaymentsitemMapper buyPrepaymentsitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyPrepaymentsitemPojo getEntity(String key,String tid) {
        return this.buyPrepaymentsitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyPrepaymentsitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyPrepaymentsitemPojo> lst = buyPrepaymentsitemMapper.getPageList(queryParam);
            PageInfo<BuyPrepaymentsitemPojo> pageInfo = new PageInfo<BuyPrepaymentsitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BuyPrepaymentsitemPojo> getList(String Pid,String tid) { 
        try {
            List<BuyPrepaymentsitemPojo> lst = buyPrepaymentsitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param buyPrepaymentsitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyPrepaymentsitemPojo insert(BuyPrepaymentsitemPojo buyPrepaymentsitemPojo) {
        //初始化item的NULL
        BuyPrepaymentsitemPojo itempojo =this.clearNull(buyPrepaymentsitemPojo);
        BuyPrepaymentsitemEntity buyPrepaymentsitemEntity = new BuyPrepaymentsitemEntity(); 
        BeanUtils.copyProperties(itempojo,buyPrepaymentsitemEntity);
        
          buyPrepaymentsitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          buyPrepaymentsitemEntity.setRevision(1);  //乐观锁      
          this.buyPrepaymentsitemMapper.insert(buyPrepaymentsitemEntity);
        return this.getEntity(buyPrepaymentsitemEntity.getId(),buyPrepaymentsitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param buyPrepaymentsitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyPrepaymentsitemPojo update(BuyPrepaymentsitemPojo buyPrepaymentsitemPojo) {
        BuyPrepaymentsitemEntity buyPrepaymentsitemEntity = new BuyPrepaymentsitemEntity(); 
        BeanUtils.copyProperties(buyPrepaymentsitemPojo,buyPrepaymentsitemEntity);
        this.buyPrepaymentsitemMapper.update(buyPrepaymentsitemEntity);
        return this.getEntity(buyPrepaymentsitemEntity.getId(),buyPrepaymentsitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.buyPrepaymentsitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param buyPrepaymentsitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BuyPrepaymentsitemPojo clearNull(BuyPrepaymentsitemPojo buyPrepaymentsitemPojo){
     //初始化NULL字段
     if(buyPrepaymentsitemPojo.getPid()==null) buyPrepaymentsitemPojo.setPid("");
     if(buyPrepaymentsitemPojo.getOrderbillid()==null) buyPrepaymentsitemPojo.setOrderbillid("");
     if(buyPrepaymentsitemPojo.getOrderbillcode()==null) buyPrepaymentsitemPojo.setOrderbillcode("");
     if(buyPrepaymentsitemPojo.getFinishbillid()==null) buyPrepaymentsitemPojo.setFinishbillid("");
     if(buyPrepaymentsitemPojo.getFinishbillcode()==null) buyPrepaymentsitemPojo.setFinishbillcode("");
     if(buyPrepaymentsitemPojo.getBilltaxamount()==null) buyPrepaymentsitemPojo.setBilltaxamount(0D);
     if(buyPrepaymentsitemPojo.getAmount()==null) buyPrepaymentsitemPojo.setAmount(0D);
     if(buyPrepaymentsitemPojo.getRownum()==null) buyPrepaymentsitemPojo.setRownum(0);
     if(buyPrepaymentsitemPojo.getRemark()==null) buyPrepaymentsitemPojo.setRemark("");
     if(buyPrepaymentsitemPojo.getCustom1()==null) buyPrepaymentsitemPojo.setCustom1("");
     if(buyPrepaymentsitemPojo.getCustom2()==null) buyPrepaymentsitemPojo.setCustom2("");
     if(buyPrepaymentsitemPojo.getCustom3()==null) buyPrepaymentsitemPojo.setCustom3("");
     if(buyPrepaymentsitemPojo.getCustom4()==null) buyPrepaymentsitemPojo.setCustom4("");
     if(buyPrepaymentsitemPojo.getCustom5()==null) buyPrepaymentsitemPojo.setCustom5("");
     if(buyPrepaymentsitemPojo.getCustom6()==null) buyPrepaymentsitemPojo.setCustom6("");
     if(buyPrepaymentsitemPojo.getCustom7()==null) buyPrepaymentsitemPojo.setCustom7("");
     if(buyPrepaymentsitemPojo.getCustom8()==null) buyPrepaymentsitemPojo.setCustom8("");
     if(buyPrepaymentsitemPojo.getCustom9()==null) buyPrepaymentsitemPojo.setCustom9("");
     if(buyPrepaymentsitemPojo.getCustom10()==null) buyPrepaymentsitemPojo.setCustom10("");
     if(buyPrepaymentsitemPojo.getTenantid()==null) buyPrepaymentsitemPojo.setTenantid("");
     if(buyPrepaymentsitemPojo.getRevision()==null) buyPrepaymentsitemPojo.setRevision(0);
     return buyPrepaymentsitemPojo;
     }
}
