package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmCashcarryoveritemPojo;

import java.util.List;
/**
 * 对账明细(FmCashcarryoveritem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-09-26 19:41:35
 */
public interface FmCashcarryoveritemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCashcarryoveritemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmCashcarryoveritemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<FmCashcarryoveritemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param fmCashcarryoveritemPojo 实例对象
     * @return 实例对象
     */
    FmCashcarryoveritemPojo insert(FmCashcarryoveritemPojo fmCashcarryoveritemPojo);

    /**
     * 修改数据
     *
     * @param fmCashcarryoveritempojo 实例对象
     * @return 实例对象
     */
    FmCashcarryoveritemPojo update(FmCashcarryoveritemPojo fmCashcarryoveritempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param fmCashcarryoveritempojo 实例对象
     * @return 实例对象
     */
    FmCashcarryoveritemPojo clearNull(FmCashcarryoveritemPojo fmCashcarryoveritempojo);
}
