package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatCarryoverPojo;
import inks.service.sa.som.domain.pojo.MatCarryoveritemPojo;
import inks.service.sa.som.domain.pojo.MatCarryoveritemdetailPojo;

import java.util.List;
import java.util.Map;

/**
 * 仓库结转(MatCarryover)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-06 11:24:00
 */
public interface MatCarryoverService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCarryoverPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatCarryoveritemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCarryoverPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatCarryoverPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatCarryoverPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matCarryoverPojo 实例对象
     * @return 实例对象
     */
    MatCarryoverPojo insert(MatCarryoverPojo matCarryoverPojo);

    /**
     * 修改数据
     *
     * @param matCarryoverpojo 实例对象
     * @return 实例对象
     */
    MatCarryoverPojo update(MatCarryoverPojo matCarryoverpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    /**
     * 新增数据
     *
     * @param matCarryoverPojo 实例对象
     * @return 实例对象
     */
    MatCarryoverPojo createCarry(MatCarryoverPojo matCarryoverPojo);

    // 批量生产账单
    int batchCreate(MatCarryoverPojo matCarryoverPojo);


    /**
     * 新增数据
     *
     * @param matCarryoverPojo 实例对象
     * @return 实例对象
     */
    List<MatCarryoveritemPojo> pullItemList(MatCarryoverPojo matCarryoverPojo);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCarryoverPojo getMaxBillEntityByStore(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    List<MatCarryoveritemPojo> getMultItemList(QueryParam queryParam);

    // 获取供应商实时应付款报表
    PageInfo<MatCarryoverPojo> getNowPageList(QueryParam queryParam);

    void batchCreateStart(MatCarryoverPojo matCarryoverPojo, String uuid);


}
