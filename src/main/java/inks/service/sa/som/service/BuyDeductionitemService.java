package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyDeductionitemPojo;

import java.util.List;
/**
 * 采购扣款Item(BuyDeductionitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-06 20:36:08
 */
public interface BuyDeductionitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyDeductionitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyDeductionitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyDeductionitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param buyDeductionitemPojo 实例对象
     * @return 实例对象
     */
    BuyDeductionitemPojo insert(BuyDeductionitemPojo buyDeductionitemPojo);

    /**
     * 修改数据
     *
     * @param buyDeductionitempojo 实例对象
     * @return 实例对象
     */
    BuyDeductionitemPojo update(BuyDeductionitemPojo buyDeductionitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param buyDeductionitempojo 实例对象
     * @return 实例对象
     */
    BuyDeductionitemPojo clearNull(BuyDeductionitemPojo buyDeductionitempojo);
}
