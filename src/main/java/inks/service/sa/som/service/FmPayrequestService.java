package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmPayrequestPojo;
import inks.service.sa.som.domain.pojo.FmPayrequestitemdetailPojo;

/**
 * 付款申请单(FmPayrequest)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-07 13:31:28
 */
public interface FmPayrequestService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmPayrequestPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmPayrequestitemdetailPojo> getPageList(QueryParam queryParam);

 /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmPayrequestPojo getBillEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmPayrequestPojo> getBillList(QueryParam queryParam);
    
        /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmPayrequestPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param fmPayrequestPojo 实例对象
     * @return 实例对象
     */
    FmPayrequestPojo insert(FmPayrequestPojo fmPayrequestPojo);

    /**
     * 修改数据
     *
     * @param fmPayrequestpojo 实例对象
     * @return 实例对象
     */
    FmPayrequestPojo update(FmPayrequestPojo fmPayrequestpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

                                                                                                                                                                     /**
     * 审核数据
     *
     * @param fmPayrequestPojo 实例对象
     * @return 实例对象
     */
     FmPayrequestPojo approval(FmPayrequestPojo fmPayrequestPojo);
                                                                                                              }
