package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatInvenoteitemEntity;
import inks.service.sa.som.domain.pojo.MatInvenoteitemPojo;
import inks.service.sa.som.mapper.MatInvenoteitemMapper;
import inks.service.sa.som.service.MatInvenoteitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 盘点项目(MatInvenoteitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-13 09:25:16
 */
@Service("matInvenoteitemService")
public class MatInvenoteitemServiceImpl implements MatInvenoteitemService {
    @Resource
    private MatInvenoteitemMapper matInvenoteitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatInvenoteitemPojo getEntity(String key, String tid) {
        return this.matInvenoteitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatInvenoteitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatInvenoteitemPojo> lst = matInvenoteitemMapper.getPageList(queryParam);
            PageInfo<MatInvenoteitemPojo> pageInfo = new PageInfo<MatInvenoteitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatInvenoteitemPojo> getList(String Pid, String tid) {
        try {
            List<MatInvenoteitemPojo> lst = matInvenoteitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matInvenoteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatInvenoteitemPojo insert(MatInvenoteitemPojo matInvenoteitemPojo) {
        //初始化item的NULL
        MatInvenoteitemPojo itempojo = this.clearNull(matInvenoteitemPojo);
        MatInvenoteitemEntity matInvenoteitemEntity = new MatInvenoteitemEntity();
        BeanUtils.copyProperties(itempojo, matInvenoteitemEntity);

        matInvenoteitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matInvenoteitemEntity.setRevision(1);  //乐观锁
        this.matInvenoteitemMapper.insert(matInvenoteitemEntity);
        return this.getEntity(matInvenoteitemEntity.getId(), matInvenoteitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matInvenoteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatInvenoteitemPojo update(MatInvenoteitemPojo matInvenoteitemPojo) {
        MatInvenoteitemEntity matInvenoteitemEntity = new MatInvenoteitemEntity();
        BeanUtils.copyProperties(matInvenoteitemPojo, matInvenoteitemEntity);
        this.matInvenoteitemMapper.update(matInvenoteitemEntity);
        return this.getEntity(matInvenoteitemEntity.getId(), matInvenoteitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matInvenoteitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param matInvenoteitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatInvenoteitemPojo clearNull(MatInvenoteitemPojo matInvenoteitemPojo) {
        //初始化NULL字段
        if (matInvenoteitemPojo.getPid() == null) matInvenoteitemPojo.setPid("");
        if (matInvenoteitemPojo.getInveid() == null) matInvenoteitemPojo.setInveid("");
        if (matInvenoteitemPojo.getGoodsid() == null) matInvenoteitemPojo.setGoodsid("");
        if (matInvenoteitemPojo.getItemcode() == null) matInvenoteitemPojo.setItemcode("");
        if (matInvenoteitemPojo.getItemname() == null) matInvenoteitemPojo.setItemname("");
        if (matInvenoteitemPojo.getItemspec() == null) matInvenoteitemPojo.setItemspec("");
        if (matInvenoteitemPojo.getItemunit() == null) matInvenoteitemPojo.setItemunit("");
        if (matInvenoteitemPojo.getQuantity() == null) matInvenoteitemPojo.setQuantity(0D);
        if (matInvenoteitemPojo.getPrice() == null) matInvenoteitemPojo.setPrice(0D);
        if (matInvenoteitemPojo.getAmount() == null) matInvenoteitemPojo.setAmount(0D);
        if (matInvenoteitemPojo.getLocation() == null) matInvenoteitemPojo.setLocation("");
        if (matInvenoteitemPojo.getBatchno() == null) matInvenoteitemPojo.setBatchno("");
        if (matInvenoteitemPojo.getPacksn() == null) matInvenoteitemPojo.setPacksn("");
        if (matInvenoteitemPojo.getSkuid() == null) matInvenoteitemPojo.setSkuid("");
        if (matInvenoteitemPojo.getExpidate() == null) matInvenoteitemPojo.setExpidate(new Date());
        if (matInvenoteitemPojo.getRownum() == null) matInvenoteitemPojo.setRownum(0);
        if (matInvenoteitemPojo.getEnduid() == null) matInvenoteitemPojo.setEnduid("");
        if (matInvenoteitemPojo.getEndinuid() == null) matInvenoteitemPojo.setEndinuid("");
        if (matInvenoteitemPojo.getEndindate() == null) matInvenoteitemPojo.setEndindate(new Date());
        if (matInvenoteitemPojo.getEndoutuid() == null) matInvenoteitemPojo.setEndoutuid("");
        if (matInvenoteitemPojo.getEndoutdate() == null) matInvenoteitemPojo.setEndoutdate(new Date());
        if (matInvenoteitemPojo.getCurrqty() == null) matInvenoteitemPojo.setCurrqty(0D);
        if (matInvenoteitemPojo.getCurramt() == null) matInvenoteitemPojo.setCurramt(0D);
        if (matInvenoteitemPojo.getOverflowqty() == null) matInvenoteitemPojo.setOverflowqty(0D);
        if (matInvenoteitemPojo.getOverflowamt() == null) matInvenoteitemPojo.setOverflowamt(0D);
        if (matInvenoteitemPojo.getFinishqty() == null) matInvenoteitemPojo.setFinishqty(0D);
        if (matInvenoteitemPojo.getFinishamt() == null) matInvenoteitemPojo.setFinishamt(0D);
        if (matInvenoteitemPojo.getRemark() == null) matInvenoteitemPojo.setRemark("");
        if (matInvenoteitemPojo.getCustom1() == null) matInvenoteitemPojo.setCustom1("");
        if (matInvenoteitemPojo.getCustom2() == null) matInvenoteitemPojo.setCustom2("");
        if (matInvenoteitemPojo.getCustom3() == null) matInvenoteitemPojo.setCustom3("");
        if (matInvenoteitemPojo.getCustom4() == null) matInvenoteitemPojo.setCustom4("");
        if (matInvenoteitemPojo.getCustom5() == null) matInvenoteitemPojo.setCustom5("");
        if (matInvenoteitemPojo.getCustom6() == null) matInvenoteitemPojo.setCustom6("");
        if (matInvenoteitemPojo.getCustom7() == null) matInvenoteitemPojo.setCustom7("");
        if (matInvenoteitemPojo.getCustom8() == null) matInvenoteitemPojo.setCustom8("");
        if (matInvenoteitemPojo.getCustom9() == null) matInvenoteitemPojo.setCustom9("");
        if (matInvenoteitemPojo.getCustom10() == null) matInvenoteitemPojo.setCustom10("");
        if (matInvenoteitemPojo.getTenantid() == null) matInvenoteitemPojo.setTenantid("");
        if (matInvenoteitemPojo.getTenantidname() == null) matInvenoteitemPojo.setTenantidname("");
        if (matInvenoteitemPojo.getRevision() == null) matInvenoteitemPojo.setRevision(0);
        return matInvenoteitemPojo;
    }
}
