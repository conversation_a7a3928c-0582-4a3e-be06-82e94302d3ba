package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.MatBomdetailPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.som.domain.WkMrpEntity;
import inks.service.sa.som.domain.WkMrpitemEntity;
import inks.service.sa.som.domain.WkMrpobjEntity;
import inks.service.sa.som.domain.constant.MyConstant;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.domain.vo.WkMrpitemAndPatentVO;
import inks.service.sa.som.mapper.WkMrpMapper;
import inks.service.sa.som.mapper.WkMrpitemMapper;
import inks.service.sa.som.mapper.WkMrplogMapper;
import inks.service.sa.som.mapper.WkMrpobjMapper;
import inks.service.sa.som.service.WkMrpService;
import inks.service.sa.som.service.WkMrpitemService;
import inks.service.sa.som.service.WkMrplogService;
import inks.service.sa.som.service.WkMrpobjService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * MRP运算(WkMrp)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-14 21:00:35
 */
@Service("wkMrpService")
public class WkMrpServiceImpl implements WkMrpService {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(WkMrpServiceImpl.class);
    @Resource
    private WkMrpMapper wkMrpMapper;
    @Resource
    private WkMrpitemMapper wkMrpitemMapper;
    /**
     * 服务对象Item
     */
    @Resource
    private WkMrpitemService wkMrpitemService;
    @Resource
    private WkMrpobjMapper wkMrpobjMapper;
    @Resource
    private WkMrplogMapper wkMrplogMapper;
    @Resource
    private WkMrplogService wkMrplogService;
    /**
     * 服务对象Item
     */
    @Resource
    private WkMrpobjService wkMrpobjService;
    @Resource
    private GoodsBomServiceImpl goodsBomService;
    /**
     * 引用Token服务
     */
    @Resource
    private SaRedisService saRedisService;

    //    --------------------------------------------------异步--------------------------------------
    @Resource
    private GoodsBomServiceImpl goodsBomServiceImpl;
    @Resource
    private Executor threadPoolExecutor;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkMrpPojo getEntity(String key, String tid) {
        return this.wkMrpMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMrpitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMrpitemdetailPojo> lst = wkMrpMapper.getPageList(queryParam);
            PageInfo<WkMrpitemdetailPojo> pageInfo = new PageInfo<WkMrpitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
    @Override
    public PageInfo<WkMrpobjdetailPojo> getObjPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMrpobjdetailPojo> lst = wkMrpMapper.getObjPageList(queryParam);
            PageInfo<WkMrpobjdetailPojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //传入key(即objid)：根据objid,去查无children的item行(无bomid)，查出来的行根据itemparentid查到父亲的货品加pnl尺寸，父亲货品为pgoods。\n" +
    //        "返回的格式obj内容是主表，子表为查出来的list。
    @Override
    public WkMrpobjdetailPojo getMatListAndParent(String key, String tid) {
        // obj
        WkMrpobjdetailPojo wkMrpobjdetailPojo = wkMrpobjMapper.getEntityDetail(key, tid);
        // 关联的无children的item行(无bomid)
        List<String> objids = new ArrayList<>();
        objids.add(key);
        List<WkMrpitemAndPatentVO> mrpItemListNoneBomid = wkMrpitemMapper.getListByMrpObjIdNoneBomid(objids, tid);
        for (WkMrpitemAndPatentVO wkMrpitemAndPatentVO : mrpItemListNoneBomid) {
            String itemparentid = wkMrpitemAndPatentVO.getItemparentid();
            WkMrpitemPojo mrpItemParent = wkMrpitemMapper.getEntity(itemparentid, tid);
            //    //  关联的父级mrpitem的货品信息
            //    private String pgoodsid;
            //    private String pgoodsupid;
            //    private String pgoodsname;
            //    private String pgoodsspec;
            //    private String pgoodsunit;
            //    private String ppcsx;
            //    private String ppcsy;
            //    private String psetx;
            //    private String psety;
            //    private String pset2pcs;
            //    private String ppnlx;
            //    private String ppnly;
            //    private String ppnl2pcs;
            //    private String psizeunit;
            //    private String pgoodsgroupname;
            wkMrpitemAndPatentVO.setPgoodsid(mrpItemParent.getGoodsid());
            wkMrpitemAndPatentVO.setPgoodsuid(mrpItemParent.getGoodsuid());
            wkMrpitemAndPatentVO.setPgoodsname(mrpItemParent.getGoodsname());
            wkMrpitemAndPatentVO.setPgoodsspec(mrpItemParent.getGoodsspec());
            wkMrpitemAndPatentVO.setPgoodsunit(mrpItemParent.getGoodsunit());
            wkMrpitemAndPatentVO.setPpcsx(mrpItemParent.getPcsx());
            wkMrpitemAndPatentVO.setPpcsy(mrpItemParent.getPcsy());
            wkMrpitemAndPatentVO.setPsetx(mrpItemParent.getSetx());
            wkMrpitemAndPatentVO.setPsety(mrpItemParent.getSety());
            wkMrpitemAndPatentVO.setPset2pcs(mrpItemParent.getSet2pcs());
            wkMrpitemAndPatentVO.setPpnlx(mrpItemParent.getPnlx());
            wkMrpitemAndPatentVO.setPpnly(mrpItemParent.getPnly());
            wkMrpitemAndPatentVO.setPpnl2pcs(mrpItemParent.getPnl2pcs());
            wkMrpitemAndPatentVO.setPgoodsgroupname(mrpItemParent.getGoodsgroupname());
            wkMrpitemAndPatentVO.setPneedqty(mrpItemParent.getNeedqty());

        }
        wkMrpobjdetailPojo.setItem(mrpItemListNoneBomid);

        return wkMrpobjdetailPojo;
    }


    //传入mrp主表的ids：拿到所有obj，循环根据objid,去查无children的item行(无bomid)，查出来的行根据itemparentid查到父亲的货品加pnl尺寸，父亲货品为pgoods。\n" +
    //        "返回的格式obj第一个内容是主表，子表为查出来的合并。
    @Override
    public WkMrpobjdetailPojo getMatListAndParentByids(List<String> mrpids, String tid) {
        List<String> mrpObjids = wkMrpobjMapper.getObjidsByMrpids(mrpids, tid);
        // 第一个objid作为最终返回的主表
        String firstObjid = mrpObjids.get(0);
        WkMrpobjdetailPojo wkMrpobjdetailPojo = wkMrpobjMapper.getEntityDetail(firstObjid, tid);
        // 关联的无children的item行(无bomid)
        List<WkMrpitemAndPatentVO> mrpItemListNoneBomid = wkMrpitemMapper.getListByMrpObjIdNoneBomid(mrpObjids, tid);
        if (CollectionUtils.isEmpty(mrpItemListNoneBomid)) {
            throw new BaseBusinessException("无Item数据");
        }
        for (WkMrpitemAndPatentVO wkMrpitemAndPatentVO : mrpItemListNoneBomid) {
            String itemparentid = wkMrpitemAndPatentVO.getItemparentid();
            WkMrpitemPojo mrpItemParent = wkMrpitemMapper.getEntity(itemparentid, tid);
            //    //  关联的父级mrpitem的货品信息
            //    private String pgoodsid;
            //    private String pgoodsupid;
            //    private String pgoodsname;
            //    private String pgoodsspec;
            //    private String pgoodsunit;
            //    private String ppcsx;
            //    private String ppcsy;
            //    private String psetx;
            //    private String psety;
            //    private String pset2pcs;
            //    private String ppnlx;
            //    private String ppnly;
            //    private String ppnl2pcs;
            //    private String psizeunit;
            //    private String pgoodsgroupname;
            wkMrpitemAndPatentVO.setPgoodsid(mrpItemParent.getGoodsid());
            wkMrpitemAndPatentVO.setPgoodsuid(mrpItemParent.getGoodsuid());
            wkMrpitemAndPatentVO.setPgoodsname(mrpItemParent.getGoodsname());
            wkMrpitemAndPatentVO.setPgoodsspec(mrpItemParent.getGoodsspec());
            wkMrpitemAndPatentVO.setPgoodsunit(mrpItemParent.getGoodsunit());
            wkMrpitemAndPatentVO.setPpcsx(mrpItemParent.getPcsx());
            wkMrpitemAndPatentVO.setPpcsy(mrpItemParent.getPcsy());
            wkMrpitemAndPatentVO.setPsetx(mrpItemParent.getSetx());
            wkMrpitemAndPatentVO.setPsety(mrpItemParent.getSety());
            wkMrpitemAndPatentVO.setPset2pcs(mrpItemParent.getSet2pcs());
            wkMrpitemAndPatentVO.setPpnlx(mrpItemParent.getPnlx());
            wkMrpitemAndPatentVO.setPpnly(mrpItemParent.getPnly());
            wkMrpitemAndPatentVO.setPpnl2pcs(mrpItemParent.getPnl2pcs());
            wkMrpitemAndPatentVO.setPgoodsgroupname(mrpItemParent.getGoodsgroupname());
            wkMrpitemAndPatentVO.setPneedqty(mrpItemParent.getNeedqty());

        }
        wkMrpobjdetailPojo.setItem(mrpItemListNoneBomid);

        return wkMrpobjdetailPojo;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public WkMrpPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            WkMrpPojo wkMrpPojo = this.wkMrpMapper.getEntity(key, tid);
            String id = wkMrpPojo.getId();
            //读取子表
            wkMrpPojo.setItem(wkMrpitemMapper.getList(id, tid));
            //读取子表
            wkMrpPojo.setObj(wkMrpobjMapper.getList(id, tid));
            //读取日志子表
            wkMrpPojo.setLog(wkMrplogMapper.getList(id, tid));
            return wkMrpPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMrpPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMrpPojo> lst = wkMrpMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表、obj子表、log子表
            for (WkMrpPojo wkMrpPojo : lst) {
                wkMrpPojo.setItem(wkMrpitemMapper.getList(wkMrpPojo.getId(), wkMrpPojo.getTenantid()));
                wkMrpPojo.setObj(wkMrpobjMapper.getList(wkMrpPojo.getId(), wkMrpPojo.getTenantid()));
                wkMrpPojo.setLog(wkMrplogMapper.getList(wkMrpPojo.getId(), wkMrpPojo.getTenantid()));
            }
            return new PageInfo<WkMrpPojo>(lst);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<WkMrpPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<WkMrpPojo> lst = wkMrpMapper.getPageTh(queryParam);
            PageInfo<WkMrpPojo> pageInfo = new PageInfo<WkMrpPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param wkMrpPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkMrpPojo insert(WkMrpPojo wkMrpPojo) {
//初始化NULL字段
        if (wkMrpPojo.getRefno() == null) wkMrpPojo.setRefno("");
        if (wkMrpPojo.getBilltype() == null) wkMrpPojo.setBilltype("");
        if (wkMrpPojo.getBilldate() == null) wkMrpPojo.setBilldate(new Date());
        if (wkMrpPojo.getMrpdate() == null) wkMrpPojo.setMrpdate(new Date());
        if (wkMrpPojo.getBilltitle() == null) wkMrpPojo.setBilltitle("");
        if (wkMrpPojo.getOperator() == null) wkMrpPojo.setOperator("");
        if (wkMrpPojo.getSummary() == null) wkMrpPojo.setSummary("");
        if (wkMrpPojo.getCreateby() == null) wkMrpPojo.setCreateby("");
        if (wkMrpPojo.getCreatebyid() == null) wkMrpPojo.setCreatebyid("");
        if (wkMrpPojo.getCreatedate() == null) wkMrpPojo.setCreatedate(new Date());
        if (wkMrpPojo.getLister() == null) wkMrpPojo.setLister("");
        if (wkMrpPojo.getListerid() == null) wkMrpPojo.setListerid("");
        if (wkMrpPojo.getModifydate() == null) wkMrpPojo.setModifydate(new Date());
        if (wkMrpPojo.getCustom1() == null) wkMrpPojo.setCustom1("");
        if (wkMrpPojo.getCustom2() == null) wkMrpPojo.setCustom2("");
        if (wkMrpPojo.getCustom3() == null) wkMrpPojo.setCustom3("");
        if (wkMrpPojo.getCustom4() == null) wkMrpPojo.setCustom4("");
        if (wkMrpPojo.getCustom5() == null) wkMrpPojo.setCustom5("");
        if (wkMrpPojo.getCustom6() == null) wkMrpPojo.setCustom6("");
        if (wkMrpPojo.getCustom7() == null) wkMrpPojo.setCustom7("");
        if (wkMrpPojo.getCustom8() == null) wkMrpPojo.setCustom8("");
        if (wkMrpPojo.getCustom9() == null) wkMrpPojo.setCustom9("");
        if (wkMrpPojo.getCustom10() == null) wkMrpPojo.setCustom10("");
        if (wkMrpPojo.getTenantid() == null) wkMrpPojo.setTenantid("");
        if (wkMrpPojo.getRevision() == null) wkMrpPojo.setRevision(0);
        if (wkMrpPojo.getItemcount() == null) wkMrpPojo.setItemcount(0);
        if (wkMrpPojo.getFinishcount() == null) wkMrpPojo.setFinishcount(0);
        if (wkMrpPojo.getItem() != null) wkMrpPojo.setItemcount(wkMrpPojo.getItem().size());

        WkMrpEntity wkMrpEntity = new WkMrpEntity();
        BeanUtils.copyProperties(wkMrpPojo, wkMrpEntity);
        //设置id和新建日期
        wkMrpEntity.setRevision(1);  //乐观锁
        //插入主表
        this.wkMrpMapper.insert(wkMrpEntity);


        //obj子表处理
        List<WkMrpobjPojo> lstobj = wkMrpPojo.getObj();
        if (lstobj != null) {
            //循环每个obj子表
            for (WkMrpobjPojo wkMrpobjPojo : lstobj) {
                //初始化obj的NULL
                WkMrpobjPojo objPojo = this.wkMrpobjService.clearNull(wkMrpobjPojo);
                WkMrpobjEntity wkMrpobjEntity = new WkMrpobjEntity();
                BeanUtils.copyProperties(objPojo, wkMrpobjEntity);
                //设置id和Pid
                wkMrpobjEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                wkMrpobjEntity.setPid(wkMrpPojo.getId());
                wkMrpobjEntity.setTenantid(wkMrpPojo.getTenantid());
                wkMrpobjEntity.setRevision(1);  //乐观锁
                //插入子表
                this.wkMrpobjMapper.insert(wkMrpobjEntity);

                // 更新销售订单 MrpUid
                if (isNotBlank(wkMrpobjEntity.getMachitemid())) {
                    this.wkMrpMapper.updateMachMrpUid(wkMrpobjEntity.getMachitemid(), wkMrpEntity.getId(), wkMrpEntity.getRefno(), wkMrpEntity.getTenantid());
                }

                // 更新生产主计划 MrpUid
                if (wkMrpobjEntity.getMainplanitemid() != null && wkMrpobjEntity.getMainplanitemid() != "") {
                    this.wkMrpMapper.updateMpMrpUid(wkMrpobjEntity.getMainplanitemid(), wkMrpEntity.getId(), wkMrpEntity.getRefno(), wkMrpEntity.getTenantid());
                }
            }
        }

        //Item子表处理
        List<WkMrpitemPojo> lst = wkMrpPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (WkMrpitemPojo wkMrpitemPojo : lst) {
                //初始化item的NULL
                WkMrpitemPojo itemPojo = this.wkMrpitemService.clearNull(wkMrpitemPojo);
                WkMrpitemEntity wkMrpitemEntity = new WkMrpitemEntity();
                BeanUtils.copyProperties(itemPojo, wkMrpitemEntity);
                //插入子表
                this.wkMrpitemMapper.insert(wkMrpitemEntity);
            }
        }

        //返回Bill实例
        return this.getBillEntity(wkMrpEntity.getId(), wkMrpEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param wkMrpPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public WkMrpPojo update(WkMrpPojo wkMrpPojo) {
        //主表更改
        if (wkMrpPojo.getItem() != null) wkMrpPojo.setItemcount(wkMrpPojo.getItem().size());
        WkMrpEntity wkMrpEntity = new WkMrpEntity();
        BeanUtils.copyProperties(wkMrpPojo, wkMrpEntity);
        this.wkMrpMapper.update(wkMrpEntity);
        //Item子表处理
        List<WkMrpitemPojo> lst = wkMrpPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = wkMrpMapper.getDelItemIds(wkMrpPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (String lstDelId : lstDelIds) {
                this.wkMrpitemMapper.delete(lstDelId, wkMrpEntity.getTenantid());
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (WkMrpitemPojo wkMrpitemPojo : lst) {
                WkMrpitemEntity wkMrpitemEntity = new WkMrpitemEntity();
                if ("".equals(wkMrpitemPojo.getId()) || wkMrpitemPojo.getId() == null) {
                    //初始化item的NULL
                    WkMrpitemPojo itemPojo = this.wkMrpitemService.clearNull(wkMrpitemPojo);
                    BeanUtils.copyProperties(itemPojo, wkMrpitemEntity);
                    //设置id和Pid
                    wkMrpitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    wkMrpitemEntity.setPid(wkMrpEntity.getId());  // 主表 id
                    wkMrpitemEntity.setTenantid(wkMrpPojo.getTenantid());   // 租户id
                    wkMrpitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.wkMrpitemMapper.insert(wkMrpitemEntity);
                } else {
                    BeanUtils.copyProperties(wkMrpitemPojo, wkMrpitemEntity);
                    wkMrpitemEntity.setTenantid(wkMrpPojo.getTenantid());
                    this.wkMrpitemMapper.update(wkMrpitemEntity);
                }
            }
        }
        // obj子表处理
        List<WkMrpobjPojo> lstobj = wkMrpPojo.getObj();
        // 获取被删除的Obj
        List<String> lstDelObjIds = wkMrpMapper.getDelObjIds(wkMrpPojo);
        if (lstDelObjIds != null) {
            // 循环每个删除obj子表
            for (String lstDelObjId : lstDelObjIds) {
                this.wkMrpobjMapper.delete(lstDelObjId, wkMrpEntity.getTenantid());
            }
        }
        if (lstobj != null) {
            // 循环每个obj子表
            for (WkMrpobjPojo wkMrpobjPojo : lstobj) {
                WkMrpobjEntity wkMrpobjEntity = new WkMrpobjEntity();
                if ("".equals(wkMrpobjPojo.getId()) || wkMrpobjPojo.getId() == null) {
                    // 初始化obj的NULL
                    WkMrpobjPojo objPojo = this.wkMrpobjService.clearNull(wkMrpobjPojo);
                    BeanUtils.copyProperties(objPojo, wkMrpobjEntity);
                    // 设置id和Pid
                    wkMrpobjEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // obj id
                    wkMrpobjEntity.setPid(wkMrpEntity.getId());  // 主表 id
                    wkMrpobjEntity.setTenantid(wkMrpPojo.getTenantid());   // 租户id
                    wkMrpobjEntity.setRevision(1);  // 乐观锁
                    // 插入子表
                    this.wkMrpobjMapper.insert(wkMrpobjEntity);
                } else {
                    BeanUtils.copyProperties(wkMrpobjPojo, wkMrpobjEntity);
                    wkMrpobjEntity.setTenantid(wkMrpPojo.getTenantid());
                    this.wkMrpobjMapper.update(wkMrpobjEntity);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(wkMrpEntity.getId(), wkMrpEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        WkMrpPojo wkMrpPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<WkMrpitemPojo> lst = wkMrpPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (WkMrpitemPojo wkMrpitemPojo : lst) {
                if (wkMrpitemPojo.getWkwsqty() > 0) {
                    throw new RuntimeException(wkMrpitemPojo.getGoodsuid() + ":已转生产加工单");
                }
                if (wkMrpitemPojo.getWkscqty() > 0) {
                    throw new RuntimeException(wkMrpitemPojo.getGoodsuid() + ":已转委外加工单");
                }
                if (wkMrpitemPojo.getBuyplanqty() > 0) {
                    throw new RuntimeException(wkMrpitemPojo.getGoodsuid() + ":已转采购计划");
                }
                if (wkMrpitemPojo.getBuyorderqty() > 0) {
                    throw new RuntimeException(wkMrpitemPojo.getGoodsuid() + ":已转采购订单");
                }
                if (wkMrpitemPojo.getCustsuppqty() > 0) {
                    throw new RuntimeException(wkMrpitemPojo.getGoodsuid() + ":已转客供需求单");
                }
                this.wkMrpitemMapper.delete(wkMrpitemPojo.getId(), tid);
            }
        }
        //Item子表处理
        List<WkMrpobjPojo> lstObj = wkMrpPojo.getObj();
        if (lstObj != null) {
            //循环每个删除item子表
            for (WkMrpobjPojo wkMrpobjPojo : lstObj) {
                this.wkMrpobjMapper.delete(wkMrpobjPojo.getId(), tid);
                // 同步销售订单
                if (!"".equals(wkMrpobjPojo.getMachuid())) {
                    this.wkMrpMapper.updateMachMrpUid(wkMrpobjPojo.getMachitemid(), "", "", tid);
                }
                // 同步生产主计划
                if (!"".equals(wkMrpobjPojo.getMainplanuid())) {
                    this.wkMrpMapper.updateMpMrpUid(wkMrpobjPojo.getMainplanitemid(), "", "", tid);
                }
            }
        }
        this.wkMrpMapper.delete(key, tid);
        return wkMrpPojo.getRefno();
    }

    /**
     * 通过Obj 建立Mrp明细，但不包括计算
     *
     * @param lstobj 主键
     * @return 实例对象
     */
    @Override
    public List<WkMrpitemPojo> pullItemList(List<WkMrpobjPojo> lstobj, String Pid, String mrpqtyupper, int mrpqtydec) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        Integer rowNum = 1;
        for (WkMrpobjPojo obj : lstobj) {
            WkMrpitemPojo item;
            // 读取bom集 有查安全库存Mat_Goods.SafeStock  MatBomdetailPojo.getSafestock()
            // 20240926计算bomqty和needqty,根据obj表中的wkpcsqty大于0，用wkpcsqty，否则用quantity计算
            double qtyResult = Optional.ofNullable(obj.getWkpcsqty())
                    .filter(wkpcsqty -> wkpcsqty > 0)
                    .orElse(Optional.ofNullable(obj.getQuantity()).orElse(0D));
            List<MatBomdetailPojo> bomdetail = goodsBomServiceImpl.getBomDetailByGoodsid(obj.getGoodsid(), qtyResult, mrpqtyupper, mrpqtydec, loginUser.getTenantid());

            // 根据Bom生产Item
            for (MatBomdetailPojo bom : bomdetail) {
                item = new WkMrpitemPojo();
                BeanUtils.copyProperties(bom, item);// 安全库存有赋值
                item.setId(bom.getTreeid());
                item.setItemparentid(bom.getTreeparentid());
                item.setPid(Pid);
                item.setBomid(bom.getGoodsbomid());
                item.setMrpobjid(obj.getId());
                item.setBomtype("标准Bom");
                item.setTenantid(loginUser.getTenantid());
                item.setRevision(1);
                item.setRownum(rowNum);
                // 加入Obj表格 订单信息
                item.setMachgroupid(obj.getMachgroupid());
                item.setMachitemid(obj.getMachitemid());
                item.setMachuid(obj.getMachuid());
                // 加入obj 主计划信息
                item.setMainplanitemid(obj.getMainplanitemid());
                item.setMainplanuid(obj.getMainplanuid());

                //初始化item的NULL
                WkMrpitemPojo itemPojo = this.wkMrpitemService.clearNull(item);
                WkMrpitemEntity wkMrpitemEntity = new WkMrpitemEntity();
                BeanUtils.copyProperties(itemPojo, wkMrpitemEntity);
                //插入子表
                this.wkMrpitemMapper.insert(wkMrpitemEntity);

                rowNum++;
            }
        }
        //读取子表
        List<WkMrpitemPojo> itemListDB = this.wkMrpitemMapper.getList(Pid, loginUser.getTenantid());
        // 记录到Mrp日志
        recordMrpLog(Pid, "通过Obj新建Mrp明细,但不包括计算", "/pullItemList", null, loginUser);
        return itemListDB;
    }

    @Override
//    @Async("threadPoolExecutor")
    public void pullItemListStart(String redisKey, String lockKey, List<WkMrpobjPojo> lstobj, String Pid, String mrpqtyupper, int mrpqtydec, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        Map<String, Object> missionMsg;
        AtomicInteger counter = new AtomicInteger(0); // 用于计数已处理的任务数量
        int size = lstobj.size();

        //------------1设置当前计算任务进度
        missionMsg = new HashMap<>();
        missionMsg.put("total", size);
        missionMsg.put("finish", 0);
        this.saRedisService.setCacheObject(MyConstant.ASYNC_MRP_PULLITEMLIST_STATE + redisKey, missionMsg, 10L, TimeUnit.MINUTES);
        //------------2数据填充

        final Integer[] rowNum = {1};
        for (WkMrpobjPojo obj : lstobj) {
            // 提交任务到线程池
            //threadPoolExecutor.execute(() -> {

            WkMrpitemPojo item;
            // 读取bom集 有查安全库存Mat_Goods.SafeStock  MatBomdetailPojo.getSafestock()
            // 20240926计算bomqty和needqty,根据obj表中的wkpcsqty大于0，用wkpcsqty，否则用quantity计算
            double qtyResult = Optional.ofNullable(obj.getWkpcsqty())
                    .filter(wkpcsqty -> wkpcsqty > 0)
                    .orElse(Optional.ofNullable(obj.getQuantity()).orElse(0D));
            List<MatBomdetailPojo> bomdetail = goodsBomServiceImpl.getBomDetailByGoodsid(obj.getGoodsid(), qtyResult, mrpqtyupper, mrpqtydec, tid);
//               原始调用Feign: R<List<MatBomdetailPojo>> r = this.goodsFeignService.getBomDetailByGoodsid(obj.getGoodsid(), obj.getQuantity(), loginUser.getToken());

            // 20241022 当goodsid未关联bom时，bomdetail会返回null 此时需要改为要将这条为关联bom的货品本身作为mrpitem计算插入了
            if (bomdetail == null) {
                item = new WkMrpitemPojo();
                //BeanUtils.copyProperties(bom, item);// 安全库存有赋值
                item.setId(inksSnowflake.getSnowflake().nextIdStr());
                item.setBomqty(qtyResult);
                item.setLevelnum(1);
                item.setItemparentid("");
                item.setPid(Pid);
                item.setBomid("");
                item.setMrpobjid(obj.getId());
                item.setBomtype("标准Bom");
                item.setTenantid(tid);
                item.setRevision(1);
                item.setRownum(rowNum[0]);
                // 加入Obj表格 订单信息 goods信息也直接从Obj拿过来
                //这种goods信息也直接从Obj拿过来的需要是厂制 item.setAttrcode(isNotBlank(wkMrpitemPojoDB.getBomid()) ? "厂制" : "外购");
                item.setCustom1("厂制");
                item.setGoodsid(obj.getGoodsid());
                item.setItemcode(obj.getItemcode());
                item.setItemname(obj.getItemname());
                item.setItemspec(obj.getItemspec());
                item.setItemunit(obj.getItemunit());
                item.setPcsx(obj.getPcsx());
                item.setPcsy(obj.getPcsy());
                item.setMachgroupid(obj.getMachgroupid());
                item.setMachitemid(obj.getMachitemid());
                item.setMachuid(obj.getMachuid());
                // 加入obj 主计划信息
                item.setMainplanitemid(obj.getMainplanitemid());
                item.setMainplanuid(obj.getMainplanuid());
                //20240618 计算把obj的AttributeJson字段，带入item表的AttributeJson字段
                item.setAttributejson(obj.getAttributejson());
                // BomQty拷贝给GrossQty毛需求，便于合并计算
                item.setGrossqty(item.getBomqty());
                //初始化item的NULL
                WkMrpitemPojo itemPojo = this.wkMrpitemService.clearNull(item);
                WkMrpitemEntity wkMrpitemEntity = new WkMrpitemEntity();
                BeanUtils.copyProperties(itemPojo, wkMrpitemEntity);
                //插入子表
                this.wkMrpitemMapper.insert(wkMrpitemEntity);
                rowNum[0]++;
            }

            // 根据Bom生产Item
            if (CollectionUtils.isNotEmpty(bomdetail)) {
                for (MatBomdetailPojo bom : bomdetail) {
                    item = new WkMrpitemPojo();
                    BeanUtils.copyProperties(bom, item);// 安全库存有赋值
                    item.setId(bom.getTreeid());
                    item.setItemparentid(bom.getTreeparentid());
                    item.setPid(Pid);
                    item.setBomid(bom.getGoodsbomid());
                    item.setMrpobjid(obj.getId());
                    item.setBomtype("标准Bom");
                    item.setTenantid(tid);
                    item.setRevision(1);
                    item.setRownum(rowNum[0]);
                    // 加入Obj表格 订单信息
                    item.setMachgroupid(obj.getMachgroupid());
                    item.setMachitemid(obj.getMachitemid());
                    item.setMachuid(obj.getMachuid());
                    // 加入obj 主计划信息
                    item.setMainplanitemid(obj.getMainplanitemid());
                    item.setMainplanuid(obj.getMainplanuid());
                    //20240618 计算把obj的AttributeJson字段，带入item表的AttributeJson字段
                    item.setAttributejson(obj.getAttributejson());
                    // BomQty拷贝给GrossQty毛需求，便于合并计算
                    item.setGrossqty(item.getBomqty());
                    //初始化item的NULL
                    WkMrpitemPojo itemPojo = this.wkMrpitemService.clearNull(item);
                    WkMrpitemEntity wkMrpitemEntity = new WkMrpitemEntity();
                    BeanUtils.copyProperties(itemPojo, wkMrpitemEntity);
                    //插入子表
                    this.wkMrpitemMapper.insert(wkMrpitemEntity);
                    rowNum[0]++;
                }
            }
            int currentCount = counter.incrementAndGet(); // 增加计数器
            PrintColor.zi("已处理任务数量：" + currentCount + " / " + size); // 打印进度
            // 每处理100个任务，更新一次Redis中的进度信息
            missionMsg.put("finish", currentCount);
            this.saRedisService.setCacheObject(MyConstant.ASYNC_MRP_PULLITEMLIST_STATE + redisKey, missionMsg, 10L, TimeUnit.MINUTES);
            // 检查所有任务是否已经完成
            if (currentCount == size) {
                this.saRedisService.deleteObject(lockKey);
            }

            //});
        }
        //子表全部创建后，刷新主表的5个Count
        this.wkMrpMapper.syncMrpCount(Pid, tid);
        // 记录到Mrp日志
        recordMrpLog(Pid, "通过Obj新建Mrp明细,但不包括计算", "/pullItemList", null, loginUser);

    }

    @Override
    @Transactional
    public String pullMrpitemByMrpidStart(String mrpid, MrpCalculationPojo mrpFront, String mrpqtyupper, int mrpqtydec, String tid) {

        // 20241026 合并Mrpitem
        List<WkMrpitemPojo> mrpItemList = wkMrpitemMapper.getList(mrpid, tid);
        //当goodsid相同时合并，被合并的行MergeMark置为1，GrossQty置0；合并为的行MergeMark置为2；GrossQty为累加值
        // 更新合并后的mrpitems并返回
        List<WkMrpitemPojo> mrpItemMergeList = this.mergeMrpItem(mrpItemList);

        // 所有itemid
        //List<String> mrpItemIds = this.wkMrpMapper.getItemIds(mrpid, tid);
        //int size = mrpItemIds.size();
        int size = mrpItemMergeList.size();
        AtomicInteger counter = new AtomicInteger(0); // 用于计数已处理的任务数量

        //------------1设置当前计算任务进度
        String state_redisKey = MyConstant.ASYNC_MRP_PULLMRPITEMBYMRPID_STATE + inksSnowflake.getSnowflake().nextIdStr();
        Map<String, Object> missionMsg = new HashMap<>();
        missionMsg.put("total", size);
        missionMsg.put("finish", 0);
        this.saRedisService.setCacheObject(state_redisKey, missionMsg, 10L, TimeUnit.MINUTES);

        for (WkMrpitemPojo wkMrpitemPojo : mrpItemMergeList) {
            // 提交任务到线程池
            threadPoolExecutor.execute(() -> {
                pullMrpitem(wkMrpitemPojo, mrpFront, mrpqtyupper, mrpqtydec, tid);
                int currentCount = counter.incrementAndGet(); // 增加计数器
                PrintColor.zi("已处理任务数量：" + currentCount + " / " + size); // 打印进度
                // 更新Redis中的进度信息 避免频繁更新，每50次
                if (currentCount % 50 == 0 || currentCount == size) {
                    missionMsg.put("finish", currentCount);
                    this.saRedisService.setCacheObject(state_redisKey, missionMsg, 10L, TimeUnit.MINUTES);
                }
            });
        }
        return state_redisKey;
    }


    // 当goodsid相同时合并，被合并的行MergeMark置为1，GrossQty置0；合并为的行MergeMark置为2；GrossQty为累加值
    // 注意levelnum=1的成品不参与合并
    private List<WkMrpitemPojo> mergeMrpItem(List<WkMrpitemPojo> mrpitemList) {
        // 先找出重复的goodsId
        Set<String> uniqueGoodsIds = new HashSet<>();
        Set<String> duplicateGoodsIds = new HashSet<>();
        for (WkMrpitemPojo item : mrpitemList) {
            String goodsid = item.getGoodsid();
            // 检查 levelnum 是否为 1，不参与合并
            if (item.getLevelnum() != 1 && !uniqueGoodsIds.add(goodsid)) {
                duplicateGoodsIds.add(goodsid);
            }
        }

        // 对于重复的 goodsid 进行合并
        Map<String, WkMrpitemPojo> mergedMap = new HashMap<>();
        for (WkMrpitemPojo item : mrpitemList) {
            String goodsid = item.getGoodsid();
            // 再次检查 levelnum 是否为 1，不参与合并
            if (duplicateGoodsIds.contains(goodsid) && item.getLevelnum() != 1) {
                if (mergedMap.containsKey(goodsid)) {
                    // 如果 goodsid 已存在，说明需要合并
                    WkMrpitemPojo mergedItem = mergedMap.get(goodsid);

                    // 先累加 GrossQty
                    mergedItem.setGrossqty(mergedItem.getGrossqty() + item.getGrossqty());

                    // 再设置被合并的行的标志
                    item.setMergemark(1); // 被合并的行 MergeMark 置为 1
                    item.setGrossqty(0D); // 被合并的行 GrossQty 置 0
                } else {
                    // 第一次遇到该 goodsid 的行
                    item.setMergemark(2); // 合并为的行 MergeMark 置为 2
                    mergedMap.put(goodsid, item); // 保存到合并 Map 中
                }
            }
        }
        // 合并/被合并的Mrpitem更新数据库
        for (WkMrpitemPojo wkMrpitemPojo : mrpitemList) {
            if (wkMrpitemPojo.getMergemark() != 0) {
                this.wkMrpitemService.update(wkMrpitemPojo);
            }
        }
        return mrpitemList;
    }


    /**
     * 通过ID单条MRP运算
     * mrpCalculationPojo:前端传入需要计算的参数
     * mrpqtyupper:上进位
     * mrpqtydec:小数位
     *
     * @return 实例对象
     */
    @Override
    public WkMrpitemPojo pullMrpitem(WkMrpitemPojo wkMrpitemPojoDB, MrpCalculationPojo mrpFront, String mrpqtyupper, int mrpqtydec, String tid) {
        if (mrpFront == null) {
            mrpFront = new MrpCalculationPojo();
            mrpFront.setMativqty(true);
            mrpFront.setWkwsremqty(true);
            mrpFront.setWkscremqty(true);
            mrpFront.setBuyremqty(true);
            mrpFront.setBusremqty(true);
            mrpFront.setMrpremqty(true);
            mrpFront.setReqremqty(true);
            mrpFront.setSafestock(true);
            mrpFront.setStoreids(null);// 指定查询仓库集合 null会查全部仓库
        }

        //int mrpqtydec = 0;// MRP数量小数位
//        boolean mrpqtyupper = true;// MRP数量上进位
        //Item子表处理
        //WkMrpitemPojo wkMrpitemPojoDB = this.wkMrpitemMapper.getEntity(key, tid);
        WkMrpitemPojo item = new WkMrpitemPojo();
        item.setId(wkMrpitemPojoDB.getId());
        item.setTenantid(tid);
        // -----以下字段需要重新拉取计算:----
        item.setMativqty(0D);
        item.setWkwsremqty(0D);
        item.setWkscremqty(0D);
        item.setBuyremqty(0D);
        item.setBusremqty(0D);
        item.setMrpremqty(0D);
        item.setReqremqty(0D);
        item.setSafestock(0D);

        // 库存数量
        if (mrpFront.isMativqty()) {                                                                 // 指定查询仓库集合
            item.setMativqty(this.wkMrpMapper.getGoodsInveQtyInStoreids(wkMrpitemPojoDB.getGoodsid(), mrpFront.getStoreids(), tid));
            //PrintColor.printColor("--------库存数量:" + item.getMativqty());
        }

        // 生产待入
        if (mrpFront.isWkwsremqty()) {
            item.setWkwsremqty(this.wkMrpMapper.getWkWsRemQty(wkMrpitemPojoDB.getGoodsid(), wkMrpitemPojoDB.getId(), tid));
        }

        // 加工待入
        if (mrpFront.isWkscremqty()) {
            item.setWkscremqty(this.wkMrpMapper.getWkScRemQty(wkMrpitemPojoDB.getGoodsid(), wkMrpitemPojoDB.getId(), tid));
        }

        // 采购在途 即采购待入
        if (mrpFront.isBuyremqty()) {
            item.setBuyremqty(this.wkMrpMapper.getBuyOrderRemQty(wkMrpitemPojoDB.getGoodsid(), wkMrpitemPojoDB.getId(), tid) +
                    this.wkMrpMapper.getBuyPlanRemQty(wkMrpitemPojoDB.getGoodsid(), wkMrpitemPojoDB.getId(), tid));
        }

        // 销售待出 即订单待出
        if (mrpFront.isBusremqty()) {
            item.setBusremqty(this.wkMrpMapper.getBusMachRemQty(wkMrpitemPojoDB.getGoodsid(), wkMrpitemPojoDB.getMachitemid(), tid) +
                    this.wkMrpMapper.getBusDeliRemQty(wkMrpitemPojoDB.getGoodsid(), wkMrpitemPojoDB.getMachitemid(), tid));
        }

        // Mrp待用 即已分配量()
        if (mrpFront.isMrpremqty()) {
            item.setMrpremqty(this.wkMrpMapper.getWkWsMatRemQty(wkMrpitemPojoDB.getGoodsid(), wkMrpitemPojoDB.getId(), tid) +
                    this.wkMrpMapper.getWkScMatRemQty(wkMrpitemPojoDB.getGoodsid(), wkMrpitemPojoDB.getId(), tid));
        }

        // 领料待出 即其他配量（其他领料）
        if (mrpFront.isReqremqty()) {
            item.setReqremqty(this.wkMrpMapper.getReqRemQty(wkMrpitemPojoDB.getGoodsid(), wkMrpitemPojoDB.getId(), tid));
        }

        // 安全库存
        if (mrpFront.isSafestock()) {
            item.setSafestock(this.wkMrpMapper.getSafestock(wkMrpitemPojoDB.getGoodsid(), tid));
        }

        double stoqty = item.getMativqty() + item.getBuyremqty() + item.getWkwsremqty() + item.getWkscremqty();
        stoqty = stoqty - item.getBusremqty() - item.getMrpremqty() - item.getReqremqty() - (item.getSafestock() == null ? 0 : item.getSafestock());

        item.setStoqty(stoqty);
        if (item.getStoqty() > wkMrpitemPojoDB.getGrossqty()) {
            item.setNeedqty(0D);
        } else if (item.getStoqty() < 0) {
            item.setNeedqty(wkMrpitemPojoDB.getGrossqty());
        } else {
            item.setNeedqty(wkMrpitemPojoDB.getGrossqty() - item.getStoqty());
        }

        ////Needqty向上进位 保留小数位数  double needQty = 6.123456D;MRP数量小数位 0-7.0  1-6.2  2-6.13  3-6.124
        //item.setNeedqty(Math.ceil(item.getNeedqty() * Math.pow(10, mrpqtydec)) / Math.pow(10, mrpqtydec));

        // 20241111 加入上进位，小数位参数计算
        // 获取当前的需求数量
        double needQty = item.getNeedqty();
        // 根据 mrpqtydec计算出小数保留的倍率。例如，如果 mrpqtydec 为 2，则 scaleFactor = 100 表示保留 2 位小数。
        double scaleFactor = Math.pow(10, mrpqtydec);
        // 如果 mrpqtyupper为 "true"，则向上进位；否则按正常四舍五入处理
        double roundedQty = "true".equalsIgnoreCase(mrpqtyupper)
                ? Math.ceil(needQty * scaleFactor) / scaleFactor   // 向上进位处理
                : Math.round(needQty * scaleFactor) / scaleFactor; // 四舍五入处理
        // 设置最终计算得到的 Needqty
        item.setNeedqty(roundedQty);


        //前端已保存值Wk_MrpObj.MachBatch,需要同步到Wk_MrpItem.MachBatch
        item.setMachbatch(wkMrpobjMapper.getMachbatch(wkMrpitemPojoDB.getMrpobjid(), tid));
        // wkMrpitemPojoDB.getItemparentid() 是 "" 说明是Bom主表,进入厂制; 其他子表进入采购

        // 设置AttrCode 属性 厂制/委制/外购/客供
        //if (StringUtils.isBlank(wkMrpitemPojoDB.getAttrcode())) {
        // sa-oms ：设置AttrCode 属性 厂制/委制/外购/客供 如果有bomid说明下层还有东西,则当前为厂制
        item.setAttrcode(isNotBlank(wkMrpitemPojoDB.getBomid()) || "厂制".equals(wkMrpitemPojoDB.getCustom1()) ? "厂制" : "外购");
        // 原本store：item.setAttrcode(isBlank(wkMrpitemPojoDB.getItemparentid()) ? "厂制" : "外购");
        //}
        WkMrpitemEntity wkMrpitemEntity = new WkMrpitemEntity();
        BeanUtils.copyProperties(item, wkMrpitemEntity);
        this.wkMrpitemMapper.update(wkMrpitemEntity);
        return this.wkMrpitemMapper.getEntity(wkMrpitemPojoDB.getId(), tid);
    }

//    public static void main(String[] args) {
//        double needQty = 6.123456D;
//        int mrpQtyDec = 3;// MRP数量小数位 0-7.0  1-6.2  2-6.13  3-6.124 4-6.1235
//        double v = Math.ceil(needQty * Math.pow(10, mrpQtyDec)) / Math.pow(10, mrpQtyDec);
//        System.out.println(v);
//    }

    /**
     * 通过Obj 建立Mrp明细，但不包括计算
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    @Transactional
    public Map<String, Object> refreshItemList(String key, String cmd, String mrpqtyupper, int mrpqtydec, LoginUser loginUser) {
        PrintColor.zi("======refreshItemList======");
        String tid = loginUser.getTenantid();
        List<WkMrpobjPojo> lstobj = this.wkMrpobjMapper.getList(key, tid);
        if (lstobj.isEmpty()) {
            R.fail("未找到单据项目");
        }
        int rowNum = 1;
//        List<WkMrpitemPojo> lstitem = this.wkMrpitemMapper.getList(key, tid); // 得在循环里查: getListByMrpObjId
        for (WkMrpobjPojo obj : lstobj) {
            // obj货品关联的MrpItem
            List<WkMrpitemPojo> lstitem = this.wkMrpitemMapper.getListByMrpObjId(obj.getId(), tid);
            String machBatchObj = obj.getMachbatch();//前端已保存值Wk_MrpObj.MachBatch,需要同步到Wk_MrpItem.MachBatch
            WkMrpitemPojo item;
            // 读取bom集  有查安全库存Mat_Goods.SafeStock  MatBomdetailPojo.getSafestock()
            // 20240926计算bomqty和needqty,根据obj表中的wkpcsqty大于0，用wkpcsqty，否则用quantity计算
            double qtyResult = Optional.ofNullable(obj.getWkpcsqty())
                    .filter(wkpcsqty -> wkpcsqty > 0)
                    .orElse(Optional.ofNullable(obj.getQuantity()).orElse(0D));
            List<MatBomdetailPojo> bomdetail = goodsBomServiceImpl.getBomDetailByGoodsid(obj.getGoodsid(), qtyResult, mrpqtyupper, mrpqtydec, loginUser.getTenantid());

            int chkNew;
            for (WkMrpitemPojo mrpitemPojo : lstitem) {
                mrpitemPojo.setBommark(-2);  //所有定义为可删除
            }
            // 根据Bom生产Item
            for (MatBomdetailPojo bom : bomdetail) {
                if (bom.getLevelnum() > 1) {
                    chkNew = 1;
                    for (WkMrpitemPojo wkMrpitemPojo : lstitem) {
                        // 本项目+bomid+bomitemid 相同 ，并没有被处理过来（不等于0和2）
                        if (wkMrpitemPojo.getMrpobjid().equals(obj.getId()) && wkMrpitemPojo.getBommark() != 0 &&
                                wkMrpitemPojo.getBommark() != 2 && wkMrpitemPojo.getBomid().equals(bom.getGoodsbomid()) &&
                                wkMrpitemPojo.getBomitemid().equals(bom.getBomitemid())) {
                            chkNew = 0;
                            item = wkMrpitemPojo;
                            //20241028 赋值GrossQty=BomQty
                            item.setGrossqty(bom.getBomqty());
                            // 如果bom 主子件、损耗率 是否有变动  [1.有修改]
                            if (!Objects.equals(item.getMainqty(), bom.getMainqty()) || !Objects.equals(item.getSubqty(), bom.getSubqty()) || !Objects.equals(item.getLossrate(), bom.getLossrate())) {
                                item.setMainqty(bom.getMainqty());
                                item.setSubqty(bom.getSubqty());
                                item.setLossrate(bom.getLossrate());
                                PrintColor.red("------------bomqty:" + bom.getBomqty());
                                item.setBomqty(bom.getBomqty());
                                item.setBommark(2);
                                item.setBomdate(new Date());
                                item.setBomround(item.getBomround() + 1);
                                item.setMachbatch(machBatchObj);
                                WkMrpitemEntity wkMrpitemEntity = new WkMrpitemEntity();
                                BeanUtils.copyProperties(item, wkMrpitemEntity);
                                if (cmd.contains("u")) {
                                    this.wkMrpitemMapper.update(wkMrpitemEntity);
                                    rowNum++;
                                }
                            } else {
                                // 三项无变动，定义为未变动
                                wkMrpitemPojo.setBommark(0);  //定义为未变动
                            }
                            break;
                        }
                    }

                    // 新BOM Item  [2.有新增]
                    if (chkNew == 1) {
                        item = new WkMrpitemPojo();
                        BeanUtils.copyProperties(bom, item);
                        item.setId(bom.getTreeid());
                        item.setItemparentid("");
                        //20241028 赋值GrossQty=BomQty
                        item.setGrossqty(bom.getBomqty());
                        //查询父级
                        for (MatBomdetailPojo bomnew : bomdetail) {
                            // 查到新父级
                            if (bomnew.getTreeid().equals(bom.getTreeparentid())) {
                                for (WkMrpitemPojo itemorg : lstitem) {
                                    if (itemorg.getBomid().equals(bomnew.getGoodsbomid()) || itemorg.getBomitemid().equals(bomnew.getBomitemid())) {
                                        item.setItemparentid(itemorg.getId());
                                        break;
                                    }
                                }
                                break;
                            }
                        }
                        // item.getItemparentid() 是 "" 说明是Bom主表,进入厂制; 其他子表进入采购
//                        // 设置AttrCode 属性 厂制/委制/外购/客供
//                        item.setAttrcode(Objects.equals(item.getItemparentid(), "") ? "厂制" : "外购");
                        item.setPid(key);
                        item.setBomid(bom.getGoodsbomid());
                        // 设置AttrCode 属性 厂制/委制/外购/客供 如果有bomid说明下层还有东西,则当前为厂制
                        item.setAttrcode(isNotBlank(bom.getGoodsbomid()) ? "厂制" : "外购");
                        item.setMrpobjid(obj.getId());
                        item.setBomtype("标准Bom");
                        item.setTenantid(tid);
                        item.setRevision(1);
                        item.setRownum(lstitem.size() + 1);
                        // 加入Obj表格 订单信息
                        item.setMachgroupid(obj.getMachgroupid());
                        item.setMachitemid(obj.getMachitemid());
                        item.setMachuid(obj.getMachuid());
                        // 加入obj 主计划信息
                        item.setMainplanitemid(obj.getMainplanitemid());
                        item.setMainplanuid(obj.getMainplanuid());
                        item.setBomdate(new Date());
                        item.setBommark(1);
                        item.setBomround(1);
                        item.setMachbatch(machBatchObj);
                        //初始化item的NULL
                        WkMrpitemPojo itemPojo = this.wkMrpitemService.clearNull(item);
                        WkMrpitemEntity wkMrpitemEntity = new WkMrpitemEntity();
                        BeanUtils.copyProperties(itemPojo, wkMrpitemEntity);
                        //插入子表
                        if (cmd.contains("c")) {
                            this.wkMrpitemMapper.insert(wkMrpitemEntity);
                            rowNum++;
                        }
                    }
                }
            }


            //=========处理移除========  [3.有删除]
            for (WkMrpitemPojo wkMrpitemPojo : lstitem) {
                if (wkMrpitemPojo.getBommark() == -2 && wkMrpitemPojo.getLevelnum() > 1 && cmd.contains("d")) {
                    if (wkMrpitemPojo.getWkscqty() > 0 || wkMrpitemPojo.getWkwsqty() > 0 || wkMrpitemPojo.getBuyplanqty() > 0 || wkMrpitemPojo.getBuyorderqty() > 0 ||
                            wkMrpitemPojo.getCustsuppqty() > 0 || wkMrpitemPojo.getOtherqty() > 0) {
                        wkMrpitemPojo.setBommark(-1);
                        wkMrpitemPojo.setBomdate(new Date());
                        wkMrpitemPojo.setBomround(wkMrpitemPojo.getBomround() + 1);
                        WkMrpitemEntity wkMrpitemEntity = new WkMrpitemEntity();
                        BeanUtils.copyProperties(wkMrpitemPojo, wkMrpitemEntity);
                        if (cmd.contains("u")) {
                            this.wkMrpitemMapper.update(wkMrpitemEntity);
                            rowNum++;
                        }
                    } else {
                        if (cmd.contains("d")) {
                            this.wkMrpitemMapper.delete(wkMrpitemPojo.getId(), wkMrpitemPojo.getTenantid());
                            rowNum++;
                        }
                    }

                }
            }
        }

        //子表全部刷新后，刷新主表的5个Count
        this.wkMrpMapper.syncMrpCount(key, tid);

        // 20241031 之前参与过合并的mrpitem的MergeMark会变成1或2，为了下一个接口pullMrpitemByMrpidStart中重新合并mrpitem,需要恢复MergeMark=0，并且GrossQty重新等于BomQty(因为MergeMark=2的GrossQty已经进行过累加了)
        this.wkMrpMapper.syncMrpItemMergeMarkAndGrossQty(key, tid);


        Map<String, Object> map = new HashMap<>();
        map.put("num", rowNum);
        if (rowNum > 0) {
            map.put("list", this.wkMrpitemMapper.getList(key, tid));
        } else {
            map.put("list", null);
        }
        recordMrpLog(key, "重拉BOM表,刷新MrpItem", "/refreshItemList", null, loginUser);
        //读取子表
        return map;
    }

    @Override // key: Wk_Mrp.id
    @Transactional
    public List<WkMrpitemPojo> contrastItemList(String key, String mrpqtyupper, int mrpqtydec, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        List<WkMrpobjPojo> lstobj = this.wkMrpobjMapper.getList(key, tid);
        if (lstobj.isEmpty()) {
            R.fail("未找到单据项目");
        }
        // 存储所有lstobj关联的item(记录BomMark改变)
        List<WkMrpitemPojo> lstItemResult = new ArrayList<>();// 存储所有lstobj关联的item(记录BomMark改变)
        for (WkMrpobjPojo obj : lstobj) {
            // obj货品关联的MrpItem
            List<WkMrpitemPojo> lstitem = this.wkMrpitemMapper.getListByMrpObjId(obj.getId(), tid);
            WkMrpitemPojo item;
            // 读取bom集
            // 20240926计算bomqty和needqty,根据obj表中的wkpcsqty大于0，用wkpcsqty，否则用quantity计算
            double qtyResult = Optional.ofNullable(obj.getWkpcsqty())
                    .filter(wkpcsqty -> wkpcsqty > 0)
                    .orElse(Optional.ofNullable(obj.getQuantity()).orElse(0D));
            List<MatBomdetailPojo> bomdetail = goodsBomServiceImpl.getBomDetailByGoodsid(obj.getGoodsid(), qtyResult, mrpqtyupper, mrpqtydec, loginUser.getTenantid());

            int chkNew;
            // 先把所有BomMark定义为可删除 -2
            for (WkMrpitemPojo wkMrpitemPojo : lstitem) {
                wkMrpitemPojo.setBommark(-2);
            }
            // 根据Bom生产Item
            for (MatBomdetailPojo bom : bomdetail) {
                if (bom.getLevelnum() > 1) {
                    chkNew = 1;
                    for (WkMrpitemPojo wkMrpitemPojo : lstitem) {
                        // 本项目+bomid+bomitemid 相同 ，并没有被处理过来（不等于0和2）
                        if (wkMrpitemPojo.getMrpobjid().equals(obj.getId()) && wkMrpitemPojo.getBommark() != 0 &&
                                wkMrpitemPojo.getBommark() != 2 && wkMrpitemPojo.getBomid().equals(bom.getGoodsbomid()) &&
                                wkMrpitemPojo.getBomitemid().equals(bom.getBomitemid())) {
                            chkNew = 0;
                            item = wkMrpitemPojo;
                            //20241028 赋值GrossQty=BomQty
                            item.setGrossqty(bom.getBomqty());
                            // 如果bom 主子件、损耗率 是否有变动   [1.有修改]
                            if (!Objects.equals(item.getMainqty(), bom.getMainqty()) || !Objects.equals(item.getSubqty(), bom.getSubqty()) || !Objects.equals(item.getLossrate(), bom.getLossrate())) {
                                item.setMainqty(bom.getMainqty());
                                item.setSubqty(bom.getSubqty());
                                item.setLossrate(bom.getLossrate());
                                item.setBomqty(bom.getBomqty());
                                item.setBommark(2);
                            } else {
                                // 三项无变动，定义为未变动
                                wkMrpitemPojo.setBommark(0);  //定义为未变动
                            }
                            break;
                        }
                    }

                    // 新BOM Item  [2.有新增]
                    if (chkNew == 1) {
                        item = new WkMrpitemPojo();
                        BeanUtils.copyProperties(bom, item);
                        item.setId(bom.getTreeid());
                        item.setItemparentid("");
                        //20241028 赋值GrossQty=BomQty
                        item.setGrossqty(bom.getBomqty());
                        //查询父级
                        for (MatBomdetailPojo bomnew : bomdetail) {
                            // 查到新父级
                            if (bomnew.getTreeid().equals(bom.getTreeparentid())) {
                                for (WkMrpitemPojo itemorg : lstitem) {
                                    if (itemorg.getBomid().equals(bomnew.getGoodsbomid()) && itemorg.getBomitemid().equals(bomnew.getBomitemid())) {
                                        item.setItemparentid(itemorg.getId());
                                        break;
                                    }
                                }
                                break;
                            }
                        }
                        item.setPid(key);
                        item.setBomid(bom.getGoodsbomid());
                        item.setMrpobjid(obj.getId());
                        item.setBomtype("标准Bom");
                        item.setTenantid(tid);
                        item.setRevision(1);
                        item.setRownum(lstitem.size() + 1);
                        // 加入Obj表格 订单信息
                        item.setMachgroupid(obj.getMachgroupid());
                        item.setMachitemid(obj.getMachitemid());
                        item.setMachuid(obj.getMachuid());
                        // 加入obj 主计划信息
                        item.setMainplanitemid(obj.getMainplanitemid());
                        item.setMainplanuid(obj.getMainplanuid());
                        item.setBomdate(new Date());
                        item.setBommark(1);
                        item.setBomround(1);
                        lstitem.add(item);
                    }
                }
            }


            //=========处理移除========  [3.有删除]
            for (WkMrpitemPojo wkMrpitemPojo : lstitem) {
                if (wkMrpitemPojo.getBommark() == -2 && wkMrpitemPojo.getLevelnum() > 1) {
//                if (wkMrpitemPojo.getWkscqty() > 0 || wkMrpitemPojo.getWkwsqty() > 0 || wkMrpitemPojo.getBuyplanqty() > 0 || wkMrpitemPojo.getBuyorderqty() > 0 ||
//                        wkMrpitemPojo.getCustsuppqty() > 0 || wkMrpitemPojo.getOtherqty() > 0) {
                    wkMrpitemPojo.setBommark(-1);
//                } else {
//                    PrintColor.red("进入对比Bom接口-处理移除的else /contrastItemList" + wkMrpitemPojo.getGoodsuid());
//                }
                }
            }
            // 将lstitem的所有元素追加到lstItemResult中
            lstItemResult.addAll(lstitem);
        }
        //返回子表 其中BomMark=0,-2的是未变动的，BomMark=1的是新增的，BomMark=2的是修改的，BomMark=-1的是删除的,我只返回BomMark=1,2,-1的
        List<WkMrpitemPojo> collect = lstItemResult.stream()
                .filter(item -> item.getBommark() == 1 || item.getBommark() == 2 || item.getBommark() == -1)
                .collect(Collectors.toList());
        // 记录到Mrp日志
        recordMrpLog(key, "重拉BOM表,对比MrpItem", "/contrastItemList", null, loginUser);
        return collect;
    }


    // 记录到Mrp日志    operTitle:操作标题  method:操作方法  remarkJson:备注json
    private void recordMrpLog(String pid, String operTitle, String method, String remarkJson, LoginUser loginUser) {

        WkMrplogPojo wkMrplogPojo = new WkMrplogPojo();
        wkMrplogPojo.setPid(pid);
        // wkMrplogPojo.setRemarkjson(remarkJson);
        wkMrplogPojo.setOpertitle(operTitle);
        wkMrplogPojo.setMethod(method);
        wkMrplogPojo.setOperuserid(loginUser.getUserid());
        wkMrplogPojo.setOpername(loginUser.getUsername());
        wkMrplogPojo.setOpertime(new Date());
        wkMrplogPojo.setTenantid(loginUser.getTenantid());
//        wkMrplogPojo.setBusinesstype("");
//        wkMrplogPojo.setRequestmethod();
//        wkMrplogPojo.setOperatortype();
//        wkMrplogPojo.setOperurl();
//        wkMrplogPojo.setOperip();
//        wkMrplogPojo.setOperlocation();
//        wkMrplogPojo.setOperparam();
//        wkMrplogPojo.setJsonresult();
//        wkMrplogPojo.setStatus();
//        wkMrplogPojo.setErrormsg();
        wkMrplogService.insert(wkMrplogPojo);
    }


    //    /**
//     * 通过ID查询单条数据
//     *
//     * @param key 主键
//     * @return 实例对象
//     */
//    @Override
//    public List<WkMrpitemPojo> pullMrpAll(String key, String tid) {
//        // 获得用户数据
//        WkMrpPojo wkMrpPojo = this.wkMrpMapper.getEntity(key, tid);
//        //Item子表处理
//        List<WkMrpitemPojo> lst = this.wkMrpitemMapper.getList(wkMrpPojo.getId(), wkMrpPojo.getTenantid());
//
//        for (WkMrpitemPojo item : lst) {
//            pullMrpitem(item.getId(), item.getTenantid());
//
//            item.setMativqty(this.wkMrpMapper.getInveQty(item.getGoodsid(), tid));
//            if (item.getMativqty() == null) item.setMativqty(0D);
//            item.setWkwsremqty(this.wkMrpMapper.getWkWsRemQty(item.getGoodsid(), tid));
//            if (item.getWkwsremqty() == null) item.setWkwsremqty(0D);
//            item.setWkscremqty(this.wkMrpMapper.getWkScRemQty(item.getGoodsid(), tid));
//            if (item.getWkscremqty() == null) item.setWkscremqty(0D);
//            item.setBuyremqty(this.wkMrpMapper.getBuyRemQty(item.getGoodsid(), tid));
//            if (item.getBuyremqty() == null) item.setBuyremqty(0D);
//            item.setBusremqty(this.wkMrpMapper.getBusRemQty(item.getGoodsid(),item.getma tid));
//            if (item.getBusremqty() == null) item.setBusremqty(0D);
//            item.setMrpremqty(this.wkMrpMapper.getMrpRemQty(item.getGoodsid(), tid));
//            if (item.getMrpremqty() == null) item.setMrpremqty(0D);
//            item.setFreereqremqty(this.wkMrpMapper.getFreeReqRemQty(item.getGoodsid(), tid));
//            if (item.getFreereqremqty() == null) item.setFreereqremqty(0D);
//            Double stoqty = item.getMativqty() + item.getBuyremqty() + item.getWkwsremqty() + item.getWkscremqty();
//            stoqty = stoqty - item.getBusremqty() - item.getMrpremqty() - item.getFreereqremqty();
//            item.setStoqty(stoqty);
//            if (item.getStoqty() > item.getBomqty()) {
//                item.setNeedqty(0D);
//            } else if (item.getStoqty() < 0) {
//                item.setNeedqty(item.getBomqty());
//            } else {
//                item.setNeedqty(item.getBomqty() - item.getStoqty());
//            }
//            item.setTenantid(wkMrpPojo.getTenantid());
//            item.setRevision(1);
//        }
//
//        return lst;
//    }

//
//    public WkMrpPojo pullMrpOld(String key, String tid) {
//        // 获得用户数据
//        WkMrpPojo wkMrpPojo = this.wkMrpMapper.getEntity(key, tid);
//        List<WkMrpobjPojo> lstobj = this.wkMrpobjMapper.getList(wkMrpPojo.getId(), wkMrpPojo.getTenantid());
//
//        //Item子表处理
//        List<WkMrpitemPojo> lst = this.wkMrpitemMapper.getList(wkMrpPojo.getId(), wkMrpPojo.getTenantid());
//        if (lst != null) {
//            //循环每个删除item子表
//            for (int i = 0; i < lst.size(); i++) {
//                this.wkMrpitemMapper.delete(lst.get(i).getId(), tid);
//            }
//        }
//
//        //读取子表
//        wkMrpPojo.setObj(lstobj);
//        List<WkMrpitemPojo> lstitem = new ArrayList<>();
//        for (WkMrpobjPojo obj : lstobj) {
//            WkMrpitemPojo item = new WkMrpitemPojo();
//            R r = this.goodsFeignService.getBomDetailByGoodsid(obj.getGoodsid(), obj.getQuantity(), tid);
//            List<MatBomdetailPojo> bomdetail = new ArrayList<>();
//            if (r.getCode() == 200) {
//                logger.info(r.getData().toString());
//                bomdetail = JSONArray.parseArray(r.getData().toString(), MatBomdetailPojo.class);
//            } else {
//                throw new RuntimeException("读取BOM树失败:" + r.getMsg());
//            }
//            for (MatBomdetailPojo bom : bomdetail) {
//                item = new WkMrpitemPojo();
//                BeanUtils.copyProperties(bom, item);
//                item.setId(bom.getItemid());
//                item.setPid(wkMrpPojo.getId());
//                item.setBomid(bom.getChildbomid());
//                item.setMrpobjid(obj.getId());
//                item.setBomtype("标准Bom");
//                if (item.getSubqty() == null) item.setSubqty(1D);
//                if (item.getMainqty() == null) item.setMainqty(1D);
//                if (item.getLossrate() == null) item.setLossrate(0D);
////                item.setItemparentid(bom.getItemParentid());
////                item.setBomitemid(bom.getBomitemid());
////                item.setGoodsid(bom.getGoodsid());
////                item.setGoodsuid(bom.getGoodsuid());
////                item.setGoodsname(bom.getGoodsname());
////                item.setGoodsspec(bom.getGoodsspec());
////                item.setGoodsunit(bom.getGoodsunit());
////                item.setPartid(bom.getPartid());
////                item.setLevelnum(bom.getLevelnum());
////                item.setBomqty(bom.getBomqty());
//                item.setMativqty(this.wkMrpMapper.getInveQty(bom.getGoodsid(), tid));
//                if (item.getMativqty() == null) item.setMativqty(0D);
//                item.setWkwsremqty(this.wkMrpMapper.getWkWsRemQty(bom.getGoodsid(), tid));
//                if (item.getWkwsremqty() == null) item.setWkwsremqty(0D);
//                item.setWkscremqty(this.wkMrpMapper.getWkScRemQty(bom.getGoodsid(), tid));
//                if (item.getWkscremqty() == null) item.setWkscremqty(0D);
//                item.setBuyremqty(this.wkMrpMapper.getBuyRemQty(bom.getGoodsid(), tid));
//                if (item.getBuyremqty() == null) item.setBuyremqty(0D);
//                item.setBusremqty(this.wkMrpMapper.getBusRemQty(bom.getGoodsid(), tid));
//                if (item.getBusremqty() == null) item.setBusremqty(0D);
//                item.setMrpremqty(this.wkMrpMapper.getMrpRemQty(bom.getGoodsid(), tid));
//                if (item.getMrpremqty() == null) item.setMrpremqty(0D);
//                item.setFreereqremqty(this.wkMrpMapper.getFreeReqRemQty(bom.getGoodsid(), tid));
//                if (item.getFreereqremqty() == null) item.setFreereqremqty(0D);
//                Double stoqty = item.getMativqty() + item.getBuyremqty() + item.getWkwsremqty() + item.getWkscremqty();
//                stoqty = stoqty - item.getBusremqty() - item.getMrpremqty() - item.getFreereqremqty();
//                item.setStoqty(stoqty);
//                if (item.getStoqty() > item.getBomqty()) {
//                    item.setNeedqty(0D);
//                } else if (item.getStoqty() < 0) {
//                    item.setNeedqty(item.getBomqty());
//                } else {
//                    item.setNeedqty(item.getBomqty() - item.getStoqty());
//                }
//                item.setTenantid(wkMrpPojo.getTenantid());
//                item.setRevision(1);
//                lstitem.add(item);
//
//                item = this.wkMrpitemService.clearNull(item);
//                WkMrpitemEntity wkMrpitemEntity = new WkMrpitemEntity();
//                BeanUtils.copyProperties(item, wkMrpitemEntity);
//                //插入子表
//                this.wkMrpitemMapper.insert(wkMrpitemEntity);
//            }
//        }
//        //读取子表
//        wkMrpPojo.setItem(lstitem);
//        return wkMrpPojo;
//    }
}
