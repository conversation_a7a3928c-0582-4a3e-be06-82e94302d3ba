package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BusOrdercostitemEntity;
import inks.service.sa.som.domain.pojo.BusOrdercostitemPojo;
import inks.service.sa.som.mapper.BusOrdercostitemMapper;
import inks.service.sa.som.service.BusOrdercostitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 成本项目(BusOrdercostitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-30 08:18:25
 */
@Service("busOrdercostitemService")
public class BusOrdercostitemServiceImpl implements BusOrdercostitemService {
    @Resource
    private BusOrdercostitemMapper busOrdercostitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusOrdercostitemPojo getEntity(String key,String tid) {
        return this.busOrdercostitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusOrdercostitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusOrdercostitemPojo> lst = busOrdercostitemMapper.getPageList(queryParam);
            PageInfo<BusOrdercostitemPojo> pageInfo = new PageInfo<BusOrdercostitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusOrdercostitemPojo> getList(String Pid,String tid) { 
        try {
            List<BusOrdercostitemPojo> lst = busOrdercostitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busOrdercostitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusOrdercostitemPojo insert(BusOrdercostitemPojo busOrdercostitemPojo) {
        //初始化item的NULL
        BusOrdercostitemPojo itempojo =this.clearNull(busOrdercostitemPojo);
        BusOrdercostitemEntity busOrdercostitemEntity = new BusOrdercostitemEntity(); 
        BeanUtils.copyProperties(itempojo,busOrdercostitemEntity);
        
          busOrdercostitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busOrdercostitemEntity.setRevision(1);  //乐观锁      
          this.busOrdercostitemMapper.insert(busOrdercostitemEntity);
        return this.getEntity(busOrdercostitemEntity.getId(),busOrdercostitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busOrdercostitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusOrdercostitemPojo update(BusOrdercostitemPojo busOrdercostitemPojo) {
        BusOrdercostitemEntity busOrdercostitemEntity = new BusOrdercostitemEntity(); 
        BeanUtils.copyProperties(busOrdercostitemPojo,busOrdercostitemEntity);
        this.busOrdercostitemMapper.update(busOrdercostitemEntity);
        return this.getEntity(busOrdercostitemEntity.getId(),busOrdercostitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busOrdercostitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busOrdercostitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusOrdercostitemPojo clearNull(BusOrdercostitemPojo busOrdercostitemPojo){
     //初始化NULL字段
     if(busOrdercostitemPojo.getPid()==null) busOrdercostitemPojo.setPid("");
     if(busOrdercostitemPojo.getGoodsid()==null) busOrdercostitemPojo.setGoodsid("");
     if(busOrdercostitemPojo.getItemcode()==null) busOrdercostitemPojo.setItemcode("");
     if(busOrdercostitemPojo.getItemname()==null) busOrdercostitemPojo.setItemname("");
     if(busOrdercostitemPojo.getItemspec()==null) busOrdercostitemPojo.setItemspec("");
     if(busOrdercostitemPojo.getItemunit()==null) busOrdercostitemPojo.setItemunit("");
     if(busOrdercostitemPojo.getQuantity()==null) busOrdercostitemPojo.setQuantity(0D);
     if(busOrdercostitemPojo.getStdprice()==null) busOrdercostitemPojo.setStdprice(0D);
     if(busOrdercostitemPojo.getStdamount()==null) busOrdercostitemPojo.setStdamount(0D);
     if(busOrdercostitemPojo.getRebate()==null) busOrdercostitemPojo.setRebate(0D);
     if(busOrdercostitemPojo.getRebatesec()==null) busOrdercostitemPojo.setRebatesec(0D);
     if(busOrdercostitemPojo.getPrice()==null) busOrdercostitemPojo.setPrice(0D);
     if(busOrdercostitemPojo.getAmount()==null) busOrdercostitemPojo.setAmount(0D);
     if(busOrdercostitemPojo.getItemtaxrate()==null) busOrdercostitemPojo.setItemtaxrate(0);
     if(busOrdercostitemPojo.getTaxprice()==null) busOrdercostitemPojo.setTaxprice(0D);
     if(busOrdercostitemPojo.getTaxtotal()==null) busOrdercostitemPojo.setTaxtotal(0D);
     if(busOrdercostitemPojo.getTaxamount()==null) busOrdercostitemPojo.setTaxamount(0D);
     if(busOrdercostitemPojo.getPlandate()==null) busOrdercostitemPojo.setPlandate(new Date());
     if(busOrdercostitemPojo.getRownum()==null) busOrdercostitemPojo.setRownum(0);
     if(busOrdercostitemPojo.getRemark()==null) busOrdercostitemPojo.setRemark("");
     if(busOrdercostitemPojo.getDisannulmark()==null) busOrdercostitemPojo.setDisannulmark(0);
     if(busOrdercostitemPojo.getDisannullisterid()==null) busOrdercostitemPojo.setDisannullisterid("");
     if(busOrdercostitemPojo.getDisannullister()==null) busOrdercostitemPojo.setDisannullister("");
     if(busOrdercostitemPojo.getDisannuldate()==null) busOrdercostitemPojo.setDisannuldate(new Date());
     if(busOrdercostitemPojo.getVirtualitem()==null) busOrdercostitemPojo.setVirtualitem(0);
     if(busOrdercostitemPojo.getAttributejson()==null) busOrdercostitemPojo.setAttributejson("");
     if(busOrdercostitemPojo.getCostitemjson()==null) busOrdercostitemPojo.setCostitemjson("");
     if(busOrdercostitemPojo.getCostgroupjson()==null) busOrdercostitemPojo.setCostgroupjson("");
     if(busOrdercostitemPojo.getClosed()==null) busOrdercostitemPojo.setClosed(0);
     if(busOrdercostitemPojo.getMachmark()==null) busOrdercostitemPojo.setMachmark(0);
     if(busOrdercostitemPojo.getCostitem1()==null) busOrdercostitemPojo.setCostitem1("");
     if(busOrdercostitemPojo.getCostitem2()==null) busOrdercostitemPojo.setCostitem2("");
     if(busOrdercostitemPojo.getCostitem3()==null) busOrdercostitemPojo.setCostitem3("");
     if(busOrdercostitemPojo.getCostitem4()==null) busOrdercostitemPojo.setCostitem4("");
     if(busOrdercostitemPojo.getCostitem5()==null) busOrdercostitemPojo.setCostitem5("");
     if(busOrdercostitemPojo.getCostitem6()==null) busOrdercostitemPojo.setCostitem6("");
     if(busOrdercostitemPojo.getCostitem7()==null) busOrdercostitemPojo.setCostitem7("");
     if(busOrdercostitemPojo.getCostitem8()==null) busOrdercostitemPojo.setCostitem8("");
     if(busOrdercostitemPojo.getCostitem9()==null) busOrdercostitemPojo.setCostitem9("");
     if(busOrdercostitemPojo.getCostitem10()==null) busOrdercostitemPojo.setCostitem10("");
     if(busOrdercostitemPojo.getTenantid()==null) busOrdercostitemPojo.setTenantid("");
     if(busOrdercostitemPojo.getRevision()==null) busOrdercostitemPojo.setRevision(0);
     return busOrdercostitemPojo;
     }
}
