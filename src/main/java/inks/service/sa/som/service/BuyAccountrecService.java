package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyAccountrecPojo;

/**
 * 采购结账(BuyAccountrec)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-28 20:24:37
 */
public interface BuyAccountrecService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyAccountrecPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyAccountrecPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyAccountrecPojo 实例对象
     * @return 实例对象
     */
    BuyAccountrecPojo insert(BuyAccountrecPojo buyAccountrecPojo);

    /**
     * 修改数据
     *
     * @param buyAccountrecpojo 实例对象
     * @return 实例对象
     */
    BuyAccountrecPojo update(BuyAccountrecPojo buyAccountrecpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    BuyAccountrecPojo getEntityByMax(String tid);

}
