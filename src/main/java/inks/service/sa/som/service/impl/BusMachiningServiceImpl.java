package inks.service.sa.som.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.StreamUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.BusMachiningEntity;
import inks.service.sa.som.domain.BusMachiningitemEntity;
import inks.service.sa.som.domain.constant.MyConstant;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.*;
import inks.service.sa.som.service.AppWorkgroupService;
import inks.service.sa.som.service.BusMachiningService;
import inks.service.sa.som.service.BusMachiningitemService;
import inks.service.sa.som.service.MatGoodsService;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;

/**
 * 销售订单(BusMachining)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-25 14:57:39
 */
@Service("busMachiningService")
public class BusMachiningServiceImpl implements BusMachiningService {

    @Resource
    private BusMachiningMapper busMachiningMapper;

    @Resource
    private BusMachiningitemMapper busMachiningitemMapper;
    @Resource
    private MatGoodsService matGoodsService;
    @Resource
    private AppWorkgroupService appWorkgroupService;

    @Resource
    private BusMachiningitemService busMachiningitemService;

    @Resource
    private BusOrdercostitemMapper busOrdercostitemMapper;
    @Resource
    private BusQuotationitemMapper busQuotationitemMapper;


    @Resource
    private SaRedisService saRedisService;


    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private SyncMapper syncMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusMachiningPojo getEntity(String key, String tid) {
        return this.busMachiningMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusMachiningitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusMachiningitemdetailPojo> lst = busMachiningMapper.getPageList(queryParam);
            for (BusMachiningitemdetailPojo machitem : lst) {
                //采购额
                //BuytaxAmt=sum(采购合同item.taxamount) where machitemid
                machitem.setBuytaxamt(busMachiningMapper.getBuyTaxAmt(machitem.getId(), machitem.getTenantid()));
            }
            PageInfo<BusMachiningitemdetailPojo> pageInfo = new PageInfo<BusMachiningitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusMachiningPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusMachiningPojo busMachiningPojo = this.busMachiningMapper.getEntity(key, tid);
            //读取子表
            busMachiningPojo.setItem(busMachiningitemMapper.getList(busMachiningPojo.getId(), busMachiningPojo.getTenantid()));
            return busMachiningPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusMachiningPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusMachiningPojo> lst = busMachiningMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busMachiningitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusMachiningPojo> pageInfo = new PageInfo<BusMachiningPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusMachiningPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusMachiningPojo> lst = busMachiningMapper.getPageTh(queryParam);
            PageInfo<BusMachiningPojo> pageInfo = new PageInfo<BusMachiningPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busMachiningPojo 实例对象
     * @return 实例对象
     */
    @Override
    //@Transactional
    public BusMachiningPojo insert(BusMachiningPojo busMachiningPojo) {
        Date date = new Date();
        //初始化NULL字段
        if (busMachiningPojo.getRefno() == null) busMachiningPojo.setRefno("");
        if (busMachiningPojo.getBilltype() == null) busMachiningPojo.setBilltype("");
        if (busMachiningPojo.getBilltitle() == null) busMachiningPojo.setBilltitle("");
        if (busMachiningPojo.getBilldate() == null) busMachiningPojo.setBilldate(date);
        if (busMachiningPojo.getGroupid() == null) busMachiningPojo.setGroupid("");
        if (busMachiningPojo.getCustorderid() == null) busMachiningPojo.setCustorderid("");
        if (busMachiningPojo.getLogisticsmode() == null) busMachiningPojo.setLogisticsmode("");
        if (busMachiningPojo.getLogisticsport() == null) busMachiningPojo.setLogisticsport("");
        if (busMachiningPojo.getCountry() == null) busMachiningPojo.setCountry("");
        if (busMachiningPojo.getAdvaamount() == null) busMachiningPojo.setAdvaamount(0D);
        if (busMachiningPojo.getSalesman() == null) busMachiningPojo.setSalesman("");
        if (busMachiningPojo.getSalesmanid() == null) busMachiningPojo.setSalesmanid("");
        if (busMachiningPojo.getTaxrate() == null) busMachiningPojo.setTaxrate(0);
        if (busMachiningPojo.getSummary() == null) busMachiningPojo.setSummary("");
        if (busMachiningPojo.getCreateby() == null) busMachiningPojo.setCreateby("");
        if (busMachiningPojo.getCreatebyid() == null) busMachiningPojo.setCreatebyid("");
        if (busMachiningPojo.getCreatedate() == null) busMachiningPojo.setCreatedate(date);
        if (busMachiningPojo.getLister() == null) busMachiningPojo.setLister("");
        if (busMachiningPojo.getListerid() == null) busMachiningPojo.setListerid("");
        if (busMachiningPojo.getModifydate() == null) busMachiningPojo.setModifydate(date);
        if (busMachiningPojo.getAssessor() == null) busMachiningPojo.setAssessor("");
        if (busMachiningPojo.getAssessorid() == null) busMachiningPojo.setAssessorid("");
        if (busMachiningPojo.getAssessdate() == null) busMachiningPojo.setAssessdate(date);
        if (busMachiningPojo.getBilltaxamount() == null) busMachiningPojo.setBilltaxamount(0D);
        if (busMachiningPojo.getBilltaxtotal() == null) busMachiningPojo.setBilltaxtotal(0D);
        if (busMachiningPojo.getBillamount() == null) busMachiningPojo.setBillamount(0D);
        if (busMachiningPojo.getBillstatecode() == null) busMachiningPojo.setBillstatecode("");
        if (busMachiningPojo.getBillstatedate() == null) busMachiningPojo.setBillstatedate(date);
        if (busMachiningPojo.getBillplandate() == null) busMachiningPojo.setBillplandate(date);
        if (busMachiningPojo.getBillwkwpid() == null) busMachiningPojo.setBillwkwpid("");
        if (busMachiningPojo.getBillwkwpcode() == null) busMachiningPojo.setBillwkwpcode("");
        if (busMachiningPojo.getBillwkwpname() == null) busMachiningPojo.setBillwkwpname("");
        if (busMachiningPojo.getGroupcode() == null) busMachiningPojo.setGroupcode("");
        if (busMachiningPojo.getDisannulcount() == null) busMachiningPojo.setDisannulcount(0);
        if (busMachiningPojo.getItemcount() == null) busMachiningPojo.setItemcount(busMachiningPojo.getItem().size());
        if (busMachiningPojo.getPickcount() == null) busMachiningPojo.setPickcount(0);
        if (busMachiningPojo.getFinishcount() == null) busMachiningPojo.setFinishcount(0);
        if (busMachiningPojo.getPrintcount() == null) busMachiningPojo.setPrintcount(0);
        if (busMachiningPojo.getWkfinishcount() == null) busMachiningPojo.setWkfinishcount(0);
        if (busMachiningPojo.getWkitemcount() == null) busMachiningPojo.setWkitemcount(0);
        if (busMachiningPojo.getWkwipcount() == null) busMachiningPojo.setWkwipcount(0);
        if (busMachiningPojo.getPayment() == null) busMachiningPojo.setPayment("");
        if (busMachiningPojo.getOaflowmark() == null) busMachiningPojo.setOaflowmark(0);
        if (busMachiningPojo.getBillcostbudgetamt() == null) busMachiningPojo.setBillcostbudgetamt(0D);
        if (busMachiningPojo.getFirstamt() == null) busMachiningPojo.setFirstamt(0D);
        if (busMachiningPojo.getInvoamt() == null) busMachiningPojo.setInvoamt(0D);
        if (busMachiningPojo.getInvocount() == null) busMachiningPojo.setInvocount(0);
        if (busMachiningPojo.getLastamt() == null) busMachiningPojo.setLastamt(0D);
//        if (busMachiningPojo.getMoneyid() == null) busMachiningPojo.setMoneyid(0);  //0可能也是一个币种
        if (busMachiningPojo.getMoneyname() == null) busMachiningPojo.setMoneyname("");
        if (busMachiningPojo.getMainplancount() == null) busMachiningPojo.setMainplancount(0);

        if (busMachiningPojo.getFinishtaxamount() == null) busMachiningPojo.setFinishtaxamount(0D);
        if (busMachiningPojo.getFinishamount() == null) busMachiningPojo.setFinishamount(0D);
        if (busMachiningPojo.getCustom1() == null) busMachiningPojo.setCustom1("");
        if (busMachiningPojo.getCustom2() == null) busMachiningPojo.setCustom2("");
        if (busMachiningPojo.getCustom3() == null) busMachiningPojo.setCustom3("");
        if (busMachiningPojo.getCustom4() == null) busMachiningPojo.setCustom4("");
        if (busMachiningPojo.getCustom5() == null) busMachiningPojo.setCustom5("");
        if (busMachiningPojo.getCustom6() == null) busMachiningPojo.setCustom6("");
        if (busMachiningPojo.getCustom7() == null) busMachiningPojo.setCustom7("");
        if (busMachiningPojo.getCustom8() == null) busMachiningPojo.setCustom8("");
        if (busMachiningPojo.getCustom9() == null) busMachiningPojo.setCustom9("");
        if (busMachiningPojo.getCustom10() == null) busMachiningPojo.setCustom10("");
        if (busMachiningPojo.getDeptid() == null) busMachiningPojo.setDeptid("");
        String tid = busMachiningPojo.getTenantid();
        if (tid == null) busMachiningPojo.setTenantid("");
        if (busMachiningPojo.getTenantname() == null) busMachiningPojo.setTenantname("");
        if (busMachiningPojo.getRevision() == null) busMachiningPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusMachiningEntity busMachiningEntity = new BusMachiningEntity();
        BeanUtils.copyProperties(busMachiningPojo, busMachiningEntity);
        //设置id和新建日期
        busMachiningEntity.setId(id);
        busMachiningEntity.setRevision(1);  //乐观锁
        //Item子表处理
        List<BusMachiningitemPojo> lst = busMachiningPojo.getItem();
        // 统计生产行数(销售订单子表生产需求WkQty>0的行数,虚拟品WkQty=0不统计)
        Integer wkItemCount = Math.toIntExact(lst.stream().filter(item -> item.getWkqty() > 0).count());
        busMachiningEntity.setWkitemcount(wkItemCount);
//        // 获取公式
//        Map<String, Object> tencfg = this.saRedisService.getValue("tenant_config:" + busMachiningPojo.getTenantid());
        String attrstrExpr = null;
//        if (tencfg != null && tencfg.containsKey("system.bill.attributestr")) {
//            attrstrExpr = tencfg.get("system.bill.attributestr").toString();
//        }
        for (int i = 0; i < lst.size(); i++) {
            // 同步订单核价单
            if (lst.get(i).getOrdercostitemid() != null && !"".equals(lst.get(i).getOrdercostitemid())) {
                BusOrdercostitemPojo busOrdercostitemPojo = this.busOrdercostitemMapper.getEntity(lst.get(i).getOrdercostitemid(), tid);
                if (busOrdercostitemPojo != null && busOrdercostitemPojo.getMachmark() >= 1) {
                    int Rowno = i + 1;
                    throw new RuntimeException(Rowno + "行,核价单已转订单,禁止重复转单:" + busOrdercostitemPojo.getGoodsname());
                }
            }
            // 同步订单报价价单
            if (lst.get(i).getQuotitemid() != null && !"".equals(lst.get(i).getQuotitemid())) {
                BusQuotationitemPojo busQuotationitemPojo = this.busQuotationitemMapper.getEntity(lst.get(i).getQuotitemid(), tid);
                if (busQuotationitemPojo != null && busQuotationitemPojo.getMachmark() >= 1) {
                    int Rowno = i + 1;
                    throw new RuntimeException(Rowno + "行,报价单已转订单,禁止重复转单:" + busQuotationitemPojo.getGoodsname());
                }
            }
//            // 计算子表AttributeStr(通过AttributeJson和system服务的计算公式)
//            if (isNotBlank(attrstrExpr) && isNotBlank(lst.get(i).getAttributejson())) {
//                lst.get(i).setAttributestr(inks.common.core.utils.bean.BeanUtils.calculateAttrStr(lst.get(i).getAttributejson(), attrstrExpr));
//            }
        }
        transactionTemplate.execute((status) -> {
            //插入主表
            this.busMachiningMapper.insert(busMachiningEntity);
            //循环每个item子表
            for (BusMachiningitemPojo busMachiningitemPojo : lst) {
                //初始化item的NULL
                BusMachiningitemPojo itemPojo = this.busMachiningitemService.clearNull(busMachiningitemPojo);
                BusMachiningitemEntity busMachiningitemEntity = new BusMachiningitemEntity();
                BeanUtils.copyProperties(itemPojo, busMachiningitemEntity);
                //设置id和Pid
                busMachiningitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busMachiningitemEntity.setPid(id);
                busMachiningitemEntity.setTenantid(tid);
                busMachiningitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busMachiningitemMapper.insert(busMachiningitemEntity);
                // 同步订单核价单
                if (busMachiningitemEntity.getOrdercostitemid() != null && !"".equals(busMachiningitemEntity.getOrdercostitemid())) {
                    this.busMachiningMapper.updateOrderCostItemFinish(busMachiningitemEntity.getOrdercostitemid(), busMachiningitemEntity.getOrdercostuid(), busMachiningitemEntity.getTenantid());
                    this.busMachiningMapper.updateOrderCostFinishCount(busMachiningitemEntity.getOrdercostitemid(), busMachiningitemEntity.getOrdercostuid(), busMachiningitemEntity.getTenantid());
                }
                // 同步订单报价单
                if (busMachiningitemEntity.getQuotitemid() != null && !"".equals(busMachiningitemEntity.getQuotitemid())) {
                    this.busMachiningMapper.updateQuotItemFinish(busMachiningitemEntity.getQuotitemid(), busMachiningitemEntity.getTenantid());
                    this.busMachiningMapper.updateQuotFinishCount(busMachiningitemEntity.getQuotitemid(), busMachiningitemEntity.getTenantid());
                }
                // 同步物料数量
                if (busMachiningitemPojo.getMatcode() != null && !"".equals(busMachiningitemPojo.getMatcode())) {
                    String matid = this.busMachiningMapper.getGoodsidByGoodsUid(busMachiningitemPojo.getMatcode(), tid);
                    this.busMachiningMapper.updateGoodsRequRemQty(matid, busMachiningitemPojo.getMatcode(), tid);
                }
            }
            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidSet = lst.stream().map(BusMachiningitemPojo::getGoodsid).collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsidSet.forEach(goodsid -> {
                syncMapper.updateGoodsBusRemQty(goodsid, tid);
            });
            // 同步货品数量 SQL替代MQ
            syncMapper.updateWorkgroupBusMachRemAmt(busMachiningPojo.getGroupid(), tid);
            return Boolean.TRUE;
        });


        //        if (lst != null) {
//            //循环每个item子表
//            for (int i = 0; i < lst.size(); i++) {
//                //初始化item的NULL
//                BusMachiningitemPojo itemPojo = this.busMachiningitemService.clearNull(lst.get(i));
//                BusMachiningitemEntity busMachiningitemEntity = new BusMachiningitemEntity();
//                BeanUtils.copyProperties(itemPojo, busMachiningitemEntity);
//                //设置id和Pid
//                busMachiningitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
//                busMachiningitemEntity.setPid(id);
//                busMachiningitemEntity.setTenantid(busMachiningPojo.getTenantid());
//                busMachiningitemEntity.setRevision(1);  //乐观锁
//                //插入子表
//                this.busMachiningitemMapper.insert(busMachiningitemEntity);
//
//                // 同步订单核价单
//                if (busMachiningitemEntity.getOrdercostitemid() != null && !"".equals(busMachiningitemEntity.getOrdercostitemid())) {
////                    BusOrdercostitemPojo busOrdercostitemPojo = this.busOrdercostitemMapper.getEntity(busMachiningitemEntity.getOrdercostitemid(), busMachiningitemEntity.getTenantid());
////                    if (busOrdercostitemPojo != null && busOrdercostitemPojo.getMachmark() == 1) {
////                        int Rowno = i + 1;
////                        throw new RuntimeException(Rowno + "行,核价单已转订单,禁止重复转单:" + busOrdercostitemPojo.getGoodsname());
////                    }
//                    this.busMachiningMapper.updateOrderCostItemFinish(busMachiningitemEntity.getOrdercostitemid(), busMachiningitemEntity.getOrdercostuid(), busMachiningitemEntity.getTenantid());
//                    this.busMachiningMapper.updateOrderCostFinishCount(busMachiningitemEntity.getOrdercostitemid(), busMachiningitemEntity.getOrdercostuid(), busMachiningitemEntity.getTenantid());
//                }
//
//                // 同步货品数量 MQ生产者  nanno 20230222
//                MqBaseParamPojo mqBaseParamPojo = new MqBaseParamPojo(lst.get(i).getGoodsid(), "BusRemQty", busMachiningPojo.getTenantid());
//                this.rabbitTemplate.convertAndSend("updateGoodsQty", JSON.toJSONString(mqBaseParamPojo));
//
//                // 同步货品数量lst.get(i).getMatcode().isEmpty()
//                if (lst.get(i).getMatcode() != null && !"".equals(lst.get(i).getMatcode())) {
//                    String matid = this.busMachiningMapper.getGoodsidByGoodsUid(lst.get(i).getMatcode(), busMachiningPojo.getTenantid());
//                    this.busMachiningMapper.updateGoodsRequRemQty(matid, lst.get(i).getMatcode(), busMachiningPojo.getTenantid());
//                }
//            }
//        }


        //返回Bill实例
        return this.getBillEntity(busMachiningEntity.getId(), busMachiningEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busMachiningPojo 实例对象 system.bill.attributestr
     * @return 实例对象
     */
    @Override
    //@Transactional
    public BusMachiningPojo update(BusMachiningPojo busMachiningPojo) {
        String tid = busMachiningPojo.getTenantid();
        List<BusMachiningitemPojo> lst = busMachiningPojo.getItem();
        if (lst == null) throw new RuntimeException("单据内容不能为空");
        // 最终需要同步的所有GoodsidSet
        Set<String> allGoodsidSet = new HashSet<>();
        // 获取公式
//        Map<String, Object> tencfg = this.saRedisService.getValue("tenant_config:" + busMachiningPojo.getTenantid());
        String attrstrExpr = null;
//        if (tencfg != null && tencfg.containsKey("system.bill.attributestr")) {
//            attrstrExpr = tencfg.get("system.bill.attributestr").toString();
//        }
        // 检查核价单
        for (int i = 0; i < lst.size(); i++) {
            // 同步订单核价单
            if (lst.get(i).getId() == null || "".equals(lst.get(i).getId())) {
                if (lst.get(i).getOrdercostitemid() != null && !"".equals(lst.get(i).getOrdercostitemid())) {
                    BusOrdercostitemPojo busOrdercostitemPojo = this.busOrdercostitemMapper.getEntity(lst.get(i).getOrdercostitemid(), tid);
                    if (busOrdercostitemPojo != null && busOrdercostitemPojo.getMachmark() == 1) {
                        int Rowno = i + 1;
                        throw new RuntimeException(Rowno + "行,核价单已转订单,禁止重复转单:" + busOrdercostitemPojo.getGoodsname());
                    }
                }
            } else {
                // 读出原有记录
                BusMachiningitemPojo itemDbPojo = this.busMachiningitemMapper.getEntity(lst.get(i).getId(), tid);
                // 加入主料是否变更 20221212 EricRen
                if (itemDbPojo.getMatcode() != null && !itemDbPojo.getMatcode().equals((lst.get(i).getMatcode()))) {
                    String matid = this.busMachiningMapper.getGoodsidByGoodsUid(itemDbPojo.getMatcode(), tid);
                    this.busMachiningMapper.updateGoodsRequRemQty(matid, itemDbPojo.getMatcode(), tid);
                }
            }
//            // 计算子表AttributeStr(通过AttributeJson和system服务的计算公式)
//            if (isNotBlank(attrstrExpr) && isNotBlank(lst.get(i).getAttributejson())) {
//                lst.get(i).setAttributestr(inks.common.core.utils.bean.BeanUtils.calculateAttrStr(lst.get(i).getAttributejson(), attrstrExpr));
//            }
        }
        //主表更改
        BusMachiningEntity busMachiningEntity = new BusMachiningEntity();
        BeanUtils.copyProperties(busMachiningPojo, busMachiningEntity);
        busMachiningEntity.setItemcount(lst.size());
        //Item子表处理
        transactionTemplate.execute((status) -> {
            // 统计生产行数(销售订单子表生产需求WkQty>0的行数);
            busMachiningEntity.setWkitemcount(Math.toIntExact(lst.stream().filter(item -> item.getWkqty() > 0).count()));
            this.busMachiningMapper.update(busMachiningEntity);
            //获取被删除的Item
            List<String> lstDelIds = busMachiningMapper.getDelItemIds(busMachiningPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    BusMachiningitemPojo busMachiningitemPojo = this.busMachiningitemMapper.getEntity(lstDelId, busMachiningEntity.getTenantid());
                    allGoodsidSet.add(busMachiningitemPojo.getGoodsid());
                    List<String> lstcite = getItemCiteBillName(lstDelId, busMachiningitemPojo.getPid(), busMachiningEntity.getTenantid());
                    if (!lstcite.isEmpty()) {
                        throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                    }
                    this.busMachiningitemMapper.delete(lstDelId, busMachiningEntity.getTenantid());
                    // 同步订单核价单
                    if (busMachiningitemPojo.getOrdercostitemid() != null && !"".equals(busMachiningitemPojo.getOrdercostitemid())) {
                        this.busMachiningMapper.updateOrderCostItemFinish(busMachiningitemPojo.getOrdercostitemid(), busMachiningitemPojo.getOrdercostuid(), busMachiningitemPojo.getTenantid());
                        this.busMachiningMapper.updateOrderCostFinishCount(busMachiningitemPojo.getOrdercostitemid(), busMachiningitemPojo.getOrdercostuid(), busMachiningitemPojo.getTenantid());
                    }


                    // 同步货品数量
                    if (busMachiningitemPojo.getMatcode() != null && !"".equals(busMachiningitemPojo.getMatcode())) {
                        String matid = this.busMachiningMapper.getGoodsidByGoodsUid(busMachiningitemPojo.getMatcode(), tid);
                        this.busMachiningMapper.updateGoodsRequRemQty(matid, busMachiningitemPojo.getMatcode(), tid);
                    }
                }
            }

            //循环每个item子表
            for (BusMachiningitemPojo busMachiningitemPojo : lst) {
                BusMachiningitemEntity busMachiningitemEntity = new BusMachiningitemEntity();
                if ("".equals(busMachiningitemPojo.getId()) || busMachiningitemPojo.getId() == null) {
                    //初始化item的NULL
                    BusMachiningitemPojo itemPojo = this.busMachiningitemService.clearNull(busMachiningitemPojo);
                    BeanUtils.copyProperties(itemPojo, busMachiningitemEntity);
                    //设置id和Pid
                    busMachiningitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    busMachiningitemEntity.setPid(busMachiningEntity.getId());  // 主表 id
                    busMachiningitemEntity.setTenantid(tid);   // 租户id
                    busMachiningitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.busMachiningitemMapper.insert(busMachiningitemEntity);
                    // 同步订单核价单
                    if (busMachiningitemEntity.getOrdercostitemid() != null && !"".equals(busMachiningitemEntity.getOrdercostitemid())) {
                        this.busMachiningMapper.updateOrderCostItemFinish(busMachiningitemEntity.getOrdercostitemid(), busMachiningitemEntity.getOrdercostuid(), busMachiningitemEntity.getTenantid());
                        this.busMachiningMapper.updateOrderCostFinishCount(busMachiningitemEntity.getOrdercostitemid(), busMachiningitemEntity.getOrdercostuid(), busMachiningitemEntity.getTenantid());
                    }

                } else {
                    busMachiningitemPojo.setTenantid(tid);
                    // 读出原有记录
                    BeanUtils.copyProperties(busMachiningitemPojo, busMachiningitemEntity);
                    this.busMachiningitemMapper.update(busMachiningitemEntity);
                    // 刷新有送货单单价；Eric 20220708
//                    if (tencfg != null && tencfg.get("module.sale.machsyncdeliprice") != null) {
//                        String machsyncdeliprice = tencfg.get("module.sale.machsyncdeliprice").toString();
//                        if (machsyncdeliprice.equals("true")) {
//                            this.busMachiningMapper.updateMachDeliPrice(busMachiningitemPojo);
//                            this.busMachiningMapper.updateMachDeliBillAmt(busMachiningitemPojo);
//                        }
//                    }
                }
                // 同步货品数量
                if (busMachiningitemPojo.getMatcode() != null && !"".equals(busMachiningitemPojo.getMatcode())) {
                    String matid = this.busMachiningMapper.getGoodsidByGoodsUid(busMachiningitemPojo.getMatcode(), tid);
                    this.busMachiningMapper.updateGoodsRequRemQty(matid, busMachiningitemPojo.getMatcode(), tid);
                }
            }
            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BusMachiningitemPojo::getGoodsid).collect(Collectors.toSet());
            allGoodsidSet.addAll(goodsidLstSet);
            // 同步货品数量 SQL替代MQ
            allGoodsidSet.forEach(goodsid -> {
                syncMapper.updateGoodsBusRemQty(goodsid, tid);
            });
            // 同步货品数量 SQL替代MQ
            syncMapper.updateWorkgroupBusMachRemAmt(busMachiningPojo.getGroupid(), tid);

            return Boolean.TRUE;
        });

//        if (!lstDel.isEmpty()) {
//            for (BusMachiningitemPojo busMachiningitemPojo : lstDel) {
//                // 同步货品数量 MQ生产者  nanno 20230222
//                MqBaseParamPojo mqBaseParamPojo = new MqBaseParamPojo(busMachiningitemPojo.getGoodsid(), "BusRemQty", tid);
//                this.rabbitTemplate.convertAndSend("updateGoodsQty", JSON.toJSONString(mqBaseParamPojo));
//            }
//        }

        //返回Bill实例
        return this.getBillEntity(busMachiningEntity.getId(), busMachiningEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        BusMachiningPojo busMachiningPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BusMachiningitemPojo> lst = busMachiningPojo.getItem();
        Integer delNum;
        delNum = transactionTemplate.execute((status) -> {
            if (lst != null) {
                //循环每个删除item子表
                for (BusMachiningitemPojo busMachiningitemPojo : lst) {
                    List<String> lstcite = getItemCiteBillName(busMachiningitemPojo.getId(), busMachiningitemPojo.getPid(), tid);
                    if (!lstcite.isEmpty()) {
                        throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                    }
                    this.busMachiningitemMapper.delete(busMachiningitemPojo.getId(), tid);
                    // 同步订单核价单
                    if (busMachiningitemPojo.getOrdercostitemid() != null && !"".equals(busMachiningitemPojo.getOrdercostitemid())) {
                        this.busMachiningMapper.updateOrderCostItemFinish(busMachiningitemPojo.getOrdercostitemid(), busMachiningitemPojo.getOrdercostuid(), busMachiningitemPojo.getTenantid());
                    }

                    // 同步订单报价单
                    if (busMachiningitemPojo.getQuotitemid() != null && !"".equals(busMachiningitemPojo.getQuotitemid())) {
                        this.busMachiningMapper.updateQuotItemFinish(busMachiningitemPojo.getQuotitemid(), busMachiningitemPojo.getTenantid());
                    }
                    // 同步货品数量
                    if (busMachiningitemPojo.getMatcode() != null && !"".equals(busMachiningitemPojo.getMatcode())) {
                        String matid = this.busMachiningMapper.getGoodsidByGoodsUid(busMachiningitemPojo.getMatcode(), busMachiningPojo.getTenantid());
                        this.busMachiningMapper.updateGoodsRequRemQty(matid, busMachiningitemPojo.getMatcode(), busMachiningPojo.getTenantid());
                    }
                }
            }
            this.busMachiningMapper.delete(key, tid);
            if (lst != null) {
                //循环每个删除item子表
                for (BusMachiningitemPojo busMachiningitemPojo : lst) {
                    // 同步订单核价单
                    if (busMachiningitemPojo.getOrdercostitemid() != null && !"".equals(busMachiningitemPojo.getOrdercostitemid())) {
                        this.busMachiningMapper.updateOrderCostFinishCount(busMachiningitemPojo.getOrdercostitemid(), busMachiningitemPojo.getOrdercostuid(), busMachiningitemPojo.getTenantid());
                    }
                    // 同步订单报价单
                    if (busMachiningitemPojo.getQuotitemid() != null && !"".equals(busMachiningitemPojo.getQuotitemid())) {
                        this.busMachiningMapper.updateQuotFinishCount(busMachiningitemPojo.getQuotitemid(), busMachiningitemPojo.getTenantid());
                    }
                }
            }
            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BusMachiningitemPojo::getGoodsid).collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                syncMapper.updateGoodsBusRemQty(goodsid, tid);
            });
            // 同步货品数量 SQL替代MQ
            syncMapper.updateWorkgroupBusMachRemAmt(busMachiningPojo.getGroupid(), tid);
            return lst.size();
        });

        return delNum;
    }


    /**
     * 审核数据
     *
     * @param busMachiningPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusMachiningPojo approval(BusMachiningPojo busMachiningPojo) {
        //主表更改
        BusMachiningEntity busMachiningEntity = new BusMachiningEntity();
        BeanUtils.copyProperties(busMachiningPojo, busMachiningEntity);
        this.busMachiningMapper.approval(busMachiningEntity);
        //返回Bill实例
        return this.getBillEntity(busMachiningEntity.getId(), busMachiningEntity.getTenantid());
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusMachiningPojo disannul(List<BusMachiningitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            BusMachiningitemPojo Pojo = lst.get(i);
            BusMachiningitemPojo dbPojo = this.busMachiningitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getWipused() == 1 || dbPojo.getWkquantity() > 0 || dbPojo.getBuyquantity() > 0 ||
                            dbPojo.getPickqty() > 0 || dbPojo.getFinishqty() > 0) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已被引用,禁止作废操作");
                    }
                    BusMachiningitemEntity entity = new BusMachiningitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
//                    entity.setDisannuldate(new Date());
//                    entity.setDisannullister(loginUser.getRealname());
//                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(loginUser.getTenantid());
                    this.busMachiningitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BusMachiningitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            syncMapper.updateGoodsBusRemQty(goodsid, tid);
        });
        // 同步客户余额(来自同一个客户) SQL替代MQ
        String groupid = busMachiningitemMapper.getGroupidByItemid(lst.get(0).getId(), tid);
        syncMapper.updateWorkgroupBusMachRemAmt(groupid, tid);

        if (disNum > 0) {
            this.busMachiningMapper.updateDisannulCount(Pid, tid);
            //主表更改
            BusMachiningEntity busMachiningEntity = new BusMachiningEntity();
            busMachiningEntity.setId(Pid);
            busMachiningEntity.setLister(loginUser.getRealname());
            busMachiningEntity.setListerid(loginUser.getUserid());
            busMachiningEntity.setModifydate(new Date());
            busMachiningEntity.setTenantid(loginUser.getTenantid());
            this.busMachiningMapper.update(busMachiningEntity);
            //返回Bill实例
            return this.getBillEntity(busMachiningEntity.getId(), busMachiningEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    //@OperLog(title = "中止销售订单")
    public BusMachiningPojo closed(List<BusMachiningitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            BusMachiningitemPojo Pojo = lst.get(i);
            BusMachiningitemPojo dbPojo = this.busMachiningitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    BusMachiningitemEntity entity = new BusMachiningitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.busMachiningitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BusMachiningitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            syncMapper.updateGoodsBusRemQty(goodsid, tid);
        });
        // 同步客户余额(来自同一个客户) SQL替代MQ
        String groupid = busMachiningitemMapper.getGroupidByItemid(lst.get(0).getId(), tid);
        syncMapper.updateWorkgroupBusMachRemAmt(groupid, tid);

        if (disNum > 0) {
            this.busMachiningMapper.updateFinishCount(Pid, tid);
            //主表更改
            BusMachiningEntity busMachiningEntity = new BusMachiningEntity();
            busMachiningEntity.setId(Pid);
            busMachiningEntity.setLister(loginUser.getRealname());
            busMachiningEntity.setListerid(loginUser.getUserid());
            busMachiningEntity.setModifydate(new Date());
            busMachiningEntity.setTenantid(loginUser.getTenantid());
            this.busMachiningMapper.update(busMachiningEntity);
            //返回Bill实例
            return this.getBillEntity(busMachiningEntity.getId(), busMachiningEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    // 查询Item是否被引用
    public List<String> getItemCiteBillName(String key, String pid, String tid) {
        return this.busMachiningMapper.getItemCiteBillName(key, pid, tid);
    }

    /**
     * 查询 所有Item
     *
     * @param ids 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusMachiningitemPojo> getItemListByIds(String ids, String pid, String tid) {

        return this.busMachiningMapper.getItemListByIds(ids, pid, tid);
    }


    @Override
    @Async
    // 开始批量打印
    public void printBatchBillStart(List<String> ids, String uuid, String printapproved, ReportsPojo reportsPojo, LoginUser loginUser) {

        try {
            //设置当前计算任务进度
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100"); //开始处理代码
            missionMsg.put("msg", "任务开始处理");
            missionMsg.put("totalCount", ids.size());
            missionMsg.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.saRedisService.setCacheObject(MyConstant.PRINTBATCH_STATE + uuid, missionMsg, 600);
            //数据填充
            JasperPrint printAll = new JasperPrint();
            List<JasperPrint> lstPrint = new ArrayList<>();
            String content = reportsPojo.getRptdata();
            int successCount = 0;
            int failCount = 0;
            for (int a = 0; a < ids.size(); a++) {
                String key = ids.get(a);
                //获取单据信息
                BusMachiningPojo busMachiningPojo = this.getBillEntity(key, loginUser.getTenantid());
                if (busMachiningPojo == null) {
                    failCount++;
                } else if (printapproved != null && printapproved.equals("true") && "".equals(busMachiningPojo.getAssessor())) {
                    failCount++;
                } else {
                    //表头转MAP
                    Map<String, Object> map = inks.common.core.utils.bean.BeanUtils.beanToMap(busMachiningPojo);
                    // 加入公司信息
                    inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
                    // 判定是否需要追行
                    if (reportsPojo.getPagerow() > 0) {
                        int index = 0;
                        // 取行余数
                        index = busMachiningPojo.getItem().size() % reportsPojo.getPagerow();
                        if (index > 0) {
                            // 补全空白行
                            for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                                BusMachiningitemPojo busMachiningitemPojo = new BusMachiningitemPojo();
                                busMachiningPojo.getItem().add(busMachiningitemPojo);
                            }
                        }
                    }
                    // 带属性List转为Map  EricRen 20220427
                    List<Map<String, Object>> lst = attrcostListToMaps(busMachiningPojo.getItem());
                    //item转数据源
                    JRDataSource jrDataSource = new JRBeanCollectionDataSource(lst);
                    //报表生成
                    InputStream stream = new ByteArrayInputStream(content.getBytes());
                    //编译报表
                    JasperReport jasperReport = JasperCompileManager.compileReport(stream);
                    //数据填充
                    JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
                    if (a == 0) {
                        printAll = print;
                    } else {
                        List<JRPrintPage> pages = print.getPages();
                        for (JRPrintPage page : pages) {
                            printAll.addPage(page);
                        }
                    }
                    lstPrint.add(print);
                }
                successCount++;
                missionMsg.put("code", "150"); //任务处理中代码
                missionMsg.put("msg", "任务处理中");
                missionMsg.put("totalCount", ids.size());
                missionMsg.put("successCount", successCount);
                this.saRedisService.setCacheObject(MyConstant.PRINTBATCH_STATE + uuid, missionMsg, 600);
            }

//            //输出文件
//            String pdfPath = "D:\\test.pdf";
//            JasperExportManager.exportReportToPdfFile(printAll,pdfPath);


            byte[] base64File = StreamUtils.toByteArray(printAll);

            String cachekey = "report_pages:" + uuid;
            this.saRedisService.setKeyValue(cachekey, base64File, 60L, TimeUnit.MINUTES);

            missionMsg.put("code", "200");//任务完成代码
            missionMsg.put("msg", "任务处理完成");
            missionMsg.put("successCount", successCount);
            missionMsg.put("failCount", failCount);
            missionMsg.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.saRedisService.setCacheObject(MyConstant.PRINTBATCH_STATE + uuid, missionMsg, 600);

        } catch (Exception ignored) {

        }
    }


    @Override
    public int updatePrintcount(BusMachiningPojo billPrintPojo) {
        return this.busMachiningMapper.updatePrintcount(billPrintPojo);
    }

//    //查询系统参数systemFeign,获取到公式
//    private String getGongShi(String tid) {
//        // 查询系统参数systemFeign,获取到公式
//        R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, tid, null);
//        Map<String, String> tencfg = new HashMap<>();
//        if (r.getCode() == 200) {
//            tencfg = r.getData();
//        } else {
//            throw new BaseBusinessException("获得系统参数出错" + r.getMsg());
//        }
//        // 获取公式
//        String gongShi = tencfg.get("system.bill.attributestr"); // 读取公式: ${spuchang}*${spukuan}*${spuhou}
//        return gongShi;
//    }

    // 计算子表AttributeStr(通过AttributeJson和system服务的计算公式)
    private String calculateAttrStr(String attributejson, String gongShi) {
        // 解析 attributejson
        JSONArray jsonArray = JSON.parseArray(attributejson);
        // 遍历 JSON 数组并替换占位符
        for (Object obj : jsonArray) {
            if (obj instanceof JSONObject) {
                JSONObject jsonObj = (JSONObject) obj;
                String key = jsonObj.getString("key");
                String value = jsonObj.getString("value");
                // 构建占位符，例如 ${spuchang}
                String placeholder = "${" + key + "}";
                // 替换公式中的占位符为对应的值
                gongShi = gongShi.replace(placeholder, value);
            }
        }
        // 去除空格
        return gongShi.replaceAll("\\s", "");
    }


//    public static void main(String[] args) {
//        String attributejson = "[{\"key\":\"spuchang\",\"value\":\"2440\"},{\"key\":\"spukuan\",\"value\":\"1200\"},{\"key\":\"spuhou\",\"value\":\"3.6\"}]";
//        JSONArray jsonArray = JSON.parseArray(attributejson);
//        String gongShi = "${spuchang}*${spuhou}*${spukuan}";
//        for (Object obj : jsonArray) {
//            if (obj instanceof JSONObject) {
//                JSONObject jsonObj = (JSONObject) obj;
//                String key = jsonObj.getString("key");
//                String value = jsonObj.getString("value");
//
//                // 构建占位符，例如 ${spuchang}
//                String placeholder = "${" + key + "}";
//
//                // 替换公式中的占位符为对应的值
//                gongShi = gongShi.replace(placeholder, value);
//            }
//        }
//        System.out.println(gongShi);
//    }


    @Override//通过客户id查询销售订单总应收款，已收款，未发货金额
    public Map<String, Object> getLastAmtByGroupId(String groupid, String tenantid) {
        // 返回未关闭未作废订单子表的sum TaxAmount,AvgLastAmt
        Map<String, Object> map1 = this.busMachiningMapper.getLastAmtByGroupId(groupid, tenantid);
        // 返回未发货金额
        Double amt = this.busMachiningMapper.getOnlineMachTaxAmountByGroupId(groupid, tenantid);
        map1.put("onlinetaxamount", amt);

        return map1;
    }


}
