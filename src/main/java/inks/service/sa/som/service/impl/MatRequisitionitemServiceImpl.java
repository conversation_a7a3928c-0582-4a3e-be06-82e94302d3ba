package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatRequisitionitemEntity;
import inks.service.sa.som.domain.pojo.MatRequisitionitemPojo;
import inks.service.sa.som.mapper.MatRequisitionitemMapper;
import inks.service.sa.som.service.MatRequisitionitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 申领项目(MatRequisitionitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-25 13:11:56
 */
@Service("matRequisitionitemService")
public class MatRequisitionitemServiceImpl implements MatRequisitionitemService {
    @Resource
    private MatRequisitionitemMapper matRequisitionitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatRequisitionitemPojo getEntity(String key, String tid) {
        return this.matRequisitionitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatRequisitionitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatRequisitionitemPojo> lst = matRequisitionitemMapper.getPageList(queryParam);
            PageInfo<MatRequisitionitemPojo> pageInfo = new PageInfo<MatRequisitionitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatRequisitionitemPojo> getList(String Pid, String tid) {
        try {
            List<MatRequisitionitemPojo> lst = matRequisitionitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param matRequisitionitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatRequisitionitemPojo insert(MatRequisitionitemPojo matRequisitionitemPojo) {
        //初始化item的NULL
        MatRequisitionitemPojo itempojo = this.clearNull(matRequisitionitemPojo);
        MatRequisitionitemEntity matRequisitionitemEntity = new MatRequisitionitemEntity();
        BeanUtils.copyProperties(itempojo, matRequisitionitemEntity);

        matRequisitionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matRequisitionitemEntity.setRevision(1);  //乐观锁
        this.matRequisitionitemMapper.insert(matRequisitionitemEntity);
        return this.getEntity(matRequisitionitemEntity.getId(), matRequisitionitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matRequisitionitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatRequisitionitemPojo update(MatRequisitionitemPojo matRequisitionitemPojo) {
        MatRequisitionitemEntity matRequisitionitemEntity = new MatRequisitionitemEntity();
        BeanUtils.copyProperties(matRequisitionitemPojo, matRequisitionitemEntity);
        this.matRequisitionitemMapper.update(matRequisitionitemEntity);
        return this.getEntity(matRequisitionitemEntity.getId(), matRequisitionitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matRequisitionitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param matRequisitionitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatRequisitionitemPojo clearNull(MatRequisitionitemPojo matRequisitionitemPojo) {
        //初始化NULL字段
        if (matRequisitionitemPojo.getPid() == null) matRequisitionitemPojo.setPid("");
        if (matRequisitionitemPojo.getGoodsid() == null) matRequisitionitemPojo.setGoodsid("");
        if (matRequisitionitemPojo.getQuantity() == null) matRequisitionitemPojo.setQuantity(0D);
        if (matRequisitionitemPojo.getPlandate() == null) matRequisitionitemPojo.setPlandate(new Date());
        if (matRequisitionitemPojo.getRemark() == null) matRequisitionitemPojo.setRemark("");
        if (matRequisitionitemPojo.getRownum() == null) matRequisitionitemPojo.setRownum(0);
        if (matRequisitionitemPojo.getPickqty() == null) matRequisitionitemPojo.setPickqty(0D);
        if (matRequisitionitemPojo.getFinishqty() == null) matRequisitionitemPojo.setFinishqty(0D);
        if (matRequisitionitemPojo.getFinishcost() == null) matRequisitionitemPojo.setFinishcost(0D);
        if (matRequisitionitemPojo.getStatecode() == null) matRequisitionitemPojo.setStatecode("");
        if (matRequisitionitemPojo.getStatedate() == null) matRequisitionitemPojo.setStatedate(new Date());
        if (matRequisitionitemPojo.getCiteuid() == null) matRequisitionitemPojo.setCiteuid("");
        if (matRequisitionitemPojo.getCiteitemid() == null) matRequisitionitemPojo.setCiteitemid("");
        if (matRequisitionitemPojo.getCustomer() == null) matRequisitionitemPojo.setCustomer("");
        if (matRequisitionitemPojo.getCustpo() == null) matRequisitionitemPojo.setCustpo("");
        if (matRequisitionitemPojo.getBackqty() == null) matRequisitionitemPojo.setBackqty(0D);
        if (matRequisitionitemPojo.getBackcost() == null) matRequisitionitemPojo.setBackcost(0D);
        if (matRequisitionitemPojo.getClosed() == null) matRequisitionitemPojo.setClosed(0);
        if (matRequisitionitemPojo.getStdqty() == null) matRequisitionitemPojo.setStdqty(0D);
        if (matRequisitionitemPojo.getStoreid() == null) matRequisitionitemPojo.setStoreid("");
        if (matRequisitionitemPojo.getLocation() == null) matRequisitionitemPojo.setLocation("");
        if (matRequisitionitemPojo.getBatchno() == null) matRequisitionitemPojo.setBatchno("");
        if (matRequisitionitemPojo.getMachuid() == null) matRequisitionitemPojo.setMachuid("");
        if (matRequisitionitemPojo.getMachitemid() == null) matRequisitionitemPojo.setMachitemid("");
        if (matRequisitionitemPojo.getMachbatch() == null) matRequisitionitemPojo.setMachbatch("");
        if (matRequisitionitemPojo.getMachitemgoodid() == null) matRequisitionitemPojo.setMachitemgoodid("");
        if (matRequisitionitemPojo.getMachgroupid() == null) matRequisitionitemPojo.setMachgroupid("");
        if (matRequisitionitemPojo.getMainplanuid() == null) matRequisitionitemPojo.setMainplanuid("");
        if (matRequisitionitemPojo.getMainplanitemid() == null) matRequisitionitemPojo.setMainplanitemid("");
        if (matRequisitionitemPojo.getMainplanitemgoodid() == null) matRequisitionitemPojo.setMainplanitemgoodid("");
        if (matRequisitionitemPojo.getMrpuid() == null) matRequisitionitemPojo.setMrpuid("");
        if (matRequisitionitemPojo.getMrpitemid() == null) matRequisitionitemPojo.setMrpitemid("");
        if (matRequisitionitemPojo.getMrpitemgoodid() == null) matRequisitionitemPojo.setMrpitemgoodid("");
        if (matRequisitionitemPojo.getWkbilltype() == null) matRequisitionitemPojo.setWkbilltype(0);
        if (matRequisitionitemPojo.getWorkuid() == null) matRequisitionitemPojo.setWorkuid("");
        if (matRequisitionitemPojo.getWorkitemid() == null) matRequisitionitemPojo.setWorkitemid("");
        if (matRequisitionitemPojo.getWorkitemgoodid() == null) matRequisitionitemPojo.setWorkitemgoodid("");
        if (matRequisitionitemPojo.getWorkitemmatid() == null) matRequisitionitemPojo.setWorkitemmatid("");
        if (matRequisitionitemPojo.getParentgoodsid() == null) matRequisitionitemPojo.setParentgoodsid("");
        if (matRequisitionitemPojo.getDisannulmark() == null) matRequisitionitemPojo.setDisannulmark(0);
        if (matRequisitionitemPojo.getSourcetype() == null) matRequisitionitemPojo.setSourcetype(0);
        if (matRequisitionitemPojo.getAttributejson() == null) matRequisitionitemPojo.setAttributejson("");
        if (matRequisitionitemPojo.getCustom1() == null) matRequisitionitemPojo.setCustom1("");
        if (matRequisitionitemPojo.getCustom2() == null) matRequisitionitemPojo.setCustom2("");
        if (matRequisitionitemPojo.getCustom3() == null) matRequisitionitemPojo.setCustom3("");
        if (matRequisitionitemPojo.getCustom4() == null) matRequisitionitemPojo.setCustom4("");
        if (matRequisitionitemPojo.getCustom5() == null) matRequisitionitemPojo.setCustom5("");
        if (matRequisitionitemPojo.getCustom6() == null) matRequisitionitemPojo.setCustom6("");
        if (matRequisitionitemPojo.getCustom7() == null) matRequisitionitemPojo.setCustom7("");
        if (matRequisitionitemPojo.getCustom8() == null) matRequisitionitemPojo.setCustom8("");
        if (matRequisitionitemPojo.getCustom9() == null) matRequisitionitemPojo.setCustom9("");
        if (matRequisitionitemPojo.getCustom10() == null) matRequisitionitemPojo.setCustom10("");
        if (matRequisitionitemPojo.getTenantid() == null) matRequisitionitemPojo.setTenantid("");
        if (matRequisitionitemPojo.getTenantname() == null) matRequisitionitemPojo.setTenantname("");
        if (matRequisitionitemPojo.getRevision() == null) matRequisitionitemPojo.setRevision(0);
        return matRequisitionitemPojo;
    }


    /**
     * 查询 所有Item
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatRequisitionitemPojo> getListByWorkitemid(String key, String tid) {
        return this.matRequisitionitemMapper.getListByWorkitemid(key, tid);
    }

    @Override
    public List<Map<String, Object>> getMachListInMachitemids(Set<String> machitemids, String tid) {
        return this.matRequisitionitemMapper.getMachListInMachitemids(machitemids, tid);
    }
}
