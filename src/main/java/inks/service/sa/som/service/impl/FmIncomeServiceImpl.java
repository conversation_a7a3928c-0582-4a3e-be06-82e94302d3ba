package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.FmIncomeEntity;
import inks.service.sa.som.domain.FmIncomeitemEntity;
import inks.service.sa.som.domain.pojo.FmIncomePojo;
import inks.service.sa.som.domain.pojo.FmIncomeitemPojo;
import inks.service.sa.som.domain.pojo.FmIncomeitemdetailPojo;
import inks.service.sa.som.mapper.FmIncomeMapper;
import inks.service.sa.som.mapper.FmIncomeitemMapper;
import inks.service.sa.som.service.FmIncomeService;
import inks.service.sa.som.service.FmIncomeitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 其他收入(FmIncome)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-29 15:52:29
 */
@Service("fmIncomeService")
public class FmIncomeServiceImpl implements FmIncomeService {
    @Resource
    private FmIncomeMapper fmIncomeMapper;

    @Resource
    private FmIncomeitemMapper fmIncomeitemMapper;

    
    @Resource
    private FmIncomeitemService fmIncomeitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmIncomePojo getEntity(String key, String tid) {
        return this.fmIncomeMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmIncomeitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmIncomeitemdetailPojo> lst = fmIncomeMapper.getPageList(queryParam);
            PageInfo<FmIncomeitemdetailPojo> pageInfo = new PageInfo<FmIncomeitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmIncomePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            FmIncomePojo fmIncomePojo = this.fmIncomeMapper.getEntity(key, tid);
            //读取子表
            fmIncomePojo.setItem(fmIncomeitemMapper.getList(fmIncomePojo.getId(), fmIncomePojo.getTenantid()));
            return fmIncomePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmIncomePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmIncomePojo> lst = fmIncomeMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(fmIncomeitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<FmIncomePojo> pageInfo = new PageInfo<FmIncomePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmIncomePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmIncomePojo> lst = fmIncomeMapper.getPageTh(queryParam);
            PageInfo<FmIncomePojo> pageInfo = new PageInfo<FmIncomePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param fmIncomePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public FmIncomePojo insert(FmIncomePojo fmIncomePojo) {
//初始化NULL字段
        if (fmIncomePojo.getRefno() == null) fmIncomePojo.setRefno("");
        if (fmIncomePojo.getBilltype() == null) fmIncomePojo.setBilltype("");
        if (fmIncomePojo.getBilldate() == null) fmIncomePojo.setBilldate(new Date());
        if (fmIncomePojo.getBilltitle() == null) fmIncomePojo.setBilltitle("");
        if (fmIncomePojo.getProjectid() == null) fmIncomePojo.setProjectid("");
        if (fmIncomePojo.getProjcode() == null) fmIncomePojo.setProjcode("");
        if (fmIncomePojo.getProjname() == null) fmIncomePojo.setProjname("");
        if (fmIncomePojo.getGroupid() == null) fmIncomePojo.setGroupid("");
        if (fmIncomePojo.getChequenum() == null) fmIncomePojo.setChequenum("");
        if (fmIncomePojo.getMoneyid() == null) fmIncomePojo.setMoneyid("");
        if (fmIncomePojo.getAmount() == null) fmIncomePojo.setAmount(0D);
        if (fmIncomePojo.getCashaccid() == null) fmIncomePojo.setCashaccid("");
        if (fmIncomePojo.getOperator() == null) fmIncomePojo.setOperator("");
        if (fmIncomePojo.getProjectcode() == null) fmIncomePojo.setProjectcode("");
        if (fmIncomePojo.getSummary() == null) fmIncomePojo.setSummary("");
        if (fmIncomePojo.getCreateby() == null) fmIncomePojo.setCreateby("");
        if (fmIncomePojo.getCreatebyid() == null) fmIncomePojo.setCreatebyid("");
        if (fmIncomePojo.getCreatedate() == null) fmIncomePojo.setCreatedate(new Date());
        if (fmIncomePojo.getLister() == null) fmIncomePojo.setLister("");
        if (fmIncomePojo.getListerid() == null) fmIncomePojo.setListerid("");
        if (fmIncomePojo.getModifydate() == null) fmIncomePojo.setModifydate(new Date());
        if (fmIncomePojo.getModulecode() == null) fmIncomePojo.setModulecode("");
        if (fmIncomePojo.getCiteuid() == null) fmIncomePojo.setCiteuid("");
        if (fmIncomePojo.getCiteid() == null) fmIncomePojo.setCiteid("");
        if (fmIncomePojo.getBenefitid() == null) fmIncomePojo.setBenefitid("");
        if (fmIncomePojo.getCustom1() == null) fmIncomePojo.setCustom1("");
        if (fmIncomePojo.getCustom2() == null) fmIncomePojo.setCustom2("");
        if (fmIncomePojo.getCustom3() == null) fmIncomePojo.setCustom3("");
        if (fmIncomePojo.getCustom4() == null) fmIncomePojo.setCustom4("");
        if (fmIncomePojo.getCustom5() == null) fmIncomePojo.setCustom5("");
        if (fmIncomePojo.getCustom6() == null) fmIncomePojo.setCustom6("");
        if (fmIncomePojo.getCustom7() == null) fmIncomePojo.setCustom7("");
        if (fmIncomePojo.getCustom8() == null) fmIncomePojo.setCustom8("");
        if (fmIncomePojo.getCustom9() == null) fmIncomePojo.setCustom9("");
        if (fmIncomePojo.getCustom10() == null) fmIncomePojo.setCustom10("");
        if (fmIncomePojo.getTenantid() == null) fmIncomePojo.setTenantid("");
        if (fmIncomePojo.getTenantname() == null) fmIncomePojo.setTenantname("");
        if (fmIncomePojo.getRevision() == null) fmIncomePojo.setRevision(0);
        //生成id
        String id = UUID.randomUUID().toString();
        FmIncomeEntity fmIncomeEntity = new FmIncomeEntity();
        BeanUtils.copyProperties(fmIncomePojo, fmIncomeEntity);
        //设置id和新建日期
        fmIncomeEntity.setId(id);
        fmIncomeEntity.setRevision(1);  //乐观锁
        //插入主表
        this.fmIncomeMapper.insert(fmIncomeEntity);
        //Item子表处理
        List<FmIncomeitemPojo> lst = fmIncomePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                FmIncomeitemPojo itemPojo = this.fmIncomeitemService.clearNull(lst.get(i));
                FmIncomeitemEntity fmIncomeitemEntity = new FmIncomeitemEntity();
                BeanUtils.copyProperties(itemPojo, fmIncomeitemEntity);
                //设置id和Pid
                fmIncomeitemEntity.setId(UUID.randomUUID().toString());
                fmIncomeitemEntity.setPid(id);
                fmIncomeitemEntity.setTenantid(fmIncomePojo.getTenantid());
                fmIncomeitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.fmIncomeitemMapper.insert(fmIncomeitemEntity);
            }
        }
        // 同步出纳账号
        this.fmIncomeMapper.updateCashAmount(fmIncomePojo.getCashaccid(), fmIncomePojo.getAmount(), fmIncomePojo.getTenantid());
        //返回Bill实例
        return this.getBillEntity(fmIncomeEntity.getId(), fmIncomeEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param fmIncomePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public FmIncomePojo update(FmIncomePojo fmIncomePojo) {
        //主表更改
        FmIncomeEntity fmIncomeEntity = new FmIncomeEntity();
        BeanUtils.copyProperties(fmIncomePojo, fmIncomeEntity);
        FmIncomePojo dbPojo = this.fmIncomeMapper.getEntity(fmIncomePojo.getId(), fmIncomePojo.getTenantid());
        // 同步出纳账号
        this.fmIncomeMapper.updateCashAmount(fmIncomePojo.getCashaccid(), 0 - dbPojo.getAmount(), fmIncomePojo.getTenantid());
        this.fmIncomeMapper.update(fmIncomeEntity);
        // 同步出纳账号
        this.fmIncomeMapper.updateCashAmount(fmIncomePojo.getCashaccid(), fmIncomePojo.getAmount(), fmIncomePojo.getTenantid());
        if (fmIncomePojo.getItem() != null) {
            //Item子表处理
            List<FmIncomeitemPojo> lst = fmIncomePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = fmIncomeMapper.getDelItemIds(fmIncomePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.fmIncomeitemMapper.delete(lstDelIds.get(i), fmIncomeEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    FmIncomeitemEntity fmIncomeitemEntity = new FmIncomeitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        FmIncomeitemPojo itemPojo = this.fmIncomeitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, fmIncomeitemEntity);
                        //设置id和Pid
                        fmIncomeitemEntity.setId(UUID.randomUUID().toString());  // item id
                        fmIncomeitemEntity.setPid(fmIncomeEntity.getId());  // 主表 id
                        fmIncomeitemEntity.setTenantid(fmIncomePojo.getTenantid());   // 租户id
                        fmIncomeitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.fmIncomeitemMapper.insert(fmIncomeitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), fmIncomeitemEntity);
                        fmIncomeitemEntity.setTenantid(fmIncomePojo.getTenantid());
                        this.fmIncomeitemMapper.update(fmIncomeitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(fmIncomeEntity.getId(), fmIncomeEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        FmIncomePojo fmIncomePojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<FmIncomeitemPojo> lst = fmIncomePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.fmIncomeitemMapper.delete(lst.get(i).getId(), tid);
            }
        }

        this.fmIncomeMapper.delete(key, tid);
        // 同步出纳账号
        this.fmIncomeMapper.updateCashAmount(fmIncomePojo.getCashaccid(), 0 - fmIncomePojo.getAmount(), fmIncomePojo.getTenantid());
        return 1;
    }


}
