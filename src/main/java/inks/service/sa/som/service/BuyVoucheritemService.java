package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyVoucheritemPojo;

import java.util.List;
/**
 * 相关发票(BuyVoucheritem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-11 15:13:14
 */
public interface BuyVoucheritemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyVoucheritemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyVoucheritemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyVoucheritemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param buyVoucheritemPojo 实例对象
     * @return 实例对象
     */
    BuyVoucheritemPojo insert(BuyVoucheritemPojo buyVoucheritemPojo);

    /**
     * 修改数据
     *
     * @param buyVoucheritempojo 实例对象
     * @return 实例对象
     */
    BuyVoucheritemPojo update(BuyVoucheritemPojo buyVoucheritempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param buyVoucheritempojo 实例对象
     * @return 实例对象
     */
    BuyVoucheritemPojo clearNull(BuyVoucheritemPojo buyVoucheritempojo);
}
