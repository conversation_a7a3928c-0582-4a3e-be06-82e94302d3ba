package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatBrandEntity;
import inks.service.sa.som.domain.pojo.MatBrandPojo;
import inks.service.sa.som.mapper.MatBrandMapper;
import inks.service.sa.som.service.MatBrandService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 品牌列表(MatBrand)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-22 13:32:33
 */
@Service("matBrandService")
public class MatBrandServiceImpl implements MatBrandService {
    @Resource
    private MatBrandMapper matBrandMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatBrandPojo getEntity(String key, String tid) {
        return this.matBrandMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatBrandPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatBrandPojo> lst = matBrandMapper.getPageList(queryParam);
            PageInfo<MatBrandPojo> pageInfo = new PageInfo<MatBrandPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param matBrandPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatBrandPojo insert(MatBrandPojo matBrandPojo) {
    //初始化NULL字段
     if(matBrandPojo.getBrandcode()==null) matBrandPojo.setBrandcode("");
     if(matBrandPojo.getBrandname()==null) matBrandPojo.setBrandname("");
     if(matBrandPojo.getBranddesc()==null) matBrandPojo.setBranddesc("");
     if(matBrandPojo.getEnabledmark()==null) matBrandPojo.setEnabledmark(0);
     if(matBrandPojo.getRownum()==null) matBrandPojo.setRownum(0);
     if(matBrandPojo.getRemark()==null) matBrandPojo.setRemark("");
     if(matBrandPojo.getCreateby()==null) matBrandPojo.setCreateby("");
     if(matBrandPojo.getCreatebyid()==null) matBrandPojo.setCreatebyid("");
     if(matBrandPojo.getCreatedate()==null) matBrandPojo.setCreatedate(new Date());
     if(matBrandPojo.getLister()==null) matBrandPojo.setLister("");
     if(matBrandPojo.getListerid()==null) matBrandPojo.setListerid("");
     if(matBrandPojo.getModifydate()==null) matBrandPojo.setModifydate(new Date());
     if(matBrandPojo.getCustom1()==null) matBrandPojo.setCustom1("");
     if(matBrandPojo.getCustom2()==null) matBrandPojo.setCustom2("");
     if(matBrandPojo.getCustom3()==null) matBrandPojo.setCustom3("");
     if(matBrandPojo.getCustom4()==null) matBrandPojo.setCustom4("");
     if(matBrandPojo.getCustom5()==null) matBrandPojo.setCustom5("");
     if(matBrandPojo.getCustom6()==null) matBrandPojo.setCustom6("");
     if(matBrandPojo.getCustom7()==null) matBrandPojo.setCustom7("");
     if(matBrandPojo.getCustom8()==null) matBrandPojo.setCustom8("");
     if(matBrandPojo.getCustom9()==null) matBrandPojo.setCustom9("");
     if(matBrandPojo.getCustom10()==null) matBrandPojo.setCustom10("");
     if(matBrandPojo.getTenantid()==null) matBrandPojo.setTenantid("");
     if(matBrandPojo.getTenantname()==null) matBrandPojo.setTenantname("");
     if(matBrandPojo.getRevision()==null) matBrandPojo.setRevision(0);
        MatBrandEntity matBrandEntity = new MatBrandEntity(); 
        BeanUtils.copyProperties(matBrandPojo,matBrandEntity);
          //生成雪花id
          matBrandEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          matBrandEntity.setRevision(1);  //乐观锁
          this.matBrandMapper.insert(matBrandEntity);
        return this.getEntity(matBrandEntity.getId(),matBrandEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param matBrandPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatBrandPojo update(MatBrandPojo matBrandPojo) {
        MatBrandEntity matBrandEntity = new MatBrandEntity(); 
        BeanUtils.copyProperties(matBrandPojo,matBrandEntity);
        this.matBrandMapper.update(matBrandEntity);
        return this.getEntity(matBrandEntity.getId(),matBrandEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matBrandMapper.delete(key,tid) ;
    }
    
                                                                                                                                      
}
