package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyAccountinvoPojo;

import java.util.List;
/**
 * 收货单to发票(BuyAccountinvo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-17 10:52:03
 */
public interface BuyAccountinvoService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyAccountinvoPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyAccountinvoPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyAccountinvoPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param buyAccountinvoPojo 实例对象
     * @return 实例对象
     */
    BuyAccountinvoPojo insert(BuyAccountinvoPojo buyAccountinvoPojo);

    /**
     * 修改数据
     *
     * @param buyAccountinvopojo 实例对象
     * @return 实例对象
     */
    BuyAccountinvoPojo update(BuyAccountinvoPojo buyAccountinvopojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param buyAccountinvopojo 实例对象
     * @return 实例对象
     */
    BuyAccountinvoPojo clearNull(BuyAccountinvoPojo buyAccountinvopojo);
}
