package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaConfigService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.som.domain.BuyPlanEntity;
import inks.service.sa.som.domain.BuyPlanitemEntity;
import inks.service.sa.som.domain.constant.MyConstant;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.BuyPlanMapper;
import inks.service.sa.som.mapper.BuyPlanitemMapper;
import inks.service.sa.som.mapper.BuyPlanmergeMapper;
import inks.service.sa.som.service.BuyPlanService;
import inks.service.sa.som.service.BuyPlanitemService;
import inks.service.sa.som.service.BuyPlanmergeService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 采购计划(BuyPlan)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 20:32:45
 */
@Service("buyPlanService")
public class BuyPlanServiceImpl implements BuyPlanService {
    @Resource
    private BuyPlanMapper buyPlanMapper;

    @Resource
    private BuyPlanitemMapper buyPlanitemMapper;
    @Resource
    private BuyPlanmergeMapper buyPlanmergeMapper;
    @Resource
    private BuyPlanmergeService buyPlanmergeService;

    @Resource
    private BuyPlanitemService buyPlanitemService;
    @Resource
    private SaConfigService saConfigService;
    @Resource
    private Executor threadPoolExecutor;
    @Autowired
    private SaRedisService redisService;
    @Resource
    private SaBillcodeService saBillcodeService;

    // 将采购计划item子表转换为merge子表(相同goodsid+machuid的合并,累加数量、金额、含税金额、ItemCount)
    private static List<BuyPlanmergePojo> planItemListToMergeList(List<BuyPlanitemPojo> lst) {
        // 创建一个HashMap，键是goodsid+machuid的组合，值是BuyPlanmergePojo对象
        Map<String, BuyPlanmergePojo> goodsid_Merge_Map = new HashMap<>();
        // 遍历原始item列表
        for (BuyPlanitemPojo item : lst) {
            // 使用goodsid+machuid生成一个唯一的键
            String key = item.getGoodsid() + "_" + item.getMachuid();
            // 检查这个键是否已经在映射中存在
            if (goodsid_Merge_Map.containsKey(key)) {
                // 如果存在，更新映射merge中的4个值:数量、金额、含税金额、ItemCount
                BuyPlanmergePojo mergeItem = goodsid_Merge_Map.get(key);
                mergeItem.setQuantity(mergeItem.getQuantity() + item.getQuantity());
                mergeItem.setAmount(mergeItem.getAmount() + item.getAmount());
                mergeItem.setTaxamount(mergeItem.getTaxamount() + item.getTaxamount());
                mergeItem.setItemcount(mergeItem.getItemcount() + 1);//合并了几行item
            } else {
                // 如果不存在，创建一个新的BuyPlanmergePojo对象并添加到映射中
                BuyPlanmergePojo mergeItem = new BuyPlanmergePojo();
                BeanUtils.copyProperties(item, mergeItem);
                mergeItem.setItemcount(1);//合并了几行item
                goodsid_Merge_Map.put(key, mergeItem);
            }
        }
        // 将映射的值转换为一个列表 (只有id还都没有)
        List<BuyPlanmergePojo> mergedListNew = new ArrayList<>(goodsid_Merge_Map.values());
        return mergedListNew;
    }

    private static void cleanNull(BuyPlanPojo buyPlanPojo) {
        if (buyPlanPojo.getRefno() == null) buyPlanPojo.setRefno("");
        if (buyPlanPojo.getBilltype() == null) buyPlanPojo.setBilltype("");
        if (buyPlanPojo.getBilltitle() == null) buyPlanPojo.setBilltitle("");
        if (buyPlanPojo.getBilldate() == null) buyPlanPojo.setBilldate(new Date());
        if (buyPlanPojo.getGroupid() == null) buyPlanPojo.setGroupid("");
        if (buyPlanPojo.getBranthname() == null) buyPlanPojo.setBranthname("");
        if (buyPlanPojo.getTaxrate() == null) buyPlanPojo.setTaxrate(0);
        if (buyPlanPojo.getArrivaladd() == null) buyPlanPojo.setArrivaladd("");
        if (buyPlanPojo.getOperator() == null) buyPlanPojo.setOperator("");
        if (buyPlanPojo.getSummary() == null) buyPlanPojo.setSummary("");
        if (buyPlanPojo.getCreateby() == null) buyPlanPojo.setCreateby("");
        if (buyPlanPojo.getCreatebyid() == null) buyPlanPojo.setCreatebyid("");
        if (buyPlanPojo.getCreatedate() == null) buyPlanPojo.setCreatedate(new Date());
        if (buyPlanPojo.getLister() == null) buyPlanPojo.setLister("");
        if (buyPlanPojo.getListerid() == null) buyPlanPojo.setListerid("");
        if (buyPlanPojo.getModifydate() == null) buyPlanPojo.setModifydate(new Date());
        if (buyPlanPojo.getAssessor() == null) buyPlanPojo.setAssessor("");
        if (buyPlanPojo.getAssessorid() == null) buyPlanPojo.setAssessorid("");
        if (buyPlanPojo.getAssessdate() == null) buyPlanPojo.setAssessdate(new Date());
        if (buyPlanPojo.getBilltaxamount() == null) buyPlanPojo.setBilltaxamount(0D);
        if (buyPlanPojo.getBilltaxtotal() == null) buyPlanPojo.setBilltaxtotal(0D);
        if (buyPlanPojo.getBillamount() == null) buyPlanPojo.setBillamount(0D);
        if (buyPlanPojo.getBillstatecode() == null) buyPlanPojo.setBillstatecode("");
        if (buyPlanPojo.getBillstatedate() == null) buyPlanPojo.setBillstatedate(new Date());
        if (buyPlanPojo.getBillplandate() == null) buyPlanPojo.setBillplandate(new Date());
        if (buyPlanPojo.getItemcount() == null) buyPlanPojo.setItemcount(buyPlanPojo.getItem().size());
        if (buyPlanPojo.getBuycount() == null) buyPlanPojo.setBuycount(0);
        if (buyPlanPojo.getFinishcount() == null) buyPlanPojo.setFinishcount(0);
        if (buyPlanPojo.getDisannulcount() == null) buyPlanPojo.setDisannulcount(0);
        if (buyPlanPojo.getPrintcount() == null) buyPlanPojo.setPrintcount(0);
        if (buyPlanPojo.getOaflowmark() == null) buyPlanPojo.setOaflowmark(0);
        if (buyPlanPojo.getMergecount() == null) buyPlanPojo.setMergecount(0);
        if (buyPlanPojo.getMergemark() == null) buyPlanPojo.setMergemark(0);
        if (buyPlanPojo.getTrimor() == null) buyPlanPojo.setTrimor("");
        if (buyPlanPojo.getTrimdate() == null) buyPlanPojo.setTrimdate(new Date());
        if (buyPlanPojo.getCustom1() == null) buyPlanPojo.setCustom1("");
        if (buyPlanPojo.getCustom3() == null) buyPlanPojo.setCustom3("");
        if (buyPlanPojo.getCustom2() == null) buyPlanPojo.setCustom2("");
        if (buyPlanPojo.getCustom4() == null) buyPlanPojo.setCustom4("");
        if (buyPlanPojo.getCustom5() == null) buyPlanPojo.setCustom5("");
        if (buyPlanPojo.getCustom6() == null) buyPlanPojo.setCustom6("");
        if (buyPlanPojo.getCustom7() == null) buyPlanPojo.setCustom7("");
        if (buyPlanPojo.getCustom8() == null) buyPlanPojo.setCustom8("");
        if (buyPlanPojo.getCustom9() == null) buyPlanPojo.setCustom9("");
        if (buyPlanPojo.getCustom10() == null) buyPlanPojo.setCustom10("");
        if (buyPlanPojo.getDeptid() == null) buyPlanPojo.setDeptid("");
        if (buyPlanPojo.getTenantid() == null) buyPlanPojo.setTenantid("");
        if (buyPlanPojo.getTenantname() == null) buyPlanPojo.setTenantname("");
        if (buyPlanPojo.getRevision() == null) buyPlanPojo.setRevision(0);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyPlanPojo getEntity(String key, String tid) {
        return this.buyPlanMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyPlanitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyPlanitemdetailPojo> lst = buyPlanMapper.getPageList(queryParam);
            PageInfo<BuyPlanitemdetailPojo> pageInfo = new PageInfo<BuyPlanitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyPlanPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BuyPlanPojo buyPlanPojo = this.buyPlanMapper.getEntity(key, tid);
            //读取子表
            buyPlanPojo.setItem(buyPlanitemMapper.getList(buyPlanPojo.getId(), tid));
            return buyPlanPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public BuyPlanPojo getMergeBillEntity(String key, String tid) {
        try {
            //读取主表
            BuyPlanPojo buyPlanPojo = this.buyPlanMapper.getEntity(key, tid);
            //读取合并子表
            buyPlanPojo.setMerge(buyPlanmergeMapper.getList(buyPlanPojo.getId(), tid));
            return buyPlanPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<BuyPlanitemPojo> getItemListByMergeid(String key, String tid) {
        return buyPlanitemMapper.getListByMergeid(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyPlanPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyPlanPojo> lst = buyPlanMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (BuyPlanPojo buyPlanPojo : lst) {
                buyPlanPojo.setItem(buyPlanitemMapper.getList(buyPlanPojo.getId(), buyPlanPojo.getTenantid()));
            }
            PageInfo<BuyPlanPojo> pageInfo = new PageInfo<BuyPlanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyPlanPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyPlanPojo> lst = buyPlanMapper.getPageTh(queryParam);
            PageInfo<BuyPlanPojo> pageInfo = new PageInfo<BuyPlanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param buyPlanPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyPlanPojo insert(BuyPlanPojo buyPlanPojo) {

        //初始化NULL字段
        cleanNull(buyPlanPojo);
        String tid = buyPlanPojo.getTenantid();
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BuyPlanEntity buyPlanEntity = new BuyPlanEntity();
        BeanUtils.copyProperties(buyPlanPojo, buyPlanEntity);
        //设置id和新建日期
        buyPlanEntity.setId(id);
        buyPlanEntity.setRevision(1);  //乐观锁
        //插入主表
        this.buyPlanMapper.insert(buyPlanEntity);
        //Item子表处理
        List<BuyPlanitemPojo> lst = buyPlanPojo.getItem();
        // 计划时检查MRP数
        // 读取指定系统参数 warning 警告* reject 禁止 na 为不控制
        String planchkmrpqty = saConfigService.getConfigValue("module.buy.planchkmrpqty", tid);
        if (lst != null) {
            //循环每个item子表
            for (BuyPlanitemPojo buyPlanitemPojo : lst) {
                //初始化item的NULL
                BuyPlanitemPojo itemPojo = this.buyPlanitemService.clearNull(buyPlanitemPojo);
                BuyPlanitemEntity buyPlanitemEntity = new BuyPlanitemEntity();
                BeanUtils.copyProperties(itemPojo, buyPlanitemEntity);
                //设置id和Pid
                buyPlanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                buyPlanitemEntity.setPid(id);
                buyPlanitemEntity.setTenantid(tid);
                buyPlanitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.buyPlanitemMapper.insert(buyPlanitemEntity);
                // 如果是MRP需求
                if ("MRP需求".equals(buyPlanPojo.getBilltype()) && !Objects.equals(1, buyPlanitemEntity.getMergemark())) {
                    this.buyPlanMapper.updateMrpBuyPlanFinish(itemPojo.getMrpitemid(), itemPojo.getMrpuid(), tid);
                    WkMrpitemPojo wkMrpitemPojo = this.buyPlanMapper.getMrpItemEntity(buyPlanitemPojo.getMrpitemid(), tid);
                    if (!"na".equals(planchkmrpqty) && wkMrpitemPojo != null && wkMrpitemPojo.getBuyorderqty() + wkMrpitemPojo.getBuyplanqty() > wkMrpitemPojo.getNeedqty()) {
                        double wkqty = wkMrpitemPojo.getBuyorderqty() + wkMrpitemPojo.getBuyplanqty();
                        throw new RuntimeException(buyPlanitemPojo.getGoodsuid() + ":采购总数" + wkqty + "大于需求数" + wkMrpitemPojo.getNeedqty());
                    }
                    // 刷新MRP 采购相关完工数(采购订单、采购计划)
                    this.buyPlanMapper.updateMrpBuyFinishCount(itemPojo.getMrpitemid(), tid);
                    // 刷新MRP完工数
                    this.buyPlanMapper.updateMrpFinishCount(itemPojo.getMrpitemid(), tid);
                }
            }

            // 是否创建merge合并子表?
            // 读取指定系统参数 是否进行item合并创建merge 为"true"时合并
            String planmerge = saConfigService.getConfigValue("module.buy.planmerge", tid);
            if ("true".equals(planmerge)) {
                // 从数据库查出itemList:保证有id
                List<BuyPlanitemPojo> itemListDB = buyPlanitemMapper.getList(id, tid);
                // 将采购计划item子表转换为merge子表(相同goodsid+machuid的合并,累加数量、金额、含税金额、ItemCount)
                //  (只有id还都没有)
                List<BuyPlanmergePojo> mergedListNew = planItemListToMergeList(itemListDB);

                // 创建一个映射，键是goodsid+machuid的组合，值是List<BuyPlanitemPojo>
                Map<String, List<BuyPlanitemPojo>> goodsid_ItemList_Map = itemListDB.stream()
                        .collect(Collectors.groupingBy(item -> item.getGoodsid() + "_" + item.getMachuid()));
                //循环每个merge子表插入,插入后反写Buy_PlanItem.Mergeid
                for (BuyPlanmergePojo mergePojo : mergedListNew) {
                    mergePojo.setPid(id);
                    BuyPlanmergePojo insert = this.buyPlanmergeService.insert(mergePojo);
                    String mergeidInsert = insert.getId();
                    // 取出原始 BuyPlanitemPojo 对象并设置 mergeid
                    String key = mergePojo.getGoodsid() + "_" + mergePojo.getMachuid();
                    List<BuyPlanitemPojo> items = goodsid_ItemList_Map.get(key);
                    for (BuyPlanitemPojo item : items) {
                        //更新Buy_PlanItem.Mergeid '合并行id'
                        this.buyPlanitemMapper.updateMergeid(item.getId(), mergeidInsert, tid);
                    }
                }
                //更新合并行数 100条item合并为3条merge,则取3
                this.buyPlanMapper.updateMergeCount(id, mergedListNew.size(), tid);
            }
        }
        //返回Bill实例
        return this.getBillEntity(buyPlanEntity.getId(), tid);

    }

    /**
     * 修改数据
     *
     * @param buyPlanPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyPlanPojo update(BuyPlanPojo buyPlanPojo) {
        String tid = buyPlanPojo.getTenantid();
        //主表更改
        BuyPlanEntity buyPlanEntity = new BuyPlanEntity();
        BeanUtils.copyProperties(buyPlanPojo, buyPlanEntity);
        if (buyPlanPojo.getItem() != null) buyPlanEntity.setItemcount(buyPlanPojo.getItem().size());
        this.buyPlanMapper.update(buyPlanEntity);
        // 计划时检查MRP数
        // 读取指定系统参数 warning 警告* reject 禁止 na 为不控制
        String planchkmrpqty = saConfigService.getConfigValue("module.buy.planchkmrpqty", tid);
        if (buyPlanPojo.getItem() != null) {
            //Item子表处理
            List<BuyPlanitemPojo> lst = buyPlanPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = buyPlanMapper.getDelItemIds(buyPlanPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    BuyPlanitemPojo dbPojo = this.buyPlanitemMapper.getEntity(lstDelId, tid);
                    // 加上引用检查
                    List<String> lstcite = getItemCiteBillName(dbPojo.getId(), dbPojo.getPid(), tid);
                    if (!lstcite.isEmpty()) {
                        throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                    }
                    this.buyPlanitemMapper.delete(lstDelId, buyPlanEntity.getTenantid());
                    // 如果是MRP需求
                    if ("MRP需求".equals(buyPlanPojo.getBilltype())) {
                        this.buyPlanMapper.updateMrpBuyPlanFinish(dbPojo.getMrpitemid(), dbPojo.getMrpuid(), tid);
                        // 刷新MRP 采购相关完工数(采购订单、采购计划)
                        this.buyPlanMapper.updateMrpBuyFinishCount(dbPojo.getMrpitemid(), tid);
                        // 刷新MRP完工数
                        this.buyPlanMapper.updateMrpFinishCount(dbPojo.getMrpitemid(), tid);
                    }
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (BuyPlanitemPojo buyPlanitemPojo : lst) {
                    BuyPlanitemEntity buyPlanitemEntity = new BuyPlanitemEntity();
                    if ("".equals(buyPlanitemPojo.getId()) || buyPlanitemPojo.getId() == null) {
                        //初始化item的NULL
                        BuyPlanitemPojo itemPojo = this.buyPlanitemService.clearNull(buyPlanitemPojo);
                        BeanUtils.copyProperties(itemPojo, buyPlanitemEntity);
                        //设置id和Pid
                        buyPlanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        buyPlanitemEntity.setPid(buyPlanEntity.getId());  // 主表 id
                        buyPlanitemEntity.setTenantid(tid);   // 租户id
                        buyPlanitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.buyPlanitemMapper.insert(buyPlanitemEntity);
                    } else {
                        BeanUtils.copyProperties(buyPlanitemPojo, buyPlanitemEntity);
                        buyPlanitemEntity.setTenantid(tid);
                        this.buyPlanitemMapper.update(buyPlanitemEntity);
                    }

                    // 如果是MRP需求
                    if ("MRP需求".equals(buyPlanPojo.getBilltype())) {
                        Double orgQty = 0D;
                        if ("".equals(buyPlanitemPojo.getId()) || buyPlanitemPojo.getId() == null) {
                            orgQty = 0D;
                        } else {
                            BuyPlanitemPojo entity = buyPlanitemMapper.getEntity(buyPlanitemPojo.getId(), tid);
                            if (entity != null) {
                                orgQty = entity.getQuantity();
                            }
                        }
                        this.buyPlanMapper.updateMrpBuyPlanFinish(buyPlanitemPojo.getMrpitemid(), buyPlanitemPojo.getMrpuid(), tid);
                        WkMrpitemPojo wkMrpitemPojo = this.buyPlanMapper.getMrpItemEntity(buyPlanitemPojo.getMrpitemid(), tid);
                        if (!"na".equals(planchkmrpqty) && wkMrpitemPojo != null && wkMrpitemPojo.getBuyorderqty() + wkMrpitemPojo.getBuyplanqty() - orgQty > wkMrpitemPojo.getNeedqty()) {
                            double wkqty = wkMrpitemPojo.getBuyorderqty() + wkMrpitemPojo.getBuyplanqty() - orgQty;
                            throw new RuntimeException(buyPlanitemPojo.getGoodsuid() + ":采购总数" + wkqty + "大于需求数" + wkMrpitemPojo.getNeedqty());
                        }
                        // 刷新MRP 采购相关完工数(采购订单、采购计划)
                        this.buyPlanMapper.updateMrpBuyFinishCount(buyPlanitemPojo.getMrpitemid(), tid);
                        // 刷新MRP完工数
                        this.buyPlanMapper.updateMrpFinishCount(buyPlanitemPojo.getMrpitemid(), tid);
                    }
                }
            }


            // 是否创建merge合并子表? 注意: if ("true".equals(planmerge)) {}包裹下代码需要此处refreshItemList和update同步改动方法
            // 读取指定系统参数 是否进行item合并创建merge 为"true"时合并
            synsMergePlanItem(buyPlanEntity.getId(), tid);


        }
        //返回Bill实例
        return this.getBillEntity(buyPlanEntity.getId(), tid);
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public String delete(String key, String tid) {
        BuyPlanPojo buyPlanPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BuyPlanitemPojo> lst = buyPlanPojo.getItem();
        // 删除前先存储Mergeitems字段
        List<String> mergeItemIds = Collections.emptyList();
        if (lst != null) {
            //循环每个删除item子表
            for (BuyPlanitemPojo buyPlanitemPojo : lst) {
                // 确保 getMergeitems() 不为 null
                String mergeItems = buyPlanitemPojo.getMergeitems();
                if (isNotBlank(mergeItems)) {
                    mergeItemIds = Arrays.asList(mergeItems.split(","));
                }
                // 加上引用检查
                List<String> lstcite = getItemCiteBillName(buyPlanitemPojo.getId(), buyPlanitemPojo.getPid(), tid);
                if (!lstcite.isEmpty()) {
                    throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                }
                this.buyPlanitemMapper.delete(buyPlanitemPojo.getId(), tid);
                // 如果是MRP需求
                if ("MRP需求".equals(buyPlanPojo.getBilltype())) {
                    this.buyPlanMapper.updateMrpBuyPlanFinish(buyPlanitemPojo.getMrpitemid(), buyPlanitemPojo.getMrpuid(), buyPlanPojo.getTenantid());
                    // 刷新MRP 采购相关完工数(采购订单、采购计划)
                    this.buyPlanMapper.updateMrpBuyFinishCount(buyPlanitemPojo.getMrpitemid(), tid);
                    // 刷新MRP完工数
                    this.buyPlanMapper.updateMrpFinishCount(buyPlanitemPojo.getMrpitemid(), buyPlanPojo.getTenantid());
                }
            }
        }
        // merge子表处理
        // 是否创建merge合并子表?
        // 读取指定系统参数 是否进行item合并创建merge 为"true"时合并
        String planmerge = saConfigService.getConfigValue("module.buy.planmerge", tid);
        if ("true".equals(planmerge)) {
            buyPlanmergeMapper.deleteAllByPid(key, tid);
        }
        this.buyPlanMapper.delete(key, tid);

        // 如果 mergemark为1，执行mergeItem方法的反向操作
        if (Objects.equals(buyPlanPojo.getMergemark(), 1) && CollectionUtils.isNotEmpty(mergeItemIds)) {
            // 1.mergeItemIds：即合并前的主计划子表原单id集合 将 MergeMark 都置回为初始状态0
            buyPlanitemMapper.syncMergeMarkInIds(mergeItemIds, 0, tid);

            // 2.将销售订单子表的WkMegerMark置为0,WkMergeItem置为空
            List<String> machItemids = buyPlanitemMapper.getMachItemidsInPlanItemids(mergeItemIds, tid);
            buyPlanMapper.syncMachingItemWkMergeInIds(machItemids, 0, "", tid);

        }
        return buyPlanPojo.getRefno();
    }

    // 用于采购计划update或者采购计划mrp重拉refreshItemList时进行合并表的刷新
    public void synsMergePlanItem(String planid, String tid) {
        // 是否创建merge合并子表? 注意: if ("true".equals(planmerge)) {}包裹下代码需要此处refreshItemList和update同步改动方法
        // 读取指定系统参数 是否进行item合并创建merge 为"true"时合并
        String planmerge = saConfigService.getConfigValue("module.buy.planmerge", tid);
        if ("true".equals(planmerge)) {
            // 从数据库查出itemList:保证有id
            List<BuyPlanitemPojo> itemListDB = buyPlanitemMapper.getList(planid, tid);
            // 将采购计划item子表转换为merge子表(相同goodsid+machuid的合并,累加数量、金额、含税金额、ItemCount)
            //  (只有id还都没有)
            List<BuyPlanmergePojo> mergedListNew = planItemListToMergeList(itemListDB);
            // 获取数据库中的merge子表List
            List<BuyPlanmergePojo> mergeListDB = buyPlanmergeMapper.getList(planid, tid);
            // 将数据库中的列表转换为映射
            Map<String, BuyPlanmergePojo> mergeMapDB = new HashMap<>();
            for (BuyPlanmergePojo mergeDB : mergeListDB) {
                String keyDB = mergeDB.getGoodsid() + "_" + mergeDB.getMachuid();
                mergeMapDB.put(keyDB, mergeDB);
            }

            // 创建一个映射，键是 goodsid 和 machuid 的组合，值是 BuyPlanitemPojo 对象的列表
            Map<String, List<BuyPlanitemPojo>> goodsid_ItemList_Map = itemListDB.stream()
                    .collect(Collectors.groupingBy(item -> item.getGoodsid() + "_" + item.getMachuid()));

            // 遍历新的列表
            for (BuyPlanmergePojo mergeNew : mergedListNew) {
                String keyNew = mergeNew.getGoodsid() + "_" + mergeNew.getMachuid();
                if (mergeMapDB.containsKey(keyNew)) {
                    // 如果键在数据库中的映射中存在，更新对象
                    BuyPlanmergePojo mergePojoDB = mergeMapDB.get(keyNew);
                    mergeNew.setId(mergePojoDB.getId());
                    buyPlanmergeService.update(mergeNew);
                } else {
                    // 如果键在数据库中的映射中不存在，插入新对象
                    BuyPlanmergePojo insert = buyPlanmergeService.insert(mergeNew);
                    mergeNew.setId(insert.getId());  // 插入后的mergeid更新到mergeNew的id，以便后面反写到BuyPlanitemPojo
                }

                // 取出原始 BuyPlanitemPojo 对象并设置 mergeid
                List<BuyPlanitemPojo> itemList = goodsid_ItemList_Map.get(keyNew);
                for (BuyPlanitemPojo item : itemList) {
                    // 更新Buy_PlanItem.Mergeid '合并行id'
                    this.buyPlanitemMapper.updateMergeid(item.getId(), mergeNew.getId(), tid);
                }

                // 从数据库中的映射中移除键
                mergeMapDB.remove(keyNew);
            }
            // 数据库中的映射中剩下的键就是需要删除的对象
            for (BuyPlanmergePojo mergeDelete : mergeMapDB.values()) {
                buyPlanmergeMapper.delete(mergeDelete.getId(), tid);
            }
            //更新合并行数 100条item合并为3条merge,则取3
            this.buyPlanMapper.updateMergeCount(planid, mergedListNew.size(), tid);
        }
    }

    /**
     * 审核数据
     *
     * @param buyPlanPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyPlanPojo approval(BuyPlanPojo buyPlanPojo) {
        //主表更改
        BuyPlanEntity buyPlanEntity = new BuyPlanEntity();
        BeanUtils.copyProperties(buyPlanPojo, buyPlanEntity);
        this.buyPlanMapper.approval(buyPlanEntity);
        //返回Bill实例
        return this.getBillEntity(buyPlanEntity.getId(), buyPlanEntity.getTenantid());
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyPlanPojo disannul(List<BuyPlanitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            BuyPlanitemPojo Pojo = lst.get(i);
            BuyPlanitemPojo dbPojo = this.buyPlanitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getFinishqty() > 0) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已被引用,禁止作废操作");
                    }
                    BuyPlanitemEntity entity = new BuyPlanitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    entity.setDisannuldate(new Date());
                    entity.setDisannullister(loginUser.getRealname());
                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(loginUser.getTenantid());
                    this.buyPlanitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.buyPlanMapper.updateDisannulCount(Pid, tid);
            //主表更改
            BuyPlanEntity buyPlanEntity = new BuyPlanEntity();
            buyPlanEntity.setId(Pid);
            buyPlanEntity.setLister(loginUser.getRealname());
            buyPlanEntity.setListerid(loginUser.getUserid());
            buyPlanEntity.setModifydate(new Date());
            buyPlanEntity.setTenantid(loginUser.getTenantid());
            this.buyPlanMapper.update(buyPlanEntity);
            //返回Bill实例
            return this.getBillEntity(buyPlanEntity.getId(), buyPlanEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyPlanPojo closed(List<BuyPlanitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            BuyPlanitemPojo Pojo = lst.get(i);
            BuyPlanitemPojo dbPojo = this.buyPlanitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    BuyPlanitemEntity entity = new BuyPlanitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.buyPlanitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.buyPlanMapper.updateBuyFinishCount(Pid, tid);
            //主表更改
            BuyPlanEntity buyPlanEntity = new BuyPlanEntity();
            buyPlanEntity.setId(Pid);
            buyPlanEntity.setLister(loginUser.getRealname());
            buyPlanEntity.setListerid(loginUser.getUserid());
            buyPlanEntity.setModifydate(new Date());
            buyPlanEntity.setTenantid(loginUser.getTenantid());
            this.buyPlanMapper.update(buyPlanEntity);
            //返回Bill实例
            return this.getBillEntity(buyPlanEntity.getId(), buyPlanEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    // 查询Item是否被引用
    public List<String> getItemCiteBillName(String key, String pid, String tid) {
        return this.buyPlanMapper.getItemCiteBillName(key, pid, tid);
    }

    @Override
    public void updatePrintcount(BuyPlanPojo billPrintPojo) {
        this.buyPlanMapper.updatePrintcount(billPrintPojo);
    }


    @Override
    public int syncPriceFromOrderItem(String planitemid, String goodsid, String mergeid, String tid) {
        //select TaxPrice, Price, ItemTaxrate from Buy_OrderItem(最新)
        Map<String, Object> latestOrderItem = buyPlanitemMapper.getLatestOrderItemPrice(goodsid, null, tid);
        // 采购计划中存在的商品，可能在采购订单中不存在，所以如若采购订单没有该货品，就去查货品表价格InTaxPrice、InPrice、Taxrate
        if (latestOrderItem == null) {
            latestOrderItem = buyPlanitemMapper.getGoodsPrice(goodsid, tid);
        }
        if (isNotBlank(mergeid)) {// 有合并行id额外进行采购计划合并表的更新
            this.buyPlanitemMapper.syncMergePriceFromOrderItem(mergeid, latestOrderItem, tid);
        }
        return this.buyPlanitemMapper.syncPriceFromOrderItem(planitemid, latestOrderItem, tid);
    }

    //采购单价来源
//        module.buy.pricesource
//                goods 货品*
//                quot 报价
//                lastgd 货品最后一次
//                lastwg  本供应商最后一次
// Java
    @Override
    public Map<String, Object> getPriceByGoodsidAndSource(String goodsid,String groupid,String pricesource,String tid) {
        Map<String, Object> price;
        // 3. 来源分支
        switch (pricesource) {
            case "lastgd":
                price = buyPlanitemMapper.getLatestOrderItemPrice(goodsid, null, tid);
                break;
            case "lastwg":
                if (StringUtils.isBlank(groupid)) {
                    throw new RuntimeException("采购单价来源 lastwg，但 groupid 为空");
                }
                price = buyPlanitemMapper.getLatestOrderItemPrice(goodsid, groupid, tid);
                break;
            case "goods":
            case "quot":
                return buyPlanitemMapper.getGoodsPrice(goodsid, tid);
            default:
                throw new RuntimeException("未知的采购单价来源: " + pricesource);
        }

        // 4. 兜底：Goods表价格
        return price != null ? price : buyPlanitemMapper.getGoodsPrice(goodsid, tid);
    }


    @Override
    public String syncPriceFromOrderStart(String key, String tid) {
        List<BuyPlanitemPojo> planitemList = buyPlanitemMapper.getList(key, tid);
        int size = planitemList.size();
        AtomicInteger counter = new AtomicInteger(0); // 用于计数已处理的任务数量
        AtomicBoolean completed = new AtomicBoolean(false); // 新增：原子标志位
        //------------1设置当前计算任务进度
        String state_redisKey = MyConstant.ASYNC_PLAN_PRICE_FROMORDER_STATE + inksSnowflake.getSnowflake().nextIdStr();
        Map<String, Object> missionMsg = new HashMap<>();
        missionMsg.put("total", size);
        missionMsg.put("finish", 0);
        this.redisService.setCacheObject(state_redisKey, missionMsg, 10L, TimeUnit.MINUTES);

        for (BuyPlanitemPojo planitemPojo : planitemList) {
            // 提交任务到线程池
            threadPoolExecutor.execute(() -> {
                // 调用上面的同步价格方法
                syncPriceFromOrderItem(planitemPojo.getId(), planitemPojo.getGoodsid(), planitemPojo.getMergeid(), tid);
                int currentCount = counter.incrementAndGet(); // 增加计数器
                PrintColor.red("已处理任务数量：" + currentCount + " / " + size); // 打印进度

                // 更新Redis中的进度信息 避免频繁更新，每50次
                if (currentCount % 50 == 0 || currentCount == size) {
                    // 任务全部完成时同步主表金额，只执行一次
                    if (currentCount == size && completed.compareAndSet(false, true)) {
                        buyPlanMapper.syncBillAmount(key, tid);
                        missionMsg.put("finish", currentCount);
                    } else {
                        missionMsg.put("finish", currentCount - 1);
                    }
                    this.redisService.setCacheObject(state_redisKey, missionMsg, 10L, TimeUnit.MINUTES);
                }
            });
        }
        return state_redisKey;
    }

    @Override
    @Transactional
    public void mergeItem(List<String> buyPlanItemIds, LoginUser loginUser) throws SQLException {
        String tid = loginUser.getTenantid();
        List<BuyPlanitemdetailPojo> planItems = buyPlanMapper.getMainPlanItem(buyPlanItemIds, tid);
        // ----1.goodsid相同合并数量，生成新生产计划单
        // 检查 goodsid 是否相同
        String goodsId = planItems.get(0).getGoodsid();
        boolean allGoodsIdSame = planItems.stream()
                .allMatch(item -> item.getGoodsid().equals(goodsId));
        if (!allGoodsIdSame) {
            throw new IllegalArgumentException("请选择相同的物料");
        }
        // 因为所有的 goodsid 都相同，可以直接合并数量
        double totalQuantity = planItems.stream()
                .mapToDouble(BuyPlanitemdetailPojo::getQuantity)
                .sum();
        // 计算总需求数量
        double totalPlanQty = planItems.stream()
                .mapToDouble(item -> item.getPlanqty() != null ? item.getPlanqty() : 0.0)
                .sum();

        // 生成新的生产计划单
        BuyPlanPojo newPlan = new BuyPlanPojo();
        newPlan.setMergemark(1);//合并后的单据标识 // 1合单“备用”
        newPlan.setBilltype(planItems.get(0).getBilltype());
        newPlan.setGroupid(planItems.get(0).getGroupid());
        newPlan.setGroupname(planItems.get(0).getGroupname());
        newPlan.setGroupuid(planItems.get(0).getGroupuid());
        newPlan.setAbbreviate(planItems.get(0).getAbbreviate());

        // 收集所有 planItems 的 refno 并拼接成一个字符串，设置为单据标题
        String refnos = planItems.stream()
                .map(BuyPlanitemdetailPojo::getRefno)
                .collect(Collectors.joining(", "));
        newPlan.setBilltitle("合并来自【" + refnos + "】");

        // 生成单据编码RefNoUtils
        String moduleCode = "D03M01B1";
        //String refno = RefNoUtils.generateRefNo(moduleCode, "Buy_Plan", null, loginUser.getTenantid());
        String refno = saBillcodeService.getSerialNo(moduleCode, loginUser.getTenantid(), "Buy_Plan");
        newPlan.setRefno(refno);
        newPlan.setCreateby(loginUser.getRealname());
        newPlan.setCreatebyid(loginUser.getUserid());
        newPlan.setCreatedate(new Date());
        newPlan.setLister(loginUser.getRealname());
        newPlan.setListerid(loginUser.getUserid());
        newPlan.setModifydate(new Date());
        newPlan.setTenantid(tid);

        // 创建新的计划单子表
        BuyPlanitemPojo newPlanItem = new BuyPlanitemPojo();
        BeanUtils.copyProperties(planItems.get(0), newPlanItem);
        newPlanItem.setQuantity(totalQuantity);
        newPlanItem.setPlanqty(totalPlanQty);
        newPlanItem.setMergemark(1);//主计划子表 合并后的单据标识 // 0默认/1合单/2被合主/3被合
        newPlanItem.setMergeitems(String.join(",", buyPlanItemIds));
        newPlan.setItem(Collections.singletonList(newPlanItem));
        // 插入计划单主子表
        BuyPlanPojo insertPlan = insert(newPlan);
        //RefNoUtils.saveRedisRefNo(refno, moduleCode, loginUser.getTenantid());// 保存单据编码RefNoUtils

        // ----2.通过 machitemid 更新 Bus_MachingItem 表的 WkMergeMark、WkMergeItemid
        String masterMachItemid = planItems.get(0).getMachitemid();//主单machuid
        List<String> salveMachItemids = planItems.stream()
                .map(BuyPlanitemdetailPojo::getMachitemid)
                .filter(machuid -> !machuid.equals(masterMachItemid))  // 过滤掉主单的machuid
                .collect(Collectors.toList());

        String insertPlanItemid = insertPlan.getItem().get(0).getId();

        buyPlanMapper.syncMachingItemWkMergeInIds(Collections.singletonList(masterMachItemid), 2, insertPlanItemid, tid);
        buyPlanMapper.syncMachingItemWkMergeInIds(salveMachItemids, 3, insertPlanItemid, tid);


        // ----3.更新被合并的Wk_MainPlanItem.MergeMark=2/3 (被合主单2，副单3)
        String masterPlanItemid = planItems.get(0).getId();//主单
        List<String> salvePlanItemids = buyPlanItemIds.stream()
                .filter(id -> !id.equals(masterPlanItemid))  // 过滤掉主单的id
                .collect(Collectors.toList());
        buyPlanitemMapper.syncMergeMarkInIds(Collections.singletonList(masterPlanItemid), 2, tid);
        buyPlanitemMapper.syncMergeMarkInIds(salvePlanItemids, 3, tid);


    }

    @Override
    public void updateOaflowmark(BuyPlanPojo billPojo) {
        this.buyPlanMapper.updateOaflowmark(billPojo);
    }
}
