package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatCargospaceEntity;
import inks.service.sa.som.domain.pojo.MatCargospacePojo;
import inks.service.sa.som.mapper.MatCargospaceMapper;
import inks.service.sa.som.service.MatCargospaceService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 仓库货位(MatCargospace)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-13 20:43:52
 */
@Service("matCargospaceService")
public class MatCargospaceServiceImpl implements MatCargospaceService {
    @Resource
    private MatCargospaceMapper matCargospaceMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatCargospacePojo getEntity(String key, String tid) {
        return this.matCargospaceMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatCargospacePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatCargospacePojo> lst = matCargospaceMapper.getPageList(queryParam);
            PageInfo<MatCargospacePojo> pageInfo = new PageInfo<MatCargospacePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param matCargospacePojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatCargospacePojo insert(MatCargospacePojo matCargospacePojo) {
    //初始化NULL字段
     if(matCargospacePojo.getSpacecode()==null) matCargospacePojo.setSpacecode("");
     if(matCargospacePojo.getSpacename()==null) matCargospacePojo.setSpacename("");
     if(matCargospacePojo.getStoreid()==null) matCargospacePojo.setStoreid("");
     if(matCargospacePojo.getStorecode()==null) matCargospacePojo.setStorecode("");
     if(matCargospacePojo.getStorename()==null) matCargospacePojo.setStorename("");
     if(matCargospacePojo.getOperator()==null) matCargospacePojo.setOperator("");
     if(matCargospacePojo.getRemark()==null) matCargospacePojo.setRemark("");
     if(matCargospacePojo.getRownum()==null) matCargospacePojo.setRownum(0);
     if(matCargospacePojo.getAllowedit()==null) matCargospacePojo.setAllowedit(0);
     if(matCargospacePojo.getAllowdelete()==null) matCargospacePojo.setAllowdelete(0);
     if(matCargospacePojo.getEnabledmark()==null) matCargospacePojo.setEnabledmark(0);
     if(matCargospacePojo.getCreateby()==null) matCargospacePojo.setCreateby("");
     if(matCargospacePojo.getCreatebyid()==null) matCargospacePojo.setCreatebyid("");
     if(matCargospacePojo.getCreatedate()==null) matCargospacePojo.setCreatedate(new Date());
     if(matCargospacePojo.getLister()==null) matCargospacePojo.setLister("");
     if(matCargospacePojo.getListerid()==null) matCargospacePojo.setListerid("");
     if(matCargospacePojo.getModifydate()==null) matCargospacePojo.setModifydate(new Date());
     if(matCargospacePojo.getDeletemark()==null) matCargospacePojo.setDeletemark(0);
     if(matCargospacePojo.getDeletelister()==null) matCargospacePojo.setDeletelister("");
     if(matCargospacePojo.getDeletelisterid()==null) matCargospacePojo.setDeletelisterid("");
     if(matCargospacePojo.getDeletedate()==null) matCargospacePojo.setDeletedate(new Date());
     if(matCargospacePojo.getCustom1()==null) matCargospacePojo.setCustom1("");
     if(matCargospacePojo.getCustom2()==null) matCargospacePojo.setCustom2("");
     if(matCargospacePojo.getCustom3()==null) matCargospacePojo.setCustom3("");
     if(matCargospacePojo.getCustom4()==null) matCargospacePojo.setCustom4("");
     if(matCargospacePojo.getCustom5()==null) matCargospacePojo.setCustom5("");
     if(matCargospacePojo.getCustom6()==null) matCargospacePojo.setCustom6("");
     if(matCargospacePojo.getCustom7()==null) matCargospacePojo.setCustom7("");
     if(matCargospacePojo.getCustom8()==null) matCargospacePojo.setCustom8("");
     if(matCargospacePojo.getCustom9()==null) matCargospacePojo.setCustom9("");
     if(matCargospacePojo.getCustom10()==null) matCargospacePojo.setCustom10("");
     if(matCargospacePojo.getTenantid()==null) matCargospacePojo.setTenantid("");
     if(matCargospacePojo.getRevision()==null) matCargospacePojo.setRevision(0);
        MatCargospaceEntity matCargospaceEntity = new MatCargospaceEntity(); 
        BeanUtils.copyProperties(matCargospacePojo,matCargospaceEntity);
        
          matCargospaceEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          matCargospaceEntity.setRevision(1);  //乐观锁
          this.matCargospaceMapper.insert(matCargospaceEntity);
        return this.getEntity(matCargospaceEntity.getId(),matCargospaceEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param matCargospacePojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatCargospacePojo update(MatCargospacePojo matCargospacePojo) {
        MatCargospaceEntity matCargospaceEntity = new MatCargospaceEntity(); 
        BeanUtils.copyProperties(matCargospacePojo,matCargospaceEntity);
        this.matCargospaceMapper.update(matCargospaceEntity);
        return this.getEntity(matCargospaceEntity.getId(),matCargospaceEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matCargospaceMapper.delete(key,tid) ;
    }

    @Override
    public boolean checkCodeOrName(String spacecode, String spacename, String tenantid) {
        return this.matCargospaceMapper.checkCodeOrName(spacecode,spacename,tenantid) > 0;
    }

}
