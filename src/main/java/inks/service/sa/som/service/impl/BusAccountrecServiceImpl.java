package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BusAccountrecEntity;
import inks.service.sa.som.domain.pojo.BusAccountrecPojo;
import inks.service.sa.som.mapper.BusAccountMapper;
import inks.service.sa.som.mapper.BusAccountrecMapper;
import inks.service.sa.som.service.BusAccountrecService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 结转记录(BusAccountrec)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-25 08:28:53
 */
@Service("busAccountrecService")
public class BusAccountrecServiceImpl implements BusAccountrecService {
    @Resource
    private BusAccountrecMapper busAccountrecMapper;

    @Resource
    private BusAccountMapper busAccountMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusAccountrecPojo getEntity(String key, String tid) {
        return this.busAccountrecMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusAccountrecPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountrecPojo> lst = busAccountrecMapper.getPageList(queryParam);
            PageInfo<BusAccountrecPojo> pageInfo = new PageInfo<BusAccountrecPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busAccountrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusAccountrecPojo insert(BusAccountrecPojo busAccountrecPojo) {
        //初始化NULL字段
        if (busAccountrecPojo.getCarryyear() == null) busAccountrecPojo.setCarryyear(0);
        if (busAccountrecPojo.getCarrymonth() == null) busAccountrecPojo.setCarrymonth(0);
        if (busAccountrecPojo.getStartdate() == null) busAccountrecPojo.setStartdate(new Date());
        if (busAccountrecPojo.getEnddate() == null) busAccountrecPojo.setEnddate(new Date());
        if (busAccountrecPojo.getOperator() == null) busAccountrecPojo.setOperator("");
        if (busAccountrecPojo.getOperatorid() == null) busAccountrecPojo.setOperatorid("");
        if (busAccountrecPojo.getRownum() == null) busAccountrecPojo.setRownum(0);
        if (busAccountrecPojo.getRemark() == null) busAccountrecPojo.setRemark("");
        if (busAccountrecPojo.getCreateby() == null) busAccountrecPojo.setCreateby("");
        if (busAccountrecPojo.getCreatebyid() == null) busAccountrecPojo.setCreatebyid("");
        if (busAccountrecPojo.getCreatedate() == null) busAccountrecPojo.setCreatedate(new Date());
        if (busAccountrecPojo.getLister() == null) busAccountrecPojo.setLister("");
        if (busAccountrecPojo.getListerid() == null) busAccountrecPojo.setListerid("");
        if (busAccountrecPojo.getModifydate() == null) busAccountrecPojo.setModifydate(new Date());
        if (busAccountrecPojo.getBillopenamount() == null) busAccountrecPojo.setBillopenamount(0D);
        if (busAccountrecPojo.getBillinamount() == null) busAccountrecPojo.setBillinamount(0D);
        if (busAccountrecPojo.getBilloutamount() == null) busAccountrecPojo.setBilloutamount(0D);
        if (busAccountrecPojo.getBillcloseamount() == null) busAccountrecPojo.setBillcloseamount(0D);
        if (busAccountrecPojo.getInvoopenamount() == null) busAccountrecPojo.setInvoopenamount(0D);
        if (busAccountrecPojo.getInvoinamount() == null) busAccountrecPojo.setInvoinamount(0D);
        if (busAccountrecPojo.getInvooutamount() == null) busAccountrecPojo.setInvooutamount(0D);
        if (busAccountrecPojo.getInvocloseamount() == null) busAccountrecPojo.setInvocloseamount(0D);
        if (busAccountrecPojo.getArapopenamount() == null) busAccountrecPojo.setArapopenamount(0D);
        if (busAccountrecPojo.getArapinamount() == null) busAccountrecPojo.setArapinamount(0D);
        if (busAccountrecPojo.getArapoutamount() == null) busAccountrecPojo.setArapoutamount(0D);
        if (busAccountrecPojo.getArapcloseamount() == null) busAccountrecPojo.setArapcloseamount(0D);
        if (busAccountrecPojo.getPrintcount() == null) busAccountrecPojo.setPrintcount(0);
        if (busAccountrecPojo.getCustom1() == null) busAccountrecPojo.setCustom1("");
        if (busAccountrecPojo.getCustom2() == null) busAccountrecPojo.setCustom2("");
        if (busAccountrecPojo.getCustom3() == null) busAccountrecPojo.setCustom3("");
        if (busAccountrecPojo.getCustom4() == null) busAccountrecPojo.setCustom4("");
        if (busAccountrecPojo.getCustom5() == null) busAccountrecPojo.setCustom5("");
        if (busAccountrecPojo.getCustom6() == null) busAccountrecPojo.setCustom6("");
        if (busAccountrecPojo.getCustom7() == null) busAccountrecPojo.setCustom7("");
        if (busAccountrecPojo.getCustom8() == null) busAccountrecPojo.setCustom8("");
        if (busAccountrecPojo.getCustom9() == null) busAccountrecPojo.setCustom9("");
        if (busAccountrecPojo.getCustom10() == null) busAccountrecPojo.setCustom10("");
        if (busAccountrecPojo.getTenantid() == null) busAccountrecPojo.setTenantid("");
        if (busAccountrecPojo.getTenantname() == null) busAccountrecPojo.setTenantname("");
        if (busAccountrecPojo.getRevision() == null) busAccountrecPojo.setRevision(0);
        BusAccountrecEntity busAccountrecEntity = new BusAccountrecEntity();
        BeanUtils.copyProperties(busAccountrecPojo, busAccountrecEntity);

        busAccountrecEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        busAccountrecEntity.setRevision(1);  //乐观锁
        this.busAccountrecMapper.insert(busAccountrecEntity);
        return this.getEntity(busAccountrecEntity.getId(), busAccountrecEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busAccountrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusAccountrecPojo update(BusAccountrecPojo busAccountrecPojo) {
        BusAccountrecEntity busAccountrecEntity = new BusAccountrecEntity();
        BeanUtils.copyProperties(busAccountrecPojo, busAccountrecEntity);
        this.busAccountrecMapper.update(busAccountrecEntity);
        return this.getEntity(busAccountrecEntity.getId(), busAccountrecEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BusAccountrecPojo delPojo = this.busAccountrecMapper.getEntity(key, tid);
        this.busAccountMapper.deleteByMonth(delPojo.getCarryyear(), delPojo.getCarrymonth(), tid);
        return this.busAccountrecMapper.delete(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public BusAccountrecPojo getEntityByMax(String tid) {
        return this.busAccountrecMapper.getEntityByMax(tid);
    }

}
