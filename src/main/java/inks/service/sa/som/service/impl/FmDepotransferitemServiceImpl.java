package inks.service.sa.som.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.pojo.FmDepotransferitemPojo;
import inks.service.sa.som.domain.FmDepotransferitemEntity;
import inks.service.sa.som.mapper.FmDepotransferitemMapper;
import inks.service.sa.som.service.FmDepotransferitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 预收核转Item(FmDepotransferitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-14 17:21:36
 */
@Service("fmDepotransferitemService")
public class FmDepotransferitemServiceImpl implements FmDepotransferitemService {
    @Resource
    private FmDepotransferitemMapper fmDepotransferitemMapper;

    @Override
    public FmDepotransferitemPojo getEntity(String key) {
        return this.fmDepotransferitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<FmDepotransferitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmDepotransferitemPojo> lst = fmDepotransferitemMapper.getPageList(queryParam);
            PageInfo<FmDepotransferitemPojo> pageInfo = new PageInfo<FmDepotransferitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<FmDepotransferitemPojo> getList(String Pid) { 
        try {
            List<FmDepotransferitemPojo> lst = fmDepotransferitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public FmDepotransferitemPojo insert(FmDepotransferitemPojo fmDepotransferitemPojo) {
        //初始化item的NULL
        FmDepotransferitemPojo itempojo =this.clearNull(fmDepotransferitemPojo);
        FmDepotransferitemEntity fmDepotransferitemEntity = new FmDepotransferitemEntity(); 
        BeanUtils.copyProperties(itempojo,fmDepotransferitemEntity);
         //生成雪花id
          fmDepotransferitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          fmDepotransferitemEntity.setRevision(1);  //乐观锁      
          this.fmDepotransferitemMapper.insert(fmDepotransferitemEntity);
        return this.getEntity(fmDepotransferitemEntity.getId());
  
    }

    @Override
    public FmDepotransferitemPojo update(FmDepotransferitemPojo fmDepotransferitemPojo) {
        FmDepotransferitemEntity fmDepotransferitemEntity = new FmDepotransferitemEntity(); 
        BeanUtils.copyProperties(fmDepotransferitemPojo,fmDepotransferitemEntity);
        this.fmDepotransferitemMapper.update(fmDepotransferitemEntity);
        return this.getEntity(fmDepotransferitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.fmDepotransferitemMapper.delete(key) ;
    }

     @Override
     public FmDepotransferitemPojo clearNull(FmDepotransferitemPojo fmDepotransferitemPojo){
     //初始化NULL字段
     if(fmDepotransferitemPojo.getPid()==null) fmDepotransferitemPojo.setPid("");
     if(fmDepotransferitemPojo.getPaybillid()==null) fmDepotransferitemPojo.setPaybillid("");
     if(fmDepotransferitemPojo.getPayrefno()==null) fmDepotransferitemPojo.setPayrefno("");
     if(fmDepotransferitemPojo.getPaygroupid()==null) fmDepotransferitemPojo.setPaygroupid("");
     if(fmDepotransferitemPojo.getPayamount()==null) fmDepotransferitemPojo.setPayamount(0D);
     if(fmDepotransferitemPojo.getAmount()==null) fmDepotransferitemPojo.setAmount(0D);
     if(fmDepotransferitemPojo.getRownum()==null) fmDepotransferitemPojo.setRownum(0);
     if(fmDepotransferitemPojo.getRemark()==null) fmDepotransferitemPojo.setRemark("");
     if(fmDepotransferitemPojo.getCustom1()==null) fmDepotransferitemPojo.setCustom1("");
     if(fmDepotransferitemPojo.getCustom2()==null) fmDepotransferitemPojo.setCustom2("");
     if(fmDepotransferitemPojo.getCustom3()==null) fmDepotransferitemPojo.setCustom3("");
     if(fmDepotransferitemPojo.getCustom4()==null) fmDepotransferitemPojo.setCustom4("");
     if(fmDepotransferitemPojo.getCustom5()==null) fmDepotransferitemPojo.setCustom5("");
     if(fmDepotransferitemPojo.getCustom6()==null) fmDepotransferitemPojo.setCustom6("");
     if(fmDepotransferitemPojo.getCustom7()==null) fmDepotransferitemPojo.setCustom7("");
     if(fmDepotransferitemPojo.getCustom8()==null) fmDepotransferitemPojo.setCustom8("");
     if(fmDepotransferitemPojo.getCustom9()==null) fmDepotransferitemPojo.setCustom9("");
     if(fmDepotransferitemPojo.getCustom10()==null) fmDepotransferitemPojo.setCustom10("");
     if(fmDepotransferitemPojo.getTenantid()==null) fmDepotransferitemPojo.setTenantid("");
     if(fmDepotransferitemPojo.getRevision()==null) fmDepotransferitemPojo.setRevision(0);
     return fmDepotransferitemPojo;
     }
}
