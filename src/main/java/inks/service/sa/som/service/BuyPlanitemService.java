package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyPlanitemPojo;

import java.util.List;
/**
 * 采购计划项目(BuyPlanitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-06 20:32:57
 */
public interface BuyPlanitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyPlanitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyPlanitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyPlanitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param buyPlanitemPojo 实例对象
     * @return 实例对象
     */
    BuyPlanitemPojo insert(BuyPlanitemPojo buyPlanitemPojo);

    /**
     * 修改数据
     *
     * @param buyPlanitempojo 实例对象
     * @return 实例对象
     */
    BuyPlanitemPojo update(BuyPlanitemPojo buyPlanitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param buyPlanitempojo 实例对象
     * @return 实例对象
     */
    BuyPlanitemPojo clearNull(BuyPlanitemPojo buyPlanitempojo);
}
