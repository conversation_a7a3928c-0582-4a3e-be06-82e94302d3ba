package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BusDeductionitemPojo;

import java.util.List;
/**
 * 销售扣款Item(BusDeductionitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-16 16:59:35
 */
public interface BusDeductionitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDeductionitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDeductionitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusDeductionitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busDeductionitemPojo 实例对象
     * @return 实例对象
     */
    BusDeductionitemPojo insert(BusDeductionitemPojo busDeductionitemPojo);

    /**
     * 修改数据
     *
     * @param busDeductionitempojo 实例对象
     * @return 实例对象
     */
    BusDeductionitemPojo update(BusDeductionitemPojo busDeductionitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busDeductionitempojo 实例对象
     * @return 实例对象
     */
    BusDeductionitemPojo clearNull(BusDeductionitemPojo busDeductionitempojo);
}
