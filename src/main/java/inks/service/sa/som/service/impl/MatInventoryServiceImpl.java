package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatInventoryEntity;
import inks.service.sa.som.domain.pojo.MatGoodsInveQtyPojo;
import inks.service.sa.som.domain.pojo.MatGoodsPojo;
import inks.service.sa.som.domain.pojo.MatInventoryPojo;
import inks.service.sa.som.domain.pojo.MatInventoryQtyPojo;
import inks.service.sa.som.mapper.MatInventoryMapper;
import inks.service.sa.som.service.MatInventoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 库存信息(MatInventory)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-12 14:23:44
 */
@Service("matInventoryService")
public class MatInventoryServiceImpl implements MatInventoryService {
    @Resource
    private MatInventoryMapper matInventoryMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatInventoryPojo getEntity(String key, String tid) {
        return this.matInventoryMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatInventoryPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatInventoryPojo> lst = matInventoryMapper.getPageList(queryParam);
            PageInfo<MatInventoryPojo> pageInfo = new PageInfo<MatInventoryPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matInventoryPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatInventoryPojo insert(MatInventoryPojo matInventoryPojo) {
        //初始化NULL字段
        if (matInventoryPojo.getStoreid() == null) matInventoryPojo.setStoreid("");
        if (matInventoryPojo.getGoodsid() == null) matInventoryPojo.setGoodsid("");
        if (matInventoryPojo.getQuantity() == null) matInventoryPojo.setQuantity(0D);
        if (matInventoryPojo.getAmount() == null) matInventoryPojo.setAmount(0D);
        if (matInventoryPojo.getEnduid() == null) matInventoryPojo.setEnduid("");
        if (matInventoryPojo.getEndinuid() == null) matInventoryPojo.setEndinuid("");
        if (matInventoryPojo.getEndindate() == null) matInventoryPojo.setEndindate(new Date());
        if (matInventoryPojo.getEndoutuid() == null) matInventoryPojo.setEndoutuid("");
        if (matInventoryPojo.getEndoutdate() == null) matInventoryPojo.setEndoutdate(new Date());
        if (matInventoryPojo.getBatchno() == null) matInventoryPojo.setBatchno("");
        if (matInventoryPojo.getLocation() == null) matInventoryPojo.setLocation("");
        if (matInventoryPojo.getCreateby() == null) matInventoryPojo.setCreateby("");
        if (matInventoryPojo.getCreatedate() == null) matInventoryPojo.setCreatedate(new Date());
        if (matInventoryPojo.getLister() == null) matInventoryPojo.setLister("");
        if (matInventoryPojo.getModifydate() == null) matInventoryPojo.setModifydate(new Date());
        if (matInventoryPojo.getCustom1() == null) matInventoryPojo.setCustom1("");
        if (matInventoryPojo.getCustom2() == null) matInventoryPojo.setCustom2("");
        if (matInventoryPojo.getCustom3() == null) matInventoryPojo.setCustom3("");
        if (matInventoryPojo.getCustom4() == null) matInventoryPojo.setCustom4("");
        if (matInventoryPojo.getCustom5() == null) matInventoryPojo.setCustom5("");
        if (matInventoryPojo.getCustom6() == null) matInventoryPojo.setCustom6("");
        if (matInventoryPojo.getCustom7() == null) matInventoryPojo.setCustom7("");
        if (matInventoryPojo.getCustom8() == null) matInventoryPojo.setCustom8("");
        if (matInventoryPojo.getCustom9() == null) matInventoryPojo.setCustom9("");
        if (matInventoryPojo.getCustom10() == null) matInventoryPojo.setCustom10("");
        if (matInventoryPojo.getTenantid() == null) matInventoryPojo.setTenantid("");
        if (matInventoryPojo.getRevision() == null) matInventoryPojo.setRevision(0);
        if (matInventoryPojo.getPacksn() == null) matInventoryPojo.setPacksn("");
        if (matInventoryPojo.getSkuid() == null) matInventoryPojo.setSkuid("");
        if (matInventoryPojo.getExpidate() == null) matInventoryPojo.setExpidate(new Date());
        if (matInventoryPojo.getCreatebyid() == null) matInventoryPojo.setCreatebyid("");
        if (matInventoryPojo.getListerid() == null) matInventoryPojo.setListerid("");
        MatInventoryEntity matInventoryEntity = new MatInventoryEntity();
        BeanUtils.copyProperties(matInventoryPojo, matInventoryEntity);

        matInventoryEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matInventoryEntity.setRevision(1);  //乐观锁
        this.matInventoryMapper.insert(matInventoryEntity);
        return this.getEntity(matInventoryEntity.getId(), matInventoryEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matInventoryPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatInventoryPojo update(MatInventoryPojo matInventoryPojo) {
        MatInventoryEntity matInventoryEntity = new MatInventoryEntity();
        BeanUtils.copyProperties(matInventoryPojo, matInventoryEntity);
        this.matInventoryMapper.update(matInventoryEntity);
        return this.getEntity(matInventoryEntity.getId(), matInventoryEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matInventoryMapper.delete(key, tid);
    }

    /**
     * 刷新货品总仓库数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    @Override
    public int updateGoodsIvQty(String key, String tid) {
        return this.matInventoryMapper.updateGoodsIvQty(key, tid);
    }

    @Override
    public MatInventoryPojo getEntityBySn(MatInventoryPojo matInventorypojo) {
        return this.matInventoryMapper.getEntityBySn(matInventorypojo);
    }

    @Override
    public MatInventoryPojo getEntityBySnNoSku(MatInventoryPojo matInventoryPojo) {
        return this.matInventoryMapper.getEntityBySnNoSku(matInventoryPojo);
    }

    @Override
    public MatInventoryPojo getEntityBySku(MatInventoryPojo matInventorypojo) {
        return this.matInventoryMapper.getEntityBySku(matInventorypojo);
    }

    @Override
    public MatInventoryPojo getEntityByBatch(MatInventoryPojo matInventorypojo) {
        return this.matInventoryMapper.getEntityByBatch(matInventorypojo);
    }


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatInventoryQtyPojo> getQtyPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatInventoryQtyPojo> lst = matInventoryMapper.getQtyPageList(queryParam);
            PageInfo<MatInventoryQtyPojo> pageInfo = new PageInfo<MatInventoryQtyPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatGoodsInveQtyPojo> getQtyPageListByGoods(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatGoodsInveQtyPojo> lst = matInventoryMapper.getQtyPageListByGoods(queryParam);
            PageInfo<MatGoodsInveQtyPojo> pageInfo = new PageInfo<MatGoodsInveQtyPojo>(lst);
            //循环设置每个主表对象的item子表
//            for (int i = 0; i < lst.size(); i++) {
//              //  lst.get(i).setItem(this.matInventoryMapper.getListByGoods(lst.get(i).getId(), queryParam.getTenantid()));
//                lst.get(i).setStoqty(lst.get(i).getQuantity() - lst.get(i).getBusremqty() - lst.get(i).getRequremqty() +
//                        lst.get(i).getBuyremqty() + lst.get(i).getWkscremqty() + lst.get(i).getWkwsremqty());
//            }
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatInventoryPojo> getListByGoods(String key, String tid) {
        try {
            List<MatInventoryPojo> lst = matInventoryMapper.getListByGoods(key, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatGoodsInveQtyPojo> getMachMatQtyPageListByGoods(QueryParam queryParam, String itemid) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatGoodsInveQtyPojo> lst = matInventoryMapper.getMachMatQtyPageListByGoods(queryParam,itemid);
            PageInfo<MatGoodsInveQtyPojo> pageInfo = new PageInfo<MatGoodsInveQtyPojo>(lst);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(this.matInventoryMapper.getListByGoods(lst.get(i).getId(), queryParam.getTenantid()));
                lst.get(i).setStoqty(lst.get(i).getQuantity() - lst.get(i).getBusremqty() - lst.get(i).getRequremqty() + lst.get(i).getBuyremqty());
            }
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public MatInventoryPojo getEntityByNameSpecPart(String name, String goodsspec, String partid, String tid) {
        try {
            return matInventoryMapper.getEntityByNameSpecPart(name, goodsspec, partid, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public MatInventoryPojo getEntityByStoreGoods(String key, String storeid, String tid) {
        return matInventoryMapper.getEntityByStoreGoods(key, storeid, tid);
    }

    @Override
    public PageInfo<MatGoodsPojo> getMatShiftPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatGoodsPojo> lst = matInventoryMapper.getGoodsPageList(queryParam);
            // 通过物料Code获取订单占用数量
            for (MatGoodsPojo matGoodsPojo : lst) {
                matGoodsPojo.setRequremqty(matInventoryMapper.getRequremqtyByGoodsUid(matGoodsPojo.getGoodsuid(),matGoodsPojo.getTenantid()));
            }
            PageInfo<MatGoodsPojo> pageInfo = new PageInfo<MatGoodsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<MatGoodsPojo> getMatShiftPageListB(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatGoodsPojo> lst = matInventoryMapper.getGoodsPageList(queryParam);
            //    通过物料Goodsid获取订单占用数量
            for (MatGoodsPojo matGoodsPojo : lst) {
                matGoodsPojo.setRequremqty(matInventoryMapper.getRequremqtyByGoodsid(matGoodsPojo.getId(),matGoodsPojo.getTenantid()));
            }
            PageInfo<MatGoodsPojo> pageInfo = new PageInfo<MatGoodsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<Map<String,Object>> getSumPageListByGoods(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<Map<String,Object>> lst = matInventoryMapper.getSumPageListByGoods(queryParam);
            PageInfo<Map<String,Object>> pageInfo = new PageInfo<Map<String,Object>>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public MatInventoryPojo getEntityByIf(MatInventoryPojo matInventoryPojo) {
        return matInventoryMapper.getEntityByIf(matInventoryPojo);
    }
}
