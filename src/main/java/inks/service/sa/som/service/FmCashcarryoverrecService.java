package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmCashcarryoverrecPojo;

/**
 * 出纳结账(FmCashcarryoverrec)表服务接口
 *
 * <AUTHOR>
 * @since 2022-09-26 19:41:47
 */
public interface FmCashcarryoverrecService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCashcarryoverrecPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmCashcarryoverrecPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param fmCashcarryoverrecPojo 实例对象
     * @return 实例对象
     */
    FmCashcarryoverrecPojo insert(FmCashcarryoverrecPojo fmCashcarryoverrecPojo);

    /**
     * 修改数据
     *
     * @param fmCashcarryoverrecpojo 实例对象
     * @return 实例对象
     */
    FmCashcarryoverrecPojo update(FmCashcarryoverrecPojo fmCashcarryoverrecpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    FmCashcarryoverrecPojo getEntityByMax(String tid);

}
