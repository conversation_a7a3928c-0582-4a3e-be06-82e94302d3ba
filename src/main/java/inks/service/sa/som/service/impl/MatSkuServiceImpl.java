package inks.service.sa.som.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatSkuEntity;
import inks.service.sa.som.domain.pojo.MatAttributePojo;
import inks.service.sa.som.domain.pojo.MatSkuPojo;
import inks.service.sa.som.mapper.MatSkuMapper;
import inks.service.sa.som.service.MatSkuService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 货品SKU(MatSku)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-12 13:25:57
 */
@Service("matSkuService")
public class MatSkuServiceImpl implements MatSkuService {
    @Resource
    private MatSkuMapper matSkuMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSkuPojo getEntity(String key, String tid) {
        return this.matSkuMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSkuPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSkuPojo> lst = matSkuMapper.getPageList(queryParam);
            PageInfo<MatSkuPojo> pageInfo = new PageInfo<MatSkuPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matSkuPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSkuPojo insert(MatSkuPojo matSkuPojo) {
        //初始化NULL字段
        MatSkuPojo maxPojo = this.matSkuMapper.getEntityByGoodsMax(matSkuPojo.getGoodsid(), matSkuPojo.getTenantid());
        if (maxPojo == null) {
            matSkuPojo.setSkunum(1);
        } else {
            matSkuPojo.setSkunum(maxPojo.getSkunum() + 1);
        }
        matSkuPojo.setSkucode(matSkuPojo.getGoodsuid() + "_" + matSkuPojo.getSkunum());
        if (matSkuPojo.getGoodsid() == null) matSkuPojo.setGoodsid("");
        if (matSkuPojo.getItemcode() == null) matSkuPojo.setItemcode("");
        if (matSkuPojo.getItemname() == null) matSkuPojo.setItemname("");
        if (matSkuPojo.getAttributejson() == null) matSkuPojo.setAttributejson("");
        if (matSkuPojo.getBarcode() == null) matSkuPojo.setBarcode("");
        if (matSkuPojo.getSafestock() == null) matSkuPojo.setSafestock(0D);
        if (matSkuPojo.getInprice() == null) matSkuPojo.setInprice(0D);
        if (matSkuPojo.getOutprice() == null) matSkuPojo.setOutprice(0D);
        if (matSkuPojo.getIvquantity() == null) matSkuPojo.setIvquantity(0D);
        if (matSkuPojo.getIvamount() == null) matSkuPojo.setIvamount(0D);
        if (matSkuPojo.getAgeprice() == null) matSkuPojo.setAgeprice(0D);
        if (matSkuPojo.getSkuphoto() == null) matSkuPojo.setSkuphoto("");
        if (matSkuPojo.getRemark() == null) matSkuPojo.setRemark("");
        if (matSkuPojo.getRownum() == null) matSkuPojo.setRownum(0);
        if (matSkuPojo.getCreateby() == null) matSkuPojo.setCreateby("");
        if (matSkuPojo.getCreatebyid() == null) matSkuPojo.setCreatebyid("");
        if (matSkuPojo.getCreatedate() == null) matSkuPojo.setCreatedate(new Date());
        if (matSkuPojo.getLister() == null) matSkuPojo.setLister("");
        if (matSkuPojo.getListerid() == null) matSkuPojo.setListerid("");
        if (matSkuPojo.getModifydate() == null) matSkuPojo.setModifydate(new Date());
        if (matSkuPojo.getCustom1() == null) matSkuPojo.setCustom1("");
        if (matSkuPojo.getCustom2() == null) matSkuPojo.setCustom2("");
        if (matSkuPojo.getCustom3() == null) matSkuPojo.setCustom3("");
        if (matSkuPojo.getCustom4() == null) matSkuPojo.setCustom4("");
        if (matSkuPojo.getCustom5() == null) matSkuPojo.setCustom5("");
        if (matSkuPojo.getCustom6() == null) matSkuPojo.setCustom6("");
        if (matSkuPojo.getCustom7() == null) matSkuPojo.setCustom7("");
        if (matSkuPojo.getCustom8() == null) matSkuPojo.setCustom8("");
        if (matSkuPojo.getCustom9() == null) matSkuPojo.setCustom9("");
        if (matSkuPojo.getCustom10() == null) matSkuPojo.setCustom10("");
        if (matSkuPojo.getTenantid() == null) matSkuPojo.setTenantid("");
        if (matSkuPojo.getTenantname() == null) matSkuPojo.setTenantname("");
        if (matSkuPojo.getRevision() == null) matSkuPojo.setRevision(0);
        MatSkuEntity matSkuEntity = new MatSkuEntity();
        BeanUtils.copyProperties(matSkuPojo, matSkuEntity);

        matSkuEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matSkuEntity.setRevision(1);  //乐观锁
        this.matSkuMapper.insert(matSkuEntity);
        return this.getEntity(matSkuEntity.getId(), matSkuEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matSkuPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSkuPojo update(MatSkuPojo matSkuPojo) {
        MatSkuEntity matSkuEntity = new MatSkuEntity();
        BeanUtils.copyProperties(matSkuPojo, matSkuEntity);
        this.matSkuMapper.update(matSkuEntity);
        return this.getEntity(matSkuEntity.getId(), matSkuEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matSkuMapper.delete(key, tid);
    }


    /**
     * 通过ID查询单条数据
     *
     * @parag goodsid 主键
     * @return 实例对象
     */
    @Override
    public   String getAttrSku(String json,String tid) {
        List<Map<String, Object>> listObjectSec = JSONArray.parseObject(json, List.class);
        //将listObjectSec转换为k::v
        Map<String, Object> collect = listObjectSec.stream()
                .collect(Collectors.toMap(map -> String.valueOf(map.get("key")), map -> map.get("value")));
        List<MatAttributePojo> attrList = this.matSkuMapper.getAttrList(tid);

        attrList.forEach(attr -> {
            if(attr.getSkumark()==0 && collect.containsKey(attr.getAttrkey())){
                collect.remove(attr.getAttrkey());
            }
        });

        listObjectSec = collect.entrySet().stream().map(item->{
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("key", item.getKey());
            map.put("value", item.getValue());
            return map;
        }).collect(Collectors.toList());

        return JSONArray.toJSONString(listObjectSec);
    }
    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     * @parag goodsid 主键
     */
    @Override
    public MatSkuPojo getEntityByAtte(String goodsid, String json, String tid) {
        try {
            List<Map<String, Object>> listObjectSec = JSONArray.parseObject(json, List.class);

            List<MatSkuPojo> lst = this.matSkuMapper.getListByGoodsAttr(goodsid, listObjectSec, tid);
            if (lst.size() > 0) {
                MatSkuPojo matSkuPojo = null;
                if (lst.get(0).getAttributejson().length() == json.length()) matSkuPojo = lst.get(0);
                if (lst.size() > 1) {
                    for (MatSkuPojo skuPojo : lst) {
                        if (skuPojo.getAttributejson() != null && skuPojo.getAttributejson().length() == json.length()) {
                            matSkuPojo = skuPojo;
                            break;
                        }
                    }
                }
                return matSkuPojo;
            } else {
                return null;
            }


        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


}
