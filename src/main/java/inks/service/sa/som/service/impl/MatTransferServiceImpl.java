package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatTransferEntity;
import inks.service.sa.som.domain.MatTransferitemEntity;
import inks.service.sa.som.domain.pojo.MatInventoryPojo;
import inks.service.sa.som.domain.pojo.MatTransferPojo;
import inks.service.sa.som.domain.pojo.MatTransferitemPojo;
import inks.service.sa.som.domain.pojo.MatTransferitemdetailPojo;
import inks.service.sa.som.mapper.MatTransferMapper;
import inks.service.sa.som.mapper.MatTransferitemMapper;
import inks.service.sa.som.service.MatInventoryService;
import inks.service.sa.som.service.MatTransferService;
import inks.service.sa.som.service.MatTransferitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 调拨单据(MatTransfer)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-13 20:50:27
 */
@Service("matTransferService")
public class MatTransferServiceImpl implements MatTransferService {
    @Resource
    private MatTransferMapper matTransferMapper;

    @Resource
    private MatTransferitemMapper matTransferitemMapper;


    @Resource
    private MatTransferitemService matTransferitemService;


    @Resource
    private MatInventoryService matInventoryService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatTransferPojo getEntity(String key, String tid) {
        return this.matTransferMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatTransferitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatTransferitemdetailPojo> lst = matTransferMapper.getPageList(queryParam);
            PageInfo<MatTransferitemdetailPojo> pageInfo = new PageInfo<MatTransferitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatTransferPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            MatTransferPojo matTransferPojo = this.matTransferMapper.getEntity(key, tid);
            //读取子表
            matTransferPojo.setItem(matTransferitemMapper.getList(matTransferPojo.getId(), matTransferPojo.getTenantid()));
            return matTransferPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatTransferPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatTransferPojo> lst = matTransferMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(matTransferitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<MatTransferPojo> pageInfo = new PageInfo<MatTransferPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatTransferPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatTransferPojo> lst = matTransferMapper.getPageTh(queryParam);
            PageInfo<MatTransferPojo> pageInfo = new PageInfo<MatTransferPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matTransferPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatTransferPojo insert(MatTransferPojo matTransferPojo) {
        //初始化NULL字段
        cleanNull(matTransferPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        MatTransferEntity matTransferEntity = new MatTransferEntity();
        BeanUtils.copyProperties(matTransferPojo, matTransferEntity);
        //设置id和新建日期
        matTransferEntity.setId(id);
        matTransferEntity.setRevision(1);  //乐观锁
        //插入主表
        this.matTransferMapper.insert(matTransferEntity);
        //Item子表处理
        List<MatTransferitemPojo> lst = matTransferPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (MatTransferitemPojo matTransferitemPojo : lst) {
                //初始化item的NULL
                MatTransferitemPojo itemPojo = this.matTransferitemService.clearNull(matTransferitemPojo);
                MatTransferitemEntity matTransferitemEntity = new MatTransferitemEntity();
                BeanUtils.copyProperties(itemPojo, matTransferitemEntity);
                //设置id和Pid
                matTransferitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matTransferitemEntity.setPid(id);
                matTransferitemEntity.setTenantid(matTransferPojo.getTenantid());
                matTransferitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.matTransferitemMapper.insert(matTransferitemEntity);

                // 出库
                MatInventoryPojo matInventoryPojo = this.matInventoryService.getEntity(matTransferitemPojo.getInveid(), matTransferitemEntity.getTenantid());
                if (matInventoryPojo.getQuantity() - matTransferitemPojo.getQuantity() < 0) {
                    throw new RuntimeException(matTransferitemPojo.getGoodsuid() + "库存数量不足");
                }
                matInventoryPojo.setQuantity(matInventoryPojo.getQuantity() - matTransferitemPojo.getQuantity());

                //matInventoryPojo.setAmount(matInventoryPojo.getAmount()/ - lst.get(i).getAmount());
                matInventoryPojo.setEndoutdate(new Date());
                matInventoryPojo.setEndoutuid(matTransferEntity.getRefno());
                matInventoryPojo.setLister(matTransferEntity.getLister());
                matInventoryPojo.setModifydate(new Date());
                this.matInventoryService.update(matInventoryPojo);

                // 入库
                matInventoryPojo.setStoreid(matTransferPojo.getInstoreid());
                matInventoryPojo.setLocation(matTransferitemPojo.getInlocation());
                matInventoryPojo.setQuantity(matTransferitemPojo.getQuantity());
                matInventoryPojo.setEndindate(new Date());
                matInventoryPojo.setEndinuid(matTransferEntity.getRefno());
                MatInventoryPojo matInventoryPojo2 = this.matInventoryService.getEntityBySn(matInventoryPojo);
                if (matInventoryPojo2 != null) {
                    matInventoryPojo2.setEndindate(new Date());
                    matInventoryPojo2.setEndinuid(matTransferEntity.getRefno());
                    matInventoryPojo2.setQuantity(matInventoryPojo2.getQuantity() + matTransferitemPojo.getQuantity());
                    this.matInventoryService.update(matInventoryPojo2);
                } else {
                    matInventoryPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
                    this.matInventoryService.insert(matInventoryPojo);
                }
            }
        }


        //返回Bill实例
        return this.getBillEntity(matTransferEntity.getId(), matTransferEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matTransferPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatTransferPojo update(MatTransferPojo matTransferPojo) {
        //主表更改
        MatTransferEntity matTransferEntity = new MatTransferEntity();
        BeanUtils.copyProperties(matTransferPojo, matTransferEntity);
        this.matTransferMapper.update(matTransferEntity);
        //Item子表处理
        List<MatTransferitemPojo> lst = matTransferPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = matTransferMapper.getDelItemIds(matTransferPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                this.matTransferitemMapper.delete(lstDelIds.get(i), matTransferEntity.getTenantid());
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                MatTransferitemEntity matTransferitemEntity = new MatTransferitemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    MatTransferitemPojo itemPojo = this.matTransferitemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, matTransferitemEntity);
                    //设置id和Pid
                    matTransferitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    matTransferitemEntity.setPid(matTransferEntity.getId());  // 主表 id
                    matTransferitemEntity.setTenantid(matTransferPojo.getTenantid());   // 租户id
                    matTransferitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.matTransferitemMapper.insert(matTransferitemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), matTransferitemEntity);
                    matTransferitemEntity.setTenantid(matTransferPojo.getTenantid());
                    this.matTransferitemMapper.update(matTransferitemEntity);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(matTransferEntity.getId(), matTransferEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        MatTransferPojo matTransferPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<MatTransferitemPojo> lst = matTransferPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.matTransferitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.matTransferMapper.delete(key, tid);
    }


    private static void cleanNull(MatTransferPojo matTransferPojo) {
        if (matTransferPojo.getRefno() == null) matTransferPojo.setRefno("");
        if (matTransferPojo.getBilltype() == null) matTransferPojo.setBilltype("");
        if (matTransferPojo.getBilldate() == null) matTransferPojo.setBilldate(new Date());
        if (matTransferPojo.getProjectid() == null) matTransferPojo.setProjectid("");
        if (matTransferPojo.getProjcode() == null) matTransferPojo.setProjcode("");
        if (matTransferPojo.getProjname() == null) matTransferPojo.setProjname("");
        if (matTransferPojo.getBilltitle() == null) matTransferPojo.setBilltitle("");
        if (matTransferPojo.getOperator() == null) matTransferPojo.setOperator("");
        if (matTransferPojo.getOutstoreid() == null) matTransferPojo.setOutstoreid("");
        if (matTransferPojo.getOutstorecode() == null) matTransferPojo.setOutstorecode("");
        if (matTransferPojo.getOutstorename() == null) matTransferPojo.setOutstorename("");
        if (matTransferPojo.getInstoreid() == null) matTransferPojo.setInstoreid("");
        if (matTransferPojo.getInstorecode() == null) matTransferPojo.setInstorecode("");
        if (matTransferPojo.getInstorename() == null) matTransferPojo.setInstorename("");
        if (matTransferPojo.getSummary() == null) matTransferPojo.setSummary("");
        if (matTransferPojo.getCreateby() == null) matTransferPojo.setCreateby("");
        if (matTransferPojo.getCreatebyid() == null) matTransferPojo.setCreatebyid("");
        if (matTransferPojo.getCreatedate() == null) matTransferPojo.setCreatedate(new Date());
        if (matTransferPojo.getLister() == null) matTransferPojo.setLister("");
        if (matTransferPojo.getListerid() == null) matTransferPojo.setListerid("");
        if (matTransferPojo.getModifydate() == null) matTransferPojo.setModifydate(new Date());
        if (matTransferPojo.getReturnuid() == null) matTransferPojo.setReturnuid("");
        if (matTransferPojo.getOrguid() == null) matTransferPojo.setOrguid("");
        if (matTransferPojo.getCustom1() == null) matTransferPojo.setCustom1("");
        if (matTransferPojo.getCustom2() == null) matTransferPojo.setCustom2("");
        if (matTransferPojo.getCustom3() == null) matTransferPojo.setCustom3("");
        if (matTransferPojo.getCustom4() == null) matTransferPojo.setCustom4("");
        if (matTransferPojo.getCustom5() == null) matTransferPojo.setCustom5("");
        if (matTransferPojo.getCustom6() == null) matTransferPojo.setCustom6("");
        if (matTransferPojo.getCustom7() == null) matTransferPojo.setCustom7("");
        if (matTransferPojo.getCustom8() == null) matTransferPojo.setCustom8("");
        if (matTransferPojo.getCustom9() == null) matTransferPojo.setCustom9("");
        if (matTransferPojo.getCustom10() == null) matTransferPojo.setCustom10("");
        if (matTransferPojo.getTenantid() == null) matTransferPojo.setTenantid("");
        if (matTransferPojo.getRevision() == null) matTransferPojo.setRevision(0);
    }


    @Override
    @Transactional
    public MatTransferPojo insertRed(MatTransferPojo matTransferPojoRed, MatTransferEntity matTransferEntityOrg) {
        cleanNull(matTransferPojoRed);
        //类型转换
        String billtype = matTransferPojoRed.getBilltype();
        matTransferPojoRed.setBilltype(billtype+"红冲");
        //switch (billtype) {
        //
        //    case "转移仓库":
        //        matTransferPojoRed.setBilltype("转仓红冲");
        //        break;
        //    case "转移库位":
        //        matTransferPojoRed.setBilltype("转库红冲");
        //        break;
        //    default:
        //        matTransferPojoRed.setBilltype("未定义红冲");
        //}
        // 调入仓库和调出仓库互换
        String instoreid = matTransferPojoRed.getInstoreid();
        String instorename = matTransferPojoRed.getInstorename();
        String outstoreid = matTransferPojoRed.getOutstoreid();
        String outstorename = matTransferPojoRed.getOutstorename();
        matTransferPojoRed.setInstoreid(outstoreid);
        matTransferPojoRed.setOutstoreid(instoreid);
        matTransferPojoRed.setInstorename(outstorename);
        matTransferPojoRed.setOutstorename(instorename);
        // 子表的调出库位和调入库位互换
        List<MatTransferitemPojo> itemList = matTransferPojoRed.getItem();
        for (MatTransferitemPojo itemPojo : itemList) {
            String inlocation = itemPojo.getInlocation();
            String outlocation = itemPojo.getOutlocation();
            itemPojo.setInlocation(outlocation);
            itemPojo.setOutlocation(inlocation);
            // 设置红冲调拨子表的 转出者的库存id
            MatInventoryPojo inventoryPojo = new MatInventoryPojo();
            inventoryPojo.setGoodsid(itemPojo.getGoodsid());
            inventoryPojo.setStoreid(matTransferPojoRed.getOutstoreid());
            inventoryPojo.setBatchno(itemPojo.getBatchno());
            inventoryPojo.setLocation(itemPojo.getOutlocation());
            inventoryPojo.setPacksn(itemPojo.getPacksn());
            inventoryPojo.setTenantid(itemPojo.getTenantid());
            MatInventoryPojo matInventoryPojo = this.matInventoryService.getEntityBySn(inventoryPojo);
            itemPojo.setInveid(matInventoryPojo.getId());
        }

        // 调入库位和调出库位互换
        MatTransferPojo redPojo = this.insert(matTransferPojoRed);
        //更新原表
        this.matTransferMapper.update(matTransferEntityOrg);
        //返回Bill实例
        return redPojo;
    }
}
