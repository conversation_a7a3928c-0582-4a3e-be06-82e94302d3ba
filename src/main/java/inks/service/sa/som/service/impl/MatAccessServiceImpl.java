package inks.service.sa.som.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.config.InksConfigThreadLocal_Sa;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.MatAccessEntity;
import inks.service.sa.som.domain.MatAccessitemEntity;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.MatAccessCiteMapper;
import inks.service.sa.som.mapper.MatAccessMapper;
import inks.service.sa.som.mapper.MatAccessitemMapper;
import inks.service.sa.som.mapper.MatStorageMapper;
import inks.service.sa.som.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 出入库主表(MatAccess)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-12 14:44:34
 */
@Service("matAccessService")
public class MatAccessServiceImpl implements MatAccessService {
    @Resource
    private MatAccessMapper matAccessMapper;

    @Resource
    private MatAccessitemMapper matAccessitemMapper;
    @Resource
    private MatGoodsService matGoodsService;

    @Resource
    private MatAccessitemService matAccessitemService;


    @Resource
    private MatInventoryService matInventoryService;
    @Resource
    private MatSkuService matSkuService;
//    @Resource
//    private MatCustfinishMapper matCustfinishMapper;
//
//    @Resource
//    private MatRequisitionMapper matRequisitionMapper;
//    @Resource
//    private MatRequisitionitemMapper matRequisitionitemMapper;
//    @Resource
//    private MatReqretrunMapper matReqretrunMapper;

    @Resource
    private MatAccessCiteMapper matAccessCiteMapper;
    @Resource
    private SaRedisService saRedisService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private MatStorageMapper matStorageMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatAccessPojo getEntity(String key, String tid) {
        return this.matAccessMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatAccessitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatAccessitemdetailPojo> lst = matAccessMapper.getPageList(queryParam);
            PageInfo<MatAccessitemdetailPojo> pageInfo = new PageInfo<MatAccessitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatAccessPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            MatAccessPojo matAccessPojo = this.matAccessMapper.getEntity(key, tid);
            //读取子表
            matAccessPojo.setItem(matAccessitemMapper.getList(matAccessPojo.getId(), matAccessPojo.getTenantid()));
            return matAccessPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatAccessPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatAccessPojo> lst = matAccessMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(matAccessitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<MatAccessPojo> pageInfo = new PageInfo<MatAccessPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatAccessPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatAccessPojo> lst = matAccessMapper.getPageTh(queryParam);
            PageInfo<MatAccessPojo> pageInfo = new PageInfo<MatAccessPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
//    @Transactional(isolation = Isolation.READ_COMMITTED)
    public MatAccessPojo insert(MatAccessPojo matAccessPojo) {

        //MatAccessPojo初始化NULL字段
        cleanNull(matAccessPojo);
        String direction = matAccessPojo.getDirection();
        Date nowDate = new Date();
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        MatAccessEntity matAccessEntity = new MatAccessEntity();
        BeanUtils.copyProperties(matAccessPojo, matAccessEntity);
        String tid = matAccessEntity.getTenantid();
        String billType = matAccessEntity.getBilltype();
        //设置id和新建日期
        matAccessEntity.setId(id);
        matAccessEntity.setRevision(1);  //乐观锁
        //Item子表处理
        List<MatAccessitemPojo> lst = matAccessPojo.getItem();

        // 1.先获取(并检查)货品信息、库存id  (将goodsid和对应的货品信息存入Map)
        Map<String, MatGoodsBatchAttrPojo> goodsMap = new HashMap<>();
        int num = 0;
        if (CollectionUtils.isNotEmpty(lst)) {
            for (MatAccessitemPojo matAccessitemPojo : lst) {
                num++;
                String goodsidReq = matAccessitemPojo.getGoodsid();
                String goodsUidReq = matAccessitemPojo.getGoodsuid();
                //初始化item的NULL
                matAccessitemPojo = this.matAccessitemService.clearNull(matAccessitemPojo);

                // 1.查询货品信息 货品批次管理属性
                //MatGoodsBatchAttrPojo markPojo = this.matPacksnService.getGoodsBatchAttr(matAccessitemPojo.getGoodsid(), matAccessEntity.getTenantid());
                MatGoodsBatchAttrPojo goodsInfoPojo = this.matGoodsService.getGoodsBatchAttr(matAccessitemPojo.getGoodsid(), matAccessEntity.getTenantid());
                if (goodsInfoPojo == null) {
                    throw new RuntimeException("第" + num + "行:" + goodsUidReq + ":货品信息丢失");
                }
                // 当SkuMark=1时，传入的Attributejson需要重新计算
                // ：传入的Attributejson不能为空，且需将传入的Attributejson和Mat_Attribute表取交集(且SkuMark=1)，拿到新Attributejson赋值回去
                if (Objects.equals(goodsInfoPojo.getSkumark(), 1)) {
                    String attributejson = matAccessitemPojo.getAttributejson();
                    if (isBlank(attributejson)) {
                        throw new RuntimeException("第" + num + "行:" + goodsUidReq + ":缺少SKU属性信息");
                    }
                    // 传入的Attributejson和Mat_Attribute表取交集(且SkuMark=1)，拿到新newAttrSkuJson
                    String newAttrSku = this.matSkuService.getAttrSku(attributejson, tid);
                    matAccessitemPojo.setAttributejson(newAttrSku);
                }
                //// 有效期检查
                //Integer goodsExpiMark = goodsInfoPojo.getExpimark();
                //if (Objects.equals(goodsExpiMark, 1) && matAccessitemPojo.getExpidate() == null) {
                //    throw new RuntimeException("第" + num + "行:" + goodsUidReq + ":请设置过期日期");
                //}
                // 批次号校验 (入库单时)
                if ("入库单".equals(direction) && Objects.equals(goodsInfoPojo.getBatchmg(), 1)) {
                    if (isBlank(matAccessitemPojo.getBatchno())) {
                        throw new RuntimeException("第" + num + "行:" + goodsUidReq + ":缺少批次信息");
                    }
                    // 是否独立批次：到库存表校验批次号重复 (红冲单不校验)
                    if (Objects.equals(goodsInfoPojo.getBatchonly(), 1) && isBlank(matAccessPojo.getOrguid())) {
                        MatInventoryPojo matInventoryPojo = new MatInventoryPojo();
                        matInventoryPojo.setGoodsid(goodsidReq);
                        matInventoryPojo.setBatchno(matAccessitemPojo.getBatchno() != null ? matAccessitemPojo.getBatchno() : "");
                        matInventoryPojo.setTenantid(tid);
                        MatInventoryPojo matInvoOrgPojo = this.matInventoryService.getEntityByBatch(matInventoryPojo);
                        if (matInvoOrgPojo != null) {
                            throw new RuntimeException("第" + num + "行:" + goodsUidReq + ":批次号重复");
                        }
                    }
                }
                goodsMap.put(goodsidReq, goodsInfoPojo);


                // 2.如果未传入库存id 查询库存id

                MatInventoryPojo matInventoryPojo;
                if (isBlank(matAccessitemPojo.getInveid())) {
                    // 没有指定库存id
                    matInventoryPojo = new MatInventoryPojo();
                    matInventoryPojo.setGoodsid(goodsidReq);
                    matInventoryPojo.setQuantity(0D);//无库存id的入库单，且查不到库存，新建可传时数量、金额置0
                    matInventoryPojo.setAmount(0D);
                    matInventoryPojo.setBatchno(matAccessitemPojo.getBatchno() != null ? matAccessitemPojo.getBatchno() : "");
                    matInventoryPojo.setLocation(matAccessitemPojo.getLocation() != null ? matAccessitemPojo.getLocation() : "");
                    matInventoryPojo.setPacksn(matAccessitemPojo.getPacksn() != null ? matAccessitemPojo.getPacksn() : "");
                    matInventoryPojo.setSkuid(matAccessitemPojo.getSkuid() != null ? matAccessitemPojo.getSkuid() : "");
                    matInventoryPojo.setAttributejson(matAccessitemPojo.getAttributejson() != null ? matAccessitemPojo.getAttributejson() : "");
                    matInventoryPojo.setStoreid(matAccessEntity.getStoreid());
                    matInventoryPojo.setTenantid(tid);
                    matInventoryPojo.setLister(matAccessEntity.getLister());
                    matInventoryPojo.setCreateby(matAccessEntity.getLister());
                    matInventoryPojo.setCreatedate(nowDate);
                    matInventoryPojo.setModifydate(nowDate);
                    matInventoryPojo.setEndindate(nowDate);
                    matInventoryPojo.setEndinuid(matAccessEntity.getRefno());
                    matInventoryPojo.setExpidate(matAccessitemPojo.getExpidate());// 20240820 matAccessitem的有效期存入库存中
                    if ("入库单".equals(direction)) {
                        //无库存id的"入库单"
                        MatInventoryPojo matInvoOrgPojo = new MatInventoryPojo();
                        // 有SkuMark
                        if (Objects.equals(goodsInfoPojo.getSkumark(), 1)) {
                            // 新AttrSkuJson查询Mat_Sku
                            MatSkuPojo matSkuDB = this.matSkuService.getEntityByAtte(goodsidReq, matAccessitemPojo.getAttributejson(), tid);
                            // 如果入库单无skuid,则新建一个sku  TODO 事务外 不回滚
                            if (matSkuDB == null) {
                                matSkuDB = new MatSkuPojo();
                                matSkuDB.setGoodsid(goodsidReq);
                                matSkuDB.setGoodsuid(goodsUidReq);
                                matSkuDB.setItemcode(goodsUidReq);
                                matSkuDB.setItemname(matAccessitemPojo.getGoodsname());
                                matSkuDB.setAttributejson(matAccessitemPojo.getAttributejson());
                                matSkuDB.setTenantid(tid);
                                matSkuDB.setLister(matAccessEntity.getLister());
                                matSkuDB.setListerid(matAccessEntity.getListerid());
                                matSkuDB.setCreateby(matAccessEntity.getLister());
                                matSkuDB.setCreatebyid(matAccessEntity.getCreatebyid());
                                matSkuDB.setCreatedate(nowDate);
                                matSkuDB.setModifydate(nowDate);
                                matSkuDB.setRemark(matAccessEntity.getRefno() + " 时自动建立");
                                matSkuDB = this.matSkuService.insert(matSkuDB);
                                matAccessitemPojo.setSkuid(matSkuDB.getId());
                            }
                            // 赋值库存的skuid
                            matAccessitemPojo.setSkuid(matSkuDB.getId());
                            matInventoryPojo.setSkuid(matSkuDB.getId());
                            matInvoOrgPojo = this.matInventoryService.getEntityBySku(matInventoryPojo);
                        } else {
                            //无SkuMark，查询库存不带Attributejson
                            matInvoOrgPojo = this.matInventoryService.getEntityBySn(matInventoryPojo);
                        }

                        // 如果没有找到库存，则创建一行库存 TODO 事务外 不回滚
                        if (matInvoOrgPojo == null || matInvoOrgPojo.getId() == null) {
                            matInvoOrgPojo = this.matInventoryService.insert(matInventoryPojo);
                        }
                        // 至此，无库存id的"入库单"都赋值上了库存id
                        matAccessitemPojo.setInveid(matInvoOrgPojo.getId());
                    }
                    //无库存id的"出库单"
                    else if ("出库单".equals(direction)) {
                        // Sku库存
                        if (Objects.equals(goodsInfoPojo.getSkumark(), 1)) {
                            // 新AttrSkuJson查询Mat_Sku
                            MatSkuPojo matSkuDB = this.matSkuService.getEntityByAtte(goodsidReq, matAccessitemPojo.getAttributejson(), tid);
                            if (matSkuDB != null) {
                                matAccessitemPojo.setSkuid(matSkuDB.getId());
                                matInventoryPojo.setSkuid(matSkuDB.getId());
                                matInventoryPojo = this.matInventoryService.getEntityBySku(matInventoryPojo);
                            } else {
                                throw new RuntimeException("第" + num + "行:" + goodsUidReq + ":未找到SKU信息,确认是否有入库记录");
                            }
                        } else {// 无SkuMark
                            matInventoryPojo = this.matInventoryService.getEntityBySn(matInventoryPojo);
                        }
                        if (matInventoryPojo != null) {
                            // 至此，无库存id的"出库单"都赋值上了库存id
                            matAccessitemPojo.setInveid(matInventoryPojo.getId());
                        } else {
                            throw new RuntimeException("第" + num + "行:" + goodsUidReq + ":未找到库存信息");
                        }
                    }
                }

                // 3.根据类型获取单价
                double priceByType = getPriceByType(matAccessEntity, matAccessitemPojo, goodsInfoPojo.getAgeprice());
                matAccessitemPojo.setPrice(priceByType);
            }
        }


        // 在需要的地方设置事务隔离级别为READ_COMMITTED
        transactionTemplate.setIsolationLevel(Isolation.READ_COMMITTED.value());
        transactionTemplate.execute((status) -> {
            //插入主表
            this.matAccessMapper.insert(matAccessEntity);
            int rowNum = 0;//用于报错第几行,下标要从1开始
            if (CollectionUtils.isNotEmpty(lst)) {
                //循环每个item子表
                for (MatAccessitemPojo matAccessitemPojo : lst) {
                    rowNum++;
                    // 前端传入的原生出入库单子表信息Req
                    String goodsidReq = matAccessitemPojo.getGoodsid();
                    String goodsUidReq = matAccessitemPojo.getGoodsuid();


                    // 从Map拿到货品信息
                    MatGoodsBatchAttrPojo goodsInfoPojo = goodsMap.get(goodsidReq);

                    // 4.如果是入库单 (还有额外操作：初始化创建PackSN和SKU)
                    if ("入库单".equals(direction)) {
                        //// 如果是SN包装 初始化创建一条 Mat_Packsn
                        //if (Objects.equals(goodsInfoPojo.getPacksnmark(), 1)) {
                        //    MatPacksnPojo matPacksnPojo = new MatPacksnPojo();
                        //    matPacksnPojo.setGoodsid(goodsidReq);
                        //    matPacksnPojo.setSubmark(0);
                        //    matPacksnPojo.setTenantid(tid);
                        //    matPacksnPojo.setLister(matAccessEntity.getLister());
                        //    matPacksnPojo.setListerid(matAccessEntity.getListerid());
                        //    matPacksnPojo.setCreateby(matAccessEntity.getCreateby());
                        //    matPacksnPojo.setCreatebyid(matAccessEntity.getCreatebyid());
                        //    matPacksnPojo.setCreatedate(nowDate);
                        //    matPacksnPojo.setModifydate(nowDate);
                        //    matPacksnPojo = this.matPacksnService.getNewPackSn(matPacksnPojo);
                        //    matAccessitemPojo.setPacksn(matPacksnPojo.getPacksn());
                        //} else {
                            matAccessitemPojo.setPacksn("");
                        //}
                    }


                    // 3.如果传入了库存id
                    if (isNotBlank(matAccessitemPojo.getInveid())) {
                        // 通过类型拿到的单价
                        double priceByType = matAccessitemPojo.getPrice();
                        // 有库存id，直接查出库存信息
                        MatInventoryPojo matInventoryDB = this.matInventoryService.getEntity(matAccessitemPojo.getInveid(), tid);
                        if (matInventoryDB == null) {
                            throw new RuntimeException("第" + rowNum + "行:" + goodsUidReq + ":库存数据丢失");
                        }
                        matAccessitemPojo.setSkuid(matInventoryDB.getSkuid());

                        // 类型的单价、本次子表数量
                        BigDecimal priceByTypeBig = BigDecimal.valueOf(priceByType);
                        BigDecimal quantityBig = BigDecimal.valueOf(matAccessitemPojo.getQuantity());
                        if ("入库单".equals(direction)) {
                            // 计算入库单子表金额：数量 * 类型的单价
                            BigDecimal amountBig = quantityBig.multiply(priceByTypeBig);
                            matAccessitemPojo.setAmount(amountBig.doubleValue());
                            // 更新库存数量：原库存数量 + 本次入库数量
                            BigDecimal orgInvQuantityBig = BigDecimal.valueOf(matInventoryDB.getQuantity());
                            BigDecimal newInvQuantityBig = orgInvQuantityBig.add(quantityBig);
                            matInventoryDB.setQuantity(newInvQuantityBig.doubleValue());
                            // 更新库存金额：原库存金额 + 本次入库的金额
                            BigDecimal orgInvAmountBig = BigDecimal.valueOf(matInventoryDB.getAmount());
                            BigDecimal newInvAmountBig = orgInvAmountBig.add(amountBig);
                            matInventoryDB.setAmount(newInvAmountBig.doubleValue());
                            // 更新库存入库日期与单据号
                            matInventoryDB.setEndindate(nowDate);
                            matInventoryDB.setEndinuid(matAccessEntity.getRefno());
                        } else { // 出库单
                            BigDecimal orgInvQuantityBig = BigDecimal.valueOf(matInventoryDB.getQuantity());
                            // 检查库存数量是否足够：原库存数量 - 出库数量 < 0 则异常
                            if (orgInvQuantityBig.subtract(quantityBig).compareTo(BigDecimal.ZERO) < 0) {
                                throw new RuntimeException("第" + rowNum + "行:" + goodsUidReq
                                        + ":库存数量不足" + matInventoryDB.getQuantity() + "<" + matAccessitemPojo.getQuantity());
                            }
                            // 计算出库单子表金额：数量 * 类型的单价
                            BigDecimal amountBig = quantityBig.multiply(priceByTypeBig);
                            matAccessitemPojo.setAmount(amountBig.doubleValue());

                            // 更新库存数量：原库存数量 - 本次入库数量
                            BigDecimal newInvQuantityBig = orgInvQuantityBig.subtract(quantityBig);
                            matInventoryDB.setQuantity(newInvQuantityBig.doubleValue());
                            // 更新库存金额：原库存金额 - 本次出库的金额
                            BigDecimal orgInvAmountBig = BigDecimal.valueOf(matInventoryDB.getAmount());
                            BigDecimal newInventoryAmount = orgInvAmountBig.subtract(amountBig);
                            matInventoryDB.setAmount(newInventoryAmount.doubleValue());
                            // 更新库存出库日期与单据号
                            matInventoryDB.setEndoutdate(nowDate);
                            matInventoryDB.setEndoutuid(matAccessEntity.getRefno());
                        }
                        matInventoryDB.setLister(matAccessEntity.getLister());
                        matInventoryDB.setModifydate(nowDate);
                        matInventoryDB.setExpidate(matAccessitemPojo.getExpidate());// 20240820 matAccessitem的有效期存入库存中
                        this.matInventoryService.update(matInventoryDB);
                    }


                    // 插入子表 matAccessitemPojo
                    MatAccessitemEntity matAccessitemEntity = new MatAccessitemEntity();
                    BeanUtils.copyProperties(matAccessitemPojo, matAccessitemEntity);
                    //设置id和Pid
                    matAccessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                    matAccessitemEntity.setPid(id);
                    matAccessitemEntity.setTenantid(tid);
                    matAccessitemEntity.setRevision(1);  //乐观锁
                    this.matAccessitemMapper.insert(matAccessitemEntity);
                    // 同步关联单据
                    this.updateCiteBill(matAccessEntity, matAccessitemPojo);

                }
            }
            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(MatAccessitemPojo::getGoodsid).collect(Collectors.toSet());
            // 同步货品库存数量和库存单价 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                matGoodsService.updateGoodsIvQuantity(goodsid, tid);
                this.updateCiteGoodsQty(billType, goodsid, tid);//少了一个类型是领料出库,需要额外处理见下行(不仅要更新货品数量，还要更新单据上的订单成本)
            });
            if ("领料出库".equals(billType) || "领料红冲".equals(billType)) {
                this.syncRequGoodsAndCostAmt(lst, tid);
            }
            return Boolean.TRUE;
        });

        //返回Bill实例
        return this.getBillEntity(matAccessEntity.getId(), tid);

    }

    // 单价根据单据类型获取； agePrice是货品表的库存单价Mat_Goods.AgePrice
    private double getPriceByType(MatAccessEntity matAccessEntity, MatAccessitemPojo item, double agePrice) {
        String tid = matAccessEntity.getTenantid();
        String billtype = matAccessEntity.getBilltype();
        String citeitemid = item.getCiteitemid();

        double price = 0D;
        switch (billtype) {
            case "发货出库":
            case "客退入库":
            case "领料出库":
            case "退料入库":
            case "其他出库":
            case "报废出库":
            case "盘盈入库":
            case "盘亏出库":
                // 单价来自加权平均 Mat_Goods.AgePrice
                price = agePrice;
                break;
            case "收货入库":
            case "购退出库":
                // 单价来自采购验收单
                price = matAccessCiteMapper.getPriceFromBuyFinishItem(citeitemid, tid);
                break;
            case "其他入库":
            case "他入红冲":
            case "发货红冲":
            case "客退红冲":
            case "领料红冲":
            case "退料红冲":
            case "他出红冲":
            case "报废红冲":
            case "盘盈红冲":
            case "盘亏红冲":
            case "收货红冲":
            case "购退红冲":
            case "生产红冲":
            case "加工红冲":
            case "厂制红冲":
            case "委制红冲":
                // 其他入库 (以及所有红冲单)单价来自入库单自定义
                price = item.getPrice();
                break;
            case "生产入库":
            case "加工入库":
            case "厂制入库":
            case "委制入库":
                price = 0D;
                break;
            default:
                // 其他单据类型，抛异常
                throw new RuntimeException("单据类型错误：" + billtype);
        }
        return price;
    }


//    //@Override
////    @Transactional(isolation = Isolation.READ_COMMITTED)
//    public MatAccessPojo insertOld(MatAccessPojo matAccessPojo) {
//        //LoginUser loginUser = saRedisService.getLoginUser();
//        // 读取所有参数,改为 feign Eric 20230203
////        R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, loginUser.getTenantid(), loginUser.getToken());
//        Map<String, String> mapConfig = new HashMap<>();
////        if (r.getCode() == 200) {
////            mapConfig = r.getData();
////        }
//        //MatAccessPojo初始化NULL字段
//        cleanNull(matAccessPojo);
//        //生成id
//        String id = inksSnowflake.getSnowflake().nextIdStr();
//        MatAccessEntity matAccessEntity = new MatAccessEntity();
//        BeanUtils.copyProperties(matAccessPojo, matAccessEntity);
//        String tid = matAccessEntity.getTenantid();
//        String billType = matAccessEntity.getBilltype();
//        //设置id和新建日期
//        matAccessEntity.setId(id);
//        matAccessEntity.setRevision(1);  //乐观锁
//        //Item子表处理
//        List<MatAccessitemPojo> lst = matAccessPojo.getItem();
//        Map<String, String> finalMapConfig = mapConfig;
//        // 在需要的地方设置事务隔离级别为READ_COMMITTED
//        transactionTemplate.setIsolationLevel(Isolation.READ_COMMITTED.value());
//        transactionTemplate.execute((status) -> {
//            //插入主表
//            this.matAccessMapper.insert(matAccessEntity);
//            int rowNum = 0;//用于报错第几行,下标要从1开始
//            if (lst != null) {
//                //循环每个item子表
//                for (MatAccessitemPojo matAccessitemPojo : lst) {
//                    rowNum++;
//                    //初始化item的NULL
//                    MatAccessitemPojo itemPojo = this.matAccessitemService.clearNull(matAccessitemPojo);
//                    MatAccessitemEntity matAccessitemEntity = new MatAccessitemEntity();
//                    BeanUtils.copyProperties(itemPojo, matAccessitemEntity);
//                    //设置id和Pid
//                    matAccessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
//                    matAccessitemEntity.setPid(id);
//                    matAccessitemEntity.setTenantid(tid);
//                    matAccessitemEntity.setRevision(1);  //乐观锁
//                    // 货品批次管理属性
//                    //MatGoodsBatchAttrPojo markPojo = this.matPacksnService.getGoodsBatchAttr(matAccessitemPojo.getGoodsid(), matAccessEntity.getTenantid());
//                    MatGoodsPojo markPojo = this.matGoodsService.getEntity(matAccessitemPojo.getGoodsid(), matAccessEntity.getTenantid());
//
//                    if (markPojo == null) {
//                        throw new RuntimeException("第" + rowNum + "行:" + matAccessitemPojo.getGoodsuid() + ":货品信息丢失");
//                    }
//                    // 入库单,初始化SN
//                    if ("入库单".equals(matAccessPojo.getDirection())) {
//                        // 如果是SN包装
////                        if (markPojo.getPacksnmark() != null && markPojo.getPacksnmark() > 0) {
////                            MatPacksnPojo matPacksnPojo = new MatPacksnPojo();
////                            matPacksnPojo.setGoodsid(lst.get(i).getGoodsid());
////                            matPacksnPojo.setSubmark(0);
////                            matPacksnPojo.setTenantid(matAccessEntity.getTenantid());
////                            matPacksnPojo.setLister(matAccessEntity.getLister());
////                            matPacksnPojo.setListerid(matAccessEntity.getListerid());
////                            matPacksnPojo.setCreateby(matAccessEntity.getCreateby());
////                            matPacksnPojo.setCreatebyid(matAccessEntity.getCreatebyid());
////                            matPacksnPojo.setCreatedate(new Date());
////                            matPacksnPojo.setModifydate(new Date());
////                            matPacksnPojo = this.matPacksnService.getNewPackSn(matPacksnPojo);
////                            matAccessitemEntity.setPacksn(matPacksnPojo.getPacksn());
////                            lst.get(i).setPacksn(matPacksnPojo.getPacksn());
////                        } else {
//                        matAccessitemEntity.setPacksn("");
//                        matAccessitemPojo.setPacksn("");
////                        }
//                        // 批次管理
//                        if (markPojo.getBatchmg() != null && markPojo.getBatchmg() > 0) {
//                            if (matAccessitemPojo.getBatchno() == null || matAccessitemPojo.getBatchno().isEmpty()) {
//                                throw new RuntimeException("第" + rowNum + "行:" + matAccessitemPojo.getGoodsuid() + ":缺少批次信息");
//                            }
//                            // 是否独立批次
//                            if (markPojo.getBatchonly() != null && markPojo.getBatchonly() > 0) {
//                                MatInventoryPojo matInventoryPojo = new MatInventoryPojo();
//                                matInventoryPojo.setGoodsid(matAccessitemPojo.getGoodsid());
//                                matInventoryPojo.setBatchno(matAccessitemPojo.getBatchno() != null ? matAccessitemPojo.getBatchno() : "");
//                                matInventoryPojo.setTenantid(tid);
//                                MatInventoryPojo matInvoOrgPojo = this.matInventoryService.getEntityByBatch(matInventoryPojo);
//                                if (matInvoOrgPojo != null && StringUtils.isBlank(matAccessPojo.getOrguid())) {//红冲单不校验批次号重复
//                                    throw new RuntimeException("第" + rowNum + "行:" + matAccessitemPojo.getGoodsuid() + ":批次号重复");
//                                }
//                            }
//                        }
//                        // Sku库存
//                        if (markPojo.getSkumark() != null && markPojo.getSkumark() > 0) {
//                            if (matAccessitemPojo.getAttributejson() == null || matAccessitemPojo.getAttributejson().isEmpty()) {
//                                throw new RuntimeException("第" + rowNum + "行:" + matAccessitemPojo.getGoodsuid() + ":缺少SKU信息");
//                            }
//                            String AttrSkuJson = this.matSkuService.getAttrSku(matAccessitemPojo.getAttributejson(), tid);
//                            MatSkuPojo matSkuPojo = this.matSkuService.getEntityByAtte(matAccessitemPojo.getGoodsid(), AttrSkuJson, tid);
//                            matAccessitemPojo.setAttributejson(AttrSkuJson);
//                            // item赋值SKUid
//                            if (matSkuPojo != null) {
//                                matAccessitemPojo.setSkuid(matSkuPojo.getId());
//                            } else {
//                                matSkuPojo = new MatSkuPojo();
//                                matSkuPojo.setGoodsid(matAccessitemPojo.getGoodsid());
//                                matSkuPojo.setGoodsuid(matAccessitemPojo.getGoodsuid());
//                                matSkuPojo.setItemcode(matAccessitemPojo.getGoodsuid());
//                                matSkuPojo.setItemname(matAccessitemPojo.getGoodsname());
//                                matSkuPojo.setAttributejson(matAccessitemPojo.getAttributejson());
//                                matSkuPojo.setTenantid(tid);
//                                matSkuPojo.setLister(matAccessEntity.getLister());
//                                matSkuPojo.setListerid(matAccessEntity.getListerid());
//                                matSkuPojo.setCreateby(matAccessEntity.getLister());
//                                matSkuPojo.setCreatebyid(matAccessEntity.getCreatebyid());
//                                matSkuPojo.setCreatedate(new Date());
//                                matSkuPojo.setModifydate(new Date());
//                                matSkuPojo.setRemark(matAccessEntity.getRefno() + " 时自动建立");
//                                matSkuPojo = this.matSkuService.insert(matSkuPojo);
//                                matAccessitemPojo.setSkuid(matSkuPojo.getId());
//                            }
//                            matAccessitemEntity.setSkuid(matAccessitemPojo.getId());
//                        }
//                    }
//
//                    if ("".equals(matAccessitemPojo.getInveid())) {
//                        // 没有指定库存id
//                        MatInventoryPojo matInventoryPojo = new MatInventoryPojo();
//                        matInventoryPojo.setGoodsid(matAccessitemPojo.getGoodsid());
//                        matInventoryPojo.setQuantity(matAccessitemPojo.getQuantity());
//                        matInventoryPojo.setAmount(matAccessitemPojo.getAmount());
//                        matInventoryPojo.setBatchno(matAccessitemPojo.getBatchno() != null ? matAccessitemPojo.getBatchno() : "");
//                        matInventoryPojo.setLocation(matAccessitemPojo.getLocation() != null ? matAccessitemPojo.getLocation() : "");
//                        matInventoryPojo.setPacksn(matAccessitemPojo.getPacksn() != null ? matAccessitemPojo.getPacksn() : "");
//                        matInventoryPojo.setSkuid(matAccessitemPojo.getSkuid() != null ? matAccessitemPojo.getSkuid() : "");
//                        matInventoryPojo.setAttributejson(matAccessitemPojo.getAttributejson() != null ? matAccessitemPojo.getAttributejson() : "");
//                        matInventoryPojo.setStoreid(matAccessEntity.getStoreid());
//                        matInventoryPojo.setTenantid(tid);
//                        matInventoryPojo.setLister(matAccessEntity.getLister());
//                        matInventoryPojo.setCreateby(matAccessEntity.getLister());
//                        matInventoryPojo.setCreatedate(new Date());
//                        matInventoryPojo.setModifydate(new Date());
//                        matInventoryPojo.setEndindate(new Date());
//                        matInventoryPojo.setEndinuid(matAccessEntity.getRefno());
//                        if ("入库单".equals(matAccessPojo.getDirection())) {
//                            MatInventoryPojo matInvoOrgPojo = new MatInventoryPojo();
//                            // Sku库存
//                            if (markPojo.getSkumark() != null && markPojo.getSkumark() > 0) {
//                                MatSkuPojo matSkuPojo = this.matSkuService.getEntityByAtte(matInventoryPojo.getGoodsid(), matInventoryPojo.getAttributejson(), tid);
//                                if (matSkuPojo != null) {
//                                    matAccessitemPojo.setSkuid(matSkuPojo.getId());
//                                    matInventoryPojo.setSkuid(matSkuPojo.getId());
//                                    matInvoOrgPojo = this.matInventoryService.getEntityBySku(matInventoryPojo);
//                                }
//                            } else {
//                                matInvoOrgPojo = this.matInventoryService.getEntityBySn(matInventoryPojo);
//                            }
//
//                            if (matInvoOrgPojo != null && matInvoOrgPojo.getId() != null) {
//                                matInvoOrgPojo.setQuantity(matInvoOrgPojo.getQuantity() + matAccessitemPojo.getQuantity());
//                                matInvoOrgPojo.setAmount(matInvoOrgPojo.getAmount() + matAccessitemPojo.getAmount());
//                                matInvoOrgPojo.setEndindate(new Date());
//                                matInvoOrgPojo.setEndinuid(matAccessEntity.getRefno());
//                                matInvoOrgPojo.setLister(matAccessEntity.getLister());
//                                matInvoOrgPojo.setModifydate(new Date());
//                                this.matInventoryService.update(matInvoOrgPojo);
//                            } else {
//                                matInvoOrgPojo = this.matInventoryService.insert(matInventoryPojo);
//                                matAccessitemPojo.setInveid(matInvoOrgPojo.getId());
//                            }
//                        } else {
//                            // Sku库存
//                            if (markPojo.getSkumark() != null && markPojo.getSkumark() > 0) {
//                                String attributejson = matInventoryPojo.getAttributejson();
//                                if (isNotBlank(attributejson)) {
//                                    String AttrSkuJson = this.matSkuService.getAttrSku(matAccessitemPojo.getAttributejson(), tid);
//                                    matInventoryPojo.setAttributejson(AttrSkuJson);
//                                    MatSkuPojo matSkuPojo = this.matSkuService.getEntityByAtte(matInventoryPojo.getGoodsid(), attributejson, tid);
//                                    if (matSkuPojo != null) {
//                                        matAccessitemPojo.setSkuid(matSkuPojo.getId());
//                                        matInventoryPojo.setSkuid(matSkuPojo.getId());
//                                        matInventoryPojo = this.matInventoryService.getEntityBySku(matInventoryPojo);
//                                    } else {
//                                        throw new RuntimeException("第" + rowNum + "行:" + matAccessitemPojo.getGoodsuid() + ":未找到SKU信息,确认是否有入库记录");
//                                    }
//                                } else {//出库类型，手动设置货品无sku，则在库存中查找Attributejson为空或''的记录
//                                    matInventoryPojo = this.matInventoryService.getEntityBySnNoSku(matInventoryPojo);
//                                }
//
//                            } else {
//                                matInventoryPojo = this.matInventoryService.getEntityBySn(matInventoryPojo);
//                            }
//                            if (matInventoryPojo != null) {
//                                if (matInventoryPojo.getQuantity() - matAccessitemPojo.getQuantity() < 0) {
//                                    throw new RuntimeException("第" + rowNum + "行:" + matAccessitemPojo.getGoodsuid() + ":库存数量不足");
//                                }
//                                double ivPrice = 0;
//                                if (matInventoryPojo.getQuantity() != 0) {
//                                    ivPrice = matInventoryPojo.getAmount() / matInventoryPojo.getQuantity();
//                                }
//                                matAccessitemEntity.setPrice(ivPrice);
//                                matAccessitemEntity.setAmount(matAccessitemPojo.getQuantity() * ivPrice);
//                                matInventoryPojo.setQuantity(matInventoryPojo.getQuantity() - matAccessitemPojo.getQuantity());
//                                matInventoryPojo.setAmount(matInventoryPojo.getQuantity() * ivPrice);
//                                if (matInventoryPojo.getAmount() < 0) matInventoryPojo.setAmount(0D);
//                                matInventoryPojo.setEndoutdate(new Date());
//                                matInventoryPojo.setEndoutuid(matAccessEntity.getRefno());
//                                matInventoryPojo.setLister(matAccessEntity.getLister());
//                                matInventoryPojo.setModifydate(new Date());
//                                // sa-oms专用，红冲单价取原单价 原金额 库存金额直接减去
//                                if (billType.contains("红冲")) {
//                                    matAccessitemEntity.setPrice(matAccessitemPojo.getPrice());
//                                    matAccessitemEntity.setAmount(matAccessitemPojo.getAmount());
//                                    matInventoryPojo.setAmount(matInventoryPojo.getAmount() - matAccessitemPojo.getAmount());
//                                }
//                                this.matInventoryService.update(matInventoryPojo);
//                            } else {
//                                throw new RuntimeException("第" + rowNum + "行:" + matAccessitemPojo.getGoodsuid() + ":未找到库存信息");
//                            }
//                        }
//                    } else {
//                        // 有指库存id
//                        MatInventoryPojo matInventoryPojo = this.matInventoryService.getEntity(matAccessitemPojo.getInveid(), tid);
//                        if (matInventoryPojo == null) {
//                            throw new RuntimeException("第" + rowNum + "行:" + matAccessitemPojo.getGoodsuid() + ":库存数据丢失");
//                        }
//                        if ("入库单".equals(matAccessPojo.getDirection())) {
//                            matInventoryPojo.setQuantity(matInventoryPojo.getQuantity() + matAccessitemPojo.getQuantity());
//                            matInventoryPojo.setAmount(matInventoryPojo.getAmount() + matAccessitemPojo.getAmount());
//                            matInventoryPojo.setEndindate(new Date());
//                            matInventoryPojo.setEndinuid(matAccessEntity.getRefno());
//                        } else {
//                            if (matInventoryPojo.getQuantity() - matAccessitemPojo.getQuantity() < 0) {
//                                throw new RuntimeException("第" + rowNum + "行:" + matAccessitemPojo.getGoodsuid() + ":库存数量不足" + matInventoryPojo.getQuantity() + "<" + matAccessitemPojo.getQuantity());
//                            }
//                            double ivPrice = 0;
//                            if (matInventoryPojo.getQuantity() != 0) {
//                                ivPrice = matInventoryPojo.getAmount() / matInventoryPojo.getQuantity();
//                            }
//                            matAccessitemEntity.setPrice(ivPrice);
//                            matAccessitemEntity.setAmount(matAccessitemPojo.getQuantity() * ivPrice);
//                            matInventoryPojo.setQuantity(matInventoryPojo.getQuantity() - matAccessitemPojo.getQuantity());
//                            matInventoryPojo.setAmount(matInventoryPojo.getQuantity() * ivPrice);
//                            if (matInventoryPojo.getAmount() < 0) matInventoryPojo.setAmount(0D);
//                            matInventoryPojo.setEndoutdate(new Date());
//                            matInventoryPojo.setEndoutuid(matAccessEntity.getRefno());
//                            // sa-oms专用，红冲单价取原单价 原金额 库存金额直接减去
//                            if (billType.contains("红冲")) {
//                                matAccessitemEntity.setPrice(matAccessitemPojo.getPrice());
//                                matAccessitemEntity.setAmount(matAccessitemPojo.getAmount());
//                                matInventoryPojo.setAmount(matInventoryPojo.getAmount() - matAccessitemPojo.getAmount());
//                            }
//                        }
//                        matInventoryPojo.setLister(matAccessEntity.getLister());
//                        matInventoryPojo.setModifydate(new Date());
//                        this.matInventoryService.update(matInventoryPojo);
//                    }
//                    // 插入子表
//                    this.matAccessitemMapper.insert(matAccessitemEntity);
//                    // 同步货品数量
//
//                    // 同步关联单据
//                    this.updateCiteBill(matAccessEntity, matAccessitemPojo, finalMapConfig);
//
//
//                }
//            }
//            for (MatAccessitemPojo matAccessitemPojo : lst) {
//                // 同步货品数量 MQ生产者  nanno 20230222
////MqBaseParamPojo mqBaseParamPojo = new MqBaseParamPojo(lst.get(i).getGoodsid(), "IvQuantity", tid);
////            this.rabbitTemplate.convertAndSend("updateGoodsQty", JSON.toJSONString(mqBaseParamPojo));
//                matGoodsService.updateGoodsIvQuantity(matAccessitemPojo.getGoodsid(), tid);
//                // 同步关联单据
//                this.updateCiteGoodsQty(matAccessEntity, matAccessitemPojo);
//
//            }
//            if ("领料出库".equals(billType) || "领料红冲".equals(billType)) {
//                this.syncRequGoodsAndCostAmt(lst, tid);
//            }
//            return Boolean.TRUE;
//        });
//
//        //返回Bill实例
//        return this.getBillEntity(matAccessEntity.getId(), tid);
//
//    }


    // 更新关联单据
    private void updateCiteBill(MatAccessEntity matAccessEntity, MatAccessitemPojo item) {
        String tid = matAccessEntity.getTenantid();
        String billtype = matAccessEntity.getBilltype();
        String citeuid = item.getCiteuid();
        String citeitemid = item.getCiteitemid();
        String orderitemid = item.getOrderitemid();
        String orderuid = item.getOrderuid();
        String machitemid = item.getMachitemid();
        String machuid = item.getMachuid();
        Date now = new Date();
        //1发货出库
        if ("发货出库".equals(billtype) || "客退入库".equals(billtype) ||
                "发货红冲".equals(billtype) || "客退红冲".equals(billtype)) {
            if (!"".equals(citeitemid) && !"".equals(citeuid)) {
                this.matAccessCiteMapper.updateDeliAcceFinish(citeitemid, citeuid, tid);
                Double remQty = this.matAccessCiteMapper.getDeliRemQty(citeitemid, tid);
                if (remQty == null || remQty < 0) {
                    throw new RuntimeException(item.getGoodsuid() + ":超数" + billtype + remQty);
                }
                // 刷新单据完工行数
                this.matAccessCiteMapper.updateDeliFinishCount(citeitemid, citeuid, tid);
            }
            if (!"".equals(machitemid) && !"".equals(machuid)) {
                // 同步销售订单子表的已出入库数：OutQuantity
                this.matAccessCiteMapper.updateBusMachItemOutQuantity(machitemid, machuid, tid);
            }
        }

        //2收货入库
        if ("收货入库".equals(billtype) || "购退出库".equals(billtype) ||
                "收货红冲".equals(billtype) || "购退红冲".equals(billtype)) {
            if (!"".equals(citeitemid) && !"".equals(citeuid)) {
                this.matAccessCiteMapper.updateBuyFAcceFinish(citeitemid, citeuid, tid);
                Double remQty = this.matAccessCiteMapper.getBuyFRemQty(citeitemid, tid);
                if (remQty == null || remQty < 0) {
                    throw new RuntimeException(item.getGoodsuid() + ":超数" + billtype + remQty);
                }
                // 刷新单据完工行数
                this.matAccessCiteMapper.updateBuyFFinishCount(citeitemid, citeuid, tid);
            }
            if (!"".equals(orderitemid) && !"".equals(orderuid)) {
                // 同步采购订单子表的已出入库数：InStoreQty
                this.matAccessCiteMapper.updateBuyOrderItemInStoreQty(orderitemid, orderuid, tid);
            }
        }

        //3生产入库
        if ("生产入库".equals(billtype) || "生产红冲".equals(billtype)) {
            if (!"".equals(citeitemid) && !"".equals(citeuid)) {
                this.matAccessCiteMapper.updateWkAcceFinish(citeitemid, citeuid, now, tid);
                Double remQty = this.matAccessCiteMapper.getWkRemQty(citeitemid, tid);
                if (remQty == null || remQty < 0) {
                    throw new RuntimeException(item.getGoodsuid() + ":超数" + billtype + remQty);
                }

                this.matAccessCiteMapper.updateWkAcceFinishCount(citeitemid, citeuid, tid);
            }


            // 订单上的入库数
            if (!"".equals(machitemid) && !"".equals(machuid)) {
                this.matAccessCiteMapper.updateMachInQty(machitemid, machuid, item.getGoodsid(), tid);
                this.matAccessCiteMapper.updateMachWkFinishCount(machitemid, machuid, tid);
            }
            // 更新过数记录的Acceid(/manu/getOnlinePageListByLastMark关闭过数)
            if (!"".equals(item.getWkqtyid()) && item.getWkqtyid() != null) {
                this.matAccessCiteMapper.updateWkQtyAcceid(item.getWkqtyid(), matAccessEntity.getId(), tid);
            }

        }


        //4加工入库
        if ("加工入库".equals(billtype) || "加工红冲".equals(billtype)) {
            if (!"".equals(citeitemid) && !"".equals(citeuid)) {
                this.matAccessCiteMapper.updateWsAcceFinish(citeitemid, citeuid, tid);
                Double remQty = this.matAccessCiteMapper.getWsRemQty(citeitemid, tid);
                if (remQty == null || remQty < 0) {
                    throw new RuntimeException(item.getGoodsuid() + ":超数" + billtype + remQty);
                }
                this.matAccessCiteMapper.updateWsAcceFinishCount(citeitemid, citeuid, tid);
            }

        }

        //5委制入库
        if ("委制入库".equals(billtype) || "委制红冲".equals(billtype)) {
            if (!"".equals(citeitemid) && !"".equals(citeuid)) {
                this.matAccessCiteMapper.updateWkScCompleteFinish(citeitemid, tid);
                Double remQty = this.matAccessCiteMapper.getWkScCompleteRemQty(citeitemid, tid);
                if (remQty == null || remQty < 0) {
                    throw new RuntimeException(item.getGoodsuid() + ":超数" + billtype + Math.abs(remQty));
                }
                this.matAccessCiteMapper.updateWkScCompleteFinishCount(citeitemid, citeuid, tid);
            }

        }


        //6客供入库
        if ("客供入库".equals(billtype) || "供退出库".equals(billtype) ||
                "客供红冲".equals(billtype) || "供退红冲".equals(billtype)) {
            if (!"".equals(citeitemid) && !"".equals(citeuid)) {
                this.matAccessCiteMapper.updateAcceFinish(citeitemid, citeuid, tid);
            }

        }

        //7领料出库
        if ("领料出库".equals(billtype) || "领料红冲".equals(billtype)) {
            if (!"".equals(citeitemid) && !"".equals(citeuid)) {
                this.matAccessCiteMapper.updateRequAcceFinish(citeitemid, citeuid, tid);
                if (InksConfigThreadLocal_Sa.getConfig("module.store.requoverflow") == null || "false".equals(InksConfigThreadLocal_Sa.getConfig("module.store.requoverflow"))) {
                    Double remQty = this.matAccessCiteMapper.getRequRemQty(citeitemid, tid);
                    if (remQty == null || remQty < 0) {
                        throw new RuntimeException(item.getGoodsuid() + ":超数" + billtype + remQty);
                    }
                }
                this.matAccessCiteMapper.updateRequFinishCount(citeitemid, citeuid, tid);
            }
        }
        //8退料入库
        if ("退料入库".equals(billtype) || "退料红冲".equals(billtype)) {
            if (!"".equals(citeitemid) && !"".equals(citeuid)) {
                this.matAccessCiteMapper.updateRequAcceFinish(citeitemid, citeuid, tid);
                Double remQty = this.matAccessCiteMapper.getRequRemQty(citeitemid, tid);
                if (remQty == null || remQty < 0) {
                    throw new RuntimeException(item.getGoodsuid() + ":超数" + billtype + remQty);
                }
                this.matAccessCiteMapper.updateRequFinishCount(citeitemid, citeuid, tid);
            }
        }

        //9盘点出入
        if ("盘盈入库".equals(billtype) || "盘盈红冲".equals(billtype) ||
                "盘亏出库".equals(billtype) || "盘亏红冲".equals(billtype)) {
            if (!"".equals(citeitemid) && !"".equals(citeuid)) {
                this.matAccessCiteMapper.updateNoteAcceFinish(citeitemid, citeuid, tid);
            }
        }
    }

    //    类型是领料出库/领料红冲,需要额外处理(不仅要更新货品数量，还要更新单据上的订单成本)
    private void syncRequGoodsAndCostAmt(List<MatAccessitemPojo> lst, String tid) {
        for (MatAccessitemPojo matAccessitemPojo : lst) {
            //领料出库/领料红冲需要额外处理(不仅要更新货品数量，还要更新单据上的订单成本)
            // 刷新领料单成本 SQL替代MQ
            if (isNotBlank(matAccessitemPojo.getCiteitemid())) {
                matAccessMapper.updateRequMatCostAmt(matAccessitemPojo.getCiteitemid(), tid);
            }
            // 刷新销售订单成本 SQL替代MQ
            if (isNotBlank(matAccessitemPojo.getMachitemid())) {
                matAccessMapper.updateMachMatCostAmt(matAccessitemPojo.getMachitemid(), tid);
            }
        }
    }

    // (少了一个类型领料出库: 当类型是领料出库需要额外处理,即调用syncRequGoodsAndCostAmt方法(不仅要更新货品数量，还要更新单据上的订单成本))
    // 更新关联单据  之前是MQ,现全部改为SQL替代MQ
    private void updateCiteGoodsQty(String billtype, String goodsid, String tid) {
        switch (billtype) {
            case "发货出库":
            case "客退入库":
            case "发货红冲":
            case "客退红冲":
                matGoodsService.updateGoodsBusRemQty(goodsid, tid);
                break;
            case "收货入库":
            case "购退出库":
            case "收货红冲":
            case "购退红冲":
                matGoodsService.updateGoodsBuyRemQty(goodsid, tid);
                break;
            case "生产入库":
            case "生产红冲":
                matGoodsService.updateGoodsWkWsRemQty(goodsid, tid);
                break;
            case "加工入库":
            case "加工红冲":
            case "委制入库":
                matGoodsService.updateGoodsWkScRemQty(goodsid, tid);
            case "委制红冲":
                break;
            case "退料入库":
            case "退料红冲":
            case "领料出库"://领料出库/领料红冲需要额外处理(不仅要更新货品数量，还要更新单据上的订单成本)
            case "领料红冲":
                matGoodsService.updateGoodsRequRemQty(goodsid, tid);
                break;
            case "盘盈入库":
            case "盘盈红冲":
            case "盘亏出库":
            case "盘亏红冲":// TODO 处理盘点出入的逻辑
                break;
            case "客供入库":
            case "供退出库":
            case "客供红冲":
            case "供退红冲":// TODO 处理客供入库的逻辑
                break;
            default:
                // 处理未知billtype的逻辑
                break;
        }
    }

    /**
     * 修改数据
     *
     * @param matAccessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatAccessPojo update(MatAccessPojo matAccessPojo) {
        //主表更改
        MatAccessEntity matAccessEntity = new MatAccessEntity();
        BeanUtils.copyProperties(matAccessPojo, matAccessEntity);
        if (matAccessPojo.getItem() != null) matAccessEntity.setItemcount(matAccessPojo.getItem().size());
        this.matAccessMapper.update(matAccessEntity);
        //Item子表处理
        List<MatAccessitemPojo> lst = matAccessPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = matAccessMapper.getDelItemIds(matAccessPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                this.matAccessitemMapper.delete(lstDelIds.get(i), matAccessEntity.getTenantid());
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                MatAccessitemEntity matAccessitemEntity = new MatAccessitemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    MatAccessitemPojo itemPojo = this.matAccessitemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, matAccessitemEntity);
                    //设置id和Pid
                    matAccessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    matAccessitemEntity.setPid(matAccessEntity.getId());  // 主表 id
                    matAccessitemEntity.setTenantid(matAccessPojo.getTenantid());   // 租户id
                    matAccessitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.matAccessitemMapper.insert(matAccessitemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), matAccessitemEntity);
                    matAccessitemEntity.setTenantid(matAccessPojo.getTenantid());
                    this.matAccessitemMapper.update(matAccessitemEntity);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(matAccessEntity.getId(), matAccessEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        MatAccessPojo matAccessPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<MatAccessitemPojo> lst = matAccessPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.matAccessitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.matAccessMapper.delete(key, tid);
    }

    /**
     * 新增数据
     *
     * @param matAccessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatAccessPojo insertRed(MatAccessPojo matAccessPojo, MatAccessEntity matAccessEntityOrg) {
        //MatAccessPojo初始化NULL字段
        cleanNull(matAccessPojo);
        // 出入转换
        if ("入库单".equals(matAccessPojo.getDirection())) {
            matAccessPojo.setDirection("出库单");
        } else {
            matAccessPojo.setDirection("入库单");
        }
        //类型转换
        switch (matAccessPojo.getBilltype()) {

            //生产出入库
            case "生产入库":
                matAccessPojo.setBilltype("生产红冲");
                break;
            case "加工入库":
                matAccessPojo.setBilltype("加工红冲");
                break;
            case "厂制入库":
                matAccessPojo.setBilltype("厂制红冲");
                break;
            case "委制入库":
                matAccessPojo.setBilltype("委制红冲");
                break;

            //物料领用出入库
            case "领料出库":
                matAccessPojo.setBilltype("领料红冲");
                break;
            case "退料入库":
                matAccessPojo.setBilltype("退料红冲");
                break;

            //销售出入库
            case "发货出库":
                matAccessPojo.setBilltype("发货红冲");
                break;
            case "客退入库":
                matAccessPojo.setBilltype("客退红冲");
                break;

            //采购出入库
            case "收货入库":  //20190302
                matAccessPojo.setBilltype("收货红冲");
                break;
            case "购退出库":  //20190302
                matAccessPojo.setBilltype("购退红冲");
                break;
            //其他出入库
            case "其他入库":   //改为 其他入库
                matAccessPojo.setBilltype("他入红冲");
                break;
            case "其他出库":
                matAccessPojo.setBilltype("他出红冲");
                break;

            case "报废出库":
                matAccessPojo.setBilltype("报废红冲");
                break;
            //盘点出入库
            case "盘盈入库":
                matAccessPojo.setBilltype("盘盈红冲");
                break;
            case "盘亏出库":
                matAccessPojo.setBilltype("盘亏红冲");
                break;
            //普及版出入库
            case "进货入库"://20190302
                matAccessPojo.setBilltype("进货红冲");
                break;
            case "销货出库"://20190302
                matAccessPojo.setBilltype("销货红冲");
                break;
        }
        MatAccessPojo redPojo = this.insert(matAccessPojo);
        //更新原表
        this.matAccessMapper.update(matAccessEntityOrg);
        //返回Bill实例
        return redPojo;
    }


//    /**
//     * Wip过数入库
//     *
//     * @return 实例对象
//     */
//    @Override
//    @Transactional
//    public MatAccessPojo createByWip(QuickWipqtyPojo quickWipqtyPojo) {
//
//        return null;
//    }


    /**
     * 新增数据
     *
     * @param matAccessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatAccessPojo fillInvoid(MatAccessPojo matAccessPojo) {
        //Item子表处理
        List<MatAccessitemPojo> lst = matAccessPojo.getItem();
        //循环每个item子表
        for (int i = 0; i < lst.size(); i++) {
//            // 货品批次管理属性
//            MatGoodsBatchAttrPojo markPojo = this.matPacksnService.getGoodsBatchAttr(lst.get(i).getGoodsid(), matAccessPojo.getTenantid());
            if ("".equals(lst.get(i).getInveid())) {
                MatInventoryPojo matInventoryPojo = new MatInventoryPojo();
                matInventoryPojo.setGoodsid(lst.get(i).getGoodsid());
                matInventoryPojo.setQuantity(lst.get(i).getQuantity());
                matInventoryPojo.setAmount(lst.get(i).getAmount());
                matInventoryPojo.setBatchno(lst.get(i).getBatchno() != null ? lst.get(i).getBatchno() : "");
                matInventoryPojo.setLocation(lst.get(i).getLocation() != null ? lst.get(i).getLocation() : "");
                matInventoryPojo.setPacksn(lst.get(i).getPacksn() != null ? lst.get(i).getPacksn() : "");
                matInventoryPojo.setSkuid(lst.get(i).getSkuid() != null ? lst.get(i).getSkuid() : "");
                matInventoryPojo.setAttributejson(lst.get(i).getAttributejson() != null ? lst.get(i).getAttributejson() : "");
                matInventoryPojo.setStoreid(matAccessPojo.getStoreid());
                matInventoryPojo.setTenantid(matAccessPojo.getTenantid());
//                if (markPojo.getSkumark() != null && markPojo.getSkumark() > 0 && !"".equals(matInventoryPojo.getAttributejson())) {
//                    // Sku库存
//                    MatSkuPojo matSkuPojo = this.matSkuService.getEntityByAtte(matInventoryPojo.getGoodsid(), matInventoryPojo.getAttributejson(), matAccessPojo.getTenantid());
//                    if (matSkuPojo != null) {
//                        lst.get(i).setSkuid(matSkuPojo.getId());
//                        matInventoryPojo.setSkuid(matSkuPojo.getId());
//                        matInventoryPojo = this.matInventoryService.getEntityBySku(matInventoryPojo);
//                    }
//                } else {
                // 标准查询
                matInventoryPojo = this.matInventoryService.getEntityBySn(matInventoryPojo);
//                }
                if (matInventoryPojo != null) {
                    // 可以查询库存
                    if (matInventoryPojo.getQuantity() - lst.get(i).getQuantity() < 0) {
                        throw new RuntimeException(lst.get(i).getGoodsuid() + ":库存数量不足" + matInventoryPojo.getQuantity() + "<" + lst.get(i).getQuantity());
                    }
                    lst.get(i).setInveid(matInventoryPojo.getId());
                } else {
                    throw new RuntimeException(lst.get(i).getGoodsuid() + ":未找到库存信息");
                }
            }
        }
        //返回Bill实例
        return matAccessPojo;
    }

    //刷新领料单成本
    @Override
    public int updateRequMatCostAmt(String key, String tid) {
        return this.matAccessMapper.updateRequMatCostAmt(key, tid);
    }

    //刷新销售订单成本
    @Override
    public int updateMachMatCostAmt(String key, String tid) {
        return this.matAccessMapper.updateMachMatCostAmt(key, tid);
    }


    /**
     * @return MatAccessPojo
     * @Description 保存初始化 检查出入库单,生成
     * <AUTHOR>
     * @param[1] matAccessPojo
     * @time 2023/7/8 15:08
     */
    public String saveInit(MatAccessPojo matAccessPojo) {
        LoginUser loginUser = saRedisService.getLoginUser();
        String tid = loginUser.getTenantid();
        //Item子表处理
        List<MatAccessitemPojo> lst = matAccessPojo.getItem();
        transactionTemplate.execute((status) -> {
//            //插入主表
//            this.matAccessMapper.insert(matAccessEntity);
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
//                    MatGoodsBatchAttrPojo markPojo = this.matPacksnService.getGoodsBatchAttr(lst.get(i).getGoodsid(), tid);
//                    if (markPojo == null) {
//                        throw new RuntimeException(lst.get(i).getGoodsuid() + ":货品信息丢失");
//                    }
                    // 入库单,初始化SN
                    if ("入库单".equals(matAccessPojo.getDirection())) {
//                        // 如果是SN包装
//                        if (markPojo.getPacksnmark() != null && markPojo.getPacksnmark() > 0) {
//                            MatPacksnPojo matPacksnPojo = new MatPacksnPojo();
//                            matPacksnPojo.setGoodsid(lst.get(i).getGoodsid());
//                            matPacksnPojo.setSubmark(0);
//                            matPacksnPojo.setTenantid(tid);
//                            matPacksnPojo.setLister(matAccessPojo.getLister());
//                            matPacksnPojo.setListerid(matAccessPojo.getListerid());
//                            matPacksnPojo.setCreateby(matAccessPojo.getCreateby());
//                            matPacksnPojo.setCreatebyid(matAccessPojo.getCreatebyid());
//                            matPacksnPojo.setCreatedate(new Date());
//                            matPacksnPojo.setModifydate(new Date());
//                            matPacksnPojo = this.matPacksnService.getNewPackSn(matPacksnPojo);
////                            matAccessitemEntity.setPacksn(matPacksnPojo.getPacksn());
//                            lst.get(i).setPacksn(matPacksnPojo.getPacksn());
//                        } else {
//                            matAccessitemEntity.setPacksn("");
                        lst.get(i).setPacksn("");
//                        }
//                        // 批次管理
//                        if (markPojo.getBatchmg() != null && markPojo.getBatchmg() > 0) {
//                            if (lst.get(i).getBatchno() == null || lst.get(i).getBatchno().length() == 0) {
//                                throw new RuntimeException(lst.get(i).getGoodsuid() + ":缺少批次信息");
//                            }
//                            // 是否独立批次
//                            if (markPojo.getBatchonly() != null && markPojo.getBatchonly() > 0) {
//                                MatInventoryPojo matInventoryPojo = new MatInventoryPojo();
//                                matInventoryPojo.setGoodsid(lst.get(i).getGoodsid());
//                                matInventoryPojo.setBatchno(lst.get(i).getBatchno() != null ? lst.get(i).getBatchno() : "");
//                                matInventoryPojo.setTenantid(tid);
//                                MatInventoryPojo matInvoOrgPojo = this.matInventoryService.getEntityByBatch(matInventoryPojo);
//                                if (matInvoOrgPojo != null && StringUtils.isBlank(matAccessPojo.getOrguid())) {//红冲单不校验批次号重复
//                                    throw new RuntimeException(lst.get(i).getGoodsuid() + ":批次号重复");
//                                }
//                            }
//                        }
//                        // Sku库存
//                        if (markPojo.getSkumark() != null && markPojo.getSkumark() > 0) {
//                            if (lst.get(i).getAttributejson() == null || lst.get(i).getAttributejson().length() == 0) {
//                                throw new RuntimeException(lst.get(i).getGoodsuid() + ":缺少SKU信息");
//                            }
//                            String AttrSkuJson = this.matSkuService.getAttrSku(lst.get(i).getAttributejson(), tid);
//                            MatSkuPojo matSkuPojo = this.matSkuService.getEntityByAtte(lst.get(i).getGoodsid(), AttrSkuJson, tid);
//                            lst.get(i).setAttributejson(AttrSkuJson);
//                            // item赋值SKUid
//                            if (matSkuPojo != null) {
//                                lst.get(i).setSkuid(matSkuPojo.getId());
//                            } else {
//                                matSkuPojo = new MatSkuPojo();
//                                matSkuPojo.setGoodsid(lst.get(i).getGoodsid());
//                                matSkuPojo.setGoodsuid(lst.get(i).getGoodsuid());
//                                matSkuPojo.setItemcode(lst.get(i).getGoodsuid());
//                                matSkuPojo.setItemname(lst.get(i).getGoodsname());
//                                matSkuPojo.setAttributejson(lst.get(i).getAttributejson());
//                                matSkuPojo.setTenantid(tid);
//                                matSkuPojo.setLister(matAccessPojo.getLister());
//                                matSkuPojo.setListerid(matAccessPojo.getListerid());
//                                matSkuPojo.setCreateby(matAccessPojo.getLister());
//                                matSkuPojo.setCreatebyid(matAccessPojo.getCreatebyid());
//                                matSkuPojo.setCreatedate(new Date());
//                                matSkuPojo.setModifydate(new Date());
//                                matSkuPojo.setRemark(matAccessPojo.getRefno() + " 时自动建立");
//                                matSkuPojo = this.matSkuService.insert(matSkuPojo);
//                                lst.get(i).setSkuid(matSkuPojo.getId());
//                            }
////                            matAccessitemEntity.setSkuid(lst.get(i).getId());
//                        }
                    }

                    if ("".equals(lst.get(i).getInveid())) {
                        // 没有指定库存id
                        MatInventoryPojo matInventoryPojo = new MatInventoryPojo();
                        matInventoryPojo.setGoodsid(lst.get(i).getGoodsid());
                        matInventoryPojo.setQuantity(lst.get(i).getQuantity());
                        matInventoryPojo.setAmount(lst.get(i).getAmount());
                        matInventoryPojo.setBatchno(lst.get(i).getBatchno() != null ? lst.get(i).getBatchno() : "");
                        matInventoryPojo.setLocation(lst.get(i).getLocation() != null ? lst.get(i).getLocation() : "");
                        matInventoryPojo.setPacksn(lst.get(i).getPacksn() != null ? lst.get(i).getPacksn() : "");
                        matInventoryPojo.setSkuid(lst.get(i).getSkuid() != null ? lst.get(i).getSkuid() : "");
                        matInventoryPojo.setAttributejson(lst.get(i).getAttributejson() != null ? lst.get(i).getAttributejson() : "");
                        matInventoryPojo.setStoreid(matAccessPojo.getStoreid());
                        matInventoryPojo.setTenantid(matAccessPojo.getTenantid());
                        matInventoryPojo.setLister(matAccessPojo.getLister());
                        matInventoryPojo.setCreateby(matAccessPojo.getLister());
                        matInventoryPojo.setCreatedate(new Date());
                        matInventoryPojo.setModifydate(new Date());
                        matInventoryPojo.setEndindate(new Date());
                        matInventoryPojo.setEndinuid(matAccessPojo.getRefno());
                        if ("入库单".equals(matAccessPojo.getDirection())) {
                            MatInventoryPojo matInvoOrgPojo = new MatInventoryPojo();
//                            // Sku库存
//                            if (markPojo.getSkumark() != null && markPojo.getSkumark() > 0) {
//                                MatSkuPojo matSkuPojo = this.matSkuService.getEntityByAtte(matInventoryPojo.getGoodsid(), matInventoryPojo.getAttributejson(), tid);
//                                if (matSkuPojo != null) {
//                                    lst.get(i).setSkuid(matSkuPojo.getId());
//                                    matInventoryPojo.setSkuid(matSkuPojo.getId());
//                                    matInvoOrgPojo = this.matInventoryService.getEntityBySku(matInventoryPojo);
//                                }
//                            } else {
                            matInvoOrgPojo = this.matInventoryService.getEntityBySn(matInventoryPojo);
//                            }

                            if (matInvoOrgPojo != null && matInvoOrgPojo.getId() != null) {
                                matInvoOrgPojo.setQuantity(matInvoOrgPojo.getQuantity() + lst.get(i).getQuantity());
                                matInvoOrgPojo.setAmount(matInvoOrgPojo.getAmount() + lst.get(i).getAmount());
                                matInvoOrgPojo.setEndindate(new Date());
                                matInvoOrgPojo.setEndinuid(matAccessPojo.getRefno());
                                matInvoOrgPojo.setLister(matAccessPojo.getLister());
                                matInvoOrgPojo.setModifydate(new Date());
                                this.matInventoryService.update(matInvoOrgPojo);
                            } else {
                                matInvoOrgPojo = this.matInventoryService.insert(matInventoryPojo);
                                lst.get(i).setInveid(matInvoOrgPojo.getId());
                            }
                        } else {
//                            // Sku库存
//                            if (markPojo.getSkumark() != null && markPojo.getSkumark() > 0 && !"".equals(matInventoryPojo.getAttributejson())) {
//                                String AttrSkuJson = this.matSkuService.getAttrSku(lst.get(i).getAttributejson(), tid);
//                                matInventoryPojo.setAttributejson(AttrSkuJson);
//                                MatSkuPojo matSkuPojo = this.matSkuService.getEntityByAtte(matInventoryPojo.getGoodsid(), matInventoryPojo.getAttributejson(), tid);
//                                if (matSkuPojo != null) {
//                                    lst.get(i).setSkuid(matSkuPojo.getId());
//                                    matInventoryPojo.setSkuid(matSkuPojo.getId());
//                                    matInventoryPojo = this.matInventoryService.getEntityBySku(matInventoryPojo);
//                                } else {
//                                    throw new RuntimeException(lst.get(i).getGoodsuid() + ":未找到SKU信息,确认是否有入库记录");
//                                }
//                            } else {
                            matInventoryPojo = this.matInventoryService.getEntityBySn(matInventoryPojo);
//                            }
                            if (matInventoryPojo != null) {
                                if (matInventoryPojo.getQuantity() - lst.get(i).getQuantity() < 0) {
                                    throw new RuntimeException(lst.get(i).getGoodsuid() + ":库存数量不足");
                                }
                                double ivPrice = 0;
                                if (matInventoryPojo.getQuantity() != 0) {
                                    ivPrice = matInventoryPojo.getAmount() / matInventoryPojo.getQuantity();
                                }
                                lst.get(i).setPrice(ivPrice);
                                lst.get(i).setAmount(lst.get(i).getQuantity() * ivPrice);
                                matInventoryPojo.setQuantity(matInventoryPojo.getQuantity() - lst.get(i).getQuantity());
                                matInventoryPojo.setAmount(matInventoryPojo.getQuantity() * ivPrice);
                                if (matInventoryPojo.getAmount() < 0) matInventoryPojo.setAmount(0D);
                                matInventoryPojo.setEndoutdate(new Date());
                                matInventoryPojo.setEndoutuid(matAccessPojo.getRefno());
                                matInventoryPojo.setLister(matAccessPojo.getLister());
                                matInventoryPojo.setModifydate(new Date());
                                this.matInventoryService.update(matInventoryPojo);
                            } else {
                                throw new RuntimeException(lst.get(i).getGoodsuid() + ":未找到库存信息");
                            }
                        }
                    } else {
                        // 有指库存id
                        MatInventoryPojo matInventoryPojo = this.matInventoryService.getEntity(lst.get(i).getInveid(), tid);
                        if (matInventoryPojo == null) {
                            throw new RuntimeException(lst.get(i).getGoodsuid() + ":库存数据丢失");
                        }
                        if ("入库单".equals(matAccessPojo.getDirection())) {
                            matInventoryPojo.setQuantity(matInventoryPojo.getQuantity() + lst.get(i).getQuantity());
                            matInventoryPojo.setAmount(matInventoryPojo.getAmount() + lst.get(i).getAmount());
                            matInventoryPojo.setEndindate(new Date());
                            matInventoryPojo.setEndinuid(matAccessPojo.getRefno());
                        } else {
                            if (matInventoryPojo.getQuantity() - lst.get(i).getQuantity() < 0) {
                                throw new RuntimeException(lst.get(i).getGoodsuid() + ":库存数量不足" + matInventoryPojo.getQuantity() + "<" + lst.get(i).getQuantity());
                            }
                            double ivPrice = 0;
                            if (matInventoryPojo.getQuantity() != 0) {
                                ivPrice = matInventoryPojo.getAmount() / matInventoryPojo.getQuantity();
                            }
                            lst.get(i).setPrice(ivPrice);
                            lst.get(i).setAmount(lst.get(i).getQuantity() * ivPrice);
                            matInventoryPojo.setQuantity(matInventoryPojo.getQuantity() - lst.get(i).getQuantity());
                            matInventoryPojo.setAmount(matInventoryPojo.getQuantity() * ivPrice);
                            if (matInventoryPojo.getAmount() < 0) matInventoryPojo.setAmount(0D);
                            matInventoryPojo.setEndoutdate(new Date());
                            matInventoryPojo.setEndoutuid(matAccessPojo.getRefno());
                        }
                        matInventoryPojo.setLister(matAccessPojo.getLister());
                        matInventoryPojo.setModifydate(new Date());
                        this.matInventoryService.update(matInventoryPojo);
                    }
//                    // 插入子表
//                    this.matAccessitemMapper.insert(matAccessitemEntity);
//                    // 同步关联单据
//                    this.updateCiteBill(matAccessEntity, lst.get(i), finalMapConfig);
                }
            }
            return Boolean.TRUE;
        });
        // 初始化成功后，序列化matAccessPojo后存入redis
        String redisKey = inksSnowflake.getSnowflake().nextIdStr();
        saRedisService.setCacheObject(redisKey, JSON.toJSONString(matAccessPojo), 30L, TimeUnit.SECONDS);
        return redisKey;
    }


    /**
     * @return MatAccessPojo
     * @Description 通过redisKey获取初始化后的MatAccess主子表并保存，并更新关联单据
     * <AUTHOR>
     * @param[1] redisKey
     * @time 2023/7/8 17:17
     */
    public MatAccessPojo saveOnly(String redisKey) {
        return null;
//        LoginUser loginUser = saRedisService.getLoginUser();
//        String tid = loginUser.getTenantid();
//        // 从redis中取出matAccessPojo
//        String matAccessJson = saRedisService.getCacheObject(redisKey, String.class);
//        MatAccessPojo matAccessPojo = JSON.parseObject(matAccessJson, MatAccessPojo.class);
//        //MatAccessPojo初始化NULL字段
//        cleanNull(matAccessPojo);
//        //生成id
//        String id = inksSnowflake.getSnowflake().nextIdStr();
//        MatAccessEntity matAccessEntity = new MatAccessEntity();
//        BeanUtils.copyProperties(matAccessPojo, matAccessEntity);
//        String billType = matAccessEntity.getBilltype();
//        //设置id和新建日期
//        matAccessEntity.setId(id);
//        matAccessEntity.setRevision(1);  //乐观锁
//        //Item子表处理
//        List<MatAccessitemPojo> lst = matAccessPojo.getItem();
//        transactionTemplate.execute((status) -> {
//            //插入主表
//            this.matAccessMapper.insert(matAccessEntity);
//            // 读取所有参数,改为 feign Eric 20230203
////            R<Map<String, String>> r = this.systemFeignService.getConfigTenAll(0, tid, loginUser.getToken());
//            Map<String, String> mapConfig = new HashMap<String, String>();
////            if (r.getCode() == 200) {
////                mapConfig = r.getData();
////            }
//            if (lst != null) {
//                //循环每个item子表
//                for (MatAccessitemPojo matAccessitemPojo : lst) {
//                    //初始化item的NULL
//                    MatAccessitemPojo itemPojo = this.matAccessitemService.clearNull(matAccessitemPojo);
//                    MatAccessitemEntity matAccessitemEntity = new MatAccessitemEntity();
//                    BeanUtils.copyProperties(itemPojo, matAccessitemEntity);
//                    //设置id和Pid
//                    matAccessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
//                    matAccessitemEntity.setPid(id);
//                    matAccessitemEntity.setTenantid(tid);
//                    matAccessitemEntity.setRevision(1);
//                    // 插入子表
//                    this.matAccessitemMapper.insert(matAccessitemEntity);
//                    // 同步关联单据
//                    this.updateCiteBill(matAccessEntity, matAccessitemPojo, mapConfig);//乐观锁
//                }
//            }
//            for (MatAccessitemPojo matAccessitemPojo : lst) {
//                // 同步货品数量 MQ生产者  nanno 20230222
////MqBaseParamPojo mqBaseParamPojo = new MqBaseParamPojo(lst.get(i).getGoodsid(), "IvQuantity", matAccessEntity.getTenantid());
////            this.rabbitTemplate.convertAndSend("updateGoodsQty", JSON.toJSONString(mqBaseParamPojo));
//                matGoodsService.updateGoodsIvQuantity(matAccessitemPojo.getGoodsid(), matAccessEntity.getTenantid());
//
//                // 同步关联单据
//                this.updateCiteGoodsQty(matAccessEntity, matAccessitemPojo);
//            }
//            if ("领料出库".equals(billType) || "领料红冲".equals(billType)) {
//                this.syncRequGoodsAndCostAmt(lst, tid);
//            }
//            return Boolean.TRUE;
//        });
//
//        //返回Bill实例
//        return this.getBillEntity(matAccessEntity.getId(), tid);
    }

    private static void cleanNull(MatAccessPojo matAccessPojo) {
        //初始化NULL字段
        if (matAccessPojo.getRefno() == null) matAccessPojo.setRefno("");
        if (matAccessPojo.getBilldate() == null) matAccessPojo.setBilldate(new Date());
        if (matAccessPojo.getProjectid() == null) matAccessPojo.setProjectid("");
        if (matAccessPojo.getProjcode() == null) matAccessPojo.setProjcode("");
        if (matAccessPojo.getProjname() == null) matAccessPojo.setProjname("");
        if (matAccessPojo.getTypecode() == null) matAccessPojo.setTypecode("");
        if (matAccessPojo.getBilltype() == null) matAccessPojo.setBilltype("");
        if (matAccessPojo.getBilltitle() == null) matAccessPojo.setBilltitle("");
        if (matAccessPojo.getDirection() == null) matAccessPojo.setDirection("");
        if (matAccessPojo.getGroupid() == null) matAccessPojo.setGroupid("");
        if (matAccessPojo.getStoreid() == null) matAccessPojo.setStoreid("");
        if (matAccessPojo.getOperator() == null) matAccessPojo.setOperator("");
        if (matAccessPojo.getSummary() == null) matAccessPojo.setSummary("");
        if (matAccessPojo.getReturnuid() == null) matAccessPojo.setReturnuid("");
        if (matAccessPojo.getOrguid() == null) matAccessPojo.setOrguid("");
        if (matAccessPojo.getPlusinfo() == null) matAccessPojo.setPlusinfo("");
        if (matAccessPojo.getLister() == null) matAccessPojo.setLister("");
        if (matAccessPojo.getCreatedate() == null) matAccessPojo.setCreatedate(new Date());
        if (matAccessPojo.getModifydate() == null) matAccessPojo.setModifydate(new Date());
        if (matAccessPojo.getBilltaxamount() == null) matAccessPojo.setBilltaxamount(0D);
        if (matAccessPojo.getBilltaxtotal() == null) matAccessPojo.setBilltaxtotal(0D);
        if (matAccessPojo.getBillamount() == null) matAccessPojo.setBillamount(0D);
        if (matAccessPojo.getFmdocmark() == null) matAccessPojo.setFmdocmark(0);
        if (matAccessPojo.getFmdoccode() == null) matAccessPojo.setFmdoccode("");
        if (matAccessPojo.getCustom1() == null) matAccessPojo.setCustom1("");
        if (matAccessPojo.getCustom2() == null) matAccessPojo.setCustom2("");
        if (matAccessPojo.getCustom3() == null) matAccessPojo.setCustom3("");
        if (matAccessPojo.getCustom4() == null) matAccessPojo.setCustom4("");
        if (matAccessPojo.getCustom5() == null) matAccessPojo.setCustom5("");
        if (matAccessPojo.getCustom6() == null) matAccessPojo.setCustom6("");
        if (matAccessPojo.getCustom7() == null) matAccessPojo.setCustom7("");
        if (matAccessPojo.getCustom8() == null) matAccessPojo.setCustom8("");
        if (matAccessPojo.getCustom9() == null) matAccessPojo.setCustom9("");
        if (matAccessPojo.getCustom10() == null) matAccessPojo.setCustom10("");
        if (matAccessPojo.getTenantid() == null) matAccessPojo.setTenantid("");
        if (matAccessPojo.getStorecode() == null) matAccessPojo.setStorecode("");
        if (matAccessPojo.getStorename() == null) matAccessPojo.setStorename("");
        if (matAccessPojo.getCreateby() == null) matAccessPojo.setCreateby("");
        if (matAccessPojo.getRevision() == null) matAccessPojo.setRevision(0);
        // 加入项目数
        if (matAccessPojo.getItem() != null) matAccessPojo.setItemcount(matAccessPojo.getItem().size());
    }

    @Override
    public void updatePrintcount(MatAccessPojo billPrintPojo) {
        this.matAccessMapper.updatePrintcount(billPrintPojo);
    }

    @Override
    public PageInfo<Map<String, Object>> getSumPageListByGoods(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<Map<String, Object>> lst = matAccessMapper.getSumPageListByGoods(queryParam);
            PageInfo<Map<String, Object>> pageInfo = new PageInfo<Map<String, Object>>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public Boolean quickInStoreByBuyFinishing(String finishid, String storeid, MatAccessPojo matAccessPojo) throws ParseException {
        String tid = matAccessPojo.getTenantid();
        // 查询采购验收单主子表Map,仓库Map
        Map<String, Object> finishingMap = matAccessMapper.getBuyFinishing(finishid, tid);
        List<Map<String, Object>> finishingItemList = matAccessMapper.getBuyFinishingItemList(finishid, tid);
        MatStoragePojo storagePojo = matStorageMapper.getEntity(storeid, tid);
        // 构建入库单主表
        matAccessPojo.setBillamount(Double.valueOf(finishingMap.get("BillAmount").toString()));
        matAccessPojo.setBilldate(new Date());
        matAccessPojo.setBilltaxamount(Double.valueOf(finishingMap.get("BillTaxAmount").toString()));
        matAccessPojo.setBilltaxtotal(Double.valueOf(finishingMap.get("BillTaxTotal").toString()));
        matAccessPojo.setBilltitle("[" + finishingMap.get("RefNo").toString() + "]采购验收快捷入库");
        matAccessPojo.setBilltype("收货入库");
        matAccessPojo.setDirection("入库单");
        matAccessPojo.setGroupid(finishingMap.get("Groupid").toString());
        matAccessPojo.setGroupname(finishingMap.get("GroupName").toString());
        matAccessPojo.setGroupuid(finishingMap.get("GroupUid").toString());
        matAccessPojo.setStoreid(storeid);
        matAccessPojo.setStorecode(storagePojo.getStorecode());
        matAccessPojo.setStorename(storagePojo.getStorename());
//        matAccessPojo.setPlusinfo(delieryMap.get("PlusInfo").toString());
//        matAccessPojo.setSummary();
//        matAccessPojo.setTypecode();
        // 构建入库单子表List
        List<MatAccessitemPojo> matAccessitemPojoList = new ArrayList<>();
        for (Map<String, Object> finishingItem : finishingItemList) {
            MatAccessitemPojo matAccessitemPojo = new MatAccessitemPojo();
            matAccessitemPojo.setAmount(Double.valueOf(finishingItem.get("Amount").toString()));
            matAccessitemPojo.setAttributejson(finishingItem.get("AttributeJson").toString());
            matAccessitemPojo.setBatchno(finishingItem.get("BatchNo").toString());
            matAccessitemPojo.setCiteitemid(finishingItem.get("id").toString());
            matAccessitemPojo.setCiteuid(finishingMap.get("RefNo").toString());
            matAccessitemPojo.setCustpo(finishingItem.get("CustPO").toString());
            matAccessitemPojo.setGoodsid(finishingItem.get("Goodsid").toString());
            Map<String, Object> goodsMap = matAccessMapper.getGoods(finishingItem.get("Goodsid").toString(), tid);
            matAccessitemPojo.setGoodsuid(goodsMap.get("GoodsUid").toString());
            matAccessitemPojo.setGoodsname(goodsMap.get("GoodsName").toString());
            matAccessitemPojo.setGoodsspec(goodsMap.get("GoodsSpec").toString());
            matAccessitemPojo.setGoodsunit(goodsMap.get("GoodsUnit").toString());
            matAccessitemPojo.setItemtaxrate(Integer.valueOf(finishingItem.get("ItemTaxrate").toString()));
            matAccessitemPojo.setMachitemid(finishingItem.get("MachItemid").toString());
            matAccessitemPojo.setMachuid(finishingItem.get("MachUid").toString());
            matAccessitemPojo.setPrice(Double.valueOf(finishingItem.get("Price").toString()));
            matAccessitemPojo.setQuantity(Double.valueOf(finishingItem.get("Quantity").toString()));
//            matAccessitemPojo.setCustomer(delieryItem.get("Customer").toString());
//            matAccessitemPojo.setInveid();
//            matAccessitemPojo.setLabelcodes();
//            matAccessitemPojo.setLabelqty();
//            matAccessitemPojo.setLocation();
//            matAccessitemPojo.setPacksn();
//            matAccessitemPojo.setPid();
//            matAccessitemPojo.setRemark("");
            matAccessitemPojoList.add(matAccessitemPojo);
        }
        // 构建入库单主子表 并clean空置并插入
        matAccessPojo.setItem(matAccessitemPojoList);
        cleanNull(matAccessPojo);
        this.insert(matAccessPojo);
        return true;
    }

    @Override
    public Boolean quickInStoreByMatRequisition(String requisitionid, String storeid, String billtitle, MatAccessPojo matAccessPojo) throws ParseException {
        return null;
    }
}
