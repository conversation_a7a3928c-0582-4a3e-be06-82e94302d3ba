package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatCombinPojo;
import inks.service.sa.som.domain.pojo.MatCombinitemdetailPojo;

/**
 * 拆装单(MatCombin)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-19 11:09:23
 */
public interface MatCombinService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCombinPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatCombinitemdetailPojo> getPageList(QueryParam queryParam);

 /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCombinPojo getBillEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatCombinPojo> getBillList(QueryParam queryParam);
    
        /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatCombinPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param matCombinPojo 实例对象
     * @return 实例对象
     */
    MatCombinPojo insert(MatCombinPojo matCombinPojo);

    /**
     * 修改数据
     *
     * @param matCombinpojo 实例对象
     * @return 实例对象
     */
    MatCombinPojo update(MatCombinPojo matCombinpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

                                                                                                                                                                     }
