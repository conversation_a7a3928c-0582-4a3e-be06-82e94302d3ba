package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyAccountitemEntity;
import inks.service.sa.som.domain.pojo.BuyAccountitemPojo;
import inks.service.sa.som.mapper.BuyAccountitemMapper;
import inks.service.sa.som.service.BuyAccountitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 账单明细(BuyAccountitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-01 12:57:30
 */
@Service("buyAccountitemService")
public class BuyAccountitemServiceImpl implements BuyAccountitemService {
    @Resource
    private BuyAccountitemMapper buyAccountitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyAccountitemPojo getEntity(String key,String tid) {
        return this.buyAccountitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyAccountitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyAccountitemPojo> lst = buyAccountitemMapper.getPageList(queryParam);
            PageInfo<BuyAccountitemPojo> pageInfo = new PageInfo<BuyAccountitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BuyAccountitemPojo> getList(String Pid,String tid) { 
        try {
            List<BuyAccountitemPojo> lst = buyAccountitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param buyAccountitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyAccountitemPojo insert(BuyAccountitemPojo buyAccountitemPojo) {
        //初始化item的NULL
        BuyAccountitemPojo itempojo =this.clearNull(buyAccountitemPojo);
        BuyAccountitemEntity buyAccountitemEntity = new BuyAccountitemEntity(); 
        BeanUtils.copyProperties(itempojo,buyAccountitemEntity);
        
          buyAccountitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          buyAccountitemEntity.setRevision(1);  //乐观锁      
          this.buyAccountitemMapper.insert(buyAccountitemEntity);
        return this.getEntity(buyAccountitemEntity.getId(),buyAccountitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param buyAccountitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyAccountitemPojo update(BuyAccountitemPojo buyAccountitemPojo) {
        BuyAccountitemEntity buyAccountitemEntity = new BuyAccountitemEntity(); 
        BeanUtils.copyProperties(buyAccountitemPojo,buyAccountitemEntity);
        this.buyAccountitemMapper.update(buyAccountitemEntity);
        return this.getEntity(buyAccountitemEntity.getId(),buyAccountitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.buyAccountitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param buyAccountitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BuyAccountitemPojo clearNull(BuyAccountitemPojo buyAccountitemPojo){
     //初始化NULL字段
     if(buyAccountitemPojo.getPid()==null) buyAccountitemPojo.setPid("");
     if(buyAccountitemPojo.getDirection()==null) buyAccountitemPojo.setDirection("");
     if(buyAccountitemPojo.getModulecode()==null) buyAccountitemPojo.setModulecode("");
     if(buyAccountitemPojo.getBilltype()==null) buyAccountitemPojo.setBilltype("");
     if(buyAccountitemPojo.getBilldate()==null) buyAccountitemPojo.setBilldate(new Date());
     if(buyAccountitemPojo.getBilltitle()==null) buyAccountitemPojo.setBilltitle("");
     if(buyAccountitemPojo.getBilluid()==null) buyAccountitemPojo.setBilluid("");
     if(buyAccountitemPojo.getBillid()==null) buyAccountitemPojo.setBillid("");
     if(buyAccountitemPojo.getOpenamount()==null) buyAccountitemPojo.setOpenamount(0D);
     if(buyAccountitemPojo.getInamount()==null) buyAccountitemPojo.setInamount(0D);
     if(buyAccountitemPojo.getOutamount()==null) buyAccountitemPojo.setOutamount(0D);
     if(buyAccountitemPojo.getCloseamount()==null) buyAccountitemPojo.setCloseamount(0D);
     if(buyAccountitemPojo.getRownum()==null) buyAccountitemPojo.setRownum(0);
     if(buyAccountitemPojo.getRemark()==null) buyAccountitemPojo.setRemark("");
     if(buyAccountitemPojo.getCustom1()==null) buyAccountitemPojo.setCustom1("");
     if(buyAccountitemPojo.getCustom2()==null) buyAccountitemPojo.setCustom2("");
     if(buyAccountitemPojo.getCustom3()==null) buyAccountitemPojo.setCustom3("");
     if(buyAccountitemPojo.getCustom4()==null) buyAccountitemPojo.setCustom4("");
     if(buyAccountitemPojo.getCustom5()==null) buyAccountitemPojo.setCustom5("");
     if(buyAccountitemPojo.getCustom6()==null) buyAccountitemPojo.setCustom6("");
     if(buyAccountitemPojo.getCustom7()==null) buyAccountitemPojo.setCustom7("");
     if(buyAccountitemPojo.getCustom8()==null) buyAccountitemPojo.setCustom8("");
     if(buyAccountitemPojo.getCustom9()==null) buyAccountitemPojo.setCustom9("");
     if(buyAccountitemPojo.getCustom10()==null) buyAccountitemPojo.setCustom10("");
     if(buyAccountitemPojo.getTenantid()==null) buyAccountitemPojo.setTenantid("");
     if(buyAccountitemPojo.getRevision()==null) buyAccountitemPojo.setRevision(0);
     return buyAccountitemPojo;
     }
}
