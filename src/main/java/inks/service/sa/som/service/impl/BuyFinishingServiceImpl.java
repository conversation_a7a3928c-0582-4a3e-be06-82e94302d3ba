package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.sa.som.domain.BuyFinishingEntity;
import inks.service.sa.som.domain.BuyFinishingitemEntity;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.*;
import inks.service.sa.som.service.BuyFinishingService;
import inks.service.sa.som.service.BuyFinishingitemService;
import inks.service.sa.som.service.MatGoodsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 采购验收(BuyFinishing)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 20:34:21
 */
@Service("buyFinishingService")
public class BuyFinishingServiceImpl implements BuyFinishingService {
    @Resource
    private BuyFinishingMapper buyFinishingMapper;

    @Resource
    private BuyFinishingitemMapper buyFinishingitemMapper;

    
    @Resource
    private BuyFinishingitemService buyFinishingitemService;

    @Resource
    private BuyOrderitemMapper buyOrderitemMapper;

    @Resource
    private BuyAccountrecMapper buyAccountrecMapper;

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private SyncMapper syncMapper;
    @Resource
    private BuyOrderMapper buyOrderMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyFinishingPojo getEntity(String key, String tid) {
        return this.buyFinishingMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyFinishingitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyFinishingitemdetailPojo> lst = buyFinishingMapper.getPageList(queryParam);
            PageInfo<BuyFinishingitemdetailPojo> pageInfo = new PageInfo<BuyFinishingitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyFinishingPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BuyFinishingPojo buyFinishingPojo = this.buyFinishingMapper.getEntity(key, tid);
            //读取子表
            buyFinishingPojo.setItem(buyFinishingitemMapper.getList(buyFinishingPojo.getId(), buyFinishingPojo.getTenantid()));
            return buyFinishingPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyFinishingPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyFinishingPojo> lst = buyFinishingMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(buyFinishingitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BuyFinishingPojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyFinishingPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyFinishingPojo> lst = buyFinishingMapper.getPageTh(queryParam);
            PageInfo<BuyFinishingPojo> pageInfo = new PageInfo<BuyFinishingPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param buyFinishingPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyFinishingPojo insert(BuyFinishingPojo buyFinishingPojo) {
//初始化NULL字段
        if (buyFinishingPojo.getRefno() == null) buyFinishingPojo.setRefno("");
        if (buyFinishingPojo.getBilltype() == null) buyFinishingPojo.setBilltype("");
        if (buyFinishingPojo.getBilltitle() == null) buyFinishingPojo.setBilltitle("");
        if (buyFinishingPojo.getBilldate() == null) buyFinishingPojo.setBilldate(new Date());
        if (buyFinishingPojo.getGroupid() == null) buyFinishingPojo.setGroupid("");
        if (buyFinishingPojo.getOperator() == null) buyFinishingPojo.setOperator("");
        if (buyFinishingPojo.getArrivaladd() == null) buyFinishingPojo.setArrivaladd("");
        if (buyFinishingPojo.getTransport() == null) buyFinishingPojo.setTransport("");
        if (buyFinishingPojo.getSummary() == null) buyFinishingPojo.setSummary("");
        if (buyFinishingPojo.getCreateby() == null) buyFinishingPojo.setCreateby("");
        if (buyFinishingPojo.getCreatebyid() == null) buyFinishingPojo.setCreatebyid("");
        if (buyFinishingPojo.getCreatedate() == null) buyFinishingPojo.setCreatedate(new Date());
        if (buyFinishingPojo.getLister() == null) buyFinishingPojo.setLister("");
        if (buyFinishingPojo.getListerid() == null) buyFinishingPojo.setListerid("");
        if (buyFinishingPojo.getModifydate() == null) buyFinishingPojo.setModifydate(new Date());
        if (buyFinishingPojo.getAssessor() == null) buyFinishingPojo.setAssessor("");
        if (buyFinishingPojo.getAssessorid() == null) buyFinishingPojo.setAssessorid("");
        if (buyFinishingPojo.getAssessdate() == null) buyFinishingPojo.setAssessdate(new Date());
        if (buyFinishingPojo.getBillstatecode() == null) buyFinishingPojo.setBillstatecode("");
        if (buyFinishingPojo.getBillstatedate() == null) buyFinishingPojo.setBillstatedate(new Date());
        if (buyFinishingPojo.getBilltaxamount() == null) buyFinishingPojo.setBilltaxamount(0D);
        if (buyFinishingPojo.getBillamount() == null) buyFinishingPojo.setBillamount(0D);
        if (buyFinishingPojo.getBilltaxtotal() == null) buyFinishingPojo.setBilltaxtotal(0D);
        if (buyFinishingPojo.getBillpaid() == null) buyFinishingPojo.setBillpaid(0D);
        if (buyFinishingPojo.getItemcount() == null) buyFinishingPojo.setItemcount(buyFinishingPojo.getItem().size());
        if (buyFinishingPojo.getFinishcount() == null) buyFinishingPojo.setFinishcount(0);
        if (buyFinishingPojo.getDisannulcount() == null) buyFinishingPojo.setDisannulcount(0);
        if (buyFinishingPojo.getInvocount() == null) buyFinishingPojo.setInvocount(0);
        if (buyFinishingPojo.getPrintcount() == null) buyFinishingPojo.setPrintcount(0);
        if (buyFinishingPojo.getOaflowmark() == null) buyFinishingPojo.setOaflowmark(0);
        if (buyFinishingPojo.getCustom1() == null) buyFinishingPojo.setCustom1("");
        if (buyFinishingPojo.getCustom2() == null) buyFinishingPojo.setCustom2("");
        if (buyFinishingPojo.getCustom3() == null) buyFinishingPojo.setCustom3("");
        if (buyFinishingPojo.getCustom4() == null) buyFinishingPojo.setCustom4("");
        if (buyFinishingPojo.getCustom5() == null) buyFinishingPojo.setCustom5("");
        if (buyFinishingPojo.getCustom6() == null) buyFinishingPojo.setCustom6("");
        if (buyFinishingPojo.getCustom7() == null) buyFinishingPojo.setCustom7("");
        if (buyFinishingPojo.getCustom8() == null) buyFinishingPojo.setCustom8("");
        if (buyFinishingPojo.getCustom9() == null) buyFinishingPojo.setCustom9("");
        if (buyFinishingPojo.getCustom10() == null) buyFinishingPojo.setCustom10("");
        if (buyFinishingPojo.getDeptid() == null) buyFinishingPojo.setDeptid("");
        String tid = buyFinishingPojo.getTenantid();
        if (tid == null) buyFinishingPojo.setTenantid("");
        if (buyFinishingPojo.getTenantname() == null) buyFinishingPojo.setTenantname("");
        if (buyFinishingPojo.getRevision() == null) buyFinishingPojo.setRevision(0);
        // 结账检查
        BuyAccountrecPojo buyAccountrecPojo = this.buyAccountrecMapper.getEntityByMax(tid);
        if (buyAccountrecPojo != null && DateUtils.getTimestamp(buyFinishingPojo.getBilldate()) <= DateUtils.getTimestamp(buyAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + buyAccountrecPojo.getEnddate() + ",禁止新建结账前单据");
        }

        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BuyFinishingEntity buyFinishingEntity = new BuyFinishingEntity();
        BeanUtils.copyProperties(buyFinishingPojo, buyFinishingEntity);
        //设置id和新建日期
        buyFinishingEntity.setId(id);
        buyFinishingEntity.setRevision(1);  //乐观锁
        //Item子表处理
        List<BuyFinishingitemPojo> lst = buyFinishingPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                // 同步采购订单
                if (lst.get(i).getOrderitemid() != null && !"".equals(lst.get(i).getOrderitemid())) {
                    // 超数检查
                    BuyOrderitemPojo buyOrderitemPojo = this.buyOrderitemMapper.getEntity(lst.get(i).getOrderitemid(), tid);
                    if (buyOrderitemPojo != null) {
                        Double quantity = "采购退货".equals(buyFinishingPojo.getBilltype()) ? -lst.get(i).getQuantity() : lst.get(i).getQuantity();
                        if (buyOrderitemPojo.getFinishqty() + quantity > buyOrderitemPojo.getMaxqty()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,收货总数:" + (buyOrderitemPojo.getFinishqty() + lst.get(i).getQuantity()) + "超出允收最大数:" + buyOrderitemPojo.getMaxqty());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getOrderuid());
                    }
                }
            }
        }

        // 设置主表.未税金额,税额,含税金额=子表累加
        double billTaxAmount = 0D;
        double billAmount = 0D;
        double billTaxTotal = 0D;
        assert lst != null;
        for (BuyFinishingitemPojo item : lst) {
            billTaxAmount += Optional.ofNullable(item.getTaxamount()).orElse(0.0);
            billTaxTotal += Optional.ofNullable(item.getTaxtotal()).orElse(0.0);
            billAmount += Optional.ofNullable(item.getAmount()).orElse(0.0);
        }
        buyFinishingEntity.setBilltaxamount(billTaxAmount);
        buyFinishingEntity.setBilltaxtotal(billTaxTotal);
        buyFinishingEntity.setBillamount(billAmount);
        transactionTemplate.execute((status) -> {
            //插入主表
            this.buyFinishingMapper.insert(buyFinishingEntity);
            //循环每个item子表
            for (BuyFinishingitemPojo buyFinishingitemPojo : lst) {
                //初始化item的NULL
                BuyFinishingitemPojo itemPojo = this.buyFinishingitemService.clearNull(buyFinishingitemPojo);
                BuyFinishingitemEntity buyFinishingitemEntity = new BuyFinishingitemEntity();
                BeanUtils.copyProperties(itemPojo, buyFinishingitemEntity);
                //设置id和Pid
                buyFinishingitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                buyFinishingitemEntity.setPid(id);
                buyFinishingitemEntity.setTenantid(tid);
                buyFinishingitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.buyFinishingitemMapper.insert(buyFinishingitemEntity);
                // 同步采购订单
                String orderitemid = buyFinishingitemEntity.getOrderitemid();
                if (StringUtils.isNotBlank(orderitemid)) {
                    this.buyFinishingMapper.updateOrderFinishQty(orderitemid, buyFinishingitemEntity.getOrderuid(), tid);
                    this.buyFinishingMapper.updateOrderFinishCount(orderitemid, buyFinishingitemPojo.getOrderuid(), tid);
                }
            }
//同步采购计划Buy_PlanItem.FinishQty
//同步条件：根据Buy_FinishingItem表的OrderItemid采购Itemid字段，找到采购订单明细，根据采购订单billtype类型等于采购计划，根据Buy_OrderItem表的CiteItemid字段同步采购计划单
            //同步采购计划Buy_PlanItem.FinishQty 拿到所有item的orderitemid集合,留下billtype类型等于采购计划的orderitemid集合,再根据orderitemid集合查询planitemid集合,更新Buy_PlanItem.FinishQty
            List<String> orderitemidLst = lst.stream().map(BuyFinishingitemPojo::getOrderitemid).distinct().collect(Collectors.toList());
            //根据orderitemid集合查询采购计划itemid集合
            List<String> planitemidLst = this.buyFinishingMapper.getPlanitemidsByOrderitemids(orderitemidLst, tid);
            for (String planitemid : planitemidLst) {
                this.buyOrderMapper.updatePlanItemBuyQtyAndFinishQty(planitemid, tid);
                this.buyOrderMapper.updatePlanBuyCountAndFinishCount(planitemid, tid);
            }
            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BuyFinishingitemPojo::getGoodsid).collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                syncMapper.updateGoodsBuyRemQty(goodsid, tid);
            });
            return Boolean.TRUE;
        });

        //返回Bill实例
        return this.getBillEntity(buyFinishingEntity.getId(), buyFinishingEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param buyFinishingPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyFinishingPojo update(BuyFinishingPojo buyFinishingPojo) {
        String tid = buyFinishingPojo.getTenantid();
        // 检查结账
        BuyAccountrecPojo buyAccountrecPojo = this.buyAccountrecMapper.getEntityByMax(tid);
        if (buyAccountrecPojo != null && DateUtils.getTimestamp(buyFinishingPojo.getBilldate()) <= DateUtils.getTimestamp(buyAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + buyAccountrecPojo.getEnddate() + ",禁止修改结账前单据");
        }
        BuyFinishingPojo orgPojo = this.buyFinishingMapper.getEntity(buyFinishingPojo.getId(), tid);
        if (buyAccountrecPojo != null && DateUtils.getTimestamp(orgPojo.getBilldate()) <= DateUtils.getTimestamp(buyAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + buyAccountrecPojo.getEnddate() + ",禁止修改结账前单据");
        }
        //主表更改
        buyFinishingPojo.setItemcount(buyFinishingPojo.getItem().size());
        BuyFinishingEntity buyFinishingEntity = new BuyFinishingEntity();
        BeanUtils.copyProperties(buyFinishingPojo, buyFinishingEntity);
        //Item子表处理
        List<BuyFinishingitemPojo> lst = buyFinishingPojo.getItem();
        List<BuyFinishingitemPojo> lstDel = new ArrayList<>();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                // 同步采购订单
                if (lst.get(i).getOrderitemid() != null && !"".equals(lst.get(i).getOrderitemid())) {
                    Double orgQty = 0D;
                    if (lst.get(i).getId() != null && !"".equals(lst.get(i).getId())) {
                        BuyFinishingitemPojo buyFinishingitemPojo = this.buyFinishingitemMapper.getEntity(lst.get(i).getId(), tid);
                        if (buyFinishingitemPojo != null) {
                            orgQty = buyFinishingitemPojo.getQuantity();
                        }
                    }
                    // 超数检查
                    BuyOrderitemPojo buyOrderitemPojo = this.buyOrderitemMapper.getEntity(lst.get(i).getOrderitemid(), tid);
                    if (buyOrderitemPojo != null) {
                        double quantity = "采购退货".equals(buyFinishingPojo.getBilltype()) ? -lst.get(i).getQuantity() : lst.get(i).getQuantity();
                        if (buyOrderitemPojo.getFinishqty() - orgQty + quantity > buyOrderitemPojo.getMaxqty()) {
                            int Rowno = i + 1;
                            throw new RuntimeException(Rowno + "行,收货总数:" + (buyOrderitemPojo.getFinishqty() - orgQty + lst.get(i).getQuantity()) + "超出允收最大数:" + buyOrderitemPojo.getMaxqty());
                        }
                    } else {
                        throw new RuntimeException("关联单据丢失:" + lst.get(i).getOrderuid());
                    }
                }
            }
        }

        // 设置主表.未税金额,税额,含税金额=子表累加
        double billTaxAmount = 0D;
        double billAmount = 0D;
        double billTaxTotal = 0D;
        assert lst != null;
        for (BuyFinishingitemPojo item : lst) {
            billTaxAmount += item.getTaxamount();
            billTaxTotal += item.getTaxtotal();
            billAmount += item.getAmount();
        }
        buyFinishingEntity.setBilltaxamount(billTaxAmount);
        buyFinishingEntity.setBilltaxtotal(billTaxTotal);
        buyFinishingEntity.setBillamount(billAmount);
        transactionTemplate.execute((status) -> {
            this.buyFinishingMapper.update(buyFinishingEntity);
            if (buyFinishingPojo.getItem() != null) {
                //获取被删除的Item
                List<String> lstDelIds = buyFinishingMapper.getDelItemIds(buyFinishingPojo);
                if (lstDelIds != null) {
                    //循环每个删除item子表
                    for (String lstDelId : lstDelIds) {
                        BuyFinishingitemPojo delPojo = this.buyFinishingitemMapper.getEntity(lstDelId, buyFinishingEntity.getTenantid());
                        lstDel.add(delPojo);
                        // 加上引用检查
                        List<String> lstcite = getItemCiteBillName(delPojo.getId(), delPojo.getPid(), delPojo.getTenantid());
                        if (!lstcite.isEmpty()) {
                            throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                        }
                        this.buyFinishingitemMapper.delete(lstDelId, buyFinishingEntity.getTenantid());
                        // 同步采购订单
                        if (delPojo.getOrderitemid() != null && !"".equals(delPojo.getOrderitemid())) {
                            this.buyFinishingMapper.updateOrderFinishQty(delPojo.getOrderitemid(), delPojo.getOrderuid(), tid);
                        }
                    }
                }
                //循环每个item子表
                for (BuyFinishingitemPojo buyFinishingitemPojo : lst) {
                    BuyFinishingitemEntity buyFinishingitemEntity = new BuyFinishingitemEntity();
                    if ("".equals(buyFinishingitemPojo.getId()) || buyFinishingitemPojo.getId() == null) {
                        //初始化item的NULL
                        BuyFinishingitemPojo itemPojo = this.buyFinishingitemService.clearNull(buyFinishingitemPojo);
                        BeanUtils.copyProperties(itemPojo, buyFinishingitemEntity);
                        //设置id和Pid
                        buyFinishingitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        buyFinishingitemEntity.setPid(buyFinishingEntity.getId());  // 主表 id
                        buyFinishingitemEntity.setTenantid(tid);   // 租户id
                        buyFinishingitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.buyFinishingitemMapper.insert(buyFinishingitemEntity);
                    } else {
                        BeanUtils.copyProperties(buyFinishingitemPojo, buyFinishingitemEntity);
                        buyFinishingitemEntity.setTenantid(tid);
                        this.buyFinishingitemMapper.update(buyFinishingitemEntity);
                    }
                    // 同步采购订单
                    if (buyFinishingitemPojo.getOrderitemid() != null && !"".equals(buyFinishingitemPojo.getOrderitemid())) {
                        this.buyFinishingMapper.updateOrderFinishQty(buyFinishingitemEntity.getOrderitemid(), buyFinishingitemEntity.getOrderuid(), tid);
                    }
                }
            }
            if (!lstDel.isEmpty()) {
                for (BuyFinishingitemPojo buyFinishingitemPojo : lstDel) {
                    // 同步采购订单
                    if (buyFinishingitemPojo.getOrderitemid() != null && !"".equals(buyFinishingitemPojo.getOrderitemid())) {
                        this.buyFinishingMapper.updateOrderFinishCount(buyFinishingitemPojo.getOrderitemid(), buyFinishingitemPojo.getOrderuid(), tid);
                    }
                }
            }
            for (BuyFinishingitemPojo buyFinishingitemPojo : lst) {
                // 同步采购订单
                if (buyFinishingitemPojo.getOrderitemid() != null && !"".equals(buyFinishingitemPojo.getOrderitemid())) {
                    this.buyFinishingMapper.updateOrderFinishCount(buyFinishingitemPojo.getOrderitemid(), buyFinishingitemPojo.getOrderuid(), tid);
                }
            }

            //同步采购计划Buy_PlanItem.FinishQty 拿到所有item的orderitemid集合,留下billtype类型等于采购计划的orderitemid集合,再根据orderitemid集合查询planitemid集合,更新Buy_PlanItem.FinishQty
            List<String> orderitemidLst = lst.stream().map(BuyFinishingitemPojo::getOrderitemid).distinct().collect(Collectors.toList());
            List<String> orderitemidLstDel = lstDel.stream().map(BuyFinishingitemPojo::getOrderitemid).distinct().collect(Collectors.toList());
            orderitemidLst.addAll(orderitemidLstDel);
            //根据orderitemid集合查询采购计划itemid集合
            List<String> planitemidLst = this.buyFinishingMapper.getPlanitemidsByOrderitemids(orderitemidLst, tid);
            for (String planitemid : planitemidLst) {
                this.buyOrderMapper.updatePlanItemBuyQtyAndFinishQty(planitemid, tid);
                this.buyOrderMapper.updatePlanBuyCountAndFinishCount(planitemid, tid);
            }


            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BuyFinishingitemPojo::getGoodsid).collect(Collectors.toSet());
            Set<String> goodsidLstDelSet = lstDel.stream().map(BuyFinishingitemPojo::getGoodsid).collect(Collectors.toSet());
            goodsidLstSet.addAll(goodsidLstDelSet);
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                syncMapper.updateGoodsBuyRemQty(goodsid, tid);
            });
            return Boolean.TRUE;
        });

        //返回Bill实例
        return this.getBillEntity(buyFinishingEntity.getId(), buyFinishingEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public String delete(String key, String tid) {
        BuyFinishingPojo buyFinishingPojo = this.getBillEntity(key, tid);
        // 检查结账
        BuyAccountrecPojo busAccountrecPojo = this.buyAccountrecMapper.getEntityByMax(buyFinishingPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(buyFinishingPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getEnddate() + ",禁止删除结账前单据");
        }
        //Item子表处理
        List<BuyFinishingitemPojo> lst = buyFinishingPojo.getItem();
        transactionTemplate.execute((status) -> {
            if (lst != null) {
                //循环每个删除item子表
                for (BuyFinishingitemPojo buyFinishingitemPojo : lst) {
                    // 加上引用检查
                    List<String> lstcite = getItemCiteBillName(buyFinishingitemPojo.getId(), buyFinishingitemPojo.getPid(), tid);
                    if (!lstcite.isEmpty()) {
                        throw new RuntimeException("删除行已被引用,禁止删除:" + lstcite);
                    }
                    this.buyFinishingitemMapper.delete(buyFinishingitemPojo.getId(), tid);
                    // 同步采购订单
                    if (buyFinishingitemPojo.getOrderitemid() != null && !"".equals(buyFinishingitemPojo.getOrderitemid())) {
                        this.buyFinishingMapper.updateOrderFinishQty(buyFinishingitemPojo.getOrderitemid(), buyFinishingitemPojo.getOrderuid(), buyFinishingPojo.getTenantid());
                    }
                }
            }
            this.buyFinishingMapper.delete(key, tid);
            if (lst != null) {
                //循环每个删除item子表
                for (BuyFinishingitemPojo buyFinishingitemPojo : lst) {
                    // 同步采购订单
                    if (buyFinishingitemPojo.getOrderitemid() != null && !"".equals(buyFinishingitemPojo.getOrderitemid())) {
                        this.buyFinishingMapper.updateOrderFinishCount(buyFinishingitemPojo.getOrderitemid(), buyFinishingitemPojo.getOrderuid(), buyFinishingPojo.getTenantid());
                    }
                }
            }

            //同步采购计划Buy_PlanItem.FinishQty 拿到所有item的orderitemid集合,留下billtype类型等于采购计划的orderitemid集合,再根据orderitemid集合查询planitemid集合,更新Buy_PlanItem.FinishQty
            List<String> orderitemidLst = lst.stream().map(BuyFinishingitemPojo::getOrderitemid).distinct().collect(Collectors.toList());
            //根据orderitemid集合查询采购计划itemid集合
            List<String> planitemidLst = this.buyFinishingMapper.getPlanitemidsByOrderitemids(orderitemidLst, tid);
            for (String planitemid : planitemidLst) {
                this.buyOrderMapper.updatePlanItemBuyQtyAndFinishQty(planitemid, tid);
                this.buyOrderMapper.updatePlanBuyCountAndFinishCount(planitemid, tid);
            }


            //--> lst.stream()拿到去重的goodsid Set集合
            Set<String> goodsidLstSet = lst.stream().map(BuyFinishingitemPojo::getGoodsid).collect(Collectors.toSet());
            // 同步货品数量 SQL替代MQ
            goodsidLstSet.forEach(goodsid -> {
                syncMapper.updateGoodsBuyRemQty(goodsid, tid);
            });
            return Boolean.TRUE;
        });


        return buyFinishingPojo.getRefno();
    }


    /**
     * 审核数据
     *
     * @param buyFinishingPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyFinishingPojo approval(BuyFinishingPojo buyFinishingPojo) {
        //主表更改
        BuyFinishingEntity buyFinishingEntity = new BuyFinishingEntity();
        BeanUtils.copyProperties(buyFinishingPojo, buyFinishingEntity);
        this.buyFinishingMapper.approval(buyFinishingEntity);
        //返回Bill实例
        return this.getBillEntity(buyFinishingEntity.getId(), buyFinishingEntity.getTenantid());
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyFinishingPojo disannul(List<BuyFinishingitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            BuyFinishingitemPojo Pojo = lst.get(i);
            BuyFinishingitemPojo dbPojo = this.buyFinishingitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已关闭,禁止作废操作");
                    }
                    if (dbPojo.getFinishqty() > 0) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已被引用,禁止作废操作");
                    }
                    BuyFinishingitemEntity entity = new BuyFinishingitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    entity.setDisannuldate(new Date());
                    entity.setDisannullister(loginUser.getRealname());
                    entity.setDisannullisterid(loginUser.getUserid());
                    entity.setTenantid(loginUser.getTenantid());
                    this.buyFinishingitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BuyFinishingitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            syncMapper.updateGoodsBuyRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.buyFinishingMapper.updateDisannulCountAndAmount(Pid, tid);
            //主表更改
            BuyFinishingEntity buyFinishingEntity = new BuyFinishingEntity();
            buyFinishingEntity.setId(Pid);
            buyFinishingEntity.setLister(loginUser.getRealname());
            buyFinishingEntity.setListerid(loginUser.getUserid());
            buyFinishingEntity.setModifydate(new Date());
            buyFinishingEntity.setTenantid(loginUser.getTenantid());
            this.buyFinishingMapper.update(buyFinishingEntity);
            //返回Bill实例
            return this.getBillEntity(buyFinishingEntity.getId(), buyFinishingEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyFinishingPojo closed(List<BuyFinishingitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            BuyFinishingitemPojo Pojo = lst.get(i);
            BuyFinishingitemPojo dbPojo = this.buyFinishingitemMapper.getEntity(Pojo.getId(), tid);
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已作废,禁止操作");
                    }
                    BuyFinishingitemEntity entity = new BuyFinishingitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    entity.setTenantid(loginUser.getTenantid());
                    this.buyFinishingitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行," + dbPojo.getGoodsname() + "已" + strType + ",无需操作");
                }
            }
        }

        //--> lst.stream()拿到去重的goodsid Set集合
        Set<String> goodsidLstSet = lst.stream().map(BuyFinishingitemPojo::getGoodsid).collect(Collectors.toSet());
        // 同步货品数量 SQL替代MQ
        goodsidLstSet.forEach(goodsid -> {
            syncMapper.updateGoodsBuyRemQty(goodsid, tid);
        });

        if (disNum > 0) {
            this.buyFinishingMapper.updateFinishCount(Pid, tid);
            //主表更改
            BuyFinishingEntity buyFinishingEntity = new BuyFinishingEntity();
            buyFinishingEntity.setId(Pid);
            buyFinishingEntity.setLister(loginUser.getRealname());
            buyFinishingEntity.setListerid(loginUser.getUserid());
            buyFinishingEntity.setModifydate(new Date());
            buyFinishingEntity.setTenantid(loginUser.getTenantid());
            this.buyFinishingMapper.update(buyFinishingEntity);
            //返回Bill实例
            return this.getBillEntity(buyFinishingEntity.getId(), buyFinishingEntity.getTenantid());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    // 查询Item是否被引用
    public List<String> getItemCiteBillName(String key, String pid, String tid) {
        return this.buyFinishingMapper.getItemCiteBillName(key, pid, tid);
    }

    @Override
    public void updatePrintcount(BuyFinishingPojo billPrintPojo) {
        this.buyFinishingMapper.updatePrintcount(billPrintPojo);
    }
}
