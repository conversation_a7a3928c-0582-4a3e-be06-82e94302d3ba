package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.sa.som.domain.BuyPrepaymentsEntity;
import inks.service.sa.som.domain.BuyPrepaymentscashEntity;
import inks.service.sa.som.domain.BuyPrepaymentsitemEntity;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.*;
import inks.service.sa.som.service.BuyPrepaymentsService;
import inks.service.sa.som.service.BuyPrepaymentscashService;
import inks.service.sa.som.service.BuyPrepaymentsitemService;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

/**
 * 预付款(BuyPrepayments)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-15 15:02:14
 */
@Service("buyPrepaymentsService")
public class BuyPrepaymentsServiceImpl implements BuyPrepaymentsService {
    @Resource
    private BuyPrepaymentsMapper buyPrepaymentsMapper;

    @Resource
    private BuyPrepaymentsitemMapper buyPrepaymentsitemMapper;

    
    @Resource
    private BuyPrepaymentsitemService buyPrepaymentsitemService;

    @Resource
    private BuyPrepaymentscashMapper buyPrepaymentscashMapper;

    
    @Resource
    private BuyPrepaymentscashService buyPrepaymentscashService;

    @Resource
    private BuyAccountrecMapper buyAccountrecMapper;
    @Resource
    private BuyVoucherMapper buyVoucherMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyPrepaymentsPojo getEntity(String key, String tid) {
        return this.buyPrepaymentsMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyPrepaymentsitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyPrepaymentsitemdetailPojo> lst = buyPrepaymentsMapper.getPageList(queryParam);
            PageInfo<BuyPrepaymentsitemdetailPojo> pageInfo = new PageInfo<BuyPrepaymentsitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyPrepaymentsPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BuyPrepaymentsPojo buyPrepaymentsPojo = this.buyPrepaymentsMapper.getEntity(key, tid);
            //读取子表
            buyPrepaymentsPojo.setItem(buyPrepaymentsitemMapper.getList(buyPrepaymentsPojo.getId(), buyPrepaymentsPojo.getTenantid()));
            //读取Cash
            buyPrepaymentsPojo.setCash(buyPrepaymentscashMapper.getList(buyPrepaymentsPojo.getId(), buyPrepaymentsPojo.getTenantid()));
            return buyPrepaymentsPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyPrepaymentsPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyPrepaymentsPojo> lst = buyPrepaymentsMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(buyPrepaymentsitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setCash(buyPrepaymentscashMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BuyPrepaymentsPojo> pageInfo = new PageInfo<BuyPrepaymentsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyPrepaymentsPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyPrepaymentsPojo> lst = buyPrepaymentsMapper.getPageTh(queryParam);
            PageInfo<BuyPrepaymentsPojo> pageInfo = new PageInfo<BuyPrepaymentsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param buyPrepaymentsPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyPrepaymentsPojo insert(BuyPrepaymentsPojo buyPrepaymentsPojo) {
        String tid = buyPrepaymentsPojo.getTenantid();
//初始化NULL字段
        if (buyPrepaymentsPojo.getRefno() == null) buyPrepaymentsPojo.setRefno("");
        if (buyPrepaymentsPojo.getBilltype() == null) buyPrepaymentsPojo.setBilltype("");
        if (buyPrepaymentsPojo.getBilltitle() == null) buyPrepaymentsPojo.setBilltitle("");
        if (buyPrepaymentsPojo.getBilldate() == null) buyPrepaymentsPojo.setBilldate(new Date());
        if (buyPrepaymentsPojo.getProjectid() == null) buyPrepaymentsPojo.setProjectid("");
        if (buyPrepaymentsPojo.getProjcode() == null) buyPrepaymentsPojo.setProjcode("");
        if (buyPrepaymentsPojo.getProjname() == null) buyPrepaymentsPojo.setProjname("");
        if (buyPrepaymentsPojo.getGroupid() == null) buyPrepaymentsPojo.setGroupid("");
        if (buyPrepaymentsPojo.getBillamount() == null) buyPrepaymentsPojo.setBillamount(0D);
        if (buyPrepaymentsPojo.getOperator() == null) buyPrepaymentsPojo.setOperator("");
        if (buyPrepaymentsPojo.getCitecode() == null) buyPrepaymentsPojo.setCitecode("");
        if (buyPrepaymentsPojo.getOutamount() == null) buyPrepaymentsPojo.setOutamount(0D);
        if (buyPrepaymentsPojo.getReturnuid() == null) buyPrepaymentsPojo.setReturnuid("");
        if (buyPrepaymentsPojo.getOrguid() == null) buyPrepaymentsPojo.setOrguid("");
        if (buyPrepaymentsPojo.getSummary() == null) buyPrepaymentsPojo.setSummary("");
        if (buyPrepaymentsPojo.getCreateby() == null) buyPrepaymentsPojo.setCreateby("");
        if (buyPrepaymentsPojo.getCreatebyid() == null) buyPrepaymentsPojo.setCreatebyid("");
        if (buyPrepaymentsPojo.getCreatedate() == null) buyPrepaymentsPojo.setCreatedate(new Date());
        if (buyPrepaymentsPojo.getLister() == null) buyPrepaymentsPojo.setLister("");
        if (buyPrepaymentsPojo.getListerid() == null) buyPrepaymentsPojo.setListerid("");
        if (buyPrepaymentsPojo.getModifydate() == null) buyPrepaymentsPojo.setModifydate(new Date());
        if (buyPrepaymentsPojo.getAssessor() == null) buyPrepaymentsPojo.setAssessor("");
        if (buyPrepaymentsPojo.getAssessorid() == null) buyPrepaymentsPojo.setAssessorid("");
        if (buyPrepaymentsPojo.getAssessdate() == null) buyPrepaymentsPojo.setAssessdate(new Date());
        if (buyPrepaymentsPojo.getFmdocmark() == null) buyPrepaymentsPojo.setFmdocmark(0);
        if (buyPrepaymentsPojo.getFmdoccode() == null) buyPrepaymentsPojo.setFmdoccode("");
        if (buyPrepaymentsPojo.getCustom1() == null) buyPrepaymentsPojo.setCustom1("");
        if (buyPrepaymentsPojo.getCustom2() == null) buyPrepaymentsPojo.setCustom2("");
        if (buyPrepaymentsPojo.getCustom3() == null) buyPrepaymentsPojo.setCustom3("");
        if (buyPrepaymentsPojo.getCustom4() == null) buyPrepaymentsPojo.setCustom4("");
        if (buyPrepaymentsPojo.getCustom5() == null) buyPrepaymentsPojo.setCustom5("");
        if (buyPrepaymentsPojo.getCustom6() == null) buyPrepaymentsPojo.setCustom6("");
        if (buyPrepaymentsPojo.getCustom7() == null) buyPrepaymentsPojo.setCustom7("");
        if (buyPrepaymentsPojo.getCustom8() == null) buyPrepaymentsPojo.setCustom8("");
        if (buyPrepaymentsPojo.getCustom9() == null) buyPrepaymentsPojo.setCustom9("");
        if (buyPrepaymentsPojo.getCustom10() == null) buyPrepaymentsPojo.setCustom10("");
        if (tid == null) buyPrepaymentsPojo.setTenantid("");
        if (buyPrepaymentsPojo.getRevision() == null) buyPrepaymentsPojo.setRevision(0);

        // 结账检查
        BuyAccountrecPojo buyAccountrecPojo = this.buyAccountrecMapper.getEntityByMax(tid);
        if (buyAccountrecPojo != null && DateUtils.getTimestamp(buyPrepaymentsPojo.getBilldate()) <= DateUtils.getTimestamp(buyAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + buyAccountrecPojo.getEnddate() + ",禁止新建结账前单据");
        }
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BuyPrepaymentsEntity buyPrepaymentsEntity = new BuyPrepaymentsEntity();
        BeanUtils.copyProperties(buyPrepaymentsPojo, buyPrepaymentsEntity);
        //设置id和新建日期
        buyPrepaymentsEntity.setId(id);
        buyPrepaymentsEntity.setRevision(1);  //乐观锁
        //插入主表
        this.buyPrepaymentsMapper.insert(buyPrepaymentsEntity);
        //Item子表处理
        List<BuyPrepaymentsitemPojo> lst = buyPrepaymentsPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                BuyPrepaymentsitemPojo itemPojo = this.buyPrepaymentsitemService.clearNull(lst.get(i));
                BuyPrepaymentsitemEntity buyPrepaymentsitemEntity = new BuyPrepaymentsitemEntity();
                BeanUtils.copyProperties(itemPojo, buyPrepaymentsitemEntity);
                //设置id和Pid
                buyPrepaymentsitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                buyPrepaymentsitemEntity.setPid(id);
                buyPrepaymentsitemEntity.setTenantid(tid);
                buyPrepaymentsitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.buyPrepaymentsitemMapper.insert(buyPrepaymentsitemEntity);
                // 同步采购单主子表预收额
                String buyOrderId = lst.get(i).getOrderbillid();
                if (!"".equals(buyOrderId)) {
                    this.buyPrepaymentsMapper.updateOrderAdvaAmountFirstAmt(buyOrderId, tid);
                    this.buyPrepaymentsMapper.updateOrderItemAvgFirstAmt(buyOrderId, tid);
                }
            }
        }
        //Cash子表处理
        List<BuyPrepaymentscashPojo> lstcash = buyPrepaymentsPojo.getCash();
        if (lstcash != null) {
            //循环每个item子表
            for (int i = 0; i < lstcash.size(); i++) {
                //初始化item的NULL
                BuyPrepaymentscashPojo cashPojo = this.buyPrepaymentscashService.clearNull(lstcash.get(i));
                BuyPrepaymentscashEntity buyPrepaymentscashEntity = new BuyPrepaymentscashEntity();
                BeanUtils.copyProperties(cashPojo, buyPrepaymentscashEntity);
                //设置id和Pid
                buyPrepaymentscashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                buyPrepaymentscashEntity.setPid(id);
                buyPrepaymentscashEntity.setTenantid(tid);
                buyPrepaymentscashEntity.setRevision(1);  //乐观锁
                //插入子表
                this.buyPrepaymentscashMapper.insert(buyPrepaymentscashEntity);
                //同步出纳账户余额
                this.buyVoucherMapper.updateCashAmount(buyPrepaymentscashEntity.getCashaccid(), 0 - buyPrepaymentscashEntity.getAmount(), tid);
            }
        }

        //返回Bill实例
        return this.getBillEntity(buyPrepaymentsEntity.getId(), buyPrepaymentsEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param buyPrepaymentsPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyPrepaymentsPojo update(BuyPrepaymentsPojo buyPrepaymentsPojo) {
        String tid = buyPrepaymentsPojo.getTenantid();
        // 检查结账
        BuyAccountrecPojo buyAccountrecPojo = this.buyAccountrecMapper.getEntityByMax(tid);
        if (buyAccountrecPojo != null && DateUtils.getTimestamp(buyPrepaymentsPojo.getBilldate()) <= DateUtils.getTimestamp(buyAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + buyAccountrecPojo.getEnddate() + ",禁止修改结账前单据");
        }
        BuyPrepaymentsPojo orgPojo = this.buyPrepaymentsMapper.getEntity(buyPrepaymentsPojo.getId(), tid);
        if (buyAccountrecPojo != null && DateUtils.getTimestamp(orgPojo.getBilldate()) <= DateUtils.getTimestamp(buyAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + buyAccountrecPojo.getEnddate() + ",禁止修改结账前单据");
        }
        //主表更改
        BuyPrepaymentsEntity buyPrepaymentsEntity = new BuyPrepaymentsEntity();
        BeanUtils.copyProperties(buyPrepaymentsPojo, buyPrepaymentsEntity);
        this.buyPrepaymentsMapper.update(buyPrepaymentsEntity);
        //Item子表处理
        List<BuyPrepaymentsitemPojo> lst = buyPrepaymentsPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = buyPrepaymentsMapper.getDelItemIds(buyPrepaymentsPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                this.buyPrepaymentsitemMapper.delete(lstDelIds.get(i), buyPrepaymentsEntity.getTenantid());
                // 同步采购单主子表预收额
                String buyOrderId = lst.get(i).getOrderbillid();
                if (!"".equals(buyOrderId)) {
                    this.buyPrepaymentsMapper.updateOrderAdvaAmountFirstAmt(buyOrderId, tid);
                    this.buyPrepaymentsMapper.updateOrderItemAvgFirstAmt(buyOrderId, tid);
                }
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                BuyPrepaymentsitemEntity buyPrepaymentsitemEntity = new BuyPrepaymentsitemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    BuyPrepaymentsitemPojo itemPojo = this.buyPrepaymentsitemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, buyPrepaymentsitemEntity);
                    //设置id和Pid
                    buyPrepaymentsitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    buyPrepaymentsitemEntity.setPid(buyPrepaymentsEntity.getId());  // 主表 id
                    buyPrepaymentsitemEntity.setTenantid(tid);   // 租户id
                    buyPrepaymentsitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.buyPrepaymentsitemMapper.insert(buyPrepaymentsitemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), buyPrepaymentsitemEntity);
                    buyPrepaymentsitemEntity.setTenantid(tid);
                    this.buyPrepaymentsitemMapper.update(buyPrepaymentsitemEntity);
                }
                // 同步采购单主子表预收额
                String buyOrderId = lst.get(i).getOrderbillid();
                if (!"".equals(buyOrderId)) {
                    this.buyPrepaymentsMapper.updateOrderAdvaAmountFirstAmt(buyOrderId, tid);
                    this.buyPrepaymentsMapper.updateOrderItemAvgFirstAmt(buyOrderId, tid);
                }

            }
        }

        //Cash子表处理
        List<BuyPrepaymentscashPojo> lstcash = buyPrepaymentsPojo.getCash();
        //获取被删除的Item
        List<String> lstcashDelIds = buyPrepaymentsMapper.getDelCashIds(buyPrepaymentsPojo);
        if (lstcashDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstcashDelIds.size(); i++) {
                //删除项同步出纳账户余额
                BuyPrepaymentscashPojo delPojo = this.buyPrepaymentscashMapper.getEntity(lstcashDelIds.get(i), buyPrepaymentsEntity.getTenantid());
                this.buyVoucherMapper.updateCashAmount(delPojo.getCashaccid(), delPojo.getAmount(), tid);
                this.buyPrepaymentscashMapper.delete(lstcashDelIds.get(i), buyPrepaymentsEntity.getTenantid());

            }
        }
        if (lstcash != null) {
            //循环每个item子表
            for (int i = 0; i < lstcash.size(); i++) {
                BuyPrepaymentscashEntity buyPrepaymentscashEntity = new BuyPrepaymentscashEntity();
                if (lstcash.get(i).getId() == "" || lstcash.get(i).getId() == null) {
                    //初始化item的NULL
                    BuyPrepaymentscashPojo cashPojo = this.buyPrepaymentscashService.clearNull(lstcash.get(i));
                    BeanUtils.copyProperties(cashPojo, buyPrepaymentscashEntity);
                    //设置id和Pid
                    buyPrepaymentscashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    buyPrepaymentscashEntity.setPid(buyPrepaymentsEntity.getId());  // 主表 id
                    buyPrepaymentscashEntity.setTenantid(tid);   // 租户id
                    buyPrepaymentscashEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.buyPrepaymentscashMapper.insert(buyPrepaymentscashEntity);
                    //同步出纳账户余额
                    this.buyVoucherMapper.updateCashAmount(buyPrepaymentscashEntity.getCashaccid(), 0 - buyPrepaymentscashEntity.getAmount(), tid);

                } else {
                    //初始化item的NULL
                    BuyPrepaymentscashPojo cashPojo = this.buyPrepaymentscashService.clearNull(lstcash.get(i));
                    BeanUtils.copyProperties(cashPojo, buyPrepaymentscashEntity);
                      //BeanUtils.copyProperties(lst.get(i), buyPrepaymentscashEntity);
                      buyPrepaymentscashEntity.setTenantid(tid);
                    BuyPrepaymentscashPojo buyPrepaymentscashPojo = this.buyPrepaymentscashMapper.getEntity(buyPrepaymentscashEntity.getId(), buyPrepaymentsEntity.getTenantid());
                    this.buyVoucherMapper.updateCashAmount(buyPrepaymentscashPojo.getCashaccid(), buyPrepaymentscashPojo.getAmount(), tid);
                      this.buyPrepaymentscashMapper.update(buyPrepaymentscashEntity);
                    //同步出纳账户余额
                    this.buyVoucherMapper.updateCashAmount(buyPrepaymentscashEntity.getCashaccid(), 0 - buyPrepaymentscashEntity.getAmount(), tid);
                }
            }
        }

        //返回Bill实例
        return this.getBillEntity(buyPrepaymentsEntity.getId(), buyPrepaymentsEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BuyPrepaymentsPojo buyPrepaymentsPojo = this.getBillEntity(key, tid);
        // 检查结账
        BuyAccountrecPojo busAccountrecPojo = this.buyAccountrecMapper.getEntityByMax(buyPrepaymentsPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(buyPrepaymentsPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            // 设置时区为中国标准时间（CST）
            TimeZone timeZone = TimeZone.getTimeZone("Asia/Shanghai");
            // 使用 DateFormatUtils 格式化时间为指定格式的字符串
            String enddate = DateFormatUtils.format(busAccountrecPojo.getEnddate(), "yyyy-MM-dd HH:mm:ss", timeZone);
            throw new RuntimeException("系统已结账至:" + enddate + ",禁止删除结账前单据");
        }
        //Item子表处理
        List<BuyPrepaymentsitemPojo> lst = buyPrepaymentsPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.buyPrepaymentsitemMapper.delete(lst.get(i).getId(), tid);
                // 同步采购单主子表预收额
                String buyOrderId = lst.get(i).getOrderbillid();
                if (!"".equals(buyOrderId)) {
                    this.buyPrepaymentsMapper.updateOrderAdvaAmountFirstAmt(buyOrderId, tid);
                    this.buyPrepaymentsMapper.updateOrderItemAvgFirstAmt(buyOrderId, tid);
                }
            }
        }

        //Cash子表处理
        List<BuyPrepaymentscashPojo> lstcash = buyPrepaymentsPojo.getCash();
        if (lstcash != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstcash.size(); i++) {
                //删除项同步出纳账户余额
                BuyPrepaymentscashPojo delPojo = this.buyPrepaymentscashMapper.getEntity(lstcash.get(i).getCashaccid(), buyPrepaymentsPojo.getTenantid());
                if (delPojo!=null) {
                    this.buyVoucherMapper.updateCashAmount(delPojo.getCashaccid(), delPojo.getAmount(), buyPrepaymentsPojo.getTenantid());
                    this.buyPrepaymentscashMapper.delete(lstcash.get(i).getId(), tid);
                }
            }
        }

        return this.buyPrepaymentsMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param buyPrepaymentsPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BuyPrepaymentsPojo approval(BuyPrepaymentsPojo buyPrepaymentsPojo) {
        //主表更改
        BuyPrepaymentsEntity buyPrepaymentsEntity = new BuyPrepaymentsEntity();
        BeanUtils.copyProperties(buyPrepaymentsPojo, buyPrepaymentsEntity);
        this.buyPrepaymentsMapper.approval(buyPrepaymentsEntity);
        //返回Bill实例
        return this.getBillEntity(buyPrepaymentsEntity.getId(), buyPrepaymentsEntity.getTenantid());
    }

}
