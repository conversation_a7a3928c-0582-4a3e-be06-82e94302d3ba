package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.FmPayapplycashEntity;
import inks.service.sa.som.domain.pojo.FmPayapplycashPojo;
import inks.service.sa.som.mapper.FmPayapplycashMapper;
import inks.service.sa.som.service.FmPayapplycashService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
/**
 * 核销原始单据(FmPayapplycash)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-13 21:30:15
 */
@Service("fmPayapplycashService")
public class FmPayapplycashServiceImpl implements FmPayapplycashService {
    @Resource
    private FmPayapplycashMapper fmPayapplycashMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmPayapplycashPojo getEntity(String key,String tid) {
        return this.fmPayapplycashMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmPayapplycashPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmPayapplycashPojo> lst = fmPayapplycashMapper.getPageList(queryParam);
            PageInfo<FmPayapplycashPojo> pageInfo = new PageInfo<FmPayapplycashPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<FmPayapplycashPojo> getList(String Pid,String tid) { 
        try {
            List<FmPayapplycashPojo> lst = fmPayapplycashMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param fmPayapplycashPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmPayapplycashPojo insert(FmPayapplycashPojo fmPayapplycashPojo) {
        //初始化item的NULL
        FmPayapplycashPojo itempojo =this.clearNull(fmPayapplycashPojo);
        FmPayapplycashEntity fmPayapplycashEntity = new FmPayapplycashEntity(); 
        BeanUtils.copyProperties(itempojo,fmPayapplycashEntity);
        
          fmPayapplycashEntity.setId(UUID.randomUUID().toString());
          fmPayapplycashEntity.setRevision(1);  //乐观锁      
          this.fmPayapplycashMapper.insert(fmPayapplycashEntity);
        return this.getEntity(fmPayapplycashEntity.getId(),fmPayapplycashEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param fmPayapplycashPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmPayapplycashPojo update(FmPayapplycashPojo fmPayapplycashPojo) {
        FmPayapplycashEntity fmPayapplycashEntity = new FmPayapplycashEntity(); 
        BeanUtils.copyProperties(fmPayapplycashPojo,fmPayapplycashEntity);
        this.fmPayapplycashMapper.update(fmPayapplycashEntity);
        return this.getEntity(fmPayapplycashEntity.getId(),fmPayapplycashEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.fmPayapplycashMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param fmPayapplycashPojo 实例对象
     * @return 实例对象
     */
     @Override
     public FmPayapplycashPojo clearNull(FmPayapplycashPojo fmPayapplycashPojo){
     //初始化NULL字段
     if(fmPayapplycashPojo.getPid()==null) fmPayapplycashPojo.setPid("");
     if(fmPayapplycashPojo.getPaybillid()==null) fmPayapplycashPojo.setPaybillid("");
     if(fmPayapplycashPojo.getPaybillcode()==null) fmPayapplycashPojo.setPaybillcode("");
     if(fmPayapplycashPojo.getPaybillamount()==null) fmPayapplycashPojo.setPaybillamount(0D);
     if(fmPayapplycashPojo.getAmount()==null) fmPayapplycashPojo.setAmount(0D);
     if(fmPayapplycashPojo.getRownum()==null) fmPayapplycashPojo.setRownum(0);
     if(fmPayapplycashPojo.getRemark()==null) fmPayapplycashPojo.setRemark("");
     if(fmPayapplycashPojo.getCustom1()==null) fmPayapplycashPojo.setCustom1("");
     if(fmPayapplycashPojo.getCustom2()==null) fmPayapplycashPojo.setCustom2("");
     if(fmPayapplycashPojo.getCustom3()==null) fmPayapplycashPojo.setCustom3("");
     if(fmPayapplycashPojo.getCustom4()==null) fmPayapplycashPojo.setCustom4("");
     if(fmPayapplycashPojo.getCustom5()==null) fmPayapplycashPojo.setCustom5("");
     if(fmPayapplycashPojo.getCustom6()==null) fmPayapplycashPojo.setCustom6("");
     if(fmPayapplycashPojo.getCustom7()==null) fmPayapplycashPojo.setCustom7("");
     if(fmPayapplycashPojo.getCustom8()==null) fmPayapplycashPojo.setCustom8("");
     if(fmPayapplycashPojo.getCustom9()==null) fmPayapplycashPojo.setCustom9("");
     if(fmPayapplycashPojo.getCustom10()==null) fmPayapplycashPojo.setCustom10("");
     if(fmPayapplycashPojo.getTenantid()==null) fmPayapplycashPojo.setTenantid("");
     if(fmPayapplycashPojo.getRevision()==null) fmPayapplycashPojo.setRevision(0);
     return fmPayapplycashPojo;
     }
}
