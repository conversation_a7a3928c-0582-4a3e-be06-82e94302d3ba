package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BusDeposititemEntity;
import inks.service.sa.som.domain.pojo.BusDeposititemPojo;
import inks.service.sa.som.mapper.BusDeposititemMapper;
import inks.service.sa.som.service.BusDeposititemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 预收款项目(BusDeposititem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-20 13:12:52
 */
@Service("busDeposititemService")
public class BusDeposititemServiceImpl implements BusDeposititemService {
    @Resource
    private BusDeposititemMapper busDeposititemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDeposititemPojo getEntity(String key,String tid) {
        return this.busDeposititemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDeposititemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDeposititemPojo> lst = busDeposititemMapper.getPageList(queryParam);
            PageInfo<BusDeposititemPojo> pageInfo = new PageInfo<BusDeposititemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusDeposititemPojo> getList(String Pid,String tid) { 
        try {
            List<BusDeposititemPojo> lst = busDeposititemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busDeposititemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDeposititemPojo insert(BusDeposititemPojo busDeposititemPojo) {
        //初始化item的NULL
        BusDeposititemPojo itempojo =this.clearNull(busDeposititemPojo);
        BusDeposititemEntity busDeposititemEntity = new BusDeposititemEntity(); 
        BeanUtils.copyProperties(itempojo,busDeposititemEntity);
        
          busDeposititemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busDeposititemEntity.setRevision(1);  //乐观锁      
          this.busDeposititemMapper.insert(busDeposititemEntity);
        return this.getEntity(busDeposititemEntity.getId(),busDeposititemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busDeposititemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDeposititemPojo update(BusDeposititemPojo busDeposititemPojo) {
        BusDeposititemEntity busDeposititemEntity = new BusDeposititemEntity(); 
        BeanUtils.copyProperties(busDeposititemPojo,busDeposititemEntity);
        this.busDeposititemMapper.update(busDeposititemEntity);
        return this.getEntity(busDeposititemEntity.getId(),busDeposititemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busDeposititemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busDeposititemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusDeposititemPojo clearNull(BusDeposititemPojo busDeposititemPojo){
     //初始化NULL字段
     if(busDeposititemPojo.getPid()==null) busDeposititemPojo.setPid("");
     if(busDeposititemPojo.getMachbillid()==null) busDeposititemPojo.setMachbillid("");
     if(busDeposititemPojo.getMachbillcode()==null) busDeposititemPojo.setMachbillcode("");
     if(busDeposititemPojo.getDelibillid()==null) busDeposititemPojo.setDelibillid("");
     if(busDeposititemPojo.getDelibillcode()==null) busDeposititemPojo.setDelibillcode("");
     if(busDeposititemPojo.getBilltaxamount()==null) busDeposititemPojo.setBilltaxamount(0D);
     if(busDeposititemPojo.getAmount()==null) busDeposititemPojo.setAmount(0D);
     if(busDeposititemPojo.getRownum()==null) busDeposititemPojo.setRownum(0);
     if(busDeposititemPojo.getRemark()==null) busDeposititemPojo.setRemark("");
     if(busDeposititemPojo.getCustom1()==null) busDeposititemPojo.setCustom1("");
     if(busDeposititemPojo.getCustom2()==null) busDeposititemPojo.setCustom2("");
     if(busDeposititemPojo.getCustom3()==null) busDeposititemPojo.setCustom3("");
     if(busDeposititemPojo.getCustom4()==null) busDeposititemPojo.setCustom4("");
     if(busDeposititemPojo.getCustom5()==null) busDeposititemPojo.setCustom5("");
     if(busDeposititemPojo.getCustom6()==null) busDeposititemPojo.setCustom6("");
     if(busDeposititemPojo.getCustom7()==null) busDeposititemPojo.setCustom7("");
     if(busDeposititemPojo.getCustom8()==null) busDeposititemPojo.setCustom8("");
     if(busDeposititemPojo.getCustom9()==null) busDeposititemPojo.setCustom9("");
     if(busDeposititemPojo.getCustom10()==null) busDeposititemPojo.setCustom10("");
     if(busDeposititemPojo.getTenantid()==null) busDeposititemPojo.setTenantid("");
     if(busDeposititemPojo.getRevision()==null) busDeposititemPojo.setRevision(0);
     return busDeposititemPojo;
     }
}
