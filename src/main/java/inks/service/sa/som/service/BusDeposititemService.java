package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BusDeposititemPojo;

import java.util.List;
/**
 * 预收款项目(BusDeposititem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-20 13:12:52
 */
public interface BusDeposititemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDeposititemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDeposititemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusDeposititemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param busDeposititemPojo 实例对象
     * @return 实例对象
     */
    BusDeposititemPojo insert(BusDeposititemPojo busDeposititemPojo);

    /**
     * 修改数据
     *
     * @param busDeposititempojo 实例对象
     * @return 实例对象
     */
    BusDeposititemPojo update(BusDeposititemPojo busDeposititempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param busDeposititempojo 实例对象
     * @return 实例对象
     */
    BusDeposititemPojo clearNull(BusDeposititemPojo busDeposititempojo);
}
