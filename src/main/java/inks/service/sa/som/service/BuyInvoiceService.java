package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyInvoicePojo;
import inks.service.sa.som.domain.pojo.BuyInvoiceitemdetailPojo;

/**
 * 采购开票(BuyInvoice)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-15 15:01:35
 */
public interface BuyInvoiceService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyInvoicePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyInvoiceitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyInvoicePojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyInvoicePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyInvoicePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyInvoicePojo 实例对象
     * @return 实例对象
     */
    BuyInvoicePojo insert(BuyInvoicePojo buyInvoicePojo);

    /**
     * 修改数据
     *
     * @param buyInvoicepojo 实例对象
     * @return 实例对象
     */
    BuyInvoicePojo update(BuyInvoicePojo buyInvoicepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param buyInvoicePojo 实例对象
     * @return 实例对象
     */
    BuyInvoicePojo approval(BuyInvoicePojo buyInvoicePojo);

    // 查询Item是否被引用
    String getCiteBillName(String key, String tid);
}
