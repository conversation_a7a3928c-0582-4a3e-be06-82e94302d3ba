package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatCombinEntity;
import inks.service.sa.som.domain.MatCombinitemEntity;
import inks.service.sa.som.domain.pojo.MatCombinPojo;
import inks.service.sa.som.domain.pojo.MatCombinitemPojo;
import inks.service.sa.som.domain.pojo.MatCombinitemdetailPojo;
import inks.service.sa.som.domain.pojo.MatInventoryPojo;
import inks.service.sa.som.mapper.MatCombinMapper;
import inks.service.sa.som.mapper.MatCombinitemMapper;
import inks.service.sa.som.service.MatCombinService;
import inks.service.sa.som.service.MatCombinitemService;
import inks.service.sa.som.service.MatInventoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 拆装单(MatCombin)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-19 11:09:25
 */
@Service("matCombinService")
public class MatCombinServiceImpl implements MatCombinService {
    @Resource
    private MatCombinMapper matCombinMapper;

    @Resource
    private MatCombinitemMapper matCombinitemMapper;

    
    @Resource
    private MatCombinitemService matCombinitemService;

    
    @Resource
    private MatInventoryService matInventoryService;

  
//    @Resource
//    private MatPacksnService matPacksnService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatCombinPojo getEntity(String key, String tid) {
        return this.matCombinMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatCombinitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatCombinitemdetailPojo> lst = matCombinMapper.getPageList(queryParam);
            PageInfo<MatCombinitemdetailPojo> pageInfo = new PageInfo<MatCombinitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatCombinPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            MatCombinPojo matCombinPojo = this.matCombinMapper.getEntity(key, tid);
            //读取子表
            matCombinPojo.setItem(matCombinitemMapper.getList(matCombinPojo.getId(), matCombinPojo.getTenantid()));
            return matCombinPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatCombinPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatCombinPojo> lst = matCombinMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(matCombinitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<MatCombinPojo> pageInfo = new PageInfo<MatCombinPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatCombinPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatCombinPojo> lst = matCombinMapper.getPageTh(queryParam);
            PageInfo<MatCombinPojo> pageInfo = new PageInfo<MatCombinPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matCombinPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatCombinPojo insert(MatCombinPojo matCombinPojo) {
//初始化NULL字段
        if (matCombinPojo.getRefno() == null) matCombinPojo.setRefno("");
        if (matCombinPojo.getBilltype() == null) matCombinPojo.setBilltype("");
        if (matCombinPojo.getBilldate() == null) matCombinPojo.setBilldate(new Date());
        if (matCombinPojo.getBilltitle() == null) matCombinPojo.setBilltitle("");
        if (matCombinPojo.getProjectid() == null) matCombinPojo.setProjectid("");
        if (matCombinPojo.getProjcode() == null) matCombinPojo.setProjcode("");
        if (matCombinPojo.getProjname() == null) matCombinPojo.setProjname("");
        if (matCombinPojo.getOutstoreid() == null) matCombinPojo.setOutstoreid("");
        if (matCombinPojo.getOutstorecode() == null) matCombinPojo.setOutstorecode("");
        if (matCombinPojo.getOutstorename() == null) matCombinPojo.setOutstorename("");
        if (matCombinPojo.getInstoreid() == null) matCombinPojo.setInstoreid("");
        if (matCombinPojo.getInstorecode() == null) matCombinPojo.setInstorecode("");
        if (matCombinPojo.getInstorename() == null) matCombinPojo.setInstorename("");
        if (matCombinPojo.getOperator() == null) matCombinPojo.setOperator("");
        if (matCombinPojo.getOperatorid() == null) matCombinPojo.setOperatorid("");
        if (matCombinPojo.getSummary() == null) matCombinPojo.setSummary("");
        if (matCombinPojo.getCreateby() == null) matCombinPojo.setCreateby("");
        if (matCombinPojo.getCreatebyid() == null) matCombinPojo.setCreatebyid("");
        if (matCombinPojo.getCreatedate() == null) matCombinPojo.setCreatedate(new Date());
        if (matCombinPojo.getLister() == null) matCombinPojo.setLister("");
        if (matCombinPojo.getListerid() == null) matCombinPojo.setListerid("");
        if (matCombinPojo.getModifydate() == null) matCombinPojo.setModifydate(new Date());
        if (matCombinPojo.getCustom1() == null) matCombinPojo.setCustom1("");
        if (matCombinPojo.getCustom2() == null) matCombinPojo.setCustom2("");
        if (matCombinPojo.getCustom3() == null) matCombinPojo.setCustom3("");
        if (matCombinPojo.getCustom4() == null) matCombinPojo.setCustom4("");
        if (matCombinPojo.getCustom5() == null) matCombinPojo.setCustom5("");
        if (matCombinPojo.getCustom6() == null) matCombinPojo.setCustom6("");
        if (matCombinPojo.getCustom7() == null) matCombinPojo.setCustom7("");
        if (matCombinPojo.getCustom8() == null) matCombinPojo.setCustom8("");
        if (matCombinPojo.getCustom9() == null) matCombinPojo.setCustom9("");
        if (matCombinPojo.getCustom10() == null) matCombinPojo.setCustom10("");
        if (matCombinPojo.getTenantid() == null) matCombinPojo.setTenantid("");
        if (matCombinPojo.getTenantname() == null) matCombinPojo.setTenantname("");
        if (matCombinPojo.getRevision() == null) matCombinPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        MatCombinEntity matCombinEntity = new MatCombinEntity();
        BeanUtils.copyProperties(matCombinPojo, matCombinEntity);
        //设置id和新建日期
        matCombinEntity.setId(id);
        matCombinEntity.setRevision(1);  //乐观锁
        //插入主表
        this.matCombinMapper.insert(matCombinEntity);
        //Item子表处理
        List<MatCombinitemPojo> lst = matCombinPojo.getItem();
        if (lst != null) {
            String OrgPackSn = "";
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                MatCombinitemPojo itemPojo = this.matCombinitemService.clearNull(lst.get(i));
                MatCombinitemEntity matCombinitemEntity = new MatCombinitemEntity();
                BeanUtils.copyProperties(itemPojo, matCombinitemEntity);
                //设置id和Pid
                matCombinitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                matCombinitemEntity.setPid(id);
                matCombinitemEntity.setTenantid(matCombinPojo.getTenantid());
                matCombinitemEntity.setRevision(1);  //乐观锁

                if (lst.get(i).getAccesstype() == 0) {
                    // 出库
                    MatInventoryPojo matInventoryPojo = this.matInventoryService.getEntity(lst.get(i).getInveid(), matCombinitemEntity.getTenantid());
                    if (matInventoryPojo.getQuantity() - lst.get(i).getQuantity() < 0) {
                        throw new RuntimeException(lst.get(i).getGoodsuid() + "库存数量不足");
                    }
                    OrgPackSn = matInventoryPojo.getPacksn();
                    matInventoryPojo.setQuantity(matInventoryPojo.getQuantity() - lst.get(i).getQuantity());
                    matInventoryPojo.setAmount(matInventoryPojo.getAmount() - lst.get(i).getAmount());
                    matInventoryPojo.setEndoutdate(new Date());
                    matInventoryPojo.setEndoutuid(matCombinEntity.getRefno());
                    matInventoryPojo.setLister(matCombinEntity.getLister());
                    matInventoryPojo.setModifydate(new Date());
                    this.matInventoryService.update(matInventoryPojo);
                } else {
//                    if ("拆分数量".equals(matCombinPojo.getBilltype())) {
//                        // 如果是SN防错
//                        MatGoodsBatchAttrPojo markPojo = this.matPacksnService.getGoodsBatchAttr(lst.get(i).getGoodsid(), matCombinPojo.getTenantid());
//                        if (markPojo.getPacksnmark() > 0) {
//                            MatPacksnPojo matPacksnPojo = new MatPacksnPojo();
//                            matPacksnPojo.setGoodsid(lst.get(i).getGoodsid());
//                            matPacksnPojo.setSubmark(1);
//                            matPacksnPojo.setPacksn(OrgPackSn);
//                            matPacksnPojo.setLister(matCombinPojo.getLister());
//                            matPacksnPojo.setListerid(matCombinPojo.getListerid());
//                            matPacksnPojo.setCreateby(matCombinPojo.getCreateby());
//                            matPacksnPojo.setCreatebyid(matCombinPojo.getCreatebyid());
//                            matPacksnPojo.setCreatedate(new Date());
//                            matPacksnPojo.setModifydate(new Date());
//                            matPacksnPojo.setTenantid(matCombinPojo.getTenantid());
//                            matPacksnPojo = this.matPacksnService.getNewPackSn(matPacksnPojo);
//                            matCombinitemEntity.setPacksn(matPacksnPojo.getPacksn());
//                            lst.get(i).setPacksn(matPacksnPojo.getPacksn());
//                        }
//                        else
//                        {
//                            matCombinitemEntity.setPacksn("");
//                            lst.get(i).setPacksn("");
//                        }
//                    }
                    MatInventoryPojo matInventoryPojo = new MatInventoryPojo();
                    matInventoryPojo.setGoodsid(lst.get(i).getGoodsid());
                    matInventoryPojo.setStoreid(matCombinPojo.getInstoreid());
                    matInventoryPojo.setLocation(lst.get(i).getLocation());
                    matInventoryPojo.setPacksn(lst.get(i).getPacksn());
                    matInventoryPojo.setBatchno(lst.get(i).getBatchno());
                    matInventoryPojo.setTenantid(matCombinPojo.getTenantid());
                    matInventoryPojo.setAmount(lst.get(i).getAmount());
                    matInventoryPojo.setQuantity(lst.get(i).getQuantity());
                    MatInventoryPojo matInventoryinPojo = this.matInventoryService.getEntityBySn(matInventoryPojo);
                    if (matInventoryinPojo != null) {
                        matInventoryinPojo.setEndindate(new Date());
                        matInventoryinPojo.setEndinuid(matCombinPojo.getRefno());
                        matInventoryinPojo.setQuantity(matInventoryinPojo.getQuantity() + lst.get(i).getQuantity());
                        matInventoryinPojo.setAmount(matInventoryinPojo.getAmount() + lst.get(i).getAmount());
                        this.matInventoryService.update(matInventoryinPojo);
                    } else {
                        matInventoryPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
                        matInventoryPojo.setEndindate(new Date());
                        matInventoryPojo.setEndinuid(matCombinPojo.getRefno());
                        this.matInventoryService.insert(matInventoryPojo);
                    }
                }
                //插入子表
                this.matCombinitemMapper.insert(matCombinitemEntity);
                // 同步货品总库存总
                this.matInventoryService.updateGoodsIvQty(lst.get(i).getGoodsid(), matCombinitemEntity.getTenantid());
            }
        }
        //返回Bill实例
        return this.getBillEntity(matCombinEntity.getId(), matCombinEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matCombinPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public MatCombinPojo update(MatCombinPojo matCombinPojo) {
        //主表更改
        MatCombinEntity matCombinEntity = new MatCombinEntity();
        BeanUtils.copyProperties(matCombinPojo, matCombinEntity);
        this.matCombinMapper.update(matCombinEntity);
        if (matCombinPojo.getItem() != null) {
            //Item子表处理
            List<MatCombinitemPojo> lst = matCombinPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = matCombinMapper.getDelItemIds(matCombinPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    MatCombinitemPojo matCombinitemPojo = this.matCombinitemMapper.getEntity(lstDelIds.get(i), matCombinEntity.getTenantid());
                    this.matCombinitemMapper.delete(lstDelIds.get(i), matCombinEntity.getTenantid());
                    // 同步货品总库存总
                    this.matInventoryService.updateGoodsIvQty(matCombinitemPojo.getGoodsid(), matCombinitemPojo.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    MatCombinitemEntity matCombinitemEntity = new MatCombinitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        MatCombinitemPojo itemPojo = this.matCombinitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, matCombinitemEntity);
                        //设置id和Pid
                        matCombinitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        matCombinitemEntity.setPid(matCombinEntity.getId());  // 主表 id
                        matCombinitemEntity.setTenantid(matCombinPojo.getTenantid());   // 租户id
                        matCombinitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.matCombinitemMapper.insert(matCombinitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), matCombinitemEntity);
                        matCombinitemEntity.setTenantid(matCombinPojo.getTenantid());
                        this.matCombinitemMapper.update(matCombinitemEntity);
                    }
                    // 同步货品总库存总
                    this.matInventoryService.updateGoodsIvQty(lst.get(i).getGoodsid(), matCombinitemEntity.getTenantid());
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(matCombinEntity.getId(), matCombinEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        MatCombinPojo matCombinPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<MatCombinitemPojo> lst = matCombinPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                MatCombinitemPojo matCombinitemPojo = this.matCombinitemMapper.getEntity(lst.get(i).getId(), tid);
                this.matCombinitemMapper.delete(lst.get(i).getId(), tid);
                // 同步货品总库存总
                this.matInventoryService.updateGoodsIvQty(matCombinitemPojo.getGoodsid(), matCombinitemPojo.getTenantid());
            }
        }
        return this.matCombinMapper.delete(key, tid);
    }


}
