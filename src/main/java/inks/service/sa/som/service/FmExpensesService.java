package inks.service.sa.som.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmExpensesPojo;
import inks.service.sa.som.domain.pojo.FmExpensesitemdetailPojo;
import inks.service.sa.som.domain.FmExpensesEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

/**
 * 费用报销单(FmExpenses)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-26 15:43:17
 */
public interface FmExpensesService {


    FmExpensesPojo getEntity(String key);

    PageInfo<FmExpensesitemdetailPojo> getPageList(QueryParam queryParam);

    FmExpensesPojo getBillEntity(String key);

    PageInfo<FmExpensesPojo> getBillList(QueryParam queryParam);

    PageInfo<FmExpensesPojo> getPageTh(QueryParam queryParam);

    FmExpensesPojo insert(FmExpensesPojo fmExpensesPojo);

    FmExpensesPojo update(FmExpensesPojo fmExpensespojo);

    int delete(String key);


     FmExpensesPojo approval(FmExpensesPojo fmExpensesPojo);
}
