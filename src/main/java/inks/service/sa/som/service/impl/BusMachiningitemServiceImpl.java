package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BusMachiningitemEntity;
import inks.service.sa.som.domain.pojo.BusMachiningitemPojo;
import inks.service.sa.som.mapper.BusMachiningitemMapper;
import inks.service.sa.som.service.BusMachiningitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 订单项目(BusMachiningitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-23 10:28:18
 */
@Service("busMachiningitemService")
public class BusMachiningitemServiceImpl implements BusMachiningitemService {
    @Resource
    private BusMachiningitemMapper busMachiningitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusMachiningitemPojo getEntity(String key, String tid) {
        return this.busMachiningitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusMachiningitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusMachiningitemPojo> lst = busMachiningitemMapper.getPageList(queryParam);
            PageInfo<BusMachiningitemPojo> pageInfo = new PageInfo<BusMachiningitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusMachiningitemPojo> getList(String Pid, String tid) {
        try {
            List<BusMachiningitemPojo> lst = busMachiningitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param busMachiningitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusMachiningitemPojo insert(BusMachiningitemPojo busMachiningitemPojo) {
        //初始化item的NULL
        BusMachiningitemPojo itempojo = this.clearNull(busMachiningitemPojo);
        BusMachiningitemEntity busMachiningitemEntity = new BusMachiningitemEntity();
        BeanUtils.copyProperties(itempojo, busMachiningitemEntity);

        busMachiningitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        busMachiningitemEntity.setRevision(1);  //乐观锁
        this.busMachiningitemMapper.insert(busMachiningitemEntity);
        return this.getEntity(busMachiningitemEntity.getId(), busMachiningitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busMachiningitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusMachiningitemPojo update(BusMachiningitemPojo busMachiningitemPojo) {
        BusMachiningitemEntity busMachiningitemEntity = new BusMachiningitemEntity();
        BeanUtils.copyProperties(busMachiningitemPojo, busMachiningitemEntity);
        this.busMachiningitemMapper.update(busMachiningitemEntity);
        return this.getEntity(busMachiningitemEntity.getId(), busMachiningitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.busMachiningitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param busMachiningitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusMachiningitemPojo clearNull(BusMachiningitemPojo busMachiningitemPojo) {
        //初始化NULL字段
        if (busMachiningitemPojo.getPid() == null) busMachiningitemPojo.setPid("");
        if (busMachiningitemPojo.getMachbatch() == null) busMachiningitemPojo.setMachbatch("");
        if (busMachiningitemPojo.getGoodsid() == null) busMachiningitemPojo.setGoodsid("");
        if (busMachiningitemPojo.getQuantity() == null) busMachiningitemPojo.setQuantity(0D);
        if (busMachiningitemPojo.getTaxprice() == null) busMachiningitemPojo.setTaxprice(0D);
        if (busMachiningitemPojo.getTaxamount() == null) busMachiningitemPojo.setTaxamount(0D);
        if (busMachiningitemPojo.getItemtaxrate() == null) busMachiningitemPojo.setItemtaxrate(0);
        if (busMachiningitemPojo.getTaxtotal() == null) busMachiningitemPojo.setTaxtotal(0D);
        if (busMachiningitemPojo.getPrice() == null) busMachiningitemPojo.setPrice(0D);
        if (busMachiningitemPojo.getAmount() == null) busMachiningitemPojo.setAmount(0D);
        if (busMachiningitemPojo.getItemorgdate() == null) busMachiningitemPojo.setItemorgdate(new Date());
        if (busMachiningitemPojo.getItemplandate() == null) busMachiningitemPojo.setItemplandate(new Date());
        if (busMachiningitemPojo.getWkqty() == null) busMachiningitemPojo.setWkqty(0D);
        if (busMachiningitemPojo.getStoqty() == null) busMachiningitemPojo.setStoqty(0D);
        if (busMachiningitemPojo.getRownum() == null) busMachiningitemPojo.setRownum(0);
        if (busMachiningitemPojo.getRemark() == null) busMachiningitemPojo.setRemark("");
        if (busMachiningitemPojo.getEngstatetext() == null) busMachiningitemPojo.setEngstatetext("");
        if (busMachiningitemPojo.getEngstatedate() == null) busMachiningitemPojo.setEngstatedate(new Date());
        if (busMachiningitemPojo.getWkstatetext() == null) busMachiningitemPojo.setWkstatetext("");
        if (busMachiningitemPojo.getWkstatedate() == null) busMachiningitemPojo.setWkstatedate(new Date());
        if (busMachiningitemPojo.getBusstatetext() == null) busMachiningitemPojo.setBusstatetext("");
        if (busMachiningitemPojo.getBusstatedate() == null) busMachiningitemPojo.setBusstatedate(new Date());
        if (busMachiningitemPojo.getBuyquantity() == null) busMachiningitemPojo.setBuyquantity(0D);
        if (busMachiningitemPojo.getWkquantity() == null) busMachiningitemPojo.setWkquantity(0D);
        if (busMachiningitemPojo.getInquantity() == null) busMachiningitemPojo.setInquantity(0D);
        if (busMachiningitemPojo.getPickqty() == null) busMachiningitemPojo.setPickqty(0D);
        if (busMachiningitemPojo.getFinishqty() == null) busMachiningitemPojo.setFinishqty(0D);
        if (busMachiningitemPojo.getOutquantity() == null) busMachiningitemPojo.setOutquantity(0D);
        if (busMachiningitemPojo.getOutsecqty() == null) busMachiningitemPojo.setOutsecqty(0D);
        if (busMachiningitemPojo.getEditioninfo() == null) busMachiningitemPojo.setEditioninfo("");
        if (busMachiningitemPojo.getItemcompdate() == null) busMachiningitemPojo.setItemcompdate(new Date());
        if (busMachiningitemPojo.getVirtualitem() == null) busMachiningitemPojo.setVirtualitem(0);
        if (busMachiningitemPojo.getClosed() == null) busMachiningitemPojo.setClosed(0);
        if (busMachiningitemPojo.getStdprice() == null) busMachiningitemPojo.setStdprice(0D);
        if (busMachiningitemPojo.getStdamount() == null) busMachiningitemPojo.setStdamount(0D);
        if (busMachiningitemPojo.getRebate() == null) busMachiningitemPojo.setRebate(0D);
        if (busMachiningitemPojo.getMrpuid() == null) busMachiningitemPojo.setMrpuid("");
        if (busMachiningitemPojo.getMrpid() == null) busMachiningitemPojo.setMrpid("");
        if (busMachiningitemPojo.getMaxqty() == null) busMachiningitemPojo.setMaxqty(0D);
        if (busMachiningitemPojo.getLocation() == null) busMachiningitemPojo.setLocation("");
        if (busMachiningitemPojo.getBatchno() == null) busMachiningitemPojo.setBatchno("");
        if (busMachiningitemPojo.getDisannulmark() == null) busMachiningitemPojo.setDisannulmark(0);
        if (busMachiningitemPojo.getWipused() == null) busMachiningitemPojo.setWipused(0);
        if (busMachiningitemPojo.getWkwpid() == null) busMachiningitemPojo.setWkwpid("");
        if (busMachiningitemPojo.getWkwpcode() == null) busMachiningitemPojo.setWkwpcode("");
        if (busMachiningitemPojo.getWkwpname() == null) busMachiningitemPojo.setWkwpname("");
        if (busMachiningitemPojo.getWkrownum() == null) busMachiningitemPojo.setWkrownum(0);
        if (busMachiningitemPojo.getOrdercostuid() == null) busMachiningitemPojo.setOrdercostuid("");
        if (busMachiningitemPojo.getOrdercostitemid() == null) busMachiningitemPojo.setOrdercostitemid("");
        if (busMachiningitemPojo.getQuotuid() == null) busMachiningitemPojo.setQuotuid("");
        if (busMachiningitemPojo.getQuotitemid() == null) busMachiningitemPojo.setQuotitemid("");
        if (busMachiningitemPojo.getBomtype() == null) busMachiningitemPojo.setBomtype(0);
        if (busMachiningitemPojo.getBomid() == null) busMachiningitemPojo.setBomid("");
        if (busMachiningitemPojo.getBomuid() == null) busMachiningitemPojo.setBomuid("");
        if (busMachiningitemPojo.getBomstate() == null) busMachiningitemPojo.setBomstate("");
        if (busMachiningitemPojo.getAttributejson() == null) busMachiningitemPojo.setAttributejson("");
        if (busMachiningitemPojo.getAttributestr() == null) busMachiningitemPojo.setAttributestr("");
        if (busMachiningitemPojo.getMachtype() == null) busMachiningitemPojo.setMachtype("");
        if (busMachiningitemPojo.getReordermark() == null) busMachiningitemPojo.setReordermark(0);
        if (busMachiningitemPojo.getMatcode() == null) busMachiningitemPojo.setMatcode("");
        if (busMachiningitemPojo.getMatused() == null) busMachiningitemPojo.setMatused(0);
        if (busMachiningitemPojo.getMatid() == null) busMachiningitemPojo.setMatid("");
        if (busMachiningitemPojo.getCostitemjson() == null) busMachiningitemPojo.setCostitemjson("");
        if (busMachiningitemPojo.getCostgroupjson() == null) busMachiningitemPojo.setCostgroupjson("");
        if (busMachiningitemPojo.getMatcostamt() == null) busMachiningitemPojo.setMatcostamt(0D);
        if (busMachiningitemPojo.getLaborcostamt() == null) busMachiningitemPojo.setLaborcostamt(0D);
        if (busMachiningitemPojo.getDirectcostamt() == null) busMachiningitemPojo.setDirectcostamt(0D);
        if (busMachiningitemPojo.getIndirectcostamt() == null) busMachiningitemPojo.setIndirectcostamt(0D);
        if (busMachiningitemPojo.getSourcetype() == null) busMachiningitemPojo.setSourcetype(0);
        if (busMachiningitemPojo.getAttacount() == null) busMachiningitemPojo.setAttacount(0);
        if (busMachiningitemPojo.getCostbudgetamt() == null) busMachiningitemPojo.setCostbudgetamt(0D);
        if (busMachiningitemPojo.getSpecid() == null) busMachiningitemPojo.setSpecid("");
        if (busMachiningitemPojo.getSpecuid() == null) busMachiningitemPojo.setSpecuid("");
        if (busMachiningitemPojo.getSpecstate() == null) busMachiningitemPojo.setSpecstate(0);
        if (busMachiningitemPojo.getMatbuyqty() == null) busMachiningitemPojo.setMatbuyqty(0D);
        if (busMachiningitemPojo.getMatuseqty() == null) busMachiningitemPojo.setMatuseqty(0D);
        if (busMachiningitemPojo.getAvgfirstamt() == null) busMachiningitemPojo.setAvgfirstamt(0D);
        if (busMachiningitemPojo.getAvglastamt() == null) busMachiningitemPojo.setAvglastamt(0D);
        if (busMachiningitemPojo.getAvginvoamt() == null) busMachiningitemPojo.setAvginvoamt(0D);
     if(busMachiningitemPojo.getInvoqty()==null) busMachiningitemPojo.setInvoqty(0D);
     if(busMachiningitemPojo.getInvoclosed()==null) busMachiningitemPojo.setInvoclosed(0);
        if (busMachiningitemPojo.getMainplanqty() == null) busMachiningitemPojo.setMainplanqty(0D);
     if(busMachiningitemPojo.getMainplanclosed()==null) busMachiningitemPojo.setMainplanclosed(0);
        if(busMachiningitemPojo.getWkmergemark()==null) busMachiningitemPojo.setWkmergemark(0);
        if(busMachiningitemPojo.getWkmergeitemid()==null) busMachiningitemPojo.setWkmergeitemid("");
        if(busMachiningitemPojo.getBpmergemark()==null) busMachiningitemPojo.setBpmergemark(0);
        if(busMachiningitemPojo.getBpmergeitemid()==null) busMachiningitemPojo.setBpmergeitemid("");
     if(busMachiningitemPojo.getInvoamt()==null) busMachiningitemPojo.setInvoamt(0D);
     if(busMachiningitemPojo.getSubprice()==null) busMachiningitemPojo.setSubprice(0D);
     if(busMachiningitemPojo.getSubamount()==null) busMachiningitemPojo.setSubamount(0D);
        if (busMachiningitemPojo.getCustom1() == null) busMachiningitemPojo.setCustom1("");
        if (busMachiningitemPojo.getCustom2() == null) busMachiningitemPojo.setCustom2("");
        if (busMachiningitemPojo.getCustom3() == null) busMachiningitemPojo.setCustom3("");
        if (busMachiningitemPojo.getCustom4() == null) busMachiningitemPojo.setCustom4("");
        if (busMachiningitemPojo.getCustom5() == null) busMachiningitemPojo.setCustom5("");
        if (busMachiningitemPojo.getCustom6() == null) busMachiningitemPojo.setCustom6("");
        if (busMachiningitemPojo.getCustom7() == null) busMachiningitemPojo.setCustom7("");
        if (busMachiningitemPojo.getCustom8() == null) busMachiningitemPojo.setCustom8("");
        if (busMachiningitemPojo.getCustom9() == null) busMachiningitemPojo.setCustom9("");
        if (busMachiningitemPojo.getCustom10() == null) busMachiningitemPojo.setCustom10("");
        if (busMachiningitemPojo.getCustom11() == null) busMachiningitemPojo.setCustom11("");
        if (busMachiningitemPojo.getCustom12() == null) busMachiningitemPojo.setCustom12("");
        if (busMachiningitemPojo.getCustom13() == null) busMachiningitemPojo.setCustom13("");
        if (busMachiningitemPojo.getCustom14() == null) busMachiningitemPojo.setCustom14("");
        if (busMachiningitemPojo.getCustom15() == null) busMachiningitemPojo.setCustom15("");
        if (busMachiningitemPojo.getCustom16() == null) busMachiningitemPojo.setCustom16("");
        if (busMachiningitemPojo.getCustom17() == null) busMachiningitemPojo.setCustom17("");
        if (busMachiningitemPojo.getCustom18() == null) busMachiningitemPojo.setCustom18("");
        if (busMachiningitemPojo.getTenantid() == null) busMachiningitemPojo.setTenantid("");
        if (busMachiningitemPojo.getRevision() == null) busMachiningitemPojo.setRevision(0);
        return busMachiningitemPojo;
    }
}
