package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmIncomePojo;
import inks.service.sa.som.domain.pojo.FmIncomeitemdetailPojo;

/**
 * 其他收入(FmIncome)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-29 15:52:28
 */
public interface FmIncomeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmIncomePojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmIncomeitemdetailPojo> getPageList(QueryParam queryParam);

 /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmIncomePojo getBillEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmIncomePojo> getBillList(QueryParam queryParam);
    
        /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmIncomePojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param fmIncomePojo 实例对象
     * @return 实例对象
     */
    FmIncomePojo insert(FmIncomePojo fmIncomePojo);

    /**
     * 修改数据
     *
     * @param fmIncomepojo 实例对象
     * @return 实例对象
     */
    FmIncomePojo update(FmIncomePojo fmIncomepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

                                                                                                                                                                                    }
