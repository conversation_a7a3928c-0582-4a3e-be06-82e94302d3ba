package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyInvoiceitemPojo;

import java.util.List;
/**
 * 发票项目(BuyInvoiceitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-11 15:11:53
 */
public interface BuyInvoiceitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyInvoiceitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyInvoiceitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyInvoiceitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param buyInvoiceitemPojo 实例对象
     * @return 实例对象
     */
    BuyInvoiceitemPojo insert(BuyInvoiceitemPojo buyInvoiceitemPojo);

    /**
     * 修改数据
     *
     * @param buyInvoiceitempojo 实例对象
     * @return 实例对象
     */
    BuyInvoiceitemPojo update(BuyInvoiceitemPojo buyInvoiceitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param buyInvoiceitempojo 实例对象
     * @return 实例对象
     */
    BuyInvoiceitemPojo clearNull(BuyInvoiceitemPojo buyInvoiceitempojo);
}
