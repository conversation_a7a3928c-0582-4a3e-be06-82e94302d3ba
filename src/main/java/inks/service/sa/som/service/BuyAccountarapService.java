package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyAccountarapPojo;

import java.util.List;
/**
 * 发票to付款(BuyAccountarap)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-17 10:52:03
 */
public interface BuyAccountarapService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyAccountarapPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyAccountarapPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyAccountarapPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param buyAccountarapPojo 实例对象
     * @return 实例对象
     */
    BuyAccountarapPojo insert(BuyAccountarapPojo buyAccountarapPojo);

    /**
     * 修改数据
     *
     * @param buyAccountarappojo 实例对象
     * @return 实例对象
     */
    BuyAccountarapPojo update(BuyAccountarapPojo buyAccountarappojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param buyAccountarappojo 实例对象
     * @return 实例对象
     */
    BuyAccountarapPojo clearNull(BuyAccountarapPojo buyAccountarappojo);
}
