package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyFinishingitemPojo;

import java.util.List;
/**
 * 采购验收项目(BuyFinishingitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-06 20:34:36
 */
public interface BuyFinishingitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyFinishingitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyFinishingitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyFinishingitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param buyFinishingitemPojo 实例对象
     * @return 实例对象
     */
    BuyFinishingitemPojo insert(BuyFinishingitemPojo buyFinishingitemPojo);

    /**
     * 修改数据
     *
     * @param buyFinishingitempojo 实例对象
     * @return 实例对象
     */
    BuyFinishingitemPojo update(BuyFinishingitemPojo buyFinishingitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param buyFinishingitempojo 实例对象
     * @return 实例对象
     */
    BuyFinishingitemPojo clearNull(BuyFinishingitemPojo buyFinishingitempojo);
}
