package inks.service.sa.som.service.impl;
/*
 *功能描述
 * <AUTHOR>
 * @date  2021/12/31
 * @param 采购大屏总接口服务实现
 */

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.service.sa.som.mapper.D03MBIR1Mapper;
import inks.service.sa.som.service.D03MBIR1Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
public class D03MBIR1ServiceImpl implements D03MBIR1Service {
    @Resource
    private D03MBIR1Mapper d03MBIR1Mapper;

    /*
     *
     * <AUTHOR>
     * @description 供应商金额占比
     * @date 2021/12/31
     * @param * @param null
     * @return
     */
    @Override
    public List<ChartPojo> getSumAmtByGroupMax(QueryParam queryParam) {
        try {
            return d03MBIR1Mapper.getSumAmtByGroupMax(queryParam);
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
    /*
     *
     * <AUTHOR>
     * @description 货品排行
     * @date 2021/12/31
     * @param * @param null
     * @return
     */
    @Override
    public List<ChartPojo> getSumAmtByGoodsMax(QueryParam queryParam) {
        try {
            return d03MBIR1Mapper.getSumAmtByGoodsMax(queryParam);
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图年度
     * @date 2022/1/3
     * @param * @param null
     * @return
     */
    @Override
    public List<ChartPojo> getSumAmtByYear(QueryParam queryParam, Integer trend) {
        try {
            //将 SearchPojo对象转换成json对象
            // JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
            String dateprefix = DateUtils.parseDateToStr("yyyy", queryParam.getDateRange().getStartDate());
            List<String> list = new ArrayList<>();
            //从SearchPojo 对象中取出时间
            list.add(dateprefix + "-01");
            list.add(dateprefix + "-02");
            list.add(dateprefix + "-03");
            list.add(dateprefix + "-04");
            list.add(dateprefix + "-05");
            list.add(dateprefix + "-06");
            list.add(dateprefix + "-07");
            list.add(dateprefix + "-08");
            list.add(dateprefix + "-09");
            list.add(dateprefix + "-10");
            list.add(dateprefix + "-11");
            list.add(dateprefix + "-12");
            //统计值，趋势时为累加，普通时常态为0.0
            Double sum = 0.0;
            List<ChartPojo> DbPojoList = d03MBIR1Mapper.getSumAmtByYear(queryParam);
            List<ChartPojo> pojoList = new ArrayList<>();
            //循环月list
            for (int i = 0; i < list.size(); i++) {
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(list.get(i));
                chartPojo.setValue(sum);   // 先赋值为最大值
                for (int j = 0; j < DbPojoList.size(); j++) {
                    if (DbPojoList.get(j) != null) {
                        //判断如果月等于数据库返回月那么值就等于数据库返回值 否则为0
                        if (DbPojoList.get(j).getName().equals(list.get(i))) {
                            //因为是趋势图 所以采用累加的形式
                            if (trend != null && trend == 1) {
                                sum += DbPojoList.get(j).getValue();
                                chartPojo.setValue(sum);
                            } else {
                                chartPojo.setValue(DbPojoList.get(j).getValue());
                            }
                            break;
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图月
     * @date 2022/1/3
     * @param * @param null
     * @return
     */
    @Override
    public List<ChartPojo> getSumAmtByMonth(QueryParam queryParam, Integer trend) {
        try {
            //将object对象转行成json对象
            //JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
            //  System.out.println(resultStr.getString("StartDate"));
            String strStartDate = DateUtils.dateTime(queryParam.getDateRange().getStartDate());
            ;
            List<String> dateList = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new SimpleDateFormat("yyyy-MM").parse(strStartDate));
            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");
            String nowDate = dateSdf.format(new Date());
            // 到下个月不在累计
            while (calendar.get(Calendar.MONTH) + 1 == Integer.parseInt(strStartDate.split("-")[1])) {
                // 至本年月日,不在计算
                if (dateSdf.format(calendar.getTime()).equals(nowDate)) {
                    dateList.add(dateSdf.format(calendar.getTime()));
                    calendar.add(Calendar.DATE, 1);
                    break;
                }
                dateList.add(dateSdf.format(calendar.getTime()));
                calendar.add(Calendar.DATE, 1);
            }
            List<ChartPojo> DbPojoList = d03MBIR1Mapper.getSumAmtByMonth(queryParam);
            //统计值，趋势时为累加，普通时常态为0.0
            Double sum = 0.0;
            List<ChartPojo> pojoList = new ArrayList<>();
            for (int i = 0; i < dateList.size(); i++) {
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(dateList.get(i));
                chartPojo.setValue(sum);   // 先赋值为最大值
                for (int j = 0; j < DbPojoList.size(); j++) {
                    if (DbPojoList.get(j) != null) {
                        if (DbPojoList.get(j).getName().equals(dateList.get(i))) {
                            if (trend != null && trend == 1) {
                                sum += DbPojoList.get(j).getValue();
                                chartPojo.setValue(sum);
                            } else {
                                chartPojo.setValue(DbPojoList.get(j).getValue());
                            }
                            break;
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图周
     * @date 2022/1/3
     * @param * @param null
     * @return
     */
    @Override
    public List<ChartPojo> getSumAmtByDay(QueryParam queryParam, Integer trend) {
        try {
            List<String> list = new ArrayList<>();
            for (int i = 7 - 1; i >= 0; i--) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - i);
                Date today = calendar.getTime();
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                list.add(format.format(today));
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            DateRange dateRange = new DateRange("", simpleDateFormat.parse(list.get(0)), simpleDateFormat.parse(list.get(list.size() - 1)));
            queryParam.setDateRange(dateRange);
            List<ChartPojo> DbPojoList = d03MBIR1Mapper.getSumAmtByDay(queryParam);
            //统计值，趋势时为累加，普通时常态为0.0
            Double sum = 0.0;
            List<ChartPojo> pojoList = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                ChartPojo chartPojo = new ChartPojo();
                chartPojo.setName(list.get(i));
                chartPojo.setValue(sum);   // 先赋值为最大值
                for (int j = 0; j < DbPojoList.size(); j++) {
                    if (DbPojoList.get(j) != null) {
                        if (DbPojoList.get(j).getName().equals(list.get(i))) {
                            if (trend != null && trend == 1) {
                                sum += DbPojoList.get(j).getValue();
                                chartPojo.setValue(sum);
                            } else {
                                chartPojo.setValue(DbPojoList.get(j).getValue());
                            }
                            break;
                        }
                    }
                }
                pojoList.add(chartPojo);
            }
            return pojoList;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /*
     *
     * <AUTHOR>
     * @description 本月销售额
     * @date 2022/1/3
     * @param * @param null
     * @return
     */
    @Override
    public ChartPojo getTagSumAmtQtyByDate(QueryParam queryParam) {
        try {
            ChartPojo chartPojo=d03MBIR1Mapper.getTagSumAmtQtyByDate(queryParam);
            chartPojo.setName(DateUtils.dateTime(queryParam.getDateRange().getStartDate())+"~"+DateUtils.dateTime(queryParam.getDateRange().getEndDate()));
            return chartPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    //     /*
//      *
//      * <AUTHOR>
//      * @description 应付返回的记录数
//      * @date 2021/12/31
//      * @param * @param null
//      * @return
//      */
//    @Override
//    public List<ChartPojo> getItemCountSumAmtGroupMonth(String tid) {
//        try {
//            return d03MBIR1Mapper.getItemCountSumAmtGroupMonth(tid);
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
//     /*
//      *
//      * <AUTHOR>
//      * @description 预警记录
//      * @date 2021/12/31
//      * @param * @param null
//      * @return
//      */
//    @Override
//    public List<Map<String, Object>> getList(String tid) {
//        try {
//            return d03MBIR1Mapper.getList(tid);
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }



//    /*
//     *
//     * <AUTHOR>
//     * @description 采购饼状图年
//     * @date 2021/12/31
//     * @param * @param null
//     * @return
//     */
//    @Override
//    public List<ChartPojo> getSumAmtByYearMax(QueryParam queryParam) {
//        try {
//            //将 SearchPojo对象转换成json对象
//            JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
//            List<String> list = new ArrayList<>();
//            //从SearchPojo 对象中取出时间
//            list.add(resultStr.getString("StartDate")+"-01");
//            list.add(resultStr.getString("StartDate")+"-02");
//            list.add(resultStr.getString("StartDate")+"-03");
//            list.add(resultStr.getString("StartDate")+"-04");
//            list.add(resultStr.getString("StartDate")+"-05");
//            list.add(resultStr.getString("StartDate")+"-06");
//            list.add(resultStr.getString("StartDate")+"-07");
//            list.add(resultStr.getString("StartDate")+"-08");
//            list.add(resultStr.getString("StartDate")+"-09");
//            list.add(resultStr.getString("StartDate")+"-10");
//            list.add(resultStr.getString("StartDate")+"-11");
//            list.add(resultStr.getString("StartDate")+"-12");
//            List<ChartPojo> chartPojoList = d03MBIR1Mapper.getSumAmtByYear(queryParam);
//            List<ChartPojo> pojoList = new ArrayList<>();
//            //循环月list
//            for(int i=0;i< list.size();i++){
//                ChartPojo chartPojo = new ChartPojo();
//                chartPojo.setName(list.get(i));
//                for(int j=0;j< chartPojoList.size();j++){
//                    if(chartPojoList.get(j)!=null){
//                        //判断如果月等于数据库返回月那么值就等于数据库返回值 否则为0
//                        if(chartPojoList.get(j).getName().equals(list.get(i))){
//                            //因为是趋势图 所以采用累加的形式
//                            chartPojo.setValue(chartPojoList.get(j).getValue());
//                            break;
//                        }
//                        else{
//                            chartPojo.setValue(0.0);
//                        }
//                    }
//                }
//                pojoList.add(chartPojo);
//            }
//            return pojoList;
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
//    /*
//     *
//     * <AUTHOR>
//     * @description 采购饼状图月
//     * @date 2021/12/31
//     * @param * @param null
//     * @return
//     */
//    @Override
//    public List<ChartPojo> getSumAmtByMonthMax(QueryParam queryParam) {
//        try {
//            //将object对象转行成json对象
//            JSONObject resultStr = (JSONObject) JSONObject.parse(queryParam.getSearchPojo().toString());
//            List<String> dateList = new ArrayList<>();
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(new SimpleDateFormat("yyyy-MM").parse(resultStr.getString("StartDate")));
//            SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd");
//            String nowDate = dateSdf.format(new Date());
//            // 到下个月不在累计
//            while (calendar.get(Calendar.MONTH) + 1 == Integer.parseInt(resultStr.getString("StartDate").split("-")[1])) {
//                // 至本年月日,不在计算
//                if (dateSdf.format(calendar.getTime()).equals(nowDate)) {
//                    dateList.add(dateSdf.format(calendar.getTime()));
//                    calendar.add(Calendar.DATE, 1);
//                    break;
//                }
//                dateList.add(dateSdf.format(calendar.getTime()));
//                calendar.add(Calendar.DATE, 1);
//            }
//            List<ChartPojo> chartPojoList = d03MBIR1Mapper.getSumAmtByMonth(queryParam);
//            List<ChartPojo> pojoList = new ArrayList<>();
//            for(int i=0;i< dateList.size();i++){
//                ChartPojo chartPojo = new ChartPojo();
//                chartPojo.setName(dateList.get(i));
//                for(int j=0;j< chartPojoList.size();j++){
//                    if(chartPojoList.get(j)!=null){
//                        if(chartPojoList.get(j).getName().equals(dateList.get(i))){
//                            chartPojo.setValue(chartPojoList.get(j).getValue());
//                            break;
//                        }
//                        else{
//                            chartPojo.setValue(0.0);
//                        }
//                    }
//                }
//                pojoList.add(chartPojo);
//            }
//            return pojoList;
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
//    /*
//     *
//     * <AUTHOR>
//     * @description 采购饼状图天
//     * @date 2021/12/31
//     * @param * @param null
//     * @return
//     */
//    @Override
//    public List<ChartPojo> getSumAmtByDayMax(QueryParam queryParam) {
//        try {
//            List<String> list = new ArrayList<>();
//            for(int i=7-1;i>=0;i--){
//                Calendar calendar = Calendar.getInstance();
//                calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - i);
//                Date today = calendar.getTime();
//                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//                list.add(format.format(today));
//            }
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//            DateRange dateRange = new DateRange("",simpleDateFormat.parse(list.get(0)),simpleDateFormat.parse(list.get(list.size()-1)));
//            queryParam.setDateRange(dateRange);
//            List<ChartPojo> chartPojoList = d03MBIR1Mapper.getSumAmtByDay(queryParam);
//            List<ChartPojo> pojoList = new ArrayList<>();
//            for(int i=0;i< list.size();i++){
//                ChartPojo chartPojo = new ChartPojo();
//                chartPojo.setName(list.get(i));
//                for(int j=0;j< chartPojoList.size();j++){
//                    if(chartPojoList.get(j)!=null){
//                        if(chartPojoList.get(j).getName().equals(list.get(i))){
//                            chartPojo.setValue(chartPojoList.get(j).getValue());
//                            break;
//                        }
//                        else{
//                            chartPojo.setValue(0.0);
//                        }
//                    }
//                }
//                pojoList.add(chartPojo);
//            }
//            return pojoList;
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
//    /*
//     *
//     * <AUTHOR>
//     * @description 本月采购额
//     * @date 2021/12/31
//     * @param * @param null
//     * @return
//     */
//    @Override
//    public List<ChartPojo> getItemCountByMonth(QueryParam queryParam) {
//        try {
//            return d03MBIR1Mapper.getItemCountByMonth(queryParam);
//        }catch (Exception e){
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }

}
