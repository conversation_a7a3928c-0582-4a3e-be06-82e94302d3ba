package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.service.sa.som.domain.BusDepositEntity;
import inks.service.sa.som.domain.BusDepositcashEntity;
import inks.service.sa.som.domain.BusDeposititemEntity;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.BusAccountrecMapper;
import inks.service.sa.som.mapper.BusDepositMapper;
import inks.service.sa.som.mapper.BusDepositcashMapper;
import inks.service.sa.som.mapper.BusDeposititemMapper;
import inks.service.sa.som.service.BusDepositService;
import inks.service.sa.som.service.BusDepositcashService;
import inks.service.sa.som.service.BusDeposititemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 预收款(BusDeposit)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-15 15:31:25
 */
@Service("busDepositService")
public class BusDepositServiceImpl implements BusDepositService {
    @Resource
    private BusDepositMapper busDepositMapper;

    @Resource
    private BusDeposititemMapper busDeposititemMapper;

    
    @Resource
    private BusDeposititemService busDeposititemService;

    @Resource
    private BusDepositcashMapper busDepositcashMapper;

    
    @Resource
    private BusDepositcashService busDepositcashService;

    @Resource
    private BusAccountrecMapper busAccountrecMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDepositPojo getEntity(String key, String tid) {
        return this.busDepositMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDeposititemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDeposititemdetailPojo> lst = busDepositMapper.getPageList(queryParam);
            PageInfo<BusDeposititemdetailPojo> pageInfo = new PageInfo<BusDeposititemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<BusDepositcashdetailPojo> getCashPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDepositcashdetailPojo> lst = busDepositMapper.getCashPageList(queryParam);
            PageInfo<BusDepositcashdetailPojo> pageInfo = new PageInfo<BusDepositcashdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDepositPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusDepositPojo busDepositPojo = this.busDepositMapper.getEntity(key, tid);
            //读取子表
            busDepositPojo.setItem(busDeposititemMapper.getList(busDepositPojo.getId(), busDepositPojo.getTenantid()));
            //读取Cash
            busDepositPojo.setCash(busDepositcashMapper.getList(busDepositPojo.getId(), busDepositPojo.getTenantid()));
            return busDepositPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDepositPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDepositPojo> lst = busDepositMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busDeposititemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setCash(busDepositcashMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusDepositPojo> pageInfo = new PageInfo<BusDepositPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDepositPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDepositPojo> lst = busDepositMapper.getPageTh(queryParam);
            PageInfo<BusDepositPojo> pageInfo = new PageInfo<BusDepositPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busDepositPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDepositPojo insert(BusDepositPojo busDepositPojo) {
        String tid = busDepositPojo.getTenantid();
//初始化NULL字段
        if (busDepositPojo.getRefno() == null) busDepositPojo.setRefno("");
        if (busDepositPojo.getBilltype() == null) busDepositPojo.setBilltype("");
        if (busDepositPojo.getBilltitle() == null) busDepositPojo.setBilltitle("");
        if (busDepositPojo.getBilldate() == null) busDepositPojo.setBilldate(new Date());
        if (busDepositPojo.getProjectid() == null) busDepositPojo.setProjectid("");
        if (busDepositPojo.getProjname() == null) busDepositPojo.setProjname("");
        if (busDepositPojo.getProjcode() == null) busDepositPojo.setProjcode("");
        if (busDepositPojo.getGroupid() == null) busDepositPojo.setGroupid("");
        if (busDepositPojo.getBillamount() == null) busDepositPojo.setBillamount(0D);
        if (busDepositPojo.getOperator() == null) busDepositPojo.setOperator("");
        if (busDepositPojo.getCitecode() == null) busDepositPojo.setCitecode("");
        if (busDepositPojo.getOutamount() == null) busDepositPojo.setOutamount(0D);
        if (busDepositPojo.getReturnuid() == null) busDepositPojo.setReturnuid("");
        if (busDepositPojo.getOrguid() == null) busDepositPojo.setOrguid("");
        if (busDepositPojo.getSummary() == null) busDepositPojo.setSummary("");
        if (busDepositPojo.getCreateby() == null) busDepositPojo.setCreateby("");
        if (busDepositPojo.getCreatebyid() == null) busDepositPojo.setCreatebyid("");
        if (busDepositPojo.getCreatedate() == null) busDepositPojo.setCreatedate(new Date());
        if (busDepositPojo.getLister() == null) busDepositPojo.setLister("");
        if (busDepositPojo.getListerid() == null) busDepositPojo.setListerid("");
        if (busDepositPojo.getModifydate() == null) busDepositPojo.setModifydate(new Date());
        if (busDepositPojo.getAssessor() == null) busDepositPojo.setAssessor("");
        if (busDepositPojo.getAssessorid() == null) busDepositPojo.setAssessorid("");
        if (busDepositPojo.getAssessdate() == null) busDepositPojo.setAssessdate(new Date());
        if (busDepositPojo.getFmdocmark() == null) busDepositPojo.setFmdocmark(0);
        if (busDepositPojo.getFmdoccode() == null) busDepositPojo.setFmdoccode("");
        if(busDepositPojo.getLockedamount()==null) busDepositPojo.setLockedamount(0D);
        if (busDepositPojo.getCustom1() == null) busDepositPojo.setCustom1("");
        if (busDepositPojo.getCustom2() == null) busDepositPojo.setCustom2("");
        if (busDepositPojo.getCustom3() == null) busDepositPojo.setCustom3("");
        if (busDepositPojo.getCustom4() == null) busDepositPojo.setCustom4("");
        if (busDepositPojo.getCustom5() == null) busDepositPojo.setCustom5("");
        if (busDepositPojo.getCustom6() == null) busDepositPojo.setCustom6("");
        if (busDepositPojo.getCustom7() == null) busDepositPojo.setCustom7("");
        if (busDepositPojo.getCustom8() == null) busDepositPojo.setCustom8("");
        if (busDepositPojo.getCustom9() == null) busDepositPojo.setCustom9("");
        if (busDepositPojo.getCustom10() == null) busDepositPojo.setCustom10("");
        if (tid == null) busDepositPojo.setTenantid("");
        if (busDepositPojo.getRevision() == null) busDepositPojo.setRevision(0);

        // 结账检查
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(tid);
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busDepositPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getEnddate() + ",禁止新建结账前单据");
        }

        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusDepositEntity busDepositEntity = new BusDepositEntity();
        BeanUtils.copyProperties(busDepositPojo, busDepositEntity);
        //设置id和新建日期
        busDepositEntity.setId(id);
        busDepositEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busDepositMapper.insert(busDepositEntity);

        //Item子表处理
        List<BusDeposititemPojo> lst = busDepositPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                BusDeposititemPojo itemPojo = this.busDeposititemService.clearNull(lst.get(i));
                BusDeposititemEntity busDeposititemEntity = new BusDeposititemEntity();
                BeanUtils.copyProperties(itemPojo, busDeposititemEntity);
                //设置id和Pid
                busDeposititemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busDeposititemEntity.setPid(id);
                busDeposititemEntity.setTenantid(tid);
                busDeposititemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busDeposititemMapper.insert(busDeposititemEntity);
                if (!"".equals(lst.get(i).getMachbillid())) {
                    this.busDepositMapper.updateMachAdvaAmountFirstAmt(lst.get(i).getMachbillid(), tid);
                    this.busDepositMapper.updateMachItemAvgFirstAmt(lst.get(i).getMachbillid(), tid);
                }
            }
        }

        //Cash子表处理
        List<BusDepositcashPojo> lstcash = busDepositPojo.getCash();
        if (lstcash != null) {
            //循环每个item子表
            for (int i = 0; i < lstcash.size(); i++) {
                //初始化item的NULL
                BusDepositcashPojo cashPojo = this.busDepositcashService.clearNull(lstcash.get(i));
                BusDepositcashEntity busDepositcashEntity = new BusDepositcashEntity();
                BeanUtils.copyProperties(cashPojo, busDepositcashEntity);
                //设置id和Pid
                busDepositcashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busDepositcashEntity.setPid(id);
                busDepositcashEntity.setTenantid(tid);
                busDepositcashEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busDepositcashMapper.insert(busDepositcashEntity);
                // 同步出纳账户金额
                this.busDepositMapper.updateCashAmount(busDepositcashEntity.getCashaccid(), busDepositcashEntity.getAmount(), tid);
            }
        }

        //返回Bill实例
        return this.getBillEntity(busDepositEntity.getId(), busDepositEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busDepositPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDepositPojo update(BusDepositPojo busDepositPojo) {
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(busDepositPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busDepositPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getEnddate() + ",禁止修改结账前单据");
        }
        BusDepositPojo orgPojo = this.busDepositMapper.getEntity(busDepositPojo.getId(), busDepositPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(orgPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getEnddate() + ",禁止修改结账前单据");
        }

        //主表更改
        BusDepositEntity busDepositEntity = new BusDepositEntity();
        BeanUtils.copyProperties(busDepositPojo, busDepositEntity);
        this.busDepositMapper.update(busDepositEntity);
        //Item子表处理
        List<BusDeposititemPojo> lst = busDepositPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = busDepositMapper.getDelItemIds(busDepositPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                BusDeposititemPojo dpPojo = this.busDeposititemMapper.getEntity(lstDelIds.get(i), busDepositPojo.getTenantid());
                this.busDeposititemMapper.delete(lstDelIds.get(i), busDepositEntity.getTenantid());
                if (!"".equals(dpPojo.getMachbillid())) {
                    this.busDepositMapper.updateMachAdvaAmountFirstAmt(dpPojo.getMachbillid(), busDepositPojo.getTenantid());
                    this.busDepositMapper.updateMachItemAvgFirstAmt(lst.get(i).getMachbillid(), busDepositPojo.getTenantid());
                }
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                BusDeposititemEntity busDeposititemEntity = new BusDeposititemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    BusDeposititemPojo itemPojo = this.busDeposititemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, busDeposititemEntity);
                    //设置id和Pid
                    busDeposititemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    busDeposititemEntity.setPid(busDepositEntity.getId());  // 主表 id
                    busDeposititemEntity.setTenantid(busDepositPojo.getTenantid());   // 租户id
                    busDeposititemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.busDeposititemMapper.insert(busDeposititemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), busDeposititemEntity);
                    busDeposititemEntity.setTenantid(busDepositPojo.getTenantid());
                    this.busDeposititemMapper.update(busDeposititemEntity);
                }
                if (!"".equals(lst.get(i).getMachbillid())) {
                    this.busDepositMapper.updateMachAdvaAmountFirstAmt(lst.get(i).getMachbillid(), busDepositPojo.getTenantid());
                    this.busDepositMapper.updateMachItemAvgFirstAmt(lst.get(i).getMachbillid(), busDepositPojo.getTenantid());
                }
            }
        }

        //Cash子表处理
        List<BusDepositcashPojo> lstcash = busDepositPojo.getCash();
        //获取被删除的Item
        List<String> lstcashDelIds = busDepositMapper.getDelCashIds(busDepositPojo);
        if (lstcashDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstcashDelIds.size(); i++) {
                BusDepositcashPojo delPojo = this.busDepositcashMapper.getEntity(lstcashDelIds.get(i), busDepositEntity.getTenantid());
                this.busDepositcashMapper.delete(lstcashDelIds.get(i), busDepositEntity.getTenantid());
                // 同步出纳账户金额
                this.busDepositMapper.updateCashAmount(delPojo.getCashaccid(), 0 - delPojo.getAmount(), busDepositPojo.getTenantid());
            }
        }
        if (lstcash != null) {
            //循环每个item子表
            for (int i = 0; i < lstcash.size(); i++) {
                BusDepositcashEntity busDepositcashEntity = new BusDepositcashEntity();
                if (lstcash.get(i).getId() == "" || lstcash.get(i).getId() == null) {
                    //初始化item的NULL
                    BusDepositcashPojo cashPojo = this.busDepositcashService.clearNull(lstcash.get(i));
                    BeanUtils.copyProperties(cashPojo, busDepositcashEntity);
                    //设置id和Pid
                    busDepositcashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    busDepositcashEntity.setPid(busDepositEntity.getId());  // 主表 id
                    busDepositcashEntity.setTenantid(busDepositPojo.getTenantid());   // 租户id
                    busDepositcashEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.busDepositcashMapper.insert(busDepositcashEntity);
                } else {
                    BeanUtils.copyProperties(lstcash.get(i), busDepositcashEntity);
                    busDepositcashEntity.setTenantid(busDepositPojo.getTenantid());
                    // 先减少之前金额
                    BusDepositcashPojo dbPojo = this.busDepositcashMapper.getEntity(lstcash.get(i).getId(), busDepositEntity.getTenantid());
                    this.busDepositMapper.updateCashAmount(dbPojo.getCashaccid(), 0 - dbPojo.getAmount(), busDepositPojo.getTenantid());
                    this.busDepositcashMapper.update(busDepositcashEntity);
                    // 再加上修改后的客 户
                    this.busDepositMapper.updateCashAmount(busDepositcashEntity.getCashaccid(), busDepositcashEntity.getAmount(), busDepositPojo.getTenantid());
                }
            }
        }

        //返回Bill实例
        return this.getBillEntity(busDepositEntity.getId(), busDepositEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BusDepositPojo busDepositPojo = this.getBillEntity(key, tid);
        BusAccountrecPojo busAccountrecPojo = this.busAccountrecMapper.getEntityByMax(busDepositPojo.getTenantid());
        if (busAccountrecPojo != null && DateUtils.getTimestamp(busDepositPojo.getBilldate()) <= DateUtils.getTimestamp(busAccountrecPojo.getEnddate())) {
            throw new RuntimeException("系统已结账至:" + busAccountrecPojo.getEnddate() + ",禁止删除结账前单据");
        }
        //Item子表处理
        List<BusDeposititemPojo> lst = busDepositPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                BusDeposititemPojo delPojo = this.busDeposititemMapper.getEntity(lst.get(i).getId(), busDepositPojo.getTenantid());
                this.busDeposititemMapper.delete(lst.get(i).getId(), tid);
                if (!"".equals(delPojo.getMachbillid())) {
                    this.busDepositMapper.updateMachAdvaAmountFirstAmt(delPojo.getMachbillid(), busDepositPojo.getTenantid());
                    this.busDepositMapper.updateMachItemAvgFirstAmt(lst.get(i).getMachbillid(), busDepositPojo.getTenantid());
                }
            }
        }

        //Cash子表处理  Eric ********
        List<BusDepositcashPojo> lstcash = busDepositPojo.getCash();
        if (lstcash != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstcash.size(); i++) {
                BusDepositcashPojo delPojo = this.busDepositcashMapper.getEntity(lstcash.get(i).getId(), tid);
                this.busDepositcashMapper.delete(lstcash.get(i).getId(), tid);
                // 同步出纳账户金额
                this.busDepositMapper.updateCashAmount(delPojo.getCashaccid(), 0 - delPojo.getAmount(), busDepositPojo.getTenantid());
            }
        }
        return this.busDepositMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param busDepositPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusDepositPojo approval(BusDepositPojo busDepositPojo) {
        //主表更改
        BusDepositEntity busDepositEntity = new BusDepositEntity();
        BeanUtils.copyProperties(busDepositPojo, busDepositEntity);
        this.busDepositMapper.approval(busDepositEntity);
        //返回Bill实例
        return this.getBillEntity(busDepositEntity.getId(), busDepositEntity.getTenantid());
    }

}
