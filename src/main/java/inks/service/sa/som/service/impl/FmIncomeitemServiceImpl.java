package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.FmIncomeitemEntity;
import inks.service.sa.som.domain.pojo.FmIncomeitemPojo;
import inks.service.sa.som.mapper.FmIncomeitemMapper;
import inks.service.sa.som.service.FmIncomeitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
/**
 * 收入明细(FmIncomeitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-29 15:52:48
 */
@Service("fmIncomeitemService")
public class FmIncomeitemServiceImpl implements FmIncomeitemService {
    @Resource
    private FmIncomeitemMapper fmIncomeitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmIncomeitemPojo getEntity(String key,String tid) {
        return this.fmIncomeitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmIncomeitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmIncomeitemPojo> lst = fmIncomeitemMapper.getPageList(queryParam);
            PageInfo<FmIncomeitemPojo> pageInfo = new PageInfo<FmIncomeitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<FmIncomeitemPojo> getList(String Pid,String tid) { 
        try {
            List<FmIncomeitemPojo> lst = fmIncomeitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param fmIncomeitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmIncomeitemPojo insert(FmIncomeitemPojo fmIncomeitemPojo) {
        //初始化item的NULL
        FmIncomeitemPojo itempojo =this.clearNull(fmIncomeitemPojo);
        FmIncomeitemEntity fmIncomeitemEntity = new FmIncomeitemEntity(); 
        BeanUtils.copyProperties(itempojo,fmIncomeitemEntity);
        
          fmIncomeitemEntity.setId(UUID.randomUUID().toString());
          fmIncomeitemEntity.setRevision(1);  //乐观锁      
          this.fmIncomeitemMapper.insert(fmIncomeitemEntity);
        return this.getEntity(fmIncomeitemEntity.getId(),fmIncomeitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param fmIncomeitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmIncomeitemPojo update(FmIncomeitemPojo fmIncomeitemPojo) {
        FmIncomeitemEntity fmIncomeitemEntity = new FmIncomeitemEntity(); 
        BeanUtils.copyProperties(fmIncomeitemPojo,fmIncomeitemEntity);
        this.fmIncomeitemMapper.update(fmIncomeitemEntity);
        return this.getEntity(fmIncomeitemEntity.getId(),fmIncomeitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.fmIncomeitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param fmIncomeitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public FmIncomeitemPojo clearNull(FmIncomeitemPojo fmIncomeitemPojo){
     //初始化NULL字段
     if(fmIncomeitemPojo.getPid()==null) fmIncomeitemPojo.setPid("");
     if(fmIncomeitemPojo.getCosttypeid()==null) fmIncomeitemPojo.setCosttypeid("");
     if(fmIncomeitemPojo.getItemname()==null) fmIncomeitemPojo.setItemname("");
     if(fmIncomeitemPojo.getItemdepict()==null) fmIncomeitemPojo.setItemdepict("");
     if(fmIncomeitemPojo.getAmount()==null) fmIncomeitemPojo.setAmount(0D);
     if(fmIncomeitemPojo.getRownum()==null) fmIncomeitemPojo.setRownum(0);
     if(fmIncomeitemPojo.getRemark()==null) fmIncomeitemPojo.setRemark("");
     if(fmIncomeitemPojo.getCustom1()==null) fmIncomeitemPojo.setCustom1("");
     if(fmIncomeitemPojo.getCustom2()==null) fmIncomeitemPojo.setCustom2("");
     if(fmIncomeitemPojo.getCustom3()==null) fmIncomeitemPojo.setCustom3("");
     if(fmIncomeitemPojo.getCustom4()==null) fmIncomeitemPojo.setCustom4("");
     if(fmIncomeitemPojo.getCustom5()==null) fmIncomeitemPojo.setCustom5("");
     if(fmIncomeitemPojo.getCustom6()==null) fmIncomeitemPojo.setCustom6("");
     if(fmIncomeitemPojo.getCustom7()==null) fmIncomeitemPojo.setCustom7("");
     if(fmIncomeitemPojo.getCustom8()==null) fmIncomeitemPojo.setCustom8("");
     if(fmIncomeitemPojo.getCustom9()==null) fmIncomeitemPojo.setCustom9("");
     if(fmIncomeitemPojo.getCustom10()==null) fmIncomeitemPojo.setCustom10("");
     if(fmIncomeitemPojo.getTenantid()==null) fmIncomeitemPojo.setTenantid("");
     if(fmIncomeitemPojo.getRevision()==null) fmIncomeitemPojo.setRevision(0);
     return fmIncomeitemPojo;
     }
}
