package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BusDeductionitemEntity;
import inks.service.sa.som.domain.pojo.BusDeductionitemPojo;
import inks.service.sa.som.mapper.BusDeductionitemMapper;
import inks.service.sa.som.service.BusDeductionitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 销售扣款Item(BusDeductionitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-16 16:59:36
 */
@Service("busDeductionitemService")
public class BusDeductionitemServiceImpl implements BusDeductionitemService {
    @Resource
    private BusDeductionitemMapper busDeductionitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDeductionitemPojo getEntity(String key, String tid) {
        return this.busDeductionitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDeductionitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDeductionitemPojo> lst = busDeductionitemMapper.getPageList(queryParam);
            PageInfo<BusDeductionitemPojo> pageInfo = new PageInfo<BusDeductionitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusDeductionitemPojo> getList(String Pid, String tid) {
        try {
            List<BusDeductionitemPojo> lst = busDeductionitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param busDeductionitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDeductionitemPojo insert(BusDeductionitemPojo busDeductionitemPojo) {
        //初始化item的NULL
        BusDeductionitemPojo itempojo = this.clearNull(busDeductionitemPojo);
        BusDeductionitemEntity busDeductionitemEntity = new BusDeductionitemEntity();
        BeanUtils.copyProperties(itempojo, busDeductionitemEntity);

        busDeductionitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        busDeductionitemEntity.setRevision(1);  //乐观锁
        this.busDeductionitemMapper.insert(busDeductionitemEntity);
        return this.getEntity(busDeductionitemEntity.getId(), busDeductionitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busDeductionitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDeductionitemPojo update(BusDeductionitemPojo busDeductionitemPojo) {
        BusDeductionitemEntity busDeductionitemEntity = new BusDeductionitemEntity();
        BeanUtils.copyProperties(busDeductionitemPojo, busDeductionitemEntity);
        this.busDeductionitemMapper.update(busDeductionitemEntity);
        return this.getEntity(busDeductionitemEntity.getId(), busDeductionitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.busDeductionitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param busDeductionitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDeductionitemPojo clearNull(BusDeductionitemPojo busDeductionitemPojo) {
        //初始化NULL字段
        if (busDeductionitemPojo.getPid() == null) busDeductionitemPojo.setPid("");
        if (busDeductionitemPojo.getGoodsid() == null) busDeductionitemPojo.setGoodsid("");
        if (busDeductionitemPojo.getQuantity() == null) busDeductionitemPojo.setQuantity(0D);
        if (busDeductionitemPojo.getTaxprice() == null) busDeductionitemPojo.setTaxprice(0D);
        if (busDeductionitemPojo.getTaxamount() == null) busDeductionitemPojo.setTaxamount(0D);
        if (busDeductionitemPojo.getTaxtotal() == null) busDeductionitemPojo.setTaxtotal(0D);
        if (busDeductionitemPojo.getItemtaxrate() == null) busDeductionitemPojo.setItemtaxrate(0);
        if (busDeductionitemPojo.getPrice() == null) busDeductionitemPojo.setPrice(0D);
        if (busDeductionitemPojo.getAmount() == null) busDeductionitemPojo.setAmount(0D);
        if (busDeductionitemPojo.getRemark() == null) busDeductionitemPojo.setRemark("");
        if (busDeductionitemPojo.getCiteuid() == null) busDeductionitemPojo.setCiteuid("");
        if (busDeductionitemPojo.getCiteitemid() == null) busDeductionitemPojo.setCiteitemid("");
        if (busDeductionitemPojo.getMachuid() == null) busDeductionitemPojo.setMachuid("");
        if (busDeductionitemPojo.getMachitemid() == null) busDeductionitemPojo.setMachitemid("");
        if (busDeductionitemPojo.getCustpo() == null) busDeductionitemPojo.setCustpo("");
        if (busDeductionitemPojo.getRownum() == null) busDeductionitemPojo.setRownum(0);
        if (busDeductionitemPojo.getInvoqty() == null) busDeductionitemPojo.setInvoqty(0D);
        if (busDeductionitemPojo.getInvoclosed() == null) busDeductionitemPojo.setInvoclosed(0);
        if (busDeductionitemPojo.getDisannulmark() == null) busDeductionitemPojo.setDisannulmark(0);
        if (busDeductionitemPojo.getDisannullisterid() == null) busDeductionitemPojo.setDisannullisterid("");
        if (busDeductionitemPojo.getDisannullister() == null) busDeductionitemPojo.setDisannullister("");
        if (busDeductionitemPojo.getDisannuldate() == null) busDeductionitemPojo.setDisannuldate(new Date());
        if (busDeductionitemPojo.getCustom1() == null) busDeductionitemPojo.setCustom1("");
        if (busDeductionitemPojo.getCustom2() == null) busDeductionitemPojo.setCustom2("");
        if (busDeductionitemPojo.getCustom3() == null) busDeductionitemPojo.setCustom3("");
        if (busDeductionitemPojo.getCustom4() == null) busDeductionitemPojo.setCustom4("");
        if (busDeductionitemPojo.getCustom5() == null) busDeductionitemPojo.setCustom5("");
        if (busDeductionitemPojo.getCustom6() == null) busDeductionitemPojo.setCustom6("");
        if (busDeductionitemPojo.getCustom7() == null) busDeductionitemPojo.setCustom7("");
        if (busDeductionitemPojo.getCustom8() == null) busDeductionitemPojo.setCustom8("");
        if (busDeductionitemPojo.getCustom9() == null) busDeductionitemPojo.setCustom9("");
        if (busDeductionitemPojo.getCustom10() == null) busDeductionitemPojo.setCustom10("");
        if (busDeductionitemPojo.getTenantid() == null) busDeductionitemPojo.setTenantid("");
        if (busDeductionitemPojo.getRevision() == null) busDeductionitemPojo.setRevision(0);
        return busDeductionitemPojo;
    }
}
