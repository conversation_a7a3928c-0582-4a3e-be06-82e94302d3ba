package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BusInvoiceEntity;
import inks.service.sa.som.domain.BusInvoiceitemEntity;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.*;
import inks.service.sa.som.service.BusInvoiceService;
import inks.service.sa.som.service.BusInvoiceitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 销售开票(BusInvoice)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-15 15:30:29
 */
@Service("busInvoiceService")
public class BusInvoiceServiceImpl implements BusInvoiceService {
    @Resource
    private BusInvoiceMapper busInvoiceMapper;

    @Resource
    private BusInvoiceitemMapper busInvoiceitemMapper;

    
    @Resource
    private BusInvoiceitemService busInvoiceitemService;

    @Resource
    private BusDelieryMapper busDelieryMapper;

    @Resource
    private BusDelieryitemMapper busDelieryitemMapper;

    @Resource
    private BusDeductionitemMapper busDeductionitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusInvoicePojo getEntity(String key, String tid) {
        return this.busInvoiceMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusInvoiceitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusInvoiceitemdetailPojo> lst = busInvoiceMapper.getPageList(queryParam);
            PageInfo<BusInvoiceitemdetailPojo> pageInfo = new PageInfo<BusInvoiceitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusInvoicePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            BusInvoicePojo busInvoicePojo = this.busInvoiceMapper.getEntity(key, tid);
            //读取子表
            busInvoicePojo.setItem(busInvoiceitemMapper.getList(busInvoicePojo.getId(), busInvoicePojo.getTenantid()));
            return busInvoicePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusInvoicePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusInvoicePojo> lst = busInvoiceMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busInvoiceitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusInvoicePojo> pageInfo = new PageInfo<BusInvoicePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusInvoicePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusInvoicePojo> lst = busInvoiceMapper.getPageTh(queryParam);
            PageInfo<BusInvoicePojo> pageInfo = new PageInfo<BusInvoicePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busInvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusInvoicePojo insert(BusInvoicePojo busInvoicePojo) {
//初始化NULL字段
        String tid = busInvoicePojo.getTenantid();
        if (busInvoicePojo.getRefno() == null) busInvoicePojo.setRefno("");
        if (busInvoicePojo.getBilltype() == null) busInvoicePojo.setBilltype("");
        if (busInvoicePojo.getBilltitle() == null) busInvoicePojo.setBilltitle("");
        if (busInvoicePojo.getBilldate() == null) busInvoicePojo.setBilldate(new Date());
        if (busInvoicePojo.getProjectid() == null) busInvoicePojo.setProjectid("");
        if (busInvoicePojo.getProjname() == null) busInvoicePojo.setProjname("");
        if (busInvoicePojo.getProjcode() == null) busInvoicePojo.setProjcode("");
        if (busInvoicePojo.getGroupid() == null) busInvoicePojo.setGroupid("");
        if (busInvoicePojo.getTaxamount() == null) busInvoicePojo.setTaxamount(0D);
        if (busInvoicePojo.getAmount() == null) busInvoicePojo.setAmount(0D);
        if (busInvoicePojo.getTaxtotal() == null) busInvoicePojo.setTaxtotal(0D);
        if (busInvoicePojo.getTaxrate() == null) busInvoicePojo.setTaxrate(0);
        if (busInvoicePojo.getInvocode() == null) busInvoicePojo.setInvocode("");
        if (busInvoicePojo.getInvodate() == null) busInvoicePojo.setInvodate(new Date());
        if (busInvoicePojo.getAimdate() == null) busInvoicePojo.setAimdate(new Date());
        if (busInvoicePojo.getReceipted() == null) busInvoicePojo.setReceipted(0D);
        if (busInvoicePojo.getSummary() == null) busInvoicePojo.setSummary("");
        if (busInvoicePojo.getCreateby() == null) busInvoicePojo.setCreateby("");
        if (busInvoicePojo.getCreatebyid() == null) busInvoicePojo.setCreatebyid("");
        if (busInvoicePojo.getCreatedate() == null) busInvoicePojo.setCreatedate(new Date());
        if (busInvoicePojo.getLister() == null) busInvoicePojo.setLister("");
        if (busInvoicePojo.getListerid() == null) busInvoicePojo.setListerid("");
        if (busInvoicePojo.getModifydate() == null) busInvoicePojo.setModifydate(new Date());
        if (busInvoicePojo.getAssessor() == null) busInvoicePojo.setAssessor("");
        if (busInvoicePojo.getAssessorid() == null) busInvoicePojo.setAssessorid("");
        if (busInvoicePojo.getAssessdate() == null) busInvoicePojo.setAssessdate(new Date());
        if (busInvoicePojo.getStatecode() == null) busInvoicePojo.setStatecode("");
        if (busInvoicePojo.getStatedate() == null) busInvoicePojo.setStatedate(new Date());
        if (busInvoicePojo.getClosed() == null) busInvoicePojo.setClosed(0);
        if (busInvoicePojo.getDisannulmark() == null) busInvoicePojo.setDisannulmark(0);
        if (busInvoicePojo.getFmdocmark() == null) busInvoicePojo.setFmdocmark(0);
        if (busInvoicePojo.getFmdoccode() == null) busInvoicePojo.setFmdoccode("");
        if (busInvoicePojo.getOperator() == null) busInvoicePojo.setOperator("");
        if (busInvoicePojo.getFirstamt() == null) busInvoicePojo.setFirstamt(0D);
        if (busInvoicePojo.getLastamt() == null) busInvoicePojo.setLastamt(0D);
        if (busInvoicePojo.getCustom1() == null) busInvoicePojo.setCustom1("");
        if (busInvoicePojo.getCustom2() == null) busInvoicePojo.setCustom2("");
        if (busInvoicePojo.getCustom3() == null) busInvoicePojo.setCustom3("");
        if (busInvoicePojo.getCustom4() == null) busInvoicePojo.setCustom4("");
        if (busInvoicePojo.getCustom5() == null) busInvoicePojo.setCustom5("");
        if (busInvoicePojo.getCustom6() == null) busInvoicePojo.setCustom6("");
        if (busInvoicePojo.getCustom7() == null) busInvoicePojo.setCustom7("");
        if (busInvoicePojo.getCustom8() == null) busInvoicePojo.setCustom8("");
        if (busInvoicePojo.getCustom9() == null) busInvoicePojo.setCustom9("");
        if (busInvoicePojo.getCustom10() == null) busInvoicePojo.setCustom10("");
        if (tid == null) busInvoicePojo.setTenantid("");
        if (busInvoicePojo.getRevision() == null) busInvoicePojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusInvoiceEntity busInvoiceEntity = new BusInvoiceEntity();
        BeanUtils.copyProperties(busInvoicePojo, busInvoiceEntity);
        //设置id和新建日期
        busInvoiceEntity.setId(id);
        busInvoiceEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busInvoiceMapper.insert(busInvoiceEntity);
        if (busInvoicePojo.getItem() != null) {
            //销售开票时 有明细 Eric 20220109
            //Item子表处理
            List<BusInvoiceitemPojo> lst = busInvoicePojo.getItem();
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    //初始化item的NULL
                    BusInvoiceitemPojo itemPojo = this.busInvoiceitemService.clearNull(lst.get(i));
                    BusInvoiceitemEntity busInvoiceitemEntity = new BusInvoiceitemEntity();
                    BeanUtils.copyProperties(itemPojo, busInvoiceitemEntity);
                    //设置id和Pid
                    busInvoiceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                    busInvoiceitemEntity.setPid(id);
                    busInvoiceitemEntity.setTenantid(tid);
                    busInvoiceitemEntity.setRevision(1);  //乐观锁
                    //插入子表
                    this.busInvoiceitemMapper.insert(busInvoiceitemEntity);

                    //更新订单已发货 Eric20211213
                    if (!lst.get(i).getDeliitemid().equals("") && !lst.get(i).getDeliuid().equals("")) {
                        if (lst.get(i).getDelitype().equals("发出商品") || lst.get(i).getDelitype().equals("订单退货") ||
                                lst.get(i).getDelitype().equals("其他发货") || lst.get(i).getDelitype().equals("其他退货") ||
                                lst.get(i).getDelitype().equals("退货返工") || lst.get(i).getDelitype().equals("返工补发")) {
                            this.busInvoiceMapper.updateDeliInvoFinish(lst.get(i).getDeliitemid(), lst.get(i).getDeliuid(), tid);
                            // 超数检查
                            BusDelieryitemPojo busDelieryitemPojo = this.busDelieryitemMapper.getEntity(lst.get(i).getDeliitemid(), tid);
                            if (busDelieryitemPojo != null) {
                                if (busDelieryitemPojo.getInvoqty() > busDelieryitemPojo.getQuantity()) {
                                    int Rowno = i + 1;
                                    throw new RuntimeException(Rowno + "行,开票总数:" + busDelieryitemPojo.getInvoqty() + "超出销售数:" + busDelieryitemPojo.getQuantity());
                                }
                            } else {
                                throw new RuntimeException("关联单据丢失:" + lst.get(i).getDeliuid());
                            }
                            this.busInvoiceMapper.updateDeliInvoCount(lst.get(i).getDeliitemid(), lst.get(i).getDeliuid(), tid);
                            if (isNotBlank(lst.get(i).getMachitemid())&& isNotBlank(lst.get(i).getMachuid())) {
                                // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
                                syncMachAboutInvo(lst.get(i).getMachitemid(), lst.get(i).getMachuid(), tid);
                            }
                        } else {
                            this.busInvoiceMapper.updateDeduInvoFinish(lst.get(i).getDeliitemid(), lst.get(i).getDeliuid(), tid);
                            // 超数检查
                            BusDeductionitemPojo busDeductionitemPojo = this.busDeductionitemMapper.getEntity(lst.get(i).getDeliitemid(), tid);
                            if (busDeductionitemPojo != null) {
                                if (busDeductionitemPojo.getInvoqty() > busDeductionitemPojo.getQuantity()) {
                                    int Rowno = i + 1;
                                    throw new RuntimeException(Rowno + "行,开票总数:" + busDeductionitemPojo.getInvoqty() + "超出扣款数:" + busDeductionitemPojo.getQuantity());
                                }
                            } else {
                                throw new RuntimeException("关联单据丢失:" + lst.get(i).getDeliuid());
                            }
                            this.busInvoiceMapper.updateDeduInvoCount(lst.get(i).getDeliitemid(), lst.get(i).getDeliuid(), tid);
                        }
                    }

                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());

    }


    // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
    private void syncMachAboutInvo(String machitemid, String machuid, String tid) {
        String machId = busInvoiceMapper.getMachId(machitemid, tid);
        this.busInvoiceMapper.updateMachItemInvoQty(machitemid, machuid, tid);
        this.busInvoiceMapper.updateMachInvoCountInvoAmt(machitemid, machuid, tid);
        this.busInvoiceMapper.updateMachItemAvgInvoAmt(machId, tid);
    }

    /**
     * 修改数据
     *
     * @param busInvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusInvoicePojo update(BusInvoicePojo busInvoicePojo) {
        String tid = busInvoicePojo.getTenantid();
        //主表更改
        BusInvoiceEntity busInvoiceEntity = new BusInvoiceEntity();
        BeanUtils.copyProperties(busInvoicePojo, busInvoiceEntity);
        BusInvoicePojo dbPojo = this.busInvoiceMapper.getEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
        if (busInvoiceEntity.getAmount() != null && busInvoiceEntity.getAmount() != dbPojo.getAmount()) {
            List<String> lstcite = getCiteBillName(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
            if (lstcite.size() > 0) {
                throw new RuntimeException("发票已被引用,禁止修改:" + lstcite);
            }
        }
        this.busInvoiceMapper.update(busInvoiceEntity);
        if (busInvoicePojo.getItem() != null) {
            //销售开票时 有明细 Eric 20220109
            //Item子表处理
            List<BusInvoiceitemPojo> lst = busInvoicePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = busInvoiceMapper.getDelItemIds(busInvoicePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    BusInvoiceitemPojo dpPojo = this.busInvoiceitemMapper.getEntity(lstDelIds.get(i), busInvoiceEntity.getTenantid());
                    // 加上引用检测
                    List<String> lstcite = getCiteBillName(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
                    if (lstcite.size() > 0) {
                        throw new RuntimeException("发票已被引用,禁止删除:" + lstcite);
                    }

                    this.busInvoiceitemMapper.delete(lstDelIds.get(i), busInvoiceEntity.getTenantid());
                    //更新订单已发货 Eric20211213
                    if (dpPojo.getDelitype().equals("发出商品") || dpPojo.getDelitype().equals("订单退货") ||
                            dpPojo.getDelitype().equals("其他发货") || dpPojo.getDelitype().equals("其他退货") ||
                            dpPojo.getDelitype().equals("退货返工") || dpPojo.getDelitype().equals("返工补发")) {
                        this.busInvoiceMapper.updateDeliInvoFinish(dpPojo.getDeliitemid(), dpPojo.getDeliuid(), tid);
                        // 超数检查
                        BusDelieryitemPojo busDelieryitemPojo = this.busDelieryitemMapper.getEntity(lst.get(i).getDeliitemid(), tid);
                        if (busDelieryitemPojo != null) {
                            if (busDelieryitemPojo.getInvoqty() > busDelieryitemPojo.getQuantity()) {
                                int Rowno = i + 1;
                                throw new RuntimeException(Rowno + "行,开票总数:" + busDelieryitemPojo.getInvoqty() + "超出销售数:" + busDelieryitemPojo.getQuantity());
                            }
                        } else {
                            throw new RuntimeException("关联单据丢失:" + lst.get(i).getDeliuid());
                        }
                        this.busInvoiceMapper.updateDeliInvoCount(dpPojo.getDeliitemid(), dpPojo.getDeliuid(), tid);
                        if (isNotBlank(lst.get(i).getMachitemid())&& isNotBlank(lst.get(i).getMachuid())) {
                            // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
                            syncMachAboutInvo(lst.get(i).getMachitemid(), lst.get(i).getMachuid(), tid);
                        }
                    } else {
                        this.busInvoiceMapper.updateDeduInvoFinish(dpPojo.getDeliitemid(), dpPojo.getDeliuid(), tid);
                        // 超数检查
                        BusDeductionitemPojo busDeductionitemPojo = this.busDeductionitemMapper.getEntity(lst.get(i).getDeliitemid(), tid);
                        if (busDeductionitemPojo != null) {
                            if (busDeductionitemPojo.getInvoqty() > busDeductionitemPojo.getQuantity()) {
                                int Rowno = i + 1;
                                throw new RuntimeException(Rowno + "行,开票总数:" + busDeductionitemPojo.getInvoqty() + "超出扣款数:" + busDeductionitemPojo.getQuantity());
                            }
                        } else {
                            throw new RuntimeException("关联单据丢失:" + lst.get(i).getDeliuid());
                        }
                        this.busInvoiceMapper.updateDeduInvoCount(dpPojo.getDeliitemid(), dpPojo.getDeliuid(), tid);
                    }


                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    BusInvoiceitemEntity busInvoiceitemEntity = new BusInvoiceitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        BusInvoiceitemPojo itemPojo = this.busInvoiceitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, busInvoiceitemEntity);
                        //设置id和Pid
                        busInvoiceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busInvoiceitemEntity.setPid(busInvoiceEntity.getId());  // 主表 id
                        busInvoiceitemEntity.setTenantid(tid);   // 租户id
                        busInvoiceitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busInvoiceitemMapper.insert(busInvoiceitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), busInvoiceitemEntity);
                        busInvoiceitemEntity.setTenantid(tid);
                        this.busInvoiceitemMapper.update(busInvoiceitemEntity);
                    }

                    //更新订单已发货 Eric20211213
                    if (!lst.get(i).getDeliitemid().isEmpty() && !lst.get(i).getDeliuid().isEmpty()) {
                        if (lst.get(i).getDelitype().equals("发出商品") || lst.get(i).getDelitype().equals("订单退货") ||
                                lst.get(i).getDelitype().equals("其他发货") || lst.get(i).getDelitype().equals("其他退货") ||
                                lst.get(i).getDelitype().equals("退货返工") || lst.get(i).getDelitype().equals("返工补发")) {
                            this.busInvoiceMapper.updateDeliInvoFinish(lst.get(i).getDeliitemid(), lst.get(i).getDeliuid(), tid);
                            // 超数检查
                            BusDelieryitemPojo busDelieryitemPojo = this.busDelieryitemMapper.getEntity(lst.get(i).getDeliitemid(), tid);
                            if (busDelieryitemPojo != null) {
                                if (busDelieryitemPojo.getInvoqty() > busDelieryitemPojo.getQuantity()) {
                                    int Rowno = i + 1;
                                    throw new RuntimeException(Rowno + "行,开票总数:" + busDelieryitemPojo.getInvoqty() + "超出销售数:" + busDelieryitemPojo.getQuantity());
                                }
                            } else {
                                throw new RuntimeException("关联单据丢失:" + lst.get(i).getDeliuid());
                            }
                            this.busInvoiceMapper.updateDeliInvoCount(lst.get(i).getDeliitemid(), lst.get(i).getDeliuid(), tid);
                            if (isNotBlank(lst.get(i).getMachitemid())&& isNotBlank(lst.get(i).getMachuid())) {
                                // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
                                syncMachAboutInvo(lst.get(i).getMachitemid(), lst.get(i).getMachuid(), tid);
                            }
                        } else {
                            this.busInvoiceMapper.updateDeduInvoFinish(lst.get(i).getDeliitemid(), lst.get(i).getDeliuid(), tid);
                            // 超数检查
                            BusDeductionitemPojo busDeductionitemPojo = this.busDeductionitemMapper.getEntity(lst.get(i).getDeliitemid(), tid);
                            if (busDeductionitemPojo != null) {
                                if (busDeductionitemPojo.getInvoqty() > busDeductionitemPojo.getQuantity()) {
                                    int Rowno = i + 1;
                                    throw new RuntimeException(Rowno + "行,开票总数:" + busDeductionitemPojo.getInvoqty() + "超出扣款数:" + busDeductionitemPojo.getQuantity());
                                }
                            } else {
                                throw new RuntimeException("关联单据丢失:" + lst.get(i).getDeliuid());
                            }
                            this.busInvoiceMapper.updateDeduInvoCount(lst.get(i).getDeliitemid(), lst.get(i).getDeliuid(), tid);
                        }
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BusInvoicePojo busInvoicePojo = this.getBillEntity(key, tid);
        List<String> lstcite = getCiteBillName(busInvoicePojo.getId(), busInvoicePojo.getTenantid());
        if (lstcite.size() > 0) {
            throw new RuntimeException("发票已被引用,禁止删:" + lstcite);
        }
        //Item子表处理
        List<BusInvoiceitemPojo> lst = busInvoicePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (BusInvoiceitemPojo busInvoiceitemPojo : lst) {
                BusInvoiceitemPojo dpPojo = this.busInvoiceitemMapper.getEntity(busInvoiceitemPojo.getId(), tid);
                this.busInvoiceitemMapper.delete(busInvoiceitemPojo.getId(), tid);
                if (dpPojo.getDelitype().equals("发出商品") || dpPojo.getDelitype().equals("订单退货") ||
                        dpPojo.getDelitype().equals("其他发货") || dpPojo.getDelitype().equals("其他退货") ||
                        dpPojo.getDelitype().equals("退货返工") || dpPojo.getDelitype().equals("返工补发")) {
                    this.busInvoiceMapper.updateDeliInvoFinish(dpPojo.getDeliitemid(), dpPojo.getDeliuid(), busInvoicePojo.getTenantid());
                    this.busInvoiceMapper.updateDeliInvoCount(dpPojo.getDeliitemid(), dpPojo.getDeliuid(), busInvoicePojo.getTenantid());
                    //更新采购单的发票数量和开票行数 20231226
                    if (isNotBlank(busInvoiceitemPojo.getMachitemid()) && isNotBlank(busInvoiceitemPojo.getMachuid())) {
                        // 同步销售订单的发票相关字段: Bus_MachiningItem.InvoQty,AvgInvoAmt   Bus_Machining.InvoCount,InvoAmt
                        syncMachAboutInvo(busInvoiceitemPojo.getMachitemid(), busInvoiceitemPojo.getMachuid(), tid);
                    }
                } else {
                    this.busInvoiceMapper.updateDeduInvoFinish(dpPojo.getDeliitemid(), dpPojo.getDeliuid(), busInvoicePojo.getTenantid());
                    this.busInvoiceMapper.updateDeduInvoCount(dpPojo.getDeliitemid(), dpPojo.getDeliuid(), busInvoicePojo.getTenantid());
                }
            }
        }
        return this.busInvoiceMapper.delete(key, tid);
    }


    /**
     * 审核数据
     *
     * @param busInvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusInvoicePojo approval(BusInvoicePojo busInvoicePojo) {
        //主表更改
        BusInvoiceEntity busInvoiceEntity = new BusInvoiceEntity();
        BeanUtils.copyProperties(busInvoicePojo, busInvoiceEntity);
        this.busInvoiceMapper.approval(busInvoiceEntity);
        //返回Bill实例
        return this.getBillEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
    }

    /**
     * 作废数据
     *
     * @param key 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusInvoicePojo disannul(String key, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        BusInvoicePojo dbPojo = this.busInvoiceMapper.getEntity(key, loginUser.getTenantid());
        if (dbPojo.getDisannulmark() != type) {
            if (dbPojo.getClosed() == 1) {
                throw new RuntimeException(dbPojo.getRefno() + "已关闭,禁止作废操作");
            }
            if (dbPojo.getReceipted() > 0) {
                throw new RuntimeException(dbPojo.getRefno() + "已被引用,禁止作废操作");
            }
        } else {
            throw new RuntimeException(dbPojo.getRefno() + "已" + strType + ",无需操作");
        }
        //主表更改
        BusInvoiceEntity busInvoiceEntity = new BusInvoiceEntity();
        busInvoiceEntity.setId(key);
        busInvoiceEntity.setDisannulmark(type);
        busInvoiceEntity.setLister(loginUser.getRealname());
        busInvoiceEntity.setListerid(loginUser.getUserid());
        busInvoiceEntity.setModifydate(new Date());
        busInvoiceEntity.setTenantid(loginUser.getTenantid());
        this.busInvoiceMapper.update(busInvoiceEntity);
        //返回Bill实例
        return this.getBillEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());

    }

    /**
     * 作废数据
     *
     * @param key 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusInvoicePojo closed(String key, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        BusInvoicePojo dbPojo = this.busInvoiceMapper.getEntity(key, loginUser.getTenantid());
        if (dbPojo.getClosed() != type) {
            if (dbPojo.getDisannulmark() == 1) {
                throw new RuntimeException(dbPojo.getRefno() + "已作废,禁止操作");
            }
        } else {
            throw new RuntimeException(dbPojo.getRefno() + "已" + strType + ",无需操作");
        }

        //主表更改
        BusInvoiceEntity busInvoiceEntity = new BusInvoiceEntity();
        busInvoiceEntity.setId(key);
        busInvoiceEntity.setClosed(type);
        busInvoiceEntity.setLister(loginUser.getRealname());
        busInvoiceEntity.setListerid(loginUser.getUserid());
        busInvoiceEntity.setModifydate(new Date());
        busInvoiceEntity.setTenantid(loginUser.getTenantid());
        this.busInvoiceMapper.update(busInvoiceEntity);
        //返回Bill实例
        return this.getBillEntity(busInvoiceEntity.getId(), busInvoiceEntity.getTenantid());
    }


    @Override
    // 查询Item是否被引用
    public List<String> getCiteBillName(String key, String tid) {
        return this.busInvoiceMapper.getCiteBillName(key, tid);
    }

    @Override
    // 拉取某个客户所有待对账项目，送货+扣款
    public List<BusInvoiceitemPojo> pullItem(QueryParam queryParam, String groupid) {
        return this.busInvoiceMapper.pullItem(queryParam.getDateRange().getStartDate(), queryParam.getDateRange().getEndDate(), groupid, queryParam.getTenantid());
    }

}
