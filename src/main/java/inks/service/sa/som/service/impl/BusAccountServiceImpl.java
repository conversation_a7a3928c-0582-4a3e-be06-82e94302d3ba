package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.som.domain.BusAccountEntity;
import inks.service.sa.som.domain.BusAccountarapEntity;
import inks.service.sa.som.domain.BusAccountinvoEntity;
import inks.service.sa.som.domain.BusAccountitemEntity;
import inks.service.sa.som.domain.constant.MyConstant;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.BusAccountMapper;
import inks.service.sa.som.mapper.BusAccountarapMapper;
import inks.service.sa.som.mapper.BusAccountinvoMapper;
import inks.service.sa.som.mapper.BusAccountitemMapper;
import inks.service.sa.som.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 销售账单(BusAccount)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-03 14:28:09
 */
@Service("busAccountService")
public class BusAccountServiceImpl implements BusAccountService {
    @Resource
    private BusAccountMapper busAccountMapper;

    @Resource
    private BusAccountitemMapper busAccountitemMapper;
    @Resource
    private BusAccountitemService busAccountitemService;
    @Resource
    private BusAccountinvoMapper busAccountinvoMapper;
    @Resource
    private BusAccountinvoService busAccountinvoService;
    @Resource
    private BusAccountarapMapper busAccountarapMapper;
    @Resource
    private BusAccountarapService busAccountarapService;
    @Resource
    private BusAccountrecService busAccountrecService;
    @Resource
    private SaRedisService saRedisService;


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusAccountPojo getEntity(String key, String tid) {
        return this.busAccountMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusAccountitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountitemdetailPojo> lst = busAccountMapper.getPageList(queryParam);
            PageInfo<BusAccountitemdetailPojo> pageInfo = new PageInfo<BusAccountitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusAccountPojo getBillEntity(String key, String tid) {
        try {
            // 读取主表
            BusAccountPojo busAccountPojo = this.busAccountMapper.getEntity(key, tid);
            // 读取子表
            busAccountPojo.setItem(busAccountitemMapper.getList(busAccountPojo.getId(), busAccountPojo.getTenantid()));
            // 读取发货to发票
            busAccountPojo.setInvo(busAccountinvoMapper.getList(busAccountPojo.getId(), busAccountPojo.getTenantid()));
            // 读取应收应付
            busAccountPojo.setArap(busAccountarapMapper.getList(busAccountPojo.getId(), busAccountPojo.getTenantid()));
            return busAccountPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusAccountPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountPojo> lst = busAccountMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(busAccountitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setInvo(busAccountinvoMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
                lst.get(i).setArap(busAccountarapMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<BusAccountPojo> pageInfo = new PageInfo<BusAccountPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusAccountPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountPojo> lst = busAccountMapper.getPageTh(queryParam);
            PageInfo<BusAccountPojo> pageInfo = new PageInfo<BusAccountPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param busAccountPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusAccountPojo insert(BusAccountPojo busAccountPojo) {
//初始化NULL字段
        if (busAccountPojo.getRefno() == null) busAccountPojo.setRefno("");
        if (busAccountPojo.getBilltype() == null) busAccountPojo.setBilltype("");
        if (busAccountPojo.getBilldate() == null) busAccountPojo.setBilldate(new Date());
        if (busAccountPojo.getBilltitle() == null) busAccountPojo.setBilltitle("");
        if (busAccountPojo.getProjectid() == null) busAccountPojo.setProjectid("");
        if (busAccountPojo.getProjcode() == null) busAccountPojo.setProjcode("");
        if (busAccountPojo.getProjname() == null) busAccountPojo.setProjname("");
        if (busAccountPojo.getGroupid() == null) busAccountPojo.setGroupid("");
        if (busAccountPojo.getCarryyear() == null) busAccountPojo.setCarryyear(0);
        if (busAccountPojo.getCarrymonth() == null) busAccountPojo.setCarrymonth(0);
        if (busAccountPojo.getRownum() == null) busAccountPojo.setRownum(0);
        if (busAccountPojo.getStartdate() == null) busAccountPojo.setStartdate(new Date());
        if (busAccountPojo.getEnddate() == null) busAccountPojo.setEnddate(new Date());
        if (busAccountPojo.getOperator() == null) busAccountPojo.setOperator("");
        if (busAccountPojo.getOperatorid() == null) busAccountPojo.setOperatorid("");
        if (busAccountPojo.getSummary() == null) busAccountPojo.setSummary("");
        if (busAccountPojo.getCreateby() == null) busAccountPojo.setCreateby("");
        if (busAccountPojo.getCreatebyid() == null) busAccountPojo.setCreatebyid("");
        if (busAccountPojo.getCreatedate() == null) busAccountPojo.setCreatedate(new Date());
        if (busAccountPojo.getLister() == null) busAccountPojo.setLister("");
        if (busAccountPojo.getListerid() == null) busAccountPojo.setListerid("");
        if (busAccountPojo.getModifydate() == null) busAccountPojo.setModifydate(new Date());
        if (busAccountPojo.getBillopenamount() == null) busAccountPojo.setBillopenamount(0D);
        if (busAccountPojo.getBillinamount() == null) busAccountPojo.setBillinamount(0D);
        if (busAccountPojo.getBilloutamount() == null) busAccountPojo.setBilloutamount(0D);
        if (busAccountPojo.getBillcloseamount() == null) busAccountPojo.setBillcloseamount(0D);
        if (busAccountPojo.getPrintcount() == null) busAccountPojo.setPrintcount(0);
        if (busAccountPojo.getInvoopenamount() == null) busAccountPojo.setInvoopenamount(0D);
        if (busAccountPojo.getInvoinamount() == null) busAccountPojo.setInvoinamount(0D);
        if (busAccountPojo.getInvooutamount() == null) busAccountPojo.setInvooutamount(0D);
        if (busAccountPojo.getInvocloseamount() == null) busAccountPojo.setInvocloseamount(0D);
        if (busAccountPojo.getArapopenamount() == null) busAccountPojo.setArapopenamount(0D);
        if (busAccountPojo.getArapinamount() == null) busAccountPojo.setArapinamount(0D);
        if (busAccountPojo.getArapoutamount() == null) busAccountPojo.setArapoutamount(0D);
        if (busAccountPojo.getArapcloseamount() == null) busAccountPojo.setArapcloseamount(0D);
        if (busAccountPojo.getCustom1() == null) busAccountPojo.setCustom1("");
        if (busAccountPojo.getCustom2() == null) busAccountPojo.setCustom2("");
        if (busAccountPojo.getCustom3() == null) busAccountPojo.setCustom3("");
        if (busAccountPojo.getCustom4() == null) busAccountPojo.setCustom4("");
        if (busAccountPojo.getCustom5() == null) busAccountPojo.setCustom5("");
        if (busAccountPojo.getCustom6() == null) busAccountPojo.setCustom6("");
        if (busAccountPojo.getCustom7() == null) busAccountPojo.setCustom7("");
        if (busAccountPojo.getCustom8() == null) busAccountPojo.setCustom8("");
        if (busAccountPojo.getCustom9() == null) busAccountPojo.setCustom9("");
        if (busAccountPojo.getCustom10() == null) busAccountPojo.setCustom10("");
        if (busAccountPojo.getTenantid() == null) busAccountPojo.setTenantid("");
        if (busAccountPojo.getTenantname() == null) busAccountPojo.setTenantname("");
        if (busAccountPojo.getRevision() == null) busAccountPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        BusAccountEntity busAccountEntity = new BusAccountEntity();
        BeanUtils.copyProperties(busAccountPojo, busAccountEntity);
        //设置id和新建日期
        busAccountEntity.setId(id);
        busAccountEntity.setRevision(1);  //乐观锁
        //插入主表
        this.busAccountMapper.insert(busAccountEntity);
        //Item子表处理
        List<BusAccountitemPojo> lst = busAccountPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                BusAccountitemPojo itemPojo = this.busAccountitemService.clearNull(lst.get(i));
                BusAccountitemEntity busAccountitemEntity = new BusAccountitemEntity();
                BeanUtils.copyProperties(itemPojo, busAccountitemEntity);
                //设置id和Pid
                busAccountitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busAccountitemEntity.setPid(id);
                busAccountitemEntity.setTenantid(busAccountPojo.getTenantid());
                busAccountitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busAccountitemMapper.insert(busAccountitemEntity);
            }
        }
        //Invo子表处理
        List<BusAccountinvoPojo> lstInvo = busAccountPojo.getInvo();
        if (lstInvo != null) {
            //循环每个Invo子表
            for (int i = 0; i < lstInvo.size(); i++) {
                //初始化Invo的NULL
                BusAccountinvoPojo invoPojo = this.busAccountinvoService.clearNull(lstInvo.get(i));
                BusAccountinvoEntity busAccountinvoEntity = new BusAccountinvoEntity();
                BeanUtils.copyProperties(invoPojo, busAccountinvoEntity);
                //设置id和Pid
                busAccountinvoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busAccountinvoEntity.setPid(id);
                busAccountinvoEntity.setTenantid(busAccountPojo.getTenantid());
                busAccountinvoEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busAccountinvoMapper.insert(busAccountinvoEntity);
            }
        }
        //Arap子表处理
        List<BusAccountarapPojo> lstArap = busAccountPojo.getArap();
        if (lstArap != null) {
            //循环每个Arap子表
            for (int i = 0; i < lstArap.size(); i++) {
                //初始化Arap的NULL
                BusAccountarapPojo arapPojo = this.busAccountarapService.clearNull(lstArap.get(i));
                BusAccountarapEntity busAccountarapEntity = new BusAccountarapEntity();
                BeanUtils.copyProperties(arapPojo, busAccountarapEntity);
                //设置id和Pid
                busAccountarapEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                busAccountarapEntity.setPid(id);
                busAccountarapEntity.setTenantid(busAccountPojo.getTenantid());
                busAccountarapEntity.setRevision(1);  //乐观锁
                //插入子表
                this.busAccountarapMapper.insert(busAccountarapEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(busAccountEntity.getId(), busAccountEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param busAccountPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public BusAccountPojo update(BusAccountPojo busAccountPojo) {
        //主表更改
        BusAccountEntity busAccountEntity = new BusAccountEntity();
        BeanUtils.copyProperties(busAccountPojo, busAccountEntity);
        this.busAccountMapper.update(busAccountEntity);
        //Item子表处理
        if (busAccountPojo.getItem() != null) {
            //Item子表处理
            List<BusAccountitemPojo> lst = busAccountPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = busAccountMapper.getDelItemIds(busAccountPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.busAccountitemMapper.delete(lstDelIds.get(i), busAccountEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    BusAccountitemEntity busAccountitemEntity = new BusAccountitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        BusAccountitemPojo itemPojo = this.busAccountitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, busAccountitemEntity);
                        //设置id和Pid
                        busAccountitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        busAccountitemEntity.setPid(busAccountEntity.getId());  // 主表 id
                        busAccountitemEntity.setTenantid(busAccountPojo.getTenantid());   // 租户id
                        busAccountitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busAccountitemMapper.insert(busAccountitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), busAccountitemEntity);
                        busAccountitemEntity.setTenantid(busAccountPojo.getTenantid());
                        this.busAccountitemMapper.update(busAccountitemEntity);
                    }
                }
            }
        }

        //Invo子表处理
        if (busAccountPojo.getInvo() != null) {
            //Invo子表处理
            List<BusAccountinvoPojo> lstInvo = busAccountPojo.getInvo();
            //获取被删除的Invo
            List<String> lstDelIds = busAccountMapper.getDelInvoIds(busAccountPojo);
            if (lstDelIds != null) {
                //循环每个删除Invo子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.busAccountinvoMapper.delete(lstDelIds.get(i), busAccountEntity.getTenantid());
                }
            }
            if (lstInvo != null) {
                //循环每个Invo子表
                for (int i = 0; i < lstInvo.size(); i++) {
                    BusAccountinvoEntity busAccountinvoEntity = new BusAccountinvoEntity();
                    if ("".equals(lstInvo.get(i).getId()) || lstInvo.get(i).getId() == null) {
                        //初始化Invo的NULL
                        BusAccountinvoPojo invoPojo = this.busAccountinvoService.clearNull(lstInvo.get(i));
                        BeanUtils.copyProperties(invoPojo, busAccountinvoEntity);
                        //设置id和Pid
                        busAccountinvoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // invo id
                        busAccountinvoEntity.setPid(busAccountEntity.getId());  // 主表 id
                        busAccountinvoEntity.setTenantid(busAccountPojo.getTenantid());   // 租户id
                        busAccountinvoEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busAccountinvoMapper.insert(busAccountinvoEntity);
                    } else {
                        BeanUtils.copyProperties(lstInvo.get(i), busAccountinvoEntity);
                        busAccountinvoEntity.setTenantid(busAccountPojo.getTenantid());
                        this.busAccountinvoMapper.update(busAccountinvoEntity);
                    }
                }
            }
        }

        //ArAp子表处理
        if (busAccountPojo.getArap() != null) {
            //ArAp子表处理
            List<BusAccountarapPojo> lstArap = busAccountPojo.getArap();
            //获取被删除的ArAp
            List<String> lstDelIds = busAccountMapper.getDelArapIds(busAccountPojo);
            if (lstDelIds != null) {
                //循环每个删除ArAp子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.busAccountarapMapper.delete(lstDelIds.get(i), busAccountEntity.getTenantid());
                }
            }
            if (lstArap != null) {
                //循环每个ArAp子表
                for (int i = 0; i < lstArap.size(); i++) {
                    BusAccountarapEntity busAccountarapEntity = new BusAccountarapEntity();
                    if ("".equals(lstArap.get(i).getId()) || lstArap.get(i).getId() == null) {
                        //初始化ArAp的NULL
                        BusAccountarapPojo arapPojo = this.busAccountarapService.clearNull(lstArap.get(i));
                        BeanUtils.copyProperties(arapPojo, busAccountarapEntity);
                        //设置id和Pid
                        busAccountarapEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // arap id
                        busAccountarapEntity.setPid(busAccountEntity.getId());  // 主表 id
                        busAccountarapEntity.setTenantid(busAccountPojo.getTenantid());   // 租户id
                        busAccountarapEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.busAccountarapMapper.insert(busAccountarapEntity);
                    } else {
                        BeanUtils.copyProperties(lstArap.get(i), busAccountarapEntity);
                        busAccountarapEntity.setTenantid(busAccountPojo.getTenantid());
                        this.busAccountarapMapper.update(busAccountarapEntity);
                    }
                }
            }
        }


        //返回Bill实例
        return this.getBillEntity(busAccountEntity.getId(), busAccountEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        BusAccountPojo busAccountPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<BusAccountitemPojo> lst = busAccountPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.busAccountitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        //Invo子表处理
        List<BusAccountinvoPojo> lstInvo = busAccountPojo.getInvo();
        if (lstInvo != null) {
            //循环每个删除invo子表
            for (int i = 0; i < lstInvo.size(); i++) {
                this.busAccountinvoMapper.delete(lstInvo.get(i).getId(), tid);
            }
        }
        //ArAp子表处理
        List<BusAccountarapPojo> lstArap = busAccountPojo.getArap();
        if (lstArap != null) {
            //循环每个删除arap子表
            for (int i = 0; i < lstArap.size(); i++) {
                this.busAccountarapMapper.delete(lstArap.get(i).getId(), tid);
            }
        }
        //删除主表
        return this.busAccountMapper.delete(key, tid);
    }

    /**
     * 新增数据
     *
     * @param busAccountPojo 实例对象
     * @return 实例对象
     */
    @Override
    public List<BusAccountitemPojo> pullItemList(BusAccountPojo busAccountPojo) {
        //生成id
//        AppWorkgroupPojo wgPojo = this.appWorkgroupService.getEntity(busAccountPojo.getGroupid(), busAccountPojo.getTenantid());
//        if (wgPojo == null) {
//            throw new RuntimeException("未找到对应客户信息");
//        }

        // 查询当前客户之前的销售账单
        BusAccountPojo busAccountMaxPojo = this.busAccountMapper.getMaxEntityByGroup(busAccountPojo.getGroupid(), busAccountPojo.getTenantid());
        // 初始化时间
        Date dtStart = DateUtils.parseDate("2022-01-01");
        Double openAmount = 0D;
        if (busAccountMaxPojo != null) {
//            throw new RuntimeException("请先初始化客户账单");
            dtStart = busAccountMaxPojo.getEnddate();
            openAmount = busAccountMaxPojo.getBillcloseamount();
        }
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(busAccountPojo.getEnddate()) + " 23:59:59");

        QueryParam queryParam = new QueryParam();
        String strFilter = " and Groupid='" + busAccountPojo.getGroupid() + "'";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(2000);
        queryParam.setTenantid(busAccountPojo.getTenantid());
        List<BusAccountitemPojo> lstitem = this.busAccountMapper.pullItemList(queryParam);
        if (lstitem.size() == 0) {
            //  throw new RuntimeException("未找到销售与收款信息");
            BusAccountitemPojo newitem = new BusAccountitemPojo();
            newitem.setOpenamount(0D);
            newitem.setInamount(0D);
            newitem.setOutamount(0D);
            newitem.setCloseamount(0D);
            lstitem.add(newitem);
        }


        // 改为BigDec
        BigDecimal decinAmt = BigDecimal.valueOf(0);
        BigDecimal decoutAmt = BigDecimal.valueOf(0);
        BigDecimal itemopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal itemcloseAmt = BigDecimal.valueOf(0);

        for (int i = 0; i < lstitem.size(); i++) {
            lstitem.get(i).setRownum(i);
            BigDecimal iteminAmt = BigDecimal.valueOf(lstitem.get(i).getInamount());
            BigDecimal itemoutAmt = BigDecimal.valueOf(lstitem.get(i).getOutamount());
            decinAmt = decinAmt.add(iteminAmt);
            decoutAmt = decoutAmt.add(itemoutAmt);
            // 计算行期末
            itemcloseAmt = itemopenAmt.add(iteminAmt).subtract(itemoutAmt);
            lstitem.get(i).setCloseamount(itemcloseAmt.doubleValue());
            // 更新行期初，为下一行计算用
            itemopenAmt = BigDecimal.valueOf(itemcloseAmt.doubleValue());
        }

        // 填入 初始，初末
        lstitem.get(0).setOpenamount(openAmount);

        BigDecimal decopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal deccloseAmt = BigDecimal.valueOf(0);
        deccloseAmt = deccloseAmt.add(decopenAmt).add(decinAmt).subtract(decoutAmt);

        lstitem.get(lstitem.size() - 1).setCloseamount(deccloseAmt.doubleValue());

        //返回Bill实例
        return lstitem;

    }

    @Override
    public List<BusAccountinvoPojo> pullInvoList(BusAccountPojo busAccountPojo) {
        // 查询当前客户之前的销售账单
        BusAccountPojo busAccountMaxPojo = this.busAccountMapper.getMaxEntityByGroup(busAccountPojo.getGroupid(), busAccountPojo.getTenantid());
        // 初始化时间
        Date dtStart = DateUtils.parseDate("2022-01-01");
        Double openAmount = 0D;
        if (busAccountMaxPojo != null) {
            dtStart = busAccountMaxPojo.getEnddate();
            openAmount = busAccountMaxPojo.getBillcloseamount();
        }
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(busAccountPojo.getEnddate()) + " 23:59:59");

        QueryParam queryParam = new QueryParam();
        String strFilter = " and Groupid='" + busAccountPojo.getGroupid() + "'";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(2000);
        queryParam.setTenantid(busAccountPojo.getTenantid());
        List<BusAccountinvoPojo> lstinvoitem = this.busAccountMapper.pullInvoList(queryParam);
        if (lstinvoitem.isEmpty()) {
            BusAccountinvoPojo newinvoitem = new BusAccountinvoPojo();
            newinvoitem.setOpenamount(0D);
            newinvoitem.setInamount(0D);
            newinvoitem.setOutamount(0D);
            newinvoitem.setCloseamount(0D);
            lstinvoitem.add(newinvoitem);
        }

        BigDecimal decinAmt = BigDecimal.ZERO;
        BigDecimal decoutAmt = BigDecimal.ZERO;
        BigDecimal itemopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal itemcloseAmt = BigDecimal.ZERO;

        for (int i = 0; i < lstinvoitem.size(); i++) {
            lstinvoitem.get(i).setRownum(i);
            BigDecimal iteminAmt = BigDecimal.valueOf(lstinvoitem.get(i).getInamount());
            BigDecimal itemoutAmt = BigDecimal.valueOf(lstinvoitem.get(i).getOutamount());
            decinAmt = decinAmt.add(iteminAmt);
            decoutAmt = decoutAmt.add(itemoutAmt);
            // 计算行期末
            itemcloseAmt = itemopenAmt.add(iteminAmt).subtract(itemoutAmt);
            lstinvoitem.get(i).setCloseamount(itemcloseAmt.doubleValue());
            // 更新行期初，为下一行计算用
            itemopenAmt = BigDecimal.valueOf(itemcloseAmt.doubleValue());
        }
        // 填入 初始，初末
        lstinvoitem.get(0).setOpenamount(openAmount);

        BigDecimal decopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal deccloseAmt = BigDecimal.ZERO;
        deccloseAmt = deccloseAmt.add(decopenAmt).add(decinAmt).subtract(decoutAmt);
        // 最后一行 填入期末
        lstinvoitem.get(lstinvoitem.size() - 1).setCloseamount(deccloseAmt.doubleValue());
        return lstinvoitem;
    }


    @Override
    public List<BusAccountarapPojo> pullArapList(BusAccountPojo busAccountPojo) {
        // 查询当前客户之前的销售账单
        BusAccountPojo busAccountMaxPojo = this.busAccountMapper.getMaxEntityByGroup(busAccountPojo.getGroupid(), busAccountPojo.getTenantid());
        // 初始化时间
        Date dtStart = DateUtils.parseDate("2022-01-01");
        Double openAmount = 0D;
        if (busAccountMaxPojo != null) {
            dtStart = busAccountMaxPojo.getEnddate();
            openAmount = busAccountMaxPojo.getBillcloseamount();
        }
        Date dtEnd = DateUtils.parseDate(DateUtils.dateTime(busAccountPojo.getEnddate()) + " 23:59:59");

        QueryParam queryParam = new QueryParam();
        String strFilter = " and Groupid='" + busAccountPojo.getGroupid() + "'";
        queryParam.setFilterstr(strFilter);
        queryParam.setOrderBy("BillDate");
        queryParam.setDateRange(new DateRange("BillDate", dtStart, dtEnd));
        queryParam.setOrderType(0);
        queryParam.setPageNum(1);
        queryParam.setPageSize(2000);
        queryParam.setTenantid(busAccountPojo.getTenantid());
        List<BusAccountarapPojo> lstarapitem = this.busAccountMapper.pullArapList(queryParam);
        if (lstarapitem.isEmpty()) {
            BusAccountarapPojo newarapitem = new BusAccountarapPojo();
            newarapitem.setOpenamount(0D);
            newarapitem.setInamount(0D);
            newarapitem.setOutamount(0D);
            newarapitem.setCloseamount(0D);
            lstarapitem.add(newarapitem);
        }

        BigDecimal decinAmt = BigDecimal.ZERO;
        BigDecimal decoutAmt = BigDecimal.ZERO;
        BigDecimal itemopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal itemcloseAmt = BigDecimal.ZERO;

        for (int i = 0; i < lstarapitem.size(); i++) {
            lstarapitem.get(i).setRownum(i);
            BigDecimal iteminAmt = BigDecimal.valueOf(lstarapitem.get(i).getInamount());
            BigDecimal itemoutAmt = BigDecimal.valueOf(lstarapitem.get(i).getOutamount());
            decinAmt = decinAmt.add(iteminAmt);
            decoutAmt = decoutAmt.add(itemoutAmt);
            // 计算行期末
            itemcloseAmt = itemopenAmt.add(iteminAmt).subtract(itemoutAmt);
            lstarapitem.get(i).setCloseamount(itemcloseAmt.doubleValue());
            // 更新行期初，为下一行计算用
            itemopenAmt = BigDecimal.valueOf(itemcloseAmt.doubleValue());
        }
        // 第一行填入初始，初末
        lstarapitem.get(0).setOpenamount(openAmount);

        BigDecimal decopenAmt = BigDecimal.valueOf(openAmount);
        BigDecimal deccloseAmt = BigDecimal.ZERO;
        deccloseAmt = deccloseAmt.add(decopenAmt).add(decinAmt).subtract(decoutAmt);
        // 最后一行 填入期末
        lstarapitem.get(lstarapitem.size() - 1).setCloseamount(deccloseAmt.doubleValue());
        return lstarapitem;
    }


    // 批量生成账单
    @Override
    @Transactional
    public int batchCreate(BusAccountPojo busAccountPojo) {
        try {
            int num = 0;
            String tid = busAccountPojo.getTenantid();
            // 获取所有客户
            List<String> lstwgIds = this.busAccountMapper.getCustomerIds(tid);

            // item改为BigDec
            BigDecimal decrecopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecoutAmt = BigDecimal.valueOf(0);
            BigDecimal decreccloseAmt = BigDecimal.valueOf(0);
            // invo改为BigDec
            BigDecimal decrecInvoopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecInvoinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecInvooutAmt = BigDecimal.valueOf(0);
            BigDecimal decrecInvocloseAmt = BigDecimal.valueOf(0);
            // arap改为BigDec
            BigDecimal decrecArapopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecArapinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecArapoutAmt = BigDecimal.valueOf(0);
            BigDecimal decrecArapcloseAmt = BigDecimal.valueOf(0);
            // 遍历计算每个客户账单
            for (String wgid : lstwgIds) {
                BusAccountPojo newPojo = new BusAccountPojo();
                BeanUtils.copyProperties(busAccountPojo, newPojo);
                newPojo.setGroupid(wgid);
                // 1.送货单to收款
                newPojo.setItem(pullItemList(newPojo));
                // 改为BigDec
                BigDecimal decopenAmt = BigDecimal.valueOf(0);
                BigDecimal decinAmt = BigDecimal.valueOf(0);
                BigDecimal decoutAmt = BigDecimal.valueOf(0);
                BigDecimal deccloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getItem().size() > 0) {
                    decopenAmt = decopenAmt.add(BigDecimal.valueOf(newPojo.getItem().get(0).getOpenamount()));
                    deccloseAmt = deccloseAmt.add(BigDecimal.valueOf(newPojo.getItem().get(newPojo.getItem().size() - 1).getCloseamount()));
                }
                for (BusAccountitemPojo itemPojo : newPojo.getItem()) {
                    decinAmt = decinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decoutAmt = decoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setBillopenamount(decopenAmt.doubleValue());
                newPojo.setBillinamount(decinAmt.doubleValue());
                newPojo.setBilloutamount(decoutAmt.doubleValue());
                newPojo.setBillcloseamount(deccloseAmt.doubleValue());

                // 2.送货单to发票
                newPojo.setInvo(pullInvoList(newPojo));
                // 改为BigDec
                BigDecimal decInvoopenAmt = BigDecimal.valueOf(0);
                BigDecimal decInvoinAmt = BigDecimal.valueOf(0);
                BigDecimal decInvooutAmt = BigDecimal.valueOf(0);
                BigDecimal decInvocloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getInvo().size() > 0) {
                    decInvoopenAmt = decInvoopenAmt.add(BigDecimal.valueOf(newPojo.getInvo().get(0).getOpenamount()));
                    decInvocloseAmt = decInvocloseAmt.add(BigDecimal.valueOf(newPojo.getInvo().get(newPojo.getInvo().size() - 1).getCloseamount()));
                }
                for (BusAccountinvoPojo itemPojo : newPojo.getInvo()) {
                    decInvoinAmt = decInvoinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decInvooutAmt = decInvooutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setInvoopenamount(decInvoopenAmt.doubleValue());
                newPojo.setInvoinamount(decInvoinAmt.doubleValue());
                newPojo.setInvooutamount(decInvooutAmt.doubleValue());
                newPojo.setInvocloseamount(decInvocloseAmt.doubleValue());

                // 3.发票to收款
                newPojo.setArap(pullArapList(newPojo));
                // 改为BigDec
                BigDecimal decArapopenAmt = BigDecimal.valueOf(0);
                BigDecimal decArapinAmt = BigDecimal.valueOf(0);
                BigDecimal decArapoutAmt = BigDecimal.valueOf(0);
                BigDecimal decArapcloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getArap().size() > 0) {
                    decArapopenAmt = decArapopenAmt.add(BigDecimal.valueOf(newPojo.getArap().get(0).getOpenamount()));
                    decArapcloseAmt = decArapcloseAmt.add(BigDecimal.valueOf(newPojo.getArap().get(newPojo.getArap().size() - 1).getCloseamount()));
                }
                for (BusAccountarapPojo itemPojo : newPojo.getArap()) {
                    decArapinAmt = decArapinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decArapoutAmt = decArapoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setArapopenamount(decArapopenAmt.doubleValue());
                newPojo.setArapinamount(decArapinAmt.doubleValue());
                newPojo.setArapoutamount(decArapoutAmt.doubleValue());
                newPojo.setArapcloseamount(decArapcloseAmt.doubleValue());

                insert(newPojo);
                // 当前客户item累加
                decrecopenAmt = decrecopenAmt.add(decopenAmt);
                decrecinAmt = decrecinAmt.add(decinAmt);
                decrecoutAmt = decrecoutAmt.add(decoutAmt);
                decreccloseAmt = decreccloseAmt.add(deccloseAmt);
                // 当前客户invo累加
                decrecInvoopenAmt = decrecInvoopenAmt.add(decInvoopenAmt);
                decrecInvoinAmt = decrecInvoinAmt.add(decInvoinAmt);
                decrecInvooutAmt = decrecInvooutAmt.add(decInvooutAmt);
                decrecInvocloseAmt = decrecInvocloseAmt.add(decInvocloseAmt);
                // 当前客户arap累加
                decrecArapopenAmt = decrecArapopenAmt.add(decArapopenAmt);
                decrecArapinAmt = decrecArapinAmt.add(decArapinAmt);
                decrecArapoutAmt = decrecArapoutAmt.add(decArapoutAmt);
                decrecArapcloseAmt = decrecArapcloseAmt.add(decArapcloseAmt);
                // 下一位客户
                num++;
            }
            BusAccountrecPojo busAccountrecPojo = new BusAccountrecPojo();
            BeanUtils.copyProperties(busAccountPojo, busAccountrecPojo);
            // 设置item该月累加账单
            busAccountrecPojo.setBillopenamount(decrecopenAmt.doubleValue());
            busAccountrecPojo.setBillinamount(decrecinAmt.doubleValue());
            busAccountrecPojo.setBilloutamount(decrecoutAmt.doubleValue());
            busAccountrecPojo.setBillcloseamount(decreccloseAmt.doubleValue());
            // 设置invo该月累加账单
            busAccountrecPojo.setInvoopenamount(decrecInvoopenAmt.doubleValue());
            busAccountrecPojo.setInvoinamount(decrecInvoinAmt.doubleValue());
            busAccountrecPojo.setInvooutamount(decrecInvooutAmt.doubleValue());
            busAccountrecPojo.setInvocloseamount(decrecInvocloseAmt.doubleValue());
            // 设置arap该月累加账单
            busAccountrecPojo.setArapopenamount(decrecArapopenAmt.doubleValue());
            busAccountrecPojo.setArapinamount(decrecArapinAmt.doubleValue());
            busAccountrecPojo.setArapoutamount(decrecArapoutAmt.doubleValue());
            busAccountrecPojo.setArapcloseamount(decrecArapcloseAmt.doubleValue());
            this.busAccountrecService.insert(busAccountrecPojo);
            return num;
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }
    }

    // 批量生成账单
    @Override
    @Transactional
    public int batchInit(BusAccountPojo busAccountPojo) {
        try {
            int num = 0;
            String tid = busAccountPojo.getTenantid();
            List<String> lstwgIds = this.busAccountMapper.getCustomerIds(tid);
            for (String wgid : lstwgIds) {
                BusAccountPojo dbPojo = getMaxEntityByGroup(wgid, tid);
                // 是否已有初始化
                if (dbPojo == null) {
                    BusAccountPojo newPojo = new BusAccountPojo();
                    BeanUtils.copyProperties(busAccountPojo, newPojo);
                    newPojo.setGroupid(wgid);
                    insert(newPojo);
                    num++;
                }
            }
            return num;
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }
    }

    /**
     * 通过GroupID查询最新单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusAccountPojo getMaxEntityByGroup(String key, String tid) {
        return this.busAccountMapper.getMaxEntityByGroup(key, tid);
    }

    /**
     * 通过GroupID查询最新单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusAccountPojo getMaxBillEntityByGroup(String key, String tid) {
        BusAccountPojo busAccountPojo = this.busAccountMapper.getMaxEntityByGroup(key, tid);
        //读取子表
        busAccountPojo.setItem(busAccountitemMapper.getList(busAccountPojo.getId(), busAccountPojo.getTenantid()));
        return busAccountPojo;
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusAccountitemPojo> getMultItemList(QueryParam queryParam) {
        return this.busAccountMapper.getMultItemList(queryParam);
    }

    /**
     * 分页查询实时报表
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusAccountPojo> getNowPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusAccountPojo> lst = this.busAccountMapper.getNowPageList(queryParam);
            PageInfo<BusAccountPojo> pageInfo = new PageInfo<>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

//    //销售结转redis状态key
//    private final String BUSBATCHCREATE_CODE = "busbatchcreate_codes:";

    @Async
    @Override
    @Transactional
    public void batchCreateStart(BusAccountPojo busAccountPojo, String redisKey) {
        try {
            //设置当前计算任务进度
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100");
            missionMsg.put("msg", "任务开始处理");
            missionMsg.put("startTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.saRedisService.setCacheObject(MyConstant.BUSBATCHCREATE_CODE + redisKey, missionMsg, 10L, TimeUnit.MINUTES);

            int num = 0;
            String tid = busAccountPojo.getTenantid();
            // 获取所有客户
            List<String> lstwgIds = this.busAccountMapper.getCustomerIds(tid);

            // item改为BigDec
            BigDecimal decrecopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecoutAmt = BigDecimal.valueOf(0);
            BigDecimal decreccloseAmt = BigDecimal.valueOf(0);
            // invo改为BigDec
            BigDecimal decrecInvoopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecInvoinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecInvooutAmt = BigDecimal.valueOf(0);
            BigDecimal decrecInvocloseAmt = BigDecimal.valueOf(0);
            // arap改为BigDec
            BigDecimal decrecArapopenAmt = BigDecimal.valueOf(0);
            BigDecimal decrecArapinAmt = BigDecimal.valueOf(0);
            BigDecimal decrecArapoutAmt = BigDecimal.valueOf(0);
            BigDecimal decrecArapcloseAmt = BigDecimal.valueOf(0);
            // 遍历计算每个客户账单
            for (String wgid : lstwgIds) {
                BusAccountPojo newPojo = new BusAccountPojo();
                BeanUtils.copyProperties(busAccountPojo, newPojo);
                newPojo.setGroupid(wgid);
                // 1.送货单to收款
                newPojo.setItem(pullItemList(newPojo));
                // 改为BigDec
                BigDecimal decopenAmt = BigDecimal.valueOf(0);
                BigDecimal decinAmt = BigDecimal.valueOf(0);
                BigDecimal decoutAmt = BigDecimal.valueOf(0);
                BigDecimal deccloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getItem().size() > 0) {
                    decopenAmt = decopenAmt.add(BigDecimal.valueOf(newPojo.getItem().get(0).getOpenamount()));
                    deccloseAmt = deccloseAmt.add(BigDecimal.valueOf(newPojo.getItem().get(newPojo.getItem().size() - 1).getCloseamount()));
                }
                for (BusAccountitemPojo itemPojo : newPojo.getItem()) {
                    decinAmt = decinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decoutAmt = decoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setBillopenamount(decopenAmt.doubleValue());
                newPojo.setBillinamount(decinAmt.doubleValue());
                newPojo.setBilloutamount(decoutAmt.doubleValue());
                newPojo.setBillcloseamount(deccloseAmt.doubleValue());

                // 2.送货单to发票
                newPojo.setInvo(pullInvoList(newPojo));
                // 改为BigDec
                BigDecimal decInvoopenAmt = BigDecimal.valueOf(0);
                BigDecimal decInvoinAmt = BigDecimal.valueOf(0);
                BigDecimal decInvooutAmt = BigDecimal.valueOf(0);
                BigDecimal decInvocloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getInvo().size() > 0) {
                    decInvoopenAmt = decInvoopenAmt.add(BigDecimal.valueOf(newPojo.getInvo().get(0).getOpenamount()));
                    decInvocloseAmt = decInvocloseAmt.add(BigDecimal.valueOf(newPojo.getInvo().get(newPojo.getInvo().size() - 1).getCloseamount()));
                }
                for (BusAccountinvoPojo itemPojo : newPojo.getInvo()) {
                    decInvoinAmt = decInvoinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decInvooutAmt = decInvooutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setInvoopenamount(decInvoopenAmt.doubleValue());
                newPojo.setInvoinamount(decInvoinAmt.doubleValue());
                newPojo.setInvooutamount(decInvooutAmt.doubleValue());
                newPojo.setInvocloseamount(decInvocloseAmt.doubleValue());

                // 3.发票to收款
                newPojo.setArap(pullArapList(newPojo));
                // 改为BigDec
                BigDecimal decArapopenAmt = BigDecimal.valueOf(0);
                BigDecimal decArapinAmt = BigDecimal.valueOf(0);
                BigDecimal decArapoutAmt = BigDecimal.valueOf(0);
                BigDecimal decArapcloseAmt = BigDecimal.valueOf(0);
                if (newPojo.getArap().size() > 0) {
                    decArapopenAmt = decArapopenAmt.add(BigDecimal.valueOf(newPojo.getArap().get(0).getOpenamount()));
                    decArapcloseAmt = decArapcloseAmt.add(BigDecimal.valueOf(newPojo.getArap().get(newPojo.getArap().size() - 1).getCloseamount()));
                }
                for (BusAccountarapPojo itemPojo : newPojo.getArap()) {
                    decArapinAmt = decArapinAmt.add(BigDecimal.valueOf(itemPojo.getInamount()));
                    decArapoutAmt = decArapoutAmt.add(BigDecimal.valueOf(itemPojo.getOutamount()));
                }
                newPojo.setArapopenamount(decArapopenAmt.doubleValue());
                newPojo.setArapinamount(decArapinAmt.doubleValue());
                newPojo.setArapoutamount(decArapoutAmt.doubleValue());
                newPojo.setArapcloseamount(decArapcloseAmt.doubleValue());

                insert(newPojo);
                // 当前客户item累加
                decrecopenAmt = decrecopenAmt.add(decopenAmt);
                decrecinAmt = decrecinAmt.add(decinAmt);
                decrecoutAmt = decrecoutAmt.add(decoutAmt);
                decreccloseAmt = decreccloseAmt.add(deccloseAmt);
                // 当前客户invo累加
                decrecInvoopenAmt = decrecInvoopenAmt.add(decInvoopenAmt);
                decrecInvoinAmt = decrecInvoinAmt.add(decInvoinAmt);
                decrecInvooutAmt = decrecInvooutAmt.add(decInvooutAmt);
                decrecInvocloseAmt = decrecInvocloseAmt.add(decInvocloseAmt);
                // 当前客户arap累加
                decrecArapopenAmt = decrecArapopenAmt.add(decArapopenAmt);
                decrecArapinAmt = decrecArapinAmt.add(decArapinAmt);
                decrecArapoutAmt = decrecArapoutAmt.add(decArapoutAmt);
                decrecArapcloseAmt = decrecArapcloseAmt.add(decArapcloseAmt);
                // 下一位客户
                num++;
            }
            BusAccountrecPojo busAccountrecPojo = new BusAccountrecPojo();
            BeanUtils.copyProperties(busAccountPojo, busAccountrecPojo);
            // 设置item该月累加账单
            busAccountrecPojo.setBillopenamount(decrecopenAmt.doubleValue());
            busAccountrecPojo.setBillinamount(decrecinAmt.doubleValue());
            busAccountrecPojo.setBilloutamount(decrecoutAmt.doubleValue());
            busAccountrecPojo.setBillcloseamount(decreccloseAmt.doubleValue());
            // 设置invo该月累加账单
            busAccountrecPojo.setInvoopenamount(decrecInvoopenAmt.doubleValue());
            busAccountrecPojo.setInvoinamount(decrecInvoinAmt.doubleValue());
            busAccountrecPojo.setInvooutamount(decrecInvooutAmt.doubleValue());
            busAccountrecPojo.setInvocloseamount(decrecInvocloseAmt.doubleValue());
            // 设置arap该月累加账单
            busAccountrecPojo.setArapopenamount(decrecArapopenAmt.doubleValue());
            busAccountrecPojo.setArapinamount(decrecArapinAmt.doubleValue());
            busAccountrecPojo.setArapoutamount(decrecArapoutAmt.doubleValue());
            busAccountrecPojo.setArapcloseamount(decrecArapcloseAmt.doubleValue());
            this.busAccountrecService.insert(busAccountrecPojo);

            missionMsg.put("code", "200");//任务完成代码
            missionMsg.put("msg", "任务处理完成");
            missionMsg.put("endTime", DateUtils.parseDateToStr("yyyy-MM-dd hh:mm:ss", new Date()));
            this.saRedisService.setCacheObject(MyConstant.BUSBATCHCREATE_CODE + redisKey, missionMsg, 10L, TimeUnit.MINUTES);

        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage());
        }
    }


}
