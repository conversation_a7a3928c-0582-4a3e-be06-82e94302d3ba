package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmCostitemPojo;

import java.util.List;
/**
 * 费用明细(FmCostitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-29 15:25:30
 */
public interface FmCostitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCostitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmCostitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<FmCostitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param fmCostitemPojo 实例对象
     * @return 实例对象
     */
    FmCostitemPojo insert(FmCostitemPojo fmCostitemPojo);

    /**
     * 修改数据
     *
     * @param fmCostitempojo 实例对象
     * @return 实例对象
     */
    FmCostitemPojo update(FmCostitemPojo fmCostitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param fmCostitempojo 实例对象
     * @return 实例对象
     */
    FmCostitemPojo clearNull(FmCostitemPojo fmCostitempojo);
}
