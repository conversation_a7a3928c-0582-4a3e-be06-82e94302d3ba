package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatRequisitionPojo;
import inks.service.sa.som.domain.pojo.MatRequisitionitemPojo;
import inks.service.sa.som.domain.pojo.MatRequisitionitemdetailPojo;

import java.util.List;
import java.util.Map;

/**
 * 物料申领(MatRequisition)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-25 13:06:03
 */
public interface MatRequisitionService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatRequisitionPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatRequisitionitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatRequisitionPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatRequisitionPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatRequisitionPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matRequisitionPojo 实例对象
     * @return 实例对象
     */
    MatRequisitionPojo insert(MatRequisitionPojo matRequisitionPojo,String token);

    /**
     * 修改数据
     *
     * @param matRequisitionpojo 实例对象
     * @return 实例对象
     */
    MatRequisitionPojo update(MatRequisitionPojo matRequisitionpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param matRequisitionPojo 实例对象
     * @return 实例对象
     */
    MatRequisitionPojo approval(MatRequisitionPojo matRequisitionPojo);

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    MatRequisitionPojo disannul(List<MatRequisitionitemPojo> lst, Integer type, LoginUser loginUser);

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    MatRequisitionPojo closed(List<MatRequisitionitemPojo> lst, Integer type, LoginUser loginUser);
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Map<String, Object> getWipByWorkUid( String key, String tid);


    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Map<String, Object> getMachItem(String key, String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Map<String, Object> getWsItem(String key, String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Map<String, Object> getGoodsEntityByGoodsUid(String key,  String tid);


    /*
     *
     * <AUTHOR>
     * @description 销售单金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    PageInfo<MatRequisitionitemdetailPojo> getSumPageListByGroup(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 货品销售金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    PageInfo<MatRequisitionitemdetailPojo> getSumPageListByGoods(QueryParam queryParam);


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatRequisitionitemdetailPojo> getMachPageList(QueryParam queryParam);

    // 查询Item是否被引用
    Integer getItemCiteBillName(String key, String pid, String tid);

    void updatePrintcount(MatRequisitionPojo billPrintPojo);
}
