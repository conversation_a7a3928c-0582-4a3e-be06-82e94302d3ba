package inks.service.sa.som.service.impl;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.pojo.MatInventoryPojo;
import inks.service.sa.som.mapper.D04MBIR1Mapper;
import inks.service.sa.som.service.D04MBIR1Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class D04MBIR1ServiceImpl implements D04MBIR1Service {
    @Resource
    private D04MBIR1Mapper d04MBIR1Mapper;


    /*
    库存品金额排行
     */
    @Override
    public List<MatInventoryPojo> getSumAmtByGoodsMax(QueryParam queryParam) {
        try {
            return d04MBIR1Mapper.getSumAmtByGoodsMax(queryParam);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /*
    库存汇总：金额、数量、记录数
     */
    @Override
    public List<ChartPojo> getSumAmtQtyByStore(QueryParam queryParam) {
        try {
            return d04MBIR1Mapper.getSumAmtQtyByStore(queryParam);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getSumQtyByMonth(QueryParam queryParam) {
        try {
            return d04MBIR1Mapper.getSumQtyByMonth(queryParam);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getSumOutQtyOneMonth(QueryParam queryParam) {
        try {
            return d04MBIR1Mapper.getSumOutQtyOneMonth(queryParam);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getSumInQtyOneMonth(QueryParam queryParam) {
        try {
            return d04MBIR1Mapper.getSumInQtyOneMonth(queryParam);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getWorkshopProdValueByDay(Date startDate, Date endDate,String workshopid, String tenantid) {
        try {
            return d04MBIR1Mapper.getWorkshopProdValueByDay(startDate, endDate,workshopid, tenantid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getWorkshopProdValueByMonth(String workshopid, String tenantid) {
        try {
            return d04MBIR1Mapper.getWorkshopProdValueByMonth(workshopid, tenantid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
