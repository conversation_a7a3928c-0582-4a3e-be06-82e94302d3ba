package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.*;

import java.util.List;
import java.util.Map;

/**
 * 应付账单(BuyAccount)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-09 08:28:58
 */
public interface BuyAccountService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyAccountPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyAccountitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyAccountPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyAccountPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyAccountPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyAccountPojo 实例对象
     * @return 实例对象
     */
    BuyAccountPojo insert(BuyAccountPojo buyAccountPojo);

    /**
     * 修改数据
     *
     * @param buyAccountpojo 实例对象
     * @return 实例对象
     */
    BuyAccountPojo update(BuyAccountPojo buyAccountpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    /**
     * 新增数据
     *
     * @param busAccountPojo 实例对象
     * @return 实例对象
     */
    List<BuyAccountitemPojo> pullItemList(BuyAccountPojo buyAccountPojo);
    List<BuyAccountinvoPojo> pullInvoList(BuyAccountPojo buyAccountPojo);
    List<BuyAccountarapPojo> pullArapList(BuyAccountPojo buyAccountPojo);

    // 批量生产账单
    int batchCreate(BuyAccountPojo buyAccountPojo);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyAccountPojo getMaxEntityByGroup(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyAccountPojo getMaxBillEntityByGroup(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    List<BuyAccountitemPojo> getMultItemList(QueryParam queryParam);

    // 获取供应商实时应付款报表
    PageInfo<BuyAccountPojo> getNowPageList(QueryParam queryParam, Integer online);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    AppWgSupplierPojo getSupplierGeneral(String key, String tid);

    void batchCreateStart(BuyAccountPojo buyAccountPojo, String uuid);

}
