package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatGroupEntity;
import inks.service.sa.som.domain.pojo.MatGroupPojo;
import inks.service.sa.som.mapper.MatGroupMapper;
import inks.service.sa.som.service.MatGroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 物料分组(MatGroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-21 09:03:24
 */
@Service("matGroupService")
public class MatGroupServiceImpl implements MatGroupService {
    @Resource
    private MatGroupMapper matGroupMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatGroupPojo getEntity(String key, String tid) {
        return this.matGroupMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatGroupPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatGroupPojo> lst = matGroupMapper.getPageList(queryParam);
            PageInfo<MatGroupPojo> pageInfo = new PageInfo<MatGroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matGroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatGroupPojo insert(MatGroupPojo matGroupPojo) {
        //初始化NULL字段
        if (matGroupPojo.getParentid() == null) matGroupPojo.setParentid("");
        if (matGroupPojo.getGrouptype() == null) matGroupPojo.setGrouptype("");
        if (matGroupPojo.getGroupcode() == null) matGroupPojo.setGroupcode("");
        if (matGroupPojo.getGroupname() == null) matGroupPojo.setGroupname("");
        if (matGroupPojo.getRownum() == null) matGroupPojo.setRownum(0);
        if (matGroupPojo.getGrouplevel() == null) matGroupPojo.setGrouplevel(0);
        if (matGroupPojo.getStatecode() == null) matGroupPojo.setStatecode("");
        if (matGroupPojo.getAllowitem() == null) matGroupPojo.setAllowitem(0);
        if (matGroupPojo.getChildcount() == null) matGroupPojo.setChildcount(0);
        if (matGroupPojo.getPrefix() == null) matGroupPojo.setPrefix("");
        if (matGroupPojo.getSuffix() == null) matGroupPojo.setSuffix("");
        if (matGroupPojo.getSncode() == null) matGroupPojo.setSncode("");
        if (matGroupPojo.getRemark() == null) matGroupPojo.setRemark("");
        if (matGroupPojo.getCreateby() == null) matGroupPojo.setCreateby("");
        if (matGroupPojo.getCreatebyid() == null) matGroupPojo.setCreatebyid("");
        if (matGroupPojo.getCreatedate() == null) matGroupPojo.setCreatedate(new Date());
        if (matGroupPojo.getLister() == null) matGroupPojo.setLister("");
        if (matGroupPojo.getListerid() == null) matGroupPojo.setListerid("");
        if (matGroupPojo.getModifydate() == null) matGroupPojo.setModifydate(new Date());
        if (matGroupPojo.getCustom1() == null) matGroupPojo.setCustom1("");
        if (matGroupPojo.getCustom2() == null) matGroupPojo.setCustom2("");
        if (matGroupPojo.getCustom3() == null) matGroupPojo.setCustom3("");
        if (matGroupPojo.getCustom4() == null) matGroupPojo.setCustom4("");
        if (matGroupPojo.getTenantid() == null) matGroupPojo.setTenantid("");
        if (matGroupPojo.getRevision() == null) matGroupPojo.setRevision(0);

        if (matGroupPojo.getTenantname() == null) matGroupPojo.setTenantname("");
        if (matGroupPojo.getGroupsvg() == null) matGroupPojo.setGroupsvg("");

        MatGroupEntity matGroupEntity = new MatGroupEntity();
        BeanUtils.copyProperties(matGroupPojo, matGroupEntity);

        matGroupEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matGroupEntity.setRevision(1);  //乐观锁
        this.matGroupMapper.insert(matGroupEntity);
        return this.getEntity(matGroupEntity.getId(), matGroupEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matGroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatGroupPojo update(MatGroupPojo matGroupPojo) {
        MatGroupEntity matGroupEntity = new MatGroupEntity();
        BeanUtils.copyProperties(matGroupPojo, matGroupEntity);
        this.matGroupMapper.update(matGroupEntity);
        return this.getEntity(matGroupEntity.getId(), matGroupEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {

        return this.matGroupMapper.delete(key, tid);
    }

    /**
     * 通过主键读取子集
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public List<MatGroupPojo> getListByParentid(String key, String tid) {
        return this.matGroupMapper.getListByParentid(key, tid);
    }
}
