package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyInvoiceitemEntity;
import inks.service.sa.som.domain.pojo.BuyInvoiceitemPojo;
import inks.service.sa.som.mapper.BuyInvoiceitemMapper;
import inks.service.sa.som.service.BuyInvoiceitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 发票项目(BuyInvoiceitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-11 15:11:54
 */
@Service("buyInvoiceitemService")
public class BuyInvoiceitemServiceImpl implements BuyInvoiceitemService {
    @Resource
    private BuyInvoiceitemMapper buyInvoiceitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyInvoiceitemPojo getEntity(String key,String tid) {
        return this.buyInvoiceitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyInvoiceitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyInvoiceitemPojo> lst = buyInvoiceitemMapper.getPageList(queryParam);
            PageInfo<BuyInvoiceitemPojo> pageInfo = new PageInfo<BuyInvoiceitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BuyInvoiceitemPojo> getList(String Pid,String tid) {
        try {
            List<BuyInvoiceitemPojo> lst = buyInvoiceitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param buyInvoiceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyInvoiceitemPojo insert(BuyInvoiceitemPojo buyInvoiceitemPojo) {
        //初始化item的NULL
        BuyInvoiceitemPojo itempojo =this.clearNull(buyInvoiceitemPojo);
        BuyInvoiceitemEntity buyInvoiceitemEntity = new BuyInvoiceitemEntity();
        BeanUtils.copyProperties(itempojo,buyInvoiceitemEntity);

          buyInvoiceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          buyInvoiceitemEntity.setRevision(1);  //乐观锁
          this.buyInvoiceitemMapper.insert(buyInvoiceitemEntity);
        return this.getEntity(buyInvoiceitemEntity.getId(),buyInvoiceitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param buyInvoiceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyInvoiceitemPojo update(BuyInvoiceitemPojo buyInvoiceitemPojo) {
        BuyInvoiceitemEntity buyInvoiceitemEntity = new BuyInvoiceitemEntity();
        BeanUtils.copyProperties(buyInvoiceitemPojo,buyInvoiceitemEntity);
        this.buyInvoiceitemMapper.update(buyInvoiceitemEntity);
        return this.getEntity(buyInvoiceitemEntity.getId(),buyInvoiceitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.buyInvoiceitemMapper.delete(key,tid) ;
    }

     /**
     * 修改数据
     *
     * @param buyInvoiceitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BuyInvoiceitemPojo clearNull(BuyInvoiceitemPojo buyInvoiceitemPojo){
     //初始化NULL字段
     if(buyInvoiceitemPojo.getPid()==null) buyInvoiceitemPojo.setPid("");
     if(buyInvoiceitemPojo.getFinishuid()==null) buyInvoiceitemPojo.setFinishuid("");
     if(buyInvoiceitemPojo.getFinishdate()==null) buyInvoiceitemPojo.setFinishdate(new Date());
     if(buyInvoiceitemPojo.getFinishtype()==null) buyInvoiceitemPojo.setFinishtype("");
     if(buyInvoiceitemPojo.getFinishitemid()==null) buyInvoiceitemPojo.setFinishitemid("");
     if(buyInvoiceitemPojo.getGoodsid()==null) buyInvoiceitemPojo.setGoodsid("");
     if(buyInvoiceitemPojo.getBillqty()==null) buyInvoiceitemPojo.setBillqty(0D);
     if(buyInvoiceitemPojo.getQuantity()==null) buyInvoiceitemPojo.setQuantity(0D);
     if(buyInvoiceitemPojo.getTaxprice()==null) buyInvoiceitemPojo.setTaxprice(0D);
     if(buyInvoiceitemPojo.getTaxamount()==null) buyInvoiceitemPojo.setTaxamount(0D);
     if(buyInvoiceitemPojo.getPrice()==null) buyInvoiceitemPojo.setPrice(0D);
     if(buyInvoiceitemPojo.getAmount()==null) buyInvoiceitemPojo.setAmount(0D);
     if(buyInvoiceitemPojo.getTaxtotal()==null) buyInvoiceitemPojo.setTaxtotal(0D);
     if(buyInvoiceitemPojo.getRownum()==null) buyInvoiceitemPojo.setRownum(0);
     if(buyInvoiceitemPojo.getRemark()==null) buyInvoiceitemPojo.setRemark("");
     if(buyInvoiceitemPojo.getOrderno()==null) buyInvoiceitemPojo.setOrderno("");
     if(buyInvoiceitemPojo.getOrderuid()==null) buyInvoiceitemPojo.setOrderuid("");
     if(buyInvoiceitemPojo.getOrderitemid()==null) buyInvoiceitemPojo.setOrderitemid("");
     if(buyInvoiceitemPojo.getMachuid()==null) buyInvoiceitemPojo.setMachuid("");
     if(buyInvoiceitemPojo.getMachitemid()==null) buyInvoiceitemPojo.setMachitemid("");
     if(buyInvoiceitemPojo.getMachgroupid()==null) buyInvoiceitemPojo.setMachgroupid("");
     if(buyInvoiceitemPojo.getCustpo()==null) buyInvoiceitemPojo.setCustpo("");
     if(buyInvoiceitemPojo.getCustomer()==null) buyInvoiceitemPojo.setCustomer("");
     if(buyInvoiceitemPojo.getMrpuid()==null) buyInvoiceitemPojo.setMrpuid("");
     if(buyInvoiceitemPojo.getMrpitemid()==null) buyInvoiceitemPojo.setMrpitemid("");
     if(buyInvoiceitemPojo.getItemtaxrate()==null) buyInvoiceitemPojo.setItemtaxrate(0);
     if(buyInvoiceitemPojo.getAvgfirstamt()==null) buyInvoiceitemPojo.setAvgfirstamt(0D);
     if(buyInvoiceitemPojo.getAvglastamt()==null) buyInvoiceitemPojo.setAvglastamt(0D);
     if(buyInvoiceitemPojo.getCustom1()==null) buyInvoiceitemPojo.setCustom1("");
     if(buyInvoiceitemPojo.getCustom2()==null) buyInvoiceitemPojo.setCustom2("");
     if(buyInvoiceitemPojo.getCustom3()==null) buyInvoiceitemPojo.setCustom3("");
     if(buyInvoiceitemPojo.getCustom4()==null) buyInvoiceitemPojo.setCustom4("");
     if(buyInvoiceitemPojo.getCustom5()==null) buyInvoiceitemPojo.setCustom5("");
     if(buyInvoiceitemPojo.getCustom6()==null) buyInvoiceitemPojo.setCustom6("");
     if(buyInvoiceitemPojo.getCustom7()==null) buyInvoiceitemPojo.setCustom7("");
     if(buyInvoiceitemPojo.getCustom8()==null) buyInvoiceitemPojo.setCustom8("");
     if(buyInvoiceitemPojo.getCustom9()==null) buyInvoiceitemPojo.setCustom9("");
     if(buyInvoiceitemPojo.getCustom10()==null) buyInvoiceitemPojo.setCustom10("");
     if(buyInvoiceitemPojo.getCustom11()==null) buyInvoiceitemPojo.setCustom11("");
     if(buyInvoiceitemPojo.getCustom12()==null) buyInvoiceitemPojo.setCustom12("");
     if(buyInvoiceitemPojo.getCustom13()==null) buyInvoiceitemPojo.setCustom13("");
     if(buyInvoiceitemPojo.getCustom14()==null) buyInvoiceitemPojo.setCustom14("");
     if(buyInvoiceitemPojo.getCustom15()==null) buyInvoiceitemPojo.setCustom15("");
     if(buyInvoiceitemPojo.getCustom16()==null) buyInvoiceitemPojo.setCustom16("");
     if(buyInvoiceitemPojo.getCustom17()==null) buyInvoiceitemPojo.setCustom17("");
     if(buyInvoiceitemPojo.getCustom18()==null) buyInvoiceitemPojo.setCustom18("");
     if(buyInvoiceitemPojo.getTenantid()==null) buyInvoiceitemPojo.setTenantid("");
     if(buyInvoiceitemPojo.getRevision()==null) buyInvoiceitemPojo.setRevision(0);
     return buyInvoiceitemPojo;
     }
}
