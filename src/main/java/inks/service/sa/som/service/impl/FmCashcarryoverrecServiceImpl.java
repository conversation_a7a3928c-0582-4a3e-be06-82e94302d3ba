package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.FmCashcarryoverrecEntity;
import inks.service.sa.som.domain.pojo.FmCashcarryoverrecPojo;
import inks.service.sa.som.mapper.FmCashcarryoverMapper;
import inks.service.sa.som.mapper.FmCashcarryoverrecMapper;
import inks.service.sa.som.service.FmCashcarryoverrecService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 出纳结账(FmCashcarryoverrec)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-09-26 19:41:48
 */
@Service("fmCashcarryoverrecService")
public class FmCashcarryoverrecServiceImpl implements FmCashcarryoverrecService {
    @Resource
    private FmCashcarryoverrecMapper fmCashcarryoverrecMapper;

    @Resource
    private FmCashcarryoverMapper fmCashcarryoverMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmCashcarryoverrecPojo getEntity(String key, String tid) {
        return this.fmCashcarryoverrecMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmCashcarryoverrecPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmCashcarryoverrecPojo> lst = fmCashcarryoverrecMapper.getPageList(queryParam);
            PageInfo<FmCashcarryoverrecPojo> pageInfo = new PageInfo<FmCashcarryoverrecPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param fmCashcarryoverrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmCashcarryoverrecPojo insert(FmCashcarryoverrecPojo fmCashcarryoverrecPojo) {
    //初始化NULL字段
     if(fmCashcarryoverrecPojo.getCarryyear()==null) fmCashcarryoverrecPojo.setCarryyear(0);
     if(fmCashcarryoverrecPojo.getCarrymonth()==null) fmCashcarryoverrecPojo.setCarrymonth(0);
     if(fmCashcarryoverrecPojo.getStartdate()==null) fmCashcarryoverrecPojo.setStartdate(new Date());
     if(fmCashcarryoverrecPojo.getEnddate()==null) fmCashcarryoverrecPojo.setEnddate(new Date());
     if(fmCashcarryoverrecPojo.getOperator()==null) fmCashcarryoverrecPojo.setOperator("");
     if(fmCashcarryoverrecPojo.getOperatorid()==null) fmCashcarryoverrecPojo.setOperatorid("");
     if(fmCashcarryoverrecPojo.getRownum()==null) fmCashcarryoverrecPojo.setRownum(0);
     if(fmCashcarryoverrecPojo.getRemark()==null) fmCashcarryoverrecPojo.setRemark("");
     if(fmCashcarryoverrecPojo.getCreateby()==null) fmCashcarryoverrecPojo.setCreateby("");
     if(fmCashcarryoverrecPojo.getCreatebyid()==null) fmCashcarryoverrecPojo.setCreatebyid("");
     if(fmCashcarryoverrecPojo.getCreatedate()==null) fmCashcarryoverrecPojo.setCreatedate(new Date());
     if(fmCashcarryoverrecPojo.getLister()==null) fmCashcarryoverrecPojo.setLister("");
     if(fmCashcarryoverrecPojo.getListerid()==null) fmCashcarryoverrecPojo.setListerid("");
     if(fmCashcarryoverrecPojo.getModifydate()==null) fmCashcarryoverrecPojo.setModifydate(new Date());
     if(fmCashcarryoverrecPojo.getBillopenamount()==null) fmCashcarryoverrecPojo.setBillopenamount(0D);
     if(fmCashcarryoverrecPojo.getBillinamount()==null) fmCashcarryoverrecPojo.setBillinamount(0D);
     if(fmCashcarryoverrecPojo.getBilloutamount()==null) fmCashcarryoverrecPojo.setBilloutamount(0D);
     if(fmCashcarryoverrecPojo.getBillcloseamount()==null) fmCashcarryoverrecPojo.setBillcloseamount(0D);
     if(fmCashcarryoverrecPojo.getPrintcount()==null) fmCashcarryoverrecPojo.setPrintcount(0);
     if(fmCashcarryoverrecPojo.getCustom1()==null) fmCashcarryoverrecPojo.setCustom1("");
     if(fmCashcarryoverrecPojo.getCustom2()==null) fmCashcarryoverrecPojo.setCustom2("");
     if(fmCashcarryoverrecPojo.getCustom3()==null) fmCashcarryoverrecPojo.setCustom3("");
     if(fmCashcarryoverrecPojo.getCustom4()==null) fmCashcarryoverrecPojo.setCustom4("");
     if(fmCashcarryoverrecPojo.getCustom5()==null) fmCashcarryoverrecPojo.setCustom5("");
     if(fmCashcarryoverrecPojo.getCustom6()==null) fmCashcarryoverrecPojo.setCustom6("");
     if(fmCashcarryoverrecPojo.getCustom7()==null) fmCashcarryoverrecPojo.setCustom7("");
     if(fmCashcarryoverrecPojo.getCustom8()==null) fmCashcarryoverrecPojo.setCustom8("");
     if(fmCashcarryoverrecPojo.getCustom9()==null) fmCashcarryoverrecPojo.setCustom9("");
     if(fmCashcarryoverrecPojo.getCustom10()==null) fmCashcarryoverrecPojo.setCustom10("");
     if(fmCashcarryoverrecPojo.getTenantid()==null) fmCashcarryoverrecPojo.setTenantid("");
     if(fmCashcarryoverrecPojo.getTenantname()==null) fmCashcarryoverrecPojo.setTenantname("");
     if(fmCashcarryoverrecPojo.getRevision()==null) fmCashcarryoverrecPojo.setRevision(0);
        FmCashcarryoverrecEntity fmCashcarryoverrecEntity = new FmCashcarryoverrecEntity(); 
        BeanUtils.copyProperties(fmCashcarryoverrecPojo,fmCashcarryoverrecEntity);
        
          fmCashcarryoverrecEntity.setId(UUID.randomUUID().toString());
          fmCashcarryoverrecEntity.setRevision(1);  //乐观锁
          this.fmCashcarryoverrecMapper.insert(fmCashcarryoverrecEntity);
        return this.getEntity(fmCashcarryoverrecEntity.getId(),fmCashcarryoverrecEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param fmCashcarryoverrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmCashcarryoverrecPojo update(FmCashcarryoverrecPojo fmCashcarryoverrecPojo) {
        FmCashcarryoverrecEntity fmCashcarryoverrecEntity = new FmCashcarryoverrecEntity(); 
        BeanUtils.copyProperties(fmCashcarryoverrecPojo,fmCashcarryoverrecEntity);
        this.fmCashcarryoverrecMapper.update(fmCashcarryoverrecEntity);
        return this.getEntity(fmCashcarryoverrecEntity.getId(),fmCashcarryoverrecEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        FmCashcarryoverrecPojo delPojo = this.fmCashcarryoverrecMapper.getEntity(key, tid);
        this.fmCashcarryoverMapper.deleteByMonth(delPojo.getCarryyear(), delPojo.getCarrymonth(), tid);
        return this.fmCashcarryoverrecMapper.delete(key,tid) ;
    }

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    @Override
    public FmCashcarryoverrecPojo getEntityByMax(String tid) {
        return this.fmCashcarryoverrecMapper.getEntityByMax(tid);
    }


}
