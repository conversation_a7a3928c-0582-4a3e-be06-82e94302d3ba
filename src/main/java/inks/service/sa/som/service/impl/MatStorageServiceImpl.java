package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatStorageEntity;
import inks.service.sa.som.domain.pojo.MatStoragePojo;
import inks.service.sa.som.mapper.MatStorageMapper;
import inks.service.sa.som.service.MatStorageService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 仓库管理(MatStorage)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-30 13:34:00
 */
@Service("matStorageService")
public class MatStorageServiceImpl implements MatStorageService {
    @Resource
    private MatStorageMapper matStorageMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatStoragePojo getEntity(String key, String tid) {
        return this.matStorageMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatStoragePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatStoragePojo> lst = matStorageMapper.getPageList(queryParam);
            PageInfo<MatStoragePojo> pageInfo = new PageInfo<MatStoragePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matStoragePojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatStoragePojo insert(MatStoragePojo matStoragePojo) {
        //初始化NULL字段
        if (matStoragePojo.getProjectid() == null) matStoragePojo.setProjectid("");
        if (matStoragePojo.getProjcode() == null) matStoragePojo.setProjcode("");
        if (matStoragePojo.getProjname() == null) matStoragePojo.setProjname("");
        if (matStoragePojo.getStorecode() == null) matStoragePojo.setStorecode("");
        if (matStoragePojo.getStorename() == null) matStoragePojo.setStorename("");
        if (matStoragePojo.getStoreadd() == null) matStoragePojo.setStoreadd("");
        if (matStoragePojo.getOperator() == null) matStoragePojo.setOperator("");
        if (matStoragePojo.getStoretel() == null) matStoragePojo.setStoretel("");
        if (matStoragePojo.getRemark() == null) matStoragePojo.setRemark("");
        if (matStoragePojo.getRownum() == null) matStoragePojo.setRownum(0);
        if (matStoragePojo.getAllowedit() == null) matStoragePojo.setAllowedit(0);
        if (matStoragePojo.getAllowdelete() == null) matStoragePojo.setAllowdelete(0);
        if (matStoragePojo.getEnabledmark() == null) matStoragePojo.setEnabledmark(0);
        if (matStoragePojo.getCreateby() == null) matStoragePojo.setCreateby("");
        if (matStoragePojo.getCreatebyid() == null) matStoragePojo.setCreatebyid("");
        if (matStoragePojo.getCreatedate() == null) matStoragePojo.setCreatedate(new Date());
        if (matStoragePojo.getLister() == null) matStoragePojo.setLister("");
        if (matStoragePojo.getListerid() == null) matStoragePojo.setListerid("");
        if (matStoragePojo.getModifydate() == null) matStoragePojo.setModifydate(new Date());
        if (matStoragePojo.getDeletemark() == null) matStoragePojo.setDeletemark(0);
        if (matStoragePojo.getDeletelister() == null) matStoragePojo.setDeletelister("");
        if (matStoragePojo.getDeletelisterid() == null) matStoragePojo.setDeletelisterid("");
        if (matStoragePojo.getDeletedate() == null) matStoragePojo.setDeletedate(new Date());
        if (matStoragePojo.getMachmark() == null) matStoragePojo.setMachmark(0);
        if (matStoragePojo.getStoretype() == null) matStoragePojo.setStoretype(0);
        // 仓库设定时加入 UsableMark ，1 可用量计算 （默认） 0 为不计算；
        if (matStoragePojo.getUsablemark() == null) matStoragePojo.setUsablemark(1);
        if (matStoragePojo.getDmsmark() == null) matStoragePojo.setDmsmark(0);
        if (matStoragePojo.getScmmark() == null) matStoragePojo.setScmmark(0);
        if (matStoragePojo.getCustom1() == null) matStoragePojo.setCustom1("");
        if (matStoragePojo.getCustom2() == null) matStoragePojo.setCustom2("");
        if (matStoragePojo.getCustom3() == null) matStoragePojo.setCustom3("");
        if (matStoragePojo.getCustom4() == null) matStoragePojo.setCustom4("");
        if (matStoragePojo.getCustom5() == null) matStoragePojo.setCustom5("");
        if (matStoragePojo.getCustom6() == null) matStoragePojo.setCustom6("");
        if (matStoragePojo.getCustom7() == null) matStoragePojo.setCustom7("");
        if (matStoragePojo.getCustom8() == null) matStoragePojo.setCustom8("");
        if (matStoragePojo.getCustom9() == null) matStoragePojo.setCustom9("");
        if (matStoragePojo.getCustom10() == null) matStoragePojo.setCustom10("");
        if (matStoragePojo.getTenantid() == null) matStoragePojo.setTenantid("");
        if (matStoragePojo.getRevision() == null) matStoragePojo.setRevision(0);
        MatStorageEntity matStorageEntity = new MatStorageEntity();
        BeanUtils.copyProperties(matStoragePojo, matStorageEntity);

        matStorageEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matStorageEntity.setRevision(1);  //乐观锁
        this.matStorageMapper.insert(matStorageEntity);
        return this.getEntity(matStorageEntity.getId(), matStorageEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matStoragePojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatStoragePojo update(MatStoragePojo matStoragePojo) {
        MatStorageEntity matStorageEntity = new MatStorageEntity();
        BeanUtils.copyProperties(matStoragePojo, matStorageEntity);
        this.matStorageMapper.update(matStorageEntity);
        return this.getEntity(matStorageEntity.getId(), matStorageEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matStorageMapper.delete(key, tid);
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @Override
    public List<MatStoragePojo> getMachList(String tid) {
        return this.matStorageMapper.getMachList(tid);
    }

    @Override
    // 查询仓库是否被引用
    public List<String> getItemCiteBillName(String key, String tid) {
        return this.matStorageMapper.getItemCiteBillName(key, tid);
    }

    @Override
    public boolean checkCodeOrName(String storecode, String storename, String tid) {
        return this.matStorageMapper.checkCodeOrName(storecode, storename, tid) > 0;
    }
}
