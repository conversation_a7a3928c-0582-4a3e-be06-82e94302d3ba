package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatCarryoveritemEntity;
import inks.service.sa.som.domain.pojo.MatCarryoveritemPojo;
import inks.service.sa.som.mapper.MatCarryoveritemMapper;
import inks.service.sa.som.service.MatCarryoveritemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 仓库结转子表(MatCarryoveritem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-06 11:24:17
 */
@Service("matCarryoveritemService")
public class MatCarryoveritemServiceImpl implements MatCarryoveritemService {
    @Resource
    private MatCarryoveritemMapper matCarryoveritemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatCarryoveritemPojo getEntity(String key,String tid) {
        return this.matCarryoveritemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatCarryoveritemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatCarryoveritemPojo> lst = matCarryoveritemMapper.getPageList(queryParam);
            PageInfo<MatCarryoveritemPojo> pageInfo = new PageInfo<MatCarryoveritemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<MatCarryoveritemPojo> getList(String Pid,String tid) { 
        try {
            List<MatCarryoveritemPojo> lst = matCarryoveritemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param matCarryoveritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatCarryoveritemPojo insert(MatCarryoveritemPojo matCarryoveritemPojo) {
        //初始化item的NULL
        MatCarryoveritemPojo itempojo =this.clearNull(matCarryoveritemPojo);
        MatCarryoveritemEntity matCarryoveritemEntity = new MatCarryoveritemEntity(); 
        BeanUtils.copyProperties(itempojo,matCarryoveritemEntity);
        
          matCarryoveritemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          matCarryoveritemEntity.setRevision(1);  //乐观锁      
          this.matCarryoveritemMapper.insert(matCarryoveritemEntity);
        return this.getEntity(matCarryoveritemEntity.getId(),matCarryoveritemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param matCarryoveritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatCarryoveritemPojo update(MatCarryoveritemPojo matCarryoveritemPojo) {
        MatCarryoveritemEntity matCarryoveritemEntity = new MatCarryoveritemEntity(); 
        BeanUtils.copyProperties(matCarryoveritemPojo,matCarryoveritemEntity);
        this.matCarryoveritemMapper.update(matCarryoveritemEntity);
        return this.getEntity(matCarryoveritemEntity.getId(),matCarryoveritemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.matCarryoveritemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param matCarryoveritemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public MatCarryoveritemPojo clearNull(MatCarryoveritemPojo matCarryoveritemPojo){
     //初始化NULL字段
     if(matCarryoveritemPojo.getPid()==null) matCarryoveritemPojo.setPid("");
     if(matCarryoveritemPojo.getGoodsid()==null) matCarryoveritemPojo.setGoodsid("");
     if(matCarryoveritemPojo.getItemcode()==null) matCarryoveritemPojo.setItemcode("");
     if(matCarryoveritemPojo.getItemname()==null) matCarryoveritemPojo.setItemname("");
     if(matCarryoveritemPojo.getItemspec()==null) matCarryoveritemPojo.setItemspec("");
     if(matCarryoveritemPojo.getItemunit()==null) matCarryoveritemPojo.setItemunit("");
     if(matCarryoveritemPojo.getOpenqty()==null) matCarryoveritemPojo.setOpenqty(0D);
     if(matCarryoveritemPojo.getOpenamount()==null) matCarryoveritemPojo.setOpenamount(0D);
     if(matCarryoveritemPojo.getInqty()==null) matCarryoveritemPojo.setInqty(0D);
     if(matCarryoveritemPojo.getInamount()==null) matCarryoveritemPojo.setInamount(0D);
     if(matCarryoveritemPojo.getOutqty()==null) matCarryoveritemPojo.setOutqty(0D);
     if(matCarryoveritemPojo.getOutamount()==null) matCarryoveritemPojo.setOutamount(0D);
     if(matCarryoveritemPojo.getCloseqty()==null) matCarryoveritemPojo.setCloseqty(0D);
     if(matCarryoveritemPojo.getCloseamount()==null) matCarryoveritemPojo.setCloseamount(0D);
     if(matCarryoveritemPojo.getSkuid()==null) matCarryoveritemPojo.setSkuid("");
     if(matCarryoveritemPojo.getAttributejson()==null) matCarryoveritemPojo.setAttributejson("");
     if(matCarryoveritemPojo.getRownum()==null) matCarryoveritemPojo.setRownum(0);
     if(matCarryoveritemPojo.getCustom1()==null) matCarryoveritemPojo.setCustom1("");
     if(matCarryoveritemPojo.getCustom2()==null) matCarryoveritemPojo.setCustom2("");
     if(matCarryoveritemPojo.getCustom3()==null) matCarryoveritemPojo.setCustom3("");
     if(matCarryoveritemPojo.getCustom4()==null) matCarryoveritemPojo.setCustom4("");
     if(matCarryoveritemPojo.getCustom5()==null) matCarryoveritemPojo.setCustom5("");
     if(matCarryoveritemPojo.getCustom6()==null) matCarryoveritemPojo.setCustom6("");
     if(matCarryoveritemPojo.getCustom7()==null) matCarryoveritemPojo.setCustom7("");
     if(matCarryoveritemPojo.getCustom8()==null) matCarryoveritemPojo.setCustom8("");
     if(matCarryoveritemPojo.getCustom9()==null) matCarryoveritemPojo.setCustom9("");
     if(matCarryoveritemPojo.getCustom10()==null) matCarryoveritemPojo.setCustom10("");
     if(matCarryoveritemPojo.getTenantid()==null) matCarryoveritemPojo.setTenantid("");
     if(matCarryoveritemPojo.getRevision()==null) matCarryoveritemPojo.setRevision(0);
     return matCarryoveritemPojo;
     }
}
