package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.FmPayrequestEntity;
import inks.service.sa.som.domain.FmPayrequestitemEntity;
import inks.service.sa.som.domain.pojo.FmPayrequestPojo;
import inks.service.sa.som.domain.pojo.FmPayrequestitemPojo;
import inks.service.sa.som.domain.pojo.FmPayrequestitemdetailPojo;
import inks.service.sa.som.mapper.FmPayrequestMapper;
import inks.service.sa.som.mapper.FmPayrequestitemMapper;
import inks.service.sa.som.service.FmPayrequestService;
import inks.service.sa.som.service.FmPayrequestitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 付款申请单(FmPayrequest)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-07 13:31:28
 */
@Service("fmPayrequestService")
public class FmPayrequestServiceImpl implements FmPayrequestService {
    @Resource
    private FmPayrequestMapper fmPayrequestMapper;
    
    @Resource
    private FmPayrequestitemMapper fmPayrequestitemMapper;
    
     
    @Resource
    private FmPayrequestitemService fmPayrequestitemService;
    
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmPayrequestPojo getEntity(String key) {
        return this.fmPayrequestMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmPayrequestitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmPayrequestitemdetailPojo> lst = fmPayrequestMapper.getPageList(queryParam);
            PageInfo<FmPayrequestitemdetailPojo> pageInfo = new PageInfo<FmPayrequestitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    
     /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmPayrequestPojo getBillEntity(String key) {
       try {
        //读取主表
        FmPayrequestPojo fmPayrequestPojo = this.fmPayrequestMapper.getEntity(key);
        //读取子表
        fmPayrequestPojo.setItem(fmPayrequestitemMapper.getList(fmPayrequestPojo.getId()));
        return fmPayrequestPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmPayrequestPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmPayrequestPojo> lst = fmPayrequestMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(fmPayrequestitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<FmPayrequestPojo> pageInfo = new PageInfo<FmPayrequestPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    
       /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmPayrequestPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmPayrequestPojo> lst = fmPayrequestMapper.getPageTh(queryParam);
            PageInfo<FmPayrequestPojo> pageInfo = new PageInfo<FmPayrequestPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param fmPayrequestPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public FmPayrequestPojo insert(FmPayrequestPojo fmPayrequestPojo) {
//初始化NULL字段
     if(fmPayrequestPojo.getRefno()==null) fmPayrequestPojo.setRefno("");
     if(fmPayrequestPojo.getBilltype()==null) fmPayrequestPojo.setBilltype("");
     if(fmPayrequestPojo.getBilltitle()==null) fmPayrequestPojo.setBilltitle("");
     if(fmPayrequestPojo.getBilldate()==null) fmPayrequestPojo.setBilldate(new Date());
     if(fmPayrequestPojo.getProjectid()==null) fmPayrequestPojo.setProjectid("");
     if(fmPayrequestPojo.getProjcode()==null) fmPayrequestPojo.setProjcode("");
     if(fmPayrequestPojo.getProjname()==null) fmPayrequestPojo.setProjname("");
     if(fmPayrequestPojo.getApplicantdept()==null) fmPayrequestPojo.setApplicantdept("");
     if(fmPayrequestPojo.getBelong()==null) fmPayrequestPojo.setBelong("");
     if(fmPayrequestPojo.getSuppliers()==null) fmPayrequestPojo.setSuppliers("");
     if(fmPayrequestPojo.getItemname()==null) fmPayrequestPojo.setItemname("");
     if(fmPayrequestPojo.getQuantity()==null) fmPayrequestPojo.setQuantity(0D);
     if(fmPayrequestPojo.getPrice()==null) fmPayrequestPojo.setPrice(0D);
     if(fmPayrequestPojo.getAmount()==null) fmPayrequestPojo.setAmount(0D);
     if(fmPayrequestPojo.getTaxtotal()==null) fmPayrequestPojo.setTaxtotal(0D);
     if(fmPayrequestPojo.getTaxamount()==null) fmPayrequestPojo.setTaxamount(0D);
     if(fmPayrequestPojo.getInvocode()==null) fmPayrequestPojo.setInvocode("");
     if(fmPayrequestPojo.getAimdate()==null) fmPayrequestPojo.setAimdate(new Date());
     if(fmPayrequestPojo.getPayment()==null) fmPayrequestPojo.setPayment("");
     if(fmPayrequestPojo.getOperator()==null) fmPayrequestPojo.setOperator("");
     if(fmPayrequestPojo.getOperatorid()==null) fmPayrequestPojo.setOperatorid("");
     if(fmPayrequestPojo.getSummary()==null) fmPayrequestPojo.setSummary("");
     if(fmPayrequestPojo.getStatecode()==null) fmPayrequestPojo.setStatecode("");
     if(fmPayrequestPojo.getStatedate()==null) fmPayrequestPojo.setStatedate(new Date());
     if(fmPayrequestPojo.getCreateby()==null) fmPayrequestPojo.setCreateby("");
     if(fmPayrequestPojo.getCreatebyid()==null) fmPayrequestPojo.setCreatebyid("");
     if(fmPayrequestPojo.getCreatedate()==null) fmPayrequestPojo.setCreatedate(new Date());
     if(fmPayrequestPojo.getLister()==null) fmPayrequestPojo.setLister("");
     if(fmPayrequestPojo.getListerid()==null) fmPayrequestPojo.setListerid("");
     if(fmPayrequestPojo.getModifydate()==null) fmPayrequestPojo.setModifydate(new Date());
     if(fmPayrequestPojo.getAssessor()==null) fmPayrequestPojo.setAssessor("");
     if(fmPayrequestPojo.getAssessorid()==null) fmPayrequestPojo.setAssessorid("");
     if(fmPayrequestPojo.getAssessdate()==null) fmPayrequestPojo.setAssessdate(new Date());
     if(fmPayrequestPojo.getDisannulmark()==null) fmPayrequestPojo.setDisannulmark(0);
     if(fmPayrequestPojo.getDisannuldate()==null) fmPayrequestPojo.setDisannuldate(new Date());
     if(fmPayrequestPojo.getDisannullister()==null) fmPayrequestPojo.setDisannullister("");
     if(fmPayrequestPojo.getDisannullisterid()==null) fmPayrequestPojo.setDisannullisterid("");
     if(fmPayrequestPojo.getPrintcount()==null) fmPayrequestPojo.setPrintcount(0);
     if(fmPayrequestPojo.getCustom1()==null) fmPayrequestPojo.setCustom1("");
     if(fmPayrequestPojo.getCustom2()==null) fmPayrequestPojo.setCustom2("");
     if(fmPayrequestPojo.getCustom3()==null) fmPayrequestPojo.setCustom3("");
     if(fmPayrequestPojo.getCustom4()==null) fmPayrequestPojo.setCustom4("");
     if(fmPayrequestPojo.getCustom5()==null) fmPayrequestPojo.setCustom5("");
     if(fmPayrequestPojo.getCustom6()==null) fmPayrequestPojo.setCustom6("");
     if(fmPayrequestPojo.getCustom7()==null) fmPayrequestPojo.setCustom7("");
     if(fmPayrequestPojo.getCustom8()==null) fmPayrequestPojo.setCustom8("");
     if(fmPayrequestPojo.getCustom9()==null) fmPayrequestPojo.setCustom9("");
     if(fmPayrequestPojo.getCustom10()==null) fmPayrequestPojo.setCustom10("");
     if(fmPayrequestPojo.getDeptid()==null) fmPayrequestPojo.setDeptid("");
     if(fmPayrequestPojo.getTenantid()==null) fmPayrequestPojo.setTenantid("");
     if(fmPayrequestPojo.getTenantname()==null) fmPayrequestPojo.setTenantname("");
     if(fmPayrequestPojo.getRevision()==null) fmPayrequestPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        FmPayrequestEntity fmPayrequestEntity = new FmPayrequestEntity(); 
        BeanUtils.copyProperties(fmPayrequestPojo,fmPayrequestEntity);
        //设置id和新建日期
        fmPayrequestEntity.setId(id);
        fmPayrequestEntity.setRevision(1);  //乐观锁
        //插入主表
        this.fmPayrequestMapper.insert(fmPayrequestEntity);
        //Item子表处理
        List<FmPayrequestitemPojo> lst = fmPayrequestPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               //初始化item的NULL
               FmPayrequestitemPojo itemPojo =this.fmPayrequestitemService.clearNull(lst.get(i));
               FmPayrequestitemEntity fmPayrequestitemEntity = new FmPayrequestitemEntity(); 
               BeanUtils.copyProperties(itemPojo,fmPayrequestitemEntity);
               //设置id和Pid
               fmPayrequestitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               fmPayrequestitemEntity.setPid(id);
               fmPayrequestitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.fmPayrequestitemMapper.insert(fmPayrequestitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(fmPayrequestEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param fmPayrequestPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public FmPayrequestPojo update(FmPayrequestPojo fmPayrequestPojo) {
        //主表更改
        FmPayrequestEntity fmPayrequestEntity = new FmPayrequestEntity(); 
        BeanUtils.copyProperties(fmPayrequestPojo,fmPayrequestEntity);
        this.fmPayrequestMapper.update(fmPayrequestEntity);
        if (fmPayrequestPojo.getItem() != null) {
        //Item子表处理
        List<FmPayrequestitemPojo> lst = fmPayrequestPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =fmPayrequestMapper.getDelItemIds(fmPayrequestPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.fmPayrequestitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               FmPayrequestitemEntity fmPayrequestitemEntity = new FmPayrequestitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               FmPayrequestitemPojo itemPojo =this.fmPayrequestitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,fmPayrequestitemEntity);
               //设置id和Pid
               fmPayrequestitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               fmPayrequestitemEntity.setPid(fmPayrequestEntity.getId());  // 主表 id
               fmPayrequestitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.fmPayrequestitemMapper.insert(fmPayrequestitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),fmPayrequestitemEntity);             
               this.fmPayrequestitemMapper.update(fmPayrequestitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(fmPayrequestEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key) {
       FmPayrequestPojo fmPayrequestPojo =  this.getBillEntity(key);
        //Item子表处理
        List<FmPayrequestitemPojo> lst = fmPayrequestPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(int i=0;i<lst.size();i++){
             this.fmPayrequestitemMapper.delete(lst.get(i).getId());
            }
        }        
        return this.fmPayrequestMapper.delete(key) ;
    }
    

    
                                                                                                                                                                     /**
     * 审核数据
     *
     * @param fmPayrequestPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public FmPayrequestPojo approval(FmPayrequestPojo fmPayrequestPojo) {
        //主表更改
        FmPayrequestEntity fmPayrequestEntity = new FmPayrequestEntity();
        BeanUtils.copyProperties(fmPayrequestPojo,fmPayrequestEntity);
        this.fmPayrequestMapper.approval(fmPayrequestEntity);
        //返回Bill实例
        return this.getBillEntity(fmPayrequestEntity.getId());
    }
                                                                                                              
}
