package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyOrderitemPojo;

import java.util.List;
/**
 * 采购单子表(BuyOrderitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-06 20:33:54
 */
public interface BuyOrderitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyOrderitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyOrderitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyOrderitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param buyOrderitemPojo 实例对象
     * @return 实例对象
     */
    BuyOrderitemPojo insert(BuyOrderitemPojo buyOrderitemPojo);

    /**
     * 修改数据
     *
     * @param buyOrderitempojo 实例对象
     * @return 实例对象
     */
    BuyOrderitemPojo update(BuyOrderitemPojo buyOrderitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param buyOrderitempojo 实例对象
     * @return 实例对象
     */
    BuyOrderitemPojo clearNull(BuyOrderitemPojo buyOrderitempojo);
}
