package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BusDepositcashEntity;
import inks.service.sa.som.domain.pojo.BusDepositcashPojo;
import inks.service.sa.som.mapper.BusDepositcashMapper;
import inks.service.sa.som.service.BusDepositcashService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 现金项目(BusDepositcash)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-20 13:12:41
 */
@Service("busDepositcashService")
public class BusDepositcashServiceImpl implements BusDepositcashService {
    @Resource
    private BusDepositcashMapper busDepositcashMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BusDepositcashPojo getEntity(String key,String tid) {
        return this.busDepositcashMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BusDepositcashPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BusDepositcashPojo> lst = busDepositcashMapper.getPageList(queryParam);
            PageInfo<BusDepositcashPojo> pageInfo = new PageInfo<BusDepositcashPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BusDepositcashPojo> getList(String Pid,String tid) { 
        try {
            List<BusDepositcashPojo> lst = busDepositcashMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param busDepositcashPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDepositcashPojo insert(BusDepositcashPojo busDepositcashPojo) {
        //初始化item的NULL
        BusDepositcashPojo itempojo =this.clearNull(busDepositcashPojo);
        BusDepositcashEntity busDepositcashEntity = new BusDepositcashEntity(); 
        BeanUtils.copyProperties(itempojo,busDepositcashEntity);
        
          busDepositcashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          busDepositcashEntity.setRevision(1);  //乐观锁      
          this.busDepositcashMapper.insert(busDepositcashEntity);
        return this.getEntity(busDepositcashEntity.getId(),busDepositcashEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param busDepositcashPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BusDepositcashPojo update(BusDepositcashPojo busDepositcashPojo) {
        BusDepositcashEntity busDepositcashEntity = new BusDepositcashEntity(); 
        BeanUtils.copyProperties(busDepositcashPojo,busDepositcashEntity);
        this.busDepositcashMapper.update(busDepositcashEntity);
        return this.getEntity(busDepositcashEntity.getId(),busDepositcashEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.busDepositcashMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param busDepositcashPojo 实例对象
     * @return 实例对象
     */
     @Override
     public BusDepositcashPojo clearNull(BusDepositcashPojo busDepositcashPojo){
     //初始化NULL字段
     if(busDepositcashPojo.getPid()==null) busDepositcashPojo.setPid("");
     if(busDepositcashPojo.getCashaccid()==null) busDepositcashPojo.setCashaccid("");
     if(busDepositcashPojo.getCashaccname()==null) busDepositcashPojo.setCashaccname("");
     if(busDepositcashPojo.getAmount()==null) busDepositcashPojo.setAmount(0D);
     if(busDepositcashPojo.getRownum()==null) busDepositcashPojo.setRownum(0);
     if(busDepositcashPojo.getRemark()==null) busDepositcashPojo.setRemark("");
     if(busDepositcashPojo.getCustom1()==null) busDepositcashPojo.setCustom1("");
     if(busDepositcashPojo.getCustom2()==null) busDepositcashPojo.setCustom2("");
     if(busDepositcashPojo.getCustom3()==null) busDepositcashPojo.setCustom3("");
     if(busDepositcashPojo.getCustom4()==null) busDepositcashPojo.setCustom4("");
     if(busDepositcashPojo.getCustom5()==null) busDepositcashPojo.setCustom5("");
     if(busDepositcashPojo.getCustom6()==null) busDepositcashPojo.setCustom6("");
     if(busDepositcashPojo.getCustom7()==null) busDepositcashPojo.setCustom7("");
     if(busDepositcashPojo.getCustom8()==null) busDepositcashPojo.setCustom8("");
     if(busDepositcashPojo.getCustom9()==null) busDepositcashPojo.setCustom9("");
     if(busDepositcashPojo.getCustom10()==null) busDepositcashPojo.setCustom10("");
     if(busDepositcashPojo.getTenantid()==null) busDepositcashPojo.setTenantid("");
     if(busDepositcashPojo.getRevision()==null) busDepositcashPojo.setRevision(0);
     return busDepositcashPojo;
     }
}
