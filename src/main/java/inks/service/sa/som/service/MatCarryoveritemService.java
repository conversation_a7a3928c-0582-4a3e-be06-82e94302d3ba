package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatCarryoveritemPojo;

import java.util.List;
/**
 * 仓库结转子表(MatCarryoveritem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-06 11:24:17
 */
public interface MatCarryoveritemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCarryoveritemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatCarryoveritemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatCarryoveritemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param matCarryoveritemPojo 实例对象
     * @return 实例对象
     */
    MatCarryoveritemPojo insert(MatCarryoveritemPojo matCarryoveritemPojo);

    /**
     * 修改数据
     *
     * @param matCarryoveritempojo 实例对象
     * @return 实例对象
     */
    MatCarryoveritemPojo update(MatCarryoveritemPojo matCarryoveritempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param matCarryoveritempojo 实例对象
     * @return 实例对象
     */
    MatCarryoveritemPojo clearNull(MatCarryoveritemPojo matCarryoveritempojo);
}
