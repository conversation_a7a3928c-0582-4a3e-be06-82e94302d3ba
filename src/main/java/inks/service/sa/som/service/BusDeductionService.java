package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BusDeductionPojo;
import inks.service.sa.som.domain.pojo.BusDeductionitemdetailPojo;

import java.util.List;

/**
 * 销售扣款(BusDeduction)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-16 16:57:08
 */
public interface BusDeductionService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDeductionPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDeductionitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDeductionPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDeductionPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BusDeductionPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busDeductionPojo 实例对象
     * @return 实例对象
     */
    BusDeductionPojo insert(BusDeductionPojo busDeductionPojo);

    /**
     * 修改数据
     *
     * @param busDeductionpojo 实例对象
     * @return 实例对象
     */
    BusDeductionPojo update(BusDeductionPojo busDeductionpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param busDeductionPojo 实例对象
     * @return 实例对象
     */
    BusDeductionPojo approval(BusDeductionPojo busDeductionPojo);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(String key, String pid, String tid);

    void updatePrintcount(BusDeductionPojo billPrintPojo);
}
