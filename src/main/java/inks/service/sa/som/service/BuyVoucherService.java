package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyVoucherPojo;
import inks.service.sa.som.domain.pojo.BuyVoucheritemdetailPojo;

/**
 * 采购付款(BuyVoucher)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-15 15:02:31
 */
public interface BuyVoucherService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyVoucherPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyVoucheritemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyVoucherPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyVoucherPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyVoucherPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyVoucherPojo 实例对象
     * @return 实例对象
     */
    BuyVoucherPojo insert(BuyVoucherPojo buyVoucherPojo);

    /**
     * 修改数据
     *
     * @param buyVoucherpojo 实例对象
     * @return 实例对象
     */
    BuyVoucherPojo update(BuyVoucherPojo buyVoucherpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param buyVoucherPojo 实例对象
     * @return 实例对象
     */
    BuyVoucherPojo approval(BuyVoucherPojo buyVoucherPojo);
}
