package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatSkuPojo;

/**
 * 货品SKU(MatSku)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-12 13:25:57
 */
public interface MatSkuService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSkuPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatSkuPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matSkuPojo 实例对象
     * @return 实例对象
     */
    MatSkuPojo insert(MatSkuPojo matSkuPojo);

    /**
     * 修改数据
     *
     * @param matSkupojo 实例对象
     * @return 实例对象
     */
    MatSkuPojo update(MatSkuPojo matSkupojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    /**
     * 通过ID查询单条数据
     *
     * @parag goodsid 主键
     * @return 实例对象
     */
    MatSkuPojo getEntityByAtte(String goodsid, String json, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @parag goodsid 主键
     * @return 实例对象
     */
    String getAttrSku(String json, String tid);
}
