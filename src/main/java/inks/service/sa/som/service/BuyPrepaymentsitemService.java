package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyPrepaymentsitemPojo;

import java.util.List;
/**
 * 预付款项目(BuyPrepaymentsitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-11 15:13:48
 */
public interface BuyPrepaymentsitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyPrepaymentsitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyPrepaymentsitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyPrepaymentsitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param buyPrepaymentsitemPojo 实例对象
     * @return 实例对象
     */
    BuyPrepaymentsitemPojo insert(BuyPrepaymentsitemPojo buyPrepaymentsitemPojo);

    /**
     * 修改数据
     *
     * @param buyPrepaymentsitempojo 实例对象
     * @return 实例对象
     */
    BuyPrepaymentsitemPojo update(BuyPrepaymentsitemPojo buyPrepaymentsitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param buyPrepaymentsitempojo 实例对象
     * @return 实例对象
     */
    BuyPrepaymentsitemPojo clearNull(BuyPrepaymentsitemPojo buyPrepaymentsitempojo);
}
