package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatAttributeEntity;
import inks.service.sa.som.domain.pojo.MatAttributePojo;
import inks.service.sa.som.mapper.MatAttributeMapper;
import inks.service.sa.som.service.MatAttributeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * SPU属性表(MatAttribute)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-14 14:45:49
 */
@Service("matAttributeService")
public class MatAttributeServiceImpl implements MatAttributeService {
    @Resource
    private MatAttributeMapper matAttributeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatAttributePojo getEntity(String key, String tid) {
        return this.matAttributeMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatAttributePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatAttributePojo> lst = matAttributeMapper.getPageList(queryParam);
            PageInfo<MatAttributePojo> pageInfo = new PageInfo<MatAttributePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param matAttributePojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatAttributePojo insert(MatAttributePojo matAttributePojo) {
        //初始化NULL字段
        if (matAttributePojo.getAttrgroupid() == null) matAttributePojo.setAttrgroupid("");
        if (matAttributePojo.getAttrkey() == null) matAttributePojo.setAttrkey("");
        if (matAttributePojo.getAttrname() == null) matAttributePojo.setAttrname("");
        if (matAttributePojo.getValuetype() == null) matAttributePojo.setValuetype("");
        if (matAttributePojo.getDefvalue() == null) matAttributePojo.setDefvalue("");
        if (matAttributePojo.getValuejson() == null) matAttributePojo.setValuejson("");
        if (matAttributePojo.getListshow() == null) matAttributePojo.setListshow(0);
        if (matAttributePojo.getEnabledmark() == null) matAttributePojo.setEnabledmark(0);
        if (matAttributePojo.getSkumark() == null) matAttributePojo.setSkumark(0);
        if (matAttributePojo.getNumericmark() == null) matAttributePojo.setNumericmark(0);
        if (matAttributePojo.getRequiredmark() == null) matAttributePojo.setRequiredmark(0);
        if (matAttributePojo.getUsedomain() == null) matAttributePojo.setUsedomain("");
        if (matAttributePojo.getRemark() == null) matAttributePojo.setRemark("");
        if (matAttributePojo.getRownum() == null) matAttributePojo.setRownum(0);
        if (matAttributePojo.getCreateby() == null) matAttributePojo.setCreateby("");
        if (matAttributePojo.getCreatebyid() == null) matAttributePojo.setCreatebyid("");
        if (matAttributePojo.getCreatedate() == null) matAttributePojo.setCreatedate(new Date());
        if (matAttributePojo.getLister() == null) matAttributePojo.setLister("");
        if (matAttributePojo.getListerid() == null) matAttributePojo.setListerid("");
        if (matAttributePojo.getModifydate() == null) matAttributePojo.setModifydate(new Date());
        if (matAttributePojo.getCustom1() == null) matAttributePojo.setCustom1("");
        if (matAttributePojo.getCustom2() == null) matAttributePojo.setCustom2("");
        if (matAttributePojo.getCustom3() == null) matAttributePojo.setCustom3("");
        if (matAttributePojo.getCustom4() == null) matAttributePojo.setCustom4("");
        if (matAttributePojo.getCustom5() == null) matAttributePojo.setCustom5("");
        if (matAttributePojo.getCustom6() == null) matAttributePojo.setCustom6("");
        if (matAttributePojo.getCustom7() == null) matAttributePojo.setCustom7("");
        if (matAttributePojo.getCustom8() == null) matAttributePojo.setCustom8("");
        if (matAttributePojo.getCustom9() == null) matAttributePojo.setCustom9("");
        if (matAttributePojo.getCustom10() == null) matAttributePojo.setCustom10("");
        if (matAttributePojo.getTenantid() == null) matAttributePojo.setTenantid("");
        if (matAttributePojo.getTenantname() == null) matAttributePojo.setTenantname("");
        if (matAttributePojo.getRevision() == null) matAttributePojo.setRevision(0);
        MatAttributeEntity matAttributeEntity = new MatAttributeEntity();
        BeanUtils.copyProperties(matAttributePojo, matAttributeEntity);

        matAttributeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        matAttributeEntity.setRevision(1);  //乐观锁
        this.matAttributeMapper.insert(matAttributeEntity);
        return this.getEntity(matAttributeEntity.getId(), matAttributeEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param matAttributePojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatAttributePojo update(MatAttributePojo matAttributePojo) {
        MatAttributeEntity matAttributeEntity = new MatAttributeEntity();
        BeanUtils.copyProperties(matAttributePojo, matAttributeEntity);
        this.matAttributeMapper.update(matAttributeEntity);
        return this.getEntity(matAttributeEntity.getId(), matAttributeEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matAttributeMapper.delete(key, tid);
    }


    /**
     * 显示列
     *
     * @return 查询结果
     */

    @Override
    public List<MatAttributePojo> getListByShow(String useDomain,String tid) {
        return this.matAttributeMapper.getListByShow(useDomain,tid);

    }


}
