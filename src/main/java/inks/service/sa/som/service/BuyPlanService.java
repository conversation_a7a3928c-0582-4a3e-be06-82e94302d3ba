package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyPlanPojo;
import inks.service.sa.som.domain.pojo.BuyPlanitemPojo;
import inks.service.sa.som.domain.pojo.BuyPlanitemdetailPojo;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 采购计划(BuyPlan)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-06 20:32:44
 */
public interface BuyPlanService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyPlanPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyPlanitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyPlanPojo getBillEntity(String key, String tid);

    BuyPlanPojo getMergeBillEntity(String key, String tid);

    List<BuyPlanitemPojo> getItemListByMergeid(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyPlanPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyPlanPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyPlanPojo 实例对象
     * @return 实例对象
     */
    BuyPlanPojo insert(BuyPlanPojo buyPlanPojo);

    /**
     * 修改数据
     *
     * @param buyPlanpojo 实例对象
     * @return 实例对象
     */
    BuyPlanPojo update(BuyPlanPojo buyPlanpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param buyPlanPojo 实例对象
     * @return 实例对象
     */
    BuyPlanPojo approval(BuyPlanPojo buyPlanPojo);

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    BuyPlanPojo disannul(List<BuyPlanitemPojo> lst, Integer type, LoginUser loginUser);

    /**
     * 中止数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    BuyPlanPojo closed(List<BuyPlanitemPojo> lst, Integer type, LoginUser loginUser);


    // 查询Item是否被引用
    List<String> getItemCiteBillName(String key, String pid, String tid);

    void updatePrintcount(BuyPlanPojo billPrintPojo);


    int syncPriceFromOrderItem(String planitemid, String goodsid, String mergeid, String tid);

    Map<String, Object> getPriceByGoodsidAndSource(String goodsid, String groupid, String pricesource, String tid);

    String syncPriceFromOrderStart(String key, String tenantid);

    void mergeItem(List<String> planItemids, LoginUser loginUser) throws SQLException;

    void updateOaflowmark(BuyPlanPojo billEntity);
}
