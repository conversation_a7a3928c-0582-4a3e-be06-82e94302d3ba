package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyPrepaymentsPojo;
import inks.service.sa.som.domain.pojo.BuyPrepaymentsitemdetailPojo;

/**
 * 预付款(BuyPrepayments)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-15 15:02:14
 */
public interface BuyPrepaymentsService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyPrepaymentsPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyPrepaymentsitemdetailPojo> getPageList(QueryParam queryParam);

 /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyPrepaymentsPojo getBillEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyPrepaymentsPojo> getBillList(QueryParam queryParam);
    
        /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyPrepaymentsPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param buyPrepaymentsPojo 实例对象
     * @return 实例对象
     */
    BuyPrepaymentsPojo insert(BuyPrepaymentsPojo buyPrepaymentsPojo);

    /**
     * 修改数据
     *
     * @param buyPrepaymentspojo 实例对象
     * @return 实例对象
     */
    BuyPrepaymentsPojo update(BuyPrepaymentsPojo buyPrepaymentspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

                                                                                                         /**
     * 审核数据
     *
     * @param buyPrepaymentsPojo 实例对象
     * @return 实例对象
     */
     BuyPrepaymentsPojo approval(BuyPrepaymentsPojo buyPrepaymentsPojo);
                                                                           }
