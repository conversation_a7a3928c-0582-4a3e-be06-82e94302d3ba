package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmCashaccountPojo;

import java.util.List;

/**
 * 出纳账户(FmCashaccount)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-15 16:58:10
 */
public interface FmCashaccountService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCashaccountPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmCashaccountPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param fmCashaccountPojo 实例对象
     * @return 实例对象
     */
    FmCashaccountPojo insert(FmCashaccountPojo fmCashaccountPojo);

    /**
     * 修改数据
     *
     * @param fmCashaccountpojo 实例对象
     * @return 实例对象
     */
    FmCashaccountPojo update(FmCashaccountPojo fmCashaccountpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    // 查询往来单是否被引用
    List<String> getCiteBillName(String key, String tid);

}
