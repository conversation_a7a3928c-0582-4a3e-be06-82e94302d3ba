package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmCosttypePojo;

/**
 * 费用科目(FmCosttype)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-29 15:21:43
 */
public interface FmCosttypeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCosttypePojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<FmCosttypePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param fmCosttypePojo 实例对象
     * @return 实例对象
     */
    FmCosttypePojo insert(FmCosttypePojo fmCosttypePojo);

    /**
     * 修改数据
     *
     * @param fmCosttypepojo 实例对象
     * @return 实例对象
     */
    FmCosttypePojo update(FmCosttypePojo fmCosttypepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);
                                                                                                                                                           }
