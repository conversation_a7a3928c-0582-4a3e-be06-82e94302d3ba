package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyPlanmergePojo;

import java.util.List;

/**
 * 合并项目(BuyPlanmerge)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-18 12:58:12
 */
public interface BuyPlanmergeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyPlanmergePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyPlanmergePojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyPlanmergePojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param buyPlanmergePojo 实例对象
     * @return 实例对象
     */
    BuyPlanmergePojo insert(BuyPlanmergePojo buyPlanmergePojo);

    /**
     * 修改数据
     *
     * @param buyPlanmergepojo 实例对象
     * @return 实例对象
     */
    BuyPlanmergePojo update(BuyPlanmergePojo buyPlanmergepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param buyPlanmergepojo 实例对象
     * @return 实例对象
     */
    BuyPlanmergePojo clearNull(BuyPlanmergePojo buyPlanmergepojo);
}
