package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.FmCashcarryoveritemEntity;
import inks.service.sa.som.domain.pojo.FmCashcarryoveritemPojo;
import inks.service.sa.som.mapper.FmCashcarryoveritemMapper;
import inks.service.sa.som.service.FmCashcarryoveritemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 对账明细(FmCashcarryoveritem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-09-26 19:41:35
 */
@Service("fmCashcarryoveritemService")
public class FmCashcarryoveritemServiceImpl implements FmCashcarryoveritemService {
    @Resource
    private FmCashcarryoveritemMapper fmCashcarryoveritemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmCashcarryoveritemPojo getEntity(String key,String tid) {
        return this.fmCashcarryoveritemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmCashcarryoveritemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmCashcarryoveritemPojo> lst = fmCashcarryoveritemMapper.getPageList(queryParam);
            PageInfo<FmCashcarryoveritemPojo> pageInfo = new PageInfo<FmCashcarryoveritemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<FmCashcarryoveritemPojo> getList(String Pid,String tid) { 
        try {
            List<FmCashcarryoveritemPojo> lst = fmCashcarryoveritemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param fmCashcarryoveritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmCashcarryoveritemPojo insert(FmCashcarryoveritemPojo fmCashcarryoveritemPojo) {
        //初始化item的NULL
        FmCashcarryoveritemPojo itempojo =this.clearNull(fmCashcarryoveritemPojo);
        FmCashcarryoveritemEntity fmCashcarryoveritemEntity = new FmCashcarryoveritemEntity(); 
        BeanUtils.copyProperties(itempojo,fmCashcarryoveritemEntity);
        
          fmCashcarryoveritemEntity.setId(UUID.randomUUID().toString());
          fmCashcarryoveritemEntity.setRevision(1);  //乐观锁      
          this.fmCashcarryoveritemMapper.insert(fmCashcarryoveritemEntity);
        return this.getEntity(fmCashcarryoveritemEntity.getId(),fmCashcarryoveritemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param fmCashcarryoveritemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmCashcarryoveritemPojo update(FmCashcarryoveritemPojo fmCashcarryoveritemPojo) {
        FmCashcarryoveritemEntity fmCashcarryoveritemEntity = new FmCashcarryoveritemEntity(); 
        BeanUtils.copyProperties(fmCashcarryoveritemPojo,fmCashcarryoveritemEntity);
        this.fmCashcarryoveritemMapper.update(fmCashcarryoveritemEntity);
        return this.getEntity(fmCashcarryoveritemEntity.getId(),fmCashcarryoveritemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.fmCashcarryoveritemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param fmCashcarryoveritemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public FmCashcarryoveritemPojo clearNull(FmCashcarryoveritemPojo fmCashcarryoveritemPojo){
     //初始化NULL字段
     if(fmCashcarryoveritemPojo.getPid()==null) fmCashcarryoveritemPojo.setPid("");
     if(fmCashcarryoveritemPojo.getDirection()==null) fmCashcarryoveritemPojo.setDirection("");
     if(fmCashcarryoveritemPojo.getModulecode()==null) fmCashcarryoveritemPojo.setModulecode("");
     if(fmCashcarryoveritemPojo.getBilltype()==null) fmCashcarryoveritemPojo.setBilltype("");
     if(fmCashcarryoveritemPojo.getBilldate()==null) fmCashcarryoveritemPojo.setBilldate(new Date());
     if(fmCashcarryoveritemPojo.getBilltitle()==null) fmCashcarryoveritemPojo.setBilltitle("");
     if(fmCashcarryoveritemPojo.getBilluid()==null) fmCashcarryoveritemPojo.setBilluid("");
     if(fmCashcarryoveritemPojo.getBillid()==null) fmCashcarryoveritemPojo.setBillid("");
     if(fmCashcarryoveritemPojo.getOpenamount()==null) fmCashcarryoveritemPojo.setOpenamount(0D);
     if(fmCashcarryoveritemPojo.getInamount()==null) fmCashcarryoveritemPojo.setInamount(0D);
     if(fmCashcarryoveritemPojo.getOutamount()==null) fmCashcarryoveritemPojo.setOutamount(0D);
     if(fmCashcarryoveritemPojo.getCloseamount()==null) fmCashcarryoveritemPojo.setCloseamount(0D);
     if(fmCashcarryoveritemPojo.getRownum()==null) fmCashcarryoveritemPojo.setRownum(0);
     if(fmCashcarryoveritemPojo.getGroupid()==null)  fmCashcarryoveritemPojo.setGroupid("");
     if(fmCashcarryoveritemPojo.getGroupuid()==null)  fmCashcarryoveritemPojo.setGroupuid("");
     if(fmCashcarryoveritemPojo.getGroupname()==null) fmCashcarryoveritemPojo.setGroupname("");
     if(fmCashcarryoveritemPojo.getAbbreviate()==null) fmCashcarryoveritemPojo.setAbbreviate("");
     if(fmCashcarryoveritemPojo.getRemark()==null) fmCashcarryoveritemPojo.setRemark("");
     if(fmCashcarryoveritemPojo.getCustom1()==null) fmCashcarryoveritemPojo.setCustom1("");
     if(fmCashcarryoveritemPojo.getCustom2()==null) fmCashcarryoveritemPojo.setCustom2("");
     if(fmCashcarryoveritemPojo.getCustom3()==null) fmCashcarryoveritemPojo.setCustom3("");
     if(fmCashcarryoveritemPojo.getCustom4()==null) fmCashcarryoveritemPojo.setCustom4("");
     if(fmCashcarryoveritemPojo.getCustom5()==null) fmCashcarryoveritemPojo.setCustom5("");
     if(fmCashcarryoveritemPojo.getCustom6()==null) fmCashcarryoveritemPojo.setCustom6("");
     if(fmCashcarryoveritemPojo.getCustom7()==null) fmCashcarryoveritemPojo.setCustom7("");
     if(fmCashcarryoveritemPojo.getCustom8()==null) fmCashcarryoveritemPojo.setCustom8("");
     if(fmCashcarryoveritemPojo.getCustom9()==null) fmCashcarryoveritemPojo.setCustom9("");
     if(fmCashcarryoveritemPojo.getCustom10()==null) fmCashcarryoveritemPojo.setCustom10("");
     if(fmCashcarryoveritemPojo.getTenantid()==null) fmCashcarryoveritemPojo.setTenantid("");
     if(fmCashcarryoveritemPojo.getRevision()==null) fmCashcarryoveritemPojo.setRevision(0);
     return fmCashcarryoveritemPojo;
     }
}
