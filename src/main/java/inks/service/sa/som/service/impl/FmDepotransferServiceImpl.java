package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.FmDepotransferEntity;
import inks.service.sa.som.domain.FmDepotransferitemEntity;
import inks.service.sa.som.domain.FmDepotransfermachEntity;
import inks.service.sa.som.domain.pojo.*;
import inks.service.sa.som.mapper.BusDepositMapper;
import inks.service.sa.som.mapper.FmDepotransferMapper;
import inks.service.sa.som.mapper.FmDepotransferitemMapper;
import inks.service.sa.som.mapper.FmDepotransfermachMapper;
import inks.service.sa.som.service.FmDepotransferService;
import inks.service.sa.som.service.FmDepotransferitemService;
import inks.service.sa.som.service.FmDepotransfermachService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 预收核转(FmDepotransfer)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-14 17:21:14
 */
@Service("fmDepotransferService")
public class FmDepotransferServiceImpl implements FmDepotransferService {
    @Resource
    private FmDepotransferMapper fmDepotransferMapper;

    @Resource
    private FmDepotransferitemMapper fmDepotransferitemMapper;

    @Resource
    private FmDepotransfermachMapper fmDepotransfermachMapper;
    @Resource
    private FmDepotransferitemService fmDepotransferitemService;
    @Resource
    private FmDepotransfermachService fmDepotransfermachService;

    @Resource
    private BusDepositMapper busDepositMapper;

    @Override
    public FmDepotransferPojo getEntity(String key) {
        return this.fmDepotransferMapper.getEntity(key);
    }


    @Override
    public PageInfo<FmDepotransferitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmDepotransferitemdetailPojo> lst = fmDepotransferMapper.getPageList(queryParam);
            PageInfo<FmDepotransferitemdetailPojo> pageInfo = new PageInfo<FmDepotransferitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public FmDepotransferPojo getBillEntity(String key) {
        try {
            //读取主表
            FmDepotransferPojo fmDepotransferPojo = this.fmDepotransferMapper.getEntity(key);
            //读取子表
            fmDepotransferPojo.setItem(fmDepotransferitemMapper.getList(fmDepotransferPojo.getId()));
            // mach子表
            fmDepotransferPojo.setMach(fmDepotransfermachMapper.getList(fmDepotransferPojo.getId()));
            return fmDepotransferPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<FmDepotransferPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmDepotransferPojo> lst = fmDepotransferMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            // mach子表
            for (FmDepotransferPojo fmDepotransferPojo : lst) {
                fmDepotransferPojo.setItem(fmDepotransferitemMapper.getList(fmDepotransferPojo.getId()));
                fmDepotransferPojo.setMach(fmDepotransfermachMapper.getList(fmDepotransferPojo.getId()));
            }
            PageInfo<FmDepotransferPojo> pageInfo = new PageInfo<FmDepotransferPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<FmDepotransferPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmDepotransferPojo> lst = fmDepotransferMapper.getPageTh(queryParam);
            PageInfo<FmDepotransferPojo> pageInfo = new PageInfo<FmDepotransferPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public FmDepotransferPojo insert(FmDepotransferPojo fmDepotransferPojo) {
        String tid = fmDepotransferPojo.getTenantid();
        //初始化NULL字段
        cleanNull(fmDepotransferPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        FmDepotransferEntity fmDepotransferEntity = new FmDepotransferEntity();
        BeanUtils.copyProperties(fmDepotransferPojo, fmDepotransferEntity);
        //设置id和新建日期
        fmDepotransferEntity.setId(id);
        fmDepotransferEntity.setRevision(1);  //乐观锁
        //插入主表
        this.fmDepotransferMapper.insert(fmDepotransferEntity);
        //Item子表处理，预收单
        List<FmDepotransferitemPojo> lst = fmDepotransferPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (FmDepotransferitemPojo fmDepotransferitemPojo : lst) {
                //初始化item的NULL
                FmDepotransferitemPojo itemPojo = this.fmDepotransferitemService.clearNull(fmDepotransferitemPojo);
                FmDepotransferitemEntity fmDepotransferitemEntity = new FmDepotransferitemEntity();
                BeanUtils.copyProperties(itemPojo, fmDepotransferitemEntity);
                //设置id和Pid
                fmDepotransferitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                fmDepotransferitemEntity.setPid(id);
                fmDepotransferitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.fmDepotransferitemMapper.insert(fmDepotransferitemEntity);
                //更新关联单据:子表循环去更新预收单的转出金额Bus_Deposit.OutAmount
                //先检查加上本次的转入金额是否超过了预收单的金额
                BusDepositPojo depositDB = busDepositMapper.getEntity(fmDepotransferitemPojo.getPaybillid(), tid);
                if (depositDB.getOutamount() + fmDepotransferitemPojo.getAmount() > depositDB.getBillamount()) {
                    throw new BaseBusinessException("预收单[" + depositDB.getRefno() + "] 剩余可转金额：" + (depositDB.getBillamount() - depositDB.getOutamount()));
                }
                this.busDepositMapper.updateDepositOutAmount(fmDepotransferitemPojo.getPaybillid(), tid);
            }
        }
        // mach子表处理
        List<FmDepotransfermachPojo> machList = fmDepotransferPojo.getMach();
        if (machList != null) {
            //循环每个mach子表
            for (FmDepotransfermachPojo fmDepotransfermachPojo : machList) {
                //初始化mach的NULL
                FmDepotransfermachPojo machPojo = this.fmDepotransfermachService.clearNull(fmDepotransfermachPojo);
                FmDepotransfermachEntity fmDepotransfermachEntity = new FmDepotransfermachEntity();
                BeanUtils.copyProperties(machPojo, fmDepotransfermachEntity);
                //设置id和Pid
                fmDepotransfermachEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                fmDepotransfermachEntity.setPid(id);
                fmDepotransfermachEntity.setRevision(1);  //乐观锁
                //插入子表
                this.fmDepotransfermachMapper.insert(fmDepotransfermachEntity);
                //更新关联单据:子表循环去更新销售订单表的预收款Bus_Machining.AdvaAmount
                this.busDepositMapper.updateMachAdvaAmountFirstAmt(fmDepotransfermachPojo.getMachbillid(), tid);
                this.busDepositMapper.updateMachItemAvgFirstAmt(fmDepotransfermachPojo.getMachbillid(), tid);
            }
        }
        //返回Bill实例
        return this.getBillEntity(fmDepotransferEntity.getId());
    }


    @Override
    @Transactional
    public FmDepotransferPojo update(FmDepotransferPojo fmDepotransferPojo) {
        String tid = fmDepotransferPojo.getTenantid();
        //主表更改
        FmDepotransferEntity fmDepotransferEntity = new FmDepotransferEntity();
        BeanUtils.copyProperties(fmDepotransferPojo, fmDepotransferEntity);
        this.fmDepotransferMapper.update(fmDepotransferEntity);
        //Item子表处理
        if (fmDepotransferPojo.getItem() != null) {
            List<FmDepotransferitemPojo> lst = fmDepotransferPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = fmDepotransferMapper.getDelItemIds(fmDepotransferPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    FmDepotransferitemPojo itemDB = fmDepotransferitemMapper.getEntity(lstDelId);
                    this.fmDepotransferitemMapper.delete(lstDelId);
                    //更新关联单据:子表循环去更新预收单的转出金额Bus_Deposit.OutAmount
                    //先检查加上本次的转入金额是否超过了预收单的金额
                    this.busDepositMapper.updateDepositOutAmount(itemDB.getPaybillid(), tid);
                }
            }
            //循环每个item子表
            for (FmDepotransferitemPojo fmDepotransferitemPojo : lst) {
                FmDepotransferitemEntity fmDepotransferitemEntity = new FmDepotransferitemEntity();
                if ("".equals(fmDepotransferitemPojo.getId()) || fmDepotransferitemPojo.getId() == null) {
                    //初始化item的NULL
                    FmDepotransferitemPojo itemPojo = this.fmDepotransferitemService.clearNull(fmDepotransferitemPojo);
                    BeanUtils.copyProperties(itemPojo, fmDepotransferitemEntity);
                    //设置id和Pid
                    fmDepotransferitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    fmDepotransferitemEntity.setPid(fmDepotransferEntity.getId());  // 主表 id
                    fmDepotransferitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.fmDepotransferitemMapper.insert(fmDepotransferitemEntity);
                    //更新关联单据:子表循环去更新预收单的转出金额Bus_Deposit.OutAmount
                    //先检查加上本次的转入金额是否超过了预收单的金额
                    BusDepositPojo depositDB = busDepositMapper.getEntity(fmDepotransferitemPojo.getPaybillid(), tid);
                    if (depositDB.getOutamount() + fmDepotransferitemPojo.getAmount() > depositDB.getBillamount()) {
                        throw new BaseBusinessException("预收单[" + depositDB.getRefno() + "] 剩余可转金额：" + (depositDB.getBillamount() - depositDB.getOutamount()));
                    }
                    this.busDepositMapper.updateDepositOutAmount(fmDepotransferitemPojo.getPaybillid(), tid);
                } else {
                    BeanUtils.copyProperties(fmDepotransferitemPojo, fmDepotransferitemEntity);
                    FmDepotransferitemPojo itemDB = fmDepotransferitemMapper.getEntity(fmDepotransferitemEntity.getId());
                    this.fmDepotransferitemMapper.update(fmDepotransferitemEntity);
                    //更新关联单据:子表循环去更新预收单的转出金额Bus_Deposit.OutAmount
                    //先检查加上本次的转入金额是否超过了预收单的金额
                    BusDepositPojo depositDB = busDepositMapper.getEntity(fmDepotransferitemPojo.getPaybillid(), tid);
                    if (depositDB.getOutamount() + fmDepotransferitemPojo.getAmount() - itemDB.getAmount() > depositDB.getBillamount()) {
                        throw new BaseBusinessException("预收单[" + depositDB.getRefno() + "] 剩余可转金额：" + (depositDB.getBillamount() - depositDB.getOutamount() + itemDB.getAmount()));
                    }
                    this.busDepositMapper.updateDepositOutAmount(fmDepotransferitemPojo.getPaybillid(), tid);

                }

            }
        }
        //mach子表处理
        if (fmDepotransferPojo.getMach() != null) {
            List<FmDepotransfermachPojo> machList = fmDepotransferPojo.getMach();
            //获取被删除的mach
            List<String> lstDelIds = fmDepotransferMapper.getDelMachIds(fmDepotransferPojo);
            if (lstDelIds != null) {
                //循环每个删除mach子表
                for (String lstDelId : lstDelIds) {
                    FmDepotransfermachPojo machDB = fmDepotransfermachMapper.getEntity(lstDelId);
                    this.fmDepotransfermachMapper.delete(lstDelId);
                    //更新关联单据:子表循环去更新销售订单表的预收款Bus_Machining.AdvaAmount
                    this.busDepositMapper.updateMachAdvaAmountFirstAmt(machDB.getMachbillid(), tid);
                    this.busDepositMapper.updateMachItemAvgFirstAmt(machDB.getMachbillid(), tid);
                }
            }
            //循环每个mach子表
            for (FmDepotransfermachPojo fmDepotransfermachPojo : machList) {
                FmDepotransfermachEntity fmDepotransfermachEntity = new FmDepotransfermachEntity();
                if ("".equals(fmDepotransfermachPojo.getId()) || fmDepotransfermachPojo.getId() == null) {
                    //初始化mach的NULL
                    FmDepotransfermachPojo machPojo = this.fmDepotransfermachService.clearNull(fmDepotransfermachPojo);
                    BeanUtils.copyProperties(machPojo, fmDepotransfermachEntity);
                    //设置id和Pid
                    fmDepotransfermachEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // mach id
                    fmDepotransfermachEntity.setPid(fmDepotransferEntity.getId());  // 主表 id
                    fmDepotransfermachEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.fmDepotransfermachMapper.insert(fmDepotransfermachEntity);
                } else {
                    BeanUtils.copyProperties(fmDepotransfermachPojo, fmDepotransfermachEntity);
                    this.fmDepotransfermachMapper.update(fmDepotransfermachEntity);
                }
                //更新关联单据:子表循环去更新销售订单表的预收款Bus_Machining.AdvaAmount
                this.busDepositMapper.updateMachAdvaAmountFirstAmt(fmDepotransfermachPojo.getMachbillid(), tid);
                this.busDepositMapper.updateMachItemAvgFirstAmt(fmDepotransfermachPojo.getMachbillid(), tid);
            }
        }
        //返回Bill实例
        return this.getBillEntity(fmDepotransferEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
        FmDepotransferPojo fmDepotransferPojo = this.getBillEntity(key);
        String tid = fmDepotransferPojo.getTenantid();
        //Item子表处理
        List<FmDepotransferitemPojo> lst = fmDepotransferPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表、mach子表
            for (FmDepotransferitemPojo fmDepotransferitemPojo : lst) {
                this.fmDepotransferitemMapper.delete(fmDepotransferitemPojo.getId());
                //更新关联单据:子表循环去更新预收单的转出金额Bus_Deposit.OutAmount
                //先检查加上本次的转入金额是否超过了预收单的金额
                this.busDepositMapper.updateDepositOutAmount(fmDepotransferitemPojo.getPaybillid(), tid);
            }
        }
        // mach子表处理
        List<FmDepotransfermachPojo> machList = fmDepotransferPojo.getMach();
        if (machList != null) {
            //循环每个删除mach子表
            for (FmDepotransfermachPojo fmDepotransfermachPojo : machList) {
                this.fmDepotransfermachMapper.delete(fmDepotransfermachPojo.getId());
                //更新关联单据:子表循环去更新销售订单表的预收款Bus_Machining.AdvaAmount
                this.busDepositMapper.updateMachAdvaAmountFirstAmt(fmDepotransfermachPojo.getMachbillid(), null);
                this.busDepositMapper.updateMachItemAvgFirstAmt(fmDepotransfermachPojo.getMachbillid(), null);
            }
        }
        return this.fmDepotransferMapper.delete(key);
    }


    @Override
    @Transactional
    public FmDepotransferPojo approval(FmDepotransferPojo fmDepotransferPojo) {
        //主表更改
        FmDepotransferEntity fmDepotransferEntity = new FmDepotransferEntity();
        BeanUtils.copyProperties(fmDepotransferPojo, fmDepotransferEntity);
        this.fmDepotransferMapper.approval(fmDepotransferEntity);
        //返回Bill实例
        return this.getBillEntity(fmDepotransferEntity.getId());
    }

    private static void cleanNull(FmDepotransferPojo fmDepotransferPojo) {
        if (fmDepotransferPojo.getRefno() == null) fmDepotransferPojo.setRefno("");
        if (fmDepotransferPojo.getBilltype() == null) fmDepotransferPojo.setBilltype("");
        if (fmDepotransferPojo.getBilldate() == null) fmDepotransferPojo.setBilldate(new Date());
        if (fmDepotransferPojo.getBilltitle() == null) fmDepotransferPojo.setBilltitle("");
        if (fmDepotransferPojo.getGroupid() == null) fmDepotransferPojo.setGroupid("");
        if (fmDepotransferPojo.getPayamount() == null) fmDepotransferPojo.setPayamount(0D);
        if (fmDepotransferPojo.getApplied() == null) fmDepotransferPojo.setApplied(0D);
        if (fmDepotransferPojo.getOperator() == null) fmDepotransferPojo.setOperator("");
        if (fmDepotransferPojo.getSummary() == null) fmDepotransferPojo.setSummary("");
        if (fmDepotransferPojo.getCreateby() == null) fmDepotransferPojo.setCreateby("");
        if (fmDepotransferPojo.getCreatebyid() == null) fmDepotransferPojo.setCreatebyid("");
        if (fmDepotransferPojo.getCreatedate() == null) fmDepotransferPojo.setCreatedate(new Date());
        if (fmDepotransferPojo.getLister() == null) fmDepotransferPojo.setLister("");
        if (fmDepotransferPojo.getListerid() == null) fmDepotransferPojo.setListerid("");
        if (fmDepotransferPojo.getModifydate() == null) fmDepotransferPojo.setModifydate(new Date());
        if (fmDepotransferPojo.getAssessor() == null) fmDepotransferPojo.setAssessor("");
        if (fmDepotransferPojo.getAssessorid() == null) fmDepotransferPojo.setAssessorid("");
        if (fmDepotransferPojo.getAssessdate() == null) fmDepotransferPojo.setAssessdate(new Date());
        if (fmDepotransferPojo.getCustom1() == null) fmDepotransferPojo.setCustom1("");
        if (fmDepotransferPojo.getCustom2() == null) fmDepotransferPojo.setCustom2("");
        if (fmDepotransferPojo.getCustom3() == null) fmDepotransferPojo.setCustom3("");
        if (fmDepotransferPojo.getCustom4() == null) fmDepotransferPojo.setCustom4("");
        if (fmDepotransferPojo.getCustom5() == null) fmDepotransferPojo.setCustom5("");
        if (fmDepotransferPojo.getCustom6() == null) fmDepotransferPojo.setCustom6("");
        if (fmDepotransferPojo.getCustom7() == null) fmDepotransferPojo.setCustom7("");
        if (fmDepotransferPojo.getCustom8() == null) fmDepotransferPojo.setCustom8("");
        if (fmDepotransferPojo.getCustom9() == null) fmDepotransferPojo.setCustom9("");
        if (fmDepotransferPojo.getCustom10() == null) fmDepotransferPojo.setCustom10("");
        if (fmDepotransferPojo.getTenantid() == null) fmDepotransferPojo.setTenantid("");
        if (fmDepotransferPojo.getTenantname() == null) fmDepotransferPojo.setTenantname("");
        if (fmDepotransferPojo.getRevision() == null) fmDepotransferPojo.setRevision(0);
    }

}
