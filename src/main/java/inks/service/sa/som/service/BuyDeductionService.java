package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyDeductionPojo;
import inks.service.sa.som.domain.pojo.BuyDeductionitemdetailPojo;

import java.util.List;

/**
 * 采购扣款(BuyDeduction)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-06 20:35:56
 */
public interface BuyDeductionService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyDeductionPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyDeductionitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyDeductionPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyDeductionPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyDeductionPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyDeductionPojo 实例对象
     * @return 实例对象
     */
    BuyDeductionPojo insert(BuyDeductionPojo buyDeductionPojo);

    /**
     * 修改数据
     *
     * @param buyDeductionpojo 实例对象
     * @return 实例对象
     */
    BuyDeductionPojo update(BuyDeductionPojo buyDeductionpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param buyDeductionPojo 实例对象
     * @return 实例对象
     */
    BuyDeductionPojo approval(BuyDeductionPojo buyDeductionPojo);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(String key, String pid, String tid);

    void updatePrintcount(BuyDeductionPojo billPrintPojo);
}
