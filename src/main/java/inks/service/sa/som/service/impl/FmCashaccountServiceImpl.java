package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.FmCashaccountEntity;
import inks.service.sa.som.domain.pojo.FmCashaccountPojo;
import inks.service.sa.som.mapper.FmCashaccountMapper;
import inks.service.sa.som.service.FmCashaccountService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 出纳账户(FmCashaccount)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-15 16:58:10
 */
@Service("fmCashaccountService")
public class FmCashaccountServiceImpl implements FmCashaccountService {
    @Resource
    private FmCashaccountMapper fmCashaccountMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public FmCashaccountPojo getEntity(String key, String tid) {
        return this.fmCashaccountMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<FmCashaccountPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmCashaccountPojo> lst = fmCashaccountMapper.getPageList(queryParam);
            PageInfo<FmCashaccountPojo> pageInfo = new PageInfo<FmCashaccountPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param fmCashaccountPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmCashaccountPojo insert(FmCashaccountPojo fmCashaccountPojo) {
    //初始化NULL字段
     if(fmCashaccountPojo.getAccounttype()==null) fmCashaccountPojo.setAccounttype("");
     if(fmCashaccountPojo.getAccountname()==null) fmCashaccountPojo.setAccountname("");
     if(fmCashaccountPojo.getMinlimit()==null) fmCashaccountPojo.setMinlimit(0D);
     if(fmCashaccountPojo.getMaxlimit()==null) fmCashaccountPojo.setMaxlimit(0D);
     if(fmCashaccountPojo.getMoneyid()==null) fmCashaccountPojo.setMoneyid("");
     if(fmCashaccountPojo.getCurrentamt()==null) fmCashaccountPojo.setCurrentamt(0D);
     if(fmCashaccountPojo.getRownum()==null) fmCashaccountPojo.setRownum(0);
     if(fmCashaccountPojo.getEnabledmark()==null) fmCashaccountPojo.setEnabledmark(0);
     if(fmCashaccountPojo.getRemark()==null) fmCashaccountPojo.setRemark("");
     if(fmCashaccountPojo.getCreateby()==null) fmCashaccountPojo.setCreateby("");
     if(fmCashaccountPojo.getCreatebyid()==null) fmCashaccountPojo.setCreatebyid("");
     if(fmCashaccountPojo.getCreatedate()==null) fmCashaccountPojo.setCreatedate(new Date());
     if(fmCashaccountPojo.getLister()==null) fmCashaccountPojo.setLister("");
     if(fmCashaccountPojo.getListerid()==null) fmCashaccountPojo.setListerid("");
     if(fmCashaccountPojo.getModifydate()==null) fmCashaccountPojo.setModifydate(new Date());
     if(fmCashaccountPojo.getEndinuid()==null) fmCashaccountPojo.setEndinuid("");
     if(fmCashaccountPojo.getEndindate()==null) fmCashaccountPojo.setEndindate(new Date());
     if(fmCashaccountPojo.getEndoutuid()==null) fmCashaccountPojo.setEndoutuid("");
     if(fmCashaccountPojo.getEndoutdate()==null) fmCashaccountPojo.setEndoutdate(new Date());
     if(fmCashaccountPojo.getCustom1()==null) fmCashaccountPojo.setCustom1("");
     if(fmCashaccountPojo.getCustom2()==null) fmCashaccountPojo.setCustom2("");
     if(fmCashaccountPojo.getCustom3()==null) fmCashaccountPojo.setCustom3("");
     if(fmCashaccountPojo.getCustom4()==null) fmCashaccountPojo.setCustom4("");
     if(fmCashaccountPojo.getCustom5()==null) fmCashaccountPojo.setCustom5("");
     if(fmCashaccountPojo.getCustom6()==null) fmCashaccountPojo.setCustom6("");
     if(fmCashaccountPojo.getCustom7()==null) fmCashaccountPojo.setCustom7("");
     if(fmCashaccountPojo.getCustom8()==null) fmCashaccountPojo.setCustom8("");
     if(fmCashaccountPojo.getCustom9()==null) fmCashaccountPojo.setCustom9("");
     if(fmCashaccountPojo.getCustom10()==null) fmCashaccountPojo.setCustom10("");
     if(fmCashaccountPojo.getTenantid()==null) fmCashaccountPojo.setTenantid("");
     if(fmCashaccountPojo.getRevision()==null) fmCashaccountPojo.setRevision(0);
        FmCashaccountEntity fmCashaccountEntity = new FmCashaccountEntity(); 
        BeanUtils.copyProperties(fmCashaccountPojo,fmCashaccountEntity);
        
          fmCashaccountEntity.setId(UUID.randomUUID().toString());
          fmCashaccountEntity.setRevision(1);  //乐观锁
          this.fmCashaccountMapper.insert(fmCashaccountEntity);
        return this.getEntity(fmCashaccountEntity.getId(),fmCashaccountEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param fmCashaccountPojo 实例对象
     * @return 实例对象
     */
    @Override
    public FmCashaccountPojo update(FmCashaccountPojo fmCashaccountPojo) {
        FmCashaccountEntity fmCashaccountEntity = new FmCashaccountEntity(); 
        BeanUtils.copyProperties(fmCashaccountPojo,fmCashaccountEntity);
        this.fmCashaccountMapper.update(fmCashaccountEntity);
        return this.getEntity(fmCashaccountEntity.getId(),fmCashaccountEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.fmCashaccountMapper.delete(key,tid) ;
    }

    // 查询往来单是否被引用
    @Override
    public List<String> getCiteBillName(String key, String tid) {
        return this.fmCashaccountMapper.getCiteBillName(key, tid);
    }
}
