package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.MatSkumodelEntity;
import inks.service.sa.som.domain.pojo.MatSkumodelPojo;
import inks.service.sa.som.mapper.MatSkumodelMapper;
import inks.service.sa.som.service.MatSkumodelService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * SKU模版(MatSkumodel)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-10 08:49:03
 */
@Service("matSkumodelService")
public class MatSkumodelServiceImpl implements MatSkumodelService {
    @Resource
    private MatSkumodelMapper matSkumodelMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSkumodelPojo getEntity(String key, String tid) {
        return this.matSkumodelMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<MatSkumodelPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<MatSkumodelPojo> lst = matSkumodelMapper.getPageList(queryParam);
            PageInfo<MatSkumodelPojo> pageInfo = new PageInfo<MatSkumodelPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param matSkumodelPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSkumodelPojo insert(MatSkumodelPojo matSkumodelPojo) {
    //初始化NULL字段
     if(matSkumodelPojo.getSkuname()==null) matSkumodelPojo.setSkuname("");
     if(matSkumodelPojo.getQuickcode()==null) matSkumodelPojo.setQuickcode("");
     if(matSkumodelPojo.getAttributejson()==null) matSkumodelPojo.setAttributejson("");
     if(matSkumodelPojo.getSkuphoto()==null) matSkumodelPojo.setSkuphoto("");
     if(matSkumodelPojo.getRemark()==null) matSkumodelPojo.setRemark("");
     if(matSkumodelPojo.getRownum()==null) matSkumodelPojo.setRownum(0);
     if(matSkumodelPojo.getCreateby()==null) matSkumodelPojo.setCreateby("");
     if(matSkumodelPojo.getCreatebyid()==null) matSkumodelPojo.setCreatebyid("");
     if(matSkumodelPojo.getCreatedate()==null) matSkumodelPojo.setCreatedate(new Date());
     if(matSkumodelPojo.getLister()==null) matSkumodelPojo.setLister("");
     if(matSkumodelPojo.getListerid()==null) matSkumodelPojo.setListerid("");
     if(matSkumodelPojo.getModifydate()==null) matSkumodelPojo.setModifydate(new Date());
     if(matSkumodelPojo.getCustom1()==null) matSkumodelPojo.setCustom1("");
     if(matSkumodelPojo.getCustom2()==null) matSkumodelPojo.setCustom2("");
     if(matSkumodelPojo.getCustom3()==null) matSkumodelPojo.setCustom3("");
     if(matSkumodelPojo.getCustom4()==null) matSkumodelPojo.setCustom4("");
     if(matSkumodelPojo.getCustom5()==null) matSkumodelPojo.setCustom5("");
     if(matSkumodelPojo.getCustom6()==null) matSkumodelPojo.setCustom6("");
     if(matSkumodelPojo.getCustom7()==null) matSkumodelPojo.setCustom7("");
     if(matSkumodelPojo.getCustom8()==null) matSkumodelPojo.setCustom8("");
     if(matSkumodelPojo.getCustom9()==null) matSkumodelPojo.setCustom9("");
     if(matSkumodelPojo.getCustom10()==null) matSkumodelPojo.setCustom10("");
     if(matSkumodelPojo.getTenantid()==null) matSkumodelPojo.setTenantid("");
     if(matSkumodelPojo.getTenantname()==null) matSkumodelPojo.setTenantname("");
     if(matSkumodelPojo.getRevision()==null) matSkumodelPojo.setRevision(0);
        MatSkumodelEntity matSkumodelEntity = new MatSkumodelEntity(); 
        BeanUtils.copyProperties(matSkumodelPojo,matSkumodelEntity);
        
          matSkumodelEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          matSkumodelEntity.setRevision(1);  //乐观锁
          this.matSkumodelMapper.insert(matSkumodelEntity);
        return this.getEntity(matSkumodelEntity.getId(),matSkumodelEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param matSkumodelPojo 实例对象
     * @return 实例对象
     */
    @Override
    public MatSkumodelPojo update(MatSkumodelPojo matSkumodelPojo) {
        MatSkumodelEntity matSkumodelEntity = new MatSkumodelEntity(); 
        BeanUtils.copyProperties(matSkumodelPojo,matSkumodelEntity);
        this.matSkumodelMapper.update(matSkumodelEntity);
        return this.getEntity(matSkumodelEntity.getId(),matSkumodelEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.matSkumodelMapper.delete(key,tid) ;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public MatSkumodelPojo getEntityByCode(String key, String tid) {
        return this.matSkumodelMapper.getEntityByCode(key,tid);
    }
}
