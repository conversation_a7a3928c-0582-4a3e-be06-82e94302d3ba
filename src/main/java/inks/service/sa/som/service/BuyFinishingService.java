package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyFinishingPojo;
import inks.service.sa.som.domain.pojo.BuyFinishingitemPojo;
import inks.service.sa.som.domain.pojo.BuyFinishingitemdetailPojo;

import java.util.List;

/**
 * 采购验收(BuyFinishing)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-06 20:34:21
 */
public interface BuyFinishingService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyFinishingPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyFinishingitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyFinishingPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyFinishingPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<BuyFinishingPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyFinishingPojo 实例对象
     * @return 实例对象
     */
    BuyFinishingPojo insert(BuyFinishingPojo buyFinishingPojo);

    /**
     * 修改数据
     *
     * @param buyFinishingpojo 实例对象
     * @return 实例对象
     */
    BuyFinishingPojo update(BuyFinishingPojo buyFinishingpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    String delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param buyFinishingPojo 实例对象
     * @return 实例对象
     */
    BuyFinishingPojo approval(BuyFinishingPojo buyFinishingPojo);

    /**
     * 作废数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    BuyFinishingPojo disannul(List<BuyFinishingitemPojo> lst, Integer type, LoginUser loginUser);

    /**
     * 中止数据
     *
     * @param lst 实例对象
     * @return 实例对象
     */
    BuyFinishingPojo closed(List<BuyFinishingitemPojo> lst,Integer type, LoginUser loginUser);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(String key, String pid, String tid);

    void updatePrintcount(BuyFinishingPojo billPrintPojo);
}
