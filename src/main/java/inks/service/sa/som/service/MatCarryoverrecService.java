package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatCarryoverrecPojo;

/**
 * 仓库结账(MatCarryoverrec)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-05 12:50:55
 */
public interface MatCarryoverrecService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCarryoverrecPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatCarryoverrecPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matCarryoverrecPojo 实例对象
     * @return 实例对象
     */
    MatCarryoverrecPojo insert(MatCarryoverrecPojo matCarryoverrecPojo);

    /**
     * 修改数据
     *
     * @param matCarryoverrecpojo 实例对象
     * @return 实例对象
     */
    MatCarryoverrecPojo update(MatCarryoverrecPojo matCarryoverrecpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    MatCarryoverrecPojo getEntityByMax(String tid);
}
