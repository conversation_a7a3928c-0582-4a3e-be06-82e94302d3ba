package inks.service.sa.som.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatCargospacePojo;

/**
 * 仓库货位(MatCargospace)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-13 20:43:52
 */
public interface MatCargospaceService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCargospacePojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<MatCargospacePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matCargospacePojo 实例对象
     * @return 实例对象
     */
    MatCargospacePojo insert(MatCargospacePojo matCargospacePojo);

    /**
     * 修改数据
     *
     * @param matCargospacepojo 实例对象
     * @return 实例对象
     */
    MatCargospacePojo update(MatCargospacePojo matCargospacepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    boolean checkCodeOrName(String spacecode, String spacename, String tenantid);
}
