package inks.service.sa.som.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.som.domain.BuyPlanitemEntity;
import inks.service.sa.som.domain.pojo.BuyPlanitemPojo;
import inks.service.sa.som.mapper.BuyPlanitemMapper;
import inks.service.sa.som.service.BuyPlanitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 采购计划项目(BuyPlanitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 20:32:57
 */
@Service("buyPlanitemService")
public class BuyPlanitemServiceImpl implements BuyPlanitemService {
    @Resource
    private BuyPlanitemMapper buyPlanitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public BuyPlanitemPojo getEntity(String key, String tid) {
        return this.buyPlanitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<BuyPlanitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<BuyPlanitemPojo> lst = buyPlanitemMapper.getPageList(queryParam);
            PageInfo<BuyPlanitemPojo> pageInfo = new PageInfo<BuyPlanitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<BuyPlanitemPojo> getList(String Pid, String tid) {
        try {
            List<BuyPlanitemPojo> lst = buyPlanitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param buyPlanitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyPlanitemPojo insert(BuyPlanitemPojo buyPlanitemPojo) {
        //初始化item的NULL
        BuyPlanitemPojo itempojo = this.clearNull(buyPlanitemPojo);
        BuyPlanitemEntity buyPlanitemEntity = new BuyPlanitemEntity();
        BeanUtils.copyProperties(itempojo, buyPlanitemEntity);

        buyPlanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        buyPlanitemEntity.setRevision(1);  //乐观锁
        this.buyPlanitemMapper.insert(buyPlanitemEntity);
        return this.getEntity(buyPlanitemEntity.getId(), buyPlanitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param buyPlanitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyPlanitemPojo update(BuyPlanitemPojo buyPlanitemPojo) {
        BuyPlanitemEntity buyPlanitemEntity = new BuyPlanitemEntity();
        BeanUtils.copyProperties(buyPlanitemPojo, buyPlanitemEntity);
        this.buyPlanitemMapper.update(buyPlanitemEntity);
        return this.getEntity(buyPlanitemEntity.getId(), buyPlanitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.buyPlanitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param buyPlanitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public BuyPlanitemPojo clearNull(BuyPlanitemPojo buyPlanitemPojo) {
        //初始化NULL字段
        if (buyPlanitemPojo.getPid() == null) buyPlanitemPojo.setPid("");
        if (buyPlanitemPojo.getItemtype() == null) buyPlanitemPojo.setItemtype("");
        if (buyPlanitemPojo.getGoodsid() == null) buyPlanitemPojo.setGoodsid("");
        if (buyPlanitemPojo.getItemcode() == null) buyPlanitemPojo.setItemcode("");
        if (buyPlanitemPojo.getItemname() == null) buyPlanitemPojo.setItemname("");
        if (buyPlanitemPojo.getItemspec() == null) buyPlanitemPojo.setItemspec("");
        if (buyPlanitemPojo.getItemunit() == null) buyPlanitemPojo.setItemunit("");
        if (buyPlanitemPojo.getQuantity() == null) buyPlanitemPojo.setQuantity(0D);
        if (buyPlanitemPojo.getTaxprice() == null) buyPlanitemPojo.setTaxprice(0D);
        if (buyPlanitemPojo.getTaxamount() == null) buyPlanitemPojo.setTaxamount(0D);
        if (buyPlanitemPojo.getTaxtotal() == null) buyPlanitemPojo.setTaxtotal(0D);
        if (buyPlanitemPojo.getItemtaxrate() == null) buyPlanitemPojo.setItemtaxrate(0);
        if (buyPlanitemPojo.getPrice() == null) buyPlanitemPojo.setPrice(0D);
        if (buyPlanitemPojo.getAmount() == null) buyPlanitemPojo.setAmount(0D);
        if (buyPlanitemPojo.getPlandate() == null) buyPlanitemPojo.setPlandate(new Date());
        if (buyPlanitemPojo.getGroupid() == null) buyPlanitemPojo.setGroupid("");
        if (buyPlanitemPojo.getRemark() == null) buyPlanitemPojo.setRemark("");
        if (buyPlanitemPojo.getCiteuid() == null) buyPlanitemPojo.setCiteuid("");
        if (buyPlanitemPojo.getCiteitemid() == null) buyPlanitemPojo.setCiteitemid("");
        if (buyPlanitemPojo.getStatecode() == null) buyPlanitemPojo.setStatecode("");
        if (buyPlanitemPojo.getStatedate() == null) buyPlanitemPojo.setStatedate(new Date());
        if (buyPlanitemPojo.getClosed() == null) buyPlanitemPojo.setClosed(0);
        if (buyPlanitemPojo.getBuyqty() == null) buyPlanitemPojo.setBuyqty(0D);
        if (buyPlanitemPojo.getFinishqty() == null) buyPlanitemPojo.setFinishqty(0D);
        if (buyPlanitemPojo.getRownum() == null) buyPlanitemPojo.setRownum(0);
        if (buyPlanitemPojo.getMachuid() == null) buyPlanitemPojo.setMachuid("");
        if (buyPlanitemPojo.getMachitemid() == null) buyPlanitemPojo.setMachitemid("");
        if (buyPlanitemPojo.getMachbatch() == null) buyPlanitemPojo.setMachbatch("");
        if (buyPlanitemPojo.getMachgroupid() == null) buyPlanitemPojo.setMachgroupid("");
        if (buyPlanitemPojo.getMainplanuid() == null) buyPlanitemPojo.setMainplanuid("");
        if (buyPlanitemPojo.getMainplanitemid() == null) buyPlanitemPojo.setMainplanitemid("");
        if (buyPlanitemPojo.getMrpuid() == null) buyPlanitemPojo.setMrpuid("");
     if(buyPlanitemPojo.getMrpitemid()==null) buyPlanitemPojo.setMrpitemid("");
     if(buyPlanitemPojo.getMrpobjgoodsid()==null) buyPlanitemPojo.setMrpobjgoodsid("");
        if (buyPlanitemPojo.getCustomer() == null) buyPlanitemPojo.setCustomer("");
        if (buyPlanitemPojo.getCustpo() == null) buyPlanitemPojo.setCustpo("");
        if (buyPlanitemPojo.getBatchno() == null) buyPlanitemPojo.setBatchno("");
        if (buyPlanitemPojo.getDisannulmark() == null) buyPlanitemPojo.setDisannulmark(0);
        if (buyPlanitemPojo.getDisannullisterid() == null) buyPlanitemPojo.setDisannullisterid("");
        if (buyPlanitemPojo.getDisannullister() == null) buyPlanitemPojo.setDisannullister("");
        if (buyPlanitemPojo.getDisannuldate() == null) buyPlanitemPojo.setDisannuldate(new Date());
        if (buyPlanitemPojo.getAttributejson() == null) buyPlanitemPojo.setAttributejson("");
        if (buyPlanitemPojo.getSourcetype() == null) buyPlanitemPojo.setSourcetype(0);
     if(buyPlanitemPojo.getMergeid()==null) buyPlanitemPojo.setMergeid("");
     if(buyPlanitemPojo.getMergebuyqty()==null) buyPlanitemPojo.setMergebuyqty(0D);
     if(buyPlanitemPojo.getMergefinishqty()==null) buyPlanitemPojo.setMergefinishqty(0D);
     if(buyPlanitemPojo.getMergemark()==null) buyPlanitemPojo.setMergemark(0);
     if(buyPlanitemPojo.getMergeitems()==null) buyPlanitemPojo.setMergeitems("");
     if(buyPlanitemPojo.getPlanqty()==null) buyPlanitemPojo.setPlanqty(0D);
     if(buyPlanitemPojo.getCustom1()==null) buyPlanitemPojo.setCustom1("");
        if (buyPlanitemPojo.getCustom2() == null) buyPlanitemPojo.setCustom2("");
        if (buyPlanitemPojo.getCustom3() == null) buyPlanitemPojo.setCustom3("");
        if (buyPlanitemPojo.getCustom4() == null) buyPlanitemPojo.setCustom4("");
        if (buyPlanitemPojo.getCustom5() == null) buyPlanitemPojo.setCustom5("");
        if (buyPlanitemPojo.getCustom6() == null) buyPlanitemPojo.setCustom6("");
        if (buyPlanitemPojo.getCustom7() == null) buyPlanitemPojo.setCustom7("");
        if (buyPlanitemPojo.getCustom8() == null) buyPlanitemPojo.setCustom8("");
        if (buyPlanitemPojo.getCustom9() == null) buyPlanitemPojo.setCustom9("");
        if (buyPlanitemPojo.getCustom10() == null) buyPlanitemPojo.setCustom10("");
        if (buyPlanitemPojo.getTenantid() == null) buyPlanitemPojo.setTenantid("");
        if (buyPlanitemPojo.getRevision() == null) buyPlanitemPojo.setRevision(0);
        return buyPlanitemPojo;
    }
}
