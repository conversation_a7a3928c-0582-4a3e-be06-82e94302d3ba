package inks.service.sa.som.service;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;

import java.util.List;

public interface D03MBIR1Service {
    /*
     *
     * <AUTHOR>
     * @description 供应商金额占比
     * @date 2021/12/31
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByGroupMax(QueryParam queryParam);

    /*
     *
     * <AUTHOR>
     * @description 货品排行
     * @date 2021/12/31
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByGoodsMax(QueryParam queryParam);

    /*
     *
     * <AUTHOR>
     * @description 销售趋势图年度
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByYear(QueryParam queryParam, Integer trend);
    /*
     *
     * <AUTHOR>
     * @description 销售趋势图月度
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByMonth(QueryParam queryParam, Integer trend);
    /*
     *
     * <AUTHOR>
     * @description 销售趋势图周度
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByDay(QueryParam queryParam, Integer trend);

    /*
     *
     * <AUTHOR>
     * @description 本月销售额
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    ChartPojo getTagSumAmtQtyByDate(QueryParam queryParam);

//     /*
//      *
//      * <AUTHOR>
//      * @description 应付返回的记录数
//      * @date 2021/12/31
//      * @param * @param null
//      * @return
//      */
//    List<ChartPojo> getItemCountSumAmtGroupMonth(String tid);
//     /*
//      *
//      * <AUTHOR>
//      * @description 预警记录
//      * @date 2021/12/31
//      * @param * @param null
//      * @return
//      */
//    List<Map<String,Object>> getList(String tid);
//
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 采购饼状图年
//     * @date 2021/12/31
//     * @param * @param null
//     * @return
//     */
//    List<ChartPojo> getSumAmtByYearMax(QueryParam queryParam);
//    /*
//     *
//     * <AUTHOR>
//     * @description 采购饼状图月
//     * @date 2021/12/31
//     * @param * @param null
//     * @return
//     */
//    List<ChartPojo> getSumAmtByMonthMax(QueryParam queryParam);
//    /*
//     *
//     * <AUTHOR>
//     * @description 采购饼状图日
//     * @date 2021/12/31
//     * @param * @param null
//     * @return
//     */
//    List<ChartPojo> getSumAmtByDayMax(QueryParam queryParam);
//    /*
//     *
//     * <AUTHOR>
//     * @description 本月采购额
//     * @date 2021/12/31
//     * @param * @param null
//     * @return
//     */
//    List<ChartPojo> getItemCountByMonth(QueryParam queryParam);

}
