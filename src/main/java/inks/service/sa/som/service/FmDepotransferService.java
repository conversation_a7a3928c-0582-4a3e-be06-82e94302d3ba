package inks.service.sa.som.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmDepotransferPojo;
import inks.service.sa.som.domain.pojo.FmDepotransferitemdetailPojo;
import inks.service.sa.som.domain.FmDepotransferEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

/**
 * 预收核转(FmDepotransfer)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-14 17:21:14
 */
public interface FmDepotransferService {


    FmDepotransferPojo getEntity(String key);

    PageInfo<FmDepotransferitemdetailPojo> getPageList(QueryParam queryParam);

    FmDepotransferPojo getBillEntity(String key);

    PageInfo<FmDepotransferPojo> getBillList(QueryParam queryParam);

    PageInfo<FmDepotransferPojo> getPageTh(QueryParam queryParam);

    FmDepotransferPojo insert(FmDepotransferPojo fmDepotransferPojo);

    FmDepotransferPojo update(FmDepotransferPojo fmDepotransferpojo);

    int delete(String key);


     FmDepotransferPojo approval(FmDepotransferPojo fmDepotransferPojo);
}
