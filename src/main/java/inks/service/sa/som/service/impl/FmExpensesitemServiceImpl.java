package inks.service.sa.som.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.som.domain.pojo.FmExpensesitemPojo;
import inks.service.sa.som.domain.FmExpensesitemEntity;
import inks.service.sa.som.mapper.FmExpensesitemMapper;
import inks.service.sa.som.service.FmExpensesitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 费用报销单明细表(FmExpensesitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-26 15:43:25
 */
@Service("fmExpensesitemService")
public class FmExpensesitemServiceImpl implements FmExpensesitemService {
    @Resource
    private FmExpensesitemMapper fmExpensesitemMapper;

    @Override
    public FmExpensesitemPojo getEntity(String key) {
        return this.fmExpensesitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<FmExpensesitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<FmExpensesitemPojo> lst = fmExpensesitemMapper.getPageList(queryParam);
            PageInfo<FmExpensesitemPojo> pageInfo = new PageInfo<FmExpensesitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<FmExpensesitemPojo> getList(String Pid) { 
        try {
            List<FmExpensesitemPojo> lst = fmExpensesitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public FmExpensesitemPojo insert(FmExpensesitemPojo fmExpensesitemPojo) {
        //初始化item的NULL
        FmExpensesitemPojo itempojo =this.clearNull(fmExpensesitemPojo);
        FmExpensesitemEntity fmExpensesitemEntity = new FmExpensesitemEntity(); 
        BeanUtils.copyProperties(itempojo,fmExpensesitemEntity);
         //生成雪花id
          fmExpensesitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          fmExpensesitemEntity.setRevision(1);  //乐观锁      
          this.fmExpensesitemMapper.insert(fmExpensesitemEntity);
        return this.getEntity(fmExpensesitemEntity.getId());
  
    }

    @Override
    public FmExpensesitemPojo update(FmExpensesitemPojo fmExpensesitemPojo) {
        FmExpensesitemEntity fmExpensesitemEntity = new FmExpensesitemEntity(); 
        BeanUtils.copyProperties(fmExpensesitemPojo,fmExpensesitemEntity);
        this.fmExpensesitemMapper.update(fmExpensesitemEntity);
        return this.getEntity(fmExpensesitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.fmExpensesitemMapper.delete(key) ;
    }

     @Override
     public FmExpensesitemPojo clearNull(FmExpensesitemPojo fmExpensesitemPojo){
     //初始化NULL字段
     if(fmExpensesitemPojo.getPid()==null) fmExpensesitemPojo.setPid("");
     if(fmExpensesitemPojo.getExpedate()==null) fmExpensesitemPojo.setExpedate(new Date());
     if(fmExpensesitemPojo.getExpeitem()==null) fmExpensesitemPojo.setExpeitem("");
     if(fmExpensesitemPojo.getExpetype()==null) fmExpensesitemPojo.setExpetype("");
     if(fmExpensesitemPojo.getAmount()==null) fmExpensesitemPojo.setAmount(0D);
     if(fmExpensesitemPojo.getRownum()==null) fmExpensesitemPojo.setRownum(0);
     if(fmExpensesitemPojo.getRemark()==null) fmExpensesitemPojo.setRemark("");
     if(fmExpensesitemPojo.getCreateby()==null) fmExpensesitemPojo.setCreateby("");
     if(fmExpensesitemPojo.getCreatebyid()==null) fmExpensesitemPojo.setCreatebyid("");
     if(fmExpensesitemPojo.getCreatedate()==null) fmExpensesitemPojo.setCreatedate(new Date());
     if(fmExpensesitemPojo.getLister()==null) fmExpensesitemPojo.setLister("");
     if(fmExpensesitemPojo.getListerid()==null) fmExpensesitemPojo.setListerid("");
     if(fmExpensesitemPojo.getModifydate()==null) fmExpensesitemPojo.setModifydate(new Date());
     if(fmExpensesitemPojo.getCustom1()==null) fmExpensesitemPojo.setCustom1("");
     if(fmExpensesitemPojo.getCustom2()==null) fmExpensesitemPojo.setCustom2("");
     if(fmExpensesitemPojo.getCustom3()==null) fmExpensesitemPojo.setCustom3("");
     if(fmExpensesitemPojo.getCustom4()==null) fmExpensesitemPojo.setCustom4("");
     if(fmExpensesitemPojo.getCustom5()==null) fmExpensesitemPojo.setCustom5("");
     if(fmExpensesitemPojo.getCustom6()==null) fmExpensesitemPojo.setCustom6("");
     if(fmExpensesitemPojo.getCustom7()==null) fmExpensesitemPojo.setCustom7("");
     if(fmExpensesitemPojo.getCustom8()==null) fmExpensesitemPojo.setCustom8("");
     if(fmExpensesitemPojo.getCustom9()==null) fmExpensesitemPojo.setCustom9("");
     if(fmExpensesitemPojo.getCustom10()==null) fmExpensesitemPojo.setCustom10("");
     if(fmExpensesitemPojo.getTenantid()==null) fmExpensesitemPojo.setTenantid("");
     if(fmExpensesitemPojo.getRevision()==null) fmExpensesitemPojo.setRevision(0);
     return fmExpensesitemPojo;
     }
}
