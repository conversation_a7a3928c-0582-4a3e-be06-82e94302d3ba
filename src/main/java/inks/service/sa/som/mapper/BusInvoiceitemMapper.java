package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BusInvoiceitemEntity;
import inks.service.sa.som.domain.pojo.BusInvoiceitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发票项目(BusInvoiceitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-11 10:00:35
 */
 @Mapper
public interface BusInvoiceitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusInvoiceitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusInvoiceitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusInvoiceitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param busInvoiceitemEntity 实例对象
     * @return 影响行数
     */
    int insert(BusInvoiceitemEntity busInvoiceitemEntity);

    
    /**
     * 修改数据
     *
     * @param busInvoiceitemEntity 实例对象
     * @return 影响行数
     */
    int update(BusInvoiceitemEntity busInvoiceitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

