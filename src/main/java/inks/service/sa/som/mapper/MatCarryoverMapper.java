package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatCarryoverEntity;
import inks.service.sa.som.domain.pojo.MatCarryoverPojo;
import inks.service.sa.som.domain.pojo.MatCarryoveritemPojo;
import inks.service.sa.som.domain.pojo.MatCarryoveritemdetailPojo;
import inks.service.sa.som.domain.pojo.MatStoragePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 仓库结转(MatCarryover)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-06 11:23:59
 */
@Mapper
public interface MatCarryoverMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCarryoverPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatCarryoveritemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatCarryoverPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matCarryoverEntity 实例对象
     * @return 影响行数
     */
    int insert(MatCarryoverEntity matCarryoverEntity);


    /**
     * 修改数据
     *
     * @param matCarryoverEntity 实例对象
     * @return 影响行数
     */
    int update(MatCarryoverEntity matCarryoverEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param matCarryoverPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(MatCarryoverPojo matCarryoverPojo);


    /**
     * 根据
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    List<MatCarryoveritemPojo> getGoodsAcceList(QueryParam queryParam);

    /**
     * 查询 被删除的Item
     *
     * @param tid 筛选条件
     * @return 查询结果
     */
    List<MatStoragePojo> getStoreList(String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCarryoverPojo getMaxEntityByStore(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 所有Item
     *
     * @return 查询结果
     */
    List<MatCarryoveritemPojo> getMultItemList(QueryParam queryParam);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatCarryoverPojo> getNowPageList(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteByMonth(@Param("year") Integer year, @Param("nonth") Integer nonth,@Param("tid") String tid);

}

