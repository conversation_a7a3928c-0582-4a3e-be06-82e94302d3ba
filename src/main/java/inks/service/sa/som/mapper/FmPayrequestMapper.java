package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.FmPayrequestEntity;
import inks.service.sa.som.domain.pojo.FmPayrequestPojo;
import inks.service.sa.som.domain.pojo.FmPayrequestitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 付款申请单(FmPayrequest)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-07 13:31:28
 */
@Mapper
public interface FmPayrequestMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmPayrequestPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmPayrequestitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmPayrequestPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param fmPayrequestEntity 实例对象
     * @return 影响行数
     */
    int insert(FmPayrequestEntity fmPayrequestEntity);

    
    /**
     * 修改数据
     *
     * @param fmPayrequestEntity 实例对象
     * @return 影响行数
     */
    int update(FmPayrequestEntity fmPayrequestEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);
    
     /**
     * 查询 被删除的Item
     *
     * @param fmPayrequestPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(FmPayrequestPojo fmPayrequestPojo);
                                                                                                                                                                    /**
     * 修改数据
     *
     * @param fmPayrequestEntity 实例对象
     * @return 影响行数
     */
    int approval(FmPayrequestEntity fmPayrequestEntity);
                                                                                                               }

