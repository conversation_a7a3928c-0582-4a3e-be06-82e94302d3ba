package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyPrepaymentsEntity;
import inks.service.sa.som.domain.pojo.BuyPrepaymentsPojo;
import inks.service.sa.som.domain.pojo.BuyPrepaymentsitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预付款(BuyPrepayments)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:02:14
 */
@Mapper
public interface BuyPrepaymentsMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyPrepaymentsPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyPrepaymentsitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyPrepaymentsPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param buyPrepaymentsEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyPrepaymentsEntity buyPrepaymentsEntity);

    
    /**
     * 修改数据
     *
     * @param buyPrepaymentsEntity 实例对象
     * @return 影响行数
     */
    int update(BuyPrepaymentsEntity buyPrepaymentsEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param buyPrepaymentsPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(BuyPrepaymentsPojo buyPrepaymentsPojo);

    /**
     * 查询 被删除的Item
     *
     * @param buyPrepaymentsPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelCashIds(BuyPrepaymentsPojo buyPrepaymentsPojo);                                                                                             /**
     * 修改数据
     *
     * @param buyPrepaymentsEntity 实例对象
     * @return 影响行数
     */
    int approval(BuyPrepaymentsEntity buyPrepaymentsEntity);

    void updateOrderAdvaAmountFirstAmt(@Param("buyOrderId") String buyOrderId, @Param("tid") String tid);

    void updateOrderItemAvgFirstAmt(@Param("buyOrderId") String buyOrderId, @Param("tid") String tid);
}

