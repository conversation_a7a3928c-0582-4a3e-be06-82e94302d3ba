package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatGroupEntity;
import inks.service.sa.som.domain.pojo.MatGroupPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物料分组(MatGroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-21 09:03:24
 */
@Mapper
public interface MatGroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatGroupPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatGroupPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param matGroupEntity 实例对象
     * @return 影响行数
     */
    int insert(MatGroupEntity matGroupEntity);

    
    /**
     * 修改数据
     *
     * @param matGroupEntity 实例对象
     * @return 影响行数
     */
    int update(MatGroupEntity matGroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);


    List<MatGroupPojo> getListByParentid(@Param("key")String key,@Param("tid")String tid);

}

