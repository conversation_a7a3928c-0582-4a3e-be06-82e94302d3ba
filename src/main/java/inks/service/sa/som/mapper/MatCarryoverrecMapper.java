package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatCarryoverrecEntity;
import inks.service.sa.som.domain.pojo.MatCarryoverrecPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 仓库结账(MatCarryoverrec)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-10-05 12:50:55
 */
@Mapper
public interface MatCarryoverrecMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCarryoverrecPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatCarryoverrecPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param matCarryoverrecEntity 实例对象
     * @return 影响行数
     */
    int insert(MatCarryoverrecEntity matCarryoverrecEntity);


    /**
     * 修改数据
     *
     * @param matCarryoverrecEntity 实例对象
     * @return 影响行数
     */
    int update(MatCarryoverrecEntity matCarryoverrecEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    MatCarryoverrecPojo getEntityByMax(@Param("tid") String tid);

}

