package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BusAccountinvoEntity;
import inks.service.sa.som.domain.pojo.BusAccountinvoPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 销售单to发票(BusAccountinvo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-16 11:27:03
 */
 @Mapper
public interface BusAccountinvoMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountinvoPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusAccountinvoPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusAccountinvoPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param busAccountinvoEntity 实例对象
     * @return 影响行数
     */
    int insert(BusAccountinvoEntity busAccountinvoEntity);

    
    /**
     * 修改数据
     *
     * @param busAccountinvoEntity 实例对象
     * @return 影响行数
     */
    int update(BusAccountinvoEntity busAccountinvoEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

