package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyAccountinvoEntity;
import inks.service.sa.som.domain.pojo.BuyAccountinvoPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收货单to发票(BuyAccountinvo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-17 10:52:03
 */
 @Mapper
public interface BuyAccountinvoMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyAccountinvoPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyAccountinvoPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyAccountinvoPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param buyAccountinvoEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyAccountinvoEntity buyAccountinvoEntity);

    
    /**
     * 修改数据
     *
     * @param buyAccountinvoEntity 实例对象
     * @return 影响行数
     */
    int update(BuyAccountinvoEntity buyAccountinvoEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

