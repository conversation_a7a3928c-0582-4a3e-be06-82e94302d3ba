package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BusDeductionEntity;
import inks.service.sa.som.domain.pojo.BusDeductionPojo;
import inks.service.sa.som.domain.pojo.BusDeductionitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 销售扣款(BusDeduction)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:31:48
 */
@Mapper
public interface BusDeductionMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusDeductionPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusDeductionitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusDeductionPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param busDeductionEntity 实例对象
     * @return 影响行数
     */
    int insert(BusDeductionEntity busDeductionEntity);


    /**
     * 修改数据
     *
     * @param busDeductionEntity 实例对象
     * @return 影响行数
     */
    int update(BusDeductionEntity busDeductionEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param busDeductionPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BusDeductionPojo busDeductionPojo);

    /**
     * 修改数据
     *
     * @param busDeductionEntity 实例对象
     * @return 影响行数
     */
    int approval(BusDeductionEntity busDeductionEntity);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(@Param("key") String key, @Param("pid") String pid,@Param("tid") String tid);

    void updatePrintcount(BusDeductionPojo billPrintPojo);
}

