package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatSkuEntity;
import inks.service.sa.som.domain.pojo.MatAttributePojo;
import inks.service.sa.som.domain.pojo.MatSkuPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 货品SKU(MatSku)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-12 13:25:57
 */
@Mapper
public interface MatSkuMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSkuPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSkuPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param matSkuEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSkuEntity matSkuEntity);


    /**
     * 修改数据
     *
     * @param matSkuEntity 实例对象
     * @return 影响行数
     */
    int update(MatSkuEntity matSkuEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    List<MatSkuPojo> getListByGoodsid(@Param("goodsid") String goodsid, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param goodsid 主键
     * @return 实例对象
     */
    List<MatSkuPojo> getListByGoodsAttr(@Param("goodsid") String goodsid, @Param("lst") List<Map<String, Object>> lst, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param goodsid 主键
     * @return 实例对象
     */
    MatSkuPojo getEntityByGoodsMax(@Param("goodsid") String goodsid, @Param("tid") String tid);

    List<MatAttributePojo> getAttrList(@Param("tid") String tid);
}

