package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyInvoiceEntity;
import inks.service.sa.som.domain.pojo.BuyInvoicePojo;
import inks.service.sa.som.domain.pojo.BuyInvoiceitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 采购开票(BuyInvoice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:01:35
 */
@Mapper
public interface BuyInvoiceMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyInvoicePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyInvoiceitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyInvoicePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyInvoiceEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyInvoiceEntity buyInvoiceEntity);


    /**
     * 修改数据
     *
     * @param buyInvoiceEntity 实例对象
     * @return 影响行数
     */
    int update(BuyInvoiceEntity buyInvoiceEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param buyInvoicePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BuyInvoicePojo buyInvoicePojo);

    /**
     * 修改数据
     *
     * @param buyInvoiceEntity 实例对象
     * @return 影响行数
     */
    int approval(BuyInvoiceEntity buyInvoiceEntity);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateFiniInvoFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    int updateOrderItemInvoFinish(@Param("key") String orderitemid, @Param("refno") String orderuid, @Param("tid") String tenantid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateFiniInvoCount(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    int updateOrderInvoCount(@Param("key") String orderitemid, @Param("refno") String orderuid, @Param("tid") String tenantid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDeduInvoFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDeduInvoCount(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    // 查询Item是否被引用
    String getCiteBillName(@Param("key") String key,  @Param("tid") String tid);

    void updateScCompleteItemInvoFinish(@Param("key") String finishitemid, @Param("tid") String tenantid);

    void updateScCompleteInvoCount(@Param("key") String finishitemid, @Param("tid") String tenantid);

    Map<String, Object> getScCompleteItemEntity(@Param("scCompleteItemid") String finishitemid, @Param("tid") String tenantid);

    String getOrerId(@Param("orderitemid") String orderitemid, @Param("tenantid") String tenantid);

    void updateOrderItemAvgInvoAmt(@Param("orerId") String orerId, @Param("tenantid") String tenantid);
}

