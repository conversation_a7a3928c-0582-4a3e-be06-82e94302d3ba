package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyDeductionEntity;
import inks.service.sa.som.domain.pojo.BuyDeductionPojo;
import inks.service.sa.som.domain.pojo.BuyDeductionitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购扣款(BuyDeduction)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-06 20:35:56
 */
@Mapper
public interface BuyDeductionMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyDeductionPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyDeductionitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyDeductionPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyDeductionEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyDeductionEntity buyDeductionEntity);


    /**
     * 修改数据
     *
     * @param buyDeductionEntity 实例对象
     * @return 影响行数
     */
    int update(BuyDeductionEntity buyDeductionEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param buyDeductionPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BuyDeductionPojo buyDeductionPojo);

    /**
     * 修改数据
     *
     * @param buyDeductionEntity 实例对象
     * @return 影响行数
     */
    int approval(BuyDeductionEntity buyDeductionEntity);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(@Param("key") String key, @Param("pid") String pid,@Param("tid") String tid);

    void updatePrintcount(BuyDeductionPojo billPrintPojo);
}

