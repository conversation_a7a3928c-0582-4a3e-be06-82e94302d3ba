package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmExpensesitemPojo;
import inks.service.sa.som.domain.FmExpensesitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 费用报销单明细表(FmExpensesitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-26 15:43:25
 */
 @Mapper
public interface FmExpensesitemMapper {

    FmExpensesitemPojo getEntity(@Param("key") String key);

    List<FmExpensesitemPojo> getPageList(QueryParam queryParam);

    List<FmExpensesitemPojo> getList(@Param("Pid") String Pid);    

    int insert(FmExpensesitemEntity fmExpensesitemEntity);

    int update(FmExpensesitemEntity fmExpensesitemEntity);

    int delete(@Param("key") String key);

}

