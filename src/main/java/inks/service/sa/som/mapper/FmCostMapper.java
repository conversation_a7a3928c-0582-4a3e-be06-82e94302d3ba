package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.FmCostEntity;
import inks.service.sa.som.domain.pojo.FmCostPojo;
import inks.service.sa.som.domain.pojo.FmCostitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 费用开支(FmCost)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-08-29 15:25:07
 */
@Mapper
public interface FmCostMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCostPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmCostitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmCostPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param fmCostEntity 实例对象
     * @return 影响行数
     */
    int insert(FmCostEntity fmCostEntity);


    /**
     * 修改数据
     *
     * @param fmCostEntity 实例对象
     * @return 影响行数
     */
    int update(FmCostEntity fmCostEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param fmCostPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(FmCostPojo fmCostPojo);

    /**
     * 刷新出纳账户额
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateCashAmount(@Param("key") String key, @Param("amount") Double amount, @Param("tid") String tid);

}

