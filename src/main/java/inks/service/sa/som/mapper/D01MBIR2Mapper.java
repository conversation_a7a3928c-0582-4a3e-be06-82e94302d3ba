package inks.service.sa.som.mapper;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface D01MBIR2Mapper {
    /*
     *
     * <AUTHOR>
     * @description 货品金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByGoodsMax(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 销售趋势图年度
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByYear(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 销售趋势图月度
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByMonth(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 销售趋势图周度
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByDay(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 本月销售额
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    ChartPojo getTagSumAmtQtyByDate(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 根据当前月查询本月开票
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getTagSumAmtByMonth(String tid);
    /*
     *
     * <AUTHOR>
     * @description 客户订单金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByGroupMax(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 订单逾期
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    ChartPojo getPageList(String tid);
    /*
     *
     * <AUTHOR>
     * @description 热销产品
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumByGoodsMax(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 业务员订单金额占比
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtBySalesman(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 查询在线订单数
     * @date 2021/12/29
     * @param * @param null
     * @return
     */
    Integer getCountMachItemOnline(String tid);
}
