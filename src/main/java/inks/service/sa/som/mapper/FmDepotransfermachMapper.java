package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmDepotransfermachPojo;
import inks.service.sa.som.domain.FmDepotransfermachEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 销售订单子表(FmDepotransfermach)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-14 17:21:36
 */
 @Mapper
public interface FmDepotransfermachMapper {

    FmDepotransfermachPojo getEntity(@Param("key") String key);

    List<FmDepotransfermachPojo> getPageList(QueryParam queryParam);

    List<FmDepotransfermachPojo> getList(@Param("Pid") String Pid);    

    int insert(FmDepotransfermachEntity fmDepotransfermachEntity);

    int update(FmDepotransfermachEntity fmDepotransfermachEntity);

    int delete(@Param("key") String key);

}

