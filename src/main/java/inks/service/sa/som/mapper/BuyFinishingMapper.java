package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyFinishingEntity;
import inks.service.sa.som.domain.pojo.BuyFinishingPojo;
import inks.service.sa.som.domain.pojo.BuyFinishingitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购验收(BuyFinishing)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 14:59:52
 */
@Mapper
public interface BuyFinishingMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyFinishingPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyFinishingitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyFinishingPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyFinishingEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyFinishingEntity buyFinishingEntity);


    /**
     * 修改数据
     *
     * @param buyFinishingEntity 实例对象
     * @return 影响行数
     */
    int update(BuyFinishingEntity buyFinishingEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param buyFinishingPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BuyFinishingPojo buyFinishingPojo);

    /**
     * 修改数据
     *
     * @param buyFinishingEntity 实例对象
     * @return 影响行数
     */
    int approval(BuyFinishingEntity buyFinishingEntity);

    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDisannulCountAndAmount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateFinishCount(@Param("key") String key, @Param("tid") String tid);
    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateOrderFinishQty(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateOrderFinishCount(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(@Param("key") String key, @Param("pid") String pid, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateGoodsBuyRemQty(@Param("key") String key, @Param("goodsuid") String goodsuid, @Param("tid") String tid);

    void updatePrintcount(BuyFinishingPojo billPrintPojo);

    List<String> getPlanitemidsByOrderitemids(List<String> orderitemidLst, String tid);
}

