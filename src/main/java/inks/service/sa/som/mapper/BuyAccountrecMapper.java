package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyAccountrecEntity;
import inks.service.sa.som.domain.pojo.BuyAccountrecPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购结账(BuyAccountrec)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-28 20:24:37
 */
@Mapper
public interface BuyAccountrecMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyAccountrecPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyAccountrecPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param buyAccountrecEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyAccountrecEntity buyAccountrecEntity);


    /**
     * 修改数据
     *
     * @param buyAccountrecEntity 实例对象
     * @return 影响行数
     */
    int update(BuyAccountrecEntity buyAccountrecEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    BuyAccountrecPojo getEntityByMax(@Param("tid") String tid);

}

