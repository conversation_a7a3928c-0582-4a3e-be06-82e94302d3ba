package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyPlanmergeEntity;
import inks.service.sa.som.domain.pojo.BuyPlanmergePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合并项目(BuyPlanmerge)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-18 12:58:11
 */
@Mapper
public interface BuyPlanmergeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyPlanmergePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyPlanmergePojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyPlanmergePojo> getList(@Param("Pid") String Pid, @Param("tid") String tid);


    /**
     * 新增数据
     *
     * @param buyPlanmergeEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyPlanmergeEntity buyPlanmergeEntity);


    /**
     * 修改数据
     *
     * @param buyPlanmergeEntity 实例对象
     * @return 影响行数
     */
    int update(BuyPlanmergeEntity buyPlanmergeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    int deleteAllByPid(@Param("Pid") String key, @Param("tid") String tid);
}

