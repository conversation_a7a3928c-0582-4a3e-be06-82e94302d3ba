package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BusMachiningitemEntity;
import inks.service.sa.som.domain.pojo.BusMachiningitemPojo;
import inks.service.sa.som.domain.pojo.MatSpecorderitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 订单项目(BusMachiningitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-22 19:31:11
 */
@Mapper
public interface BusMachiningitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusMachiningitemPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusMachiningitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusMachiningitemPojo> getList(@Param("Pid") String Pid, @Param("tid") String tid);


    /**
     * 新增数据
     *
     * @param busMachiningitemEntity 实例对象
     * @return 影响行数
     */
    int insert(BusMachiningitemEntity busMachiningitemEntity);


    /**
     * 修改数据
     *
     * @param busMachiningitemEntity 实例对象
     * @return 影响行数
     */
    int update(BusMachiningitemEntity busMachiningitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);


//    int updateLaborCostAmt(String machitemid, Double laborcost, String tenantid);
    int updateLaborCostAmt(@Param("machitemid") String machitemid, @Param("laborcost") Double laborcost, @Param("tid") String tid);

    List<MatSpecorderitemPojo> getSpecOrderItemList(@Param("specid") String specid, @Param("tenantid") String tenantid);

    Map<String, Object> getSpecOrder(@Param("specid") String specid, @Param("tenantid") String tenantid);

    List<Map<String,String>> getAllByTid(String tid);

    int upateAttrStr(@Param("id") String id, @Param("attrStr") String attrStr, @Param("tid") String tid);

    String getGroupidByItemid(String itemid, String tid);
}

