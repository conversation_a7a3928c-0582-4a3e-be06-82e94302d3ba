package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.FmCashaccountEntity;
import inks.service.sa.som.domain.pojo.FmCashaccountPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 出纳账户(FmCashaccount)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 16:58:09
 */
@Mapper
public interface FmCashaccountMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCashaccountPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmCashaccountPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param fmCashaccountEntity 实例对象
     * @return 影响行数
     */
    int insert(FmCashaccountEntity fmCashaccountEntity);


    /**
     * 修改数据
     *
     * @param fmCashaccountEntity 实例对象
     * @return 影响行数
     */
    int update(FmCashaccountEntity fmCashaccountEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    // 查询往来单位是否被引用
    List<String>  getCiteBillName(@Param("key") String key,@Param("tid") String tid);


    List<String> getAccountIds(@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param tid 查询条件
     * @return 对象列表
     */
    List<FmCashaccountPojo> getList(@Param("tid") String tid);
}

