package inks.service.sa.som.mapper;

import inks.common.core.domain.MatBomdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface GoodsBomMapper {


    @Select("SELECT Bomid FROM Mat_Goods WHERE id = #{goodsid} AND Tenantid = #{tid}")
    String getBomid(String goodsid, String tid);


    @Select("SELECT Mat_Bom.id, Mat_Bom.Goodsid, Mat_Bom.ItemCode, Mat_Bom.ItemName, Mat_Bom.ItemSpec, Mat_Bom.ItemUnit, Mat_Bom.QuotaHour, Mat_Bom.ProGroupid, Mat_Bom.MinTime, Mat_Bom.Summary, Mat_Bom.EnabledMark, Mat_Bom.VersionNum, Mat_Bom.DeleteMark, Mat_Bom.DeleteListerid, Mat_Bom.DeleteLister, Mat_<PERSON><PERSON>.DeleteDate, Mat_<PERSON><PERSON>.<PERSON><PERSON><PERSON><PERSON>, Mat_<PERSON><PERSON>.<PERSON>reateByid, <PERSON>_<PERSON><PERSON>.<PERSON>, <PERSON>_<PERSON><PERSON>.Lister, <PERSON>_<PERSON><PERSON>.Listerid, Mat_Bo<PERSON>.ModifyDate, Mat_Bom.Assessor, Mat_Bom.Assessorid, Mat_Bom.AssessDate, Mat_Bom.ItemCount, Mat_Bom.PrintCount, Mat_Bom.Custom1, Mat_Bom.Custom2, Mat_Bom.Custom3, Mat_Bom.Custom4, Mat_Bom.Custom5, Mat_Bom.Custom6, Mat_Bom.Custom7, Mat_Bom.Custom8, Mat_Bom.Custom9, Mat_Bom.Custom10, Mat_Bom.Tenantid, Mat_Bom.TenantName, Mat_Bom.Revision," +
            " Mat_Goods.GoodsUid, Mat_Goods.GoodsName, Mat_Goods.GoodsSpec, Mat_Goods.GoodsUnit, Mat_Goods.Partid, Mat_Goods.Surface, Mat_Goods.Drawing, Mat_Goods.BrandName, Mat_Goods.Material as GoodsMaterial, Mat_Goods.Material as goodsmaterial " +
            "FROM Mat_Bom LEFT JOIN Mat_Goods ON Mat_Bom.Goodsid = Mat_Goods.id " +
            "where Mat_Bom.id = #{key} and Mat_Bom.Tenantid = #{tid}")
    MatBomdetailPojo getEntity(String key, String tid);


    @Select("SELECT Mat_BomItem.id, Mat_BomItem.Pid, Mat_BomItem.Goodsid, Mat_BomItem.ItemCode, Mat_BomItem.ItemName, Mat_BomItem.ItemSpec, Mat_BomItem.ItemUnit, Mat_BomItem.MainQty, Mat_BomItem.SubQty, Mat_BomItem.LossRate, Mat_BomItem.AttrCode, Mat_BomItem.FlowCode, Mat_BomItem.Description, Mat_BomItem.ItemLabel, Mat_BomItem.Parentid, Mat_BomItem.ParentGoodsid, Mat_BomItem.RowNum, Mat_BomItem.SubCount, Mat_BomItem.Remark, Mat_BomItem.SubLossQty, Mat_BomItem.Custom1, Mat_BomItem.Custom2, Mat_BomItem.Custom3, Mat_BomItem.Custom4, Mat_BomItem.Custom5, Mat_BomItem.Custom6, Mat_BomItem.Custom7, Mat_BomItem.Custom8, Mat_BomItem.Custom9, Mat_BomItem.Custom10, Mat_BomItem.Tenantid, Mat_BomItem.Revision, " +
            "Mat_Goods.GoodsUid, Mat_Goods.GoodsName, Mat_Goods.GoodsSpec, Mat_Goods.GoodsUnit, Mat_Goods.Partid, Mat_Goods.SafeStock, #{treeid} as TreeParentid, Mat_BomItem.id as BomItemid, Mat_Goods.Bomid as GoodsBomid " +
            "FROM Mat_BomItem LEFT JOIN Mat_Goods ON Mat_Goods.id = Mat_BomItem.Goodsid " +
            "where Mat_BomItem.Pid = #{pid} and Mat_BomItem.Tenantid = #{tid} and (Mat_BomItem.parentid is null or Mat_BomItem.parentid = '') " +
            "order by RowNum")
    List<MatBomdetailPojo> getDetailList(String pid, String treeid, String tid);
}
