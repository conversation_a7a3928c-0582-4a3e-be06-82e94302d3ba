package inks.service.sa.som.mapper;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.MatInventoryPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface D04MBIR1Mapper {
    /*
    库存品金额排行
     */
    List<MatInventoryPojo> getSumAmtByGoodsMax(QueryParam queryParam);
    /*
    库存汇总：金额、数量、记录数
     */
    List<ChartPojo> getSumAmtQtyByStore(QueryParam queryParam);


    List<Map<String,Object>> getSumQtyByMonth(QueryParam queryParam);

    List<Map<String,Object>> getSumOutQtyOneMonth(QueryParam queryParam);

    List<Map<String,Object>> getSumInQtyOneMonth(QueryParam queryParam);

    List<Map<String, Object>> getWorkshopProdValueByDay(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("workshopid") String workshopid, @Param("tenantid") String tenantid);

    List<Map<String,String>> getAllWorkshop(String tenantid);

    List<Map<String, Object>> getWorkshopProdValueByMonth(@Param("workshopid") String workshopid, @Param("tenantid") String tenantid);

    List<Map<String, Object>> getWorkshopProdValueIn12Month(Integer year, String tenantid);

    List<Map<String, Object>> getWorkshopReqFinishCostIn12Month(Integer year, String tenantid);

    List<Map<String, Object>> getWorkshopFmIn12Month(Integer year, String tenantid);
}
