package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyOrderEntity;
import inks.service.sa.som.domain.pojo.BuyOrderPojo;
import inks.service.sa.som.domain.pojo.BuyOrderitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 采购合同(BuyOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 14:59:22
 */
@Mapper
public interface BuyOrderMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyOrderPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyOrderitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyOrderPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyOrderEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyOrderEntity buyOrderEntity);


    /**
     * 修改数据
     *
     * @param buyOrderEntity 实例对象
     * @return 影响行数
     */
    int update(BuyOrderEntity buyOrderEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param buyOrderPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BuyOrderPojo buyOrderPojo);

    /**
     * 修改数据
     *
     * @param buyOrderEntity 实例对象
     * @return 影响行数
     */
    int approval(BuyOrderEntity buyOrderEntity);

    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDisannulCount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateFinishCount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updatePlanItemBuyQtyAndFinishQty(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updatePlanBuyCountAndFinishCount(@Param("key") String key, @Param("tid") String tid);


    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateMrpBuyOrderFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(@Param("key") String key, @Param("pid") String pid, @Param("tid") String tid);

    //刷新销售待出数
    int updateGoodsBusRemQty(@Param("key") String goodsid, @Param("tid") String tid);

    int updateMachingBuyQuantity(@Param("key") String key, @Param("tid") String tid);

    int updateMachingMatBuyQty(@Param("key") String key, @Param("tid") String tenantid);

    Map<String, Object> getWorkgroupInfo(@Param("groupid") String groupid, @Param("tid") String tid);


    void updateOaflowmark(BuyOrderPojo billPojo);
}