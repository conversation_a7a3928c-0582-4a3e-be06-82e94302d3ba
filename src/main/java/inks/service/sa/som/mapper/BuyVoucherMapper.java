package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyVoucherEntity;
import inks.service.sa.som.domain.pojo.BuyVoucherPojo;
import inks.service.sa.som.domain.pojo.BuyVoucheritemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购付款(BuyVoucher)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-15 15:02:31
 */
@Mapper
public interface BuyVoucherMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyVoucherPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyVoucheritemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyVoucherPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyVoucherEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyVoucherEntity buyVoucherEntity);


    /**
     * 修改数据
     *
     * @param buyVoucherEntity 实例对象
     * @return 影响行数
     */
    int update(BuyVoucherEntity buyVoucherEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param buyVoucherPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BuyVoucherPojo buyVoucherPojo);

    /**
     * 查询 被删除的Item
     *
     * @param buyVoucherPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelCashIds(BuyVoucherPojo buyVoucherPojo);

    /**
     * 修改数据
     *
     * @param buyVoucherEntity 实例对象
     * @return 影响行数
     */
    int approval(BuyVoucherEntity buyVoucherEntity);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateBuyInvoFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    int updateBuyInvoItemAvgAmt(@Param("key") String invobillid, @Param("refno") String invobillcode, @Param("tid") String tenantid);

    int updateBuyOrderItemAvgAmt(@Param("key") String invobillid, @Param("refno") String invobillcode, @Param("tid") String tenantid);

    int updateBuyOrderFirstLastAmt(@Param("key") String invobillid, @Param("refno") String invobillcode, @Param("tid") String tenantid);
    /**
     * 刷新收货单付款
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateFiniFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);
    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateCashAmount(@Param("key") String key, @Param("amount") Double amount, @Param("tid") String tid);


    int updateOrgReturn(@Param("orgUid") String orguid, @Param("redUid") String refno, @Param("tid") String tenantid);
}

