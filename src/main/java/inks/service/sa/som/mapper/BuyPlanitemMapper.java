package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyPlanitemEntity;
import inks.service.sa.som.domain.pojo.BuyPlanitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 采购计划项目(BuyPlanitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-06 20:32:57
 */
@Mapper
public interface BuyPlanitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyPlanitemPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyPlanitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyPlanitemPojo> getList(@Param("Pid") String Pid, @Param("tid") String tid);


    /**
     * 新增数据
     *
     * @param buyPlanitemEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyPlanitemEntity buyPlanitemEntity);


    /**
     * 修改数据
     *
     * @param buyPlanitemEntity 实例对象
     * @return 影响行数
     */
    int update(BuyPlanitemEntity buyPlanitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    List<BuyPlanitemPojo> getListByMergeid(@Param("mergeid") String key, @Param("tid") String tid);

    int updateMergeid(@Param("id") String id, @Param("mergeidInsert") String mergeidInsert, @Param("tid") String tid);

    int syncPriceFromOrderItem(@Param("planitemid") String planitemid, @Param("latestOrderItem") Map<String, Object> latestOrderItem, @Param("tid") String tid);

    int syncMergePriceFromOrderItem(@Param("mergeid") String mergeid, @Param("latestOrderItem") Map<String, Object> latestOrderItem, @Param("tid") String tid);

    Map<String, Object> getLatestOrderItemPrice(@Param("goodsid") String goodsid, @Param("groupid") String groupid, @Param("tid") String tid);

    Map<String, Object> getGoodsPrice(String goodsid, String tid);

    List<Map<String, Object>> getMachItemListInMachitemids(Set<String> machitemids, String tid);

    void syncMergeMarkInIds(List<String> mainPlanItemIds, int mergeMark, String tid);

    List<String> getMachItemidsInPlanItemids(List<String> mergeItemIds, String tid);
}


