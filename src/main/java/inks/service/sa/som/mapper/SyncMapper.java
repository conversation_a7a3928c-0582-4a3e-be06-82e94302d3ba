package inks.service.sa.som.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * SQL替代MQ:同步货品/客户
 * @date  最后更新时间：2024-05-21
 */
@Mapper
public interface SyncMapper {



    // ----------------------------------同步货品各项数量-----------------------------------
    //刷新销售待出数
    int updateGoodsBusRemQty(@Param("key")String goodsid, @Param("tid") String tid);
    //刷新收货待入数
    int updateGoodsBuyRemQty(@Param("key")String key, @Param("tid")String tid);
    //刷新生产待入数
    int updateGoodsWkWsRemQty(@Param("key")String key, @Param("tid")String tid);
    //刷新加工待入数
    int updateGoodsWkScRemQty(@Param("key")String key,  @Param("tid")String tid);
    //刷新领料待出数
    int updateGoodsRequRemQty(@Param("key")String key,  @Param("tid")String tid);
    //刷新当前库存数和单价
    int updateGoodsIvQuantity(@Param("key")String key, @Param("tid")String tid);


    // ----------------------------------同步客户各项余额-----------------------------------
    //刷新销售订单结余额
    int updateWorkgroupBusMachRemAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新销售发货结余额
    int updateWorkgroupBusDeliRemAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新销售发票结余额
    int updateWorkgroupBusInvoRemAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新销售结转期末额
    int updateWorkgroupBusAccoCloseAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新销售结转本期额
    int updateWorkgroupBusAccoNowAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新采购订单结余额
    int updateWorkgroupBuyOrderRemAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新采购收货结余额
    int updateWorkgroupBuyFiniRemAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新采购发票结余额
    int updateWorkgroupBuyInvoRemAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新采购结转期末额
    int updateWorkgroupBuyAccoCloseAmt(@Param("key")String key,@Param("tid")String tid);
    //刷新采购结转本期额
    int updateWorkgroupBuyAccoNowAmt(@Param("key")String key,@Param("tid")String tid);

}

