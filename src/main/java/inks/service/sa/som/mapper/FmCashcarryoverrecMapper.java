package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmCashcarryoverrecPojo;
import inks.service.sa.som.domain.FmCashcarryoverrecEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 出纳结账(FmCashcarryoverrec)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-09-26 19:41:47
 */
@Mapper
public interface FmCashcarryoverrecMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCashcarryoverrecPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmCashcarryoverrecPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param fmCashcarryoverrecEntity 实例对象
     * @return 影响行数
     */
    int insert(FmCashcarryoverrecEntity fmCashcarryoverrecEntity);

    
    /**
     * 修改数据
     *
     * @param fmCashcarryoverrecEntity 实例对象
     * @return 影响行数
     */
    int update(FmCashcarryoverrecEntity fmCashcarryoverrecEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    FmCashcarryoverrecPojo getEntityByMax(@Param("tid") String tid);

}

