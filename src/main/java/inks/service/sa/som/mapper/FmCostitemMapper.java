package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.FmCostitemEntity;
import inks.service.sa.som.domain.pojo.FmCostitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 费用明细(FmCostitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-08-29 15:25:30
 */
 @Mapper
public interface FmCostitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCostitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmCostitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<FmCostitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param fmCostitemEntity 实例对象
     * @return 影响行数
     */
    int insert(FmCostitemEntity fmCostitemEntity);

    
    /**
     * 修改数据
     *
     * @param fmCostitemEntity 实例对象
     * @return 影响行数
     */
    int update(FmCostitemEntity fmCostitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

