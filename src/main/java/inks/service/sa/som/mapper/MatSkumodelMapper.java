package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatSkumodelEntity;
import inks.service.sa.som.domain.pojo.MatSkumodelPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SKU模版(MatSkumodel)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-10 08:49:02
 */
@Mapper
public interface MatSkumodelMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSkumodelPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatSkumodelPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param matSkumodelEntity 实例对象
     * @return 影响行数
     */
    int insert(MatSkumodelEntity matSkumodelEntity);


    /**
     * 修改数据
     *
     * @param matSkumodelEntity 实例对象
     * @return 影响行数
     */
    int update(MatSkumodelEntity matSkumodelEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatSkumodelPojo getEntityByCode(@Param("key") String key, @Param("tid") String tid);

}

