package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatInvenoteitemEntity;
import inks.service.sa.som.domain.pojo.MatInvenoteitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 盘点项目(MatInvenoteitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-14 21:02:45
 */
 @Mapper
public interface MatInvenoteitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatInvenoteitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatInvenoteitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatInvenoteitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matInvenoteitemEntity 实例对象
     * @return 影响行数
     */
    int insert(MatInvenoteitemEntity matInvenoteitemEntity);

    
    /**
     * 修改数据
     *
     * @param matInvenoteitemEntity 实例对象
     * @return 影响行数
     */
    int update(MatInvenoteitemEntity matInvenoteitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatInvenoteitemPojo> getListByAlter(@Param("Pid") String Pid,@Param("tid") String tid);

    List<MatInvenoteitemPojo> getItemListByIds(String ids, String pid, String tid);
}

