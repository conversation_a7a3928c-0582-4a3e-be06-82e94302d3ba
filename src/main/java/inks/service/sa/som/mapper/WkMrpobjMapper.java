package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.WkMrpobjEntity;
import inks.service.sa.som.domain.pojo.WkMrpobjPojo;
import inks.service.sa.som.domain.pojo.WkMrpobjdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * MRP对象(WkMrpobj)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-23 10:00:36
 */
 @Mapper
public interface WkMrpobjMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMrpobjPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkMrpobjPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkMrpobjPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkMrpobjEntity 实例对象
     * @return 影响行数
     */
    int insert(WkMrpobjEntity wkMrpobjEntity);

    
    /**
     * 修改数据
     *
     * @param wkMrpobjEntity 实例对象
     * @return 影响行数
     */
    int update(WkMrpobjEntity wkMrpobjEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    String getMachbatch(@Param("mrpobjid") String mrpobjid, @Param("tid") String tid);

    List<WkMrpobjPojo> getListInids(Set<String> objids, String tid);

    WkMrpobjdetailPojo getEntityDetail(String key, String tid);

    List<String> getObjidsByMrpids(List<String> mrpids, String tid);}

