package inks.service.sa.som.mapper;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.BuyFinishingitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface D03M03R1Mapper {
    List<ChartPojo> getSumAmtByYear(QueryParam queryParam);
    List<ChartPojo> getSumAmtByMonth(QueryParam queryParam);
    List<ChartPojo> getSumAmtByDay(QueryParam queryParam);

    List<BuyFinishingitemdetailPojo> getSumPageListByGoods(QueryParam queryParam);

    List<BuyFinishingitemdetailPojo> getSumPageListByGroup(QueryParam queryParam);
}
