package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmDepotransferitemPojo;
import inks.service.sa.som.domain.FmDepotransferitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 预收核转Item(FmDepotransferitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-14 17:21:36
 */
 @Mapper
public interface FmDepotransferitemMapper {

    FmDepotransferitemPojo getEntity(@Param("key") String key);

    List<FmDepotransferitemPojo> getPageList(QueryParam queryParam);

    List<FmDepotransferitemPojo> getList(@Param("Pid") String Pid);    

    int insert(FmDepotransferitemEntity fmDepotransferitemEntity);

    int update(FmDepotransferitemEntity fmDepotransferitemEntity);

    int delete(@Param("key") String key);

}

