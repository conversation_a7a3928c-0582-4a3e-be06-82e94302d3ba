package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatAccessEntity;
import inks.service.sa.som.domain.pojo.MatAccessPojo;
import inks.service.sa.som.domain.pojo.MatAccessitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 出入库主表(MatAccess)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-12 14:31:15
 */
@Mapper
public interface MatAccessMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatAccessPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatAccessitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatAccessPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matAccessEntity 实例对象
     * @return 影响行数
     */
    int insert(MatAccessEntity matAccessEntity);


    /**
     * 修改数据
     *
     * @param matAccessEntity 实例对象
     * @return 影响行数
     */
    int update(MatAccessEntity matAccessEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param matAccessPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(MatAccessPojo matAccessPojo);

    //刷新领料单成本
    int updateRequMatCostAmt(@Param("key")String key, @Param("tid")String tid);

    //刷新销售订单成本
    int updateMachMatCostAmt(@Param("key")String key, @Param("tid")String tid);

    void updatePrintcount(MatAccessPojo billPrintPojo);

    List<Map<String, Object>> getSumPageListByGoods(QueryParam queryParam);

    Map<String, Object> getBuyFinishing(@Param("finishid") String finishid, @Param("tid") String tid);

    List<Map<String, Object>> getBuyFinishingItemList(@Param("finishid") String finishid, @Param("tid") String tid);

    Map<String, Object> getGoods(@Param("goodsid") String goodsid, @Param("tid") String tid);

    Map<String, Object> getRequisition(@Param("requisitionid") String requisitionid, @Param("tid") String tid);

    List<Map<String, Object>> getRequisitionItemList(@Param("requisitionid") String requisitionid, @Param("tid") String tid);
}

