package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatCombinEntity;
import inks.service.sa.som.domain.pojo.MatCombinPojo;
import inks.service.sa.som.domain.pojo.MatCombinitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 拆装单(MatCombin)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-19 11:09:23
 */
@Mapper
public interface MatCombinMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCombinPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatCombinitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatCombinPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param matCombinEntity 实例对象
     * @return 影响行数
     */
    int insert(MatCombinEntity matCombinEntity);

    
    /**
     * 修改数据
     *
     * @param matCombinEntity 实例对象
     * @return 影响行数
     */
    int update(MatCombinEntity matCombinEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param matCombinPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(MatCombinPojo matCombinPojo);
                                                                                                                                                                     }

