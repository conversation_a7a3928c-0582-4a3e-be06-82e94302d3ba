package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmDepotransferPojo;
import inks.service.sa.som.domain.pojo.FmDepotransferitemdetailPojo;
import inks.service.sa.som.domain.FmDepotransferEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 预收核转(FmDepotransfer)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-14 17:21:14
 */
@Mapper
public interface FmDepotransferMapper {

    FmDepotransferPojo getEntity(@Param("key") String key);

    List<FmDepotransferitemdetailPojo> getPageList(QueryParam queryParam);

    List<FmDepotransferPojo> getPageTh(QueryParam queryParam);

    int insert(FmDepotransferEntity fmDepotransferEntity);

    int update(FmDepotransferEntity fmDepotransferEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(FmDepotransferPojo fmDepotransferPojo);

    int approval(FmDepotransferEntity fmDepotransferEntity);

    List<String> getDelMachIds(FmDepotransferPojo fmDepotransferPojo);
}

