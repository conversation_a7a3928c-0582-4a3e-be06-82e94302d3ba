package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatAttributeEntity;
import inks.service.sa.som.domain.pojo.MatAttributePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SPU属性表(MatAttribute)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-14 14:45:49
 */
@Mapper
public interface MatAttributeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatAttributePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatAttributePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param matAttributeEntity 实例对象
     * @return 影响行数
     */
    int insert(MatAttributeEntity matAttributeEntity);


    /**
     * 修改数据
     *
     * @param matAttributeEntity 实例对象
     * @return 影响行数
     */
    int update(MatAttributeEntity matAttributeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 显示列
     *
     * @return 查询结果
     */
    List<MatAttributePojo> getListByShow(@Param("usedomain") String usedomain, @Param("tid") String tid);


}

