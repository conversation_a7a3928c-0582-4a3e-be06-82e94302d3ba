package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmCashcarryoverPojo;
import inks.service.sa.som.domain.pojo.FmCashcarryoveritemPojo;
import inks.service.sa.som.domain.pojo.FmCashcarryoveritemdetailPojo;
import inks.service.sa.som.domain.FmCashcarryoverEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 出纳账单(FmCashcarryover)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-09-26 19:41:23
 */
@Mapper
public interface FmCashcarryoverMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCashcarryoverPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmCashcarryoveritemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmCashcarryoverPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param fmCashcarryoverEntity 实例对象
     * @return 影响行数
     */
    int insert(FmCashcarryoverEntity fmCashcarryoverEntity);

    
    /**
     * 修改数据
     *
     * @param fmCashcarryoverEntity 实例对象
     * @return 影响行数
     */
    int update(FmCashcarryoverEntity fmCashcarryoverEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param fmCashcarryoverPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(FmCashcarryoverPojo fmCashcarryoverPojo);

    /**
     * 现金银行明细
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
     List<FmCashcarryoveritemPojo> getBillItemList(QueryParam queryParam);

    FmCashcarryoverPojo getMaxEntityByCash(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmCashcarryoveritemPojo> pullItemList(QueryParam queryParam);


    /**
     * 查询 所有Item
     *
     * @return 查询结果
     */
    List<FmCashcarryoveritemPojo> getMultItemList(QueryParam queryParam);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmCashcarryoverPojo> getNowPageList(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteByMonth(@Param("year") Integer year, @Param("nonth") Integer nonth,@Param("tid") String tid);

}

