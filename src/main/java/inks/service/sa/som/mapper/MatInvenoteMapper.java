package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatInvenoteEntity;
import inks.service.sa.som.domain.pojo.MatInvenotePojo;
import inks.service.sa.som.domain.pojo.MatInvenoteitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 仓库盘点(MatInvenote)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-14 19:24:20
 */
@Mapper
public interface MatInvenoteMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatInvenotePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatInvenoteitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatInvenotePojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param matInvenoteEntity 实例对象
     * @return 影响行数
     */
    int insert(MatInvenoteEntity matInvenoteEntity);

    
    /**
     * 修改数据
     *
     * @param matInvenoteEntity 实例对象
     * @return 影响行数
     */
    int update(MatInvenoteEntity matInvenoteEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param matInvenotePojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(MatInvenotePojo matInvenotePojo);
                                                                                                   /**
     * 修改数据
     *
     * @param matInvenoteEntity 实例对象
     * @return 影响行数
     */
    int approval(MatInvenoteEntity matInvenoteEntity);
                                                                                                     }

