package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BusAccountarapEntity;
import inks.service.sa.som.domain.pojo.BusAccountarapPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发票to收款(BusAccountarap)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-16 11:31:29
 */
 @Mapper
public interface BusAccountarapMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountarapPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusAccountarapPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusAccountarapPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param busAccountarapEntity 实例对象
     * @return 影响行数
     */
    int insert(BusAccountarapEntity busAccountarapEntity);

    
    /**
     * 修改数据
     *
     * @param busAccountarapEntity 实例对象
     * @return 影响行数
     */
    int update(BusAccountarapEntity busAccountarapEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

