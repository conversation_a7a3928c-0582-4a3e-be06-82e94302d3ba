package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatTransferEntity;
import inks.service.sa.som.domain.pojo.MatTransferPojo;
import inks.service.sa.som.domain.pojo.MatTransferitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调拨单据(MatTransfer)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-13 20:50:26
 */
@Mapper
public interface MatTransferMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatTransferPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatTransferitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatTransferPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param matTransferEntity 实例对象
     * @return 影响行数
     */
    int insert(MatTransferEntity matTransferEntity);

    
    /**
     * 修改数据
     *
     * @param matTransferEntity 实例对象
     * @return 影响行数
     */
    int update(MatTransferEntity matTransferEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param matTransferPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(MatTransferPojo matTransferPojo);
                                                                                                                                                           }

