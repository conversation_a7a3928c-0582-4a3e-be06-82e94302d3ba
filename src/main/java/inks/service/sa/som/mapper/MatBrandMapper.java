package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatBrandEntity;
import inks.service.sa.som.domain.pojo.MatBrandPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 品牌列表(MatBrand)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-22 13:32:33
 */
@Mapper
public interface MatBrandMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatBrandPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatBrandPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param matBrandEntity 实例对象
     * @return 影响行数
     */
    int insert(MatBrandEntity matBrandEntity);

    
    /**
     * 修改数据
     *
     * @param matBrandEntity 实例对象
     * @return 影响行数
     */
    int update(MatBrandEntity matBrandEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                                                                  }

