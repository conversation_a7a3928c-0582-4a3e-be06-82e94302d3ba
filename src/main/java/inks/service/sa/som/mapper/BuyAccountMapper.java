package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyAccountEntity;
import inks.service.sa.som.domain.pojo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应付账单(BuyAccount)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-09 08:28:58
 */
@Mapper
public interface BuyAccountMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyAccountPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyAccountitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyAccountPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyAccountEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyAccountEntity buyAccountEntity);


    /**
     * 修改数据
     *
     * @param buyAccountEntity 实例对象
     * @return 影响行数
     */
    int update(BuyAccountEntity buyAccountEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param buyAccountPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BuyAccountPojo buyAccountPojo);

    List<String> getDelInvoIds(BuyAccountPojo buyAccountPojo);

    List<String> getDelArapIds(BuyAccountPojo buyAccountPojo);


    /**
     * 查询 被删除的Item
     *
     * @param tid 筛选条件
     * @return 查询结果
     */
    List<String> getSupplierIds(String tid);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteByMonth(@Param("year") Integer year, @Param("nonth") Integer nonth,@Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyAccountPojo getMaxEntityByGroup(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 所有Item
     *
     * @return 查询结果
     */
    List<BuyAccountitemPojo> getMultItemList(QueryParam queryParam);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyAccountPojo> getNowPageList(@Param("queryParam")QueryParam queryParam, @Param("online")Integer online);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyAccountitemPojo> pullItemList(QueryParam queryParam);
    List<BuyAccountinvoPojo> pullInvoList(QueryParam queryParam);


    List<BuyAccountarapPojo> pullArapList(QueryParam queryParam);

    List<String> getDeleteIds();

    AppWgSupplierPojo getWorkGroupEntity(@Param("key") String key, @Param("tid") String tid);
}

