package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmExpensesPojo;
import inks.service.sa.som.domain.pojo.FmExpensesitemdetailPojo;
import inks.service.sa.som.domain.FmExpensesEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 费用报销单(FmExpenses)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-26 15:43:17
 */
@Mapper
public interface FmExpensesMapper {

    FmExpensesPojo getEntity(@Param("key") String key);

    List<FmExpensesitemdetailPojo> getPageList(QueryParam queryParam);

    List<FmExpensesPojo> getPageTh(QueryParam queryParam);

    int insert(FmExpensesEntity fmExpensesEntity);

    int update(FmExpensesEntity fmExpensesEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(FmExpensesPojo fmExpensesPojo);

    int approval(FmExpensesEntity fmExpensesEntity);
}

