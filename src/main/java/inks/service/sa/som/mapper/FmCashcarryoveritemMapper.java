package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.pojo.FmCashcarryoveritemPojo;
import inks.service.sa.som.domain.FmCashcarryoveritemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 对账明细(FmCashcarryoveritem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-09-26 19:41:35
 */
 @Mapper
public interface FmCashcarryoveritemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCashcarryoveritemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmCashcarryoveritemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<FmCashcarryoveritemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param fmCashcarryoveritemEntity 实例对象
     * @return 影响行数
     */
    int insert(FmCashcarryoveritemEntity fmCashcarryoveritemEntity);

    
    /**
     * 修改数据
     *
     * @param fmCashcarryoveritemEntity 实例对象
     * @return 影响行数
     */
    int update(FmCashcarryoveritemEntity fmCashcarryoveritemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

