package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyPlanEntity;
import inks.service.sa.som.domain.pojo.BuyPlanPojo;
import inks.service.sa.som.domain.pojo.BuyPlanitemdetailPojo;
import inks.service.sa.som.domain.pojo.WkMrpitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购计划(BuyPlan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-06 20:32:44
 */
@Mapper
public interface BuyPlanMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyPlanPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyPlanitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyPlanPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param buyPlanEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyPlanEntity buyPlanEntity);


    /**
     * 修改数据
     *
     * @param buyPlanEntity 实例对象
     * @return 影响行数
     */
    int update(BuyPlanEntity buyPlanEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param buyPlanPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(BuyPlanPojo buyPlanPojo);

    /**
     * 修改数据
     *
     * @param buyPlanEntity 实例对象
     * @return 影响行数
     */
    int approval(BuyPlanEntity buyPlanEntity);

    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDisannulCount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateBuyFinishCount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateMrpBuyPlanFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * 修改完工记数
     *
     * @param key Item的id
     * @return 影响行数
     */
    int updateMrpFinishCount(@Param("key") String key, @Param("tid") String tid);

    // 查询Item是否被引用
    List<String> getItemCiteBillName(@Param("key") String key, @Param("pid") String pid, @Param("tid") String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMrpitemPojo getMrpItemEntity(@Param("key") String key, @Param("tid") String tid);


    void updatePrintcount(BuyPlanPojo billPrintPojo);

    List<WkMrpitemPojo> getMrpItemListByMrpUid(@Param("mrpUid") String mrpUid, @Param("tid") String tid);

    int updateMergeCount(@Param("id") String id, @Param("size") int size, @Param("tid") String tid);

    int updateItemCount(@Param("planid") String planid, @Param("tid") String tid);

    List<BuyPlanitemdetailPojo> getMainPlanItem(List<String> buyPlanItemIds, String tid);

    void syncMachingItemWkMergeInIds(List<String> salveMachItemids, int wkMergeMark, String insertPlanItemid, String tid);

    void updateMrpBuyFinishCount(@Param("key")String mrpitemid, @Param("tid")String tid);

    void updateOaflowmark(BuyPlanPojo billPojo);

    void syncBillAmount(String key, String tid);
}

