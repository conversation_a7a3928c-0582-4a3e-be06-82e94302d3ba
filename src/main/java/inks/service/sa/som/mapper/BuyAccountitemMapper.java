package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyAccountitemEntity;
import inks.service.sa.som.domain.pojo.BuyAccountitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 账单明细(BuyAccountitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-09 08:29:10
 */
 @Mapper
public interface BuyAccountitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyAccountitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyAccountitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyAccountitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param buyAccountitemEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyAccountitemEntity buyAccountitemEntity);

    
    /**
     * 修改数据
     *
     * @param buyAccountitemEntity 实例对象
     * @return 影响行数
     */
    int update(BuyAccountitemEntity buyAccountitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int deleteByPid(@Param("deleteIds")List<String> deleteIds, @Param("tid")String tid);
}

