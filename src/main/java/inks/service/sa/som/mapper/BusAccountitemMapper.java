package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BusAccountitemEntity;
import inks.service.sa.som.domain.pojo.BusAccountitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 账单明细(BusAccountitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-03 14:28:20
 */
 @Mapper
public interface BusAccountitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusAccountitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusAccountitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BusAccountitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param busAccountitemEntity 实例对象
     * @return 影响行数
     */
    int insert(BusAccountitemEntity busAccountitemEntity);

    
    /**
     * 修改数据
     *
     * @param busAccountitemEntity 实例对象
     * @return 影响行数
     */
    int update(BusAccountitemEntity busAccountitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

