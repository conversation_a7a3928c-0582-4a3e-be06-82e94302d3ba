package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatStorageEntity;
import inks.service.sa.som.domain.pojo.MatStoragePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 仓库管理(MatStorage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-30 13:33:59
 */
@Mapper
public interface MatStorageMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatStoragePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatStoragePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param matStorageEntity 实例对象
     * @return 影响行数
     */
    int insert(MatStorageEntity matStorageEntity);


    /**
     * 修改数据
     *
     * @param matStorageEntity 实例对象
     * @return 影响行数
     */
    int update(MatStorageEntity matStorageEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    List<MatStoragePojo> getMachList(@Param("tid") String tid);

    // 查询仓库是否被引用
    List<String> getItemCiteBillName(@Param("key") String key, @Param("tid") String tid);

    // 检查是否存在相同的仓库编码或仓库名称
    int checkCodeOrName(@Param("storecode") String storecode, @Param("storename") String storename, @Param("tid") String tid);
}

