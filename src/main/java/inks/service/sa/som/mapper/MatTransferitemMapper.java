package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatTransferitemEntity;
import inks.service.sa.som.domain.pojo.MatTransferitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调拨项目(MatTransferitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-15 14:23:15
 */
 @Mapper
public interface MatTransferitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatTransferitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatTransferitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatTransferitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matTransferitemEntity 实例对象
     * @return 影响行数
     */
    int insert(MatTransferitemEntity matTransferitemEntity);

    
    /**
     * 修改数据
     *
     * @param matTransferitemEntity 实例对象
     * @return 影响行数
     */
    int update(MatTransferitemEntity matTransferitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

