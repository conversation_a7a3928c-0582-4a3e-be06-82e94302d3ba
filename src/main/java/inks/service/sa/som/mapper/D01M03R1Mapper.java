package inks.service.sa.som.mapper;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 销售订单(BusMachining)报表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-13 10:19:46
 */
@Mapper
public interface D01M03R1Mapper {
     /*
      *
      * <AUTHOR>
      * @description 客户订单金额排名
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByGroupMax(QueryParam queryParam);
     /*
      *
      * <AUTHOR>
      * @description 订单逾期
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    ChartPojo getPageList(String tid);
     /*
      *
      * <AUTHOR>
      * @description 热销产品
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtByGoodsMax(QueryParam queryParam);
     /*
      *
      * <AUTHOR>
      * @description 业务员订单金额占比
      * @date 2021/12/30
      * @param * @param null
      * @return
      */
    List<ChartPojo> getSumAmtBySalesman(QueryParam queryParam);
}


