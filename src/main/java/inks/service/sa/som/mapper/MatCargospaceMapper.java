package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatCargospaceEntity;
import inks.service.sa.som.domain.pojo.MatCargospacePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 仓库货位(MatCargospace)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-13 20:43:52
 */
@Mapper
public interface MatCargospaceMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCargospacePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatCargospacePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param matCargospaceEntity 实例对象
     * @return 影响行数
     */
    int insert(MatCargospaceEntity matCargospaceEntity);

    
    /**
     * 修改数据
     *
     * @param matCargospaceEntity 实例对象
     * @return 影响行数
     */
    int update(MatCargospaceEntity matCargospaceEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int checkCodeOrName( @Param("spacecode")String spacecode,  @Param("spacename")String spacename,  @Param("tid")String tenantid);
}

