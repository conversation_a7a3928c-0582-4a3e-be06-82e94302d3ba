package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.FmPayapplyEntity;
import inks.service.sa.som.domain.pojo.FmPayapplyPojo;
import inks.service.sa.som.domain.pojo.FmPayapplyitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 往来核销(FmPayapply)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-13 21:30:00
 */
@Mapper
public interface FmPayapplyMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmPayapplyPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmPayapplyitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmPayapplyPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param fmPayapplyEntity 实例对象
     * @return 影响行数
     */
    int insert(FmPayapplyEntity fmPayapplyEntity);


    /**
     * 修改数据
     *
     * @param fmPayapplyEntity 实例对象
     * @return 影响行数
     */
    int update(FmPayapplyEntity fmPayapplyEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param fmPayapplyPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(FmPayapplyPojo fmPayapplyPojo);

    /**
     * 查询 被删除的Item
     *
     * @param fmPayapplyPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelCashIds(FmPayapplyPojo fmPayapplyPojo);

    /**
     * 刷新预付款完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateSaleDepoFinish(@Param("key") String key, @Param("tid") String tid);

    /**
     * get出纳账户余额
     *
     * @param key 实例对象
     * @return 影响行数
     */
    Double getSaleDepoAmount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新发票完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateBusInvoFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    int updateBusInvoItemAvgAmt(@Param("key") String invobillid, @Param("refno") String invobillcode, @Param("tid") String tenantid);

    int updateBusMachingItemAvgAmt(@Param("key") String invobillid, @Param("refno") String invobillcode, @Param("tid") String tenantid);
    int updateBusMachingFirstLastAmt(@Param("key") String invobillid, @Param("refno") String invobillcode, @Param("tid") String tenantid);


    /**
     * get出纳账户余额
     *
     * @param key 实例对象
     * @return 影响行数
     */
    Double getSaleInvoAmount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新预收款完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateBuyPrepFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * get出纳账户余额
     *
     * @param key 实例对象
     * @return 影响行数
     */
    Double getBuyPrepAmount(@Param("key") String key, @Param("tid") String tid);
    /**
     * 刷新发票完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateBuyInvoFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * get出纳账户余额
     *
     * @param key 实例对象
     * @return 影响行数
     */
    Double getBuyInvoAmount(@Param("key") String key, @Param("tid") String tid);

}

