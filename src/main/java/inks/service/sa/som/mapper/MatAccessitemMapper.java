package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatAccessitemEntity;
import inks.service.sa.som.domain.pojo.MatAccessitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 出入库项目(MatAccessitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-12 14:38:15
 */
 @Mapper
public interface MatAccessitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatAccessitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatAccessitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatAccessitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matAccessitemEntity 实例对象
     * @return 影响行数
     */
    int insert(MatAccessitemEntity matAccessitemEntity);

    
    /**
     * 修改数据
     *
     * @param matAccessitemEntity 实例对象
     * @return 影响行数
     */
    int update(MatAccessitemEntity matAccessitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

