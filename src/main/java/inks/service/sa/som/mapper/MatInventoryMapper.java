package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatInventoryEntity;
import inks.service.sa.som.domain.pojo.MatGoodsInveQtyPojo;
import inks.service.sa.som.domain.pojo.MatGoodsPojo;
import inks.service.sa.som.domain.pojo.MatInventoryPojo;
import inks.service.sa.som.domain.pojo.MatInventoryQtyPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 库存信息(MatInventory)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-12 14:23:43
 */
@Mapper
public interface MatInventoryMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatInventoryPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatInventoryPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param matInventoryEntity 实例对象
     * @return 影响行数
     */
    int insert(MatInventoryEntity matInventoryEntity);


    /**
     * 修改数据
     *
     * @param matInventoryEntity 实例对象
     * @return 影响行数
     */
    int update(MatInventoryEntity matInventoryEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新货品总仓库数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateGoodsIvQty(@Param("key") String key, @Param("tid") String tid);


    //获取EntityBySN
    MatInventoryPojo getEntityBySn(MatInventoryPojo matInventorypojo);

    MatInventoryPojo getEntityBySnNoSku(MatInventoryPojo matInventoryPojo);
    //获取EntityBySN
    MatInventoryPojo getEntityBySku(MatInventoryPojo matInventorypojo);

    //获取EntityBy批号
    MatInventoryPojo getEntityByBatch(MatInventoryPojo matInventorypojo);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatInventoryQtyPojo> getQtyPageList(QueryParam queryParam);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatGoodsInveQtyPojo> getQtyPageListByGoods(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<MatInventoryPojo> getListByGoods(@Param("key") String key, @Param("tid") String tid);


    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<MatInventoryPojo> getOnlineListByStore(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatGoodsInveQtyPojo> getMachMatQtyPageListByGoods(@Param("queryParam") QueryParam queryParam, @Param("itemid") String itemid);

    MatInventoryPojo getEntityByNameSpecPart(@Param("name")String name,@Param("goodsspec") String goodsspec,@Param("partid")String partid,@Param("tid")String tid);

    MatInventoryPojo getEntityByStoreGoods(@Param("goodsid")String key, @Param("storeid")String storeid, @Param("tid")String tid);

    List<MatGoodsPojo> getGoodsPageList(QueryParam queryParam);

    Double getRequremqtyByGoodsUid(@Param("goodsuid")String goodsuid, @Param("tid")String tenantid);

    Double getRequremqtyByGoodsid(@Param("goodsid")String goodsuid, @Param("tid") String tenantid);


    List<Map<String, Object>> getSumPageListByGoods(QueryParam queryParam);

    MatInventoryPojo getEntityByIf(MatInventoryPojo matInventoryPojo);

}

