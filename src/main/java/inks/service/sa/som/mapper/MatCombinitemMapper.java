package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatCombinitemEntity;
import inks.service.sa.som.domain.pojo.MatCombinitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组合项目(MatCombinitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-19 11:09:44
 */
 @Mapper
public interface MatCombinitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCombinitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatCombinitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatCombinitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matCombinitemEntity 实例对象
     * @return 影响行数
     */
    int insert(MatCombinitemEntity matCombinitemEntity);

    
    /**
     * 修改数据
     *
     * @param matCombinitemEntity 实例对象
     * @return 影响行数
     */
    int update(MatCombinitemEntity matCombinitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

