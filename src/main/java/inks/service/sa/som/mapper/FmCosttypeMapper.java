package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.FmCosttypeEntity;
import inks.service.sa.som.domain.pojo.FmCosttypePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 费用科目(FmCosttype)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-08-29 15:21:43
 */
@Mapper
public interface FmCosttypeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmCosttypePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmCosttypePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param fmCosttypeEntity 实例对象
     * @return 影响行数
     */
    int insert(FmCosttypeEntity fmCosttypeEntity);

    
    /**
     * 修改数据
     *
     * @param fmCosttypeEntity 实例对象
     * @return 影响行数
     */
    int update(FmCosttypeEntity fmCosttypeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<String> getCiteBillName(@Param("key") String key, @Param("tid") String tid);
}

