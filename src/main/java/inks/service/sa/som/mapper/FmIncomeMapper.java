package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.FmIncomeEntity;
import inks.service.sa.som.domain.pojo.FmIncomePojo;
import inks.service.sa.som.domain.pojo.FmIncomeitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 其他收入(FmIncome)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-08-29 15:52:28
 */
@Mapper
public interface FmIncomeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmIncomePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmIncomeitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmIncomePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param fmIncomeEntity 实例对象
     * @return 影响行数
     */
    int insert(FmIncomeEntity fmIncomeEntity);


    /**
     * 修改数据
     *
     * @param fmIncomeEntity 实例对象
     * @return 影响行数
     */
    int update(FmIncomeEntity fmIncomeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param fmIncomePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(FmIncomePojo fmIncomePojo);

    /**
     * 刷新出纳账户额
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateCashAmount(@Param("key") String key, @Param("amount") Double amount, @Param("tid") String tid);

}

