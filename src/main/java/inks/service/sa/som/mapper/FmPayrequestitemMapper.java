package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.FmPayrequestitemEntity;
import inks.service.sa.som.domain.pojo.FmPayrequestitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 付款申请单(FmPayrequestitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-07 13:31:44
 */
 @Mapper
public interface FmPayrequestitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    FmPayrequestitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<FmPayrequestitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<FmPayrequestitemPojo> getList(@Param("Pid") String Pid);    
     
    
    /**
     * 新增数据
     *
     * @param fmPayrequestitemEntity 实例对象
     * @return 影响行数
     */
    int insert(FmPayrequestitemEntity fmPayrequestitemEntity);

    
    /**
     * 修改数据
     *
     * @param fmPayrequestitemEntity 实例对象
     * @return 影响行数
     */
    int update(FmPayrequestitemEntity fmPayrequestitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

