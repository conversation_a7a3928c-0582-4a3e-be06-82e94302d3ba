package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatGoodsEntity;
import inks.service.sa.som.domain.pojo.MatGoodsBatchAttrPojo;
import inks.service.sa.som.domain.pojo.MatGoodsPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 货品信息(MatGoods)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-14 08:36:14
 */
@Mapper
public interface MatGoodsMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatGoodsPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatGoodsPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param matGoodsEntity 实例对象
     * @return 影响行数
     */
    int insert(MatGoodsEntity matGoodsEntity);


    /**
     * 修改数据
     *
     * @param matGoodsEntity 实例对象
     * @return 影响行数
     */
    int update(MatGoodsEntity matGoodsEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    //2021-12-13 倒叙获取最近的一个单据编码   song
    //根据料号获取货品信息
    MatGoodsPojo getEntityByGoodsUid(@Param("goodsuid") String goodsuid, @Param("tid") String tid);

    //按货品名称查询货品信息
    MatGoodsPojo getEntityByName(@Param("name") String name, @Param("tid") String tid);

    //按货品名称\规格查询货品信息
    MatGoodsPojo getEntityByNameSpec(@Param("name") String name, @Param("goodsspec") String goodsspec, @Param("tid") String tid);

    //按货品名称\规格\外部编码查询货品信息
    MatGoodsPojo getEntityByNameSpecPart(@Param("name") String name, @Param("goodsspec") String goodsspec, @Param("partid") String partid, @Param("tid") String tid);

    MatGoodsPojo getEntityByNameSpecPartBrandNameSurface(MatGoodsPojo matGoodsPojo);

    //获取最大货品编码
    MatGoodsPojo getEntityByGroup(@Param("groupid") String groupid, @Param("tid") String tid);

    // 查询货品是否被引用
    List<String> getCiteBillName(@Param("key") String key, @Param("tid") String tid);

    //按外部编码获取货品信息
    MatGoodsPojo getEntityByPartid(@Param("key") String key, @Param("tid") String tid);

    //按快速码获取货品信息
    MatGoodsPojo getEntityByQuickCode(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改数据
     *
     * @param tid 实例对象
     * @return 影响行数
     */
    int updateIvQty(@Param("tid") String tid);

    //刷新销售待出数

    int updateGoodsBusRemQty(@Param("key") String goodsid, @Param("tid") String tid);

    //刷新收货待入数

    int updateGoodsBuyRemQty(@Param("key") String key, @Param("tid") String tid);
    //刷新生产待入数

    int updateGoodsWkWsRemQty(@Param("key") String key, @Param("tid") String tid);
    //刷新加工待入数

    int updateGoodsWkScRemQty(@Param("key") String key, @Param("tid") String tid);
    //刷新领料待出数

    int updateGoodsRequRemQty(@Param("key") String key, @Param("tid") String tid);
    //刷新当前库存数和单价

    int updateGoodsIvQuantity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param matGoodsPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelUnitIds(MatGoodsPojo matGoodsPojo);

    Integer checkGoodsUid(@Param("id") String id, @Param("goodsuid") String goodsuid, @Param("tid") String tid);

    int updateGoodsSpecid(@Param("id") String id, @Param("goodsid") String goodsid, @Param("tid") String tid);

    String getGoodsstate(@Param("goodsid") String goodsid, @Param("tenantid") String tenantid);


    List<Map<String, Object>> getSupplierByGoods(@Param("goodsidList") List<String> goodsidList);

    List<Map<String, Object>> getSupplierByLastOrders(@Param("goodsidList") List<String> goodsidList);

    List<MatGoodsPojo> getListByBomids(List<String> bomids, String tid);

    MatGoodsBatchAttrPojo getGoodsBatchAttr(String goodsid, String tenantid);
}

