package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatRequisitionEntity;
import inks.service.sa.som.domain.pojo.MatRequisitionPojo;
import inks.service.sa.som.domain.pojo.MatRequisitionitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 物料申领(MatRequisition)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-25 13:06:03
 */
@Mapper
public interface MatRequisitionMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatRequisitionPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatRequisitionitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatRequisitionPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param matRequisitionEntity 实例对象
     * @return 影响行数
     */
    int insert(MatRequisitionEntity matRequisitionEntity);


    /**
     * 修改数据
     *
     * @param matRequisitionEntity 实例对象
     * @return 影响行数
     */
    int update(MatRequisitionEntity matRequisitionEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param matRequisitionPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(MatRequisitionPojo matRequisitionPojo);

    /**
     * 修改数据
     *
     * @param matRequisitionEntity 实例对象
     * @return 影响行数
     */
    int approval(MatRequisitionEntity matRequisitionEntity);


    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDisannulCount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateFinishCount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateMachMatUsed(@Param("key") String key, @Param("tid") String tid);


    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateUnMachMatUsed(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateWsMatUsed(@Param("key") String key, @Param("tid") String tid);


    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateUnWsMatUsed(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateWipMatUsed(@Param("key") String key, @Param("tid") String tid);


    /**
     * 修改数据
     *
     * @return 影响行数
     */
    int updateUnWipMatUsed(@Param("key") String key, @Param("tid") String tid);


    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Map<String, Object> getWipByWorkUid(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Map<String, Object> getMachItem(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Map<String, Object> getWsItem(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    Map<String, Object> getGoodsEntityByGoodsUid(@Param("key") String key, @Param("tid") String tid);

    /*
     *
     * <AUTHOR>
     * @description 客户订单金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<MatRequisitionitemdetailPojo> getSumPageListByGroup(QueryParam queryParam);

    /*
     *
     * <AUTHOR>
     * @description 货品金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<MatRequisitionitemdetailPojo> getSumPageListByGoods(QueryParam queryParam);


    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateWkWsMatFinishCount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改作废记数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateWkScMatFinishCount(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateGoodsRequRemQty(@Param("key") String key, @Param("goodsuid") String goodsuid, @Param("tid") String tid);

    /*
     *
     * <AUTHOR>
     * @description 货品金额排名
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    List<MatRequisitionitemdetailPojo> getMachPageList(QueryParam queryParam);

    // 查询Item是否被引用
    Integer getItemCiteBillName(@Param("key") String key, @Param("pid") String pid, @Param("tid") String tid);

//    同步更新合并的所有销售订单子项->物料已领
    int updateMergeMachMatUsed(@Param("wipid")String wipid, @Param("tid")String tenantid);

    void updatePrintcount(MatRequisitionPojo billPrintPojo);

    void updateMachMatUseQty(@Param("key") String machitemid, @Param("tid") String tenantid);

    Map<String, Object> getSheetMatInfo(@Param("workitemmatid") String workitemmatid, @Param("tenantid") String tenantid);

    Map<String, Object> getSubMatInfo(@Param("workitemmatid") String workitemmatid, @Param("tenantid") String tenantid);

    void updateMrpMatReqQty(String mrpitemid, String tid);
    void updateMrpFinishCount(String mrpitemid, String tid);

    // 查询Item是否被引用，只查被红冲单引用的
}

