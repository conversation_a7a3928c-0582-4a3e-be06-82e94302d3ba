package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatRequisitionitemEntity;
import inks.service.sa.som.domain.pojo.MatRequisitionitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 申领项目(MatRequisitionitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-14 14:10:31
 */
 @Mapper
public interface MatRequisitionitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatRequisitionitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatRequisitionitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatRequisitionitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matRequisitionitemEntity 实例对象
     * @return 影响行数
     */
    int insert(MatRequisitionitemEntity matRequisitionitemEntity);

    
    /**
     * 修改数据
     *
     * @param matRequisitionitemEntity 实例对象
     * @return 影响行数
     */
    int update(MatRequisitionitemEntity matRequisitionitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);


    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<MatRequisitionitemPojo> getListByWorkitemid(@Param("key") String key,@Param("tid") String tid);

    List<Map<String, Object>> getMachListInMachitemids(@Param("machitemids") Set<String> machitemids, @Param("tid") String tid);
}

