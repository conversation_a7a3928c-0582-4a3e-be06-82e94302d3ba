package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.WkMrpitemEntity;
import inks.service.sa.som.domain.pojo.WkMrpitemPojo;
import inks.service.sa.som.domain.vo.WkMrpitemAndPatentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * MRP项目(WkMrpitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-22 18:55:27
 */
 @Mapper
public interface WkMrpitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    WkMrpitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<WkMrpitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<WkMrpitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param wkMrpitemEntity 实例对象
     * @return 影响行数
     */
    int insert(WkMrpitemEntity wkMrpitemEntity);

    
    /**
     * 修改数据
     *
     * @param wkMrpitemEntity 实例对象
     * @return 影响行数
     */
    int update(WkMrpitemEntity wkMrpitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);


    int setBomMarkByPid(@Param("bomMark") int i, @Param("pid") String key, @Param("tid") String tid);

    List<WkMrpitemPojo> getListByMrpObjId(@Param("mrpobjid") String id, @Param("tid") String tid);

    List<WkMrpitemAndPatentVO> getListByMrpObjIdNoneBomid(@Param("mrpobjids") List<String> mrpobjids, @Param("tid") String tid);

    int updateItemAttrCode(@Param("itemids") List<String> itemids, @Param("attrcode") String attrcode, @Param("tid") String tenantid);

    String getPid(String id);

    String getAttrCode(@Param("itemid") String s, @Param("tid") String tenantid);

    List<String> checkWk_WorkSheetItem(@Param("itemids") List<String> itemids, @Param("tid") String tenantid);

    int checkLastMrpitem(@Param("key") String key, @Param("tid") String tid);

    void setCustom1Null(@Param("pid") String pid, @Param("tid") String tid);

    List<WkMrpitemPojo> getListBySheetid(@Param("sheetid") String key, @Param("tid") String tid);

    List<WkMrpitemPojo> getMrpItemListByMachItemids(List<String> machitems, String tenantid);

    List<WkMrpitemPojo> getListInObjids(Set<String> objids, String tid);

}

