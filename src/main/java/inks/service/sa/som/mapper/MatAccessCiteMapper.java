package inks.service.sa.som.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@Mapper
public interface MatAccessCiteMapper {

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateBuyFAcceFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);



    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    Double getBuyFRemQty(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新送货单完工数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateBuyFFinishCount(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);
    int updateBuyOrderFinishCount(@Param("key") String orderitemid, @Param("refno") String orderuid, @Param("tid") String tid);

    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateGoodsBuyRemQty(@Param("key") String key, @Param("goodsuid") String goodsuid, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDeliAcceFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    Double getDeliRemQty(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新送货单完工数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateDeliFinishCount(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    int updateBusMachItemOutQuantity(@Param("key") String machitemid, @Param("refno") String machuid, @Param("tid") String tid);


    /**
     * 刷新发货完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateGoodsBusRemQty(@Param("key") String key, @Param("goodsuid") String goodsuid, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateWkAcceFinish(@Param("key") String key, @Param("refno") String refno, @Param("now") Date now, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateMachInQty(@Param("key") String key, @Param("refno") String refno, @Param("goodsid") String goodsid, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateMachWkFinishCount(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    Double getWkRemQty(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新送货单完工数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateWkAcceFinishCount(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateWsAcceFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);


    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    Double getWsRemQty(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新送货单完工数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateWsAcceFinishCount(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateNoteAcceFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);


    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateRequAcceFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    Double getRequRemQty(@Param("key") String key, @Param("tid") String tid);

    /**
     * 刷新出入库完成数
     *
     * @param key 实例对象
     * @return 影响行数
     */
    int updateRequFinishCount(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

    /**
     * @return int
     * @Description 更新过数记录的Acceid
     * <AUTHOR>
     * @param[1] wkqtyid 过数记录id
     * @param[2] id 出入库单id
     * @param[3] tid
     * @time 2023/3/24 13:46
     */
    int updateWkQtyAcceid(@Param("key") String wkqtyid, @Param("acceid") String id, @Param("tid") String tenantid);

    int updateWkScCompleteFinish(@Param("citeitemid") String citeitemid, @Param("tid") String tid);

    Double getWkScCompleteRemQty(@Param("citeitemid") String citeitemid, @Param("tid") String tid);

    int updateWkScCompleteFinishCount(@Param("citeitemid") String citeitemid, @Param("citeuid") String citeuid, @Param("tid") String tid);

    int updateBuyOrderItemInStoreQty(@Param("key") String machitemid, @Param("refno") String machuid, @Param("tid") String tid);

    double getPriceFromBuyFinishItem(String citeitemid, String tid);

    int updateAcceFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

//    /**
//     * 刷新出入库完成数
//     *
//     * @param key 实例对象
//     * @return 影响行数
//     */
//    int updateReqrAcceFinish(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);
//
//    /**
//     * 刷新出入库完成数
//     *
//     * @param key 实例对象
//     * @return 影响行数
//     */
//    Double getReqrRemQty(@Param("key") String key, @Param("tid") String tid);

//    /**
//     * 刷新出入库完成数
//     *
//     * @param key 实例对象
//     * @return 影响行数
//     */
//    int updateReqrFinishCount(@Param("key") String key, @Param("refno") String refno, @Param("tid") String tid);

}
