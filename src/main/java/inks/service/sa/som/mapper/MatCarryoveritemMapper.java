package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.MatCarryoveritemEntity;
import inks.service.sa.som.domain.pojo.MatCarryoveritemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 仓库结转子表(MatCarryoveritem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-06 11:24:17
 */
 @Mapper
public interface MatCarryoveritemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    MatCarryoveritemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<MatCarryoveritemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<MatCarryoveritemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param matCarryoveritemEntity 实例对象
     * @return 影响行数
     */
    int insert(MatCarryoveritemEntity matCarryoveritemEntity);

    
    /**
     * 修改数据
     *
     * @param matCarryoveritemEntity 实例对象
     * @return 影响行数
     */
    int update(MatCarryoveritemEntity matCarryoveritemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

