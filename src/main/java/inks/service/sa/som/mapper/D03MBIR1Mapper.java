package inks.service.sa.som.mapper;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface D03MBIR1Mapper {
    /*
     *
     * <AUTHOR>
     * @description 供应商金额占比
     * @date 2021/12/31
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByGroupMax(QueryParam queryParam);

    /*
     *
     * <AUTHOR>
     * @description 产品排行
     * @date 2021/12/31
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByGoodsMax(QueryParam queryParam);

    /*
     *
     * <AUTHOR>
     * @description 采购统计图年
     * @date 2021/12/31
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByYear(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 采购统计图月
     * @date 2021/12/31
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByMonth(QueryParam queryParam);
    /*
     *
     * <AUTHOR>
     * @description 采购统计图日
     * @date 2021/12/31
     * @param * @param null
     * @return
     */
    List<ChartPojo> getSumAmtByDay(QueryParam queryParam);


    /*
     *
     * <AUTHOR>
     * @description 本月销售额
     * @date 2021/12/30
     * @param * @param null
     * @return
     */
    ChartPojo getTagSumAmtQtyByDate(QueryParam queryParam);



//    /*
//      *
//      * <AUTHOR>
//      * @description 应付返回的记录数
//      * @date 2021/12/31
//      * @param * @param null
//      * @return
//      */
//    List<ChartPojo> getItemCountSumAmtGroupMonth(String tid);
//
//
//     /*
//      *
//      * <AUTHOR>
//      * @description 预警记录
//      * @date 2021/12/31
//      * @param * @param null
//      * @return
//      */
//    List<Map<String,Object>> getList(String tid);
//
//    /*
//     *
//     * <AUTHOR>
//     * @description 本月采购额
//     * @date 2021/12/31
//     * @param * @param null
//     * @return
//     */
//    List<ChartPojo> getItemCountByMonth(QueryParam queryParam);

}
