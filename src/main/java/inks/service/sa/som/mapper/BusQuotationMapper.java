package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BusQuotationEntity;
import inks.service.sa.som.domain.pojo.BusQuotationPojo;
import inks.service.sa.som.domain.pojo.BusQuotationitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报价单(BusQuotation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-06-06 08:21:09
 */
@Mapper
public interface BusQuotationMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BusQuotationPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusQuotationitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BusQuotationPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param busQuotationEntity 实例对象
     * @return 影响行数
     */
    int insert(BusQuotationEntity busQuotationEntity);

    
    /**
     * 修改数据
     *
     * @param busQuotationEntity 实例对象
     * @return 影响行数
     */
    int update(BusQuotationEntity busQuotationEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param busQuotationPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(BusQuotationPojo busQuotationPojo);
                                                                                                                                                                              /**
     * 修改数据
     *
     * @param busQuotationEntity 实例对象
     * @return 影响行数
     */
    int approval(BusQuotationEntity busQuotationEntity);

    void updateIntendedItemFinishMark(@Param("intendeditemid") String intendeditemid, @Param("finishMark") int finishMark, @Param("tid") String tid);

    void updateIntendedFinishCount(@Param("intendeditemid") String intendeditemid, @Param("tid") String tid);
}

