package inks.service.sa.som.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.som.domain.BuyDeductionitemEntity;
import inks.service.sa.som.domain.pojo.BuyDeductionitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购扣款Item(BuyDeductionitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-05-06 20:36:07
 */
 @Mapper
public interface BuyDeductionitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    BuyDeductionitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<BuyDeductionitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<BuyDeductionitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param buyDeductionitemEntity 实例对象
     * @return 影响行数
     */
    int insert(BuyDeductionitemEntity buyDeductionitemEntity);

    
    /**
     * 修改数据
     *
     * @param buyDeductionitemEntity 实例对象
     * @return 影响行数
     */
    int update(BuyDeductionitemEntity buyDeductionitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

