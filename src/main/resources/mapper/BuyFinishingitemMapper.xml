<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyFinishingitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyFinishingitemPojo">
        <include refid="selectBuyFinishingitemVo"/>
        where Buy_FinishingItem.id = #{key}
          and Buy_FinishingItem.Tenantid = #{tid}
    </select>
    <sql id="selectBuyFinishingitemVo">
        SELECT
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10,
            Buy_FinishingItem.id,
            Buy_FinishingItem.Pid,
            Buy_FinishingItem.Goodsid,
            Buy_FinishingItem.ItemCode,
            Buy_FinishingItem.ItemName,
            Buy_FinishingItem.ItemSpec,
            Buy_FinishingItem.ItemUnit,
            Buy_FinishingItem.Quantity,
            Buy_FinishingItem.TaxPrice,
            Buy_FinishingItem.TaxAmount,
            Buy_FinishingItem.Price,
            Buy_FinishingItem.Amount,
            Buy_FinishingItem.TaxTotal,
            Buy_FinishingItem.ItemTaxrate,
            Buy_FinishingItem.Remark,
            Buy_FinishingItem.OrderNo,
            Buy_FinishingItem.OrderUid,
            Buy_FinishingItem.OrderItemid,
            Buy_FinishingItem.StateCode,
            Buy_FinishingItem.StateDate,
            Buy_FinishingItem.Inspectid,
            Buy_FinishingItem.InspectUid,
            Buy_FinishingItem.PassedQty,
            Buy_FinishingItem.FinishQty,
            Buy_FinishingItem.Closed,
            Buy_FinishingItem.RowNum,
            Buy_FinishingItem.InvoQty,
            Buy_FinishingItem.InvoClosed,
            Buy_FinishingItem.VirtualItem,
            Buy_FinishingItem.Customer,
            Buy_FinishingItem.CustPO,
            Buy_FinishingItem.Location,
            Buy_FinishingItem.BatchNo,
            Buy_FinishingItem.MachUid,
            Buy_FinishingItem.MachItemid,
            Buy_FinishingItem.MachBatch,
            Buy_FinishingItem.MachGroupid,
            Buy_FinishingItem.MainPlanUid,
            Buy_FinishingItem.MainPlanItemid,
            Buy_FinishingItem.MrpUid,
            Buy_FinishingItem.MrpItemid,
            Buy_FinishingItem.MrpObjGoodsid,
            Buy_FinishingItem.DeliQty,
            Buy_FinishingItem.DisannulMark,
            Buy_FinishingItem.DisannulListerid,
            Buy_FinishingItem.DisannulLister,
            Buy_FinishingItem.DisannulDate,
            Buy_FinishingItem.AttributeJson,
            Buy_FinishingItem.SourceType,
            Buy_FinishingItem.Custom1,
            Buy_FinishingItem.Custom2,
            Buy_FinishingItem.Custom3,
            Buy_FinishingItem.Custom4,
            Buy_FinishingItem.Custom5,
            Buy_FinishingItem.Custom6,
            Buy_FinishingItem.Custom7,
            Buy_FinishingItem.Custom8,
            Buy_FinishingItem.Custom9,
            Buy_FinishingItem.Custom10,
            Buy_FinishingItem.Custom11,
            Buy_FinishingItem.Custom12,
            Buy_FinishingItem.Custom13,
            Buy_FinishingItem.Custom14,
            Buy_FinishingItem.Custom15,
            Buy_FinishingItem.Custom16,
            Buy_FinishingItem.Custom17,
            Buy_FinishingItem.Custom18,
            Buy_FinishingItem.Tenantid,
            Buy_FinishingItem.Revision,
            g2.GoodsName as MrpObjGoodsName,
            g2.GoodsUid as MrpObjGoodsUid
        FROM
            Mat_Goods
                RIGHT JOIN Buy_FinishingItem ON Buy_FinishingItem.Goodsid = Mat_Goods.id
                LEFT JOIN Mat_Goods g2 ON Buy_FinishingItem.MrpObjGoodsid = g2.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyFinishingitemPojo">
        <include refid="selectBuyFinishingitemVo"/>
        where 1 = 1 and Buy_FinishingItem.Tenantid =#{tenantid}
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_FinishingItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Buy_FinishingItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Buy_FinishingItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Buy_FinishingItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Buy_FinishingItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Buy_FinishingItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Buy_FinishingItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Buy_FinishingItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.orderno != null and SearchPojo.orderno != ''">
            and Buy_FinishingItem.orderno like concat('%', #{SearchPojo.orderno}, '%')
        </if>
        <if test="SearchPojo.orderuid != null and SearchPojo.orderuid != ''">
            and Buy_FinishingItem.orderuid like concat('%', #{SearchPojo.orderuid}, '%')
        </if>
        <if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid != ''">
            and Buy_FinishingItem.orderitemid like concat('%', #{SearchPojo.orderitemid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Buy_FinishingItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.inspectid != null and SearchPojo.inspectid != ''">
            and Buy_FinishingItem.inspectid like concat('%', #{SearchPojo.inspectid}, '%')
        </if>
        <if test="SearchPojo.inspectuid != null and SearchPojo.inspectuid != ''">
            and Buy_FinishingItem.inspectuid like concat('%', #{SearchPojo.inspectuid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Buy_FinishingItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Buy_FinishingItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Buy_FinishingItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Buy_FinishingItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Buy_FinishingItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Buy_FinishingItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Buy_FinishingItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Buy_FinishingItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Buy_FinishingItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Buy_FinishingItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Buy_FinishingItem.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Buy_FinishingItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Buy_FinishingItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Buy_FinishingItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Buy_FinishingItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Buy_FinishingItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Buy_FinishingItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Buy_FinishingItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Buy_FinishingItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Buy_FinishingItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Buy_FinishingItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Buy_FinishingItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Buy_FinishingItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Buy_FinishingItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.custom11 != null and SearchPojo.custom11 != ''">
            and Buy_FinishingItem.custom11 like concat('%', #{SearchPojo.custom11}, '%')
        </if>
        <if test="SearchPojo.custom12 != null and SearchPojo.custom12 != ''">
            and Buy_FinishingItem.custom12 like concat('%', #{SearchPojo.custom12}, '%')
        </if>
        <if test="SearchPojo.custom13 != null and SearchPojo.custom13 != ''">
            and Buy_FinishingItem.custom13 like concat('%', #{SearchPojo.custom13}, '%')
        </if>
        <if test="SearchPojo.custom14 != null and SearchPojo.custom14 != ''">
            and Buy_FinishingItem.custom14 like concat('%', #{SearchPojo.custom14}, '%')
        </if>
        <if test="SearchPojo.custom15 != null and SearchPojo.custom15 != ''">
            and Buy_FinishingItem.custom15 like concat('%', #{SearchPojo.custom15}, '%')
        </if>
        <if test="SearchPojo.custom16 != null and SearchPojo.custom16 != ''">
            and Buy_FinishingItem.custom16 like concat('%', #{SearchPojo.custom16}, '%')
        </if>
        <if test="SearchPojo.custom17 != null and SearchPojo.custom17 != ''">
            and Buy_FinishingItem.custom17 like concat('%', #{SearchPojo.custom17}, '%')
        </if>
        <if test="SearchPojo.custom18 != null and SearchPojo.custom18 != ''">
            and Buy_FinishingItem.custom18 like concat('%', #{SearchPojo.custom18}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Buy_FinishingItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Buy_FinishingItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Buy_FinishingItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Buy_FinishingItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Buy_FinishingItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Buy_FinishingItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Buy_FinishingItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.orderno != null and SearchPojo.orderno != ''">
                or Buy_FinishingItem.OrderNo like concat('%', #{SearchPojo.orderno}, '%')
            </if>
            <if test="SearchPojo.orderuid != null and SearchPojo.orderuid != ''">
                or Buy_FinishingItem.OrderUid like concat('%', #{SearchPojo.orderuid}, '%')
            </if>
            <if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid != ''">
                or Buy_FinishingItem.OrderItemid like concat('%', #{SearchPojo.orderitemid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Buy_FinishingItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.inspectid != null and SearchPojo.inspectid != ''">
                or Buy_FinishingItem.Inspectid like concat('%', #{SearchPojo.inspectid}, '%')
            </if>
            <if test="SearchPojo.inspectuid != null and SearchPojo.inspectuid != ''">
                or Buy_FinishingItem.InspectUid like concat('%', #{SearchPojo.inspectuid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Buy_FinishingItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Buy_FinishingItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Buy_FinishingItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Buy_FinishingItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Buy_FinishingItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Buy_FinishingItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Buy_FinishingItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Buy_FinishingItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Buy_FinishingItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Buy_FinishingItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Buy_FinishingItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Buy_FinishingItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Buy_FinishingItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Buy_FinishingItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Buy_FinishingItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Buy_FinishingItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Buy_FinishingItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Buy_FinishingItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Buy_FinishingItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Buy_FinishingItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Buy_FinishingItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Buy_FinishingItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Buy_FinishingItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Buy_FinishingItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.custom11 != null and SearchPojo.custom11 != ''">
                or Buy_FinishingItem.Custom11 like concat('%', #{SearchPojo.custom11}, '%')
            </if>
            <if test="SearchPojo.custom12 != null and SearchPojo.custom12 != ''">
                or Buy_FinishingItem.Custom12 like concat('%', #{SearchPojo.custom12}, '%')
            </if>
            <if test="SearchPojo.custom13 != null and SearchPojo.custom13 != ''">
                or Buy_FinishingItem.Custom13 like concat('%', #{SearchPojo.custom13}, '%')
            </if>
            <if test="SearchPojo.custom14 != null and SearchPojo.custom14 != ''">
                or Buy_FinishingItem.Custom14 like concat('%', #{SearchPojo.custom14}, '%')
            </if>
            <if test="SearchPojo.custom15 != null and SearchPojo.custom15 != ''">
                or Buy_FinishingItem.Custom15 like concat('%', #{SearchPojo.custom15}, '%')
            </if>
            <if test="SearchPojo.custom16 != null and SearchPojo.custom16 != ''">
                or Buy_FinishingItem.Custom16 like concat('%', #{SearchPojo.custom16}, '%')
            </if>
            <if test="SearchPojo.custom17 != null and SearchPojo.custom17 != ''">
                or Buy_FinishingItem.Custom17 like concat('%', #{SearchPojo.custom17}, '%')
            </if>
            <if test="SearchPojo.custom18 != null and SearchPojo.custom18 != ''">
                or Buy_FinishingItem.Custom18 like concat('%', #{SearchPojo.custom18}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.BuyFinishingitemPojo">
        SELECT Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Surface,
               Mat_Goods.Drawing,
               Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10,
               Buy_FinishingItem.id,
               Buy_FinishingItem.Pid,
               Buy_FinishingItem.Goodsid,
               Buy_FinishingItem.ItemCode,
               Buy_FinishingItem.ItemName,
               Buy_FinishingItem.ItemSpec,
               Buy_FinishingItem.ItemUnit,
               Buy_FinishingItem.Quantity,
               Buy_FinishingItem.TaxPrice,
               Buy_FinishingItem.TaxAmount,
               Buy_FinishingItem.Price,
               Buy_FinishingItem.Amount,
               Buy_FinishingItem.TaxTotal,
               Buy_FinishingItem.ItemTaxrate,
               Buy_FinishingItem.Remark,
               Buy_FinishingItem.OrderNo,
               Buy_FinishingItem.OrderUid,
               Buy_FinishingItem.OrderItemid,
               Buy_FinishingItem.StateCode,
               Buy_FinishingItem.StateDate,
               Buy_FinishingItem.Inspectid,
               Buy_FinishingItem.InspectUid,
               Buy_FinishingItem.PassedQty,
               Buy_FinishingItem.FinishQty,
               Buy_FinishingItem.Closed,
               Buy_FinishingItem.RowNum,
               Buy_FinishingItem.InvoQty,
               Buy_FinishingItem.InvoClosed,
               Buy_FinishingItem.VirtualItem,
               Buy_FinishingItem.Customer,
               Buy_FinishingItem.CustPO,
               Buy_FinishingItem.Location,
               Buy_FinishingItem.BatchNo,
               Buy_FinishingItem.MachUid,
               Buy_FinishingItem.MachItemid,
               Buy_FinishingItem.MachBatch,
               Buy_FinishingItem.MachGroupid,
               Buy_FinishingItem.MainPlanUid,
               Buy_FinishingItem.MainPlanItemid,
               Buy_FinishingItem.MrpUid,
               Buy_FinishingItem.MrpItemid,
               Buy_FinishingItem.MrpObjGoodsid,
               Buy_FinishingItem.DeliQty,
               Buy_FinishingItem.DisannulMark,
               Buy_FinishingItem.DisannulListerid,
               Buy_FinishingItem.DisannulLister,
               Buy_FinishingItem.DisannulDate,
               Buy_FinishingItem.AttributeJson,
               Buy_FinishingItem.SourceType,
               Buy_FinishingItem.Custom1,
               Buy_FinishingItem.Custom2,
               Buy_FinishingItem.Custom3,
               Buy_FinishingItem.Custom4,
               Buy_FinishingItem.Custom5,
               Buy_FinishingItem.Custom6,
               Buy_FinishingItem.Custom7,
               Buy_FinishingItem.Custom8,
               Buy_FinishingItem.Custom9,
               Buy_FinishingItem.Custom10,
               Buy_FinishingItem.Custom11,
               Buy_FinishingItem.Custom12,
               Buy_FinishingItem.Custom13,
               Buy_FinishingItem.Custom14,
               Buy_FinishingItem.Custom15,
               Buy_FinishingItem.Custom16,
               Buy_FinishingItem.Custom17,
               Buy_FinishingItem.Custom18,
               Buy_FinishingItem.Tenantid,
               Buy_FinishingItem.Revision
        FROM Mat_Goods
                 RIGHT JOIN Buy_FinishingItem ON Buy_FinishingItem.Goodsid = Mat_Goods.id
        where Buy_FinishingItem.Pid = #{Pid}
          and Buy_FinishingItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Buy_FinishingItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice, TaxAmount, Price, Amount, TaxTotal, ItemTaxrate, Remark, OrderNo, OrderUid, OrderItemid, StateCode, StateDate, Inspectid, InspectUid, PassedQty, FinishQty, Closed, RowNum, InvoQty, InvoClosed, VirtualItem, Customer, CustPO, Location, BatchNo, MachUid, MachItemid, MachBatch, MachGroupid, MainPlanUid, MainPlanItemid, MrpUid, MrpItemid, MrpObjGoodsid, DeliQty, DisannulMark, DisannulListerid, DisannulLister, DisannulDate, AttributeJson, SourceType, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Custom11, Custom12, Custom13, Custom14, Custom15, Custom16, Custom17, Custom18, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{taxprice}, #{taxamount}, #{price}, #{amount}, #{taxtotal}, #{itemtaxrate}, #{remark}, #{orderno}, #{orderuid}, #{orderitemid}, #{statecode}, #{statedate}, #{inspectid}, #{inspectuid}, #{passedqty}, #{finishqty}, #{closed}, #{rownum}, #{invoqty}, #{invoclosed}, #{virtualitem}, #{customer}, #{custpo}, #{location}, #{batchno}, #{machuid}, #{machitemid}, #{machbatch}, #{machgroupid}, #{mainplanuid}, #{mainplanitemid}, #{mrpuid}, #{mrpitemid}, #{mrpobjgoodsid}, #{deliqty}, #{disannulmark}, #{disannullisterid}, #{disannullister}, #{disannuldate}, #{attributejson}, #{sourcetype}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{custom11}, #{custom12}, #{custom13}, #{custom14}, #{custom15}, #{custom16}, #{custom17}, #{custom18}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_FinishingItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="orderno != null ">
                OrderNo = #{orderno},
            </if>
            <if test="orderuid != null ">
                OrderUid = #{orderuid},
            </if>
            <if test="orderitemid != null ">
                OrderItemid = #{orderitemid},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="inspectid != null ">
                Inspectid = #{inspectid},
            </if>
            <if test="inspectuid != null ">
                InspectUid = #{inspectuid},
            </if>
            <if test="passedqty != null">
                PassedQty = #{passedqty},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="invoqty != null">
                InvoQty = #{invoqty},
            </if>
            <if test="invoclosed != null">
                InvoClosed = #{invoclosed},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machbatch != null ">
                MachBatch = #{machbatch},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null ">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="mrpobjgoodsid != null ">
                MrpObjGoodsid = #{mrpobjgoodsid},
            </if>
            <if test="deliqty != null">
                DeliQty = #{deliqty},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="custom11 != null ">
                Custom11 = #{custom11},
            </if>
            <if test="custom12 != null ">
                Custom12 = #{custom12},
            </if>
            <if test="custom13 != null ">
                Custom13 = #{custom13},
            </if>
            <if test="custom14 != null ">
                Custom14 = #{custom14},
            </if>
            <if test="custom15 != null ">
                Custom15 = #{custom15},
            </if>
            <if test="custom16 != null ">
                Custom16 = #{custom16},
            </if>
            <if test="custom17 != null ">
                Custom17 = #{custom17},
            </if>
            <if test="custom18 != null ">
                Custom18 = #{custom18},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_FinishingItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

