<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatAccessCiteMapper">

    <!--    收货 Eric 20211213-->
    <update id="updateBuyFAcceFinish">
        update Buy_FinishingItem
        SET FinishQty =COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                 FROM Mat_AccessItem
                                          LEFT OUTER JOIN Mat_Access
                                                          ON Mat_AccessItem.pid = Mat_Access.id
                                 where (Mat_Access.BillType = '收货入库' or Mat_Access.BillType = '购退出库')
                                   and Mat_AccessItem.citeUid = #{refno}
                                   and Mat_AccessItem.citeitemid = #{key}
                                   and Mat_AccessItem.Tenantid = #{tid}), 0) -
                       COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                 FROM Mat_AccessItem
                                          LEFT OUTER JOIN Mat_Access ON Mat_AccessItem.pid =
                                                                        Mat_Access.id
                                 where (Mat_Access.BillType = '收货红冲' or Mat_Access.BillType = '购退红冲')
                                   and Mat_AccessItem.citeUid = #{refno}
                                   and Mat_AccessItem.citeitemid = #{key}
                                   and Mat_AccessItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <update id="updateBuyOrderItemInStoreQty">
        update Buy_OrderItem
        SET InStoreQty =COALESCE((SELECT SUM(Buy_FinishingItem.FinishQty)
                                  FROM Buy_FinishingItem
                                           LEFT OUTER JOIN Buy_Finishing
                                                           ON Buy_FinishingItem.pid = Buy_Finishing.id
                                  where (Buy_Finishing.BillType = '采购验收')
                                    and Buy_FinishingItem.OrderUid = #{refno}
                                    and Buy_FinishingItem.OrderItemid = #{key}
                                    and Buy_FinishingItem.Tenantid = #{tid}), 0) -
                        COALESCE((SELECT SUM(Buy_FinishingItem.FinishQty)
                                  FROM Buy_FinishingItem
                                           LEFT OUTER JOIN Buy_Finishing ON Buy_FinishingItem.pid = Buy_Finishing.id
                                  where (Buy_Finishing.BillType = '采购退货')
                                    and Buy_FinishingItem.OrderUid = #{refno}
                                    and Buy_FinishingItem.OrderItemid = #{key}
                                    and Buy_FinishingItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <!--    收货 Eric 20211213-->
    <select id="getBuyFRemQty" resultType="double">
        select coalesce(Quantity - FinishQty,0) as RemQty
        FROM Buy_FinishingItem
        where id = #{key}
          and Tenantid = #{tid}
    </select>
    <!--    刷新发货完成行数 Eric 20211213-->
    <update id="updateBuyFFinishCount">
        update Buy_Finishing
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Buy_FinishingItem
                                   where Buy_FinishingItem.Pid =
                                         (SELECT Pid FROM Buy_FinishingItem where id = #{key})
                                     and Buy_FinishingItem.Tenantid = #{tid}
                                     and (Buy_FinishingItem.FinishQty >= Buy_FinishingItem.Quantity or
                                          Buy_FinishingItem.Closed = 1 OR Buy_FinishingItem.VirtualItem = 1)), 0)
        where id = (SELECT Pid FROM Buy_FinishingItem where id = #{key})
          and Tenantid = #{tid}
    </update>


    <update id="updateBuyOrderFinishCount">
        update Buy_Order
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Buy_OrderItem
                                   where Buy_OrderItem.Pid =
                                         (SELECT Pid FROM Buy_OrderItem where id = #{key})
                                     and Buy_OrderItem.Tenantid = #{tid}
                                     and (Buy_OrderItem.FinishQty >= Buy_OrderItem.Quantity or
                                          Buy_OrderItem.Closed = 1 OR Buy_OrderItem.VirtualItem = 1)), 0)
        where id = (SELECT Pid FROM Buy_OrderItem where id = #{key})
          and Tenantid = #{tid}
    </update>


    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateGoodsBuyRemQty">
        update Mat_Goods
        SET BuyRemQty = COALESCE((SELECT SUM(Buy_OrderItem.Quantity - Buy_OrderItem.FinishQty)
                                  FROM Buy_OrderItem
                                           LEFT OUTER JOIN Buy_Order ON Buy_OrderItem.pid =
                                                                        Buy_Order.id
                                  where Buy_OrderItem.Goodsid = #{key}
                                    and Buy_OrderItem.Quantity &gt; Buy_OrderItem.FinishQty
                                    and Buy_OrderItem.Closed = 0
                                    and Buy_OrderItem.DisannulMark = 0
                                    and Buy_Order.Tenantid = #{tid}), 0) +
                        COALESCE((SELECT SUM(CASE
                                                 WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货')
                                                     THEN Buy_FinishingItem.Quantity - Buy_FinishingItem.FinishQty
                                                 ELSE Buy_FinishingItem.FinishQty - Buy_FinishingItem.Quantity END)
                                  FROM Buy_FinishingItem
                                           LEFT OUTER JOIN Buy_Finishing ON Buy_FinishingItem.pid =
                                                                            Buy_Finishing.id
                                  where Buy_Finishing.BillType IN ('采购验收', '其他收货', '采购退货', '其他退货')
                                    and Buy_FinishingItem.Goodsid = #{key}
                                    and Buy_FinishingItem.Quantity &gt; Buy_FinishingItem.FinishQty
                                    and Buy_FinishingItem.Closed = 0
                                    and Buy_FinishingItem.DisannulMark = 0
                                    and Buy_Finishing.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateDeliAcceFinish">
        update Bus_DelieryItem
        SET FinishQty =COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                 FROM Mat_AccessItem
                                          LEFT OUTER JOIN Mat_Access
                                                          ON Mat_AccessItem.pid = Mat_Access.id
                                 where (Mat_Access.BillType = '发货出库' or Mat_Access.BillType = '客退入库')
                                   and Mat_AccessItem.citeUid = #{refno}
                                   and Mat_AccessItem.citeitemid = #{key}
                                   and Mat_AccessItem.Tenantid = #{tid}), 0) -
                       COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                 FROM Mat_AccessItem
                                          LEFT OUTER JOIN Mat_Access ON Mat_AccessItem.pid =
                                                                        Mat_Access.id
                                 where (Mat_Access.BillType = '发货红冲' or Mat_Access.BillType = '客退红冲')
                                   and Mat_AccessItem.citeUid = #{refno}
                                   and Mat_AccessItem.citeitemid = #{key}
                                   and Mat_AccessItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    收货 Eric 20211213-->
    <select id="getDeliRemQty" resultType="double">
        SELECT COALESCE((Quantity + FreeQty - FinishQty), 0) as RemQty
        FROM Bus_DelieryItem
        WHERE id = #{key}
          AND Tenantid = #{tid};
    </select>

    <!--    刷新发货完成行数 Eric 20211213-->
    <update id="updateDeliFinishCount">
        update Bus_Deliery
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Bus_DelieryItem
                                   where Bus_DelieryItem.Pid =
                                         (SELECT Pid FROM Bus_DelieryItem where id = #{key})
                                     and Bus_DelieryItem.Tenantid = #{tid}
                                     and (Bus_DelieryItem.FinishQty >= Bus_DelieryItem.Quantity or
                                          Bus_DelieryItem.FinishClosed = 1 OR Bus_DelieryItem.VirtualItem = 1)), 0)
        where id = (SELECT Pid FROM Bus_DelieryItem where id = #{key})
          and Tenantid = #{tid}
    </update>


    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateGoodsBusRemQty">
        update Mat_Goods
        SET BusRemQty = COALESCE((SELECT SUM(Bus_MachiningItem.Quantity - Bus_MachiningItem.FinishQty)
                                  FROM Bus_MachiningItem
                                           LEFT OUTER JOIN Bus_Machining ON Bus_MachiningItem.pid =
                                                                            Bus_Machining.id
                                  where Bus_MachiningItem.Goodsid = #{key}
                                    and Bus_MachiningItem.Quantity &gt; Bus_MachiningItem.FinishQty
                                    and Bus_MachiningItem.Closed = 0
                                    and Bus_MachiningItem.DisannulMark = 0
                                    and Bus_Machining.Tenantid = #{tid}), 0) +
                        COALESCE((SELECT SUM(CASE
                                                 WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货')
                                                     THEN Bus_DelieryItem.Quantity - Bus_DelieryItem.FinishQty
                                                 ELSE Bus_DelieryItem.FinishQty - Bus_DelieryItem.Quantity END)
                                  FROM Bus_DelieryItem
                                           LEFT OUTER JOIN Bus_Deliery ON Bus_DelieryItem.pid =
                                                                          Bus_Deliery.id
                                  where Bus_Deliery.BillType IN ('发出商品', '其他发货', '订单退货', '其他退货')
                                    and Bus_DelieryItem.Goodsid = #{key}
                                    and Bus_DelieryItem.Quantity &gt; Bus_DelieryItem.FinishQty
                                    and Bus_DelieryItem.FinishClosed = 0
                                    and Bus_DelieryItem.DisannulMark = 0
                                    and Bus_Deliery.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    收货 Eric 20211213-->
    <update id="updateWkAcceFinish">
        update Wk_WorksheetItem
        SET FinishQty =COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                 FROM Mat_AccessItem
                                          LEFT OUTER JOIN Mat_Access
                                                          ON Mat_AccessItem.pid = Mat_Access.id
                                 where Mat_Access.BillType = '生产入库'
                                   and Mat_AccessItem.citeUid = #{refno}
                                   and Mat_AccessItem.citeitemid = #{key}
                                   and Mat_AccessItem.Tenantid = #{tid}), 0) -
                       COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                 FROM Mat_AccessItem
                                          LEFT OUTER JOIN Mat_Access ON Mat_AccessItem.pid =
                                                                        Mat_Access.id
                                 where Mat_Access.BillType = '生产红冲'
                                   and Mat_AccessItem.citeUid = #{refno}
                                   and Mat_AccessItem.citeitemid = #{key}
                                   and Mat_AccessItem.Tenantid = #{tid}), 0)
          , StateDate=#{now}
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--    收货 Eric 20211213-->
    <update id="updateMachInQty">
        update Bus_MachiningItem
        SET InQuantity =COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                  FROM Mat_AccessItem
                                           LEFT OUTER JOIN Mat_Access
                                                           ON Mat_AccessItem.pid = Mat_Access.id
                                  where Mat_Access.BillType = '生产入库'
                                    and Mat_AccessItem.MachUid = #{refno}
                                    and Mat_AccessItem.Machitemid = #{key}
                                    and Mat_AccessItem.Goodsid = #{goodsid}
                                    and Mat_AccessItem.Tenantid = #{tid}), 0) -
                        COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                  FROM Mat_AccessItem
                                           LEFT OUTER JOIN Mat_Access ON Mat_AccessItem.pid =
                                                                         Mat_Access.id
                                  where Mat_Access.BillType = '生产红冲'
                                    and Mat_AccessItem.MachUid = #{refno}
                                    and Mat_AccessItem.Machitemid = #{key}
                                    and Mat_AccessItem.Goodsid = #{goodsid}
                                    and Mat_AccessItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <update id="updateBusMachItemOutQuantity">
        update Bus_MachiningItem
        SET OutQuantity =COALESCE((SELECT SUM(Bus_DelieryItem.FinishQty)
                                   FROM Bus_DelieryItem
                                            LEFT OUTER JOIN Bus_Deliery
                                                            ON Bus_DelieryItem.pid = Bus_Deliery.id
                                   where (Bus_Deliery.BillType = '发出商品')
                                     and Bus_DelieryItem.MachUid = #{refno}
                                     and Bus_DelieryItem.MachItemid = #{key}
                                     and Bus_DelieryItem.Tenantid = #{tid}), 0) -
                         COALESCE((SELECT SUM(Bus_DelieryItem.FinishQty)
                                   FROM Bus_DelieryItem
                                            LEFT OUTER JOIN Bus_Deliery ON Bus_DelieryItem.pid = Bus_Deliery.id
                                   where (Bus_Deliery.BillType = '订单退货')
                                     and Bus_DelieryItem.MachUid = #{refno}
                                     and Bus_DelieryItem.MachItemid = #{key}
                                     and Bus_DelieryItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateMachWkFinishCount">
        update Bus_Machining
        SET WkFinishCount =COALESCE((SELECT COUNT(0)
                                     FROM Bus_MachiningItem
                                     where Bus_MachiningItem.Pid =
                                           (SELECT Pid FROM Bus_MachiningItem where id = #{key})
                                       and Bus_MachiningItem.Tenantid = #{tid}
                                       and Bus_MachiningItem.InQuantity >= Bus_MachiningItem.WkQty), 0)
        where id = (SELECT Pid FROM Bus_MachiningItem where id = #{key})
          and Tenantid = #{tid}
    </update>
    <!--    收货 Eric 20211213-->
    <select id="getWkRemQty" resultType="double">
        select IFNULL(IFNULL(WkPcsQty,Quantity) - FinishQty, 0) AS RemQty
        FROM Wk_WorksheetItem
        where id = #{key}
          and Tenantid = #{tid}
    </select>

    <!--    刷新发货完成行数 Eric 20211213-->
    <update id="updateWkAcceFinishCount">
        update Wk_Worksheet
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_WorksheetItem
                                   where Wk_WorksheetItem.Pid =
                                         (SELECT Pid FROM Wk_WorksheetItem where id = #{key})
                                     and Wk_WorksheetItem.Tenantid = #{tid}
                                     and (Wk_WorksheetItem.FinishQty >= Wk_WorksheetItem.Quantity or
                                          Wk_WorksheetItem.Closed = 1)), 0)
        where id = (SELECT Pid FROM Wk_WorksheetItem where id = #{key})
          and Tenantid = #{tid}
    </update>

    <!--   刷新委制单的完成数 记得同步修改SQL: manu服务的updateWsAcceFinish方法-->
    <update id="updateWsAcceFinish">
        update Wk_SubcontractItem
        SET FinishQty =COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                 FROM Mat_AccessItem
                                          LEFT OUTER JOIN Mat_Access
                                                          ON Mat_AccessItem.pid = Mat_Access.id
                                 where Mat_Access.BillType = '加工入库'
                                   and Mat_AccessItem.citeUid = #{refno}
                                   and Mat_AccessItem.citeitemid = #{key}
                                   and Mat_AccessItem.Tenantid = #{tid}), 0) -
                       COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                 FROM Mat_AccessItem
                                          LEFT OUTER JOIN Mat_Access ON Mat_AccessItem.pid =
                                                                        Mat_Access.id
                                 where Mat_Access.BillType = '加工红冲'
                                   and Mat_AccessItem.citeUid = #{refno}
                                   and Mat_AccessItem.citeitemid = #{key}
                                   and Mat_AccessItem.Tenantid = #{tid}), 0) +
                       COALESCE((SELECT SUM(Wk_ScCompleteItem.quantity)
                                 FROM Wk_ScCompleteItem
                                          LEFT OUTER JOIN Wk_ScComplete
                                                          ON Wk_ScCompleteItem.pid = Wk_ScComplete.id
                                 where Wk_ScCompleteItem.citeitemid = #{key}
                                   and Wk_ScCompleteItem.Closed = 0
                                   and Wk_ScCompleteItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--    收货 Eric 20211213-->
    <select id="getWsRemQty" resultType="double">
        select COALESCE(Quantity - FinishQty, 0) AS RemQty
        FROM Wk_SubcontractItem
        where id = #{key}
          and Tenantid = #{tid}
    </select>
    <!--    刷新发货完成行数 Eric 20211213-->
    <update id="updateWsAcceFinishCount">
        update Wk_Subcontract
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_SubcontractItem
                                   where Wk_SubcontractItem.Pid =
                                         (SELECT Pid FROM Wk_SubcontractItem where id = #{key})
                                     and Wk_SubcontractItem.Tenantid = #{tid}
                                     and (Wk_SubcontractItem.FinishQty >= Wk_SubcontractItem.Quantity or
                                          Wk_SubcontractItem.Closed = 1)), 0)
        where id = (SELECT Pid FROM Wk_SubcontractItem where id = #{key})
          and Tenantid = #{tid}
    </update>
    <!--    刷新发货完成数 Eric 20211213-->
<!--    <update id="updateNoteAcceFinish">-->
<!--        update Mat_InveNoteItem-->
<!--        SET FinishQty =COALESCE((SELECT SUM(Mat_AccessItem.quantity)-->
<!--                                 FROM Mat_AccessItem-->
<!--                                          LEFT OUTER JOIN Mat_Access-->
<!--                                                          ON Mat_AccessItem.pid = Mat_Access.id-->
<!--                                 where (Mat_Access.BillType = '盘盈入库' or Mat_Access.BillType = '盘亏红冲')-->
<!--                                   and Mat_AccessItem.citeUid = #{refno}-->
<!--                                   and Mat_AccessItem.citeitemid = #{key}-->
<!--                                   and Mat_AccessItem.Tenantid = #{tid}), 0) - -->
<!--                       COALESCE((SELECT SUM(Mat_AccessItem.quantity)-->
<!--                                 FROM Mat_AccessItem-->
<!--                                          LEFT OUTER JOIN Mat_Access ON Mat_AccessItem.pid =-->
<!--                                                                        Mat_Access.id-->
<!--                                 where (Mat_Access.BillType = '盘亏入库' or Mat_Access.BillType = '盘盈红冲')-->
<!--                                   and Mat_AccessItem.citeUid = #{refno}-->
<!--                                   and Mat_AccessItem.citeitemid = #{key}-->
<!--                                   and Mat_AccessItem.Tenantid = #{tid}), 0)-->
<!--        where id = #{key}-->
<!--          and Tenantid = #{tid}-->
<!--    </update>-->

<!--    上面的SQL很慢(900ms),优化:通过合并子查询、使用INNER JOIN和避免COALESCE函数，可以显著提高SQL的性能;再加上Mat_AccessItem.citeitemid索引()-->
    <update id="updateNoteAcceFinish">
        update Mat_InveNoteItem
        SET FinishQty = (SELECT SUM(CASE
                                        WHEN Mat_Access.BillType IN ('盘盈入库', '盘亏红冲')
                                            THEN Mat_AccessItem.quantity
                                        ELSE 0
            END) - SUM(CASE
                           WHEN Mat_Access.BillType IN ('盘亏入库', '盘盈红冲')
                               THEN Mat_AccessItem.quantity
                           ELSE 0
            END)
                         FROM Mat_AccessItem
                                  INNER JOIN Mat_Access ON Mat_AccessItem.pid = Mat_Access.id
                         WHERE Mat_AccessItem.citeitemid = #{key}
                           AND Mat_AccessItem.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>





    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateRequAcceFinish">
        update Mat_RequisitionItem
        SET FinishQty =COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                 FROM Mat_AccessItem
                                          LEFT OUTER JOIN Mat_Access
                                                          ON Mat_AccessItem.pid = Mat_Access.id
                                 where Mat_Access.BillType IN ('领料出库','退料入库')
                                   and Mat_AccessItem.citeUid = #{refno}
                                   and Mat_AccessItem.citeitemid = #{key}
                                   and Mat_AccessItem.Tenantid = #{tid}), 0) -
                       COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                 FROM Mat_AccessItem
                                          LEFT OUTER JOIN Mat_Access ON Mat_AccessItem.pid =
                                                                        Mat_Access.id
                                 where Mat_Access.BillType IN ('领料红冲','退料红冲')
                                   and Mat_AccessItem.citeUid = #{refno}
                                   and Mat_AccessItem.citeitemid = #{key}
                                   and Mat_AccessItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--    收货 Eric 20211213-->
    <select id="getRequRemQty" resultType="double">
        select COALESCE(Quantity - FinishQty, 0) as RemQty
        FROM Mat_RequisitionItem
        where id = #{key}
          and Tenantid = #{tid}
    </select>
    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateRequFinishCount">
        update Mat_Requisition
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Mat_RequisitionItem
                                   where Mat_RequisitionItem.Pid =
                                         (SELECT Pid FROM Mat_RequisitionItem where id = #{key})
                                     and Mat_RequisitionItem.Tenantid = #{tid}
                                     and (Mat_RequisitionItem.Closed = 1
                                       or Mat_RequisitionItem.FinishQty >= Mat_RequisitionItem.Quantity)
                                  ), 0)
        where id = (SELECT Pid FROM Mat_RequisitionItem where id = #{key})
          and Tenantid = #{tid}
    </update>
<!--    更新过数记录的Acceid-->
    <update id="updateWkQtyAcceid">
          update Wk_WipQty
          set Acceid = #{acceid}
          where id = #{key}
             and Tenantid = #{tid}
    </update>

    <update id="updateWkScCompleteFinish">
        update Wk_ScCompleteItem
        SET FinishQty =COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                 FROM Mat_AccessItem
                                          LEFT OUTER JOIN Mat_Access
                                                          ON Mat_AccessItem.pid = Mat_Access.id
                                 where Mat_Access.BillType = '委制入库'
                                   and Mat_AccessItem.citeitemid = #{citeitemid}
                                   and Mat_AccessItem.Tenantid = #{tid}), 0) -
                       COALESCE((SELECT SUM(Mat_AccessItem.quantity)
                                 FROM Mat_AccessItem
                                          LEFT OUTER JOIN Mat_Access ON Mat_AccessItem.pid =
                                                                        Mat_Access.id
                                 where Mat_Access.BillType = '委制红冲'
                                   and Mat_AccessItem.citeitemid = #{citeitemid}
                                   and Mat_AccessItem.Tenantid = #{tid}), 0)
        where id = #{citeitemid}
          and Tenantid = #{tid}
    </update>

    <select id="getWkScCompleteRemQty" resultType="java.lang.Double">
        select COALESCE(ABS(Quantity) - ABS(FinishQty), 0) AS RemQty
        FROM Wk_ScCompleteItem
        where id = #{citeitemid}
        and Tenantid = #{tid}
    </select>

    <update id="updateWkScCompleteFinishCount">
        update Wk_ScComplete
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_ScCompleteItem
                                   where Wk_ScCompleteItem.Pid =
                                         (SELECT Pid FROM Wk_ScCompleteItem where id = #{citeitemid})
                                     and Wk_ScCompleteItem.Tenantid = #{tid}
                                     and (ABS(Wk_ScCompleteItem.FinishQty) >= ABS(Wk_ScCompleteItem.Quantity) or
                                          Wk_ScCompleteItem.Closed = 1)), 0)
        where id = (SELECT Pid FROM Wk_ScCompleteItem where id = #{citeitemid})
          and Tenantid = #{tid}
    </update>

    <select id="getPriceFromBuyFinishItem" resultType="double">
        select COALESCE(Price, 0)
        from Buy_FinishingItem
        where id = #{citeitemid}
          and Tenantid = #{tid}
    </select>

    <update id="updateAcceFinish">
        UPDATE Mat_CustFinishItem
        SET FinishQty = COALESCE(
                (SELECT SUM(
                                CASE
                                    WHEN Mat_Access.BillType IN ('客供入库', '供退出库') THEN Mat_AccessItem.quantity
                                    WHEN Mat_Access.BillType IN ('客供红冲', '供退红冲') THEN -Mat_AccessItem.quantity
                                    ELSE 0
                                    END
                        )
                 FROM Mat_AccessItem
                          INNER JOIN Mat_Access ON Mat_AccessItem.pid = Mat_Access.id
                 WHERE Mat_AccessItem.citeUid = #{refno}
                   AND Mat_AccessItem.citeitemid = #{key}
                   AND Mat_AccessItem.Tenantid = #{tid}
                   AND Mat_Access.BillType IN ('客供入库', '供退出库', '客供红冲', '供退红冲')), 0)
        WHERE id = #{key}
          AND Tenantid = #{tid}
    </update>


    <!--    &lt;!&ndash;    刷新发货完成数 Eric 20211213&ndash;&gt;-->
<!--    <update id="updateReqrAcceFinish">-->
<!--        update Mat_ReqRetrunItem-->
<!--        SET FinishQty =COALESCE((SELECT SUM(Mat_AccessItem.quantity)-->
<!--                                 FROM Mat_AccessItem-->
<!--                                          LEFT OUTER JOIN Mat_Access-->
<!--                                                          ON Mat_AccessItem.pid = Mat_Access.id-->
<!--                                 where Mat_Access.BillType = '退料入库'-->
<!--                                   and Mat_AccessItem.citeUid = #{refno}-->
<!--                                   and Mat_AccessItem.citeitemid = #{key}-->
<!--                                   and Mat_AccessItem.Tenantid = #{tid}), 0) - -->
<!--                       COALESCE((SELECT SUM(Mat_AccessItem.quantity)-->
<!--                                 FROM Mat_AccessItem-->
<!--                                          LEFT OUTER JOIN Mat_Access ON Mat_AccessItem.pid =-->
<!--                                                                        Mat_Access.id-->
<!--                                 where Mat_Access.BillType = '退料红冲'-->
<!--                                   and Mat_AccessItem.citeUid = #{refno}-->
<!--                                   and Mat_AccessItem.citeitemid = #{key}-->
<!--                                   and Mat_AccessItem.Tenantid = #{tid}), 0)-->
<!--        where id = #{key}-->
<!--          and Tenantid = #{tid}-->
<!--    </update>-->
<!--    &lt;!&ndash;    收货 Eric 20211213&ndash;&gt;-->
<!--    <select id="getReqrRemQty" resultType="double">-->
<!--        select Quantity - FinishQty as RemQty-->
<!--        FROM Mat_ReqRetrunItem-->
<!--        where id = #{key}-->
<!--          and Tenantid = #{tid}-->
<!--    </select>-->

<!--    &lt;!&ndash;    刷新发货完成数 Eric 20211213&ndash;&gt;-->
<!--    <update id="updateReqrFinishCount">-->
<!--        update Mat_ReqRetrun-->
<!--        SET FinishCount =COALESCE((SELECT COUNT(0)-->
<!--                                   FROM Mat_ReqRetrunItem-->
<!--                                   where Mat_ReqRetrunItem.Pid = (SELECT Pid FROM Mat_ReqRetrunItem where id = #{key})-->
<!--                                     and Mat_ReqRetrunItem.Tenantid = #{tid}-->
<!--                                     and (Mat_ReqRetrunItem.Closed = 1-->
<!--                                       or Mat_ReqRetrunItem.FinishQty >= Mat_ReqRetrunItem.Quantity)-->
<!--                                  ), 0)-->
<!--        where id = (SELECT Pid FROM Mat_ReqRetrunItem where id = #{key})-->
<!--          and Tenantid = #{tid}-->
<!--    </update>-->

</mapper>

