<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.D03M03R1Mapper">
    <select id="getSumAmtByYear" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m') AS NAME,
               sum(Bus_MachiningItem.quantity)              AS
                                                               VALUE
                ,
               SUM(Bus_MachiningItem.TaxAmount)             AS value2
        FROM Bus_Machining
                 LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
                 RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE 1 = 1
          AND DATE_FORMAT(Bus_Machining.BillDate, '%Y') = #{SearchPojo.StartDate}
          AND Bus_Machining.Tenantid = #{Tenantid}

        GROUP BY DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m')
    </select>
    <select id="getSumAmtByMonth" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m-%d') AS NAME,
               sum(Bus_MachiningItem.quantity)                 AS
                                                                  VALUE
                ,
               SUM(Bus_MachiningItem.TaxAmount)                AS value2
        FROM Bus_Machining
                 LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
                 RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE 1 = 1
          AND DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m') = #{SearchPojo.StartDate}
          AND Bus_Machining.Tenantid = #{Tenantid}

        GROUP BY DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m-%d')
    </select>
    <select id="getSumAmtByDay" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m-%d') AS NAME,
               sum(Bus_MachiningItem.quantity)                 AS
                                                                  VALUE
                ,
               SUM(Bus_MachiningItem.TaxAmount)                AS value2
        FROM Bus_Machining
                 LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
                 RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE 1 = 1
          AND (DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m-%d') BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
          AND Bus_Machining.Tenantid = #{Tenantid}
        GROUP BY DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m-%d')
    </select>

    <select id="getSumPageListByGroup" resultType="inks.service.sa.som.domain.pojo.BuyFinishingitemdetailPojo">
        select App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               sum(IF(Buy_Finishing.BillType IN ('采购验收', '其他收货'), Buy_FinishingItem.TaxAmount,
                      0 - Buy_FinishingItem.TaxAmount)) as TaxAmount
        FROM Buy_Finishing
                 RIGHT JOIN Buy_FinishingItem
                            ON Buy_FinishingItem.Pid = Buy_Finishing.id
                 LEFT JOIN App_Workgroup ON Buy_Finishing.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods ON Buy_FinishingItem.Goodsid = Mat_Goods.id
        where Buy_FinishingItem.disannulmark = 0
          and Buy_Finishing.Tenantid = #{tenantid}
          and (Buy_Finishing.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        group by App_Workgroup.Abbreviate, App_Workgroup.GroupName, App_Workgroup.GroupUid
        order by GroupUid
    </select>

    <select id="getSumPageListByGoods" resultType="inks.service.sa.som.domain.pojo.BuyFinishingitemdetailPojo">
        select Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               sum(IF(Buy_Finishing.BillType IN ('采购验收', '其他收货'), Buy_FinishingItem.TaxAmount,
                      0 - Buy_FinishingItem.TaxAmount)) as TaxAmount
        FROM Buy_Finishing
                 RIGHT JOIN Buy_FinishingItem
                            ON Buy_FinishingItem.Pid = Buy_Finishing.id
                 LEFT JOIN App_Workgroup ON Buy_Finishing.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods ON Buy_FinishingItem.Goodsid = Mat_Goods.id
        where Buy_FinishingItem.disannulmark = 0
          and Buy_Finishing.Tenantid = #{tenantid}
          and (Buy_Finishing.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        group by Mat_Goods.GoodsUid, Mat_Goods.GoodsName, Mat_Goods.GoodsSpec, Mat_Goods.GoodsUnit
        order by GoodsUid
    </select>
</mapper>
