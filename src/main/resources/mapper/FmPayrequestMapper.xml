<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.FmPayrequestMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.FmPayrequestPojo">
        select
          id, RefNo, BillType, BillTitle, BillDate, Projectid, ProjCode, ProjName, ApplicantDept, Belong, Suppliers, ItemName, Quantity, Price, Amount, TaxTotal, TaxAmount, InvoCode, AimDate, Payment, Operator, Operatorid, Summary, StateCode, StateDate, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision
        from Fm_PayRequest
        where Fm_PayRequest.id = #{key} 
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
          id, RefNo, BillType, BillTitle, BillDate, Projectid, ProjCode, ProjName, ApplicantDept, Belong, Suppliers, ItemName, Quantity, Price, Amount, TaxTotal, TaxAmount, InvoCode, AimDate, Payment, Operator, Operatorid, Summary, StateCode, StateDate, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, DisannulMark, DisannulDate, DisannulLister, DisannulListerid, PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision        from Fm_PayRequest
    </sql>
    <sql id="selectdetailVo">
        select Fm_PayRequestItem.id,
               Fm_PayRequestItem.Pid,
               Fm_PayRequestItem.Invoid,
               Fm_PayRequestItem.InvoBillCode,
               Fm_PayRequestItem.InvoCode,
               Fm_PayRequestItem.InvoAmount,
               Fm_PayRequestItem.Amount,
               Fm_PayRequestItem.RowNum,
               Fm_PayRequestItem.Remark,
               Fm_PayRequestItem.Custom1,
               Fm_PayRequestItem.Custom2,
               Fm_PayRequestItem.Custom3,
               Fm_PayRequestItem.Custom4,
               Fm_PayRequestItem.Custom5,
               Fm_PayRequestItem.Custom6,
               Fm_PayRequestItem.Custom7,
               Fm_PayRequestItem.Custom8,
               Fm_PayRequestItem.Custom9,
               Fm_PayRequestItem.Custom10,
               Fm_PayRequestItem.Tenantid,
               Fm_PayRequestItem.Revision,
               Fm_PayRequest.RefNo,
               Fm_PayRequest.BillType,
               Fm_PayRequest.BillDate,
               Fm_PayRequest.BillTitle
        from Fm_PayRequestItem
                 left join Fm_PayRequest on Fm_PayRequestItem.Pid = Fm_PayRequest.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.som.domain.pojo.FmPayrequestitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Fm_PayRequest.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Fm_PayRequest.refno like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Fm_PayRequest.billtype like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Fm_PayRequest.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.projectid != null ">
   and Fm_PayRequest.projectid like concat('%', #{SearchPojo.projectid}, '%')
</if>
<if test="SearchPojo.projcode != null ">
   and Fm_PayRequest.projcode like concat('%', #{SearchPojo.projcode}, '%')
</if>
<if test="SearchPojo.projname != null ">
   and Fm_PayRequest.projname like concat('%', #{SearchPojo.projname}, '%')
</if>
<if test="SearchPojo.applicantdept != null ">
   and Fm_PayRequest.applicantdept like concat('%', #{SearchPojo.applicantdept}, '%')
</if>
<if test="SearchPojo.belong != null ">
   and Fm_PayRequest.belong like concat('%', #{SearchPojo.belong}, '%')
</if>
<if test="SearchPojo.suppliers != null ">
   and Fm_PayRequest.suppliers like concat('%', #{SearchPojo.suppliers}, '%')
</if>
<if test="SearchPojo.itemname != null ">
   and Fm_PayRequest.itemname like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.invocode != null ">
   and Fm_PayRequest.invocode like concat('%', #{SearchPojo.invocode}, '%')
</if>
<if test="SearchPojo.payment != null ">
   and Fm_PayRequest.payment like concat('%', #{SearchPojo.payment}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Fm_PayRequest.operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Fm_PayRequest.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Fm_PayRequest.summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   and Fm_PayRequest.statecode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Fm_PayRequest.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Fm_PayRequest.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Fm_PayRequest.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Fm_PayRequest.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Fm_PayRequest.assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Fm_PayRequest.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.disannullister != null ">
   and Fm_PayRequest.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
</if>
<if test="SearchPojo.disannullisterid != null ">
   and Fm_PayRequest.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Fm_PayRequest.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Fm_PayRequest.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Fm_PayRequest.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Fm_PayRequest.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Fm_PayRequest.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Fm_PayRequest.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Fm_PayRequest.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Fm_PayRequest.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Fm_PayRequest.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Fm_PayRequest.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Fm_PayRequest.deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Fm_PayRequest.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Fm_PayRequest.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Fm_PayRequest.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Fm_PayRequest.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.projectid != null ">
   or Fm_PayRequest.Projectid like concat('%', #{SearchPojo.projectid}, '%')
</if>
<if test="SearchPojo.projcode != null ">
   or Fm_PayRequest.ProjCode like concat('%', #{SearchPojo.projcode}, '%')
</if>
<if test="SearchPojo.projname != null ">
   or Fm_PayRequest.ProjName like concat('%', #{SearchPojo.projname}, '%')
</if>
<if test="SearchPojo.applicantdept != null ">
   or Fm_PayRequest.ApplicantDept like concat('%', #{SearchPojo.applicantdept}, '%')
</if>
<if test="SearchPojo.belong != null ">
   or Fm_PayRequest.Belong like concat('%', #{SearchPojo.belong}, '%')
</if>
<if test="SearchPojo.suppliers != null ">
   or Fm_PayRequest.Suppliers like concat('%', #{SearchPojo.suppliers}, '%')
</if>
<if test="SearchPojo.itemname != null ">
   or Fm_PayRequest.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.invocode != null ">
   or Fm_PayRequest.InvoCode like concat('%', #{SearchPojo.invocode}, '%')
</if>
<if test="SearchPojo.payment != null ">
   or Fm_PayRequest.Payment like concat('%', #{SearchPojo.payment}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Fm_PayRequest.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Fm_PayRequest.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Fm_PayRequest.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   or Fm_PayRequest.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Fm_PayRequest.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Fm_PayRequest.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Fm_PayRequest.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Fm_PayRequest.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Fm_PayRequest.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Fm_PayRequest.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.disannullister != null ">
   or Fm_PayRequest.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
</if>
<if test="SearchPojo.disannullisterid != null ">
   or Fm_PayRequest.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Fm_PayRequest.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Fm_PayRequest.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Fm_PayRequest.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Fm_PayRequest.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Fm_PayRequest.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Fm_PayRequest.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Fm_PayRequest.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Fm_PayRequest.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Fm_PayRequest.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Fm_PayRequest.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Fm_PayRequest.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Fm_PayRequest.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.som.domain.pojo.FmPayrequestPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Fm_PayRequest.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.refno != null ">
   and Fm_PayRequest.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Fm_PayRequest.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Fm_PayRequest.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.projectid != null ">
   and Fm_PayRequest.Projectid like concat('%', #{SearchPojo.projectid}, '%')
</if>
<if test="SearchPojo.projcode != null ">
   and Fm_PayRequest.ProjCode like concat('%', #{SearchPojo.projcode}, '%')
</if>
<if test="SearchPojo.projname != null ">
   and Fm_PayRequest.ProjName like concat('%', #{SearchPojo.projname}, '%')
</if>
<if test="SearchPojo.applicantdept != null ">
   and Fm_PayRequest.ApplicantDept like concat('%', #{SearchPojo.applicantdept}, '%')
</if>
<if test="SearchPojo.belong != null ">
   and Fm_PayRequest.Belong like concat('%', #{SearchPojo.belong}, '%')
</if>
<if test="SearchPojo.suppliers != null ">
   and Fm_PayRequest.Suppliers like concat('%', #{SearchPojo.suppliers}, '%')
</if>
<if test="SearchPojo.itemname != null ">
   and Fm_PayRequest.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.invocode != null ">
   and Fm_PayRequest.InvoCode like concat('%', #{SearchPojo.invocode}, '%')
</if>
<if test="SearchPojo.payment != null ">
   and Fm_PayRequest.Payment like concat('%', #{SearchPojo.payment}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Fm_PayRequest.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Fm_PayRequest.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Fm_PayRequest.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   and Fm_PayRequest.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Fm_PayRequest.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Fm_PayRequest.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Fm_PayRequest.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Fm_PayRequest.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Fm_PayRequest.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Fm_PayRequest.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.disannullister != null ">
   and Fm_PayRequest.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
</if>
<if test="SearchPojo.disannullisterid != null ">
   and Fm_PayRequest.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Fm_PayRequest.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Fm_PayRequest.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Fm_PayRequest.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Fm_PayRequest.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Fm_PayRequest.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Fm_PayRequest.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Fm_PayRequest.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Fm_PayRequest.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Fm_PayRequest.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Fm_PayRequest.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Fm_PayRequest.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Fm_PayRequest.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Fm_PayRequest.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Fm_PayRequest.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Fm_PayRequest.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.projectid != null ">
   or Fm_PayRequest.Projectid like concat('%', #{SearchPojo.projectid}, '%')
</if>
<if test="SearchPojo.projcode != null ">
   or Fm_PayRequest.ProjCode like concat('%', #{SearchPojo.projcode}, '%')
</if>
<if test="SearchPojo.projname != null ">
   or Fm_PayRequest.ProjName like concat('%', #{SearchPojo.projname}, '%')
</if>
<if test="SearchPojo.applicantdept != null ">
   or Fm_PayRequest.ApplicantDept like concat('%', #{SearchPojo.applicantdept}, '%')
</if>
<if test="SearchPojo.belong != null ">
   or Fm_PayRequest.Belong like concat('%', #{SearchPojo.belong}, '%')
</if>
<if test="SearchPojo.suppliers != null ">
   or Fm_PayRequest.Suppliers like concat('%', #{SearchPojo.suppliers}, '%')
</if>
<if test="SearchPojo.itemname != null ">
   or Fm_PayRequest.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.invocode != null ">
   or Fm_PayRequest.InvoCode like concat('%', #{SearchPojo.invocode}, '%')
</if>
<if test="SearchPojo.payment != null ">
   or Fm_PayRequest.Payment like concat('%', #{SearchPojo.payment}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Fm_PayRequest.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Fm_PayRequest.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Fm_PayRequest.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.statecode != null ">
   or Fm_PayRequest.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Fm_PayRequest.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Fm_PayRequest.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Fm_PayRequest.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Fm_PayRequest.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Fm_PayRequest.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Fm_PayRequest.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.disannullister != null ">
   or Fm_PayRequest.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
</if>
<if test="SearchPojo.disannullisterid != null ">
   or Fm_PayRequest.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Fm_PayRequest.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Fm_PayRequest.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Fm_PayRequest.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Fm_PayRequest.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Fm_PayRequest.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Fm_PayRequest.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Fm_PayRequest.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Fm_PayRequest.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Fm_PayRequest.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Fm_PayRequest.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Fm_PayRequest.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Fm_PayRequest.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Fm_PayRequest(id, RefNo, BillType, BillTitle, BillDate, Projectid, ProjCode, ProjName, ApplicantDept, Belong, Suppliers, ItemName, Quantity, Price, Amount, TaxTotal, TaxAmount, InvoCode, AimDate, Payment, Operator, Operatorid, Summary, StateCode, StateDate, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, DisannulMark, DisannulDate, DisannulLister, DisannulListerid, PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{applicantdept}, #{belong}, #{suppliers}, #{itemname}, #{quantity}, #{price}, #{amount}, #{taxtotal}, #{taxamount}, #{invocode}, #{aimdate}, #{payment}, #{operator}, #{operatorid}, #{summary}, #{statecode}, #{statedate}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{disannulmark}, #{disannuldate}, #{disannullister}, #{disannullisterid}, #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Fm_PayRequest
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="applicantdept != null ">
                ApplicantDept =#{applicantdept},
            </if>
            <if test="belong != null ">
                Belong =#{belong},
            </if>
            <if test="suppliers != null ">
                Suppliers =#{suppliers},
            </if>
            <if test="itemname != null ">
                ItemName =#{itemname},
            </if>
            <if test="quantity != null">
                Quantity =#{quantity},
            </if>
            <if test="price != null">
                Price =#{price},
            </if>
            <if test="amount != null">
                Amount =#{amount},
            </if>
            <if test="taxtotal != null">
                TaxTotal =#{taxtotal},
            </if>
            <if test="taxamount != null">
                TaxAmount =#{taxamount},
            </if>
            <if test="invocode != null ">
                InvoCode =#{invocode},
            </if>
            <if test="aimdate != null">
                AimDate =#{aimdate},
            </if>
            <if test="payment != null ">
                Payment =#{payment},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="disannulmark != null">
                DisannulMark =#{disannulmark},
            </if>
            <if test="disannuldate != null">
                DisannulDate =#{disannuldate},
            </if>
            <if test="disannullister != null ">
                DisannulLister =#{disannullister},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid =#{disannullisterid},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Fm_PayRequest where id = #{key} 
    </delete>
                                                                                                                                    <!--通过主键审核数据-->
    <update id="approval">
        update Fm_PayRequest SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>
                                                                                            <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.som.domain.pojo.FmPayrequestPojo">
        select
          id
        from Fm_PayRequestItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

</mapper>

