<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.FmIncomeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.FmIncomePojo">
        SELECT Fm_Income.id,
               Fm_Income.RefNo,
               Fm_Income.BillType,
               Fm_Income.BillDate,
               Fm_Income.BillTitle,
               Fm_Income.Projectid,
               Fm_Income.ProjName,
               Fm_Income.ProjCode,
               Fm_Income.Groupid,
               Fm_Income.ChequeNum,
               Fm_Income.Moneyid,
               Fm_Income.Amount,
               Fm_Income.CashAccid,
               Fm_Income.Operator,
               Fm_Income.ProjectCode,
               Fm_Income.Summary,
               Fm_Income.CreateBy,
               Fm_Income.CreateByid,
               Fm_Income.CreateDate,
               Fm_Income.Lister,
               Fm_Income.Listerid,
               Fm_Income.ModifyDate,
               Fm_Income.ModuleCode,
               Fm_Income.CiteUid,
               Fm_Income.Citeid,
               Fm_Income.Benefitid,
               Fm_Income.Custom1,
               Fm_Income.Custom2,
               Fm_Income.Custom3,
               Fm_Income.Custom4,
               Fm_Income.Custom5,
               Fm_Income.Custom6,
               Fm_Income.Custom7,
               Fm_Income.Custom8,
               Fm_Income.Custom9,
               Fm_Income.Custom10,
               Fm_Income.Tenantid,
               Fm_Income.TenantName,
               Fm_Income.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Fm_CashAccount.AccountName
        FROM Fm_Income
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Fm_Income.Groupid
                 LEFT JOIN Fm_CashAccount ON Fm_CashAccount.id = Fm_Income.CashAccid
        where Fm_Income.id = #{key}
          and Fm_Income.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT
            Fm_Income.id,
            Fm_Income.RefNo,
            Fm_Income.BillType,
            Fm_Income.BillDate,
            Fm_Income.BillTitle,
            Fm_Income.Projectid,
            Fm_Income.ProjName,
            Fm_Income.ProjCode,
            Fm_Income.Groupid,
            Fm_Income.ChequeNum,
            Fm_Income.Moneyid,
            Fm_Income.Amount,
            Fm_Income.CashAccid,
            Fm_Income.Operator,
            Fm_Income.ProjectCode,
            Fm_Income.Summary,
            Fm_Income.CreateBy,
            Fm_Income.CreateByid,
            Fm_Income.CreateDate,
            Fm_Income.Lister,
            Fm_Income.Listerid,
            Fm_Income.ModifyDate,
            Fm_Income.ModuleCode,
            Fm_Income.CiteUid,
            Fm_Income.Citeid,
            Fm_Income.Benefitid,
            Fm_Income.Custom1,
            Fm_Income.Custom2,
            Fm_Income.Custom3,
            Fm_Income.Custom4,
            Fm_Income.Custom5,
            Fm_Income.Custom6,
            Fm_Income.Custom7,
            Fm_Income.Custom8,
            Fm_Income.Custom9,
            Fm_Income.Custom10,
            Fm_Income.Tenantid,
            Fm_Income.TenantName,
            Fm_Income.Revision,
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Fm_CashAccount.AccountName
        FROM
            Fm_Income
                LEFT JOIN App_Workgroup ON App_Workgroup.id = Fm_Income.Groupid
                LEFT JOIN Fm_CashAccount ON Fm_CashAccount.id = Fm_Income.CashAccid
    </sql>
    <sql id="selectdetailVo">
        SELECT
            Fm_IncomeItem.id,
            Fm_IncomeItem.Pid,
            Fm_IncomeItem.CostTypeid,
            Fm_IncomeItem.ItemName,
            Fm_IncomeItem.ItemDepict,
            Fm_IncomeItem.Amount,
            Fm_IncomeItem.RowNum,
            Fm_IncomeItem.Remark,
            Fm_IncomeItem.Custom1,
            Fm_IncomeItem.Custom2,
            Fm_IncomeItem.Custom3,
            Fm_IncomeItem.Custom4,
            Fm_IncomeItem.Custom5,
            Fm_IncomeItem.Custom6,
            Fm_IncomeItem.Custom7,
            Fm_IncomeItem.Custom8,
            Fm_IncomeItem.Custom9,
            Fm_IncomeItem.Custom10,
            Fm_IncomeItem.Tenantid,
            Fm_IncomeItem.Revision,
            Fm_CostType.CostCode,
            Fm_CostType.CostName,
            Fm_Income.RefNo,
            Fm_Income.BillType,
            Fm_Income.BillDate,
            Fm_Income.BillTitle,
            Fm_Income.Projectid,
            Fm_Income.ProjName,
            Fm_Income.ProjCode,
            Fm_Income.ChequeNum,
            Fm_Income.Amount,
            Fm_Income.Operator,
            Fm_Income.CreateBy,
            Fm_Income.Lister,
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Fm_CashAccount.AccountName
        FROM
            App_Workgroup
                RIGHT JOIN Fm_Income ON Fm_Income.Groupid = App_Workgroup.id
                RIGHT JOIN Fm_IncomeItem ON Fm_IncomeItem.Pid = Fm_Income.id
                LEFT JOIN Fm_CostType ON Fm_IncomeItem.CostTypeid = Fm_CostType.id
                LEFT JOIN Fm_CashAccount ON Fm_Income.CashAccid = Fm_CashAccount.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.FmIncomeitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Fm_Income.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Fm_Income.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Fm_Income.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Fm_Income.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Fm_Income.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Fm_Income.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.chequenum != null ">
            and Fm_Income.chequenum like concat('%', #{SearchPojo.chequenum}, '%')
        </if>
        <if test="SearchPojo.moneyid != null ">
            and Fm_Income.moneyid like concat('%', #{SearchPojo.moneyid}, '%')
        </if>
        <if test="SearchPojo.cashaccid != null ">
            and Fm_Income.cashaccid like concat('%', #{SearchPojo.cashaccid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Fm_Income.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.projectcode != null ">
            and Fm_Income.projectcode like concat('%', #{SearchPojo.projectcode}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Fm_Income.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Fm_Income.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Fm_Income.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Fm_Income.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Fm_Income.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null ">
            and Fm_Income.modulecode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.citeuid != null ">
            and Fm_Income.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeid != null ">
            and Fm_Income.citeid like concat('%', #{SearchPojo.citeid}, '%')
        </if>
        <if test="SearchPojo.benefitid != null ">
            and Fm_Income.benefitid like concat('%', #{SearchPojo.benefitid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Fm_Income.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Fm_Income.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Fm_Income.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Fm_Income.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Fm_Income.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Fm_Income.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Fm_Income.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Fm_Income.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Fm_Income.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Fm_Income.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Fm_Income.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Fm_Income.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Fm_Income.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Fm_Income.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Fm_Income.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.chequenum != null ">
                or Fm_Income.ChequeNum like concat('%', #{SearchPojo.chequenum}, '%')
            </if>
            <if test="SearchPojo.moneyid != null ">
                or Fm_Income.Moneyid like concat('%', #{SearchPojo.moneyid}, '%')
            </if>
            <if test="SearchPojo.cashaccid != null ">
                or Fm_Income.CashAccid like concat('%', #{SearchPojo.cashaccid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Fm_Income.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.projectcode != null ">
                or Fm_Income.ProjectCode like concat('%', #{SearchPojo.projectcode}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Fm_Income.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Fm_Income.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Fm_Income.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Fm_Income.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Fm_Income.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.modulecode != null ">
                or Fm_Income.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.citeuid != null ">
                or Fm_Income.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeid != null ">
                or Fm_Income.Citeid like concat('%', #{SearchPojo.citeid}, '%')
            </if>
            <if test="SearchPojo.benefitid != null ">
                or Fm_Income.Benefitid like concat('%', #{SearchPojo.benefitid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Fm_Income.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Fm_Income.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Fm_Income.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Fm_Income.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Fm_Income.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Fm_Income.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Fm_Income.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Fm_Income.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Fm_Income.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Fm_Income.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Fm_Income.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.FmIncomePojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Fm_Income.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Fm_Income.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Fm_Income.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Fm_Income.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Fm_Income.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Fm_Income.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.chequenum != null ">
            and Fm_Income.ChequeNum like concat('%', #{SearchPojo.chequenum}, '%')
        </if>
        <if test="SearchPojo.moneyid != null ">
            and Fm_Income.Moneyid like concat('%', #{SearchPojo.moneyid}, '%')
        </if>
        <if test="SearchPojo.cashaccid != null ">
            and Fm_Income.CashAccid like concat('%', #{SearchPojo.cashaccid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Fm_Income.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.projectcode != null ">
            and Fm_Income.ProjectCode like concat('%', #{SearchPojo.projectcode}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Fm_Income.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Fm_Income.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Fm_Income.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Fm_Income.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Fm_Income.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null ">
            and Fm_Income.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.citeuid != null ">
            and Fm_Income.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeid != null ">
            and Fm_Income.Citeid like concat('%', #{SearchPojo.citeid}, '%')
        </if>
        <if test="SearchPojo.benefitid != null ">
            and Fm_Income.Benefitid like concat('%', #{SearchPojo.benefitid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Fm_Income.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Fm_Income.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Fm_Income.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Fm_Income.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Fm_Income.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Fm_Income.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Fm_Income.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Fm_Income.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Fm_Income.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Fm_Income.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Fm_Income.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Fm_Income.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Fm_Income.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Fm_Income.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Fm_Income.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.chequenum != null ">
                or Fm_Income.ChequeNum like concat('%', #{SearchPojo.chequenum}, '%')
            </if>
            <if test="SearchPojo.moneyid != null ">
                or Fm_Income.Moneyid like concat('%', #{SearchPojo.moneyid}, '%')
            </if>
            <if test="SearchPojo.cashaccid != null ">
                or Fm_Income.CashAccid like concat('%', #{SearchPojo.cashaccid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Fm_Income.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.projectcode != null ">
                or Fm_Income.ProjectCode like concat('%', #{SearchPojo.projectcode}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Fm_Income.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Fm_Income.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Fm_Income.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Fm_Income.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Fm_Income.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.modulecode != null ">
                or Fm_Income.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.citeuid != null ">
                or Fm_Income.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeid != null ">
                or Fm_Income.Citeid like concat('%', #{SearchPojo.citeid}, '%')
            </if>
            <if test="SearchPojo.benefitid != null ">
                or Fm_Income.Benefitid like concat('%', #{SearchPojo.benefitid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Fm_Income.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Fm_Income.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Fm_Income.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Fm_Income.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Fm_Income.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Fm_Income.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Fm_Income.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Fm_Income.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Fm_Income.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Fm_Income.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Fm_Income.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Fm_Income(id, RefNo, BillType, BillDate, Projectid, ProjCode, ProjName, BillTitle, Groupid, ChequeNum, Moneyid, Amount, CashAccid,
                              Operator, ProjectCode, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid,
                              ModifyDate, ModuleCode, CiteUid, Citeid, Benefitid, Custom1, Custom2, Custom3, Custom4,
                              Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{billtitle}, #{groupid}, #{chequenum}, #{moneyid},
                #{amount}, #{cashaccid}, #{operator}, #{projectcode}, #{summary}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{modulecode}, #{citeuid}, #{citeid},
                #{benefitid}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Fm_Income
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="chequenum != null ">
                ChequeNum =#{chequenum},
            </if>
            <if test="moneyid != null ">
                Moneyid =#{moneyid},
            </if>
            <if test="amount != null">
                Amount =#{amount},
            </if>
            <if test="cashaccid != null ">
                CashAccid =#{cashaccid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="projectcode != null ">
                ProjectCode =#{projectcode},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="citeuid != null ">
                CiteUid =#{citeuid},
            </if>
            <if test="citeid != null ">
                Citeid =#{citeid},
            </if>
            <if test="benefitid != null ">
                Benefitid =#{benefitid},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Fm_Income
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.FmIncomePojo">
        select
        id
        from Fm_IncomeItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--    刷新发货完成数 Eric ********-->
    <update id="updateCashAmount">
        update Fm_CashAccount
        SET CurrentAmt =CurrentAmt + #{amount}
        where id = #{key}
          and Tenantid = #{tid}
    </update>

</mapper>

