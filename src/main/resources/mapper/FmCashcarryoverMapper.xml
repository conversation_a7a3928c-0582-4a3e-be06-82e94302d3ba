<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.FmCashcarryoverMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.FmCashcarryoverPojo">
        select id,
               RefNo,
               BillType,
               BillDate,
               BillTitle,
               CashAccid,
               CashAccName,
               CarryYear,
               CarryMonth,
               StartDate,
               EndDate,
               Operator,
               Operatorid,
               RowNum,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               BillOpenAmount,
               BillInAmount,
               BillOutAmount,
               BillCloseAmount,
               ItemCount,
               PrintCount,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Deptid,
               Tenantid,
               TenantName,
               Revision
        from Fm_CashCarryover
        where Fm_CashCarryover.id = #{key}
          and Fm_CashCarryover.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               RefNo,
               BillType,
               BillDate,
               BillTitle,
               CashAccid,
               CashAccName,
               CarryYear,
               CarryMonth,
               StartDate,
               EndDate,
               Operator,
               Operatorid,
               RowNum,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               BillOpenAmount,
               BillInAmount,
               BillOutAmount,
               BillCloseAmount,
               ItemCount,
               PrintCount,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Deptid,
               Tenantid,
               TenantName,
               Revision
        from Fm_CashCarryover
    </sql>
    <sql id="selectdetailVo">
        select id,
               RefNo,
               BillType,
               BillDate,
               BillTitle,
               CashAccid,
               CashAccName,
               CarryYear,
               CarryMonth,
               StartDate,
               EndDate,
               Operator,
               Operatorid,
               RowNum,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               BillOpenAmount,
               BillInAmount,
               BillOutAmount,
               BillCloseAmount,
               ItemCount,
               PrintCount,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Deptid,
               Tenantid,
               TenantName,
               Revision
        from Fm_CashCarryover
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.FmCashcarryoveritemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Fm_CashCarryover.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Fm_CashCarryover.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Fm_CashCarryover.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Fm_CashCarryover.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Fm_CashCarryover.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.cashaccid != null ">
            and Fm_CashCarryover.cashaccid like concat('%', #{SearchPojo.cashaccid}, '%')
        </if>
        <if test="SearchPojo.cashaccname != null ">
            and Fm_CashCarryover.cashaccname like concat('%', #{SearchPojo.cashaccname}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Fm_CashCarryover.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Fm_CashCarryover.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Fm_CashCarryover.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Fm_CashCarryover.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Fm_CashCarryover.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Fm_CashCarryover.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Fm_CashCarryover.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Fm_CashCarryover.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Fm_CashCarryover.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Fm_CashCarryover.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Fm_CashCarryover.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Fm_CashCarryover.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Fm_CashCarryover.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Fm_CashCarryover.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Fm_CashCarryover.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Fm_CashCarryover.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Fm_CashCarryover.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Fm_CashCarryover.deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Fm_CashCarryover.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Fm_CashCarryover.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Fm_CashCarryover.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Fm_CashCarryover.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.cashaccid != null ">
                or Fm_CashCarryover.CashAccid like concat('%', #{SearchPojo.cashaccid}, '%')
            </if>
            <if test="SearchPojo.cashaccname != null ">
                or Fm_CashCarryover.CashAccName like concat('%', #{SearchPojo.cashaccname}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Fm_CashCarryover.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Fm_CashCarryover.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Fm_CashCarryover.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Fm_CashCarryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Fm_CashCarryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Fm_CashCarryover.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Fm_CashCarryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Fm_CashCarryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Fm_CashCarryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Fm_CashCarryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Fm_CashCarryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Fm_CashCarryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Fm_CashCarryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Fm_CashCarryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Fm_CashCarryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Fm_CashCarryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Fm_CashCarryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Fm_CashCarryover.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Fm_CashCarryover.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.FmCashcarryoverPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Fm_CashCarryover.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Fm_CashCarryover.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Fm_CashCarryover.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Fm_CashCarryover.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Fm_CashCarryover.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.cashaccid != null ">
            and Fm_CashCarryover.CashAccid like concat('%', #{SearchPojo.cashaccid}, '%')
        </if>
        <if test="SearchPojo.cashaccname != null ">
            and Fm_CashCarryover.CashAccName like concat('%', #{SearchPojo.cashaccname}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Fm_CashCarryover.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Fm_CashCarryover.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Fm_CashCarryover.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Fm_CashCarryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Fm_CashCarryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Fm_CashCarryover.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Fm_CashCarryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Fm_CashCarryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Fm_CashCarryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Fm_CashCarryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Fm_CashCarryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Fm_CashCarryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Fm_CashCarryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Fm_CashCarryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Fm_CashCarryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Fm_CashCarryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Fm_CashCarryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Fm_CashCarryover.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Fm_CashCarryover.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Fm_CashCarryover.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Fm_CashCarryover.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Fm_CashCarryover.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.cashaccid != null ">
                or Fm_CashCarryover.CashAccid like concat('%', #{SearchPojo.cashaccid}, '%')
            </if>
            <if test="SearchPojo.cashaccname != null ">
                or Fm_CashCarryover.CashAccName like concat('%', #{SearchPojo.cashaccname}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Fm_CashCarryover.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Fm_CashCarryover.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Fm_CashCarryover.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Fm_CashCarryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Fm_CashCarryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Fm_CashCarryover.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Fm_CashCarryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Fm_CashCarryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Fm_CashCarryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Fm_CashCarryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Fm_CashCarryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Fm_CashCarryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Fm_CashCarryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Fm_CashCarryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Fm_CashCarryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Fm_CashCarryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Fm_CashCarryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Fm_CashCarryover.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Fm_CashCarryover.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Fm_CashCarryover(id, RefNo, BillType, BillDate, BillTitle, CashAccid, CashAccName, CarryYear,
                                     CarryMonth, StartDate, EndDate, Operator, Operatorid, RowNum, Summary, CreateBy,
                                     CreateByid, CreateDate, Lister, Listerid, ModifyDate, BillOpenAmount, BillInAmount,
                                     BillOutAmount, BillCloseAmount, ItemCount, PrintCount, Custom1, Custom2, Custom3,
                                     Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid,
                                     TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{cashaccid}, #{cashaccname}, #{carryyear},
                #{carrymonth}, #{startdate}, #{enddate}, #{operator}, #{operatorid}, #{rownum}, #{summary}, #{createby},
                #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{billopenamount}, #{billinamount},
                #{billoutamount}, #{billcloseamount}, #{itemcount}, #{printcount}, #{custom1}, #{custom2}, #{custom3},
                #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid},
                #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Fm_CashCarryover
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="cashaccid != null ">
                CashAccid =#{cashaccid},
            </if>
            <if test="cashaccname != null ">
                CashAccName =#{cashaccname},
            </if>
            <if test="carryyear != null">
                CarryYear =#{carryyear},
            </if>
            <if test="carrymonth != null">
                CarryMonth =#{carrymonth},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="billopenamount != null">
                BillOpenAmount =#{billopenamount},
            </if>
            <if test="billinamount != null">
                BillInAmount =#{billinamount},
            </if>
            <if test="billoutamount != null">
                BillOutAmount =#{billoutamount},
            </if>
            <if test="billcloseamount != null">
                BillCloseAmount =#{billcloseamount},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Fm_CashCarryover
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.FmCashcarryoverPojo">
        select
        id
        from Fm_CashCarryoverItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="getBillItemList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.FmCashcarryoveritemPojo">
        SELECT
        'in' AS Direction,
        'D01M08DEP' AS ModuleCode,
        Bus_Deposit.id AS Billid,
        Bus_Deposit.RefNo AS BillUid,
        Bus_Deposit.BillType,
        Bus_Deposit.BillTitle,
        Bus_Deposit.BillDate,
        0.0 AS OpenAmount,
        Bus_DepositCash.Amount AS InAmount,
        0.0 AS OutAmount,
        0.0 AS CloseAmount,
        Fm_CashAccount.AccountName,
        App_Workgroup.GroupName
        FROM
        Bus_Deposit
        RIGHT JOIN Bus_DepositCash ON Bus_Deposit.id = Bus_DepositCash.Pid
        LEFT JOIN Fm_CashAccount ON Fm_CashAccount.id = Bus_DepositCash.CashAccid
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Bus_Deposit.groupid
        WHERE(Bus_Deposit.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        AND Bus_Deposit.Tenantid=#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'in' AS Direction,
        'D01M08' AS ModuleCode,
        Bus_Receipt.id AS Billid,
        Bus_Receipt.RefNo AS BillUid,
        Bus_Receipt.BillType,
        Bus_Receipt.BillTitle,
        Bus_Receipt.BillDate,
        0.0 AS OpenAmount,
        Bus_ReceiptCash.Amount AS InAmount,
        0.0 AS OutAmount,
        0.0 AS CloseAmount,
        Fm_CashAccount.AccountName,
        App_Workgroup.GroupName
        FROM
        Bus_Receipt
        RIGHT JOIN Bus_ReceiptCash ON Bus_Receipt.id = Bus_ReceiptCash.Pid
        LEFT JOIN Fm_CashAccount ON Fm_CashAccount.id = Bus_ReceiptCash.CashAccid
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Bus_Receipt.groupid
        WHERE(Bus_Receipt.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        AND Bus_Receipt.Tenantid=#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' AS Direction,
        'D03M06PRE' AS ModuleCode,
        Buy_Prepayments.id AS Billid,
        Buy_Prepayments.RefNo AS BillUid,
        Buy_Prepayments.BillType,
        Buy_Prepayments.BillTitle,
        Buy_Prepayments.BillDate,
        0.0 AS OpenAmount,
        0.0 AS InAmount,
        Buy_PrepaymentsCash.Amount AS OutAmount,
        0.0 AS CloseAmount,
        Fm_CashAccount.AccountName,
        App_Workgroup.GroupName
        FROM
        Buy_Prepayments
        RIGHT JOIN Buy_PrepaymentsCash ON Buy_Prepayments.id = Buy_PrepaymentsCash.Pid
        LEFT JOIN Fm_CashAccount ON Fm_CashAccount.id = Buy_PrepaymentsCash.CashAccid
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Buy_Prepayments.groupid
        WHERE(Buy_Prepayments.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        AND Buy_Prepayments.Tenantid=#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' AS Direction,
        'D03M06' AS ModuleCode,
        Buy_Voucher.id AS Billid,
        Buy_Voucher.RefNo AS BillUid,
        Buy_Voucher.BillType,
        Buy_Voucher.BillTitle,
        Buy_Voucher.BillDate,
        0.0 AS OpenAmount,
        0.0 AS InAmount,
        Buy_VoucherCash.Amount AS OutAmount,
        0.0 AS CloseAmount,
        Buy_VoucherCash.CashAccName AS accountName,
        App_Workgroup.GroupName
        FROM
        Buy_Voucher
        RIGHT JOIN Buy_VoucherCash ON Buy_Voucher.id = Buy_VoucherCash.pid
        LEFT JOIN Fm_CashAccount ON Fm_CashAccount.id = Buy_VoucherCash.CashAccid
        LEFT JOIN App_Workgroup ON Buy_Voucher.Groupid = App_Workgroup.id
        WHERE(Buy_Voucher.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        AND Buy_Voucher.Tenantid=#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'in' AS Direction,
        'D07M01IN' AS ModuleCode,
        Fm_Income.id AS Billid,
        Fm_Income.RefNo AS BillUid,
        Fm_Income.BillType,
        Fm_Income.BillTitle,
        Fm_Income.BillDate,
        0.0 AS OpenAmount,
        Fm_Income.Amount AS InAmount,
        0.0 AS OutAmount,
        0.0 AS CloseAmount,
        Fm_CashAccount.AccountName AS accountName,
        App_Workgroup.GroupName
        FROM
        Fm_Income
        LEFT JOIN Fm_CashAccount ON Fm_Income.CashAccid = Fm_CashAccount.id
        LEFT JOIN App_Workgroup ON Fm_Income.Groupid = App_Workgroup.id
        WHERE(Fm_Income.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        AND Fm_Income.Tenantid=#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' AS Direction,
        'D07M01OUT' AS ModuleCode,
        Fm_Cost.id AS Billid,
        Fm_Cost.RefNo AS BillUid,
        Fm_Cost.BillType,
        Fm_Cost.BillTitle,
        Fm_Cost.BillDate,
        0.0 AS OpenAmount,
        0.0 AS InAmount,
        Fm_Cost.Amount AS OutAmount,
        0.0 AS CloseAmount,
        Fm_CashAccount.AccountName AS accountName,
        App_Workgroup.GroupName
        FROM
        Fm_Cost
        LEFT JOIN Fm_CashAccount ON Fm_Cost.CashAccid = Fm_CashAccount.id
        LEFT JOIN App_Workgroup ON Fm_Cost.Groupid = App_Workgroup.id
        WHERE(Fm_Cost.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        AND Fm_Cost.Tenantid=#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        ORDER BY Billdate DESC
    </select>

    <!--查询单个-->
    <select id="getMaxEntityByCash" resultType="inks.service.sa.som.domain.pojo.FmCashcarryoverPojo">
        <include refid="selectbillVo"/>
        where Fm_CashCarryover.CashAccid = #{key}
        and Fm_CashCarryover.Tenantid = #{tid}
        Order by EndDate Desc LIMIT 1
    </select>
    <!--查询指定行数据-->
    <select id="pullItemList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.FmCashcarryoveritemPojo">
        SELECT
        'in' as Direction,
        'D01M08DEP' as ModuleCode,
        Bus_Deposit.id as Billid,
        Bus_Deposit.RefNo as BillUid,
        Bus_Deposit.BillType,
        Bus_Deposit.BillTitle,
        Bus_Deposit.BillDate,
        0.0 as OpenAmount,
        Bus_DepositCash.Amount as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount,
        Bus_Deposit.Groupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM
        Bus_DepositCash
        LEFT JOIN Bus_Deposit ON Bus_DepositCash.Pid = Bus_Deposit.id
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Bus_Deposit.groupid
        where (Bus_Deposit.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Deposit.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'in' as Direction,
        'D01M08' as ModuleCode,
        Bus_Receipt.id as Billid,
        Bus_Receipt.RefNo as BillUid,
        Bus_Receipt.BillType,
        Bus_Receipt.BillTitle,
        Bus_Receipt.BillDate,
        0.0 as OpenAmount,
        Bus_ReceiptCash.Amount as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount,
        Bus_Receipt.Groupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM
        Bus_ReceiptCash
        LEFT JOIN Bus_Receipt ON Bus_ReceiptCash.Pid = Bus_Receipt.id
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Bus_Receipt.groupid
        where (Bus_Receipt.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Receipt.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D03M06PRE' as ModuleCode,
        Buy_Prepayments.id as Billid,
        Buy_Prepayments.RefNo as BillUid,
        Buy_Prepayments.BillType,
        Buy_Prepayments.BillTitle,
        Buy_Prepayments.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Buy_PrepaymentsCash.Amount as OutAmount,
        0.0 as CloseAmount,
        Buy_Prepayments.Groupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM
        Buy_PrepaymentsCash
        LEFT JOIN Buy_Prepayments ON Buy_PrepaymentsCash.Pid = Buy_Prepayments.id
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Buy_Prepayments.groupid
        where (Buy_Prepayments.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Prepayments.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D03M06' as ModuleCode,
        Buy_Voucher.id as Billid,
        Buy_Voucher.RefNo as BillUid,
        Buy_Voucher.BillType,
        Buy_Voucher.BillTitle,
        Buy_Voucher.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Buy_VoucherCash.Amount as OutAmount,
        0.0 as CloseAmount,
        Buy_Voucher.Groupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM
        Buy_VoucherCash
        LEFT JOIN Buy_Voucher ON Buy_VoucherCash.Pid = Buy_Voucher.id
        LEFT JOIN App_Workgroup ON App_Workgroup.id =Buy_Voucher.groupid
        where (Buy_Voucher.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Voucher.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'in' as Direction,
        'D07M01IN' as ModuleCode,
        Fm_Income.id as Billid,
        Fm_Income.RefNo as BillUid,
        Fm_Income.BillType,
        Fm_Income.BillTitle,
        Fm_Income.BillDate,
        0.0 as OpenAmount,
        Fm_Income.Amount as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount,
        Fm_Income.Groupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM
        Fm_Income
        LEFT JOIN App_Workgroup ON App_Workgroup.id =Fm_Income.groupid
        where (Fm_Income.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Fm_Income.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D07M01OUT' as ModuleCode,
        Fm_Cost.id as Billid,
        Fm_Cost.RefNo as BillUid,
        Fm_Cost.BillType,
        Fm_Cost.BillTitle,
        Fm_Cost.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Fm_Cost.Amount as OutAmount,
        0.0 as CloseAmount,
        Fm_Cost.Groupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM
        Fm_Cost
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Fm_Cost.groupid
        where (Fm_Cost.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Fm_Cost.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
    </select>

    <!--查询指定行数据-->
    <select id="getMultItemList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.FmCashcarryoveritemPojo">
        <include refid="selectdetailVo"/>
        where Bus_Account.Tenantid =#{tenantid}
        and Bus_AccountItem.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        ${filterstr}
        order by Bus_AccountItem.BillDate
    </select>

    <!--查询指定行数据-->
    <select id="getNowPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.FmCashcarryoverPojo">
        SELECT * FROM (        SELECT
        'in' as Direction,
        'D01M08DEP' as ModuleCode,
        Bus_Deposit.id as Billid,
        Bus_Deposit.RefNo as BillUid,
        Bus_Deposit.BillType,
        Bus_Deposit.BillTitle,
        Bus_Deposit.BillDate,
        0.0 as OpenAmount,
        Bus_DepositCash.Amount as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount,
        Bus_Deposit.Groupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM
        Bus_DepositCash
        LEFT JOIN Bus_Deposit ON Bus_DepositCash.Pid = Bus_Deposit.id
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Bus_Deposit.groupid
        where (Bus_Deposit.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Deposit.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'in' as Direction,
        'D01M08' as ModuleCode,
        Bus_Receipt.id as Billid,
        Bus_Receipt.RefNo as BillUid,
        Bus_Receipt.BillType,
        Bus_Receipt.BillTitle,
        Bus_Receipt.BillDate,
        0.0 as OpenAmount,
        Bus_ReceiptCash.Amount as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount,
        Bus_Receipt.Groupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM
        Bus_ReceiptCash
        LEFT JOIN Bus_Receipt ON Bus_ReceiptCash.Pid = Bus_Receipt.id
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Bus_Receipt.groupid
        where (Bus_Receipt.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Receipt.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D03M06PRE' as ModuleCode,
        Buy_Prepayments.id as Billid,
        Buy_Prepayments.RefNo as BillUid,
        Buy_Prepayments.BillType,
        Buy_Prepayments.BillTitle,
        Buy_Prepayments.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Buy_PrepaymentsCash.Amount as OutAmount,
        0.0 as CloseAmount,
        Buy_Prepayments.Groupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM
        Buy_PrepaymentsCash
        LEFT JOIN Buy_Prepayments ON Buy_PrepaymentsCash.Pid = Buy_Prepayments.id
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Buy_Prepayments.groupid
        where (Buy_Prepayments.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Prepayments.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D03M06' as ModuleCode,
        Buy_Voucher.id as Billid,
        Buy_Voucher.RefNo as BillUid,
        Buy_Voucher.BillType,
        Buy_Voucher.BillTitle,
        Buy_Voucher.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Buy_VoucherCash.Amount as OutAmount,
        0.0 as CloseAmount,
        Buy_Voucher.Groupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM
        Buy_VoucherCash
        LEFT JOIN Buy_Voucher ON Buy_VoucherCash.Pid = Buy_Voucher.id
        LEFT JOIN App_Workgroup ON App_Workgroup.id =Buy_Voucher.groupid
        where (Buy_Voucher.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Voucher.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'in' as Direction,
        'D07M01IN' as ModuleCode,
        Fm_Income.id as Billid,
        Fm_Income.RefNo as BillUid,
        Fm_Income.BillType,
        Fm_Income.BillTitle,
        Fm_Income.BillDate,
        0.0 as OpenAmount,
        Fm_Income.Amount as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount,
        Fm_Income.Groupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM
        Fm_Income
        LEFT JOIN App_Workgroup ON App_Workgroup.id =Fm_Income.groupid
        where (Fm_Income.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Fm_Income.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D07M01OUT' as ModuleCode,
        Fm_Cost.id as Billid,
        Fm_Cost.RefNo as BillUid,
        Fm_Cost.BillType,
        Fm_Cost.BillTitle,
        Fm_Cost.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Fm_Cost.Amount as OutAmount,
        0.0 as CloseAmount,
        Fm_Cost.Groupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate
        FROM
        Fm_Cost
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Fm_Cost.groupid
        where (Fm_Cost.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Fm_Cost.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        ) t
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>

    <!--通过主键删除-->
    <delete id="deleteByMonth">
        delete
        from Fm_CashCarryover
        where CarryYear = #{year}
          and CarryMonth = #{nonth}
          and Tenantid = #{tid}
    </delete>

</mapper>

