<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyInvoiceMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyInvoicePojo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Buy_Invoice.id,
               Buy_Invoice.RefNo,
               Buy_Invoice.BillType,
               Buy_Invoice.BillTitle,
               Buy_Invoice.BillDate,
               Buy_Invoice.Projectid,
               Buy_Invoice.ProjName,
               Buy_Invoice.ProjCode,
               Buy_Invoice.Groupid,
               Buy_Invoice.Taxrate,
               Buy_Invoice.TaxAmount,
               Buy_Invoice.TaxTotal,
               Buy_Invoice.Amount,
               Buy_Invoice.InvoDate,
               Buy_Invoice.InvoCode,
               Buy_Invoice.AimDate,
               Buy_Invoice.Paid,
               Buy_Invoice.Summary,
               Buy_Invoice.CreateBy,
               Buy_Invoice.CreateByid,
               Buy_Invoice.CreateDate,
               Buy_Invoice.Lister,
               Buy_Invoice.Listerid,
               Buy_Invoice.ModifyDate,
               Buy_Invoice.Assessor,
               Buy_Invoice.Assessorid,
               Buy_Invoice.AssessDate,
               Buy_Invoice.StateCode,
               Buy_Invoice.StateDate,
               Buy_Invoice.Closed,
               Buy_Invoice.DisannulMark,
               Buy_Invoice.FmDocMark,
               Buy_Invoice.FmDocCode,
               Buy_Invoice.Operator,
               Buy_Invoice.FirstAmt,
               Buy_Invoice.LastAmt,
               Buy_Invoice.Custom1,
               Buy_Invoice.Custom2,
               Buy_Invoice.Custom3,
               Buy_Invoice.Custom4,
               Buy_Invoice.Custom5,
               Buy_Invoice.Custom6,
               Buy_Invoice.Custom7,
               Buy_Invoice.Custom8,
               Buy_Invoice.Custom9,
               Buy_Invoice.Custom10,
               Buy_Invoice.Tenantid,
               Buy_Invoice.Revision
        FROM App_Workgroup
                 RIGHT JOIN Buy_Invoice ON App_Workgroup.id = Buy_Invoice.Groupid

        where Buy_Invoice.id = #{key}
          and Buy_Invoice.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Buy_Invoice.id,
               Buy_Invoice.RefNo,
               Buy_Invoice.BillType,
               Buy_Invoice.BillTitle,
               Buy_Invoice.BillDate,
               Buy_Invoice.Projectid,
               Buy_Invoice.ProjName,
               Buy_Invoice.ProjCode,
               Buy_Invoice.Groupid,
               Buy_Invoice.Taxrate,
               Buy_Invoice.TaxAmount,
               Buy_Invoice.TaxTotal,
               Buy_Invoice.Amount,
               Buy_Invoice.InvoDate,
               Buy_Invoice.InvoCode,
               Buy_Invoice.AimDate,
               Buy_Invoice.Paid,
               Buy_Invoice.Summary,
               Buy_Invoice.CreateBy,
               Buy_Invoice.CreateByid,
               Buy_Invoice.CreateDate,
               Buy_Invoice.Lister,
               Buy_Invoice.Listerid,
               Buy_Invoice.ModifyDate,
               Buy_Invoice.Assessor,
               Buy_Invoice.Assessorid,
               Buy_Invoice.AssessDate,
               Buy_Invoice.StateCode,
               Buy_Invoice.StateDate,
               Buy_Invoice.Closed,
               Buy_Invoice.DisannulMark,
               Buy_Invoice.FmDocMark,
               Buy_Invoice.FmDocCode,
               Buy_Invoice.Operator,
               Buy_Invoice.FirstAmt,
               Buy_Invoice.LastAmt,
               Buy_Invoice.Custom1,
               Buy_Invoice.Custom2,
               Buy_Invoice.Custom3,
               Buy_Invoice.Custom4,
               Buy_Invoice.Custom5,
               Buy_Invoice.Custom6,
               Buy_Invoice.Custom7,
               Buy_Invoice.Custom8,
               Buy_Invoice.Custom9,
               Buy_Invoice.Custom10,
               Buy_Invoice.Tenantid,
               Buy_Invoice.Revision
        FROM App_Workgroup
                 RIGHT JOIN Buy_Invoice ON App_Workgroup.id = Buy_Invoice.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT Buy_InvoiceItem.id,
               Buy_InvoiceItem.Pid,
               Buy_InvoiceItem.FinishUid,
               Buy_InvoiceItem.FinishDate,
               Buy_InvoiceItem.FinishType,
               Buy_InvoiceItem.FinishItemid,
               Buy_InvoiceItem.Goodsid,
               Buy_InvoiceItem.BillQty,
               Buy_InvoiceItem.Quantity,
               Buy_InvoiceItem.TaxPrice,
               Buy_InvoiceItem.TaxAmount,
               Buy_InvoiceItem.Price,
               Buy_InvoiceItem.Amount,
               Buy_InvoiceItem.TaxTotal,
               Buy_InvoiceItem.RowNum,
               Buy_InvoiceItem.Remark,
               Buy_InvoiceItem.MachUid,
               Buy_InvoiceItem.MachItemid,
               Buy_InvoiceItem.MachGroupid,
               Buy_InvoiceItem.CustPO,
               Buy_InvoiceItem.Customer,
               Buy_InvoiceItem.MrpUid,
               Buy_InvoiceItem.MrpItemid,
               Buy_InvoiceItem.ItemTaxrate,
               Buy_InvoiceItem.AvgFirstAmt,
               Buy_InvoiceItem.AvgLastAmt,
               Buy_InvoiceItem.Custom1,
               Buy_InvoiceItem.Custom2,
               Buy_InvoiceItem.Custom3,
               Buy_InvoiceItem.Custom4,
               Buy_InvoiceItem.Custom5,
               Buy_InvoiceItem.Custom6,
               Buy_InvoiceItem.Custom7,
               Buy_InvoiceItem.Custom8,
               Buy_InvoiceItem.Custom9,
               Buy_InvoiceItem.Custom10,
               Buy_InvoiceItem.Custom11,
               Buy_InvoiceItem.Custom12,
               Buy_InvoiceItem.Custom13,
               Buy_InvoiceItem.Custom14,
               Buy_InvoiceItem.Custom15,
               Buy_InvoiceItem.Custom16,
               Buy_InvoiceItem.Custom17,
               Buy_InvoiceItem.Custom18,
               Buy_InvoiceItem.Tenantid,
               Buy_InvoiceItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10,
               Buy_Invoice.RefNo,
               Buy_Invoice.BillType,
               Buy_Invoice.BillTitle,
               Buy_Invoice.BillDate,
               Buy_Invoice.Projectid,
               Buy_Invoice.ProjName,
               Buy_Invoice.ProjCode,
               Buy_Invoice.Taxrate,
               Buy_Invoice.TaxAmount,
               Buy_Invoice.TaxTotal,
               Buy_Invoice.Amount,
               Buy_Invoice.InvoDate,
               Buy_Invoice.InvoCode,
               Buy_Invoice.AimDate,
               Buy_Invoice.Paid,
               Buy_Invoice.CreateBy,
               Buy_Invoice.Summary,
               Buy_Invoice.CreateDate,
               Buy_Invoice.Lister,
               Buy_Invoice.ModifyDate,
               Buy_Invoice.Assessor,
               Buy_Invoice.AssessDate,
               Buy_Invoice.FmDocMark,
               Buy_Invoice.FmDocCode,
               Buy_Invoice.Operator,
               Buy_Invoice.FirstAmt,
               Buy_Invoice.LastAmt,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Buy_Invoice ON Buy_Invoice.Groupid = App_Workgroup.id
                 RIGHT JOIN Buy_InvoiceItem ON Buy_Invoice.id = Buy_InvoiceItem.Pid
                 LEFT JOIN Mat_Goods ON Buy_InvoiceItem.Goodsid = Mat_Goods.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyInvoiceitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Buy_Invoice.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Buy_Invoice.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Buy_Invoice.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Buy_Invoice.billtype like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Buy_Invoice.billtitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Buy_Invoice.groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.invocode != null and SearchPojo.invocode != ''">
            and Buy_Invoice.invocode like concat('%',
                #{SearchPojo.invocode}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Buy_Invoice.summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Buy_Invoice.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Buy_Invoice.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Buy_Invoice.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Buy_Invoice.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Buy_Invoice.assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Buy_Invoice.assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Buy_Invoice.statecode like concat('%',
                #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Buy_Invoice.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Buy_Invoice.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Buy_Invoice.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Buy_Invoice.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Buy_Invoice.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Buy_Invoice.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Buy_Invoice.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Buy_Invoice.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Buy_Invoice.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Buy_Invoice.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.finishuid != null and SearchPojo.finishuid != ''">
            and Buy_InvoiceItem.FinishUid like concat('%',
                #{SearchPojo.finishuid}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Buy_Invoice.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Buy_Invoice.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Buy_Invoice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Buy_Invoice.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.invocode != null and SearchPojo.invocode != ''">
                or Buy_Invoice.InvoCode like concat('%', #{SearchPojo.invocode}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Buy_Invoice.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Buy_Invoice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Buy_Invoice.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Buy_Invoice.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Buy_Invoice.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Buy_Invoice.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Buy_Invoice.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Buy_Invoice.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Buy_Invoice.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Buy_Invoice.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Buy_Invoice.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Buy_Invoice.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Buy_Invoice.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Buy_Invoice.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Buy_Invoice.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Buy_Invoice.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Buy_Invoice.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Buy_Invoice.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.finishuid != null and SearchPojo.finishuid != ''">
                or Buy_InvoiceItem.FinishUid like concat('%', #{SearchPojo.finishuid}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyInvoicePojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Buy_Invoice.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Buy_Invoice.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Buy_Invoice.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Buy_Invoice.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Buy_Invoice.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Buy_Invoice.Groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.invocode != null and SearchPojo.invocode != ''">
            and Buy_Invoice.InvoCode like concat('%',
                #{SearchPojo.invocode}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Buy_Invoice.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Buy_Invoice.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Buy_Invoice.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Buy_Invoice.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Buy_Invoice.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Buy_Invoice.Assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Buy_Invoice.Assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Buy_Invoice.StateCode like concat('%',
                #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Buy_Invoice.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Buy_Invoice.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Buy_Invoice.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Buy_Invoice.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Buy_Invoice.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Buy_Invoice.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Buy_Invoice.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Buy_Invoice.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Buy_Invoice.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Buy_Invoice.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Buy_Invoice.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Buy_Invoice.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Buy_Invoice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Buy_Invoice.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.invocode != null and SearchPojo.invocode != ''">
                or Buy_Invoice.InvoCode like concat('%', #{SearchPojo.invocode}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Buy_Invoice.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Buy_Invoice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Buy_Invoice.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Buy_Invoice.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Buy_Invoice.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Buy_Invoice.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Buy_Invoice.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Buy_Invoice.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Buy_Invoice.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Buy_Invoice.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Buy_Invoice.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Buy_Invoice.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Buy_Invoice.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Buy_Invoice.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Buy_Invoice.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Buy_Invoice.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Buy_Invoice.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Buy_Invoice.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Buy_Invoice(id, RefNo, BillType, BillTitle, BillDate,Projectid, ProjCode, ProjName,  Groupid, Taxrate, TaxAmount, TaxTotal, Amount,
                                InvoDate, InvoCode, AimDate, Paid, Summary, CreateBy, CreateByid, CreateDate, Lister,
                                Listerid, ModifyDate, Assessor, Assessorid, AssessDate, StateCode, StateDate, Closed,
                                DisannulMark, FmDocMark, FmDocCode, Operator, FirstAmt, LastAmt, Custom1, Custom2, Custom3, Custom4,
                                Custom5,
                                Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{projectid}, #{projcode}, #{projname},
                #{groupid}, #{taxrate}, #{taxamount},
                #{taxtotal}, #{amount}, #{invodate}, #{invocode}, #{aimdate}, #{paid}, #{summary}, #{createby},
                #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid},
                #{assessdate}, #{statecode}, #{statedate}, #{closed}, #{disannulmark}, #{fmdocmark}, #{fmdoccode},
                #{operator},#{firstamt},#{lastamt},
                #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8},
                #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_Invoice
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="taxrate != null">
                Taxrate =#{taxrate},
            </if>
            <if test="taxamount != null">
                TaxAmount =#{taxamount},
            </if>
            <if test="taxtotal != null">
                TaxTotal =#{taxtotal},
            </if>
            <if test="amount != null">
                Amount =#{amount},
            </if>
            <if test="invodate != null">
                InvoDate =#{invodate},
            </if>
            <if test="invocode != null">
                InvoCode =#{invocode},
            </if>
            <if test="aimdate != null">
                AimDate =#{aimdate},
            </if>
            <if test="paid != null">
                Paid =#{paid},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="statecode != null">
                StateCode =#{statecode},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="closed != null">
                Closed =#{closed},
            </if>
            <if test="disannulmark != null">
                DisannulMark =#{disannulmark},
            </if>
            <if test="fmdocmark != null">
                FmDocMark =#{fmdocmark},
            </if>
            <if test="fmdoccode != null">
                FmDocCode =#{fmdoccode},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="firstamt != null">
                FirstAmt =#{firstamt},
            </if>
            <if test="lastamt != null">
                LastAmt =#{lastamt},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_Invoice
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Buy_Invoice
        SET Assessor   = #{assessor},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BuyInvoicePojo">
        select id
        from Buy_InvoiceItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateFiniInvoFinish">
        update Buy_FinishingItem
        SET InvoQty =COALESCE((SELECT SUM(Buy_InvoiceItem.quantity)
                               FROM Buy_InvoiceItem
                                        LEFT OUTER JOIN Buy_Invoice
                                                        ON Buy_InvoiceItem.pid = Buy_Invoice.id
                               where Buy_InvoiceItem.FinishUid = #{refno}
                                 and Buy_InvoiceItem.FinishItemid = #{key}
                                 and Buy_InvoiceItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <update id="updateOrderItemInvoFinish">
        update Buy_OrderItem
        SET InvoQty =COALESCE((SELECT SUM(Buy_InvoiceItem.quantity)
                               FROM Buy_InvoiceItem
                               where Buy_InvoiceItem.OrderUid = #{refno}
                                 and Buy_InvoiceItem.OrderItemid = #{key}
                                 and Buy_InvoiceItem.Tenantid = #{tid}), 0),
            InvoAmt =COALESCE((SELECT SUM(Buy_InvoiceItem.TaxAmount)
                               FROM Buy_InvoiceItem
                               where Buy_InvoiceItem.OrderUid = #{refno}
                                 and Buy_InvoiceItem.OrderItemid = #{key}
                                 and Buy_InvoiceItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateFiniInvoCount">
        update Buy_Finishing
        SET InvoCount =COALESCE((SELECT COUNT(0)
                                 FROM Buy_FinishingItem
                                 where Buy_FinishingItem.Pid = (SELECT Pid FROM Buy_FinishingItem where id = #{key})
                                   and Buy_FinishingItem.Tenantid = #{tid}
                                   and (Buy_FinishingItem.InvoClosed = 1
                                     or Buy_FinishingItem.InvoQty >= Buy_FinishingItem.Quantity)), 0)
        where id = (SELECT Pid FROM Buy_FinishingItem where id = #{key})
          and Tenantid = #{tid}
    </update>


    <update id="updateOrderInvoCount">
        update Buy_Order
        SET InvoCount =COALESCE((SELECT COUNT(0)
                                 FROM Buy_OrderItem
                                 where Buy_OrderItem.Pid = (SELECT Pid FROM Buy_OrderItem where id = #{key})
                                   and Buy_OrderItem.Tenantid = #{tid}
                                   and (Buy_OrderItem.InvoClosed = 1
                                     or Buy_OrderItem.InvoQty >= Buy_OrderItem.Quantity)), 0),
            InvoAmt   =COALESCE((SELECT SUM(Buy_InvoiceItem.TaxAmount)
                                    FROM Buy_InvoiceItem
                                    where Buy_InvoiceItem.OrderUid = #{refno}
                                    and Buy_InvoiceItem.Tenantid = #{tid}), 0)
        where id = (SELECT Pid FROM Buy_OrderItem where id = #{key})
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateDeduInvoFinish">
        update Buy_DeductionItem
        SET InvoQty =COALESCE((SELECT SUM(Buy_InvoiceItem.quantity)
                               FROM Buy_InvoiceItem
                                        LEFT OUTER JOIN Buy_Invoice
                                                        ON Buy_InvoiceItem.pid = Buy_Invoice.id
                               where Buy_InvoiceItem.FinishUid = #{refno}
                                 and Buy_InvoiceItem.FinishItemid = #{key}
                                 and Buy_InvoiceItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <update id="updateScCompleteItemInvoFinish">
        update Wk_ScCompleteItem
        SET InvoQty =COALESCE((SELECT SUM(Buy_InvoiceItem.quantity)
                               FROM Buy_InvoiceItem
                                        LEFT OUTER JOIN Buy_Invoice
                                                        ON Buy_InvoiceItem.pid = Buy_Invoice.id
                               where Buy_InvoiceItem.FinishItemid = #{key}
                                 and Buy_InvoiceItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateDeduInvoCount">
        update Buy_Deduction
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Buy_DeductionItem
                                   where Buy_DeductionItem.Pid = (SELECT Pid FROM Buy_DeductionItem where id = #{key})
                                     and Buy_DeductionItem.Tenantid = #{tid}
                                     and (Buy_DeductionItem.InvoClosed = 1
                                       or Buy_DeductionItem.InvoQty >= Buy_DeductionItem.Quantity)), 0)
        where id = (SELECT Pid FROM Buy_DeductionItem where id = #{key})
          and Tenantid = #{tid}
    </update>


    <update id="updateScCompleteInvoCount">
        update Wk_ScComplete
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_ScCompleteItem
                                   where Wk_ScCompleteItem.Pid = (SELECT Pid FROM Wk_ScCompleteItem where id = #{key})
                                     and Wk_ScCompleteItem.Tenantid = #{tid}
                                     and (Wk_ScCompleteItem.InvoClosed = 1
                                       or Wk_ScCompleteItem.InvoQty >= Wk_ScCompleteItem.Quantity)), 0)
        where id = (SELECT Pid FROM Wk_ScCompleteItem where id = #{key})
          and Tenantid = #{tid}
    </update>

    <!--  查询采购开票是否被引用（可以删除的情况：比如Invoid查到的4个付款主表:OrgUid为''的和OrgUid不为''的条数相等时，表示每个单子都有对应的红冲单了）
    目前只校验采购付款,如果新增校验项需要改此接口的ServiceIml层-->
    <select id="getCiteBillName" resultType="string">
        SELECT IF(COUNT(IF(Buy_Voucher.OrgUid != '', 1, NULL)) <![CDATA[!=]]> COUNT(IF(Buy_Voucher.OrgUid = '', 1, NULL)) > 0,
                       '采购付款',
                       NULL
               ) as result
        FROM Buy_VoucherItem
                 JOIN Buy_Voucher ON Buy_VoucherItem.Pid = Buy_Voucher.id
        WHERE Buy_VoucherItem.Invoid = #{key}
          AND Buy_VoucherItem.Tenantid = #{tid}
    </select>

    <select id="getScCompleteItemEntity" resultType="java.util.Map">
        select InvoQty,Quantity
        from Wk_ScCompleteItem
        where id = #{scCompleteItemid}
          and Tenantid = #{tid}
    </select>

    <select id="getOrerId" resultType="java.lang.String">
        select Pid
        from Buy_OrderItem
        where id = #{orderitemid}
          and Tenantid = #{tenantid}
    </select>

    <update id="updateOrderItemAvgInvoAmt">
        UPDATE Buy_OrderItem
        SET AvgInvoAmt = (SELECT tmp.InvoAmt / tmp.ItemCount
                          FROM (SELECT (SELECT InvoAmt FROM Buy_Order WHERE id = #{orerId}) AS InvoAmt,
                                       COUNT(*)                                                 AS ItemCount
                                FROM Buy_OrderItem
                                WHERE Pid = #{orerId}) AS tmp)
        WHERE Buy_OrderItem.Pid = #{orerId}
          and Tenantid = #{tenantid}
    </update>
</mapper>

