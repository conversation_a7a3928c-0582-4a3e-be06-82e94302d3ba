<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatAccessMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatAccessPojo">
        SELECT Mat_Access.id,
               Mat_Access.RefNo,
               Mat_Access.BillDate,
               Mat_Access.Projectid,
               Mat_Access.ProjName,
               Mat_Access.ProjCode,
               Mat_Access.TypeCode,
               Mat_Access.BillType,
               Mat_Access.BillTitle,
               Mat_Access.Direction,
               Mat_Access.Groupid,
               Mat_Access.Storeid,
               Mat_Access.StoreCode,
               Mat_Access.StoreName,
               Mat_Access.Operator,
               Mat_Access.Summary,
               Mat_Access.ReturnUid,
               Mat_Access.OrgUid,
               Mat_Access.PlusInfo,
               Mat_Access.CreateBy,
               Mat_Access.CreateByid,
               Mat_Access.CreateDate,
               Mat_Access.Lister,
               Mat_Access.Listerid,
               Mat_Access.ModifyDate,
               Mat_Access.ItemCount,
               Mat_Access.PrintCount,
               Mat_Access.BillTaxAmount,
               Mat_Access.BillTaxTotal,
               Mat_Access.BillAmount,
               Mat_Access.FmDocMark,
               Mat_Access.FmDocCode,
               Mat_Access.Custom1,
               Mat_Access.Custom2,
               Mat_Access.Custom3,
               Mat_Access.Custom4,
               Mat_Access.Custom5,
               Mat_Access.Custom6,
               Mat_Access.Custom7,
               Mat_Access.Custom8,
               Mat_Access.Custom9,
               Mat_Access.Custom10,
               Mat_Access.Tenantid,
               Mat_Access.TenantName,
               Mat_Access.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Mat_Access ON App_Workgroup.id = Mat_Access.Groupid
        where Mat_Access.id = #{key}
          and Mat_Access.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Mat_Access.id,
               Mat_Access.RefNo,
               Mat_Access.BillDate,
               Mat_Access.Projectid,
               Mat_Access.ProjName,
               Mat_Access.ProjCode,
               Mat_Access.TypeCode,
               Mat_Access.BillType,
               Mat_Access.BillTitle,
               Mat_Access.Direction,
               Mat_Access.Groupid,
               Mat_Access.Storeid,
               Mat_Access.StoreCode,
               Mat_Access.StoreName,
               Mat_Access.Operator,
               Mat_Access.Summary,
               Mat_Access.ReturnUid,
               Mat_Access.OrgUid,
               Mat_Access.PlusInfo,
               Mat_Access.CreateBy,
               Mat_Access.CreateByid,
               Mat_Access.CreateDate,
               Mat_Access.Lister,
               Mat_Access.Listerid,
               Mat_Access.ModifyDate,
               Mat_Access.ItemCount,
               Mat_Access.PrintCount,
               Mat_Access.BillTaxAmount,
               Mat_Access.BillTaxTotal,
               Mat_Access.BillAmount,
               Mat_Access.FmDocMark,
               Mat_Access.FmDocCode,
               Mat_Access.Custom1,
               Mat_Access.Custom2,
               Mat_Access.Custom3,
               Mat_Access.Custom4,
               Mat_Access.Custom5,
               Mat_Access.Custom6,
               Mat_Access.Custom7,
               Mat_Access.Custom8,
               Mat_Access.Custom9,
               Mat_Access.Custom10,
               Mat_Access.Tenantid,
               Mat_Access.TenantName,
               Mat_Access.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Mat_Access ON App_Workgroup.id = Mat_Access.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT Mat_AccessItem.id,
               Mat_AccessItem.Pid,
               Mat_AccessItem.Goodsid,
               Mat_AccessItem.Quantity,
               Mat_AccessItem.Price,
               Mat_AccessItem.Amount,
               Mat_AccessItem.TaxPrice,
               Mat_AccessItem.TaxAmount,
               Mat_AccessItem.ItemTaxrate,
               Mat_AccessItem.TaxTotal,
               Mat_AccessItem.Remark,
               Mat_AccessItem.CiteUid,
               Mat_AccessItem.CiteItemid,
               Mat_AccessItem.StateCode,
               Mat_AccessItem.StateDate,
               Mat_AccessItem.RowNum,
               Mat_AccessItem.Location,
               Mat_AccessItem.BatchNo,
               Mat_AccessItem.PackSn,
               Mat_AccessItem.ExpiDate,
               Mat_AccessItem.Customer,
               Mat_AccessItem.CustPO,
               Mat_AccessItem.MachUid,
               Mat_AccessItem.MachItemid,
               Mat_AccessItem.MachGroupid,
               Mat_AccessItem.MainPlanUid,
               Mat_AccessItem.MainPlanItemid,
               Mat_AccessItem.MrpUid,
               Mat_AccessItem.MrpItemid,
               Mat_AccessItem.Inveid,
               Mat_AccessItem.Skuid,
               Mat_AccessItem.AttributeJson,
               Mat_AccessItem.WkQtyid,
               Mat_AccessItem.OrderUid,
               Mat_AccessItem.OrderItemid,
               Mat_AccessItem.WorkUid,
               Mat_AccessItem.WorkItemid,
               Mat_AccessItem.SubcUid,
               Mat_AccessItem.SubcItemid,
               Mat_AccessItem.CustUid,
               Mat_AccessItem.CuistItemid,
               Mat_AccessItem.Custom1,
               Mat_AccessItem.Custom2,
               Mat_AccessItem.Custom3,
               Mat_AccessItem.Custom4,
               Mat_AccessItem.Custom5,
               Mat_AccessItem.Custom6,
               Mat_AccessItem.Custom7,
               Mat_AccessItem.Custom8,
               Mat_AccessItem.Custom9,
               Mat_AccessItem.Custom10,
               Mat_AccessItem.Tenantid,
               Mat_AccessItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10,
               Mat_Access.RefNo,
               Mat_Access.BillDate,
               Mat_Access.Projectid,
               Mat_Access.ProjName,
               Mat_Access.ProjCode,
               Mat_Access.TypeCode,
               Mat_Access.BillType,
               Mat_Access.BillTitle,
               Mat_Access.Direction,
               Mat_Access.ItemCount,
               Mat_Access.PrintCount,
               Mat_Access.BillTaxAmount,
               Mat_Access.BillTaxTotal,
               Mat_Access.BillAmount,
               Mat_Access.FmDocMark,
               Mat_Access.FmDocCode,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Mat_Access
                 RIGHT JOIN Mat_AccessItem ON Mat_AccessItem.Pid = Mat_Access.id
                 RIGHT JOIN Mat_Goods ON Mat_Goods.id = Mat_AccessItem.Goodsid
                 LEFT JOIN App_Workgroup ON Mat_Access.Groupid = App_Workgroup.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatAccessitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Mat_Access.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Mat_Access.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Mat_Access.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.typecode != null and SearchPojo.typecode != ''">
            and Mat_Access.typecode like concat('%',
                #{SearchPojo.typecode}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Mat_Access.billtype like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Mat_Access.billtitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.direction != null and SearchPojo.direction != ''">
            and Mat_Access.direction like concat('%',
                #{SearchPojo.direction}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Mat_Access.groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.storeid != null and SearchPojo.storeid != ''">
            and Mat_Access.storeid like concat('%',
                #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.storecode != null and SearchPojo.storecode != ''">
            and Mat_Access.storecode like concat('%',
                #{SearchPojo.storecode}, '%')
        </if>
        <if test="SearchPojo.storename != null and SearchPojo.storename != ''">
            and Mat_Access.storename like concat('%',
                #{SearchPojo.storename}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Mat_Access.operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Mat_Access.summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            and Mat_Access.returnuid like concat('%',
                #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            and Mat_Access.orguid like concat('%',
                #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.plusinfo != null and SearchPojo.plusinfo != ''">
            and Mat_Access.plusinfo like concat('%',
                #{SearchPojo.plusinfo}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Mat_Access.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Mat_Access.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Mat_Access.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Mat_Access.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_Access.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_Access.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_Access.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_Access.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_Access.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_Access.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_Access.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_Access.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_Access.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_Access.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.id != null and SearchPojo.id != ''">
            and Mat_AccessItem.id=
                #{SearchPojo.id}
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_AccessItem.goodsid= #{SearchPojo.goodsid}
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Mat_AccessItem.location like concat('%',
                #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Mat_AccessItem.batchno like concat('%',
                #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.packsn != null">
            and Mat_AccessItem.packsn=
                #{SearchPojo.packsn}
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Mat_AccessItem.citeuid=#{SearchPojo.citeuid}
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Mat_AccessItem.machitemid=
                #{SearchPojo.machitemid}
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Mat_Access.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.typecode != null and SearchPojo.typecode != ''">
                or Mat_Access.TypeCode like concat('%', #{SearchPojo.typecode}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Mat_Access.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Mat_Access.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.direction != null and SearchPojo.direction != ''">
                or Mat_Access.Direction like concat('%', #{SearchPojo.direction}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Mat_Access.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.storeid != null and SearchPojo.storeid != ''">
                or Mat_Access.Storeid like concat('%', #{SearchPojo.storeid}, '%')
            </if>
            <if test="SearchPojo.storecode != null and SearchPojo.storecode != ''">
                or Mat_Access.StoreCode like concat('%', #{SearchPojo.storecode}, '%')
            </if>
            <if test="SearchPojo.storename != null and SearchPojo.storename != ''">
                or Mat_Access.StoreName like concat('%', #{SearchPojo.storename}, '%')
            </if>
            <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
                or Mat_Access.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Mat_Access.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
                or Mat_Access.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
            </if>
            <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
                or Mat_Access.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
            </if>
            <if test="SearchPojo.plusinfo != null and SearchPojo.plusinfo != ''">
                or Mat_Access.PlusInfo like concat('%', #{SearchPojo.plusinfo}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Mat_Access.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Mat_Access.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Mat_Access.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Mat_Access.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_Access.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_Access.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_Access.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_Access.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_Access.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_Access.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_Access.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_Access.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_Access.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_Access.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Mat_AccessItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Mat_AccessItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.packsn != null">
                or Mat_AccessItem.packsn= #{SearchPojo.packsn}
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Mat_AccessItem.CiteUid = #{SearchPojo.citeuid}
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Mat_AccessItem.machitemid=#{SearchPojo.machitemid}
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatAccessPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Mat_Access.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Mat_Access.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Mat_Access.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.typecode != null and SearchPojo.typecode != ''">
            and Mat_Access.TypeCode like concat('%',
                #{SearchPojo.typecode}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Mat_Access.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Mat_Access.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.direction != null and SearchPojo.direction != ''">
            and Mat_Access.Direction like concat('%',
                #{SearchPojo.direction}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Mat_Access.Groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.storeid != null and SearchPojo.storeid != ''">
            and Mat_Access.Storeid like concat('%',
                #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.storecode != null and SearchPojo.storecode != ''">
            and Mat_Access.StoreCode like concat('%',
                #{SearchPojo.storecode}, '%')
        </if>
        <if test="SearchPojo.storename != null and SearchPojo.storename != ''">
            and Mat_Access.StoreName like concat('%',
                #{SearchPojo.storename}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Mat_Access.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Mat_Access.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            and Mat_Access.ReturnUid like concat('%',
                #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            and Mat_Access.OrgUid like concat('%',
                #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.plusinfo != null and SearchPojo.plusinfo != ''">
            and Mat_Access.PlusInfo like concat('%',
                #{SearchPojo.plusinfo}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Mat_Access.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Mat_Access.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Mat_Access.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Mat_Access.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_Access.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_Access.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_Access.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_Access.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_Access.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_Access.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_Access.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_Access.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_Access.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_Access.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Mat_Access.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.typecode != null and SearchPojo.typecode != ''">
                or Mat_Access.TypeCode like concat('%', #{SearchPojo.typecode}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Mat_Access.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Mat_Access.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.direction != null and SearchPojo.direction != ''">
                or Mat_Access.Direction like concat('%', #{SearchPojo.direction}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Mat_Access.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.storeid != null and SearchPojo.storeid != ''">
                or Mat_Access.Storeid like concat('%', #{SearchPojo.storeid}, '%')
            </if>
            <if test="SearchPojo.storecode != null and SearchPojo.storecode != ''">
                or Mat_Access.StoreCode like concat('%', #{SearchPojo.storecode}, '%')
            </if>
            <if test="SearchPojo.storename != null and SearchPojo.storename != ''">
                or Mat_Access.StoreName like concat('%', #{SearchPojo.storename}, '%')
            </if>
            <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
                or Mat_Access.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Mat_Access.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
                or Mat_Access.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
            </if>
            <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
                or Mat_Access.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
            </if>
            <if test="SearchPojo.plusinfo != null and SearchPojo.plusinfo != ''">
                or Mat_Access.PlusInfo like concat('%', #{SearchPojo.plusinfo}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Mat_Access.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Mat_Access.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Mat_Access.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Mat_Access.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_Access.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_Access.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_Access.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_Access.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_Access.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_Access.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_Access.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_Access.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_Access.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_Access.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Access(id, RefNo, BillDate, Projectid, ProjCode, ProjName, TypeCode, BillType, BillTitle,
                               Direction, Groupid, Storeid,
                               StoreCode, StoreName, Operator, Summary, ReturnUid, OrgUid, PlusInfo, BillTaxAmount,
                               BillTaxTotal, BillAmount, FmDocMark,
                               FmDocCode, CreateBy,
                               CreateByid, CreateDate, Lister, Listerid, ModifyDate, ItemCount, PrintCount, Custom1,
                               Custom2, Custom3, Custom4,
                               Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{typecode}, #{billtype},
                #{billtitle}, #{direction}, #{groupid},
                #{storeid}, #{storecode}, #{storename}, #{operator}, #{summary}, #{returnuid}, #{orguid}, #{plusinfo},
                #{billtaxamount}, #{billtaxtotal}, #{billamount},
                #{fmdocmark}, #{fmdoccode},
                #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{itemcount},
                #{printcount}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Access
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="typecode != null">
                TypeCode =#{typecode},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="direction != null">
                Direction =#{direction},
            </if>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="storeid != null">
                Storeid =#{storeid},
            </if>
            <if test="storecode != null">
                StoreCode =#{storecode},
            </if>
            <if test="storename != null">
                StoreName =#{storename},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="returnuid != null">
                ReturnUid =#{returnuid},
            </if>
            <if test="orguid != null">
                OrgUid =#{orguid},
            </if>
            <if test="plusinfo != null">
                PlusInfo =#{plusinfo},
            </if>

            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>


    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Access
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.MatAccessPojo">
        select id
        from Mat_AccessItem
        where Pid = #{id}
          and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

    <!--    刷新领料单成本-->
    <update id="updateRequMatCostAmt">
        update Mat_RequisitionItem
        SET Mat_RequisitionItem.FinishCost=(select COALESCE(sum(CASE
                                                                    WHEN Mat_Access.BillType IN ('领料出库', '退料红冲')
                                                                        THEN Mat_AccessItem.Amount
                                                                    ELSE 0 - Mat_AccessItem.Amount END), 0)
                                            from Mat_AccessItem
                                                     left join Mat_Access on Mat_Access.id = Mat_AccessItem.Pid
                                            where Mat_AccessItem.CiteItemid = #{key}
                                              and Mat_AccessItem.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新销售订单成本-->
    <update id="updateMachMatCostAmt">
        update Bus_MachiningItem
        SET Bus_MachiningItem.MatCostAmt=(select COALESCE(sum(IF(Mat_Access.BillType IN ('领料出库', '退料红冲'),
                                                                 Mat_AccessItem.Amount, 0 - Mat_AccessItem.Amount)), 0)
                                          from Mat_AccessItem
                                                   left join Mat_Access on Mat_Access.id = Mat_AccessItem.Pid
                                          where Mat_AccessItem.MachItemid = #{key}
                                            and Mat_Access.BillType IN ('领料出库', '领料红冲', '退料红冲', '退料入库')
                                            and Mat_AccessItem.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--刷新打印次数-->
    <update id="updatePrintcount">
        update Mat_Access
        SET PrintCount = #{printcount}
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <select id="getSumPageListByGoods" resultType="java.util.Map">
        SELECT Mat_Goods.GoodsUid                                                                     as goodsuid,
               Mat_Goods.GoodsName                                                                    as goodsname,
               Mat_Goods.GoodsSpec                                                                    as goodsspec,
               Mat_Goods.GoodsUnit                                                                    as goodsunit,
               count(*)                                                                               as count,
               SUM(CASE WHEN Mat_Access.Direction = '出库单' THEN Mat_AccessItem.Quantity ELSE 0 END) as outsumqty,
               SUM(CASE WHEN Mat_Access.Direction = '入库单' THEN Mat_AccessItem.Quantity ELSE 0 END) as insumqty
        FROM Mat_Goods
                 RIGHT JOIN Mat_AccessItem ON Mat_Goods.id = Mat_AccessItem.Goodsid
                 LEFT JOIN Mat_Access ON Mat_AccessItem.Pid = Mat_Access.id
        WHERE Mat_AccessItem.Tenantid = #{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Access.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
            </if>
            <if test="SearchType == 1">
                <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
                    <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
                </trim>
            </if>
        </if>
        GROUP BY Mat_Goods.GoodsUid,
                 Mat_Goods.GoodsName,
                 Mat_Goods.GoodsSpec,
                 Mat_Goods.GoodsUnit
        order by outsumqty desc
    </select>

    <select id="getBuyFinishing" resultType="java.util.Map">
        select Buy_Finishing.BillAmount,
        Buy_Finishing.BillTaxAmount,
        Buy_Finishing.BillTaxTotal,
        Buy_Finishing.RefNo,
        Buy_Finishing.Groupid,
        App_Workgroup.GroupName,
        App_Workgroup.GroupUid
        from Buy_Finishing Left join App_Workgroup on Buy_Finishing.Groupid = App_Workgroup.id
        where Buy_Finishing.id = #{finishid} and Buy_Finishing.Tenantid = #{tid}
    </select>

    <select id="getBuyFinishingItemList" resultType="java.util.Map">
        select * from Buy_FinishingItem
        where Pid = #{finishid} and Tenantid = #{tid}
    </select>

    <select id="getGoods" resultType="java.util.Map">
        select GoodsUid,
        GoodsName,
        GoodsSpec,
        GoodsUnit
        from Mat_Goods
        where id = #{goodsid}
        and Tenantid = #{tid}
    </select>

    <select id="getRequisitionItemList" resultType="java.util.Map">
        select * from Mat_RequisitionItem
        where Pid = #{requisitionid} and Tenantid = #{tid}
    </select>

    <select id="getRequisition" resultType="java.util.Map">
        select * from Mat_Requisition
        where id = #{requisitionid} and Tenantid = #{tid}
    </select>
</mapper>

