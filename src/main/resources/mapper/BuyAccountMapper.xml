<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyAccountMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyAccountPojo">
        SELECT Buy_Account.id,
               Buy_Account.RefNo,
               Buy_Account.BillType,
               Buy_Account.BillDate,
               Buy_Account.Projectid,
               Buy_Account.ProjName,
               Buy_Account.ProjCode,
               Buy_Account.BillTitle,
               Buy_Account.Groupid,
               Buy_Account.CarryYear,
               Buy_Account.CarryMonth,
               Buy_Account.StartDate,
               Buy_Account.EndDate,
               Buy_Account.Operator,
               Buy_Account.Operatorid,
               Buy_Account.RowNum,
               Buy_Account.Summary,
               Buy_Account.CreateBy,
               Buy_Account.CreateByid,
               Buy_Account.CreateDate,
               Buy_Account.Lister,
               Buy_Account.Listerid,
               Buy_Account.ModifyDate,
               Buy_Account.BillOpenAmount,
               Buy_Account.BillInAmount,
               Buy_Account.BillOutAmount,
               Buy_Account.BillCloseAmount,
               Buy_Account.InvoOpenAmount,
               Buy_Account.InvoInAmount,
               Buy_Account.InvoOutAmount,
               Buy_Account.InvoCloseAmount,
               Buy_Account.ArapOpenAmount,
               Buy_Account.ArapInAmount,
               Buy_Account.ArapOutAmount,
               Buy_Account.ArapCloseAmount,
               Buy_Account.PrintCount,
               Buy_Account.Custom1,
               Buy_Account.Custom2,
               Buy_Account.Custom3,
               Buy_Account.Custom4,
               Buy_Account.Custom5,
               Buy_Account.Custom6,
               Buy_Account.Custom7,
               Buy_Account.Custom8,
               Buy_Account.Custom9,
               Buy_Account.Custom10,
               Buy_Account.Tenantid,
               Buy_Account.TenantName,
               Buy_Account.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Buy_Account ON App_Workgroup.id = Buy_Account.Groupid
        where Buy_Account.id = #{key}
          and Buy_Account.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Buy_Account.id,
               Buy_Account.RefNo,
               Buy_Account.BillType,
               Buy_Account.BillDate,
               Buy_Account.Projectid,
               Buy_Account.ProjName,
               Buy_Account.ProjCode,
               Buy_Account.BillTitle,
               Buy_Account.Groupid,
               Buy_Account.CarryYear,
               Buy_Account.CarryMonth,
               Buy_Account.StartDate,
               Buy_Account.EndDate,
               Buy_Account.Operator,
               Buy_Account.Operatorid,
               Buy_Account.RowNum,
               Buy_Account.Summary,
               Buy_Account.CreateBy,
               Buy_Account.CreateByid,
               Buy_Account.CreateDate,
               Buy_Account.Lister,
               Buy_Account.Listerid,
               Buy_Account.ModifyDate,
               Buy_Account.BillOpenAmount,
               Buy_Account.BillInAmount,
               Buy_Account.BillOutAmount,
               Buy_Account.BillCloseAmount,
               Buy_Account.InvoOpenAmount,
               Buy_Account.InvoInAmount,
               Buy_Account.InvoOutAmount,
               Buy_Account.InvoCloseAmount,
               Buy_Account.ArapOpenAmount,
               Buy_Account.ArapInAmount,
               Buy_Account.ArapOutAmount,
               Buy_Account.ArapCloseAmount,
               Buy_Account.PrintCount,
               Buy_Account.Custom1,
               Buy_Account.Custom2,
               Buy_Account.Custom3,
               Buy_Account.Custom4,
               Buy_Account.Custom5,
               Buy_Account.Custom6,
               Buy_Account.Custom7,
               Buy_Account.Custom8,
               Buy_Account.Custom9,
               Buy_Account.Custom10,
               Buy_Account.Tenantid,
               Buy_Account.TenantName,
               Buy_Account.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.Seller
        FROM App_Workgroup
                 RIGHT JOIN Buy_Account ON App_Workgroup.id = Buy_Account.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Buy_AccountItem.id,
            Buy_AccountItem.Pid,
            Buy_AccountItem.Direction,
            Buy_AccountItem.ModuleCode,
            Buy_AccountItem.BillType,
            Buy_AccountItem.BillDate,
            Buy_AccountItem.BillTitle,
            Buy_AccountItem.BillUid,
            Buy_AccountItem.Billid,
            Buy_AccountItem.OpenAmount,
            Buy_AccountItem.InAmount,
            Buy_AccountItem.OutAmount,
            Buy_AccountItem.CloseAmount,
            Buy_AccountItem.RowNum,
            Buy_AccountItem.Remark,
            Buy_AccountItem.Custom1,
            Buy_AccountItem.Custom2,
            Buy_AccountItem.Custom3,
            Buy_AccountItem.Custom4,
            Buy_AccountItem.Custom5,
            Buy_AccountItem.Custom6,
            Buy_AccountItem.Custom7,
            Buy_AccountItem.Custom8,
            Buy_AccountItem.Custom9,
            Buy_AccountItem.Custom10,
            Buy_AccountItem.Tenantid,
            Buy_AccountItem.Revision,
            Buy_Account.RefNo,
            Buy_Account.BillType,
            Buy_Account.BillDate,
            Buy_Account.BillTitle,
            Buy_Account.Projectid,
            Buy_Account.ProjName,
            Buy_Account.ProjCode,
            Buy_Account.CarryYear,
            Buy_Account.CarryMonth,
            Buy_Account.StartDate,
            Buy_Account.EndDate,
            Buy_Account.Operator,
            Buy_Account.Summary
        FROM
            App_Workgroup
                RIGHT JOIN Buy_Account ON App_Workgroup.id = Buy_Account.Groupid
                RIGHT JOIN Buy_AccountItem ON Buy_Account.id = Buy_AccountItem.Pid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyAccountitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Buy_Account.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_Account.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Buy_Account.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Buy_Account.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Buy_Account.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Buy_Account.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Buy_Account.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Buy_Account.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Buy_Account.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Buy_Account.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Buy_Account.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Buy_Account.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Buy_Account.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Buy_Account.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Buy_Account.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Buy_Account.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Buy_Account.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Buy_Account.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Buy_Account.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Buy_Account.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Buy_Account.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Buy_Account.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Buy_Account.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Buy_Account.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Buy_Account.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Buy_Account.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Buy_Account.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Buy_Account.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Buy_Account.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Buy_Account.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Buy_Account.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Buy_Account.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Buy_Account.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Buy_Account.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Buy_Account.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Buy_Account.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Buy_Account.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Buy_Account.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Buy_Account.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Buy_Account.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Buy_Account.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Buy_Account.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Buy_Account.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Buy_Account.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Buy_Account.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Buy_Account.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyAccountPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Buy_Account.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_Account.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Buy_Account.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Buy_Account.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Buy_Account.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Buy_Account.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Buy_Account.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Buy_Account.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Buy_Account.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Buy_Account.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Buy_Account.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Buy_Account.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Buy_Account.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Buy_Account.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Buy_Account.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Buy_Account.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Buy_Account.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Buy_Account.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Buy_Account.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Buy_Account.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Buy_Account.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Buy_Account.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Buy_Account.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Buy_Account.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Buy_Account.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Buy_Account.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Buy_Account.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Buy_Account.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Buy_Account.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Buy_Account.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Buy_Account.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Buy_Account.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Buy_Account.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Buy_Account.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Buy_Account.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Buy_Account.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Buy_Account.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Buy_Account.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Buy_Account.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Buy_Account.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Buy_Account.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Buy_Account.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Buy_Account.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Buy_Account.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Buy_Account.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Buy_Account.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Buy_Account(id, RefNo, BillType, BillDate, Projectid, ProjCode, ProjName, BillTitle, Groupid,
                                CarryYear, CarryMonth, StartDate,
                                EndDate, Operator, Operatorid, RowNum, Summary, CreateBy, CreateByid, CreateDate,
                                Lister,
                                Listerid, ModifyDate, BillOpenAmount, BillInAmount, BillOutAmount, BillCloseAmount,
                                InvoOpenAmount, InvoInAmount, InvoOutAmount, InvoCloseAmount,
                                ArapOpenAmount, ArapInAmount, ArapOutAmount, ArapCloseAmount,
                                PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{billtitle},
                #{groupid}, #{carryyear}, #{carrymonth},
                #{startdate}, #{enddate}, #{operator}, #{operatorid}, #{rownum}, #{summary}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{billopenamount}, #{billinamount},
                #{billoutamount}, #{billcloseamount}, #{invoopenamount}, #{invoinamount}, #{invooutamount},
                #{invocloseamount}, #{arapopenamount}, #{arapinamount}, #{arapoutamount}, #{arapcloseamount},
                #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_Account
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="carryyear != null">
                CarryYear =#{carryyear},
            </if>
            <if test="carrymonth != null">
                CarryMonth =#{carrymonth},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="rownum != null ">
                RowNum =#{rownum},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="billopenamount != null">
                BillOpenAmount =#{billopenamount},
            </if>
            <if test="billinamount != null">
                BillInAmount =#{billinamount},
            </if>
            <if test="billoutamount != null">
                BillOutAmount =#{billoutamount},
            </if>
            <if test="billcloseamount != null">
                BillCloseAmount =#{billcloseamount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="invoopenamount != null">
                InvoOpenAmount =#{invoopenamount},
            </if>
            <if test="invoinamount != null">
                InvoInAmount =#{invoinamount},
            </if>
            <if test="invooutamount != null">
                InvoOutAmount =#{invooutamount},
            </if>
            <if test="invocloseamount != null">
                InvoCloseAmount =#{invocloseamount},
            </if>
            <if test="arapopenamount != null">
                ArapOpenAmount =#{arapopenamount},
            </if>
            <if test="arapinamount != null">
                ArapInAmount =#{arapinamount},
            </if>
            <if test="arapoutamount != null">
                ArapOutAmount =#{arapoutamount},
            </if>
            <if test="arapcloseamount != null">
                ArapCloseAmount =#{arapcloseamount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_Account
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BuyAccountPojo">
        select
        id
        from Buy_AccountItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>
    <!--查询DelInvoIds-->
    <select id="getDelInvoIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BuyAccountPojo">
        select
        id
        from Buy_AccountInvo
        where Pid = #{id} and id not in
        <foreach collection="invo" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>
    <!--查询DelArapIds-->
    <select id="getDelArapIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BuyAccountPojo">
        select
        id
        from Buy_AccountArap
        where Pid = #{id} and id not in
        <foreach collection="arap" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>
    <!--查询指定行数据-->
    <select id="pullItemList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyAccountitemPojo">
        SELECT
        'in' as Direction,
        'D03M04' as ModuleCode,
        Buy_Deduction.id as Billid,
        Buy_Deduction.RefNo as BillUid,
        Buy_Deduction.BillType,
        Buy_Deduction.BillTitle,
        Buy_Deduction.BillDate,
        0.0 as OpenAmount,
        0 - Buy_Deduction.BillTaxAmount as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount
        FROM Buy_Deduction
        where (Buy_Deduction.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Deduction.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'in' as Direction,
        'D03M03' as ModuleCode,
        Buy_Finishing.id as Billid,
        Buy_Finishing.RefNo as BillUid,
        Buy_Finishing.BillType,
        Buy_Finishing.BillTitle,
        Buy_Finishing.BillDate,
        0.0 as OpenAmount,
        (CASE WHEN Buy_Finishing.BillType IN ('采购验收','其他收货') THEN Buy_Finishing.BillTaxAmount
        ELSE 0 - Buy_Finishing.BillTaxAmount END) as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount
        FROM Buy_Finishing
        where (Buy_Finishing.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Finishing.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D03M06PRE' as ModuleCode,
        Buy_Prepayments.id as Billid,
        Buy_Prepayments.RefNo as BillUid,
        Buy_Prepayments.BillType,
        Buy_Prepayments.BillTitle,
        Buy_Prepayments.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Buy_Prepayments.BillAmount as OutAmount,
        0.0 as CloseAmount
        FROM Buy_Prepayments
        where (Buy_Prepayments.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Prepayments.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D03M06' as ModuleCode,
        Buy_Voucher.id as Billid,
        Buy_Voucher.RefNo as BillUid,
        Buy_Voucher.BillType,
        Buy_Voucher.BillTitle,
        Buy_Voucher.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Buy_Voucher.BillAmount as OutAmount,
        0.0 as CloseAmount
        FROM Buy_Voucher
        where (Buy_Voucher.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Voucher.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
    </select>

    <select id="pullInvoList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.som.domain.pojo.BuyAccountinvoPojo">
        SELECT
        'in' as Direction,
        'D03M04' as ModuleCode,
        Buy_Deduction.id as Billid,
        Buy_Deduction.RefNo as BillUid,
        Buy_Deduction.BillType,
        Buy_Deduction.BillTitle,
        Buy_Deduction.BillDate,
        0.0 as OpenAmount,
        0 - Buy_Deduction.BillTaxAmount as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount
        FROM Buy_Deduction
        where (Buy_Deduction.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Deduction.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'in' as Direction,
        'D03M03' as ModuleCode,
        Buy_Finishing.id as Billid,
        Buy_Finishing.RefNo as BillUid,
        Buy_Finishing.BillType,
        Buy_Finishing.BillTitle,
        Buy_Finishing.BillDate,
        0.0 as OpenAmount,
        (CASE WHEN Buy_Finishing.BillType IN ('采购验收','其他收货') THEN Buy_Finishing.BillTaxAmount
        ELSE 0 - Buy_Finishing.BillTaxAmount END) as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount
        FROM Buy_Finishing
        where (Buy_Finishing.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Finishing.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D03M05' as ModuleCode,
        Buy_Invoice.id as Billid,
        Buy_Invoice.RefNo as BillUid,
        Buy_Invoice.BillType,
        Buy_Invoice.BillTitle,
        Buy_Invoice.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Buy_Invoice.TaxAmount as OutAmount,
        0.0 as CloseAmount
        FROM Buy_Invoice
        where (Buy_Invoice.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Invoice.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
    </select>

    <select id="pullArapList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.som.domain.pojo.BuyAccountarapPojo">
        SELECT
        'in' as Direction,
        'D03M05' as ModuleCode,
        Buy_Invoice.id as Billid,
        Buy_Invoice.RefNo as BillUid,
        Buy_Invoice.BillType,
        Buy_Invoice.BillTitle,
        Buy_Invoice.BillDate,
        0.0 as OpenAmount,
        Buy_Invoice.TaxAmount as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount
        FROM Buy_Invoice
        where (Buy_Invoice.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Invoice.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D03M06PRE' as ModuleCode,
        Buy_Prepayments.id as Billid,
        Buy_Prepayments.RefNo as BillUid,
        Buy_Prepayments.BillType,
        Buy_Prepayments.BillTitle,
        Buy_Prepayments.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Buy_Prepayments.BillAmount as OutAmount,
        0.0 as CloseAmount
        FROM Buy_Prepayments
        where (Buy_Prepayments.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Prepayments.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D03M06' as ModuleCode,
        Buy_Voucher.id as Billid,
        Buy_Voucher.RefNo as BillUid,
        Buy_Voucher.BillType,
        Buy_Voucher.BillTitle,
        Buy_Voucher.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Buy_Voucher.BillAmount as OutAmount,
        0.0 as CloseAmount
        FROM Buy_Voucher
        where (Buy_Voucher.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Buy_Voucher.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
    </select>



    <!--查询单个-->
    <select id="getSupplierIds" resultType="java.lang.String">
        select id
        from App_Workgroup
        where App_Workgroup.EnabledMark = 1
          and App_Workgroup.DeleteMark = 0
          and (GroupType = '供应商' or GroupType = '外协厂商')
          and App_Workgroup.Tenantid = #{tid}
    </select>

    <!--通过主键删除-->
    <delete id="deleteByMonth">
        delete
        from Buy_Account
        where CarryYear = #{year}
          and CarryMonth = #{nonth}
          and Tenantid = #{tid}
    </delete>
    <select id="getDeleteIds" resultType="java.lang.String">
        select id
        from Buy_Account
        where CarryYear = #{year}
          and CarryMonth = #{nonth}
          and Tenantid = #{tid}
    </select>
    <!--查询单个-->
    <select id="getMaxEntityByGroup" resultType="inks.service.sa.som.domain.pojo.BuyAccountPojo">
        <include refid="selectbillVo"/>
        where Buy_Account.Groupid = #{key}
        and Buy_Account.Tenantid = #{tid}
        Order by EndDate Desc LIMIT 1
    </select>

    <!--查询指定行数据-->
    <select id="getMultItemList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyAccountitemPojo">
        <include refid="selectdetailVo"/>
        where Buy_Account.Tenantid =#{tenantid}
        and Buy_AccountItem.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        ${filterstr}
        order by Buy_AccountItem.BillDate
    </select>

    <!--查询指定行数据-->
    <select id="getNowPageList" resultType="inks.service.sa.som.domain.pojo.BuyAccountPojo">
        SELECT * FROM (SELECT
        App_Workgroup.id,
        App_Workgroup.WgGroupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate,
        App_Workgroup.Seller,
        COALESCE((SELECT
        Buy_Account.BillCloseAmount
        FROM Buy_Account
        WHERE Buy_Account.Groupid=App_Workgroup.id
        Order By RowNum Desc LIMIT 1),0) as BillOpenAmount,

        COALESCE((SELECT
        sum(CASE
        WHEN Buy_Finishing.BillType IN ('采购验收','其他收货') THEN Buy_Finishing.BillTaxAmount
        ELSE 0 - Buy_Finishing.BillTaxAmount END)
        FROM Buy_Finishing where Buy_Finishing.Groupid=App_Workgroup.id
        and Buy_Finishing.BillDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
        ),0)-
        COALESCE((SELECT Sum(Buy_Deduction.BillTaxAmount) FROM Buy_Deduction
        where Buy_Deduction.Groupid=App_Workgroup.id
        and Buy_Deduction.BillDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}),0)
        AS BillInAmount,

        COALESCE((SELECT
        Sum(Buy_Prepayments.BillAmount)
        FROM Buy_Prepayments WHERE Buy_Prepayments.Groupid=App_Workgroup.id
        and Buy_Prepayments.BillDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}),0)+
        COALESCE((SELECT
        Sum(Buy_Voucher.BillAmount)
        FROM Buy_Voucher WHERE Buy_Voucher.Groupid=App_Workgroup.id
        and Buy_Voucher.BillDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}),0)
        as BillOutAmount,

        COALESCE((SELECT
        Buy_Account.BillCloseAmount
        FROM Buy_Account
        WHERE Buy_Account.Groupid=App_Workgroup.id
        Order By RowNum Desc LIMIT 1),0) +
        COALESCE((SELECT
        sum(CASE
        WHEN Buy_Finishing.BillType IN ('采购验收','其他收货') THEN Buy_Finishing.BillTaxAmount
        ELSE 0 - Buy_Finishing.BillTaxAmount END)
        FROM Buy_Finishing where Buy_Finishing.Groupid=App_Workgroup.id
        and Buy_Finishing.BillDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
        ),0)-
        COALESCE((SELECT Sum(Buy_Deduction.BillTaxAmount) FROM Buy_Deduction
        where Buy_Deduction.Groupid=App_Workgroup.id
        and Buy_Deduction.BillDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}),0)
        -
        COALESCE((SELECT
        Sum(Buy_Prepayments.BillAmount)
        FROM Buy_Prepayments WHERE Buy_Prepayments.Groupid=App_Workgroup.id
        and Buy_Prepayments.BillDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}),0)-
        COALESCE((SELECT
        Sum(Buy_Voucher.BillAmount)
        FROM Buy_Voucher WHERE Buy_Voucher.Groupid=App_Workgroup.id
        and Buy_Voucher.BillDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}),0)
        AS BillCloseAmount

        FROM
        App_Workgroup
        where App_Workgroup.EnabledMark = 1
        and App_Workgroup.DeleteMark = 0
        and (GroupType='供应商' OR GroupType='外协厂商')
        and App_Workgroup.Tenantid =#{queryParam.tenantid}
        <if test="queryParam.filterstr != null ">
            ${queryParam.filterstr}
        </if>
        <if test="queryParam.SearchPojo != null ">
            <if test="queryParam.SearchType==0">
                <if test="queryParam.SearchPojo.groupuid != null and queryParam.SearchPojo.groupuid  != ''">
                    and App_Workgroup.GroupUid like concat('%', #{queryParam.SearchPojo.groupuid}, '%')
                </if>
                <if test="queryParam.SearchPojo.groupname != null and queryParam.SearchPojo.groupname  != ''">
                    and App_Workgroup.GroupName like concat('%', #{queryParam.SearchPojo.groupname}, '%')
                </if>
            </if>
            <if test="queryParam.SearchType==1">
                <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
                    <if test="queryParam.SearchPojo.groupuid != null and queryParam.SearchPojo.groupuid != ''">
                        or App_Workgroup.GroupUid like concat('%', #{queryParam.SearchPojo.groupuid}, '%')
                    </if>
                    <if test="queryParam.SearchPojo.groupname != null and queryParam.SearchPojo.groupname != ''">
                        or App_Workgroup.GroupName like concat('%', #{queryParam.SearchPojo.groupname}, '%')
                    </if>
                </trim>
            </if>
        </if>
        ) t
        <if test="online == 1 ">
            WHERE BillCloseAmount != 0
        </if>
        order by ${queryParam.OrderBy}
        <if test="queryParam.OrderType==0">asc</if>
        <if test="queryParam.OrderType==1">desc</if>
    </select>

    <select id="getWorkGroupEntity" resultType="inks.service.sa.som.domain.pojo.AppWgSupplierPojo">
        SELECT App_Workgroup.id,
               App_Workgroup.WgGroupid,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.GroupClass,
               App_Workgroup.Linkman,
               App_Workgroup.Telephone,
               App_Workgroup.GroupFax,
               App_Workgroup.GroupAdd,
               App_Workgroup.Remark,
               App_Workgroup.InvalidDate,
               App_Workgroup.GroupType,
               App_Workgroup.CreditDUint,
               App_Workgroup.CreditDQuantity,
               App_Workgroup.CreditCUint,
               App_Workgroup.CreditCQuantity,
               App_Workgroup.RowNum,
               App_Workgroup.CreateBy,
               App_Workgroup.CreateByid,
               App_Workgroup.CreateDate,
               App_Workgroup.Lister,
               App_Workgroup.Listerid,
               App_Workgroup.ModifyDate,
               App_Workgroup.Mobile,
               App_Workgroup.LinkmanS,
               App_Workgroup.TelephoneS,
               App_Workgroup.MobileS,
               App_Workgroup.Country,
               App_Workgroup.Province,
               App_Workgroup.GroupZip,
               App_Workgroup.DeliverAdd,
               App_Workgroup.InvoiceAdd,
               App_Workgroup.Seller,
               App_Workgroup.GroupLabel,
               App_Workgroup.GroupLevel,
               App_Workgroup.GroupState,
               App_Workgroup.Source,
               App_Workgroup.Credit,
               App_Workgroup.PaymentMethod,
               App_Workgroup.CreditCode,
               App_Workgroup.DepositBank,
               App_Workgroup.BankAccount,
               App_Workgroup.EnabledMark,
               App_Workgroup.DeleteMark,
               App_Workgroup.DeleteLister,
               App_Workgroup.DeleteListerid,
               App_Workgroup.DeleteDate,
               App_Workgroup.FmAccoid,
               App_Workgroup.ForeAccoid,
               App_Workgroup.BusMachRemAmt,
               App_Workgroup.BusDeliRemAmt,
               App_Workgroup.BusInvoRemAmt,
               App_Workgroup.BusAccoCloseAmt,
               App_Workgroup.BusAccoNowAmt,
               App_Workgroup.BuyOrderRemAmt,
               App_Workgroup.BuyFiniRemAmt,
               App_Workgroup.BuyInvoRemAmt,
               App_Workgroup.BuyAccoCloseAmt,
               App_Workgroup.BuyAccoNowAmt,
               App_Workgroup.City,
               App_Workgroup.County,
               App_Workgroup.Street,
               App_Workgroup.LocalAdd,
               App_Workgroup.Custom1,
               App_Workgroup.Custom2,
               App_Workgroup.Custom3,
               App_Workgroup.Custom4,
               App_Workgroup.Custom5,
               App_Workgroup.Custom6,
               App_Workgroup.Custom7,
               App_Workgroup.Custom8,
               App_Workgroup.Custom9,
               App_Workgroup.Custom10,
               App_Workgroup.Tenantid,
               App_Workgroup.Revision,
               a2.AccoCode AS FmAccoCode,
               a1.AccoCode AS ForeAccoCode,
               a2.AccoName AS FmAccoName,
               a1.AccoName AS ForeAccoName
        FROM App_Workgroup
                 LEFT JOIN Fm_Account AS a1 ON App_Workgroup.ForeAccoid = a1.id
                 LEFT JOIN Fm_Account AS a2 ON App_Workgroup.FmAccoid = a2.id
        where App_Workgroup.id = #{key}
          and App_Workgroup.Tenantid = #{tid}
    </select>
</mapper>

