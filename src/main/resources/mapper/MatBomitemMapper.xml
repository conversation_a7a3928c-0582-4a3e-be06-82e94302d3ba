<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatBomitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatBomitemPojo">
        SELECT Mat_BomItem.id,
               Mat_BomItem.Pid,
               Mat_BomItem.Goodsid,
               Mat_BomItem.ItemCode,
               Mat_BomItem.ItemName,
               Mat_BomItem.ItemSpec,
               Mat_BomItem.ItemUnit,
               Mat_BomItem.MainQty,
               Mat_BomItem.SubQty,
               Mat_BomItem.LossRate,
               Mat_BomItem.AttrCode,
               Mat_BomItem.FlowCode,
               Mat_BomItem.Description,
               Mat_BomItem.ItemLabel,
               Mat_BomItem.Parentid,
               Mat_BomItem.ParentGoodsid,
               Mat_BomItem.SubCount,
               Mat_BomItem.RowNum,
               Mat_BomItem.Remark,
               Mat_BomItem.SubLossQty,
               Mat_BomItem.MatType,
               Mat_BomItem.CostPrice,
               Mat_BomItem.CostAmount,
               Mat_BomItem.Custom1,
               Mat_BomItem.Custom2,
               Mat_BomItem.Custom3,
               Mat_BomItem.Custom4,
               Mat_BomItem.Custom5,
               Mat_BomItem.Custom6,
               Mat_BomItem.Custom7,
               Mat_BomItem.Custom8,
               Mat_BomItem.Custom9,
               Mat_BomItem.Custom10,
               Mat_BomItem.Tenantid,
               Mat_BomItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
        Mat_Goods.Material as goodsmaterial,
               Mat_Goods.Bomid
        FROM Mat_BomItem
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Mat_BomItem.Goodsid
        where Mat_BomItem.id = #{key}
          and Mat_BomItem.Tenantid = #{tid}
    </select>
    <sql id="selectMatBomitemVo">
        SELECT Mat_BomItem.id,
               Mat_BomItem.Pid,
               Mat_BomItem.Goodsid,
               Mat_BomItem.ItemCode,
               Mat_BomItem.ItemName,
               Mat_BomItem.ItemSpec,
               Mat_BomItem.ItemUnit,
               Mat_BomItem.MainQty,
               Mat_BomItem.SubQty,
               Mat_BomItem.LossRate,
               Mat_BomItem.AttrCode,
               Mat_BomItem.FlowCode,
               Mat_BomItem.Description,
               Mat_BomItem.ItemLabel,
               Mat_BomItem.Parentid,
               Mat_BomItem.ParentGoodsid,
               Mat_BomItem.SubCount,
               Mat_BomItem.RowNum,
               Mat_BomItem.Remark,
               Mat_BomItem.SubLossQty,
               Mat_BomItem.MatType,
               Mat_BomItem.CostPrice,
               Mat_BomItem.CostAmount,
               Mat_BomItem.Custom1,
               Mat_BomItem.Custom2,
               Mat_BomItem.Custom3,
               Mat_BomItem.Custom4,
               Mat_BomItem.Custom5,
               Mat_BomItem.Custom6,
               Mat_BomItem.Custom7,
               Mat_BomItem.Custom8,
               Mat_BomItem.Custom9,
               Mat_BomItem.Custom10,
               Mat_BomItem.Tenantid,
               Mat_BomItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Material as goodsmaterial,
               Mat_Goods.Bomid
        FROM Mat_BomItem
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Mat_BomItem.Goodsid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatBomitemPojo">
        <include refid="selectMatBomitemVo"/>
        where 1 = 1 and Mat_BomItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_BomItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Mat_BomItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_BomItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Mat_BomItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Mat_BomItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Mat_BomItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Mat_BomItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.attrcode != null and SearchPojo.attrcode != ''">
            and Mat_BomItem.attrcode like concat('%', #{SearchPojo.attrcode}, '%')
        </if>
        <if test="SearchPojo.flowcode != null and SearchPojo.flowcode != ''">
            and Mat_BomItem.flowcode like concat('%', #{SearchPojo.flowcode}, '%')
        </if>
        <if test="SearchPojo.description != null and SearchPojo.description != ''">
            and Mat_BomItem.description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.itemlabel != null and SearchPojo.itemlabel != ''">
            and Mat_BomItem.itemlabel like concat('%', #{SearchPojo.itemlabel}, '%')
        </if>
        <if test="SearchPojo.parentid != null and SearchPojo.parentid != ''">
            and Mat_BomItem.parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.parentgoodsid != null and SearchPojo.parentgoodsid != ''">
            and Mat_BomItem.parentgoodsid like concat('%', #{SearchPojo.parentgoodsid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Mat_BomItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_BomItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_BomItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_BomItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_BomItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_BomItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_BomItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_BomItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_BomItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_BomItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_BomItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Mat_BomItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_BomItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Mat_BomItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Mat_BomItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Mat_BomItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Mat_BomItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.attrcode != null and SearchPojo.attrcode != ''">
                or Mat_BomItem.AttrCode like concat('%', #{SearchPojo.attrcode}, '%')
            </if>
            <if test="SearchPojo.flowcode != null and SearchPojo.flowcode != ''">
                or Mat_BomItem.FlowCode like concat('%', #{SearchPojo.flowcode}, '%')
            </if>
            <if test="SearchPojo.description != null and SearchPojo.description != ''">
                or Mat_BomItem.Description like concat('%', #{SearchPojo.description}, '%')
            </if>
            <if test="SearchPojo.itemlabel != null and SearchPojo.itemlabel != ''">
                or Mat_BomItem.ItemLabel like concat('%', #{SearchPojo.itemlabel}, '%')
            </if>
            <if test="SearchPojo.parentid != null and SearchPojo.parentid != ''">
                or Mat_BomItem.Parentid like concat('%', #{SearchPojo.parentid}, '%')
            </if>
            <if test="SearchPojo.parentgoodsid != null and SearchPojo.parentgoodsid != ''">
                or Mat_BomItem.ParentGoodsid like concat('%', #{SearchPojo.parentgoodsid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Mat_BomItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_BomItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_BomItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_BomItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_BomItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_BomItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_BomItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_BomItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_BomItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_BomItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_BomItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.MatBomitemPojo">
        SELECT Mat_BomItem.id,
               Mat_BomItem.Pid,
               Mat_BomItem.Goodsid,
               Mat_BomItem.ItemCode,
               Mat_BomItem.ItemName,
               Mat_BomItem.ItemSpec,
               Mat_BomItem.ItemUnit,
               Mat_BomItem.MainQty,
               Mat_BomItem.SubQty,
               Mat_BomItem.LossRate,
               Mat_BomItem.AttrCode,
               Mat_BomItem.FlowCode,
               Mat_BomItem.Description,
               Mat_BomItem.ItemLabel,
               Mat_BomItem.Parentid,
               Mat_BomItem.ParentGoodsid,
               Mat_BomItem.RowNum,
               Mat_BomItem.SubCount,
               Mat_BomItem.Remark,
               Mat_BomItem.SubLossQty,
               Mat_BomItem.MatType,
        Mat_BomItem.CostPrice,
        Mat_BomItem.CostAmount,
               Mat_BomItem.Custom1,
               Mat_BomItem.Custom2,
               Mat_BomItem.Custom3,
               Mat_BomItem.Custom4,
               Mat_BomItem.Custom5,
               Mat_BomItem.Custom6,
               Mat_BomItem.Custom7,
               Mat_BomItem.Custom8,
               Mat_BomItem.Custom9,
               Mat_BomItem.Custom10,
               Mat_BomItem.Tenantid,
               Mat_BomItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.GoodsState,
               Mat_Goods.Partid,
               Mat_Goods.Material as goodsmaterial,
               Mat_Goods.Bomid,
               Mat_Goods.BuyRemQty,
               Mat_Goods.WkWsRemQty,
               Mat_Goods.WkScRemQty,
               Mat_Goods.BusRemQty,
               Mat_Goods.MrpRemQty,
               Mat_Goods.RequRemQty,
               Mat_Goods.IvQuantity,
               Mat_Goods.surface,
        Mat_Goods.drawing,
        Mat_Goods.brandname
        FROM Mat_BomItem
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Mat_BomItem.Goodsid
        where Mat_BomItem.Pid = #{Pid}
          and Mat_BomItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_BomItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, MainQty, SubQty, LossRate, AttrCode, FlowCode, Description, ItemLabel, Parentid, ParentGoodsid, RowNum, SubCount, Remark, SubLossQty, MatType, CostPrice, CostAmount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{mainqty}, #{subqty}, #{lossrate}, #{attrcode}, #{flowcode}, #{description}, #{itemlabel}, #{parentid}, #{parentgoodsid}, #{rownum}, #{subcount}, #{remark}, #{sublossqty}, #{mattype}, #{costprice}, #{costamount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_BomItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="mainqty != null">
                MainQty = #{mainqty},
            </if>
            <if test="subqty != null">
                SubQty = #{subqty},
            </if>
            <if test="lossrate != null">
                LossRate = #{lossrate},
            </if>
            <if test="attrcode != null ">
                AttrCode = #{attrcode},
            </if>
            <if test="flowcode != null ">
                FlowCode = #{flowcode},
            </if>
            <if test="description != null ">
                Description = #{description},
            </if>
            <if test="itemlabel != null ">
                ItemLabel = #{itemlabel},
            </if>
            <if test="parentid != null ">
                Parentid = #{parentid},
            </if>
            <if test="parentgoodsid != null ">
                ParentGoodsid = #{parentgoodsid},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="subcount != null">
                SubCount = #{subcount},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="sublossqty != null">
                SubLossQty = #{sublossqty},
            </if>
            <if test="mattype != null">
                MatType = #{mattype},
            </if>
            <if test="costprice != null">
                CostPrice = #{costprice},
            </if>
            <if test="costamount != null">
                CostAmount = #{costamount},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_BomItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询单个-->
    <select id="getListByGoodsid" resultType="inks.service.sa.som.domain.pojo.MatBomitemPojo">
        SELECT Mat_BomItem.id,
               Mat_BomItem.Pid,
               Mat_BomItem.Goodsid,
               Mat_BomItem.ItemCode,
               Mat_BomItem.ItemName,
               Mat_BomItem.ItemSpec,
               Mat_BomItem.ItemUnit,
               Mat_BomItem.MainQty,
               Mat_BomItem.SubQty,
               Mat_BomItem.LossRate,
               Mat_BomItem.AttrCode,
               Mat_BomItem.FlowCode,
               Mat_BomItem.Description,
               Mat_BomItem.ItemLabel,
               Mat_BomItem.Parentid,
               Mat_BomItem.ParentGoodsid,
               Mat_BomItem.RowNum,
               Mat_BomItem.SubCount,
               Mat_BomItem.Remark,
               Mat_BomItem.SubLossQty,
               Mat_BomItem.MatType,
               Mat_BomItem.CostPrice,
               Mat_BomItem.CostAmount,
               Mat_BomItem.Custom1,
               Mat_BomItem.Custom2,
               Mat_BomItem.Custom3,
               Mat_BomItem.Custom4,
               Mat_BomItem.Custom5,
               Mat_BomItem.Custom6,
               Mat_BomItem.Custom7,
               Mat_BomItem.Custom8,
               Mat_BomItem.Custom9,
               Mat_BomItem.Custom10,
               Mat_BomItem.Tenantid,
               Mat_BomItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Bomid as GoodsBomid
        FROM Mat_BomItem
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Mat_BomItem.Goodsid
        where Mat_BomItem.Goodsid = #{key}
          and Mat_BomItem.Pid = #{pid}
          and Mat_BomItem.Tenantid = #{tid}
    </select>


    <!--查询List-->
    <select id="getDetailList" resultType="inks.common.core.domain.MatBomdetailPojo">
        SELECT Mat_BomItem.id,
               Mat_BomItem.Pid,
               Mat_BomItem.Goodsid,
               Mat_BomItem.ItemCode,
               Mat_BomItem.ItemName,
               Mat_BomItem.ItemSpec,
               Mat_BomItem.ItemUnit,
               Mat_BomItem.MainQty,
               Mat_BomItem.SubQty,
               Mat_BomItem.LossRate,
               Mat_BomItem.AttrCode,
               Mat_BomItem.FlowCode,
               Mat_BomItem.Description,
               Mat_BomItem.ItemLabel,
               Mat_BomItem.Parentid,
               Mat_BomItem.ParentGoodsid,
               Mat_BomItem.RowNum,
               Mat_BomItem.SubCount,
               Mat_BomItem.Remark,
               Mat_BomItem.SubLossQty,
               Mat_BomItem.MatType,
               Mat_BomItem.CostPrice,
               Mat_BomItem.CostAmount,
               Mat_BomItem.Custom1,
               Mat_BomItem.Custom2,
               Mat_BomItem.Custom3,
               Mat_BomItem.Custom4,
               Mat_BomItem.Custom5,
               Mat_BomItem.Custom6,
               Mat_BomItem.Custom7,
               Mat_BomItem.Custom8,
               Mat_BomItem.Custom9,
               Mat_BomItem.Custom10,
               Mat_BomItem.Tenantid,
               Mat_BomItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.SafeStock,
               #{treeid}       as TreeParentid,
               Mat_BomItem.id  as BomItemid,
               Mat_Goods.Bomid as GoodsBomid
        FROM Mat_BomItem
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Mat_BomItem.Goodsid
        where Mat_BomItem.Pid = #{pid}
          and Mat_BomItem.Tenantid = #{tid}
          and (Mat_BomItem.parentid is null or Mat_BomItem.parentid = '')
        order by RowNum
    </select>
    <select id="getPidsByGoodsid" resultType="java.lang.String">
        SELECT DISTINCT Pid
        FROM Mat_BomItem
        where Goodsid = #{key} and Tenantid = #{tid}
    </select>
</mapper>

