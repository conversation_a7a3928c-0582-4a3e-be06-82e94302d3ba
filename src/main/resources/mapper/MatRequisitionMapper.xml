<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatRequisitionMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatRequisitionPojo">
        SELECT Mat_Requisition.id,
               Mat_Requisition.RefNo,
               Mat_Requisition.BillType,
               Mat_Requisition.BillDate,
               Mat_Requisition.BillTitle,
               Mat_Requisition.Groupid,
               Mat_Requisition.BranthName,
               Mat_Requisition.Operator,
               Mat_Requisition.Operatorid,
               Mat_Requisition.Summary,
               Mat_Requisition.CreateBy,
               Mat_Requisition.CreateByid,
               Mat_Requisition.CreateDate,
               Mat_Requisition.Lister,
               Mat_Requisition.Listerid,
               Mat_Requisition.ModifyDate,
               Mat_Requisition.Assessor,
               Mat_Requisition.Assessorid,
               Mat_Requisition.AssessDate,
               Mat_Requisition.BillStateCode,
               Mat_Requisition.BillStateDate,
               Mat_Requisition.ModuleCode,
               Mat_Requisition.CiteUid,
               Mat_Requisition.Citeid,
               Mat_Requisition.DisannulCount,
               Mat_Requisition.ItemCount,
               Mat_Requisition.PickCount,
               Mat_Requisition.FinishCount,
               Mat_Requisition.PrintCount,
               Mat_Requisition.Projectid,
               Mat_Requisition.ProjCode,
               Mat_Requisition.ProjName,
               Mat_Requisition.OaFlowMark,
               Mat_Requisition.Custom1,
               Mat_Requisition.Custom2,
               Mat_Requisition.Custom3,
               Mat_Requisition.Custom4,
               Mat_Requisition.Custom5,
               Mat_Requisition.Custom6,
               Mat_Requisition.Custom7,
               Mat_Requisition.Custom8,
               Mat_Requisition.Custom9,
               Mat_Requisition.Custom10,
               Mat_Requisition.Tenantid,
               Mat_Requisition.TenantName,
               Mat_Requisition.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Mat_Requisition
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Mat_Requisition.Groupid
        where Mat_Requisition.id = #{key}
          and Mat_Requisition.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Mat_Requisition.id,
               Mat_Requisition.RefNo,
               Mat_Requisition.BillType,
               Mat_Requisition.BillDate,
               Mat_Requisition.BillTitle,
               Mat_Requisition.Groupid,
               Mat_Requisition.BranthName,
               Mat_Requisition.Operator,
               Mat_Requisition.Operatorid,
               Mat_Requisition.Summary,
               Mat_Requisition.CreateBy,
               Mat_Requisition.CreateByid,
               Mat_Requisition.CreateDate,
               Mat_Requisition.Lister,
               Mat_Requisition.Listerid,
               Mat_Requisition.ModifyDate,
               Mat_Requisition.Assessor,
               Mat_Requisition.Assessorid,
               Mat_Requisition.AssessDate,
               Mat_Requisition.BillStateCode,
               Mat_Requisition.BillStateDate,
               Mat_Requisition.ModuleCode,
               Mat_Requisition.CiteUid,
               Mat_Requisition.Citeid,
               Mat_Requisition.DisannulCount,
               Mat_Requisition.ItemCount,
               Mat_Requisition.PickCount,
               Mat_Requisition.FinishCount,
               Mat_Requisition.PrintCount,
               Mat_Requisition.Projectid,
               Mat_Requisition.ProjCode,
               Mat_Requisition.ProjName,
               Mat_Requisition.OaFlowMark,
               Mat_Requisition.Custom1,
               Mat_Requisition.Custom2,
               Mat_Requisition.Custom3,
               Mat_Requisition.Custom4,
               Mat_Requisition.Custom5,
               Mat_Requisition.Custom6,
               Mat_Requisition.Custom7,
               Mat_Requisition.Custom8,
               Mat_Requisition.Custom9,
               Mat_Requisition.Custom10,
               Mat_Requisition.Tenantid,
               Mat_Requisition.TenantName,
               Mat_Requisition.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Mat_Requisition
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Mat_Requisition.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10,
               Mat_RequisitionItem.id,
               Mat_RequisitionItem.Pid,
               Mat_RequisitionItem.Goodsid,
               (CASE
                    WHEN Mat_Requisition.BillType IN ('生产领料', '其他领料', '领料单','外协领料') THEN Mat_RequisitionItem.FinishCost
                    ELSE 0 - Mat_RequisitionItem.FinishCost END) as FinishCost,
               (CASE
                    WHEN Mat_Requisition.BillType IN ('生产领料', '其他领料', '领料单','外协领料') THEN Mat_RequisitionItem.Quantity
                    ELSE 0 - Mat_RequisitionItem.Quantity END)   as Quantity,
               Mat_RequisitionItem.PlanDate,
               Mat_RequisitionItem.FinishQty,
               Mat_RequisitionItem.Remark,
               Mat_RequisitionItem.StateCode,
               Mat_RequisitionItem.StateDate,
               Mat_RequisitionItem.CiteUid,
               Mat_RequisitionItem.CiteItemid,
               Mat_RequisitionItem.Customer,
               Mat_RequisitionItem.CustPO,
               Mat_RequisitionItem.RowNum,
               Mat_RequisitionItem.BackQty,
               Mat_RequisitionItem.BackCost,
               Mat_RequisitionItem.Closed,
               Mat_RequisitionItem.StdQty,
               Mat_RequisitionItem.Storeid,
               Mat_RequisitionItem.Location,
               Mat_RequisitionItem.BatchNo,
               Mat_RequisitionItem.MachUid,
               Mat_RequisitionItem.MachItemid,
               Mat_RequisitionItem.MachItemGoodid,
               Mat_RequisitionItem.MachGroupid,
               Mat_RequisitionItem.MainPlanUid,
               Mat_RequisitionItem.MainPlanItemid,
               Mat_RequisitionItem.MainPlanItemGoodid,
               Mat_RequisitionItem.MrpUid,
               Mat_RequisitionItem.MrpItemid,
               Mat_RequisitionItem.MrpItemGoodid,
               Mat_RequisitionItem.WorkUid,
               Mat_RequisitionItem.WorkItemid,
               Mat_RequisitionItem.WorkItemGoodid,
               Mat_RequisitionItem.WorkItemMatid,
               Mat_RequisitionItem.ParentGoodsid,
               Mat_RequisitionItem.DisannulMark,
               Mat_RequisitionItem.SourceType,
               Mat_RequisitionItem.AttributeJson,
               Mat_RequisitionItem.Custom1,
               Mat_RequisitionItem.Custom2,
               Mat_RequisitionItem.Custom3,
               Mat_RequisitionItem.Custom4,
               Mat_RequisitionItem.Custom5,
               Mat_RequisitionItem.Custom6,
               Mat_RequisitionItem.Custom7,
               Mat_RequisitionItem.Custom8,
               Mat_RequisitionItem.Custom9,
               Mat_RequisitionItem.Custom10,
               Mat_RequisitionItem.Tenantid,
               Mat_RequisitionItem.TenantName,
               Mat_RequisitionItem.Revision,
               Mat_Requisition.RefNo,
               Mat_Requisition.BillType,
               Mat_Requisition.BillDate,
               Mat_Requisition.BillTitle,
               Mat_Requisition.BranthName,
               Mat_Requisition.Operator,
               Mat_Requisition.Summary,
               Mat_Requisition.CreateBy,
               Mat_Requisition.Lister,
               Mat_Requisition.Assessor,
               Mat_Requisition.ItemCount,
               Mat_Requisition.PickCount,
               Mat_Requisition.FinishCount,
               Mat_Requisition.PrintCount,
               Mat_Requisition.Projectid,
               Mat_Requisition.ProjCode,
               Mat_Requisition.ProjName
        FROM Mat_Requisition
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Mat_Requisition.Groupid
                 RIGHT JOIN Mat_RequisitionItem ON Mat_RequisitionItem.Pid = Mat_Requisition.id
                 LEFT JOIN Mat_Goods ON Mat_RequisitionItem.Goodsid = Mat_Goods.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatRequisitionitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Mat_Requisition.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Requisition.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Mat_Requisition.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Mat_Requisition.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Mat_Requisition.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Mat_Requisition.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.branthname != null ">
            and Mat_Requisition.branthname like concat('%', #{SearchPojo.branthname}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Mat_Requisition.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Mat_Requisition.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Mat_Requisition.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Mat_Requisition.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Mat_Requisition.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Mat_Requisition.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Mat_Requisition.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Mat_Requisition.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Mat_Requisition.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null ">
            and Mat_Requisition.billstatecode like concat('%', #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.modulecode != null ">
            and Mat_Requisition.modulecode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.citeid != null ">
            and Mat_Requisition.citeid like concat('%', #{SearchPojo.citeid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Mat_Requisition.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Mat_Requisition.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Mat_Requisition.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Mat_Requisition.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Mat_Requisition.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Mat_Requisition.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Mat_Requisition.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Mat_Requisition.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Mat_Requisition.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Mat_Requisition.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Mat_Requisition.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Mat_RequisitionItem.pid= #{SearchPojo.pid}
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_RequisitionItem.goodsid=#{SearchPojo.goodsid}
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            and Mat_RequisitionItem.WorkUid=#{SearchPojo.workuid}
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Mat_RequisitionItem.CiteUid=#{SearchPojo.citeuid}
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Mat_RequisitionItem.machitemid=#{SearchPojo.machitemid}
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Mat_RequisitionItem.machuid =#{SearchPojo.machuid}
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Mat_Requisition.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Mat_Requisition.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Mat_Requisition.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Mat_Requisition.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.branthname != null ">
                or Mat_Requisition.BranthName like concat('%', #{SearchPojo.branthname}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Mat_Requisition.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Mat_Requisition.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Mat_Requisition.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Mat_Requisition.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Mat_Requisition.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Mat_Requisition.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Mat_Requisition.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Mat_Requisition.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Mat_Requisition.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null ">
                or Mat_Requisition.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.modulecode != null ">
                or Mat_Requisition.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.citeid != null ">
                or Mat_Requisition.Citeid like concat('%', #{SearchPojo.citeid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Mat_Requisition.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Mat_Requisition.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Mat_Requisition.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Mat_Requisition.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Mat_Requisition.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Mat_Requisition.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Mat_Requisition.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Mat_Requisition.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Mat_Requisition.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Mat_Requisition.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Mat_Requisition.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Mat_RequisitionItem.Pid= #{SearchPojo.pid}
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_RequisitionItem.Goodsid = #{SearchPojo.goodsid}
            </if>
            <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
                or Mat_RequisitionItem.WorkUid=#{SearchPojo.workuid}
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Mat_RequisitionItem.CiteUid=#{SearchPojo.citeuid}
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Mat_RequisitionItem.machitemid =#{SearchPojo.machitemid}
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Mat_RequisitionItem.machuid =#{SearchPojo.machuid}
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatRequisitionPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Mat_Requisition.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Requisition.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Mat_Requisition.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Mat_Requisition.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Mat_Requisition.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Mat_Requisition.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.branthname != null ">
            and Mat_Requisition.BranthName like concat('%', #{SearchPojo.branthname}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Mat_Requisition.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Mat_Requisition.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Mat_Requisition.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Mat_Requisition.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Mat_Requisition.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Mat_Requisition.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Mat_Requisition.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Mat_Requisition.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Mat_Requisition.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null ">
            and Mat_Requisition.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.modulecode != null ">
            and Mat_Requisition.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.citeuid != null ">
            and Mat_Requisition.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeid != null ">
            and Mat_Requisition.Citeid like concat('%', #{SearchPojo.citeid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Mat_Requisition.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Mat_Requisition.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Mat_Requisition.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Mat_Requisition.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Mat_Requisition.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Mat_Requisition.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Mat_Requisition.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Mat_Requisition.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Mat_Requisition.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Mat_Requisition.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Mat_Requisition.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Mat_Requisition.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Mat_Requisition.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Mat_Requisition.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Mat_Requisition.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.branthname != null ">
                or Mat_Requisition.BranthName like concat('%', #{SearchPojo.branthname}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Mat_Requisition.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Mat_Requisition.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Mat_Requisition.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Mat_Requisition.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Mat_Requisition.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Mat_Requisition.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Mat_Requisition.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Mat_Requisition.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Mat_Requisition.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null ">
                or Mat_Requisition.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.modulecode != null ">
                or Mat_Requisition.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.citeuid != null ">
                or Mat_Requisition.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeid != null ">
                or Mat_Requisition.Citeid like concat('%', #{SearchPojo.citeid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Mat_Requisition.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Mat_Requisition.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Mat_Requisition.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Mat_Requisition.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Mat_Requisition.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Mat_Requisition.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Mat_Requisition.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Mat_Requisition.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Mat_Requisition.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Mat_Requisition.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Mat_Requisition.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Requisition(id, RefNo, BillType, BillDate, BillTitle, Groupid, BranthName, Operator, Operatorid,
                                    Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor,
                                    Assessorid, AssessDate, BillStateCode, BillStateDate, ModuleCode, CiteUid, Citeid,
                                    DisannulCount, ItemCount, PickCount, FinishCount, PrintCount, Projectid, ProjCode,
                                    ProjName,OaFlowMark, Custom1, Custom2,
                                    Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                    TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{groupid}, #{branthname}, #{operator},
                #{operatorid}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{billstatecode}, #{billstatedate},
                #{modulecode}, #{citeuid}, #{citeid}, #{disannulcount}, #{itemcount}, #{pickcount}, #{finishcount},
                #{printcount}, #{projectid}, #{projcode}, #{projname},#{oaflowmark}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Requisition
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="branthname != null ">
                BranthName =#{branthname},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="billstatecode != null ">
                BillStateCode =#{billstatecode},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="citeuid != null ">
                CiteUid =#{citeuid},
            </if>
            <if test="citeid != null ">
                Citeid =#{citeid},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="pickcount != null">
                PickCount =#{pickcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="projectid != null">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null">
                ProjName =#{projname},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Requisition
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Mat_Requisition
        SET Assessor   = #{assessor},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--    刷新发货完成数 Eric 20220722-->
    <update id="updateDisannulCount">
        update Mat_Requisition
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Mat_RequisitionItem
                                     where Mat_RequisitionItem.Pid = #{key}
                                       and Mat_RequisitionItem.Tenantid = #{tid}
                                       and Mat_RequisitionItem.DisannulMark = 1), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--    刷新发货完成数 Eric 20220722-->
    <update id="updateFinishCount">
        update Mat_Requisition
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Mat_RequisitionItem
                                   where Mat_RequisitionItem.Pid = #{key}
                                     and Mat_RequisitionItem.Tenantid = #{tid}
                                     and (Mat_RequisitionItem.Closed = 1
                                       or Mat_RequisitionItem.FinishQty >= Mat_RequisitionItem.Quantity)
                                  ), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.MatRequisitionPojo">
        select
        id
        from Mat_RequisitionItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>
    <update id="updateMachMatUsed">
        UPDATE Bus_MachiningItem
        SET MatUsed=1
        WHERE Bus_MachiningItem.id = #{key}
          and Bus_MachiningItem.Tenantid = #{tid}
    </update>

    <update id="updateUnMachMatUsed">
        UPDATE Bus_MachiningItem
        SET MatUsed=0
        WHERE Bus_MachiningItem.id = #{key}
          and Bus_MachiningItem.Tenantid = #{tid}
    </update>

    <update id="updateWsMatUsed">
        UPDATE Wk_WorksheetItem
        SET MatUsed=1
        WHERE Wk_WorksheetItem.id = #{key}
          and Wk_WorksheetItem.Tenantid = #{tid}
    </update>

    <update id="updateUnWsMatUsed">
        UPDATE Wk_WorksheetItem
        SET MatUsed=0
        WHERE Wk_WorksheetItem.id = #{key}
          and Wk_WorksheetItem.Tenantid = #{tid}
    </update>

    <update id="updateWipMatUsed">
        UPDATE Wk_WipNote
        SET MatUsed=1
        WHERE Wk_WipNote.MachItemid = #{key}
          and Wk_WipNote.Tenantid = #{tid}
    </update>

    <update id="updateUnWipMatUsed">
        UPDATE Wk_WipNote
        SET MatUsed=0
        WHERE Wk_WipNote.MachItemid = #{key}
          and Wk_WipNote.Tenantid = #{tid}
    </update>


    <select id="getWipByWorkUid" resultType="java.util.Map">
        select Wk_WipNote.id,
               Wk_WipNote.refno,
               Wk_WipNote.worktype,
               Wk_WipNote.workshopid,
               Wk_WipNote.workshop,
               Wk_WipNote.goodsid,
               Wk_WipNote.plandate,
               Wk_WipNote.quantity,
               Wk_WipNote.wkpcsqty,
               Wk_WipNote.wksecqty,
               Wk_WipNote.mrbpcsqty,
               Wk_WipNote.mrbsecqty,
               Wk_WipNote.supplement,
               Wk_WipNote.createby,
               Wk_WipNote.createbyid,
               Wk_WipNote.createdate,
               Wk_WipNote.lister,
               Wk_WipNote.listerid,
               Wk_WipNote.modifydate,
               Wk_WipNote.statecode,
               Wk_WipNote.statedate,
               Wk_WipNote.wkwpid,
               Wk_WipNote.wkwpcode,
               Wk_WipNote.wkwpname,
               Wk_WipNote.wkrownum,
               Wk_WipNote.customer,
               Wk_WipNote.custpo,
               Wk_WipNote.machuid,
               Wk_WipNote.machitemid,
               Wk_WipNote.machgroupid,
               Wk_WipNote.mainplanuid,
               Wk_WipNote.mainplanitemid,
               Wk_WipNote.workuid,
               Wk_WipNote.workitemid,
               Wk_WipNote.substwpid,
               Wk_WipNote.substwpcode,
               Wk_WipNote.substwpname,
               Wk_WipNote.subendwpid,
               Wk_WipNote.subendwpcode,
               Wk_WipNote.subendwpname,
               Wk_WipNote.subuid,
               Wk_WipNote.workdate,
               Wk_WipNote.compwpid,
               Wk_WipNote.compwpcode,
               Wk_WipNote.compwpname,
               Wk_WipNote.comppcsqty,
               Wk_WipNote.wipgroupid,
               Wk_WipNote.summary,
               Wk_WipNote.attributejson,
               Wk_WipNote.itemcount,
               Wk_WipNote.finishcount,
               Wk_WipNote.printcount,
               Wk_WipNote.sourcetype,
               Wk_WipNote.orgcustom1,
               Wk_WipNote.orgcustom2,
               Wk_WipNote.orgcustom3,
               Wk_WipNote.orgcustom4,
               Wk_WipNote.orgcustom5,
               Wk_WipNote.orgcustom6,
               Wk_WipNote.orgcustom7,
               Wk_WipNote.orgcustom8,
               Wk_WipNote.orgcustom9,
               Wk_WipNote.orgcustom10,
               Wk_WipNote.orgcustom11,
               Wk_WipNote.orgcustom12,
               Wk_WipNote.orgcustom13,
               Wk_WipNote.orgcustom14,
               Wk_WipNote.orgcustom15,
               Wk_WipNote.orgcustom16,
               Wk_WipNote.colorlevel,
               Wk_WipNote.custom1,
               Wk_WipNote.custom2,
               Wk_WipNote.custom3,
               Wk_WipNote.custom4,
               Wk_WipNote.custom5,
               Wk_WipNote.custom6,
               Wk_WipNote.custom7,
               Wk_WipNote.custom8,
               Wk_WipNote.custom9,
               Wk_WipNote.custom10,
               Wk_WipNote.tenantid,
               Wk_WipNote.revision,
               Mat_Goods.goodsuid,
               Mat_Goods.goodsname,
               Mat_Goods.goodsspec,
               Mat_Goods.goodsunit,
               Mat_Goods.partid
        FROM Mat_Goods
                 RIGHT JOIN Wk_WipNote ON Wk_WipNote.Goodsid = Mat_Goods.id
        where Wk_WipNote.WorkUid = #{key}
          and Wk_WipNote.Tenantid = #{tid} Limit 1
    </select>

    <select id="getMachItem" resultType="java.util.Map">
        SELECT Bus_MachiningItem.id,
               Bus_MachiningItem.pid,
               Bus_MachiningItem.goodsid,
               Bus_MachiningItem.quantity,
               Bus_MachiningItem.taxprice,
               Bus_MachiningItem.taxamount,
               Bus_MachiningItem.itemtaxrate,
               Bus_MachiningItem.taxtotal,
               Bus_MachiningItem.price,
               Bus_MachiningItem.amount,
               Bus_MachiningItem.itemorgdate,
               Bus_MachiningItem.itemplandate,
               Bus_MachiningItem.wkqty,
               Bus_MachiningItem.stoqty,
               Bus_MachiningItem.rownum,
               Bus_MachiningItem.remark,
               Bus_MachiningItem.engstatetext,
               Bus_MachiningItem.engstatedate,
               Bus_MachiningItem.wkstatetext,
               Bus_MachiningItem.wkstatedate,
               Bus_MachiningItem.busstatetext,
               Bus_MachiningItem.busstatedate,
               Bus_MachiningItem.buyquantity,
               Bus_MachiningItem.wkquantity,
               Bus_MachiningItem.inquantity,
               Bus_MachiningItem.pickqty,
               Bus_MachiningItem.finishqty,
               Bus_MachiningItem.outquantity,
               Bus_MachiningItem.outsecqty,
               Bus_MachiningItem.editioninfo,
               Bus_MachiningItem.itemcompdate,
               Bus_MachiningItem.virtualitem,
               Bus_MachiningItem.closed,
               Bus_MachiningItem.stdprice,
               Bus_MachiningItem.stdamount,
               Bus_MachiningItem.rebate,
               Bus_MachiningItem.mrpuid,
               Bus_MachiningItem.mrpid,
               Bus_MachiningItem.maxqty,
               Bus_MachiningItem.location,
               Bus_MachiningItem.batchno,
               Bus_MachiningItem.disannulmark,
               Bus_MachiningItem.wipused,
               Bus_MachiningItem.wkwpid,
               Bus_MachiningItem.wkwpcode,
               Bus_MachiningItem.wkwpname,
               Bus_MachiningItem.wkrownum,
               Bus_MachiningItem.ordercostuid,
               Bus_MachiningItem.ordercostitemid,
               Bus_MachiningItem.quotuid,
               Bus_MachiningItem.quotitemid,
               Bus_MachiningItem.bomtype,
               Bus_MachiningItem.bomid,
               Bus_MachiningItem.bomuid,
               Bus_MachiningItem.bomstate,
               Bus_MachiningItem.attributejson,
               Bus_MachiningItem.machtype,
               Bus_MachiningItem.reordermark,
               Bus_MachiningItem.matcode,
               Bus_MachiningItem.matused,
               Bus_MachiningItem.custom1,
               Bus_MachiningItem.custom2,
               Bus_MachiningItem.custom3,
               Bus_MachiningItem.custom4,
               Bus_MachiningItem.custom5,
               Bus_MachiningItem.custom6,
               Bus_MachiningItem.custom7,
               Bus_MachiningItem.custom8,
               Bus_MachiningItem.custom9,
               Bus_MachiningItem.custom10,
               Bus_MachiningItem.custom11,
               Bus_MachiningItem.custom12,
               Bus_MachiningItem.custom13,
               Bus_MachiningItem.custom14,
               Bus_MachiningItem.custom15,
               Bus_MachiningItem.custom16,
               Bus_MachiningItem.custom17,
               Bus_MachiningItem.custom18,
               Bus_MachiningItem.tenantid,
               Bus_MachiningItem.revision,
               Mat_Goods.goodsuid,
               Mat_Goods.goodsname,
               Mat_Goods.goodsspec,
               Mat_Goods.goodsunit,
               Mat_Goods.partid
        FROM Bus_MachiningItem
                 LEFT JOIN Mat_Goods ON Bus_MachiningItem.Goodsid = Mat_Goods.id
        where Bus_MachiningItem.id = #{key}
          and Bus_MachiningItem.Tenantid = #{tid} Limit 1
    </select>
    <!--查询List-->
    <select id="getWsItem" resultType="java.util.Map">
        SELECT Mat_Goods.goodsuid,
               Mat_Goods.goodsname,
               Mat_Goods.goodsspec,
               Mat_Goods.goodsunit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Wk_WorksheetItem.id,
               Wk_WorksheetItem.pid,
               Wk_WorksheetItem.goodsid,
               Wk_WorksheetItem.quantity,
               Wk_WorksheetItem.price,
               Wk_WorksheetItem.amount,
               Wk_WorksheetItem.itemhour,
               Wk_WorksheetItem.planhour,
               Wk_WorksheetItem.startdate,
               Wk_WorksheetItem.plandate,
               Wk_WorksheetItem.finishqty,
               Wk_WorksheetItem.finishhour,
               Wk_WorksheetItem.mrbqty,
               Wk_WorksheetItem.instorage,
               Wk_WorksheetItem.enabledmark,
               Wk_WorksheetItem.closed,
               Wk_WorksheetItem.startwpid,
               Wk_WorksheetItem.endwpid,
               Wk_WorksheetItem.remark,
               Wk_WorksheetItem.statecode,
               Wk_WorksheetItem.statedate,
               Wk_WorksheetItem.rownum,
               Wk_WorksheetItem.machtype,
               Wk_WorksheetItem.machuid,
               Wk_WorksheetItem.machitemid,
               Wk_WorksheetItem.machgroupid,
               Wk_WorksheetItem.mrpuid,
               Wk_WorksheetItem.mrpitemid,
               Wk_WorksheetItem.customer,
               Wk_WorksheetItem.custpo,
               Wk_WorksheetItem.citeuid,
               Wk_WorksheetItem.citeitemid,
               Wk_WorksheetItem.mainplanuid,
               Wk_WorksheetItem.mainplanitemid,
               Wk_WorksheetItem.location,
               Wk_WorksheetItem.batchno,
               Wk_WorksheetItem.wipused,
               Wk_WorksheetItem.wkwpid,
               Wk_WorksheetItem.wkwpcode,
               Wk_WorksheetItem.wkwpname,
               Wk_WorksheetItem.wkrownum,
               Wk_WorksheetItem.disannullisterid,
               Wk_WorksheetItem.disannullister,
               Wk_WorksheetItem.disannuldate,
               Wk_WorksheetItem.disannulmark,
               Wk_WorksheetItem.attributejson,
               Wk_WorksheetItem.reportqty,
               Wk_WorksheetItem.compqty,
               Wk_WorksheetItem.finishrate,
               Wk_WorksheetItem.secqty,
               Wk_WorksheetItem.panelwidth,
               Wk_WorksheetItem.panelheight,
               Wk_WorksheetItem.pcsinpanel,
               Wk_WorksheetItem.panelthick,
               Wk_WorksheetItem.matcode,
               Wk_WorksheetItem.matused,
               Wk_WorksheetItem.custom1,
               Wk_WorksheetItem.custom2,
               Wk_WorksheetItem.custom3,
               Wk_WorksheetItem.custom4,
               Wk_WorksheetItem.custom5,
               Wk_WorksheetItem.custom6,
               Wk_WorksheetItem.custom7,
               Wk_WorksheetItem.custom8,
               Wk_WorksheetItem.custom9,
               Wk_WorksheetItem.custom10,
               Wk_WorksheetItem.tenantid,
               Wk_WorksheetItem.revision
        FROM Mat_Goods
                 RIGHT JOIN Wk_WorksheetItem ON Mat_Goods.id = Wk_WorksheetItem.Goodsid
        where Wk_WorksheetItem.id = #{key}
          and Wk_WorksheetItem.Tenantid = #{tid} Limit 1
    </select>
    <select id="getGoodsEntityByGoodsUid" resultType="java.util.Map">
        SELECT Mat_Goods.id,
               Mat_Goods.goodsuid,
               Mat_Goods.goodsname,
               Mat_Goods.goodsspec,
               Mat_Goods.goodsunit,
               Mat_Goods.goodsstate,
               Mat_Goods.goodspinyin,
               Mat_Goods.versionnum,
               Mat_Goods.material,
               Mat_Goods.surface,
               Mat_Goods.barcode,
               Mat_Goods.safestock,
               Mat_Goods.inprice,
               Mat_Goods.outprice,
               Mat_Goods.groupid,
               Mat_Goods.fileguid,
               Mat_Goods.drawing,
               Mat_Goods.storeid,
               Mat_Goods.storelistname,
               Mat_Goods.storelistguid,
               Mat_Goods.ivquantity,
               Mat_Goods.ageprice,
               Mat_Goods.uidgroupguid,
               Mat_Goods.uidgroupcode,
               Mat_Goods.uidgroupname,
               Mat_Goods.uidgroupnum,
               Mat_Goods.partid,
               Mat_Goods.pid,
               Mat_Goods.puid,
               Mat_Goods.enabledmark,
               Mat_Goods.goodsphoto1,
               Mat_Goods.goodsphoto2,
               Mat_Goods.remark,
               Mat_Goods.createby,
               Mat_Goods.createbyid,
               Mat_Goods.createdate,
               Mat_Goods.lister,
               Mat_Goods.listerid,
               Mat_Goods.modifydate,
               Mat_Goods.deletemark,
               Mat_Goods.deletelister,
               Mat_Goods.deletelisterid,
               Mat_Goods.deletedate,
               Mat_Goods.batchmg,
               Mat_Goods.batchonly,
               Mat_Goods.skumark,
               Mat_Goods.packsnmark,
               Mat_Goods.virtualitem,
               Mat_Goods.bomid,
               Mat_Goods.quickcode,
               Mat_Goods.custom1,
               Mat_Goods.custom2,
               Mat_Goods.custom3,
               Mat_Goods.custom4,
               Mat_Goods.custom5,
               Mat_Goods.custom6,
               Mat_Goods.custom7,
               Mat_Goods.custom8,
               Mat_Goods.custom9,
               Mat_Goods.custom10,
               Mat_Goods.deptid,
               Mat_Goods.tenantid,
               Mat_Goods.tenantname,
               Mat_Goods.revision,
               Mat_Storage.storecode,
               Mat_Storage.storename
        FROM Mat_Goods
                 LEFT JOIN Mat_Storage ON Mat_Goods.Storeid = Mat_Storage.id
        where Mat_Goods.GoodsUid = #{key}
          and Mat_Goods.Tenantid = #{tid} Limit 1
    </select>

    <!--    /*客户订单金额排名*/-->
    <select id="getSumPageListByGroup" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatRequisitionitemdetailPojo">
        select App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               sum(CASE
                       WHEN Mat_Requisition.BillType IN ('生产领料', '其他领料', '领料单') THEN Mat_RequisitionItem.FinishCost
                       ELSE 0 - Mat_RequisitionItem.FinishCost END) as FinishCost,
               sum(CASE
                       WHEN Mat_Requisition.BillType IN ('生产领料', '其他领料', '领料单') THEN Mat_RequisitionItem.Quantity
                       ELSE 0 - Mat_RequisitionItem.Quantity END)   as Quantity
        FROM Mat_Requisition
                 RIGHT JOIN Mat_RequisitionItem
                            ON Mat_RequisitionItem.Pid = Mat_Requisition.id
                 LEFT JOIN App_Workgroup ON Mat_Requisition.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods ON Mat_RequisitionItem.Goodsid = Mat_Goods.id
        where Mat_RequisitionItem.disannulmark = 0
          and Mat_Requisition.Tenantid = #{tenantid}
          and (Mat_Requisition.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        group by App_Workgroup.Abbreviate, App_Workgroup.GroupName, App_Workgroup.GroupUid
        order by GroupUid
    </select>

    <!--    /*客户订单金额排名*/-->
    <select id="getSumPageListByGoods" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatRequisitionitemdetailPojo">
        select Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               sum(CASE
                       WHEN Mat_Requisition.BillType IN ('生产领料', '其他领料', '领料单') THEN Mat_RequisitionItem.FinishCost
                       ELSE 0 - Mat_RequisitionItem.FinishCost END) as FinishCost,
               sum(CASE
                       WHEN Mat_Requisition.BillType IN ('生产领料', '其他领料', '领料单') THEN Mat_RequisitionItem.Quantity
                       ELSE 0 - Mat_RequisitionItem.Quantity END)   as Quantity
        FROM Mat_Requisition
                 RIGHT JOIN Mat_RequisitionItem
                            ON Mat_RequisitionItem.Pid = Mat_Requisition.id
                 LEFT JOIN App_Workgroup ON Mat_Requisition.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods ON Mat_RequisitionItem.Goodsid = Mat_Goods.id
        where Mat_RequisitionItem.disannulmark = 0
          and Mat_Requisition.Tenantid = #{tenantid}
          and (Mat_Requisition.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        group by Mat_Goods.GoodsUid, Mat_Goods.GoodsName, Mat_Goods.GoodsSpec, Mat_Goods.GoodsUnit
        order by GoodsUid
    </select>

    <!--    刷新发货完成数 Eric 20220722-->
    <update id="updateWkWsMatFinishCount">
        update Wk_WorksheetMat
        SET FinishQty =COALESCE((SELECT SUM(Mat_RequisitionItem.Quantity)
                                 FROM Mat_RequisitionItem
                                 where Mat_RequisitionItem.WorkItemMatid = #{key}
                                   and Mat_RequisitionItem.Tenantid = #{tid}
                                   and Mat_RequisitionItem.Disannulmark = 0
                                ), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric 20220722-->

    <update id="updateWkScMatFinishCount">
        update Wk_SubcontractMat
        SET FinishQty =COALESCE((SELECT SUM(Mat_RequisitionItem.Quantity)
                                 FROM Mat_RequisitionItem
                                 where Mat_RequisitionItem.WorkItemMatid = #{key}
                                   and Mat_RequisitionItem.Tenantid = #{tid}
                                   and Mat_RequisitionItem.Disannulmark = 0
                                ), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateGoodsRequRemQty">
        update Mat_Goods
        SET RequRemQty = COALESCE((SELECT SUM(CASE
                                                  WHEN Mat_Requisition.BillType IN ('生产领料', '其他领料', '领料单')
                                                      THEN Mat_RequisitionItem.Quantity - Mat_RequisitionItem.FinishQty
                                                  ELSE Mat_RequisitionItem.FinishQty - Mat_RequisitionItem.Quantity END)
                                   FROM Mat_RequisitionItem
                                            LEFT OUTER JOIN Mat_Requisition ON Mat_RequisitionItem.pid =
                                                                               Mat_Requisition.id
                                   where Mat_RequisitionItem.Goodsid = #{key}
                                     and Mat_RequisitionItem.Closed = 0
                                     and Mat_RequisitionItem.DisannulMark = 0
                                     and Mat_Requisition.Tenantid = #{tid}), 0)+
                         COALESCE((SELECT SUM(Bus_MachiningItem.WkQty)
                                   FROM Bus_MachiningItem
                                            LEFT OUTER JOIN Bus_Machining ON Bus_MachiningItem.pid =
                                                                             Bus_Machining.id
                                   where Bus_MachiningItem.MatCode = #{goodsuid}
                                     and Bus_MachiningItem.MatUsed<![CDATA[!=]]>1
        and Bus_MachiningItem.Closed=0
        and Bus_MachiningItem.DisannulMark=0
        and Bus_Machining.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--查询指定行数据-->
    <select id="getMachPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatRequisitionitemdetailPojo">
        SELECT App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate,
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
        Mat_RequisitionItem.id,
        Mat_RequisitionItem.Pid,
        Mat_RequisitionItem.Goodsid,
        (CASE
        WHEN Mat_Requisition.BillType IN ('生产领料', '其他领料', '领料单') THEN Mat_RequisitionItem.FinishCost
        ELSE 0 - Mat_RequisitionItem.FinishCost END) as FinishCost,
        (CASE
        WHEN Mat_Requisition.BillType IN ('生产领料', '其他领料', '领料单') THEN Mat_RequisitionItem.Quantity
        ELSE 0 - Mat_RequisitionItem.Quantity END)   as Quantity,
        Mat_RequisitionItem.PlanDate,
        Mat_RequisitionItem.FinishQty,
        Mat_RequisitionItem.Remark,
        Mat_RequisitionItem.StateCode,
        Mat_RequisitionItem.StateDate,
        Mat_RequisitionItem.CiteUid,
        Mat_RequisitionItem.CiteItemid,
        Mat_RequisitionItem.Customer,
        Mat_RequisitionItem.CustPO,
        Mat_RequisitionItem.RowNum,
        Mat_RequisitionItem.BackQty,
        Mat_RequisitionItem.BackCost,
        Mat_RequisitionItem.Closed,
        Mat_RequisitionItem.StdQty,
        Mat_RequisitionItem.Storeid,
        Mat_RequisitionItem.Location,
        Mat_RequisitionItem.BatchNo,
        Mat_RequisitionItem.MachUid,
        Mat_RequisitionItem.MachItemid,
        Mat_RequisitionItem.MachItemGoodid,
        Mat_RequisitionItem.MachGroupid,
        Mat_RequisitionItem.MainPlanUid,
        Mat_RequisitionItem.MainPlanItemid,
        Mat_RequisitionItem.MainPlanItemGoodid,
        Mat_RequisitionItem.MrpUid,
        Mat_RequisitionItem.MrpItemid,
        Mat_RequisitionItem.MrpItemGoodid,
        Mat_RequisitionItem.WorkUid,
        Mat_RequisitionItem.WorkItemid,
        Mat_RequisitionItem.WorkItemGoodid,
        Mat_RequisitionItem.WorkItemMatid,
        Mat_RequisitionItem.ParentGoodsid,
        Mat_RequisitionItem.DisannulMark,
        Mat_RequisitionItem.SourceType,
        Mat_RequisitionItem.AttributeJson,
        Mat_RequisitionItem.Custom1,
        Mat_RequisitionItem.Custom2,
        Mat_RequisitionItem.Custom3,
        Mat_RequisitionItem.Custom4,
        Mat_RequisitionItem.Custom5,
        Mat_RequisitionItem.Custom6,
        Mat_RequisitionItem.Custom7,
        Mat_RequisitionItem.Custom8,
        Mat_RequisitionItem.Custom9,
        Mat_RequisitionItem.Custom10,
        Mat_RequisitionItem.Tenantid,
        Mat_RequisitionItem.TenantName,
        Mat_RequisitionItem.Revision,
        Mat_Requisition.RefNo,
        Mat_Requisition.BillType,
        Mat_Requisition.BillDate,
        Mat_Requisition.BillTitle,
        Mat_Requisition.BranthName,
        Mat_Requisition.Operator,
        Mat_Requisition.Summary,
        Mat_Requisition.CreateBy,
        Mat_Requisition.Lister,
        Mat_Requisition.Assessor,
        Mat_Requisition.ItemCount,
        Mat_Requisition.PickCount,
        Mat_Requisition.FinishCount,
        Mat_Requisition.PrintCount,
        Mat_Requisition.Projectid,
        Mat_Requisition.ProjCode,
        Mat_Requisition.ProjName,
        Bus_MachiningItem.AttributeJson as MachAttributeJson
        FROM
        Mat_Requisition
        LEFT JOIN App_Workgroup ON App_Workgroup.id = Mat_Requisition.Groupid
        RIGHT JOIN Mat_RequisitionItem ON Mat_RequisitionItem.Pid = Mat_Requisition.id
        LEFT JOIN Mat_Goods ON Mat_RequisitionItem.Goodsid = Mat_Goods.id
        LEFT JOIN Bus_MachiningItem ON Mat_RequisitionItem.MachItemid = Bus_MachiningItem.id
        where 1 = 1 and Mat_Requisition.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Requisition.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>

    <!--  查询领料单是否被引用  只查没被红冲单引用的-->
    <select id="getItemCiteBillName" resultType="java.lang.Integer">
        SELECT count(*)
        From Mat_AccessItem
                 Left join Mat_Access on Mat_Access.id = Mat_AccessItem.Pid
        where (Mat_Access.OrgUid = '' OR Mat_Access.OrgUid IS NULL)
          and Mat_AccessItem.CiteItemid = #{key}
          and Mat_AccessItem.Tenantid = #{tid}
    </select>
    <!--同步更新合并的所有销售订单子项->物料已领-->
    <update id="updateMergeMachMatUsed">
        UPDATE Bus_MachiningItem
        SET MatUsed=1
        WHERE Bus_MachiningItem.id in (select MachItemid from Wk_WipNoteMerge where Mergeid = #{wipid} and Tenantid = #{tid})
          and Bus_MachiningItem.Tenantid = #{tid}
    </update>
    <!--刷新打印次数-->
    <update id="updatePrintcount">
        update Mat_Requisition
        SET PrintCount = #{printcount}
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>


    <update id="updateMachMatUseQty">
        update Bus_MachiningItem
        SET MatUseQty =COALESCE((SELECT SUM(Mat_RequisitionItem.quantity)
                                 FROM Mat_RequisitionItem LEFT JOIN Mat_Requisition ON Mat_RequisitionItem.pid = Mat_Requisition.id
                                 where Mat_RequisitionItem.MachItemid = #{key}
                                   and Mat_RequisitionItem.DisannulMark = 0
                                   and Mat_RequisitionItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <select id="getSheetMatInfo" resultType="java.util.Map">
        select Quantity,FinishQty from Wk_WorksheetMat
        where id = #{workitemmatid}
          and Tenantid = #{tenantid}
    </select>

    <select id="getSubMatInfo" resultType="java.util.Map">
        select Quantity,FinishQty from Wk_SubcontractMat
        where id = #{workitemmatid}
          and Tenantid = #{tenantid}
    </select>

    <update id="updateMrpMatReqQty">
        update Wk_MrpItem
        SET MatReqQty =COALESCE((SELECT SUM(Mat_RequisitionItem.quantity)
                                 FROM Mat_RequisitionItem
                                          LEFT OUTER JOIN Mat_Requisition
                                                          ON Mat_RequisitionItem.pid = Mat_Requisition.id
                                 where Mat_RequisitionItem.MrpItemid = #{mrpitemid}
                                   and Mat_RequisitionItem.DisannulMark = 0
                                   and Mat_RequisitionItem.Closed = 0
                                   and Mat_RequisitionItem.Tenantid = #{tid}), 0)
        where id = #{mrpitemid}
          and Tenantid = #{tid}
    </update>

    <update id="updateMrpFinishCount">
        update Wk_Mrp
        SET FinishCount =COALESCE((SELECT COUNT(0)
        FROM Wk_MrpItem
        where Wk_MrpItem.Pid =
        (SELECT Pid FROM Wk_MrpItem where id = #{mrpitemid})
        and Wk_MrpItem.Tenantid = #{tid}
        and Wk_MrpItem.BuyPlanQty + Wk_MrpItem.BuyOrderQty + Wk_MrpItem.CustSuppQty +
        Wk_MrpItem.WkWsQty + Wk_MrpItem.WkScQty + Wk_MrpItem.OtherQty >=
        Wk_MrpItem.NeedQty), 0)
        where id = (SELECT Pid FROM Wk_MrpItem where id = #{mrpitemid})
        and Tenantid = #{tid}
    </update>
</mapper>

