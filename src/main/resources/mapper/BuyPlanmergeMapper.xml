<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyPlanmergeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyPlanmergePojo">
        <include refid="selectBuyPlanmergeVo"/>
        where Buy_PlanMerge.id = #{key} and Buy_PlanMerge.Tenantid=#{tid}
    </select>
    <sql id="selectBuyPlanmergeVo">
         select
id, Pid, ItemType, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice, TaxAmount, TaxTotal, ItemTaxrate, Price, Amount, PlanDate, Groupid, Remark, StateCode, StateDate, Closed, BuyQty, FinishQty, RowNum, CiteUid, CiteItemid, MachUid, MachItemid, MachGroupid, MainPlanUid, MainPlanItemid, MrpUid, Customer, CustPO, BatchNo, AttributeJson, SourceType, ItemCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Buy_PlanMerge
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.som.domain.pojo.BuyPlanmergePojo">
        <include refid="selectBuyPlanmergeVo"/>
         where 1 = 1 and Buy_PlanMerge.Tenantid =#{Tenantid} 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Buy_PlanMerge.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Buy_PlanMerge.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
   and Buy_PlanMerge.itemtype like concat('%', #{SearchPojo.itemtype}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   and Buy_PlanMerge.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   and Buy_PlanMerge.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   and Buy_PlanMerge.itemname like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   and Buy_PlanMerge.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   and Buy_PlanMerge.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
   and Buy_PlanMerge.groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Buy_PlanMerge.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
   and Buy_PlanMerge.statecode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
   and Buy_PlanMerge.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
   and Buy_PlanMerge.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
</if>
<if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
   and Buy_PlanMerge.machuid like concat('%', #{SearchPojo.machuid}, '%')
</if>
<if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
   and Buy_PlanMerge.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
</if>
<if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
   and Buy_PlanMerge.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
</if>
<if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
   and Buy_PlanMerge.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
</if>
<if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
   and Buy_PlanMerge.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
</if>
<if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
   and Buy_PlanMerge.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
</if>
<if test="SearchPojo.customer != null and SearchPojo.customer != ''">
   and Buy_PlanMerge.customer like concat('%', #{SearchPojo.customer}, '%')
</if>
<if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
   and Buy_PlanMerge.custpo like concat('%', #{SearchPojo.custpo}, '%')
</if>
<if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
   and Buy_PlanMerge.batchno like concat('%', #{SearchPojo.batchno}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   and Buy_PlanMerge.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Buy_PlanMerge.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Buy_PlanMerge.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Buy_PlanMerge.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Buy_PlanMerge.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Buy_PlanMerge.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Buy_PlanMerge.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Buy_PlanMerge.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Buy_PlanMerge.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Buy_PlanMerge.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Buy_PlanMerge.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Buy_PlanMerge.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
   or Buy_PlanMerge.ItemType like concat('%', #{SearchPojo.itemtype}, '%')
</if>
<if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
   or Buy_PlanMerge.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
</if>
<if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
   or Buy_PlanMerge.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
</if>
<if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
   or Buy_PlanMerge.ItemName like concat('%', #{SearchPojo.itemname}, '%')
</if>
<if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
   or Buy_PlanMerge.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
</if>
<if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
   or Buy_PlanMerge.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
</if>
<if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
   or Buy_PlanMerge.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Buy_PlanMerge.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
   or Buy_PlanMerge.StateCode like concat('%', #{SearchPojo.statecode}, '%')
</if>
<if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
   or Buy_PlanMerge.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
</if>
<if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
   or Buy_PlanMerge.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
</if>
<if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
   or Buy_PlanMerge.MachUid like concat('%', #{SearchPojo.machuid}, '%')
</if>
<if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
   or Buy_PlanMerge.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
</if>
<if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
   or Buy_PlanMerge.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
</if>
<if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
   or Buy_PlanMerge.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
</if>
<if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
   or Buy_PlanMerge.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
</if>
<if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
   or Buy_PlanMerge.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
</if>
<if test="SearchPojo.customer != null and SearchPojo.customer != ''">
   or Buy_PlanMerge.Customer like concat('%', #{SearchPojo.customer}, '%')
</if>
<if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
   or Buy_PlanMerge.CustPO like concat('%', #{SearchPojo.custpo}, '%')
</if>
<if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
   or Buy_PlanMerge.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
</if>
<if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
   or Buy_PlanMerge.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Buy_PlanMerge.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Buy_PlanMerge.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Buy_PlanMerge.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Buy_PlanMerge.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Buy_PlanMerge.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Buy_PlanMerge.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Buy_PlanMerge.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Buy_PlanMerge.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Buy_PlanMerge.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Buy_PlanMerge.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.BuyPlanmergePojo">
        select Buy_PlanMerge.id,
               Buy_PlanMerge.Pid,
               Buy_PlanMerge.ItemType,
               Buy_PlanMerge.Goodsid,
               Buy_PlanMerge.ItemCode,
               Buy_PlanMerge.ItemName,
               Buy_PlanMerge.ItemSpec,
               Buy_PlanMerge.ItemUnit,
               Buy_PlanMerge.Quantity,
               Buy_PlanMerge.TaxPrice,
               Buy_PlanMerge.TaxAmount,
               Buy_PlanMerge.TaxTotal,
               Buy_PlanMerge.ItemTaxrate,
               Buy_PlanMerge.Price,
               Buy_PlanMerge.Amount,
               Buy_PlanMerge.PlanDate,
               Buy_PlanMerge.Groupid,
               Buy_PlanMerge.Remark,
               Buy_PlanMerge.StateCode,
               Buy_PlanMerge.StateDate,
               Buy_PlanMerge.Closed,
               Buy_PlanMerge.BuyQty,
               Buy_PlanMerge.FinishQty,
               Buy_PlanMerge.RowNum,
               Buy_PlanMerge.CiteUid,
               Buy_PlanMerge.CiteItemid,
               Buy_PlanMerge.MachUid,
               Buy_PlanMerge.MachItemid,
               Buy_PlanMerge.MachGroupid,
               Buy_PlanMerge.MainPlanUid,
               Buy_PlanMerge.MainPlanItemid,
               Buy_PlanMerge.MrpUid,
               Buy_PlanMerge.Customer,
               Buy_PlanMerge.CustPO,
               Buy_PlanMerge.BatchNo,
               Buy_PlanMerge.AttributeJson,
               Buy_PlanMerge.SourceType,
               Buy_PlanMerge.ItemCount,
               Buy_PlanMerge.Custom1,
               Buy_PlanMerge.Custom2,
               Buy_PlanMerge.Custom3,
               Buy_PlanMerge.Custom4,
               Buy_PlanMerge.Custom5,
               Buy_PlanMerge.Custom6,
               Buy_PlanMerge.Custom7,
               Buy_PlanMerge.Custom8,
               Buy_PlanMerge.Custom9,
               Buy_PlanMerge.Custom10,
               Buy_PlanMerge.Tenantid,
               Buy_PlanMerge.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Surface,
               Mat_Goods.Drawing,
               Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Buy_PlanMerge
                            ON Buy_PlanMerge.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods ON Buy_PlanMerge.Goodsid = Mat_Goods.id
        where Buy_PlanMerge.Pid = #{Pid}
          and Buy_PlanMerge.Tenantid = #{tid}
        order by RowNum
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Buy_PlanMerge(id, Pid, ItemType, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice, TaxAmount, TaxTotal, ItemTaxrate, Price, Amount, PlanDate, Groupid, Remark, StateCode, StateDate, Closed, BuyQty, FinishQty, RowNum, CiteUid, CiteItemid, MachUid, MachItemid, MachGroupid, MainPlanUid, MainPlanItemid, MrpUid, Customer, CustPO, BatchNo, AttributeJson, SourceType, ItemCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{itemtype}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{taxprice}, #{taxamount}, #{taxtotal}, #{itemtaxrate}, #{price}, #{amount}, #{plandate}, #{groupid}, #{remark}, #{statecode}, #{statedate}, #{closed}, #{buyqty}, #{finishqty}, #{rownum}, #{citeuid}, #{citeitemid}, #{machuid}, #{machitemid}, #{machgroupid}, #{mainplanuid}, #{mainplanitemid}, #{mrpuid}, #{customer}, #{custpo}, #{batchno}, #{attributejson}, #{sourcetype}, #{itemcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_PlanMerge
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="itemtype != null ">
                ItemType = #{itemtype},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="groupid != null ">
                Groupid = #{groupid},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="buyqty != null">
                BuyQty = #{buyqty},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="itemcount != null">
                ItemCount = #{itemcount},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Buy_PlanMerge where id = #{key} and Tenantid=#{tid}
    </delete>

    <delete id="deleteAllByPid">
        delete from Buy_PlanMerge where Pid = #{Pid} and Tenantid=#{tid}
    </delete>
</mapper>

