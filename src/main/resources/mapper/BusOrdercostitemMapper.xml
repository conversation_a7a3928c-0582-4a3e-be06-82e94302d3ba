<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BusOrdercostitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BusOrdercostitemPojo">
        SELECT
            Bus_OrderCostItem.id,
            Bus_OrderCostItem.Pid,
            Bus_OrderCostItem.Goodsid,
            Bus_OrderCostItem.ItemCode,
            Bus_OrderCostItem.ItemName,
            Bus_OrderCostItem.ItemSpec,
            Bus_OrderCostItem.ItemUnit,
            Bus_OrderCostItem.Quantity,
            Bus_OrderCostItem.StdPrice,
            Bus_OrderCostItem.StdAmount,
            Bus_OrderCostItem.Rebate,
            Bus_OrderCostItem.RebateSec,
            Bus_OrderCostItem.Price,
            Bus_OrderCostItem.Amount,
            Bus_OrderCostItem.ItemTaxrate,
            Bus_OrderCostItem.TaxPrice,
            Bus_OrderCostItem.TaxTotal,
            Bus_OrderCostItem.TaxAmount,
            Bus_OrderCostItem.PlanDate,
            Bus_OrderCostItem.RowNum,
            Bus_OrderCostItem.Remark,
            Bus_OrderCostItem.DisannulMark,
            Bus_OrderCostItem.DisannulListerid,
            Bus_OrderCostItem.DisannulLister,
            Bus_OrderCostItem.DisannulDate,
            Bus_OrderCostItem.VirtualItem,
            Bus_OrderCostItem.AttributeJson,
            Bus_OrderCostItem.CostItemJson,
            Bus_OrderCostItem.CostGroupJson,
            Bus_OrderCostItem.Closed,
            Bus_OrderCostItem.MachMark,
            Bus_OrderCostItem.CostItem1,
            Bus_OrderCostItem.CostItem2,
            Bus_OrderCostItem.CostItem3,
            Bus_OrderCostItem.CostItem4,
            Bus_OrderCostItem.CostItem5,
            Bus_OrderCostItem.CostItem6,
            Bus_OrderCostItem.CostItem7,
            Bus_OrderCostItem.CostItem8,
            Bus_OrderCostItem.CostItem9,
            Bus_OrderCostItem.CostItem10,
            Bus_OrderCostItem.Tenantid,
            Bus_OrderCostItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10
        FROM
            Bus_OrderCostItem
                LEFT JOIN Mat_Goods ON Bus_OrderCostItem.Goodsid = Mat_Goods.id
        where Bus_OrderCostItem.id = #{key}
          and Bus_OrderCostItem.Tenantid = #{tid}
    </select>
    <sql id="selectBusOrdercostitemVo">
        SELECT
            Bus_OrderCostItem.id,
            Bus_OrderCostItem.Pid,
            Bus_OrderCostItem.Goodsid,
            Bus_OrderCostItem.ItemCode,
            Bus_OrderCostItem.ItemName,
            Bus_OrderCostItem.ItemSpec,
            Bus_OrderCostItem.ItemUnit,
            Bus_OrderCostItem.Quantity,
            Bus_OrderCostItem.StdPrice,
            Bus_OrderCostItem.StdAmount,
            Bus_OrderCostItem.Rebate,
            Bus_OrderCostItem.RebateSec,
            Bus_OrderCostItem.Price,
            Bus_OrderCostItem.Amount,
            Bus_OrderCostItem.ItemTaxrate,
            Bus_OrderCostItem.TaxPrice,
            Bus_OrderCostItem.TaxTotal,
            Bus_OrderCostItem.TaxAmount,
            Bus_OrderCostItem.PlanDate,
            Bus_OrderCostItem.RowNum,
            Bus_OrderCostItem.Remark,
            Bus_OrderCostItem.DisannulMark,
            Bus_OrderCostItem.DisannulListerid,
            Bus_OrderCostItem.DisannulLister,
            Bus_OrderCostItem.DisannulDate,
            Bus_OrderCostItem.VirtualItem,
            Bus_OrderCostItem.AttributeJson,
            Bus_OrderCostItem.CostItemJson,
            Bus_OrderCostItem.CostGroupJson,
            Bus_OrderCostItem.Closed,
            Bus_OrderCostItem.MachMark,
            Bus_OrderCostItem.CostItem1,
            Bus_OrderCostItem.CostItem2,
            Bus_OrderCostItem.CostItem3,
            Bus_OrderCostItem.CostItem4,
            Bus_OrderCostItem.CostItem5,
            Bus_OrderCostItem.CostItem6,
            Bus_OrderCostItem.CostItem7,
            Bus_OrderCostItem.CostItem8,
            Bus_OrderCostItem.CostItem9,
            Bus_OrderCostItem.CostItem10,
            Bus_OrderCostItem.Tenantid,
            Bus_OrderCostItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10
        FROM
            Bus_OrderCostItem
                LEFT JOIN Mat_Goods ON Bus_OrderCostItem.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusOrdercostitemPojo">
        <include refid="selectBusOrdercostitemVo"/>
        where 1 = 1 and Bus_OrderCostItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_OrderCostItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Bus_OrderCostItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Bus_OrderCostItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Bus_OrderCostItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Bus_OrderCostItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Bus_OrderCostItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Bus_OrderCostItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Bus_OrderCostItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Bus_OrderCostItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Bus_OrderCostItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Bus_OrderCostItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.costitemjson != null and SearchPojo.costitemjson != ''">
            and Bus_OrderCostItem.costitemjson like concat('%', #{SearchPojo.costitemjson}, '%')
        </if>
        <if test="SearchPojo.costgroupjson != null and SearchPojo.costgroupjson != ''">
            and Bus_OrderCostItem.costgroupjson like concat('%', #{SearchPojo.costgroupjson}, '%')
        </if>
        <if test="SearchPojo.costitem1 != null and SearchPojo.costitem1 != ''">
            and Bus_OrderCostItem.costitem1 like concat('%', #{SearchPojo.costitem1}, '%')
        </if>
        <if test="SearchPojo.costitem2 != null and SearchPojo.costitem2 != ''">
            and Bus_OrderCostItem.costitem2 like concat('%', #{SearchPojo.costitem2}, '%')
        </if>
        <if test="SearchPojo.costitem3 != null and SearchPojo.costitem3 != ''">
            and Bus_OrderCostItem.costitem3 like concat('%', #{SearchPojo.costitem3}, '%')
        </if>
        <if test="SearchPojo.costitem4 != null and SearchPojo.costitem4 != ''">
            and Bus_OrderCostItem.costitem4 like concat('%', #{SearchPojo.costitem4}, '%')
        </if>
        <if test="SearchPojo.costitem5 != null and SearchPojo.costitem5 != ''">
            and Bus_OrderCostItem.costitem5 like concat('%', #{SearchPojo.costitem5}, '%')
        </if>
        <if test="SearchPojo.costitem6 != null and SearchPojo.costitem6 != ''">
            and Bus_OrderCostItem.costitem6 like concat('%', #{SearchPojo.costitem6}, '%')
        </if>
        <if test="SearchPojo.costitem7 != null and SearchPojo.costitem7 != ''">
            and Bus_OrderCostItem.costitem7 like concat('%', #{SearchPojo.costitem7}, '%')
        </if>
        <if test="SearchPojo.costitem8 != null and SearchPojo.costitem8 != ''">
            and Bus_OrderCostItem.costitem8 like concat('%', #{SearchPojo.costitem8}, '%')
        </if>
        <if test="SearchPojo.costitem9 != null and SearchPojo.costitem9 != ''">
            and Bus_OrderCostItem.costitem9 like concat('%', #{SearchPojo.costitem9}, '%')
        </if>
        <if test="SearchPojo.costitem10 != null and SearchPojo.costitem10 != ''">
            and Bus_OrderCostItem.costitem10 like concat('%', #{SearchPojo.costitem10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Bus_OrderCostItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Bus_OrderCostItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Bus_OrderCostItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Bus_OrderCostItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Bus_OrderCostItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Bus_OrderCostItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Bus_OrderCostItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Bus_OrderCostItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Bus_OrderCostItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Bus_OrderCostItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.costitemjson != null and SearchPojo.costitemjson != ''">
                or Bus_OrderCostItem.CostItemJson like concat('%', #{SearchPojo.costitemjson}, '%')
            </if>
            <if test="SearchPojo.costgroupjson != null and SearchPojo.costgroupjson != ''">
                or Bus_OrderCostItem.CostGroupJson like concat('%', #{SearchPojo.costgroupjson}, '%')
            </if>
            <if test="SearchPojo.costitem1 != null and SearchPojo.costitem1 != ''">
                or Bus_OrderCostItem.CostItem1 like concat('%', #{SearchPojo.costitem1}, '%')
            </if>
            <if test="SearchPojo.costitem2 != null and SearchPojo.costitem2 != ''">
                or Bus_OrderCostItem.CostItem2 like concat('%', #{SearchPojo.costitem2}, '%')
            </if>
            <if test="SearchPojo.costitem3 != null and SearchPojo.costitem3 != ''">
                or Bus_OrderCostItem.CostItem3 like concat('%', #{SearchPojo.costitem3}, '%')
            </if>
            <if test="SearchPojo.costitem4 != null and SearchPojo.costitem4 != ''">
                or Bus_OrderCostItem.CostItem4 like concat('%', #{SearchPojo.costitem4}, '%')
            </if>
            <if test="SearchPojo.costitem5 != null and SearchPojo.costitem5 != ''">
                or Bus_OrderCostItem.CostItem5 like concat('%', #{SearchPojo.costitem5}, '%')
            </if>
            <if test="SearchPojo.costitem6 != null and SearchPojo.costitem6 != ''">
                or Bus_OrderCostItem.CostItem6 like concat('%', #{SearchPojo.costitem6}, '%')
            </if>
            <if test="SearchPojo.costitem7 != null and SearchPojo.costitem7 != ''">
                or Bus_OrderCostItem.CostItem7 like concat('%', #{SearchPojo.costitem7}, '%')
            </if>
            <if test="SearchPojo.costitem8 != null and SearchPojo.costitem8 != ''">
                or Bus_OrderCostItem.CostItem8 like concat('%', #{SearchPojo.costitem8}, '%')
            </if>
            <if test="SearchPojo.costitem9 != null and SearchPojo.costitem9 != ''">
                or Bus_OrderCostItem.CostItem9 like concat('%', #{SearchPojo.costitem9}, '%')
            </if>
            <if test="SearchPojo.costitem10 != null and SearchPojo.costitem10 != ''">
                or Bus_OrderCostItem.CostItem10 like concat('%', #{SearchPojo.costitem10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.BusOrdercostitemPojo">
        SELECT
            Bus_OrderCostItem.id,
            Bus_OrderCostItem.Pid,
            Bus_OrderCostItem.Goodsid,
            Bus_OrderCostItem.ItemCode,
            Bus_OrderCostItem.ItemName,
            Bus_OrderCostItem.ItemSpec,
            Bus_OrderCostItem.ItemUnit,
            Bus_OrderCostItem.Quantity,
            Bus_OrderCostItem.StdPrice,
            Bus_OrderCostItem.StdAmount,
            Bus_OrderCostItem.Rebate,
            Bus_OrderCostItem.RebateSec,
            Bus_OrderCostItem.Price,
            Bus_OrderCostItem.Amount,
            Bus_OrderCostItem.ItemTaxrate,
            Bus_OrderCostItem.TaxPrice,
            Bus_OrderCostItem.TaxTotal,
            Bus_OrderCostItem.TaxAmount,
            Bus_OrderCostItem.PlanDate,
            Bus_OrderCostItem.RowNum,
            Bus_OrderCostItem.Remark,
            Bus_OrderCostItem.DisannulMark,
            Bus_OrderCostItem.DisannulListerid,
            Bus_OrderCostItem.DisannulLister,
            Bus_OrderCostItem.DisannulDate,
            Bus_OrderCostItem.VirtualItem,
            Bus_OrderCostItem.AttributeJson,
            Bus_OrderCostItem.CostItemJson,
            Bus_OrderCostItem.CostGroupJson,
            Bus_OrderCostItem.Closed,
            Bus_OrderCostItem.MachMark,
            Bus_OrderCostItem.CostItem1,
            Bus_OrderCostItem.CostItem2,
            Bus_OrderCostItem.CostItem3,
            Bus_OrderCostItem.CostItem4,
            Bus_OrderCostItem.CostItem5,
            Bus_OrderCostItem.CostItem6,
            Bus_OrderCostItem.CostItem7,
            Bus_OrderCostItem.CostItem8,
            Bus_OrderCostItem.CostItem9,
            Bus_OrderCostItem.CostItem10,
            Bus_OrderCostItem.Tenantid,
            Bus_OrderCostItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid
        FROM
            Bus_OrderCostItem
                LEFT JOIN Mat_Goods ON Bus_OrderCostItem.Goodsid = Mat_Goods.id
        where Bus_OrderCostItem.Pid = #{Pid}
          and Bus_OrderCostItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_OrderCostItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, StdPrice,
                                      StdAmount, Rebate, RebateSec, Price, Amount, ItemTaxrate, TaxPrice, TaxTotal,
                                      TaxAmount, PlanDate, RowNum, Remark, DisannulMark, DisannulListerid,
                                      DisannulLister, DisannulDate, VirtualItem, AttributeJson, CostItemJson,
                                      CostGroupJson, Closed, MachMark, CostItem1, CostItem2, CostItem3, CostItem4,
                                      CostItem5, CostItem6, CostItem7, CostItem8, CostItem9, CostItem10, Tenantid,
                                      Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{stdprice},
                #{stdamount}, #{rebate}, #{rebatesec}, #{price}, #{amount}, #{itemtaxrate}, #{taxprice}, #{taxtotal},
                #{taxamount}, #{plandate}, #{rownum}, #{remark}, #{disannulmark}, #{disannullisterid},
                #{disannullister}, #{disannuldate}, #{virtualitem}, #{attributejson}, #{costitemjson}, #{costgroupjson},
                #{closed}, #{machmark}, #{costitem1}, #{costitem2}, #{costitem3}, #{costitem4}, #{costitem5},
                #{costitem6}, #{costitem7}, #{costitem8}, #{costitem9}, #{costitem10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_OrderCostItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="stdprice != null">
                StdPrice = #{stdprice},
            </if>
            <if test="stdamount != null">
                StdAmount = #{stdamount},
            </if>
            <if test="rebate != null">
                Rebate = #{rebate},
            </if>
            <if test="rebatesec != null">
                RebateSec = #{rebatesec},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="costitemjson != null ">
                CostItemJson = #{costitemjson},
            </if>
            <if test="costgroupjson != null ">
                CostGroupJson = #{costgroupjson},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="machmark != null">
                MachMark = #{machmark},
            </if>
            <if test="costitem1 != null ">
                CostItem1 = #{costitem1},
            </if>
            <if test="costitem2 != null ">
                CostItem2 = #{costitem2},
            </if>
            <if test="costitem3 != null ">
                CostItem3 = #{costitem3},
            </if>
            <if test="costitem4 != null ">
                CostItem4 = #{costitem4},
            </if>
            <if test="costitem5 != null ">
                CostItem5 = #{costitem5},
            </if>
            <if test="costitem6 != null ">
                CostItem6 = #{costitem6},
            </if>
            <if test="costitem7 != null ">
                CostItem7 = #{costitem7},
            </if>
            <if test="costitem8 != null ">
                CostItem8 = #{costitem8},
            </if>
            <if test="costitem9 != null ">
                CostItem9 = #{costitem9},
            </if>
            <if test="costitem10 != null ">
                CostItem10 = #{costitem10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_OrderCostItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

