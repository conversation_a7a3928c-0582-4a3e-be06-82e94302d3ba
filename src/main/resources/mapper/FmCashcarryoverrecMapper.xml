<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.FmCashcarryoverrecMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.FmCashcarryoverrecPojo">
        select id,
               CarryYear,
               CarryMonth,
               StartDate,
               EndDate,
               Operator,
               Operatorid,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               BillOpenAmount,
               BillInAmount,
               BillOutAmount,
               BillCloseAmount,
               PrintCount,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Fm_CashCarryoverRec
        where Fm_CashCarryoverRec.id = #{key}
          and Fm_CashCarryoverRec.Tenantid = #{tid}
    </select>
    <sql id="selectFmCashcarryoverrecVo">
        select id,
               CarryYear,
               CarryMonth,
               StartDate,
               EndDate,
               Operator,
               Operatorid,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               BillOpenAmount,
               BillInAmount,
               BillOutAmount,
               BillCloseAmount,
               PrintCount,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Fm_CashCarryoverRec
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.som.domain.pojo.FmCashcarryoverrecPojo">
        <include refid="selectFmCashcarryoverrecVo"/>
         where 1 = 1 and Fm_CashCarryoverRec.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Fm_CashCarryoverRec.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.operator != null ">
   and Fm_CashCarryoverRec.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Fm_CashCarryoverRec.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Fm_CashCarryoverRec.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Fm_CashCarryoverRec.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Fm_CashCarryoverRec.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Fm_CashCarryoverRec.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Fm_CashCarryoverRec.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Fm_CashCarryoverRec.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Fm_CashCarryoverRec.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Fm_CashCarryoverRec.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Fm_CashCarryoverRec.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Fm_CashCarryoverRec.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Fm_CashCarryoverRec.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Fm_CashCarryoverRec.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Fm_CashCarryoverRec.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Fm_CashCarryoverRec.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Fm_CashCarryoverRec.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Fm_CashCarryoverRec.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
         <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
             <if test="SearchPojo.operator != null ">
                 or Fm_CashCarryoverRec.Operator like concat('%', #{SearchPojo.operator}, '%')
             </if>
             <if test="SearchPojo.operatorid != null ">
                 or Fm_CashCarryoverRec.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
             </if>
             <if test="SearchPojo.remark != null ">
                 or Fm_CashCarryoverRec.Remark like concat('%', #{SearchPojo.remark}, '%')
             </if>
             <if test="SearchPojo.createby != null ">
                 or Fm_CashCarryoverRec.CreateBy like concat('%', #{SearchPojo.createby}, '%')
             </if>
             <if test="SearchPojo.createbyid != null ">
                 or Fm_CashCarryoverRec.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
             </if>
             <if test="SearchPojo.lister != null ">
                 or Fm_CashCarryoverRec.Lister like concat('%', #{SearchPojo.lister}, '%')
             </if>
             <if test="SearchPojo.listerid != null ">
                 or Fm_CashCarryoverRec.Listerid like concat('%', #{SearchPojo.listerid}, '%')
             </if>
             <if test="SearchPojo.custom1 != null ">
                 or Fm_CashCarryoverRec.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
             </if>
             <if test="SearchPojo.custom2 != null ">
                 or Fm_CashCarryoverRec.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
             </if>
             <if test="SearchPojo.custom3 != null ">
                 or Fm_CashCarryoverRec.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
             </if>
             <if test="SearchPojo.custom4 != null ">
                 or Fm_CashCarryoverRec.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
             </if>
             <if test="SearchPojo.custom5 != null ">
                 or Fm_CashCarryoverRec.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
             </if>
             <if test="SearchPojo.custom6 != null ">
                 or Fm_CashCarryoverRec.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
             </if>
             <if test="SearchPojo.custom7 != null ">
                 or Fm_CashCarryoverRec.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
             </if>
             <if test="SearchPojo.custom8 != null ">
                 or Fm_CashCarryoverRec.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
             </if>
             <if test="SearchPojo.custom9 != null ">
                 or Fm_CashCarryoverRec.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
             </if>
             <if test="SearchPojo.custom10 != null ">
                 or Fm_CashCarryoverRec.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
             </if>
             <if test="SearchPojo.tenantname != null ">
                 or Fm_CashCarryoverRec.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
             </if>
         </trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Fm_CashCarryoverRec(id, CarryYear, CarryMonth, StartDate, EndDate, Operator, Operatorid, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, BillOpenAmount, BillInAmount, BillOutAmount, BillCloseAmount, PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{carryyear}, #{carrymonth}, #{startdate}, #{enddate}, #{operator}, #{operatorid}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{billopenamount}, #{billinamount}, #{billoutamount}, #{billcloseamount}, #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Fm_CashCarryoverRec
        <set>
            <if test="carryyear != null">
                CarryYear =#{carryyear},
            </if>
            <if test="carrymonth != null">
                CarryMonth =#{carrymonth},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="billopenamount != null">
                BillOpenAmount =#{billopenamount},
            </if>
            <if test="billinamount != null">
                BillInAmount =#{billinamount},
            </if>
            <if test="billoutamount != null">
                BillOutAmount =#{billoutamount},
            </if>
            <if test="billcloseamount != null">
                BillCloseAmount =#{billcloseamount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Fm_CashCarryoverRec where id = #{key} and Tenantid=#{tid}
    </delete>

    <!--查询单个-->
    <select id="getEntityByMax" resultType="inks.service.sa.som.domain.pojo.FmCashcarryoverrecPojo">
        <include refid="selectFmCashcarryoverrecVo"/>
        where Fm_CashCarryoverRec.Tenantid = #{tid}
        Order By RowNum Desc LIMIT 1
    </select>

</mapper>

