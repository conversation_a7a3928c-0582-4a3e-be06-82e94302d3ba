<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyOrderitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyOrderitemPojo">
        <include refid="selectBuyOrderitemVo"/>
        where Buy_OrderItem.id = #{key}
          and Buy_OrderItem.Tenantid = #{tid}
    </select>
    <sql id="selectBuyOrderitemVo">
        SELECT
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10,
            Buy_OrderItem.id,
            Buy_OrderItem.Pid,
            Buy_OrderItem.Goodsid,
            Buy_OrderItem.ItemCode,
            Buy_OrderItem.ItemName,
            Buy_OrderItem.ItemSpec,
            Buy_OrderItem.ItemUnit,
            Buy_OrderItem.Quantity,
            Buy_OrderItem.TaxPrice,
            Buy_OrderItem.TaxAmount,
            Buy_OrderItem.ItemTaxrate,
            Buy_OrderItem.TaxTotal,
            Buy_OrderItem.Price,
            Buy_OrderItem.Amount,
            Buy_OrderItem.PlanDate,
            Buy_OrderItem.MaxQty,
            Buy_OrderItem.FinishQty,
            Buy_OrderItem.Remark,
            Buy_OrderItem.CiteUid,
            Buy_OrderItem.CiteItemid,
            Buy_OrderItem.StateCode,
            Buy_OrderItem.StateDate,
            Buy_OrderItem.Closed,
            Buy_OrderItem.RowNum,
            Buy_OrderItem.OrgGoodsid,
            Buy_OrderItem.SubRate,
            Buy_OrderItem.MachUid,
            Buy_OrderItem.MachItemid,
            Buy_OrderItem.MachBatch,
            Buy_OrderItem.MachGroupid,
            Buy_OrderItem.MainPlanUid,
            Buy_OrderItem.MainPlanItemid,
            Buy_OrderItem.MrpUid,
            Buy_OrderItem.MrpItemid,
            Buy_OrderItem.MrpObjGoodsid,
            Buy_OrderItem.VirtualItem,
            Buy_OrderItem.Customer,
            Buy_OrderItem.CustPO,
            Buy_OrderItem.BatchNo,
            Buy_OrderItem.DisannulMark,
            Buy_OrderItem.DisannulListerid,
            Buy_OrderItem.DisannulLister,
            Buy_OrderItem.DisannulDate,
            Buy_OrderItem.AttributeJson,
            Buy_OrderItem.SourceType,
            Buy_OrderItem.StdPrice,
            Buy_OrderItem.StdAmount,
            Buy_OrderItem.Rebate,
            Buy_OrderItem.PassedQty,
            Buy_OrderItem.InStoreQty,
            Buy_OrderItem.InvoQty,
            Buy_OrderItem.InvoClosed,
            Buy_OrderItem.AvgInvoAmt,
            Buy_OrderItem.AvgFirstAmt,
            Buy_OrderItem.AvgLastAmt,
            Buy_OrderItem.InvoAmt,
            Buy_OrderItem.Custom1,
            Buy_OrderItem.Custom2,
            Buy_OrderItem.Custom3,
            Buy_OrderItem.Custom4,
            Buy_OrderItem.Custom5,
            Buy_OrderItem.Custom6,
            Buy_OrderItem.Custom7,
            Buy_OrderItem.Custom8,
            Buy_OrderItem.Custom9,
            Buy_OrderItem.Custom10,
            Buy_OrderItem.Custom11,
            Buy_OrderItem.Custom12,
            Buy_OrderItem.Custom13,
            Buy_OrderItem.Custom14,
            Buy_OrderItem.Custom15,
            Buy_OrderItem.Custom16,
            Buy_OrderItem.Custom17,
            Buy_OrderItem.Custom18,
            Buy_OrderItem.Tenantid,
            Buy_OrderItem.Revision,
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            g2.GoodsName as MrpObjGoodsName,
            g2.GoodsUid as MrpObjGoodsUid
        FROM
            Mat_Goods
                RIGHT JOIN Buy_OrderItem ON Buy_OrderItem.Goodsid = Mat_Goods.id
                LEFT JOIN App_Workgroup ON Buy_OrderItem.MachGroupid = App_Workgroup.id
                LEFT JOIN Mat_Goods g2 ON Buy_OrderItem.MrpObjGoodsid = g2.id
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.BuyOrderitemPojo">
        <include refid="selectBuyOrderitemVo"/>
        where Buy_OrderItem.Pid = #{Pid}
        order by RowNum
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyOrderitemPojo">
        <include refid="selectBuyOrderitemVo"/>
        where 1 = 1 and Buy_OrderItem.Tenantid =#{tenantid}
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_OrderItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Buy_OrderItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Buy_OrderItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Buy_OrderItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Buy_OrderItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Buy_OrderItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Buy_OrderItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Buy_OrderItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Buy_OrderItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Buy_OrderItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Buy_OrderItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.orggoodsid != null and SearchPojo.orggoodsid != ''">
            and Buy_OrderItem.orggoodsid like concat('%', #{SearchPojo.orggoodsid}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Buy_OrderItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Buy_OrderItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Buy_OrderItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Buy_OrderItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Buy_OrderItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Buy_OrderItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Buy_OrderItem.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Buy_OrderItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Buy_OrderItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Buy_OrderItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Buy_OrderItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Buy_OrderItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Buy_OrderItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Buy_OrderItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Buy_OrderItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Buy_OrderItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Buy_OrderItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Buy_OrderItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Buy_OrderItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Buy_OrderItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Buy_OrderItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Buy_OrderItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Buy_OrderItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.custom11 != null and SearchPojo.custom11 != ''">
            and Buy_OrderItem.custom11 like concat('%', #{SearchPojo.custom11}, '%')
        </if>
        <if test="SearchPojo.custom12 != null and SearchPojo.custom12 != ''">
            and Buy_OrderItem.custom12 like concat('%', #{SearchPojo.custom12}, '%')
        </if>
        <if test="SearchPojo.custom13 != null and SearchPojo.custom13 != ''">
            and Buy_OrderItem.custom13 like concat('%', #{SearchPojo.custom13}, '%')
        </if>
        <if test="SearchPojo.custom14 != null and SearchPojo.custom14 != ''">
            and Buy_OrderItem.custom14 like concat('%', #{SearchPojo.custom14}, '%')
        </if>
        <if test="SearchPojo.custom15 != null and SearchPojo.custom15 != ''">
            and Buy_OrderItem.custom15 like concat('%', #{SearchPojo.custom15}, '%')
        </if>
        <if test="SearchPojo.custom16 != null and SearchPojo.custom16 != ''">
            and Buy_OrderItem.custom16 like concat('%', #{SearchPojo.custom16}, '%')
        </if>
        <if test="SearchPojo.custom17 != null and SearchPojo.custom17 != ''">
            and Buy_OrderItem.custom17 like concat('%', #{SearchPojo.custom17}, '%')
        </if>
        <if test="SearchPojo.custom18 != null and SearchPojo.custom18 != ''">
            and Buy_OrderItem.custom18 like concat('%', #{SearchPojo.custom18}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Buy_OrderItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Buy_OrderItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Buy_OrderItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Buy_OrderItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Buy_OrderItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Buy_OrderItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Buy_OrderItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Buy_OrderItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Buy_OrderItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Buy_OrderItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.orggoodsid != null and SearchPojo.orggoodsid != ''">
                or Buy_OrderItem.OrgGoodsid like concat('%', #{SearchPojo.orggoodsid}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Buy_OrderItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Buy_OrderItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Buy_OrderItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Buy_OrderItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Buy_OrderItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Buy_OrderItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Buy_OrderItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Buy_OrderItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Buy_OrderItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Buy_OrderItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Buy_OrderItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Buy_OrderItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Buy_OrderItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Buy_OrderItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Buy_OrderItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Buy_OrderItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Buy_OrderItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Buy_OrderItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Buy_OrderItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Buy_OrderItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Buy_OrderItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Buy_OrderItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Buy_OrderItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.custom11 != null and SearchPojo.custom11 != ''">
                or Buy_OrderItem.Custom11 like concat('%', #{SearchPojo.custom11}, '%')
            </if>
            <if test="SearchPojo.custom12 != null and SearchPojo.custom12 != ''">
                or Buy_OrderItem.Custom12 like concat('%', #{SearchPojo.custom12}, '%')
            </if>
            <if test="SearchPojo.custom13 != null and SearchPojo.custom13 != ''">
                or Buy_OrderItem.Custom13 like concat('%', #{SearchPojo.custom13}, '%')
            </if>
            <if test="SearchPojo.custom14 != null and SearchPojo.custom14 != ''">
                or Buy_OrderItem.Custom14 like concat('%', #{SearchPojo.custom14}, '%')
            </if>
            <if test="SearchPojo.custom15 != null and SearchPojo.custom15 != ''">
                or Buy_OrderItem.Custom15 like concat('%', #{SearchPojo.custom15}, '%')
            </if>
            <if test="SearchPojo.custom16 != null and SearchPojo.custom16 != ''">
                or Buy_OrderItem.Custom16 like concat('%', #{SearchPojo.custom16}, '%')
            </if>
            <if test="SearchPojo.custom17 != null and SearchPojo.custom17 != ''">
                or Buy_OrderItem.Custom17 like concat('%', #{SearchPojo.custom17}, '%')
            </if>
            <if test="SearchPojo.custom18 != null and SearchPojo.custom18 != ''">
                or Buy_OrderItem.Custom18 like concat('%', #{SearchPojo.custom18}, '%')
            </if>
        </trim>
    </sql>


    <!--新增所有列-->
    <insert id="insert">
        insert into Buy_OrderItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice, TaxAmount, ItemTaxrate, TaxTotal, Price, Amount, PlanDate, MaxQty, FinishQty, Remark, CiteUid, CiteItemid, StateCode, StateDate, Closed, RowNum, OrgGoodsid, SubRate, MachUid, MachItemid, MachBatch, MachGroupid, MainPlanUid, MainPlanItemid, MrpUid, MrpItemid, MrpObjGoodsid, VirtualItem, Customer, CustPO, BatchNo, DisannulMark, DisannulListerid, DisannulLister, DisannulDate, AttributeJson, SourceType, StdPrice, StdAmount, Rebate, PassedQty, InStoreQty, InvoQty, InvoClosed, AvgInvoAmt, AvgFirstAmt, AvgLastAmt, InvoAmt, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Custom11, Custom12, Custom13, Custom14, Custom15, Custom16, Custom17, Custom18, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{taxprice}, #{taxamount}, #{itemtaxrate}, #{taxtotal}, #{price}, #{amount}, #{plandate}, #{maxqty}, #{finishqty}, #{remark}, #{citeuid}, #{citeitemid}, #{statecode}, #{statedate}, #{closed}, #{rownum}, #{orggoodsid}, #{subrate}, #{machuid}, #{machitemid}, #{machbatch}, #{machgroupid}, #{mainplanuid}, #{mainplanitemid}, #{mrpuid}, #{mrpitemid}, #{mrpobjgoodsid}, #{virtualitem}, #{customer}, #{custpo}, #{batchno}, #{disannulmark}, #{disannullisterid}, #{disannullister}, #{disannuldate}, #{attributejson}, #{sourcetype}, #{stdprice}, #{stdamount}, #{rebate}, #{passedqty}, #{instoreqty}, #{invoqty}, #{invoclosed}, #{avginvoamt}, #{avgfirstamt}, #{avglastamt}, #{invoamt}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{custom11}, #{custom12}, #{custom13}, #{custom14}, #{custom15}, #{custom16}, #{custom17}, #{custom18}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_OrderItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="maxqty != null">
                MaxQty = #{maxqty},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="orggoodsid != null ">
                OrgGoodsid = #{orggoodsid},
            </if>
            <if test="subrate != null">
                SubRate = #{subrate},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machbatch != null ">
                MachBatch = #{machbatch},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null ">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="mrpobjgoodsid != null ">
                MrpObjGoodsid = #{mrpobjgoodsid},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="stdprice != null">
                StdPrice = #{stdprice},
            </if>
            <if test="stdamount != null">
                StdAmount = #{stdamount},
            </if>
            <if test="rebate != null">
                Rebate = #{rebate},
            </if>
            <if test="passedqty != null">
                PassedQty = #{passedqty},
            </if>
            <if test="instoreqty != null">
                InStoreQty = #{instoreqty},
            </if>
            <if test="invoqty != null">
                InvoQty = #{invoqty},
            </if>
            <if test="invoclosed != null">
                InvoClosed = #{invoclosed},
            </if>
            <if test="avginvoamt != null">
                AvgInvoAmt = #{avginvoamt},
            </if>
            <if test="avgfirstamt != null">
                AvgFirstAmt = #{avgfirstamt},
            </if>
            <if test="avglastamt != null">
                AvgLastAmt = #{avglastamt},
            </if>
            <if test="invoamt != null">
                InvoAmt = #{invoamt},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="custom11 != null ">
                Custom11 = #{custom11},
            </if>
            <if test="custom12 != null ">
                Custom12 = #{custom12},
            </if>
            <if test="custom13 != null ">
                Custom13 = #{custom13},
            </if>
            <if test="custom14 != null ">
                Custom14 = #{custom14},
            </if>
            <if test="custom15 != null ">
                Custom15 = #{custom15},
            </if>
            <if test="custom16 != null ">
                Custom16 = #{custom16},
            </if>
            <if test="custom17 != null ">
                Custom17 = #{custom17},
            </if>
            <if test="custom18 != null ">
                Custom18 = #{custom18},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_OrderItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

