<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.FmCostMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.FmCostPojo">
        SELECT
            Fm_Cost.id,
            Fm_Cost.RefNo,
            Fm_Cost.BillType,
            Fm_Cost.BillDate,
            Fm_Cost.BillTitle,
            Fm_Cost.Projectid,
            Fm_Cost.ProjName,
            Fm_Cost.ProjCode,
            Fm_Cost.Groupid,
            Fm_Cost.ChequeNum,
            Fm_Cost.Moneyid,
            Fm_Cost.Amount,
            Fm_Cost.CashAccid,
            Fm_Cost.Operator,
            Fm_Cost.ProjectCode,
            Fm_Cost.<PERSON>,
            Fm_Cost.<PERSON>,
            Fm_Cost.<PERSON>,
            Fm_Cost.<PERSON>,
            Fm_Cost.<PERSON>er,
            Fm_Cost.Listerid,
            Fm_Cost.ModifyDate,
            Fm_Cost.ModuleCode,
            Fm_Cost.CiteUid,
            Fm_Cost.Citeid,
            Fm_Cost.Benefited,
            Fm_Cost.Custom1,
            Fm_Cost.Custom2,
            Fm_Cost.Custom3,
            Fm_Cost.Custom4,
            Fm_Cost.Custom5,
            Fm_Cost.Custom6,
            Fm_Cost.Custom7,
            Fm_Cost.Custom8,
            Fm_Cost.Custom9,
            Fm_Cost.Custom10,
            Fm_Cost.Tenantid,
            Fm_Cost.TenantName,
            Fm_Cost.Revision,
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Fm_CashAccount.AccountName
        FROM
            Fm_Cost
                LEFT JOIN App_Workgroup ON App_Workgroup.id = Fm_Cost.Groupid
                LEFT JOIN Fm_CashAccount ON Fm_Cost.CashAccid = Fm_CashAccount.id
        where Fm_Cost.id = #{key}
          and Fm_Cost.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT
            Fm_Cost.id,
            Fm_Cost.RefNo,
            Fm_Cost.BillType,
            Fm_Cost.BillDate,
            Fm_Cost.BillTitle,
            Fm_Cost.Projectid,
            Fm_Cost.ProjName,
            Fm_Cost.ProjCode,
            Fm_Cost.Groupid,
            Fm_Cost.ChequeNum,
            Fm_Cost.Moneyid,
            Fm_Cost.Amount,
            Fm_Cost.CashAccid,
            Fm_Cost.Operator,
            Fm_Cost.ProjectCode,
            Fm_Cost.Summary,
            Fm_Cost.CreateBy,
            Fm_Cost.CreateByid,
            Fm_Cost.CreateDate,
            Fm_Cost.Lister,
            Fm_Cost.Listerid,
            Fm_Cost.ModifyDate,
            Fm_Cost.ModuleCode,
            Fm_Cost.CiteUid,
            Fm_Cost.Citeid,
            Fm_Cost.Benefited,
            Fm_Cost.Custom1,
            Fm_Cost.Custom2,
            Fm_Cost.Custom3,
            Fm_Cost.Custom4,
            Fm_Cost.Custom5,
            Fm_Cost.Custom6,
            Fm_Cost.Custom7,
            Fm_Cost.Custom8,
            Fm_Cost.Custom9,
            Fm_Cost.Custom10,
            Fm_Cost.Tenantid,
            Fm_Cost.TenantName,
            Fm_Cost.Revision,
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Fm_CashAccount.AccountName
        FROM
            Fm_Cost
                LEFT JOIN App_Workgroup ON App_Workgroup.id = Fm_Cost.Groupid
                LEFT JOIN Fm_CashAccount ON Fm_Cost.CashAccid = Fm_CashAccount.id
    </sql>
    <sql id="selectdetailVo">
        SELECT
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Fm_CostItem.id,
            Fm_CostItem.Pid,
            Fm_CostItem.CostTypeid,
            Fm_CostItem.ItemName,
            Fm_CostItem.ItemDepict,
            Fm_CostItem.Amount,
            Fm_CostItem.Remark,
            Fm_CostItem.RowNum,
            Fm_CostItem.Custom1,
            Fm_CostItem.Custom2,
            Fm_CostItem.Custom3,
            Fm_CostItem.Custom4,
            Fm_CostItem.Custom5,
            Fm_CostItem.Custom6,
            Fm_CostItem.Custom7,
            Fm_CostItem.Custom8,
            Fm_CostItem.Custom9,
            Fm_CostItem.Custom10,
            Fm_CostItem.Tenantid,
            Fm_CostItem.Revision,
            Fm_CostType.CostCode,
            Fm_CostType.CostName,
            Fm_Cost.RefNo,
            Fm_Cost.BillType,
            Fm_Cost.BillDate,
            Fm_Cost.BillTitle,
            Fm_Cost.Projectid,
            Fm_Cost.ProjName,
            Fm_Cost.ProjCode,
            Fm_Cost.CreateBy,
            Fm_Cost.Lister,
            Fm_CashAccount.AccountName
        FROM
            Fm_Cost
                LEFT JOIN App_Workgroup ON App_Workgroup.id = Fm_Cost.Groupid
                RIGHT JOIN Fm_CostItem ON Fm_CostItem.Pid = Fm_Cost.id
                LEFT JOIN Fm_CostType ON Fm_CostType.id = Fm_CostItem.CostTypeid
                LEFT JOIN Fm_CashAccount ON Fm_CashAccount.id = Fm_Cost.CashAccid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.FmCostitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Fm_Cost.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Fm_Cost.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Fm_Cost.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Fm_Cost.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Fm_Cost.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Fm_Cost.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.chequenum != null ">
            and Fm_Cost.chequenum like concat('%', #{SearchPojo.chequenum}, '%')
        </if>
        <if test="SearchPojo.moneyid != null ">
            and Fm_Cost.moneyid like concat('%', #{SearchPojo.moneyid}, '%')
        </if>
        <if test="SearchPojo.cashaccid != null ">
            and Fm_Cost.cashaccid like concat('%', #{SearchPojo.cashaccid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Fm_Cost.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.projectcode != null ">
            and Fm_Cost.projectcode like concat('%', #{SearchPojo.projectcode}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Fm_Cost.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Fm_Cost.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Fm_Cost.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Fm_Cost.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Fm_Cost.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null ">
            and Fm_Cost.modulecode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.citeuid != null ">
            and Fm_Cost.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeid != null ">
            and Fm_Cost.citeid like concat('%', #{SearchPojo.citeid}, '%')
        </if>
        <if test="SearchPojo.benefited != null ">
            and Fm_Cost.benefited like concat('%', #{SearchPojo.benefited}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Fm_Cost.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Fm_Cost.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Fm_Cost.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Fm_Cost.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Fm_Cost.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Fm_Cost.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Fm_Cost.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Fm_Cost.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Fm_Cost.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Fm_Cost.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Fm_Cost.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Fm_Cost.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Fm_Cost.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Fm_Cost.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Fm_Cost.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.chequenum != null ">
                or Fm_Cost.ChequeNum like concat('%', #{SearchPojo.chequenum}, '%')
            </if>
            <if test="SearchPojo.moneyid != null ">
                or Fm_Cost.Moneyid like concat('%', #{SearchPojo.moneyid}, '%')
            </if>
            <if test="SearchPojo.cashaccid != null ">
                or Fm_Cost.CashAccid like concat('%', #{SearchPojo.cashaccid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Fm_Cost.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.projectcode != null ">
                or Fm_Cost.ProjectCode like concat('%', #{SearchPojo.projectcode}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Fm_Cost.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Fm_Cost.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Fm_Cost.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Fm_Cost.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Fm_Cost.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.modulecode != null ">
                or Fm_Cost.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.citeuid != null ">
                or Fm_Cost.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeid != null ">
                or Fm_Cost.Citeid like concat('%', #{SearchPojo.citeid}, '%')
            </if>
            <if test="SearchPojo.benefited != null ">
                or Fm_Cost.Benefited like concat('%', #{SearchPojo.benefited}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Fm_Cost.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Fm_Cost.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Fm_Cost.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Fm_Cost.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Fm_Cost.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Fm_Cost.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Fm_Cost.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Fm_Cost.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Fm_Cost.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Fm_Cost.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Fm_Cost.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.FmCostPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Fm_Cost.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Fm_Cost.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Fm_Cost.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Fm_Cost.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Fm_Cost.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Fm_Cost.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.chequenum != null ">
            and Fm_Cost.ChequeNum like concat('%', #{SearchPojo.chequenum}, '%')
        </if>
        <if test="SearchPojo.moneyid != null ">
            and Fm_Cost.Moneyid like concat('%', #{SearchPojo.moneyid}, '%')
        </if>
        <if test="SearchPojo.cashaccid != null ">
            and Fm_Cost.CashAccid like concat('%', #{SearchPojo.cashaccid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Fm_Cost.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.projectcode != null ">
            and Fm_Cost.ProjectCode like concat('%', #{SearchPojo.projectcode}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Fm_Cost.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Fm_Cost.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Fm_Cost.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Fm_Cost.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Fm_Cost.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null ">
            and Fm_Cost.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.citeuid != null ">
            and Fm_Cost.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeid != null ">
            and Fm_Cost.Citeid like concat('%', #{SearchPojo.citeid}, '%')
        </if>
        <if test="SearchPojo.benefited != null ">
            and Fm_Cost.Benefited like concat('%', #{SearchPojo.benefited}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Fm_Cost.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Fm_Cost.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Fm_Cost.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Fm_Cost.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Fm_Cost.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Fm_Cost.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Fm_Cost.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Fm_Cost.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Fm_Cost.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Fm_Cost.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Fm_Cost.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Fm_Cost.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Fm_Cost.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Fm_Cost.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Fm_Cost.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.chequenum != null ">
                or Fm_Cost.ChequeNum like concat('%', #{SearchPojo.chequenum}, '%')
            </if>
            <if test="SearchPojo.moneyid != null ">
                or Fm_Cost.Moneyid like concat('%', #{SearchPojo.moneyid}, '%')
            </if>
            <if test="SearchPojo.cashaccid != null ">
                or Fm_Cost.CashAccid like concat('%', #{SearchPojo.cashaccid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Fm_Cost.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.projectcode != null ">
                or Fm_Cost.ProjectCode like concat('%', #{SearchPojo.projectcode}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Fm_Cost.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Fm_Cost.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Fm_Cost.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Fm_Cost.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Fm_Cost.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.modulecode != null ">
                or Fm_Cost.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.citeuid != null ">
                or Fm_Cost.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeid != null ">
                or Fm_Cost.Citeid like concat('%', #{SearchPojo.citeid}, '%')
            </if>
            <if test="SearchPojo.benefited != null ">
                or Fm_Cost.Benefited like concat('%', #{SearchPojo.benefited}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Fm_Cost.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Fm_Cost.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Fm_Cost.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Fm_Cost.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Fm_Cost.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Fm_Cost.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Fm_Cost.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Fm_Cost.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Fm_Cost.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Fm_Cost.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Fm_Cost.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Fm_Cost(id, RefNo, BillType, BillDate, Projectid, ProjCode, ProjName, BillTitle, Groupid, ChequeNum, Moneyid, Amount, CashAccid,
                            Operator, ProjectCode, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid,
                            ModifyDate, ModuleCode, CiteUid, Citeid, Benefited, Custom1, Custom2, Custom3, Custom4,
                            Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{billtitle}, #{groupid}, #{chequenum}, #{moneyid},
                #{amount}, #{cashaccid}, #{operator}, #{projectcode}, #{summary}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{modulecode}, #{citeuid}, #{citeid},
                #{benefited}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Fm_Cost
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="chequenum != null ">
                ChequeNum =#{chequenum},
            </if>
            <if test="moneyid != null ">
                Moneyid =#{moneyid},
            </if>
            <if test="amount != null">
                Amount =#{amount},
            </if>
            <if test="cashaccid != null ">
                CashAccid =#{cashaccid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="projectcode != null ">
                ProjectCode =#{projectcode},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="citeuid != null ">
                CiteUid =#{citeuid},
            </if>
            <if test="citeid != null ">
                Citeid =#{citeid},
            </if>
            <if test="benefited != null ">
                Benefited =#{benefited},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Fm_Cost
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.som.domain.pojo.FmCostPojo">
        select
        id
        from Fm_CostItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--    刷新发货完成数 Eric ********-->
    <update id="updateCashAmount">
        update Fm_CashAccount
        SET CurrentAmt =CurrentAmt - #{amount}
        where id = #{key}
          and Tenantid = #{tid}
    </update>

</mapper>

