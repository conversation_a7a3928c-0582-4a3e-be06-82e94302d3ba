<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyDeductionMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyDeductionPojo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Buy_Deduction.id,
               Buy_Deduction.RefNo,
               Buy_Deduction.BillType,
               Buy_Deduction.BillTitle,
               Buy_Deduction.BillDate,
               Buy_Deduction.Projectid,
               Buy_Deduction.ProjName,
               Buy_Deduction.ProjCode,
               Buy_Deduction.Groupid,
               Buy_Deduction.Operator,
               Buy_Deduction.Taxrate,
               Buy_Deduction.BillTaxAmount,
               Buy_Deduction.BillAmount,
               Buy_Deduction.BillTaxTotal,
               Buy_Deduction.Summary,
               Buy_Deduction.CreateBy,
               Buy_Deduction.CreateByid,
               Buy_Deduction.CreateDate,
               Buy_Deduction.Lister,
               Buy_Deduction.Listerid,
               Buy_Deduction.ModifyDate,
               Buy_Deduction.Assessor,
               Buy_Deduction.Assessorid,
               Buy_Deduction.AssessDate,
               Buy_Deduction.ItemCount,
               Buy_Deduction.DisannulCount,
               Buy_Deduction.FinishCount,
               Buy_Deduction.PrintCount,
               Buy_Deduction.Custom1,
               Buy_Deduction.Custom2,
               Buy_Deduction.Custom3,
               Buy_Deduction.Custom4,
               Buy_Deduction.Custom5,
               Buy_Deduction.Custom6,
               Buy_Deduction.Custom7,
               Buy_Deduction.Custom8,
               Buy_Deduction.Custom9,
               Buy_Deduction.Custom10,
               Buy_Deduction.Deptid,
               Buy_Deduction.Tenantid,
               Buy_Deduction.TenantName,
               Buy_Deduction.Revision
        FROM App_Workgroup
                 RIGHT JOIN Buy_Deduction ON Buy_Deduction.Groupid = App_Workgroup.id
        where Buy_Deduction.id = #{key}
          and Buy_Deduction.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Buy_Deduction.id,
               Buy_Deduction.RefNo,
               Buy_Deduction.BillType,
               Buy_Deduction.BillTitle,
               Buy_Deduction.BillDate,
               Buy_Deduction.Projectid,
               Buy_Deduction.ProjName,
               Buy_Deduction.ProjCode,
               Buy_Deduction.Groupid,
               Buy_Deduction.Operator,
               Buy_Deduction.Taxrate,
               Buy_Deduction.BillTaxAmount,
               Buy_Deduction.BillAmount,
               Buy_Deduction.BillTaxTotal,
               Buy_Deduction.Summary,
               Buy_Deduction.CreateBy,
               Buy_Deduction.CreateByid,
               Buy_Deduction.CreateDate,
               Buy_Deduction.Lister,
               Buy_Deduction.Listerid,
               Buy_Deduction.ModifyDate,
               Buy_Deduction.Assessor,
               Buy_Deduction.Assessorid,
               Buy_Deduction.AssessDate,
               Buy_Deduction.ItemCount,
               Buy_Deduction.DisannulCount,
               Buy_Deduction.FinishCount,
               Buy_Deduction.PrintCount,
               Buy_Deduction.Custom1,
               Buy_Deduction.Custom2,
               Buy_Deduction.Custom3,
               Buy_Deduction.Custom4,
               Buy_Deduction.Custom5,
               Buy_Deduction.Custom6,
               Buy_Deduction.Custom7,
               Buy_Deduction.Custom8,
               Buy_Deduction.Custom9,
               Buy_Deduction.Custom10,
               Buy_Deduction.Deptid,
               Buy_Deduction.Tenantid,
               Buy_Deduction.TenantName,
               Buy_Deduction.Revision
        FROM App_Workgroup
                 RIGHT JOIN Buy_Deduction ON Buy_Deduction.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10,
               Buy_DeductionItem.id,
               Buy_DeductionItem.Pid,
               Buy_DeductionItem.Goodsid,
               Buy_DeductionItem.ItemCode,
               Buy_DeductionItem.ItemName,
               Buy_DeductionItem.ItemSpec,
               Buy_DeductionItem.ItemUnit,
               Buy_DeductionItem.Quantity,
               Buy_DeductionItem.TaxPrice,
               Buy_DeductionItem.TaxAmount,
               Buy_DeductionItem.TaxTotal,
               Buy_DeductionItem.ItemTaxrate,
               Buy_DeductionItem.Price,
               Buy_DeductionItem.Amount,
               Buy_DeductionItem.Remark,
               Buy_DeductionItem.CiteUid,
               Buy_DeductionItem.CiteItemid,
               Buy_DeductionItem.OrderUid,
               Buy_DeductionItem.OrderItemid,
               Buy_DeductionItem.CustPO,
               Buy_DeductionItem.RowNum,
               Buy_DeductionItem.InvoQty,
               Buy_DeductionItem.InvoClosed,
               Buy_DeductionItem.DisannulMark,
               Buy_DeductionItem.DisannulListerid,
               Buy_DeductionItem.DisannulLister,
               Buy_DeductionItem.DisannulDate,
               Buy_DeductionItem.Custom1,
               Buy_DeductionItem.Custom2,
               Buy_DeductionItem.Custom3,
               Buy_DeductionItem.Custom4,
               Buy_DeductionItem.Custom5,
               Buy_DeductionItem.Custom6,
               Buy_DeductionItem.Custom7,
               Buy_DeductionItem.Custom8,
               Buy_DeductionItem.Custom9,
               Buy_DeductionItem.Custom10,
               Buy_DeductionItem.Tenantid,
               Buy_DeductionItem.Revision,
               Buy_Deduction.RefNo,
               Buy_Deduction.BillType,
               Buy_Deduction.BillDate,
               Buy_Deduction.BillTitle,
               Buy_Deduction.Projectid,
               Buy_Deduction.ProjName,
               Buy_Deduction.ProjCode,
               Buy_Deduction.Operator,
               Buy_Deduction.CreateBy,
               Buy_Deduction.CreateDate,
               Buy_Deduction.Lister,
               Buy_Deduction.ModifyDate,
               Buy_Deduction.Assessor,
               Buy_Deduction.AssessDate
        FROM App_Workgroup
                 RIGHT JOIN Buy_Deduction ON Buy_Deduction.Groupid = App_Workgroup.id
                 RIGHT JOIN Buy_DeductionItem ON Buy_DeductionItem.Pid = Buy_Deduction.id
                 LEFT JOIN Mat_Goods ON Buy_DeductionItem.Goodsid = Mat_Goods.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyDeductionitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Buy_Deduction.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_Deduction.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Buy_Deduction.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Buy_Deduction.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Buy_Deduction.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Buy_Deduction.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Buy_Deduction.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Buy_Deduction.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Buy_Deduction.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Buy_Deduction.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Buy_Deduction.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Buy_Deduction.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Buy_Deduction.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Buy_Deduction.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Buy_Deduction.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Buy_Deduction.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Buy_Deduction.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Buy_Deduction.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Buy_Deduction.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Buy_Deduction.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Buy_Deduction.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Buy_Deduction.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Buy_Deduction.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Buy_Deduction.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Buy_Deduction.deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Buy_Deduction.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Buy_Deduction.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Buy_Deduction.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Buy_Deduction.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Buy_Deduction.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Buy_Deduction.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Buy_Deduction.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Buy_Deduction.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Buy_Deduction.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Buy_Deduction.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Buy_Deduction.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Buy_Deduction.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Buy_Deduction.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Buy_Deduction.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Buy_Deduction.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Buy_Deduction.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Buy_Deduction.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Buy_Deduction.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Buy_Deduction.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Buy_Deduction.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Buy_Deduction.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Buy_Deduction.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Buy_Deduction.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Buy_Deduction.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Buy_Deduction.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>

            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyDeductionPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Buy_Deduction.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_Deduction.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Buy_Deduction.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Buy_Deduction.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Buy_Deduction.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Buy_Deduction.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Buy_Deduction.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Buy_Deduction.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Buy_Deduction.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Buy_Deduction.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Buy_Deduction.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Buy_Deduction.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Buy_Deduction.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Buy_Deduction.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Buy_Deduction.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Buy_Deduction.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Buy_Deduction.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Buy_Deduction.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Buy_Deduction.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Buy_Deduction.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Buy_Deduction.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Buy_Deduction.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Buy_Deduction.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Buy_Deduction.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Buy_Deduction.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Buy_Deduction.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Buy_Deduction.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Buy_Deduction.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Buy_Deduction.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Buy_Deduction.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Buy_Deduction.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Buy_Deduction.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Buy_Deduction.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Buy_Deduction.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Buy_Deduction.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Buy_Deduction.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Buy_Deduction.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Buy_Deduction.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Buy_Deduction.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Buy_Deduction.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Buy_Deduction.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Buy_Deduction.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Buy_Deduction.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Buy_Deduction.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Buy_Deduction.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Buy_Deduction.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Buy_Deduction.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Buy_Deduction.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Buy_Deduction.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Buy_Deduction.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Buy_Deduction(id, RefNo, BillType, BillTitle, BillDate, Projectid, ProjCode, ProjName, Groupid,
                                  Operator, Taxrate, BillTaxAmount,
                                  BillAmount, BillTaxTotal, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid,
                                  ModifyDate, Assessor, Assessorid, AssessDate, ItemCount, DisannulCount, FinishCount,
                                  PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                  Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{projectid}, #{projcode}, #{projname},
                #{groupid}, #{operator}, #{taxrate},
                #{billtaxamount}, #{billamount}, #{billtaxtotal}, #{summary}, #{createby}, #{createbyid}, #{createdate},
                #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{itemcount},
                #{disannulcount}, #{finishcount}, #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid},
                #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_Deduction
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="taxrate != null">
                Taxrate =#{taxrate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_Deduction
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Buy_Deduction
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--刷新打印次数-->
    <update id="updatePrintcount">
        update Buy_Deduction
        SET PrintCount = #{printcount}
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BuyDeductionPojo">
        select
        id
        from Buy_DeductionItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--  查询往来单位是否被引用  -->
    <select id="getItemCiteBillName" resultType="string">
        (SELECT '采购开票' as billname From Buy_InvoiceItem where FinishItemid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '采购账单' as billname From Buy_AccountItem where Billid = #{pid} and Tenantid = #{tid} LIMIT 1)
    </select>
</mapper>

