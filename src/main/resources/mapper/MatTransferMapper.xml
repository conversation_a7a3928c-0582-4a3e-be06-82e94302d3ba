<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatTransferMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatTransferPojo">
        <include refid="selectbillVo"/>
        where Mat_Transfer.id = #{key} and Mat_Transfer.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               RefNo,
               BillType,
               BillDate,
               Projectid,
               ProjCode,
               ProjName,
               BillTitle,
               Operator,
               OutStoreid,
               OutStoreCode,
               OutStoreName,
               InStoreid,
               InStoreCode,
               InStoreName,
               Su<PERSON>ry,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON>er,
               <PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               ReturnUid,
               Org<PERSON><PERSON>,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Mat_Transfer
    </sql>
    <sql id="selectdetailVo">
        SELECT
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10,
            Mat_TransferItem.id,
            Mat_TransferItem.Pid,
            Mat_TransferItem.Inveid,
            Mat_TransferItem.Goodsid,
            Mat_TransferItem.BatchNo,
            Mat_TransferItem.OutLocation,
            Mat_TransferItem.InLocation,
            Mat_TransferItem.Quantity,
            Mat_TransferItem.Remark,
            Mat_TransferItem.RowNum,
            Mat_TransferItem.Custom1,
            Mat_TransferItem.Custom2,
            Mat_TransferItem.Custom3,
            Mat_TransferItem.Custom4,
            Mat_TransferItem.Custom5,
            Mat_TransferItem.Custom6,
            Mat_TransferItem.Custom7,
            Mat_TransferItem.Custom8,
            Mat_TransferItem.Custom9,
            Mat_TransferItem.Custom10,
            Mat_TransferItem.Tenantid,
            Mat_TransferItem.Revision,
            Mat_Transfer.RefNo,
            Mat_Transfer.BillType,
            Mat_Transfer.BillDate,
            Mat_Transfer.Projectid,
            Mat_Transfer.ProjName,
            Mat_Transfer.ProjCode,
            Mat_Transfer.BillTitle,
            Mat_Transfer.OutStoreCode,
            Mat_Transfer.OutStoreName,
            Mat_Transfer.InStoreCode,
            Mat_Transfer.InStoreName
        FROM
            Mat_Goods
                RIGHT JOIN Mat_TransferItem ON Mat_TransferItem.Goodsid = Mat_Goods.id
                LEFT JOIN Mat_Transfer ON Mat_TransferItem.Pid = Mat_Transfer.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatTransferitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Mat_Transfer.Tenantid =#{tenantid}
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Transfer.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Mat_Transfer.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Mat_Transfer.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Mat_Transfer.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Mat_Transfer.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.outstoreid != null and SearchPojo.outstoreid != ''">
            and Mat_Transfer.outstoreid like concat('%', #{SearchPojo.outstoreid}, '%')
        </if>
        <if test="SearchPojo.outstorecode != null and SearchPojo.outstorecode != ''">
            and Mat_Transfer.outstorecode like concat('%', #{SearchPojo.outstorecode}, '%')
        </if>
        <if test="SearchPojo.outstorename != null and SearchPojo.outstorename != ''">
            and Mat_Transfer.outstorename like concat('%', #{SearchPojo.outstorename}, '%')
        </if>
        <if test="SearchPojo.instoreid != null and SearchPojo.instoreid != ''">
            and Mat_Transfer.instoreid like concat('%', #{SearchPojo.instoreid}, '%')
        </if>
        <if test="SearchPojo.instorecode != null and SearchPojo.instorecode != ''">
            and Mat_Transfer.instorecode like concat('%', #{SearchPojo.instorecode}, '%')
        </if>
        <if test="SearchPojo.instorename != null and SearchPojo.instorename != ''">
            and Mat_Transfer.instorename like concat('%', #{SearchPojo.instorename}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Mat_Transfer.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Mat_Transfer.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Mat_Transfer.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Mat_Transfer.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Mat_Transfer.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_Transfer.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_Transfer.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_Transfer.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_Transfer.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_Transfer.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_Transfer.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_Transfer.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_Transfer.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_Transfer.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_Transfer.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or Mat_Transfer.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            or Mat_Transfer.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            or Mat_Transfer.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            or Mat_Transfer.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.outstoreid != null and SearchPojo.outstoreid != ''">
            or Mat_Transfer.OutStoreid like concat('%', #{SearchPojo.outstoreid}, '%')
        </if>
        <if test="SearchPojo.outstorecode != null and SearchPojo.outstorecode != ''">
            or Mat_Transfer.OutStoreCode like concat('%', #{SearchPojo.outstorecode}, '%')
        </if>
        <if test="SearchPojo.outstorename != null and SearchPojo.outstorename != ''">
            or Mat_Transfer.OutStoreName like concat('%', #{SearchPojo.outstorename}, '%')
        </if>
        <if test="SearchPojo.instoreid != null and SearchPojo.instoreid != ''">
            or Mat_Transfer.InStoreid like concat('%', #{SearchPojo.instoreid}, '%')
        </if>
        <if test="SearchPojo.instorecode != null and SearchPojo.instorecode != ''">
            or Mat_Transfer.InStoreCode like concat('%', #{SearchPojo.instorecode}, '%')
        </if>
        <if test="SearchPojo.instorename != null and SearchPojo.instorename != ''">
            or Mat_Transfer.InStoreName like concat('%', #{SearchPojo.instorename}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or Mat_Transfer.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or Mat_Transfer.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or Mat_Transfer.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or Mat_Transfer.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or Mat_Transfer.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Mat_Transfer.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Mat_Transfer.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Mat_Transfer.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Mat_Transfer.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or Mat_Transfer.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            or Mat_Transfer.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            or Mat_Transfer.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            or Mat_Transfer.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            or Mat_Transfer.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            or Mat_Transfer.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        )
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatTransferPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Mat_Transfer.Tenantid =#{tenantid}
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Transfer.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Mat_Transfer.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Mat_Transfer.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Mat_Transfer.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Mat_Transfer.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.outstoreid != null and SearchPojo.outstoreid != ''">
            and Mat_Transfer.OutStoreid like concat('%', #{SearchPojo.outstoreid}, '%')
        </if>
        <if test="SearchPojo.outstorecode != null and SearchPojo.outstorecode != ''">
            and Mat_Transfer.OutStoreCode like concat('%', #{SearchPojo.outstorecode}, '%')
        </if>
        <if test="SearchPojo.outstorename != null and SearchPojo.outstorename != ''">
            and Mat_Transfer.OutStoreName like concat('%', #{SearchPojo.outstorename}, '%')
        </if>
        <if test="SearchPojo.instoreid != null and SearchPojo.instoreid != ''">
            and Mat_Transfer.InStoreid like concat('%', #{SearchPojo.instoreid}, '%')
        </if>
        <if test="SearchPojo.instorecode != null and SearchPojo.instorecode != ''">
            and Mat_Transfer.InStoreCode like concat('%', #{SearchPojo.instorecode}, '%')
        </if>
        <if test="SearchPojo.instorename != null and SearchPojo.instorename != ''">
            and Mat_Transfer.InStoreName like concat('%', #{SearchPojo.instorename}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Mat_Transfer.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Mat_Transfer.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Mat_Transfer.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Mat_Transfer.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Mat_Transfer.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_Transfer.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_Transfer.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_Transfer.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_Transfer.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_Transfer.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_Transfer.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_Transfer.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_Transfer.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_Transfer.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_Transfer.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="thor">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or Mat_Transfer.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            or Mat_Transfer.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            or Mat_Transfer.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            or Mat_Transfer.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.outstoreid != null and SearchPojo.outstoreid != ''">
            or Mat_Transfer.OutStoreid like concat('%', #{SearchPojo.outstoreid}, '%')
        </if>
        <if test="SearchPojo.outstorecode != null and SearchPojo.outstorecode != ''">
            or Mat_Transfer.OutStoreCode like concat('%', #{SearchPojo.outstorecode}, '%')
        </if>
        <if test="SearchPojo.outstorename != null and SearchPojo.outstorename != ''">
            or Mat_Transfer.OutStoreName like concat('%', #{SearchPojo.outstorename}, '%')
        </if>
        <if test="SearchPojo.instoreid != null and SearchPojo.instoreid != ''">
            or Mat_Transfer.InStoreid like concat('%', #{SearchPojo.instoreid}, '%')
        </if>
        <if test="SearchPojo.instorecode != null and SearchPojo.instorecode != ''">
            or Mat_Transfer.InStoreCode like concat('%', #{SearchPojo.instorecode}, '%')
        </if>
        <if test="SearchPojo.instorename != null and SearchPojo.instorename != ''">
            or Mat_Transfer.InStoreName like concat('%', #{SearchPojo.instorename}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or Mat_Transfer.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or Mat_Transfer.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or Mat_Transfer.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or Mat_Transfer.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or Mat_Transfer.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Mat_Transfer.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Mat_Transfer.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Mat_Transfer.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Mat_Transfer.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or Mat_Transfer.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            or Mat_Transfer.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            or Mat_Transfer.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            or Mat_Transfer.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            or Mat_Transfer.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            or Mat_Transfer.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        )
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Transfer(id, RefNo, BillType, BillDate, Projectid, ProjCode, ProjName, BillTitle, Operator, OutStoreid, OutStoreCode, OutStoreName, InStoreid, InStoreCode, InStoreName, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, ReturnUid, OrgUid, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{billtitle}, #{operator}, #{outstoreid}, #{outstorecode}, #{outstorename}, #{instoreid}, #{instorecode}, #{instorename}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{returnuid}, #{orguid}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Transfer
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="outstoreid != null ">
                OutStoreid =#{outstoreid},
            </if>
            <if test="outstorecode != null ">
                OutStoreCode =#{outstorecode},
            </if>
            <if test="outstorename != null ">
                OutStoreName =#{outstorename},
            </if>
            <if test="instoreid != null ">
                InStoreid =#{instoreid},
            </if>
            <if test="instorecode != null ">
                InStoreCode =#{instorecode},
            </if>
            <if test="instorename != null ">
                InStoreName =#{instorename},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="returnuid != null ">
            ReturnUid =#{returnuid},
        </if>
            <if test="orguid != null ">
            OrgUid =#{orguid},
        </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Transfer
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.MatTransferPojo">
        select
        id
        from Mat_TransferItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

</mapper>

