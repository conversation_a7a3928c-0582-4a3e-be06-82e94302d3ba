<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BusInvoiceitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BusInvoiceitemPojo">
        SELECT Bus_InvoiceItem.id,
               Bus_InvoiceItem.Pid,
               Bus_InvoiceItem.DeliUid,
               Bus_InvoiceItem.DeliDate,
               Bus_InvoiceItem.DeliType,
               Bus_InvoiceItem.DeliItemid,
               Bus_InvoiceItem.Goodsid,
               Bus_InvoiceItem.BillQty,
               Bus_InvoiceItem.Quantity,
               Bus_InvoiceItem.TaxPrice,
               Bus_InvoiceItem.TaxAmount,
               Bus_InvoiceItem.Price,
               Bus_InvoiceItem.Amount,
               Bus_InvoiceItem.ItemTaxrate,
               Bus_InvoiceItem.TaxTotal,
               Bus_InvoiceItem.RowNum,
               Bus_InvoiceItem.Remark,
               Bus_InvoiceItem.MachUid,
               Bus_InvoiceItem.MachItemid,
               Bus_InvoiceItem.CustPO,
               Bus_InvoiceItem.AvgFirstAmt,
               Bus_InvoiceItem.AvgLastAmt,
               Bus_InvoiceItem.Custom1,
               Bus_InvoiceItem.Custom2,
               Bus_InvoiceItem.Custom3,
               Bus_InvoiceItem.Custom4,
               Bus_InvoiceItem.Custom5,
               Bus_InvoiceItem.Custom6,
               Bus_InvoiceItem.Custom7,
               Bus_InvoiceItem.Custom8,
               Bus_InvoiceItem.Custom9,
               Bus_InvoiceItem.Custom10,
               Bus_InvoiceItem.Custom11,
               Bus_InvoiceItem.Custom12,
               Bus_InvoiceItem.Custom13,
               Bus_InvoiceItem.Custom14,
               Bus_InvoiceItem.Custom15,
               Bus_InvoiceItem.Custom16,
               Bus_InvoiceItem.Custom17,
               Bus_InvoiceItem.Custom18,
               Bus_InvoiceItem.Tenantid,
               Bus_InvoiceItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Bus_InvoiceItem
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Bus_InvoiceItem.Goodsid

        where Bus_InvoiceItem.id = #{key}
          and Bus_InvoiceItem.Tenantid = #{tid}
    </select>
    <sql id="selectBusInvoiceitemVo">
        SELECT Bus_InvoiceItem.id,
               Bus_InvoiceItem.Pid,
               Bus_InvoiceItem.DeliUid,
               Bus_InvoiceItem.DeliDate,
               Bus_InvoiceItem.DeliType,
               Bus_InvoiceItem.DeliItemid,
               Bus_InvoiceItem.Goodsid,
               Bus_InvoiceItem.BillQty,
               Bus_InvoiceItem.Quantity,
               Bus_InvoiceItem.TaxPrice,
               Bus_InvoiceItem.TaxAmount,
               Bus_InvoiceItem.Price,
               Bus_InvoiceItem.Amount,
               Bus_InvoiceItem.ItemTaxrate,
               Bus_InvoiceItem.TaxTotal,
               Bus_InvoiceItem.RowNum,
               Bus_InvoiceItem.Remark,
               Bus_InvoiceItem.MachUid,
               Bus_InvoiceItem.MachItemid,
               Bus_InvoiceItem.CustPO,
               Bus_InvoiceItem.AvgFirstAmt,
               Bus_InvoiceItem.AvgLastAmt,
               Bus_InvoiceItem.Custom1,
               Bus_InvoiceItem.Custom2,
               Bus_InvoiceItem.Custom3,
               Bus_InvoiceItem.Custom4,
               Bus_InvoiceItem.Custom5,
               Bus_InvoiceItem.Custom6,
               Bus_InvoiceItem.Custom7,
               Bus_InvoiceItem.Custom8,
               Bus_InvoiceItem.Custom9,
               Bus_InvoiceItem.Custom10,
               Bus_InvoiceItem.Custom11,
               Bus_InvoiceItem.Custom12,
               Bus_InvoiceItem.Custom13,
               Bus_InvoiceItem.Custom14,
               Bus_InvoiceItem.Custom15,
               Bus_InvoiceItem.Custom16,
               Bus_InvoiceItem.Custom17,
               Bus_InvoiceItem.Custom18,
               Bus_InvoiceItem.Tenantid,
               Bus_InvoiceItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Bus_InvoiceItem
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Bus_InvoiceItem.Goodsid

    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusInvoiceitemPojo">
        <include refid="selectBusInvoiceitemVo"/>
        where 1 = 1 and Bus_InvoiceItem.Tenantid =#{tenantid}
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_InvoiceItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Bus_InvoiceItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.deliuid != null and SearchPojo.deliuid != ''">
            and Bus_InvoiceItem.deliuid like concat('%', #{SearchPojo.deliuid}, '%')
        </if>
        <if test="SearchPojo.delitype != null and SearchPojo.delitype != ''">
            and Bus_InvoiceItem.delitype like concat('%', #{SearchPojo.delitype}, '%')
        </if>
        <if test="SearchPojo.deliitemid != null and SearchPojo.deliitemid != ''">
            and Bus_InvoiceItem.deliitemid like concat('%', #{SearchPojo.deliitemid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Bus_InvoiceItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Bus_InvoiceItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Bus_InvoiceItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Bus_InvoiceItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Bus_InvoiceItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_InvoiceItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_InvoiceItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_InvoiceItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_InvoiceItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_InvoiceItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_InvoiceItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_InvoiceItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_InvoiceItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_InvoiceItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_InvoiceItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.custom11 != null and SearchPojo.custom11 != ''">
            and Bus_InvoiceItem.custom11 like concat('%', #{SearchPojo.custom11}, '%')
        </if>
        <if test="SearchPojo.custom12 != null and SearchPojo.custom12 != ''">
            and Bus_InvoiceItem.custom12 like concat('%', #{SearchPojo.custom12}, '%')
        </if>
        <if test="SearchPojo.custom13 != null and SearchPojo.custom13 != ''">
            and Bus_InvoiceItem.custom13 like concat('%', #{SearchPojo.custom13}, '%')
        </if>
        <if test="SearchPojo.custom14 != null and SearchPojo.custom14 != ''">
            and Bus_InvoiceItem.custom14 like concat('%', #{SearchPojo.custom14}, '%')
        </if>
        <if test="SearchPojo.custom15 != null and SearchPojo.custom15 != ''">
            and Bus_InvoiceItem.custom15 like concat('%', #{SearchPojo.custom15}, '%')
        </if>
        <if test="SearchPojo.custom16 != null and SearchPojo.custom16 != ''">
            and Bus_InvoiceItem.custom16 like concat('%', #{SearchPojo.custom16}, '%')
        </if>
        <if test="SearchPojo.custom17 != null and SearchPojo.custom17 != ''">
            and Bus_InvoiceItem.custom17 like concat('%', #{SearchPojo.custom17}, '%')
        </if>
        <if test="SearchPojo.custom18 != null and SearchPojo.custom18 != ''">
            and Bus_InvoiceItem.custom18 like concat('%', #{SearchPojo.custom18}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            or Bus_InvoiceItem.Pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.deliuid != null and SearchPojo.deliuid != ''">
            or Bus_InvoiceItem.DeliUid like concat('%', #{SearchPojo.deliuid}, '%')
        </if>
        <if test="SearchPojo.delitype != null and SearchPojo.delitype != ''">
            or Bus_InvoiceItem.DeliType like concat('%', #{SearchPojo.delitype}, '%')
        </if>
        <if test="SearchPojo.deliitemid != null and SearchPojo.deliitemid != ''">
            or Bus_InvoiceItem.DeliItemid like concat('%', #{SearchPojo.deliitemid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            or Bus_InvoiceItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or Bus_InvoiceItem.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            or Bus_InvoiceItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            or Bus_InvoiceItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            or Bus_InvoiceItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Bus_InvoiceItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Bus_InvoiceItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Bus_InvoiceItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Bus_InvoiceItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or Bus_InvoiceItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            or Bus_InvoiceItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            or Bus_InvoiceItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            or Bus_InvoiceItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            or Bus_InvoiceItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            or Bus_InvoiceItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.custom11 != null and SearchPojo.custom11 != ''">
            or Bus_InvoiceItem.Custom11 like concat('%', #{SearchPojo.custom11}, '%')
        </if>
        <if test="SearchPojo.custom12 != null and SearchPojo.custom12 != ''">
            or Bus_InvoiceItem.Custom12 like concat('%', #{SearchPojo.custom12}, '%')
        </if>
        <if test="SearchPojo.custom13 != null and SearchPojo.custom13 != ''">
            or Bus_InvoiceItem.Custom13 like concat('%', #{SearchPojo.custom13}, '%')
        </if>
        <if test="SearchPojo.custom14 != null and SearchPojo.custom14 != ''">
            or Bus_InvoiceItem.Custom14 like concat('%', #{SearchPojo.custom14}, '%')
        </if>
        <if test="SearchPojo.custom15 != null and SearchPojo.custom15 != ''">
            or Bus_InvoiceItem.Custom15 like concat('%', #{SearchPojo.custom15}, '%')
        </if>
        <if test="SearchPojo.custom16 != null and SearchPojo.custom16 != ''">
            or Bus_InvoiceItem.Custom16 like concat('%', #{SearchPojo.custom16}, '%')
        </if>
        <if test="SearchPojo.custom17 != null and SearchPojo.custom17 != ''">
            or Bus_InvoiceItem.Custom17 like concat('%', #{SearchPojo.custom17}, '%')
        </if>
        <if test="SearchPojo.custom18 != null and SearchPojo.custom18 != ''">
            or Bus_InvoiceItem.Custom18 like concat('%', #{SearchPojo.custom18}, '%')
        </if>
        )
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.BusInvoiceitemPojo">
        SELECT Bus_InvoiceItem.id,
               Bus_InvoiceItem.Pid,
               Bus_InvoiceItem.DeliUid,
               Bus_InvoiceItem.DeliDate,
               Bus_InvoiceItem.DeliType,
               Bus_InvoiceItem.DeliItemid,
               Bus_InvoiceItem.Goodsid,
               Bus_InvoiceItem.BillQty,
               Bus_InvoiceItem.Quantity,
               Bus_InvoiceItem.TaxPrice,
               Bus_InvoiceItem.TaxAmount,
               Bus_InvoiceItem.Price,
               Bus_InvoiceItem.Amount,
               Bus_InvoiceItem.ItemTaxrate,
               Bus_InvoiceItem.TaxTotal,
               Bus_InvoiceItem.RowNum,
               Bus_InvoiceItem.Remark,
               Bus_InvoiceItem.MachUid,
               Bus_InvoiceItem.MachItemid,
               Bus_InvoiceItem.CustPO,
               Bus_InvoiceItem.AvgFirstAmt,
               Bus_InvoiceItem.AvgLastAmt,
               Bus_InvoiceItem.Custom1,
               Bus_InvoiceItem.Custom2,
               Bus_InvoiceItem.Custom3,
               Bus_InvoiceItem.Custom4,
               Bus_InvoiceItem.Custom5,
               Bus_InvoiceItem.Custom6,
               Bus_InvoiceItem.Custom7,
               Bus_InvoiceItem.Custom8,
               Bus_InvoiceItem.Custom9,
               Bus_InvoiceItem.Custom10,
               Bus_InvoiceItem.Custom11,
               Bus_InvoiceItem.Custom12,
               Bus_InvoiceItem.Custom13,
               Bus_InvoiceItem.Custom14,
               Bus_InvoiceItem.Custom15,
               Bus_InvoiceItem.Custom16,
               Bus_InvoiceItem.Custom17,
               Bus_InvoiceItem.Custom18,
               Bus_InvoiceItem.Tenantid,
               Bus_InvoiceItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Bus_InvoiceItem
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Bus_InvoiceItem.Goodsid

        where Bus_InvoiceItem.Pid = #{Pid}
          and Bus_InvoiceItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_InvoiceItem(id, Pid, DeliUid, DeliDate, DeliType, DeliItemid, Goodsid, BillQty, Quantity,
                                    TaxPrice, TaxAmount, Price, Amount, ItemTaxrate, TaxTotal, RowNum, Remark, MachUid,
                                    MachItemid, CustPO, AvgFirstAmt, AvgLastAmt, Custom1, Custom2, Custom3, Custom4,
                                    Custom5, Custom6, Custom7,
                                    Custom8, Custom9, Custom10, Custom11, Custom12, Custom13, Custom14, Custom15,
                                    Custom16, Custom17, Custom18, Tenantid, Revision)
        values (#{id}, #{pid}, #{deliuid}, #{delidate}, #{delitype}, #{deliitemid}, #{goodsid}, #{billqty}, #{quantity},
                #{taxprice}, #{taxamount}, #{price}, #{amount}, #{itemtaxrate}, #{taxtotal}, #{rownum}, #{remark},
                #{machuid}, #{machitemid}, #{custpo}, #{avgfirstamt}, #{avglastamt}, #{custom1}, #{custom2}, #{custom3},
                #{custom4}, #{custom5},
                #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{custom11}, #{custom12}, #{custom13},
                #{custom14}, #{custom15}, #{custom16}, #{custom17}, #{custom18}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_InvoiceItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="deliuid != null ">
                DeliUid = #{deliuid},
            </if>
            <if test="delidate != null">
                DeliDate = #{delidate},
            </if>
            <if test="delitype != null ">
                DeliType = #{delitype},
            </if>
            <if test="deliitemid != null ">
                DeliItemid = #{deliitemid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="billqty != null">
                BillQty = #{billqty},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="avgfirstamt != null">
                AvgFirstAmt = #{avgfirstamt},
            </if>
            <if test="avglastamt != null">
                AvgLastAmt = #{avglastamt},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="custom11 != null ">
                Custom11 = #{custom11},
            </if>
            <if test="custom12 != null ">
                Custom12 = #{custom12},
            </if>
            <if test="custom13 != null ">
                Custom13 = #{custom13},
            </if>
            <if test="custom14 != null ">
                Custom14 = #{custom14},
            </if>
            <if test="custom15 != null ">
                Custom15 = #{custom15},
            </if>
            <if test="custom16 != null ">
                Custom16 = #{custom16},
            </if>
            <if test="custom17 != null ">
                Custom17 = #{custom17},
            </if>
            <if test="custom18 != null ">
                Custom18 = #{custom18},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_InvoiceItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

