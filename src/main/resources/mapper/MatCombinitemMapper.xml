<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatCombinitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatCombinitemPojo">
        SELECT
            Mat_CombinItem.id,
            Mat_CombinItem.Pid,
            Mat_CombinItem.AccessType,
            Mat_CombinItem.Goodsid,
            Mat_CombinItem.ItemCode,
            Mat_CombinItem.ItemName,
            Mat_CombinItem.ItemSpec,
            Mat_CombinItem.ItemUnit,
            Mat_CombinItem.Quantity,
            Mat_CombinItem.Price,
            Mat_CombinItem.Amount,
            Mat_CombinItem.Remark,
            Mat_CombinItem.RowNum,
            Mat_CombinItem.Location,
            Mat_CombinItem.BatchNo,
            Mat_CombinItem.PackSn,
            Mat_CombinItem.ExpiDate,
            Mat_CombinItem.Inveid,
            Mat_CombinItem.Custom1,
            Mat_CombinItem.Custom2,
            Mat_CombinItem.Custom3,
            Mat_CombinItem.Custom4,
            Mat_CombinItem.Custom5,
            Mat_CombinItem.Custom6,
            Mat_CombinItem.Custom7,
            Mat_CombinItem.Custom8,
            Mat_CombinItem.Custom9,
            Mat_CombinItem.Custom10,
            Mat_CombinItem.Tenantid,
            Mat_CombinItem.TenantName,
            Mat_CombinItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10
        FROM
            Mat_Goods
                RIGHT JOIN Mat_CombinItem ON Mat_Goods.id = Mat_CombinItem.Goodsid
        where Mat_CombinItem.id = #{key}
          and Mat_CombinItem.Tenantid = #{tid}
    </select>
    <sql id="selectMatCombinitemVo">
        SELECT
            Mat_CombinItem.id,
            Mat_CombinItem.Pid,
            Mat_CombinItem.AccessType,
            Mat_CombinItem.Goodsid,
            Mat_CombinItem.ItemCode,
            Mat_CombinItem.ItemName,
            Mat_CombinItem.ItemSpec,
            Mat_CombinItem.ItemUnit,
            Mat_CombinItem.Quantity,
            Mat_CombinItem.Price,
            Mat_CombinItem.Amount,
            Mat_CombinItem.Remark,
            Mat_CombinItem.RowNum,
            Mat_CombinItem.Location,
            Mat_CombinItem.BatchNo,
            Mat_CombinItem.PackSn,
            Mat_CombinItem.ExpiDate,
            Mat_CombinItem.Inveid,
            Mat_CombinItem.Custom1,
            Mat_CombinItem.Custom2,
            Mat_CombinItem.Custom3,
            Mat_CombinItem.Custom4,
            Mat_CombinItem.Custom5,
            Mat_CombinItem.Custom6,
            Mat_CombinItem.Custom7,
            Mat_CombinItem.Custom8,
            Mat_CombinItem.Custom9,
            Mat_CombinItem.Custom10,
            Mat_CombinItem.Tenantid,
            Mat_CombinItem.TenantName,
            Mat_CombinItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10
        FROM
            Mat_Goods
                RIGHT JOIN Mat_CombinItem ON Mat_Goods.id = Mat_CombinItem.Goodsid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatCombinitemPojo">
        <include refid="selectMatCombinitemVo"/>
        where 1 = 1 and Mat_CombinItem.Tenantid =#{tenantid}
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_CombinItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Mat_CombinItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_CombinItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Mat_CombinItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Mat_CombinItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Mat_CombinItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Mat_CombinItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Mat_CombinItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Mat_CombinItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Mat_CombinItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.packsn != null and SearchPojo.packsn != ''">
            and Mat_CombinItem.packsn like concat('%', #{SearchPojo.packsn}, '%')
        </if>
        <if test="SearchPojo.inveid != null and SearchPojo.inveid != ''">
            and Mat_CombinItem.inveid like concat('%', #{SearchPojo.inveid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_CombinItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_CombinItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_CombinItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_CombinItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_CombinItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_CombinItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_CombinItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_CombinItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_CombinItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_CombinItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            and Mat_CombinItem.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Mat_CombinItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_CombinItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Mat_CombinItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Mat_CombinItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Mat_CombinItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Mat_CombinItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Mat_CombinItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Mat_CombinItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Mat_CombinItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.packsn != null and SearchPojo.packsn != ''">
                or Mat_CombinItem.PackSn like concat('%', #{SearchPojo.packsn}, '%')
            </if>
            <if test="SearchPojo.inveid != null and SearchPojo.inveid != ''">
                or Mat_CombinItem.Inveid like concat('%', #{SearchPojo.inveid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_CombinItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_CombinItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_CombinItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_CombinItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_CombinItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_CombinItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_CombinItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_CombinItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_CombinItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_CombinItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
                or Mat_CombinItem.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.MatCombinitemPojo">
        SELECT
            Mat_CombinItem.id,
            Mat_CombinItem.Pid,
            Mat_CombinItem.AccessType,
            Mat_CombinItem.Goodsid,
            Mat_CombinItem.ItemCode,
            Mat_CombinItem.ItemName,
            Mat_CombinItem.ItemSpec,
            Mat_CombinItem.ItemUnit,
            Mat_CombinItem.Quantity,
            Mat_CombinItem.Price,
            Mat_CombinItem.Amount,
            Mat_CombinItem.Remark,
            Mat_CombinItem.RowNum,
            Mat_CombinItem.Location,
            Mat_CombinItem.BatchNo,
            Mat_CombinItem.PackSn,
            Mat_CombinItem.ExpiDate,
            Mat_CombinItem.Inveid,
            Mat_CombinItem.Custom1,
            Mat_CombinItem.Custom2,
            Mat_CombinItem.Custom3,
            Mat_CombinItem.Custom4,
            Mat_CombinItem.Custom5,
            Mat_CombinItem.Custom6,
            Mat_CombinItem.Custom7,
            Mat_CombinItem.Custom8,
            Mat_CombinItem.Custom9,
            Mat_CombinItem.Custom10,
            Mat_CombinItem.Tenantid,
            Mat_CombinItem.TenantName,
            Mat_CombinItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid
        FROM
            Mat_Goods
                RIGHT JOIN Mat_CombinItem ON Mat_Goods.id = Mat_CombinItem.Goodsid
        where Mat_CombinItem.Pid = #{Pid}
          and Mat_CombinItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_CombinItem(id, Pid, AccessType, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity,
                                   Price, Amount, Remark, RowNum, Location, BatchNo, PackSn, ExpiDate, Inveid, Custom1,
                                   Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10,
                                   Tenantid, TenantName, Revision)
        values (#{id}, #{pid}, #{accesstype}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit},
                #{quantity}, #{price}, #{amount}, #{remark}, #{rownum}, #{location}, #{batchno}, #{packsn}, #{expidate},
                #{inveid}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_CombinItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="accesstype != null">
                AccessType = #{accesstype},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="packsn != null ">
                PackSn = #{packsn},
            </if>
            <if test="expidate != null">
                ExpiDate = #{expidate},
            </if>
            <if test="inveid != null ">
                Inveid = #{inveid},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_CombinItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

