<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyPrepaymentsMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyPrepaymentsPojo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Buy_Prepayments.id,
               Buy_Prepayments.RefNo,
               Buy_Prepayments.BillType,
               Buy_Prepayments.BillTitle,
               Buy_Prepayments.BillDate,
               Buy_Prepayments.Projectid,
               Buy_Prepayments.ProjName,
               Buy_Prepayments.ProjCode,
               Buy_Prepayments.Groupid,
               Buy_Prepayments.BillAmount,
               Buy_Prepayments.Operator,
               Buy_Prepayments.CiteCode,
               Buy_Prepayments.OutAmount,
               Buy_Prepayments.ReturnUid,
               Buy_Prepayments.OrgUid,
               Buy_Prepayments.Summary,
               Buy_Prepayments.CreateBy,
               Buy_Prepayments.CreateByid,
               Buy_Prepayments.CreateDate,
               Buy_Prepayments.Lister,
               Buy_Prepayments.Listerid,
               Buy_Prepayments.ModifyDate,
               Buy_Prepayments.Assessor,
               Buy_Prepayments.Assessorid,
               Buy_Prepayments.AssessDate,
               Buy_Prepayments.Custom1,
               Buy_Prepayments.Custom2,
               Buy_Prepayments.Custom3,
               Buy_Prepayments.Custom4,
               Buy_Prepayments.Custom5,
               Buy_Prepayments.Custom6,
               Buy_Prepayments.Custom7,
               Buy_Prepayments.Custom8,
               Buy_Prepayments.Custom9,
               Buy_Prepayments.Custom10,
               Buy_Prepayments.Tenantid,
               Buy_Prepayments.Revision
        FROM App_Workgroup
                 RIGHT JOIN Buy_Prepayments ON App_Workgroup.id = Buy_Prepayments.Groupid

        where Buy_Prepayments.id = #{key}
          and Buy_Prepayments.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            Buy_Prepayments.id,
            Buy_Prepayments.RefNo,
            Buy_Prepayments.BillType,
            Buy_Prepayments.BillTitle,
            Buy_Prepayments.BillDate,
            Buy_Prepayments.Projectid,
            Buy_Prepayments.ProjName,
            Buy_Prepayments.ProjCode,
            Buy_Prepayments.Groupid,
            Buy_Prepayments.BillAmount,
            Buy_Prepayments.Operator,
            Buy_Prepayments.CiteCode,
            Buy_Prepayments.OutAmount,
            Buy_Prepayments.ReturnUid,
            Buy_Prepayments.OrgUid,
            Buy_Prepayments.Summary,
            Buy_Prepayments.CreateBy,
            Buy_Prepayments.CreateByid,
            Buy_Prepayments.CreateDate,
            Buy_Prepayments.Lister,
            Buy_Prepayments.Listerid,
            Buy_Prepayments.ModifyDate,
            Buy_Prepayments.Assessor,
            Buy_Prepayments.Assessorid,
            Buy_Prepayments.AssessDate,
            Buy_Prepayments.Custom1,
            Buy_Prepayments.Custom2,
            Buy_Prepayments.Custom3,
            Buy_Prepayments.Custom4,
            Buy_Prepayments.Custom5,
            Buy_Prepayments.Custom6,
            Buy_Prepayments.Custom7,
            Buy_Prepayments.Custom8,
            Buy_Prepayments.Custom9,
            Buy_Prepayments.Custom10,
            Buy_Prepayments.Tenantid,
            Buy_Prepayments.Revision
        FROM
            App_Workgroup
                RIGHT JOIN Buy_Prepayments ON App_Workgroup.id = Buy_Prepayments.Groupid

    </sql>
    <sql id="selectdetailVo">
        select id,
               RefNo,
               BillType,
               BillTitle,
               BillDate,
               Projectid,
               ProjName,
               ProjCode,
               Groupid,
               BillAmount,
               Operator,
               CiteCode,
               OutAmount,
               ReturnUid,
               OrgUid,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Assessor,
               Assessorid,
               AssessDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Buy_Prepayments
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyPrepaymentsitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Buy_Prepayments.Tenantid =#{tenantid}
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_Prepayments.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Buy_Prepayments.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Buy_Prepayments.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Buy_Prepayments.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Buy_Prepayments.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Buy_Prepayments.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
            and Buy_Prepayments.citecode like concat('%', #{SearchPojo.citecode}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            and Buy_Prepayments.returnuid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            and Buy_Prepayments.orguid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Buy_Prepayments.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Buy_Prepayments.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Buy_Prepayments.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Buy_Prepayments.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Buy_Prepayments.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Buy_Prepayments.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Buy_Prepayments.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Buy_Prepayments.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Buy_Prepayments.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Buy_Prepayments.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Buy_Prepayments.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Buy_Prepayments.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Buy_Prepayments.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Buy_Prepayments.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Buy_Prepayments.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Buy_Prepayments.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Buy_Prepayments.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or Buy_Prepayments.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            or Buy_Prepayments.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            or Buy_Prepayments.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            or Buy_Prepayments.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            or Buy_Prepayments.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
            or Buy_Prepayments.CiteCode like concat('%', #{SearchPojo.citecode}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            or Buy_Prepayments.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            or Buy_Prepayments.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or Buy_Prepayments.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or Buy_Prepayments.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or Buy_Prepayments.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or Buy_Prepayments.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or Buy_Prepayments.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            or Buy_Prepayments.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            or Buy_Prepayments.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Buy_Prepayments.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Buy_Prepayments.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Buy_Prepayments.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Buy_Prepayments.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or Buy_Prepayments.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            or Buy_Prepayments.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            or Buy_Prepayments.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            or Buy_Prepayments.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            or Buy_Prepayments.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            or Buy_Prepayments.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        )
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyPrepaymentsPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Buy_Prepayments.Tenantid =#{tenantid}
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_Prepayments.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Buy_Prepayments.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Buy_Prepayments.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Buy_Prepayments.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Buy_Prepayments.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Buy_Prepayments.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
            and Buy_Prepayments.CiteCode like concat('%', #{SearchPojo.citecode}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            and Buy_Prepayments.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            and Buy_Prepayments.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Buy_Prepayments.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Buy_Prepayments.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Buy_Prepayments.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Buy_Prepayments.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Buy_Prepayments.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Buy_Prepayments.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Buy_Prepayments.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Buy_Prepayments.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Buy_Prepayments.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Buy_Prepayments.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Buy_Prepayments.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Buy_Prepayments.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Buy_Prepayments.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Buy_Prepayments.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Buy_Prepayments.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Buy_Prepayments.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Buy_Prepayments.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="thor">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or Buy_Prepayments.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            or Buy_Prepayments.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            or Buy_Prepayments.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            or Buy_Prepayments.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            or Buy_Prepayments.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
            or Buy_Prepayments.CiteCode like concat('%', #{SearchPojo.citecode}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            or Buy_Prepayments.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            or Buy_Prepayments.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or Buy_Prepayments.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or Buy_Prepayments.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or Buy_Prepayments.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or Buy_Prepayments.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or Buy_Prepayments.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            or Buy_Prepayments.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            or Buy_Prepayments.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Buy_Prepayments.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Buy_Prepayments.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Buy_Prepayments.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Buy_Prepayments.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or Buy_Prepayments.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            or Buy_Prepayments.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            or Buy_Prepayments.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            or Buy_Prepayments.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            or Buy_Prepayments.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            or Buy_Prepayments.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        )
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Buy_Prepayments(id, RefNo, BillType, BillTitle, BillDate, Projectid, ProjCode, ProjName, Groupid, BillAmount, Operator, CiteCode, OutAmount, ReturnUid, OrgUid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, FmDocMark, FmDocCode, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{groupid}, #{billamount}, #{operator}, #{citecode}, #{outamount}, #{returnuid}, #{orguid}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{fmdocmark}, #{fmdoccode}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_Prepayments
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="citecode != null ">
                CiteCode =#{citecode},
            </if>
            <if test="outamount != null">
                OutAmount =#{outamount},
            </if>
            <if test="returnuid != null ">
                ReturnUid =#{returnuid},
            </if>
            <if test="orguid != null ">
                OrgUid =#{orguid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="fmdocmark != null">
                FmDocMark =#{fmdocmark},
            </if>
            <if test="fmdoccode != null ">
                FmDocCode =#{fmdoccode},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_Prepayments
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Buy_Prepayments
        SET Assessor   = #{assessor},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BuyPrepaymentsPojo">
        select
        id
        from Buy_PrepaymentsItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <!--查询DelListIds-->
    <select id="getDelCashIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BuyPrepaymentsPojo">
        select
        id
        from Buy_PrepaymentsCash
        where Pid = #{id}
        <if test="cash !=null and cash.size()>0">
            and id not in
            <foreach collection="cash" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <update id="updateOrderAdvaAmountFirstAmt">
        update Buy_Order
        SET Prepayments =COALESCE((SELECT sum(Buy_PrepaymentsItem.Amount)
                                  FROM Buy_PrepaymentsItem
                                           LEFT OUTER JOIN Buy_Prepayments
                                                           ON Buy_PrepaymentsItem.pid = Buy_Prepayments.id
                                  where Buy_Prepayments.BillType IN ('采购预付','预付红冲')
                                    and Buy_PrepaymentsItem.OrderBillid = #{buyOrderId}
                                    and Buy_PrepaymentsItem.Tenantid = #{tid}), 0),
            FirstAmt=Prepayments
        where id = #{buyOrderId}
          and Tenantid = #{tid}
    </update>

    <update id="updateOrderItemAvgFirstAmt">
        UPDATE Buy_OrderItem
        SET AvgFirstAmt = (SELECT tmp.FirstAmt / tmp.ItemCount
                           FROM (SELECT (SELECT FirstAmt FROM Buy_Order WHERE id = #{buyOrderId}) AS FirstAmt,
                                        COUNT(*)                                                  AS ItemCount
                                 FROM Buy_OrderItem
                                 WHERE Pid = #{buyOrderId}) AS tmp)
        WHERE Buy_OrderItem.Pid = #{buyOrderId} and Tenantid = #{tid}
    </update>
</mapper>

