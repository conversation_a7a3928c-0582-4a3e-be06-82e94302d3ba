<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyDeductionitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyDeductionitemPojo">
        SELECT
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10,
            Buy_DeductionItem.id,
            Buy_DeductionItem.Pid,
            Buy_DeductionItem.Goodsid,
            Buy_DeductionItem.ItemCode,
            Buy_DeductionItem.ItemName,
            Buy_DeductionItem.ItemSpec,
            Buy_DeductionItem.ItemUnit,
            Buy_DeductionItem.Quantity,
            Buy_DeductionItem.TaxPrice,
            Buy_DeductionItem.TaxAmount,
            Buy_DeductionItem.TaxTotal,
            Buy_DeductionItem.ItemTaxrate,
            Buy_DeductionItem.Price,
            Buy_DeductionItem.Amount,
            Buy_DeductionItem.Remark,
            Buy_DeductionItem.CiteUid,
            Buy_DeductionItem.CiteItemid,
            Buy_DeductionItem.OrderUid,
            Buy_DeductionItem.OrderItemid,
            Buy_DeductionItem.CustPO,
            Buy_DeductionItem.RowNum,
            Buy_DeductionItem.InvoQty,
            Buy_DeductionItem.InvoClosed,
            Buy_DeductionItem.DisannulMark,
            Buy_DeductionItem.DisannulListerid,
            Buy_DeductionItem.DisannulLister,
            Buy_DeductionItem.DisannulDate,
            Buy_DeductionItem.Custom1,
            Buy_DeductionItem.Custom2,
            Buy_DeductionItem.Custom3,
            Buy_DeductionItem.Custom4,
            Buy_DeductionItem.Custom5,
            Buy_DeductionItem.Custom6,
            Buy_DeductionItem.Custom7,
            Buy_DeductionItem.Custom8,
            Buy_DeductionItem.Custom9,
            Buy_DeductionItem.Custom10,
            Buy_DeductionItem.Tenantid,
            Buy_DeductionItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Buy_DeductionItem ON Buy_DeductionItem.Goodsid = Mat_Goods.id
        where Buy_DeductionItem.id = #{key}
          and Buy_DeductionItem.Tenantid = #{tid}
    </select>
    <sql id="selectBuyDeductionitemVo">
        SELECT
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10,
            Buy_DeductionItem.id,
            Buy_DeductionItem.Pid,
            Buy_DeductionItem.Goodsid,
            Buy_DeductionItem.ItemCode,
            Buy_DeductionItem.ItemName,
            Buy_DeductionItem.ItemSpec,
            Buy_DeductionItem.ItemUnit,
            Buy_DeductionItem.Quantity,
            Buy_DeductionItem.TaxPrice,
            Buy_DeductionItem.TaxAmount,
            Buy_DeductionItem.TaxTotal,
            Buy_DeductionItem.ItemTaxrate,
            Buy_DeductionItem.Price,
            Buy_DeductionItem.Amount,
            Buy_DeductionItem.Remark,
            Buy_DeductionItem.CiteUid,
            Buy_DeductionItem.CiteItemid,
            Buy_DeductionItem.OrderUid,
            Buy_DeductionItem.OrderItemid,
            Buy_DeductionItem.CustPO,
            Buy_DeductionItem.RowNum,
            Buy_DeductionItem.InvoQty,
            Buy_DeductionItem.InvoClosed,
            Buy_DeductionItem.DisannulMark,
            Buy_DeductionItem.DisannulListerid,
            Buy_DeductionItem.DisannulLister,
            Buy_DeductionItem.DisannulDate,
            Buy_DeductionItem.Custom1,
            Buy_DeductionItem.Custom2,
            Buy_DeductionItem.Custom3,
            Buy_DeductionItem.Custom4,
            Buy_DeductionItem.Custom5,
            Buy_DeductionItem.Custom6,
            Buy_DeductionItem.Custom7,
            Buy_DeductionItem.Custom8,
            Buy_DeductionItem.Custom9,
            Buy_DeductionItem.Custom10,
            Buy_DeductionItem.Tenantid,
            Buy_DeductionItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Buy_DeductionItem ON Buy_DeductionItem.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyDeductionitemPojo">
        <include refid="selectBuyDeductionitemVo"/>
        where 1 = 1 and Buy_DeductionItem.Tenantid =#{tenantid}
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_DeductionItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Buy_DeductionItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Buy_DeductionItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Buy_DeductionItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Buy_DeductionItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Buy_DeductionItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Buy_DeductionItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Buy_DeductionItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Buy_DeductionItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Buy_DeductionItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.orderuid != null and SearchPojo.orderuid != ''">
            and Buy_DeductionItem.orderuid like concat('%', #{SearchPojo.orderuid}, '%')
        </if>
        <if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid != ''">
            and Buy_DeductionItem.orderitemid like concat('%', #{SearchPojo.orderitemid}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Buy_DeductionItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Buy_DeductionItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Buy_DeductionItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Buy_DeductionItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Buy_DeductionItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Buy_DeductionItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Buy_DeductionItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Buy_DeductionItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Buy_DeductionItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Buy_DeductionItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Buy_DeductionItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Buy_DeductionItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Buy_DeductionItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Buy_DeductionItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Buy_DeductionItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Buy_DeductionItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Buy_DeductionItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Buy_DeductionItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Buy_DeductionItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Buy_DeductionItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Buy_DeductionItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Buy_DeductionItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.orderuid != null and SearchPojo.orderuid != ''">
                or Buy_DeductionItem.OrderUid like concat('%', #{SearchPojo.orderuid}, '%')
            </if>
            <if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid != ''">
                or Buy_DeductionItem.OrderItemid like concat('%', #{SearchPojo.orderitemid}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Buy_DeductionItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Buy_DeductionItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Buy_DeductionItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Buy_DeductionItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Buy_DeductionItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Buy_DeductionItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Buy_DeductionItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Buy_DeductionItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Buy_DeductionItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Buy_DeductionItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Buy_DeductionItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Buy_DeductionItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Buy_DeductionItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.BuyDeductionitemPojo">
        SELECT
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10,
            Buy_DeductionItem.id,
            Buy_DeductionItem.Pid,
            Buy_DeductionItem.Goodsid,
            Buy_DeductionItem.ItemCode,
            Buy_DeductionItem.ItemName,
            Buy_DeductionItem.ItemSpec,
            Buy_DeductionItem.ItemUnit,
            Buy_DeductionItem.Quantity,
            Buy_DeductionItem.TaxPrice,
            Buy_DeductionItem.TaxAmount,
            Buy_DeductionItem.TaxTotal,
            Buy_DeductionItem.ItemTaxrate,
            Buy_DeductionItem.Price,
            Buy_DeductionItem.Amount,
            Buy_DeductionItem.Remark,
            Buy_DeductionItem.CiteUid,
            Buy_DeductionItem.CiteItemid,
            Buy_DeductionItem.OrderUid,
            Buy_DeductionItem.OrderItemid,
            Buy_DeductionItem.CustPO,
            Buy_DeductionItem.RowNum,
            Buy_DeductionItem.InvoQty,
            Buy_DeductionItem.InvoClosed,
            Buy_DeductionItem.DisannulMark,
            Buy_DeductionItem.DisannulListerid,
            Buy_DeductionItem.DisannulLister,
            Buy_DeductionItem.DisannulDate,
            Buy_DeductionItem.Custom1,
            Buy_DeductionItem.Custom2,
            Buy_DeductionItem.Custom3,
            Buy_DeductionItem.Custom4,
            Buy_DeductionItem.Custom5,
            Buy_DeductionItem.Custom6,
            Buy_DeductionItem.Custom7,
            Buy_DeductionItem.Custom8,
            Buy_DeductionItem.Custom9,
            Buy_DeductionItem.Custom10,
            Buy_DeductionItem.Tenantid,
            Buy_DeductionItem.Revision
        FROM
            Mat_Goods
                RIGHT JOIN Buy_DeductionItem ON Buy_DeductionItem.Goodsid = Mat_Goods.id
        where Buy_DeductionItem.Pid = #{Pid}
          and Buy_DeductionItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Buy_DeductionItem(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice,
                                      TaxAmount, TaxTotal, ItemTaxrate, Price, Amount, Remark, CiteUid, CiteItemid,
                                      OrderUid, OrderItemid, CustPO, RowNum, InvoQty, InvoClosed, DisannulMark,
                                      DisannulListerid, DisannulLister, DisannulDate, Custom1, Custom2, Custom3,
                                      Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                      Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{taxprice},
                #{taxamount}, #{taxtotal}, #{itemtaxrate}, #{price}, #{amount}, #{remark}, #{citeuid}, #{citeitemid},
                #{orderuid}, #{orderitemid}, #{custpo}, #{rownum}, #{invoqty}, #{invoclosed}, #{disannulmark},
                #{disannullisterid}, #{disannullister}, #{disannuldate}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_DeductionItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="orderuid != null ">
                OrderUid = #{orderuid},
            </if>
            <if test="orderitemid != null ">
                OrderItemid = #{orderitemid},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="invoqty != null">
                InvoQty = #{invoqty},
            </if>
            <if test="invoclosed != null">
                InvoClosed = #{invoclosed},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_DeductionItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

