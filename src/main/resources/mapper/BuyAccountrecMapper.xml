<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyAccountrecMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyAccountrecPojo">
        select id,
               CarryYear,
               CarryMonth,
               StartDate,
               EndDate,
               Operator,
               Operatorid,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               BillOpenAmount,
               BillInAmount,
               BillOutAmount,
               BillCloseAmount,
               InvoOpenAmount,
               InvoInAmount,
               InvoOutAmount,
               InvoCloseAmount,
               ArapOpenAmount,
               ArapInAmount,
               ArapOutAmount,
               ArapCloseAmount,
               PrintCount,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Buy_AccountRec
        where Buy_AccountRec.id = #{key}
          and Buy_AccountRec.Tenantid = #{tid}
    </select>
    <sql id="selectBuyAccountrecVo">
        select id,
               CarryYear,
               CarryMonth,
               StartDate,
               EndDate,
               Operator,
               Operatorid,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               BillOpenAmount,
               BillInAmount,
               BillOutAmount,
               BillCloseAmount,
               InvoOpenAmount,
               InvoInAmount,
               InvoOutAmount,
               InvoCloseAmount,
               ArapOpenAmount,
               ArapInAmount,
               ArapOutAmount,
               ArapCloseAmount,
               PrintCount,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Buy_AccountRec
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyAccountrecPojo">
        <include refid="selectBuyAccountrecVo"/>
        where 1 = 1 and Buy_AccountRec.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_AccountRec.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.operator != null ">
            and Buy_AccountRec.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Buy_AccountRec.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and Buy_AccountRec.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Buy_AccountRec.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Buy_AccountRec.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Buy_AccountRec.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Buy_AccountRec.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Buy_AccountRec.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Buy_AccountRec.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Buy_AccountRec.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Buy_AccountRec.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Buy_AccountRec.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Buy_AccountRec.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Buy_AccountRec.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Buy_AccountRec.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Buy_AccountRec.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Buy_AccountRec.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Buy_AccountRec.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.operator != null ">
                or Buy_AccountRec.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Buy_AccountRec.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or Buy_AccountRec.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Buy_AccountRec.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Buy_AccountRec.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Buy_AccountRec.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Buy_AccountRec.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Buy_AccountRec.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Buy_AccountRec.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Buy_AccountRec.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Buy_AccountRec.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Buy_AccountRec.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Buy_AccountRec.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Buy_AccountRec.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Buy_AccountRec.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Buy_AccountRec.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Buy_AccountRec.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Buy_AccountRec.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Buy_AccountRec(id, CarryYear, CarryMonth, StartDate, EndDate, Operator, Operatorid, RowNum, Remark,
                                   CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, BillOpenAmount,
                                   BillInAmount, BillOutAmount, BillCloseAmount, InvoOpenAmount, InvoInAmount,
                                   InvoOutAmount, InvoCloseAmount,
                                   ArapOpenAmount, ArapInAmount, ArapOutAmount, ArapCloseAmount, PrintCount, Custom1,
                                   Custom2, Custom3,
                                   Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName,
                                   Revision)
        values (#{id}, #{carryyear}, #{carrymonth}, #{startdate}, #{enddate}, #{operator}, #{operatorid}, #{rownum},
                #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
                #{billopenamount}, #{billinamount}, #{billoutamount}, #{billcloseamount}, #{invoopenamount},
                #{invoinamount}, #{invooutamount},
                #{invocloseamount}, #{arapopenamount}, #{arapinamount}, #{arapoutamount}, #{arapcloseamount},
                #{printcount}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_AccountRec
        <set>
            <if test="carryyear != null">
                CarryYear =#{carryyear},
            </if>
            <if test="carrymonth != null">
                CarryMonth =#{carrymonth},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="billopenamount != null">
                BillOpenAmount =#{billopenamount},
            </if>
            <if test="billinamount != null">
                BillInAmount =#{billinamount},
            </if>
            <if test="billoutamount != null">
                BillOutAmount =#{billoutamount},
            </if>
            <if test="billcloseamount != null">
                BillCloseAmount =#{billcloseamount},
            </if>
            <if test="invoopenamount != null">
                InvoOpenAmount =#{invoopenamount},
            </if>
            <if test="invoinamount != null">
                InvoInAmount =#{invoinamount},
            </if>
            <if test="invooutamount != null">
                InvoOutAmount =#{invooutamount},
            </if>
            <if test="invocloseamount != null">
                InvoCloseAmount =#{invocloseamount},
            </if>
            <if test="arapopenamount != null">
                ArapOpenAmount =#{arapopenamount},
            </if>
            <if test="arapinamount != null">
                ArapInAmount =#{arapinamount},
            </if>
            <if test="arapoutamount != null">
                ArapOutAmount =#{arapoutamount},
            </if>
            <if test="arapcloseamount != null">
                ArapCloseAmount =#{arapcloseamount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_AccountRec
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询单个-->
    <select id="getEntityByMax" resultType="inks.service.sa.som.domain.pojo.BuyAccountrecPojo">
        select id,
               CarryYear,
               CarryMonth,
               StartDate,
               EndDate,
               Operator,
               Operatorid,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               BillOpenAmount,
               BillInAmount,
               BillOutAmount,
               BillCloseAmount,
               InvoOpenAmount,
               InvoInAmount,
               InvoOutAmount,
               InvoCloseAmount,
               ArapOpenAmount,
               ArapInAmount,
               ArapOutAmount,
               ArapCloseAmount,
               PrintCount,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Buy_AccountRec
        where Buy_AccountRec.Tenantid = #{tid}
        Order By RowNum Desc LIMIT 1
    </select>
</mapper>

