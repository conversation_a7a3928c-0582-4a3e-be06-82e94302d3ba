<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.FmCashaccountMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.FmCashaccountPojo">
        select
          id, AccountType, AccountName, MinLimit, MaxLimit, Moneyid, CurrentAmt, RowNum, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, EndInUid, EndInDate, EndOutUid, EndOutDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Fm_CashAccount
        where Fm_CashAccount.id = #{key} and Fm_CashAccount.Tenantid=#{tid}
    </select>

    <sql id="selectFmCashaccountVo">
         select
          id, AccountType, AccountName, MinLimit, MaxLimit, Moneyid, CurrentAmt, RowNum, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, EndInUid, EndInDate, EndOutUid, EndOutDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Fm_CashAccount
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.som.domain.pojo.FmCashaccountPojo">
        <include refid="selectFmCashaccountVo"/>
         where 1 = 1 and Fm_CashAccount.Tenantid =#{tenantid}
         <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Fm_CashAccount.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>

             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.accounttype != null and SearchPojo.accounttype  != ''">
   and Fm_CashAccount.AccountType like concat('%', #{SearchPojo.accounttype}, '%')
</if>
<if test="SearchPojo.accountname != null and SearchPojo.accountname  != ''">
   and Fm_CashAccount.AccountName like concat('%', #{SearchPojo.accountname}, '%')
</if>
<if test="SearchPojo.moneyid != null and SearchPojo.moneyid  != ''">
   and Fm_CashAccount.Moneyid like concat('%', #{SearchPojo.moneyid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
   and Fm_CashAccount.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
   and Fm_CashAccount.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid  != ''">
   and Fm_CashAccount.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
   and Fm_CashAccount.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid  != ''">
   and Fm_CashAccount.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.endinuid != null and SearchPojo.endinuid  != ''">
   and Fm_CashAccount.EndInUid like concat('%', #{SearchPojo.endinuid}, '%')
</if>
<if test="SearchPojo.endoutuid != null and SearchPojo.endoutuid  != ''">
   and Fm_CashAccount.EndOutUid like concat('%', #{SearchPojo.endoutuid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1  != ''">
   and Fm_CashAccount.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2  != ''">
   and Fm_CashAccount.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3  != ''">
   and Fm_CashAccount.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4  != ''">
   and Fm_CashAccount.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5  != ''">
   and Fm_CashAccount.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6  != ''">
   and Fm_CashAccount.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7  != ''">
   and Fm_CashAccount.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8  != ''">
   and Fm_CashAccount.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9  != ''">
   and Fm_CashAccount.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10  != ''">
   and Fm_CashAccount.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
     and (1=0 
<if test="SearchPojo.accounttype != null and SearchPojo.accounttype != ''">
   or Fm_CashAccount.AccountType like concat('%', #{SearchPojo.accounttype}, '%')
</if>
<if test="SearchPojo.accountname != null and SearchPojo.accountname != ''">
   or Fm_CashAccount.AccountName like concat('%', #{SearchPojo.accountname}, '%')
</if>
<if test="SearchPojo.moneyid != null and SearchPojo.moneyid != ''">
   or Fm_CashAccount.Moneyid like concat('%', #{SearchPojo.moneyid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Fm_CashAccount.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   or Fm_CashAccount.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   or Fm_CashAccount.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or Fm_CashAccount.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   or Fm_CashAccount.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.endinuid != null and SearchPojo.endinuid != ''">
   or Fm_CashAccount.EndInUid like concat('%', #{SearchPojo.endinuid}, '%')
</if>
<if test="SearchPojo.endoutuid != null and SearchPojo.endoutuid != ''">
   or Fm_CashAccount.EndOutUid like concat('%', #{SearchPojo.endoutuid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Fm_CashAccount.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Fm_CashAccount.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Fm_CashAccount.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Fm_CashAccount.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Fm_CashAccount.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Fm_CashAccount.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Fm_CashAccount.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Fm_CashAccount.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Fm_CashAccount.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Fm_CashAccount.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
)
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Fm_CashAccount(id, AccountType, AccountName, MinLimit, MaxLimit, Moneyid, CurrentAmt, RowNum, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, EndInUid, EndInDate, EndOutUid, EndOutDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{accounttype}, #{accountname}, #{minlimit}, #{maxlimit}, #{moneyid}, #{currentamt}, #{rownum}, #{enabledmark}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{endinuid}, #{endindate}, #{endoutuid}, #{endoutdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Fm_CashAccount
        <set>
            <if test="accounttype != null ">
                AccountType =#{accounttype},
            </if>
            <if test="accountname != null ">
                AccountName =#{accountname},
            </if>
            <if test="minlimit != null">
                MinLimit =#{minlimit},
            </if>
            <if test="maxlimit != null">
                MaxLimit =#{maxlimit},
            </if>
            <if test="moneyid != null ">
                Moneyid =#{moneyid},
            </if>
            <if test="currentamt != null">
                CurrentAmt =#{currentamt},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="endinuid != null ">
                EndInUid =#{endinuid},
            </if>
            <if test="endindate != null">
                EndInDate =#{endindate},
            </if>
            <if test="endoutuid != null ">
                EndOutUid =#{endoutuid},
            </if>
            <if test="endoutdate != null">
                EndOutDate =#{endoutdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Fm_CashAccount where id = #{key} and Tenantid=#{tid}
    </delete>
    <!--  查询往来单位是否被引用  -->
    <select id="getCiteBillName" resultType="string">
        (SELECT '预收款' as billname From Bus_DepositCash where CashAccid= #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '收款单' as billname From Bus_ReceiptCash where CashAccid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '预付款' as billname From Buy_PrepaymentsCash where CashAccid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '付款单' as billname From Buy_VoucherCash where CashAccid= #{key} and Tenantid = #{tid} LIMIT 1)
    </select>

    <!--  查询往来单位是否被引用  -->
    <select id="getAccountIds" resultType="string">
        select id
        from Fm_CashAccount
        where  Fm_CashAccount.Tenantid = #{tid}
    </select>

    <!--查询指定行数据-->
    <select id="getList"  resultType="inks.service.sa.som.domain.pojo.FmCashaccountPojo">
    <include refid="selectFmCashaccountVo"/>
    where Fm_CashAccount.Tenantid =#{tid} Order By rownum
    </select>
</mapper>

