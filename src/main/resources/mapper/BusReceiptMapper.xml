<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BusReceiptMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BusReceiptPojo">
        SELECT Bus_Receipt.id,
               Bus_Receipt.RefNo,
               Bus_Receipt.BillType,
               Bus_Receipt.BillTitle,
               Bus_Receipt.BillDate,
               Bus_Receipt.Projectid,
               Bus_Receipt.ProjName,
               Bus_Receipt.ProjCode,
               Bus_Receipt.Groupid,
               Bus_Receipt.BillAmount,
               Bus_Receipt.Operator,
               Bus_Receipt.CiteCode,
               Bus_Receipt.ReturnUid,
               Bus_Receipt.OrgUid,
               Bus_Receipt.Summary,
               Bus_Receipt.CreateBy,
               Bus_Receipt.CreateByid,
               Bus_Receipt.CreateDate,
               Bus_Receipt.Lister,
               Bus_Receipt.Listerid,
               Bus_Receipt.ModifyDate,
               Bus_Receipt.Assessor,
               Bus_Receipt.Assessorid,
               Bus_Receipt.AssessDate,
               Bus_Receipt.Custom1,
               Bus_Receipt.Custom2,
               Bus_Receipt.Custom3,
               Bus_Receipt.Custom4,
               Bus_Receipt.Custom5,
               Bus_Receipt.Custom6,
               Bus_Receipt.Custom7,
               Bus_Receipt.Custom8,
               Bus_Receipt.Custom9,
               Bus_Receipt.Custom10,
               Bus_Receipt.Tenantid,
               Bus_Receipt.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Bus_Receipt ON App_Workgroup.id = Bus_Receipt.Groupid
        where Bus_Receipt.id = #{key}
          and Bus_Receipt.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Bus_Receipt.id,
               Bus_Receipt.RefNo,
               Bus_Receipt.BillType,
               Bus_Receipt.BillTitle,
               Bus_Receipt.BillDate,
               Bus_Receipt.Projectid,
               Bus_Receipt.ProjName,
               Bus_Receipt.ProjCode,
               Bus_Receipt.Groupid,
               Bus_Receipt.BillAmount,
               Bus_Receipt.Operator,
               Bus_Receipt.CiteCode,
               Bus_Receipt.ReturnUid,
               Bus_Receipt.OrgUid,
               Bus_Receipt.Summary,
               Bus_Receipt.CreateBy,
               Bus_Receipt.CreateByid,
               Bus_Receipt.CreateDate,
               Bus_Receipt.Lister,
               Bus_Receipt.Listerid,
               Bus_Receipt.ModifyDate,
               Bus_Receipt.Assessor,
               Bus_Receipt.Assessorid,
               Bus_Receipt.AssessDate,
               Bus_Receipt.Custom1,
               Bus_Receipt.Custom2,
               Bus_Receipt.Custom3,
               Bus_Receipt.Custom4,
               Bus_Receipt.Custom5,
               Bus_Receipt.Custom6,
               Bus_Receipt.Custom7,
               Bus_Receipt.Custom8,
               Bus_Receipt.Custom9,
               Bus_Receipt.Custom10,
               Bus_Receipt.Tenantid,
               Bus_Receipt.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Bus_Receipt ON App_Workgroup.id = Bus_Receipt.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT Bus_ReceiptItem.id,
               Bus_ReceiptItem.Pid,
               Bus_ReceiptItem.Invoid,
               Bus_ReceiptItem.InvoBillCode,
               Bus_ReceiptItem.InvoCode,
               Bus_ReceiptItem.Amount,
               Bus_ReceiptItem.RowNum,
               Bus_ReceiptItem.Remark,
               Bus_ReceiptItem.Custom1,
               Bus_ReceiptItem.Custom2,
               Bus_ReceiptItem.Custom3,
               Bus_ReceiptItem.Custom4,
               Bus_ReceiptItem.Custom5,
               Bus_ReceiptItem.Custom6,
               Bus_ReceiptItem.Custom7,
               Bus_ReceiptItem.Custom8,
               Bus_ReceiptItem.Custom9,
               Bus_ReceiptItem.Custom10,
               Bus_ReceiptItem.Tenantid,
               Bus_ReceiptItem.Revision,
               Bus_Receipt.RefNo,
               Bus_Receipt.BillType,
               Bus_Receipt.BillTitle,
               Bus_Receipt.BillDate,
               Bus_Receipt.Projectid,
               Bus_Receipt.ProjName,
               Bus_Receipt.ProjCode,
               Bus_Receipt.BillAmount,
               Bus_Receipt.Operator,
               Bus_Receipt.Summary,
               Bus_Receipt.CreateBy,
               Bus_Receipt.Lister,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Bus_ReceiptItem
                 LEFT JOIN Bus_Receipt ON Bus_ReceiptItem.Pid = Bus_Receipt.id
                 LEFT JOIN App_Workgroup ON Bus_Receipt.Groupid = App_Workgroup.id

    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusReceiptitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_Receipt.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Receipt.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Bus_Receipt.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Bus_Receipt.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Bus_Receipt.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Bus_Receipt.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Bus_Receipt.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
            and Bus_Receipt.citecode like concat('%', #{SearchPojo.citecode}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            and Bus_Receipt.returnuid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            and Bus_Receipt.orguid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Bus_Receipt.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Bus_Receipt.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Bus_Receipt.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Bus_Receipt.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Bus_Receipt.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Bus_Receipt.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Bus_Receipt.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_Receipt.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_Receipt.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_Receipt.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_Receipt.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_Receipt.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_Receipt.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_Receipt.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_Receipt.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_Receipt.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_Receipt.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Bus_Receipt.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Bus_Receipt.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Bus_Receipt.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Bus_Receipt.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
                or Bus_Receipt.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
                or Bus_Receipt.CiteCode like concat('%', #{SearchPojo.citecode}, '%')
            </if>
            <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
                or Bus_Receipt.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
            </if>
            <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
                or Bus_Receipt.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Bus_Receipt.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Bus_Receipt.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Bus_Receipt.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Bus_Receipt.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Bus_Receipt.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Bus_Receipt.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Bus_Receipt.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Bus_Receipt.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Bus_Receipt.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Bus_Receipt.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Bus_Receipt.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Bus_Receipt.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Bus_Receipt.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Bus_Receipt.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Bus_Receipt.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Bus_Receipt.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Bus_Receipt.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusReceiptPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_Receipt.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Receipt.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Bus_Receipt.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Bus_Receipt.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Bus_Receipt.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Bus_Receipt.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Bus_Receipt.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
            and Bus_Receipt.CiteCode like concat('%', #{SearchPojo.citecode}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            and Bus_Receipt.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            and Bus_Receipt.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Bus_Receipt.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Bus_Receipt.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Bus_Receipt.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Bus_Receipt.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Bus_Receipt.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Bus_Receipt.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Bus_Receipt.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_Receipt.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_Receipt.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_Receipt.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_Receipt.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_Receipt.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_Receipt.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_Receipt.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_Receipt.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_Receipt.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_Receipt.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Bus_Receipt.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Bus_Receipt.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Bus_Receipt.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Bus_Receipt.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
                or Bus_Receipt.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
                or Bus_Receipt.CiteCode like concat('%', #{SearchPojo.citecode}, '%')
            </if>
            <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
                or Bus_Receipt.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
            </if>
            <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
                or Bus_Receipt.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Bus_Receipt.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Bus_Receipt.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Bus_Receipt.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Bus_Receipt.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Bus_Receipt.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Bus_Receipt.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Bus_Receipt.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Bus_Receipt.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Bus_Receipt.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Bus_Receipt.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Bus_Receipt.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Bus_Receipt.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Bus_Receipt.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Bus_Receipt.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Bus_Receipt.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Bus_Receipt.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Bus_Receipt.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
            <if test="SearchPojo.billamount != null ">
                or Bus_Receipt.BillAmount= #{SearchPojo.billamount}
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_Receipt(id, RefNo, BillType, BillTitle, BillDate,Projectid, ProjCode, ProjName,  Groupid, BillAmount, Operator, CiteCode, ReturnUid, OrgUid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, FmDocMark, FmDocCode, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{groupid}, #{billamount}, #{operator}, #{citecode}, #{returnuid}, #{orguid}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{fmdocmark}, #{fmdoccode}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_Receipt
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="citecode != null ">
                CiteCode =#{citecode},
            </if>
            <if test="returnuid != null ">
                ReturnUid =#{returnuid},
            </if>
            <if test="orguid != null ">
                OrgUid =#{orguid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="fmdocmark != null">
                FmDocMark =#{fmdocmark},
            </if>
            <if test="fmdoccode != null ">
                FmDocCode =#{fmdoccode},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>


    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_Receipt
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Bus_Receipt
        SET Assessor   = #{assessor},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BusReceiptPojo">
        select
        id
        from Bus_ReceiptItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <!--查询DelListIds-->
    <select id="getDelCashIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BusReceiptPojo">
        select
        id
        from Bus_ReceiptCash
        where Pid = #{id}
        <if test="cash !=null and cash.size()>0">
            and id not in
            <foreach collection="cash" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--    刷新发货完成数 Eric ********-->
    <!--    记得同时修改fm服务的 updateBusInvoFinish方法 使得SQL相同-->
    <update id="updateBusInvoFinish">
        update Bus_Invoice
        SET Receipted =COALESCE((SELECT SUM(Bus_ReceiptItem.Amount)
                                 FROM Bus_ReceiptItem
                                          LEFT OUTER JOIN Bus_Receipt
                                                          ON Bus_ReceiptItem.pid = Bus_Receipt.id
                                 where Bus_ReceiptItem.InvoBillCode = #{refno}
                                   and Bus_ReceiptItem.Invoid = #{key}
                                   and Bus_ReceiptItem.Tenantid = #{tid}), 0) +
                       COALESCE((SELECT SUM(Fm_PayApplyItem.Amount)
                                 FROM Fm_PayApply
                                          RIGHT JOIN Fm_PayApplyItem ON Fm_PayApplyItem.Pid = Fm_PayApply.id
                                 where Fm_PayApplyItem.InvoBillCode = #{refno}
                                   and Fm_PayApplyItem.InvoBillid = #{key}
                                   and Fm_PayApplyItem.Tenantid = #{tid}), 0),
            FirstAmt=COALESCE((SELECT SUM(Fm_PayApplyItem.Amount)
                               FROM Fm_PayApply
                                        RIGHT JOIN Fm_PayApplyItem ON Fm_PayApplyItem.Pid = Fm_PayApply.id
                               where Fm_PayApplyItem.InvoBillCode = #{refno}
                                 and Fm_PayApplyItem.InvoBillid = #{key}
                                 and Fm_PayApplyItem.Tenantid = #{tid}), 0),
            LastAmt=COALESCE((SELECT SUM(Bus_ReceiptItem.Amount)
                              FROM Bus_ReceiptItem
                                       LEFT OUTER JOIN Bus_Receipt
                                                       ON Bus_ReceiptItem.pid = Bus_Receipt.id
                              where Bus_ReceiptItem.InvoBillCode = #{refno}
                                and Bus_ReceiptItem.Invoid = #{key}
                                and Bus_ReceiptItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
<!--    <update id="updateBusInvoFinish原始">-->
<!--        update Bus_Invoice-->
<!--        SET Receipted =COALESCE((SELECT SUM(Bus_ReceiptItem.Amount)-->
<!--                                 FROM Bus_ReceiptItem-->
<!--                                          LEFT OUTER JOIN Bus_Receipt-->
<!--                                                          ON Bus_ReceiptItem.pid = Bus_Receipt.id-->
<!--                                 where Bus_ReceiptItem.InvoBillCode = #{refno}-->
<!--                                   and Bus_ReceiptItem.Invoid = #{key}-->
<!--                                   and Bus_ReceiptItem.Tenantid = #{tid}), 0) +-->
<!--                       COALESCE((SELECT SUM(Fm_PayApplyItem.Amount)-->
<!--                                 FROM Fm_PayApply-->
<!--                                          RIGHT JOIN Fm_PayApplyItem ON Fm_PayApplyItem.Pid = Fm_PayApply.id-->
<!--                                 where Fm_PayApplyItem.InvoBillCode = #{refno}-->
<!--                                   and Fm_PayApplyItem.InvoBillid = #{key}-->
<!--                                   and Fm_PayApplyItem.Tenantid = #{tid}), 0),-->
<!--            FirstAmt= COALESCE((SELECT SUM(Fm_PayApplyItem.Amount)-->
<!--                                FROM Fm_PayApply-->
<!--                                         RIGHT JOIN Fm_PayApplyItem ON Fm_PayApplyItem.Pid = Fm_PayApply.id-->
<!--                                where Fm_PayApplyItem.InvoBillCode = #{refno}-->
<!--                                  and Fm_PayApplyItem.InvoBillid = #{key}-->
<!--                                  and Fm_PayApplyItem.Tenantid = #{tid}), 0),-->
<!--            LastAmt=COALESCE((SELECT SUM(Bus_ReceiptItem.Amount)-->
<!--                              FROM Bus_ReceiptItem-->
<!--                                       LEFT OUTER JOIN Bus_Receipt-->
<!--                                                       ON Bus_ReceiptItem.pid = Bus_Receipt.id-->
<!--                              where Bus_ReceiptItem.InvoBillCode = #{refno}-->
<!--                                and Bus_ReceiptItem.Invoid = #{key}-->
<!--                                and Bus_ReceiptItem.Tenantid = #{tid}), 0)-->
<!--        where id = #{key}-->
<!--          and Tenantid = #{tid}-->
<!--    </update>-->

    <update id="updateBusInvoItemAvgAmt">
        update Bus_InvoiceItem
        SET AvgFirstAmt=COALESCE((SELECT FirstAmt/TaxAmount FROM Bus_Invoice where id=#{key} and Tenantid=#{tid})*Bus_InvoiceItem.TaxAmount,0),
        AvgLastAmt=COALESCE((SELECT LastAmt/TaxAmount FROM Bus_Invoice where id=#{key} and Tenantid=#{tid})*Bus_InvoiceItem.TaxAmount,0)
        where Pid = #{key}
        and Tenantid = #{tid}
    </update>


    <update id="updateBusMachingItemAvgAmt">
        update Bus_MachiningItem
        SET AvgFirstAmt=COALESCE((SELECT SUM(AvgFirstAmt) FROM Bus_InvoiceItem where MachItemid=Bus_MachiningItem.id),0),
        AvgLastAmt=COALESCE((SELECT SUM(AvgLastAmt) FROM Bus_InvoiceItem where MachItemid=Bus_MachiningItem.id),0)
        where id in(select distinct MachItemid from Bus_InvoiceItem where Pid=#{key} and Tenantid=#{tid})
        and Tenantid = #{tid}
    </update>


    <update id="updateBusMachingFirstLastAmt">
        update Bus_Machining
        SET FirstAmt=COALESCE((SELECT SUM(AvgFirstAmt) FROM Bus_MachiningItem where Pid=Bus_Machining.id),0),
        LastAmt=COALESCE((SELECT SUM(AvgLastAmt) FROM Bus_MachiningItem where Pid=Bus_Machining.id),0)
        where id in(select distinct Pid from Bus_MachiningItem where id in(select distinct MachItemid from Bus_InvoiceItem where Pid=#{key} and Tenantid=#{tid}))
        and Tenantid = #{tid}
    </update>




    <!--    刷新发货完成数 Eric ********-->
    <update id="updateDeliFinish">
        update Bus_Deliery
        SET BillReceived =COALESCE((SELECT SUM(Bus_ReceiptItem.Amount)
                                 FROM Bus_ReceiptItem
                                          LEFT OUTER JOIN Bus_Receipt
                                                          ON Bus_ReceiptItem.pid = Bus_Receipt.id
                                 where Bus_ReceiptItem.InvoBillCode = #{refno}
                                   and Bus_ReceiptItem.Invoid = #{key}
                                   and Bus_ReceiptItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric ********-->
    <update id="updateCashAmount">
        update Fm_CashAccount
        SET CurrentAmt =CurrentAmt + #{amount}
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--查询DelListIds-->
    <select id="getCashAmount" resultType="java.lang.Double">
        select CurrentAmt
        from Fm_CashAccount
        where id = #{key}
          and Tenantid = #{tid}
    </select>

    <update id="updateOrgReturn">
        update Buy_Voucher
        SET ReturnUid = #{redUid}
        where RefNo= #{orgUid}
        and Tenantid = #{tid}
    </update>
</mapper>

