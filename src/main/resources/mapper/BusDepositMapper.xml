<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BusDepositMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BusDepositPojo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Bus_Deposit.id,
               Bus_Deposit.RefNo,
               Bus_Deposit.BillType,
               Bus_Deposit.BillTitle,
               Bus_Deposit.BillDate,
               Bus_Deposit.Projectid,
               Bus_Deposit.ProjName,
               Bus_Deposit.ProjCode,
               Bus_Deposit.Groupid,
               Bus_Deposit.BillAmount,
               Bus_Deposit.Operator,
               Bus_Deposit.CiteCode,
               Bus_Deposit.OutAmount,
               Bus_Deposit.ReturnUid,
               Bus_Deposit.OrgUid,
               Bus_Deposit.Summary,
               Bus_Deposit.CreateBy,
               Bus_Deposit.CreateByid,
               Bus_Deposit.CreateDate,
               Bus_Deposit.Lister,
               Bus_Deposit.Listerid,
               Bus_Deposit.ModifyDate,
               Bus_Deposit.Assessor,
               Bus_Deposit.Assessorid,
               Bus_Deposit.AssessDate,
               Bus_Deposit.LockedAmount,
               Bus_Deposit.Custom1,
               Bus_Deposit.Custom2,
               Bus_Deposit.Custom3,
               Bus_Deposit.Custom4,
               Bus_Deposit.Custom5,
               Bus_Deposit.Custom6,
               Bus_Deposit.Custom7,
               Bus_Deposit.Custom8,
               Bus_Deposit.Custom9,
               Bus_Deposit.Custom10,
               Bus_Deposit.Tenantid,
               Bus_Deposit.Revision
        FROM App_Workgroup
                 RIGHT JOIN Bus_Deposit ON App_Workgroup.id = Bus_Deposit.Groupid
        where Bus_Deposit.id = #{key}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Bus_Deposit.id,
               Bus_Deposit.RefNo,
               Bus_Deposit.BillType,
               Bus_Deposit.BillTitle,
               Bus_Deposit.BillDate,
               Bus_Deposit.Projectid,
               Bus_Deposit.ProjName,
               Bus_Deposit.ProjCode,
               Bus_Deposit.Groupid,
               Bus_Deposit.BillAmount,
               Bus_Deposit.Operator,
               Bus_Deposit.CiteCode,
               Bus_Deposit.OutAmount,
               Bus_Deposit.ReturnUid,
               Bus_Deposit.OrgUid,
               Bus_Deposit.Summary,
               Bus_Deposit.CreateBy,
               Bus_Deposit.CreateByid,
               Bus_Deposit.CreateDate,
               Bus_Deposit.Lister,
               Bus_Deposit.Listerid,
               Bus_Deposit.ModifyDate,
               Bus_Deposit.Assessor,
               Bus_Deposit.Assessorid,
               Bus_Deposit.AssessDate,
               Bus_Deposit.LockedAmount,
               Bus_Deposit.Custom1,
               Bus_Deposit.Custom2,
               Bus_Deposit.Custom3,
               Bus_Deposit.Custom4,
               Bus_Deposit.Custom5,
               Bus_Deposit.Custom6,
               Bus_Deposit.Custom7,
               Bus_Deposit.Custom8,
               Bus_Deposit.Custom9,
               Bus_Deposit.Custom10,
               Bus_Deposit.Tenantid,
               Bus_Deposit.Revision
        FROM App_Workgroup
                 RIGHT JOIN Bus_Deposit ON App_Workgroup.id = Bus_Deposit.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT Bus_DepositItem.id,
               Bus_DepositItem.Pid,
               Bus_DepositItem.MachBillid,
               Bus_DepositItem.MachBillCode,
               Bus_DepositItem.DeliBillid,
               Bus_DepositItem.DeliBillCode,
               Bus_DepositItem.BillTaxAmount,
               Bus_DepositItem.Amount,
               Bus_DepositItem.RowNum,
               Bus_DepositItem.Remark,
               Bus_DepositItem.Custom1,
               Bus_DepositItem.Custom2,
               Bus_DepositItem.Custom3,
               Bus_DepositItem.Custom4,
               Bus_DepositItem.Custom5,
               Bus_DepositItem.Custom6,
               Bus_DepositItem.Custom7,
               Bus_DepositItem.Custom8,
               Bus_DepositItem.Custom9,
               Bus_DepositItem.Custom10,
               Bus_DepositItem.Tenantid,
               Bus_DepositItem.Revision,
               Bus_Deposit.RefNo,
               Bus_Deposit.BillType,
               Bus_Deposit.BillTitle,
               Bus_Deposit.BillDate,
               Bus_Deposit.Projectid,
               Bus_Deposit.ProjName,
               Bus_Deposit.ProjCode,
               Bus_Deposit.BillAmount,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Bus_Deposit.Operator,
               Bus_Deposit.CreateBy,
               Bus_Deposit.Lister,
               Bus_Deposit.OutAmount,
               Bus_Deposit.LockedAmount
        FROM Bus_DepositItem
                 LEFT JOIN Bus_Deposit ON Bus_DepositItem.Pid = Bus_Deposit.id
                 LEFT JOIN App_Workgroup ON Bus_Deposit.Groupid = App_Workgroup.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusDeposititemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Deposit.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>


    <select id="getCashPageList" resultType="inks.service.sa.som.domain.pojo.BusDepositcashdetailPojo">
        SELECT Bus_DepositCash.*,
        Bus_Deposit.RefNo,
        Bus_Deposit.BillType,
        Bus_Deposit.BillTitle,
        Bus_Deposit.BillDate,
        Bus_Deposit.Projectid,
        Bus_Deposit.ProjName,
        Bus_Deposit.ProjCode,
        Bus_Deposit.BillAmount,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate,
        Bus_Deposit.Summary,
        Bus_Deposit.Lister,
        Bus_Deposit.Assessor,
        Bus_Deposit.OutAmount,
        Bus_Deposit.LockedAmount
        FROM Bus_DepositCash
        LEFT JOIN Bus_Deposit ON Bus_DepositCash.Pid = Bus_Deposit.id
        LEFT JOIN App_Workgroup ON Bus_Deposit.Groupid = App_Workgroup.id
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Deposit.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>


    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Bus_Deposit.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Bus_Deposit.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Bus_Deposit.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Bus_Deposit.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Bus_Deposit.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
            and Bus_Deposit.citecode like concat('%', #{SearchPojo.citecode}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            and Bus_Deposit.returnuid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            and Bus_Deposit.orguid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Bus_Deposit.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Bus_Deposit.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Bus_Deposit.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Bus_Deposit.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Bus_Deposit.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Bus_Deposit.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Bus_Deposit.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_Deposit.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_Deposit.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_Deposit.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_Deposit.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_Deposit.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_Deposit.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_Deposit.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_Deposit.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_Deposit.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_Deposit.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.machbillid != null and SearchPojo.machbillid != ''">
            and Bus_DepositItem.machbillid like concat('%', #{SearchPojo.machbillid}, '%')
        </if>
        <if test="SearchPojo.machbillcode != null and SearchPojo.machbillcode != ''">
            and Bus_DepositItem.machbillcode like concat('%', #{SearchPojo.machbillcode}, '%')
        </if>
        <if test="SearchPojo.delibillid != null and SearchPojo.delibillid != ''">
            and Bus_DepositItem.delibillid like concat('%', #{SearchPojo.delibillid}, '%')
        </if>
        <if test="SearchPojo.delibillcode != null and SearchPojo.delibillcode != ''">
            and Bus_DepositItem.delibillcode like concat('%', #{SearchPojo.delibillcode}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Bus_Deposit.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Bus_Deposit.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Bus_Deposit.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Bus_Deposit.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
                or Bus_Deposit.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
                or Bus_Deposit.CiteCode like concat('%', #{SearchPojo.citecode}, '%')
            </if>
            <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
                or Bus_Deposit.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
            </if>
            <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
                or Bus_Deposit.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Bus_Deposit.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Bus_Deposit.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Bus_Deposit.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Bus_Deposit.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Bus_Deposit.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Bus_Deposit.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Bus_Deposit.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Bus_Deposit.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Bus_Deposit.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Bus_Deposit.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Bus_Deposit.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Bus_Deposit.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Bus_Deposit.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Bus_Deposit.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Bus_Deposit.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Bus_Deposit.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Bus_Deposit.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.machbillid != null and SearchPojo.machbillid != ''">
                or Bus_DepositItem.MachBillid like concat('%', #{SearchPojo.machbillid}, '%')
            </if>
            <if test="SearchPojo.machbillcode != null and SearchPojo.machbillcode != ''">
                or Bus_DepositItem.MachBillCode like concat('%', #{SearchPojo.machbillcode}, '%')
            </if>
            <if test="SearchPojo.delibillid != null and SearchPojo.delibillid != ''">
                or Bus_DepositItem.DeliBillid like concat('%', #{SearchPojo.delibillid}, '%')
            </if>
            <if test="SearchPojo.delibillcode != null and SearchPojo.delibillcode != ''">
                or Bus_DepositItem.DeliBillCode like concat('%', #{SearchPojo.delibillcode}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusDepositPojo">
        <include refid="selectbillVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Deposit.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Bus_Deposit.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Bus_Deposit.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Bus_Deposit.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Bus_Deposit.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Bus_Deposit.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
            and Bus_Deposit.CiteCode like concat('%', #{SearchPojo.citecode}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            and Bus_Deposit.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            and Bus_Deposit.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Bus_Deposit.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Bus_Deposit.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Bus_Deposit.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Bus_Deposit.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Bus_Deposit.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Bus_Deposit.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Bus_Deposit.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_Deposit.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_Deposit.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_Deposit.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_Deposit.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_Deposit.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_Deposit.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_Deposit.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_Deposit.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_Deposit.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_Deposit.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Bus_Deposit.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Bus_Deposit.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Bus_Deposit.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Bus_Deposit.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
                or Bus_Deposit.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
                or Bus_Deposit.CiteCode like concat('%', #{SearchPojo.citecode}, '%')
            </if>
            <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
                or Bus_Deposit.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
            </if>
            <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
                or Bus_Deposit.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
            </if>
            <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
                or Bus_Deposit.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Bus_Deposit.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Bus_Deposit.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Bus_Deposit.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Bus_Deposit.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
                or Bus_Deposit.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
                or Bus_Deposit.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Bus_Deposit.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Bus_Deposit.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Bus_Deposit.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Bus_Deposit.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Bus_Deposit.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Bus_Deposit.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Bus_Deposit.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Bus_Deposit.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Bus_Deposit.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Bus_Deposit.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
            <if test="SearchPojo.billamount != null ">
                or Bus_Deposit.BillAmount= #{SearchPojo.billamount}
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_Deposit(id, RefNo, BillType, BillTitle, BillDate, Projectid, ProjCode, ProjName, Groupid, BillAmount, Operator, CiteCode, OutAmount, ReturnUid, OrgUid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, FmDocMark, FmDocCode, LockedAmount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{groupid}, #{billamount}, #{operator}, #{citecode}, #{outamount}, #{returnuid}, #{orguid}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{fmdocmark}, #{fmdoccode}, #{lockedamount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_Deposit
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="citecode != null ">
                CiteCode =#{citecode},
            </if>
            <if test="outamount != null">
                OutAmount =#{outamount},
            </if>
            <if test="returnuid != null ">
                ReturnUid =#{returnuid},
            </if>
            <if test="orguid != null ">
                OrgUid =#{orguid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="fmdocmark != null">
                FmDocMark =#{fmdocmark},
            </if>
            <if test="fmdoccode != null ">
                FmDocCode =#{fmdoccode},
            </if>
            <if test="lockedamount != null">
            LockedAmount =#{lockedamount},
        </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>
    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_Deposit
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Bus_Deposit
        SET Assessor   = #{assessor},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BusDepositPojo">
        select
        id
        from Bus_DepositItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <!--查询DelCashListIds-->
    <select id="getDelCashIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BusDepositPojo">
        select
        id
        from Bus_DepositCash
        where Pid = #{id}
        <if test="cash != null and cash.size() > 0">
            and id not in
            <foreach collection="cash" item="c" index="index" open="(" close=")" separator=",">
                #{c.id}
            </foreach>
        </if>
    </select>

    <!--    刷新发货完成数 Eric ********-->
    <update id="updateCashAmount">
        update Fm_CashAccount
        SET CurrentAmt =CurrentAmt + #{amount}
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新预收款额 Eric ********-->
    <!--    记得同时修改fm服务2个方法: updateMachAdvaAmountFirstAmt和updateMachItemAvgFirstAmt方法 使得SQL相同-->
<!--    <update id="updateMachAdvaAmountFirstAmt原版">-->
<!--        update Bus_Machining-->
<!--        SET AdvaAmount =COALESCE((SELECT sum(Bus_DepositItem.Amount)-->
<!--                                  FROM Bus_DepositItem-->
<!--                                           LEFT OUTER JOIN Bus_Deposit-->
<!--                                                           ON Bus_DepositItem.pid = Bus_Deposit.id-->
<!--                                  where Bus_Deposit.BillType IN ('销售预收','预收红冲')-->
<!--                                    and Bus_DepositItem.machbillid = #{key}-->
<!--                                    and Bus_DepositItem.Tenantid = #{tid}), 0)-->
<!--            + COALESCE((SELECT sum(Fm_DepToMachItem.Amount)-->
<!--                        FROM Fm_DepToMachItem-->
<!--                                 LEFT OUTER JOIN Fm_DepToMach ON Fm_DepToMachItem.pid = Fm_DepToMach.id-->
<!--                        where Fm_DepToMachItem.machbillid = #{key}-->
<!--                          and Fm_DepToMachItem.Tenantid = #{tid}), 0),-->
<!--            FirstAmt=AdvaAmount-->
<!--        where id = #{key}-->
<!--          and Tenantid = #{tid}-->
<!--    </update>-->
    <update id="updateMachAdvaAmountFirstAmt">
        update Bus_Machining
        SET AdvaAmount =COALESCE((SELECT sum(Bus_DepositItem.Amount)
                                  FROM Bus_DepositItem
                                           LEFT OUTER JOIN Bus_Deposit
                                                           ON Bus_DepositItem.pid = Bus_Deposit.id
                                  where Bus_Deposit.BillType IN ('销售预收', '预收红冲')
                                    and Bus_DepositItem.machbillid = #{key}), 0)
            + COALESCE((SELECT sum(Fm_DepoTransferMach.Amount)
                        FROM Fm_DepoTransferMach
                        where Fm_DepoTransferMach.machbillid = #{key})
                            , 0),
            FirstAmt=AdvaAmount
        where id = #{key}
    </update>
    <update id="updateMachItemAvgFirstAmt">
        UPDATE Bus_MachiningItem
        SET AvgFirstAmt = (SELECT tmp.FirstAmt / tmp.ItemCount
                           FROM (SELECT (SELECT FirstAmt FROM Bus_Machining WHERE id = #{machid}) AS FirstAmt,
                                        COUNT(*)                                                  AS ItemCount
                                 FROM Bus_MachiningItem
                                 WHERE Pid = #{machid}) AS tmp)
        WHERE Bus_MachiningItem.Pid = #{machid}
    </update>

    <update id="updateDepositOutAmount">
        UPDATE Bus_Deposit
        SET OutAmount = COALESCE((SELECT SUM(Fm_DepoTransferItem.Amount)
                                  FROM Fm_DepoTransferItem
                                  WHERE Fm_DepoTransferItem.PayBillid = #{paybillid}), 0)
        WHERE id = #{paybillid}
    </update>



    <!--    MySQL 不允许在FROM子句中引用要更新的目标表。-->
<!--    <update id="updateMachItemAvgFirstAmt">-->
<!--        UPDATE Bus_MachiningItem-->
<!--        SET AvgFirstAmt = (-->
<!--            (SELECT FirstAmt FROM Bus_Machining WHERE id = #{machid})-->
<!--                /-->
<!--            (SELECT COUNT(*) FROM Bus_MachiningItem WHERE Pid = #{machid})-->
<!--            )-->
<!--        WHERE Bus_MachiningItem.Pid = #{machid};-->
<!--    </update>-->
</mapper>

