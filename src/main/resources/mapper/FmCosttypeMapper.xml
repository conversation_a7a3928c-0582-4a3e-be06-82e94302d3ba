<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.FmCosttypeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.FmCosttypePojo">
        select
          id, Parentid, Direction, CostCode, CostName, RowNum, Remark, EnabledMark, DeleteMark, DeleteListerid, DeleteLister, DeleteDate, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Fm_CostType
        where Fm_CostType.id = #{key} and Fm_CostType.Tenantid=#{tid}
    </select>
    <sql id="selectFmCosttypeVo">
         select
          id, Parentid, Direction, CostCode, CostName, RowNum, Remark, EnabledMark, DeleteMark, DeleteListerid, DeleteLister, DeleteDate, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Fm_CostType
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.som.domain.pojo.FmCosttypePojo">
        <include refid="selectFmCosttypeVo"/>
         where 1 = 1 and Fm_CostType.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Fm_CostType.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.parentid != null ">
   and Fm_CostType.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.direction != null ">
   and Fm_CostType.Direction like concat('%', #{SearchPojo.direction}, '%')
</if>
<if test="SearchPojo.costcode != null ">
   and Fm_CostType.CostCode like concat('%', #{SearchPojo.costcode}, '%')
</if>
<if test="SearchPojo.costname != null ">
   and Fm_CostType.CostName like concat('%', #{SearchPojo.costname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Fm_CostType.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.deletelisterid != null ">
   and Fm_CostType.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   and Fm_CostType.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Fm_CostType.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Fm_CostType.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Fm_CostType.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Fm_CostType.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Fm_CostType.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Fm_CostType.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Fm_CostType.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Fm_CostType.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Fm_CostType.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Fm_CostType.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Fm_CostType.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Fm_CostType.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Fm_CostType.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Fm_CostType.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Fm_CostType.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.parentid != null ">
   or Fm_CostType.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.direction != null ">
   or Fm_CostType.Direction like concat('%', #{SearchPojo.direction}, '%')
</if>
<if test="SearchPojo.costcode != null ">
   or Fm_CostType.CostCode like concat('%', #{SearchPojo.costcode}, '%')
</if>
<if test="SearchPojo.costname != null ">
   or Fm_CostType.CostName like concat('%', #{SearchPojo.costname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Fm_CostType.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.deletelisterid != null ">
   or Fm_CostType.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   or Fm_CostType.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Fm_CostType.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Fm_CostType.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Fm_CostType.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Fm_CostType.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Fm_CostType.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Fm_CostType.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Fm_CostType.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Fm_CostType.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Fm_CostType.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Fm_CostType.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Fm_CostType.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Fm_CostType.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Fm_CostType.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Fm_CostType.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Fm_CostType.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Fm_CostType(id, Parentid, Direction, CostCode, CostName, RowNum, Remark, EnabledMark, DeleteMark, DeleteListerid, DeleteLister, DeleteDate, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{parentid}, #{direction}, #{costcode}, #{costname}, #{rownum}, #{remark}, #{enabledmark}, #{deletemark}, #{deletelisterid}, #{deletelister}, #{deletedate}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Fm_CostType
        <set>
            <if test="parentid != null ">
                Parentid =#{parentid},
            </if>
            <if test="direction != null ">
                Direction =#{direction},
            </if>
            <if test="costcode != null ">
                CostCode =#{costcode},
            </if>
            <if test="costname != null ">
                CostName =#{costname},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelisterid != null ">
                DeleteListerid =#{deletelisterid},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Fm_CostType where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getCiteBillName" resultType="java.lang.String">
        (SELECT '其他收入' as billname From Fm_IncomeItem where CostTypeid= #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '费用开支' as billname From Fm_CostItem where CostTypeid = #{key} and Tenantid = #{tid} LIMIT 1)
    </select>
</mapper>

