<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.FmPayapplycashMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.FmPayapplycashPojo">
        select
          id, Pid, PayBillid, PayBillCode, PayBillAmount, Amount, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Fm_PayApplyCash
        where Fm_PayApplyCash.id = #{key} and Fm_PayApplyCash.Tenantid=#{tid}
    </select>
    <sql id="selectFmPayapplycashVo">
         select
          id, Pid, PayBillid, PayBillCode, PayBillAmount, Amount, <PERSON>Num, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Fm_PayApplyCash
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.som.domain.pojo.FmPayapplycashPojo">
        <include refid="selectFmPayapplycashVo"/>
         where 1 = 1 and Fm_PayApplyCash.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Fm_PayApplyCash.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Fm_PayApplyCash.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.paybillid != null and SearchPojo.paybillid != ''">
   and Fm_PayApplyCash.paybillid like concat('%', #{SearchPojo.paybillid}, '%')
</if>
<if test="SearchPojo.paybillcode != null and SearchPojo.paybillcode != ''">
   and Fm_PayApplyCash.paybillcode like concat('%', #{SearchPojo.paybillcode}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Fm_PayApplyCash.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Fm_PayApplyCash.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Fm_PayApplyCash.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Fm_PayApplyCash.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Fm_PayApplyCash.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Fm_PayApplyCash.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Fm_PayApplyCash.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Fm_PayApplyCash.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Fm_PayApplyCash.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Fm_PayApplyCash.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Fm_PayApplyCash.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Fm_PayApplyCash.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.paybillid != null and SearchPojo.paybillid != ''">
   or Fm_PayApplyCash.PayBillid like concat('%', #{SearchPojo.paybillid}, '%')
</if>
<if test="SearchPojo.paybillcode != null and SearchPojo.paybillcode != ''">
   or Fm_PayApplyCash.PayBillCode like concat('%', #{SearchPojo.paybillcode}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Fm_PayApplyCash.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Fm_PayApplyCash.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Fm_PayApplyCash.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Fm_PayApplyCash.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Fm_PayApplyCash.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Fm_PayApplyCash.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Fm_PayApplyCash.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Fm_PayApplyCash.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Fm_PayApplyCash.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Fm_PayApplyCash.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Fm_PayApplyCash.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.FmPayapplycashPojo">
        select
          id, Pid, PayBillid, PayBillCode, PayBillAmount, Amount, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Fm_PayApplyCash
        where Fm_PayApplyCash.Pid = #{Pid} and Fm_PayApplyCash.Tenantid=#{tid}
        order by RowNum 
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Fm_PayApplyCash(id, Pid, PayBillid, PayBillCode, PayBillAmount, Amount, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{paybillid}, #{paybillcode}, #{paybillamount}, #{amount}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Fm_PayApplyCash
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="paybillid != null ">
                PayBillid = #{paybillid},
            </if>
            <if test="paybillcode != null ">
                PayBillCode = #{paybillcode},
            </if>
            <if test="paybillamount != null">
                PayBillAmount = #{paybillamount},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Fm_PayApplyCash where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

