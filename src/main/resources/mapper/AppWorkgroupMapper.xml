<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.AppWorkgroupMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.AppWorkgroupPojo">
        SELECT App_Workgroup.id,
               App_Workgroup.WgGroupid,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.GroupClass,
               App_Workgroup.Linkman,
               App_Workgroup.Telephone,
               App_Workgroup.GroupFax,
               App_Workgroup.GroupAdd,
               App_Workgroup.Remark,
               App_Workgroup.InvalidDate,
               App_Workgroup.GroupType,
               App_Workgroup.CreditDUint,
               App_Workgroup.CreditDQuantity,
               App_Workgroup.CreditCUint,
               App_Workgroup.CreditCQuantity,
               App_Workgroup.RowNum,
               App_Workgroup.CreateBy,
               App_Workgroup.CreateByid,
               App_Workgroup.CreateDate,
               App_Workgroup.Lister,
               App_Workgroup.Listerid,
               App_Workgroup.ModifyDate,
               App_Workgroup.Mobile,
               App_Workgroup.LinkmanS,
               App_Workgroup.TelephoneS,
               App_Workgroup.MobileS,
               App_Workgroup.Country,
               App_Workgroup.Province,
               App_Workgroup.GroupZip,
               App_Workgroup.DeliverAdd,
               App_Workgroup.InvoiceAdd,
               App_Workgroup.Seller,
               App_Workgroup.GroupLabel,
               App_Workgroup.GroupLevel,
               App_Workgroup.GroupState,
               App_Workgroup.Source,
               App_Workgroup.Credit,
               App_Workgroup.PaymentMethod,
               App_Workgroup.CreditCode,
               App_Workgroup.DepositBank,
               App_Workgroup.BankAccount,
               App_Workgroup.EnabledMark,
               App_Workgroup.DeleteMark,
               App_Workgroup.DeleteLister,
               App_Workgroup.DeleteListerid,
               App_Workgroup.DeleteDate,
               App_Workgroup.FmAccoid,
               App_Workgroup.ForeAccoid,
               App_Workgroup.BusMachRemAmt,
               App_Workgroup.BusDeliRemAmt,
               App_Workgroup.BusInvoRemAmt,
               App_Workgroup.BusAccoCloseAmt,
               App_Workgroup.BusAccoNowAmt,
               App_Workgroup.BuyOrderRemAmt,
               App_Workgroup.BuyFiniRemAmt,
               App_Workgroup.BuyInvoRemAmt,
               App_Workgroup.BuyAccoCloseAmt,
               App_Workgroup.BuyAccoNowAmt,
               App_Workgroup.City,
               App_Workgroup.County,
               App_Workgroup.Street,
               App_Workgroup.LocalAdd,
               App_Workgroup.Custom1,
               App_Workgroup.Custom2,
               App_Workgroup.Custom3,
               App_Workgroup.Custom4,
               App_Workgroup.Custom5,
               App_Workgroup.Custom6,
               App_Workgroup.Custom7,
               App_Workgroup.Custom8,
               App_Workgroup.Custom9,
               App_Workgroup.Custom10,
               App_Workgroup.Tenantid,
               App_Workgroup.Revision,
               a2.AccoCode AS FmAccoCode,
               a1.AccoCode AS ForeAccoCode,
               a2.AccoName AS FmAccoName,
               a1.AccoName AS ForeAccoName
        FROM App_Workgroup
                 LEFT JOIN Fm_Account AS a1 ON App_Workgroup.ForeAccoid = a1.id
                 LEFT JOIN Fm_Account AS a2 ON App_Workgroup.FmAccoid = a2.id
        where App_Workgroup.id = #{key}
          and App_Workgroup.Tenantid = #{tid}
    </select>
    <sql id="selectAppWorkgroupVo">
        SELECT App_Workgroup.id,
               App_Workgroup.WgGroupid,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.GroupClass,
               App_Workgroup.Linkman,
               App_Workgroup.Telephone,
               App_Workgroup.GroupFax,
               App_Workgroup.GroupAdd,
               App_Workgroup.Remark,
               App_Workgroup.InvalidDate,
               App_Workgroup.GroupType,
               App_Workgroup.CreditDUint,
               App_Workgroup.CreditDQuantity,
               App_Workgroup.CreditCUint,
               App_Workgroup.CreditCQuantity,
               App_Workgroup.RowNum,
               App_Workgroup.CreateBy,
               App_Workgroup.CreateByid,
               App_Workgroup.CreateDate,
               App_Workgroup.Lister,
               App_Workgroup.Listerid,
               App_Workgroup.ModifyDate,
               App_Workgroup.Mobile,
               App_Workgroup.LinkmanS,
               App_Workgroup.TelephoneS,
               App_Workgroup.MobileS,
               App_Workgroup.Country,
               App_Workgroup.Province,
               App_Workgroup.GroupZip,
               App_Workgroup.DeliverAdd,
               App_Workgroup.InvoiceAdd,
               App_Workgroup.Seller,
               App_Workgroup.GroupLabel,
               App_Workgroup.GroupLevel,
               App_Workgroup.GroupState,
               App_Workgroup.Source,
               App_Workgroup.Credit,
               App_Workgroup.PaymentMethod,
               App_Workgroup.CreditCode,
               App_Workgroup.DepositBank,
               App_Workgroup.BankAccount,
               App_Workgroup.EnabledMark,
               App_Workgroup.DeleteMark,
               App_Workgroup.DeleteLister,
               App_Workgroup.DeleteListerid,
               App_Workgroup.DeleteDate,
               App_Workgroup.FmAccoid,
               App_Workgroup.ForeAccoid,
               App_Workgroup.BusMachRemAmt,
               App_Workgroup.BusDeliRemAmt,
               App_Workgroup.BusInvoRemAmt,
               App_Workgroup.BusAccoCloseAmt,
               App_Workgroup.BusAccoNowAmt,
               App_Workgroup.BuyOrderRemAmt,
               App_Workgroup.BuyFiniRemAmt,
               App_Workgroup.BuyInvoRemAmt,
               App_Workgroup.BuyAccoCloseAmt,
               App_Workgroup.BuyAccoNowAmt,
               App_Workgroup.City,
               App_Workgroup.County,
               App_Workgroup.Street,
               App_Workgroup.LocalAdd,
               App_Workgroup.Custom1,
               App_Workgroup.Custom2,
               App_Workgroup.Custom3,
               App_Workgroup.Custom4,
               App_Workgroup.Custom5,
               App_Workgroup.Custom6,
               App_Workgroup.Custom7,
               App_Workgroup.Custom8,
               App_Workgroup.Custom9,
               App_Workgroup.Custom10,
               App_Workgroup.Tenantid,
               App_Workgroup.Revision,
               a2.AccoCode AS FmAccoCode,
               a1.AccoCode AS ForeAccoCode,
               a2.AccoName AS FmAccoName,
               a1.AccoName AS ForeAccoName
        FROM App_Workgroup
                 LEFT JOIN Fm_Account AS a1 ON App_Workgroup.ForeAccoid = a1.id
                 LEFT JOIN Fm_Account AS a2 ON App_Workgroup.FmAccoid = a2.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.AppWorkgroupPojo">
        <include refid="selectAppWorkgroupVo"/>
        where 1 = 1 and App_Workgroup.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and App_Workgroup.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.wggroupid != null ">
            and App_Workgroup.WgGroupid like concat('%', #{SearchPojo.wggroupid}, '%')
        </if>
        <if test="SearchPojo.groupuid != null ">
            and LOWER(App_Workgroup.GroupUid) like concat('%', LOWER(#{SearchPojo.groupuid}), '%')
        </if>
        <if test="SearchPojo.groupname != null ">
            and LOWER(App_Workgroup.GroupName) like concat('%', LOWER(#{SearchPojo.groupname}), '%')
        </if>
        <if test="SearchPojo.abbreviate != null ">
            and LOWER(App_Workgroup.Abbreviate) like concat('%', LOWER(#{SearchPojo.abbreviate}), '%')
        </if>
        <if test="SearchPojo.groupclass != null ">
            and App_Workgroup.GroupClass like concat('%', #{SearchPojo.groupclass}, '%')
        </if>
        <if test="SearchPojo.linkman != null ">
            and App_Workgroup.Linkman like concat('%', #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.telephone != null ">
            and App_Workgroup.Telephone like concat('%', #{SearchPojo.telephone}, '%')
        </if>
        <if test="SearchPojo.groupfax != null ">
            and App_Workgroup.GroupFax like concat('%', #{SearchPojo.groupfax}, '%')
        </if>
        <if test="SearchPojo.groupadd != null ">
            and App_Workgroup.GroupAdd like concat('%', #{SearchPojo.groupadd}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and App_Workgroup.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.grouptype != null ">
            and App_Workgroup.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
        </if>
        <if test="SearchPojo.creditduint != null ">
            and App_Workgroup.CreditDUint like concat('%', #{SearchPojo.creditduint}, '%')
        </if>
        <if test="SearchPojo.creditcuint != null ">
            and App_Workgroup.CreditCUint like concat('%', #{SearchPojo.creditcuint}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and App_Workgroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and App_Workgroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and App_Workgroup.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and App_Workgroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.mobile != null ">
            and App_Workgroup.Mobile like concat('%', #{SearchPojo.mobile}, '%')
        </if>
        <if test="SearchPojo.linkmans != null ">
            and App_Workgroup.LinkmanS like concat('%', #{SearchPojo.linkmans}, '%')
        </if>
        <if test="SearchPojo.telephones != null ">
            and App_Workgroup.TelephoneS like concat('%', #{SearchPojo.telephones}, '%')
        </if>
        <if test="SearchPojo.mobiles != null ">
            and App_Workgroup.MobileS like concat('%', #{SearchPojo.mobiles}, '%')
        </if>
        <if test="SearchPojo.country != null ">
            and App_Workgroup.Country like concat('%', #{SearchPojo.country}, '%')
        </if>
        <if test="SearchPojo.province != null ">
            and App_Workgroup.Province like concat('%', #{SearchPojo.province}, '%')
        </if>
        <if test="SearchPojo.groupzip != null ">
            and App_Workgroup.GroupZip like concat('%', #{SearchPojo.groupzip}, '%')
        </if>
        <if test="SearchPojo.deliveradd != null ">
            and App_Workgroup.DeliverAdd like concat('%', #{SearchPojo.deliveradd}, '%')
        </if>
        <if test="SearchPojo.invoiceadd != null ">
            and App_Workgroup.InvoiceAdd like concat('%', #{SearchPojo.invoiceadd}, '%')
        </if>
        <if test="SearchPojo.seller != null ">
            and App_Workgroup.Seller like concat('%', #{SearchPojo.seller}, '%')
        </if>
        <if test="SearchPojo.grouplabel != null ">
            and App_Workgroup.GroupLabel like concat('%', #{SearchPojo.grouplabel}, '%')
        </if>
        <if test="SearchPojo.grouplevel != null ">
            and App_Workgroup.GroupLevel like concat('%', #{SearchPojo.grouplevel}, '%')
        </if>
        <if test="SearchPojo.groupstate != null ">
            and App_Workgroup.GroupState like concat('%', #{SearchPojo.groupstate}, '%')
        </if>
        <if test="SearchPojo.source != null ">
            and App_Workgroup.Source like concat('%', #{SearchPojo.source}, '%')
        </if>
        <if test="SearchPojo.credit != null ">
            and App_Workgroup.Credit like concat('%', #{SearchPojo.credit}, '%')
        </if>
        <if test="SearchPojo.paymentmethod != null ">
            and App_Workgroup.PaymentMethod like concat('%', #{SearchPojo.paymentmethod}, '%')
        </if>
        <if test="SearchPojo.creditcode != null ">
            and App_Workgroup.CreditCode like concat('%', #{SearchPojo.creditcode}, '%')
        </if>
        <if test="SearchPojo.depositbank != null ">
            and App_Workgroup.DepositBank like concat('%', #{SearchPojo.depositbank}, '%')
        </if>
        <if test="SearchPojo.bankaccount != null ">
            and App_Workgroup.BankAccount like concat('%', #{SearchPojo.bankaccount}, '%')
        </if>
        <if test="SearchPojo.deletelister != null ">
            and App_Workgroup.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null ">
            and App_Workgroup.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
        </if>
        <if test="SearchPojo.city != null ">
            and App_Workgroup.City like concat('%', #{SearchPojo.city}, '%')
        </if>
        <if test="SearchPojo.county != null ">
            and App_Workgroup.County like concat('%', #{SearchPojo.county}, '%')
        </if>
        <if test="SearchPojo.street != null ">
            and App_Workgroup.Street like concat('%', #{SearchPojo.street}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and App_Workgroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and App_Workgroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and App_Workgroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and App_Workgroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and App_Workgroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and App_Workgroup.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and App_Workgroup.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and App_Workgroup.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and App_Workgroup.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and App_Workgroup.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.wggroupid != null ">
                or App_Workgroup.WgGroupid like concat('%', #{SearchPojo.wggroupid}, '%')
            </if>
            <if test="SearchPojo.groupuid != null ">
                or LOWER(App_Workgroup.GroupUid) like concat('%', LOWER(#{SearchPojo.groupuid}), '%')
            </if>
            <if test="SearchPojo.groupname != null ">
                or LOWER(App_Workgroup.GroupName) like concat('%', LOWER(#{SearchPojo.groupname}), '%')
            </if>
            <if test="SearchPojo.abbreviate != null ">
                or LOWER(App_Workgroup.Abbreviate) like concat('%', LOWER(#{SearchPojo.abbreviate}), '%')
            </if>
            <if test="SearchPojo.groupclass != null ">
                or App_Workgroup.GroupClass like concat('%', #{SearchPojo.groupclass}, '%')
            </if>
            <if test="SearchPojo.linkman != null ">
                or App_Workgroup.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.telephone != null ">
                or App_Workgroup.Telephone like concat('%', #{SearchPojo.telephone}, '%')
            </if>
            <if test="SearchPojo.groupfax != null ">
                or App_Workgroup.GroupFax like concat('%', #{SearchPojo.groupfax}, '%')
            </if>
            <if test="SearchPojo.groupadd != null ">
                or App_Workgroup.GroupAdd like concat('%', #{SearchPojo.groupadd}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or App_Workgroup.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.grouptype != null ">
                or App_Workgroup.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
            </if>
            <if test="SearchPojo.creditduint != null ">
                or App_Workgroup.CreditDUint like concat('%', #{SearchPojo.creditduint}, '%')
            </if>
            <if test="SearchPojo.creditcuint != null ">
                or App_Workgroup.CreditCUint like concat('%', #{SearchPojo.creditcuint}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or App_Workgroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or App_Workgroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or App_Workgroup.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or App_Workgroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.mobile != null ">
                or App_Workgroup.Mobile like concat('%', #{SearchPojo.mobile}, '%')
            </if>
            <if test="SearchPojo.linkmans != null ">
                or App_Workgroup.LinkmanS like concat('%', #{SearchPojo.linkmans}, '%')
            </if>
            <if test="SearchPojo.telephones != null ">
                or App_Workgroup.TelephoneS like concat('%', #{SearchPojo.telephones}, '%')
            </if>
            <if test="SearchPojo.mobiles != null ">
                or App_Workgroup.MobileS like concat('%', #{SearchPojo.mobiles}, '%')
            </if>
            <if test="SearchPojo.country != null ">
                or App_Workgroup.Country like concat('%', #{SearchPojo.country}, '%')
            </if>
            <if test="SearchPojo.province != null ">
                or App_Workgroup.Province like concat('%', #{SearchPojo.province}, '%')
            </if>
            <if test="SearchPojo.groupzip != null ">
                or App_Workgroup.GroupZip like concat('%', #{SearchPojo.groupzip}, '%')
            </if>
            <if test="SearchPojo.deliveradd != null ">
                or App_Workgroup.DeliverAdd like concat('%', #{SearchPojo.deliveradd}, '%')
            </if>
            <if test="SearchPojo.invoiceadd != null ">
                or App_Workgroup.InvoiceAdd like concat('%', #{SearchPojo.invoiceadd}, '%')
            </if>
            <if test="SearchPojo.seller != null ">
                or App_Workgroup.Seller like concat('%', #{SearchPojo.seller}, '%')
            </if>
            <if test="SearchPojo.grouplabel != null ">
                or App_Workgroup.GroupLabel like concat('%', #{SearchPojo.grouplabel}, '%')
            </if>
            <if test="SearchPojo.grouplevel != null ">
                or App_Workgroup.GroupLevel like concat('%', #{SearchPojo.grouplevel}, '%')
            </if>
            <if test="SearchPojo.groupstate != null ">
                or App_Workgroup.GroupState like concat('%', #{SearchPojo.groupstate}, '%')
            </if>
            <if test="SearchPojo.source != null ">
                or App_Workgroup.Source like concat('%', #{SearchPojo.source}, '%')
            </if>
            <if test="SearchPojo.credit != null ">
                or App_Workgroup.Credit like concat('%', #{SearchPojo.credit}, '%')
            </if>
            <if test="SearchPojo.paymentmethod != null ">
                or App_Workgroup.PaymentMethod like concat('%', #{SearchPojo.paymentmethod}, '%')
            </if>
            <if test="SearchPojo.creditcode != null ">
                or App_Workgroup.CreditCode like concat('%', #{SearchPojo.creditcode}, '%')
            </if>
            <if test="SearchPojo.depositbank != null ">
                or App_Workgroup.DepositBank like concat('%', #{SearchPojo.depositbank}, '%')
            </if>
            <if test="SearchPojo.bankaccount != null ">
                or App_Workgroup.BankAccount like concat('%', #{SearchPojo.bankaccount}, '%')
            </if>
            <if test="SearchPojo.deletelister != null ">
                or App_Workgroup.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
            <if test="SearchPojo.deletelisterid != null ">
                or App_Workgroup.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
            </if>
            <if test="SearchPojo.city != null ">
                or App_Workgroup.City like concat('%', #{SearchPojo.city}, '%')
            </if>
            <if test="SearchPojo.county != null ">
                or App_Workgroup.County like concat('%', #{SearchPojo.county}, '%')
            </if>
            <if test="SearchPojo.street != null ">
                or App_Workgroup.Street like concat('%', #{SearchPojo.street}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or App_Workgroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or App_Workgroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or App_Workgroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or App_Workgroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or App_Workgroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or App_Workgroup.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or App_Workgroup.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or App_Workgroup.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or App_Workgroup.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or App_Workgroup.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into App_Workgroup(id, WgGroupid, GroupUid, GroupName, Abbreviate, GroupClass, Linkman, Telephone,
                                  GroupFax, GroupAdd, Remark, InvalidDate, GroupType, CreditDUint, CreditDQuantity,
                                  CreditCUint, CreditCQuantity, RowNum, CreateBy, CreateByid, CreateDate, Lister,
                                  Listerid, ModifyDate, Mobile, LinkmanS, TelephoneS, MobileS, Country, Province,
                                  GroupZip, DeliverAdd, InvoiceAdd, Seller, GroupLabel, GroupLevel, GroupState, Source,
                                  Credit, PaymentMethod, CreditCode, DepositBank, BankAccount, EnabledMark, DeleteMark,
                                  DeleteLister, DeleteListerid, DeleteDate, FmAccoid, ForeAccoid, BusMachRemAmt,
                                  BusDeliRemAmt, BusInvoRemAmt, BusAccoCloseAmt, BusAccoNowAmt, BuyOrderRemAmt,
                                  BuyFiniRemAmt, BuyInvoRemAmt, BuyAccoCloseAmt, BuyAccoNowAmt, City, County, Street,LocalAdd,
                                  Custom1, Custom2,
                                  Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                  Revision)
        values (#{id}, #{wggroupid}, #{groupuid}, #{groupname}, #{abbreviate}, #{groupclass}, #{linkman}, #{telephone},
                #{groupfax}, #{groupadd}, #{remark}, #{invaliddate}, #{grouptype}, #{creditduint}, #{creditdquantity},
                #{creditcuint}, #{creditcquantity}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister},
                #{listerid}, #{modifydate}, #{mobile}, #{linkmans}, #{telephones}, #{mobiles}, #{country}, #{province},
                #{groupzip}, #{deliveradd}, #{invoiceadd}, #{seller}, #{grouplabel}, #{grouplevel}, #{groupstate},
                #{source}, #{credit}, #{paymentmethod}, #{creditcode}, #{depositbank}, #{bankaccount}, #{enabledmark},
                #{deletemark}, #{deletelister}, #{deletelisterid}, #{deletedate}, #{fmaccoid}, #{foreaccoid},
                #{busmachremamt}, #{busdeliremamt}, #{businvoremamt}, #{busaccocloseamt}, #{busacconowamt},
                #{buyorderremamt}, #{buyfiniremamt}, #{buyinvoremamt}, #{buyaccocloseamt}, #{buyacconowamt}, #{city},
                #{county}, #{street},#{localadd}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update App_Workgroup
        <set>
            <if test="wggroupid != null ">
                WgGroupid =#{wggroupid},
            </if>
            <if test="groupuid != null ">
                GroupUid =#{groupuid},
            </if>
            <if test="groupname != null ">
                GroupName =#{groupname},
            </if>
            <if test="abbreviate != null ">
                Abbreviate =#{abbreviate},
            </if>
            <if test="groupclass != null ">
                GroupClass =#{groupclass},
            </if>
            <if test="linkman != null ">
                Linkman =#{linkman},
            </if>
            <if test="telephone != null ">
                Telephone =#{telephone},
            </if>
            <if test="groupfax != null ">
                GroupFax =#{groupfax},
            </if>
            <if test="groupadd != null ">
                GroupAdd =#{groupadd},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="invaliddate != null">
                InvalidDate =#{invaliddate},
            </if>
            <if test="grouptype != null ">
                GroupType =#{grouptype},
            </if>
            <if test="creditduint != null ">
                CreditDUint =#{creditduint},
            </if>
            <if test="creditdquantity != null">
                CreditDQuantity =#{creditdquantity},
            </if>
            <if test="creditcuint != null ">
                CreditCUint =#{creditcuint},
            </if>
            <if test="creditcquantity != null">
                CreditCQuantity =#{creditcquantity},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="mobile != null ">
                Mobile =#{mobile},
            </if>
            <if test="linkmans != null ">
                LinkmanS =#{linkmans},
            </if>
            <if test="telephones != null ">
                TelephoneS =#{telephones},
            </if>
            <if test="mobiles != null ">
                MobileS =#{mobiles},
            </if>
            <if test="country != null ">
                Country =#{country},
            </if>
            <if test="province != null ">
                Province =#{province},
            </if>
            <if test="groupzip != null ">
                GroupZip =#{groupzip},
            </if>
            <if test="deliveradd != null ">
                DeliverAdd =#{deliveradd},
            </if>
            <if test="invoiceadd != null ">
                InvoiceAdd =#{invoiceadd},
            </if>
            <if test="seller != null ">
                Seller =#{seller},
            </if>
            <if test="grouplabel != null ">
                GroupLabel =#{grouplabel},
            </if>
            <if test="grouplevel != null ">
                GroupLevel =#{grouplevel},
            </if>
            <if test="groupstate != null ">
                GroupState =#{groupstate},
            </if>
            <if test="source != null ">
                Source =#{source},
            </if>
            <if test="credit != null ">
                Credit =#{credit},
            </if>
            <if test="paymentmethod != null ">
                PaymentMethod =#{paymentmethod},
            </if>
            <if test="creditcode != null ">
                CreditCode =#{creditcode},
            </if>
            <if test="depositbank != null ">
                DepositBank =#{depositbank},
            </if>
            <if test="bankaccount != null ">
                BankAccount =#{bankaccount},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletelisterid != null ">
                DeleteListerid =#{deletelisterid},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="fmaccoid != null ">
                FmAccoid =#{fmaccoid},
            </if>
            <if test="foreaccoid != null ">
                ForeAccoid =#{foreaccoid},
            </if>
            <if test="city != null ">
                City =#{city},
            </if>
            <if test="county != null ">
                County =#{county},
            </if>
            <if test="street != null ">
                Street =#{street},
            </if>
            <if test="localadd != null ">
                LocalAdd =#{localadd},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>


    <!--通过主键删除-->
    <delete id="delete">
        delete
        from App_Workgroup
        where id = #{key}
          and Tenantid = #{tid}
    </delete>


    <!--  查询往来单位是否被引用  -->
    <select id="getCiteBillName" resultType="string">
        (SELECT '销售订单' as billname From Bus_Machining where Groupid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '送货单' as billname From Bus_Deliery where Groupid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '采购计划' as billname From Buy_PlanItem where Groupid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '采购合同' as billname From Buy_Order where Groupid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '采购收货' as billname From Buy_Finishing where Groupid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '出入库单' as billname From Mat_Access where Groupid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '其他收入' as billname From Fm_Income where Groupid= #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '费用开支' as billname From Fm_Cost where Groupid = #{key} and Tenantid = #{tid} LIMIT 1)
    </select>

    <!-- 按编码查询实例-->
    <select id="getEntityByUid" parameterType="inks.service.sa.som.domain.pojo.AppWorkgroupPojo"
            resultType="inks.service.sa.som.domain.pojo.AppWorkgroupPojo">
        <include refid="selectAppWorkgroupVo"/>
        where App_Workgroup.id&lt;&gt;#{id} and App_Workgroup.GroupUid= #{groupuid} and
        App_Workgroup.GroupType=#{grouptype} and App_Workgroup.Tenantid =#{tenantid} LIMIT 1
    </select>

    <!-- 按编码查询实例-->
    <select id="getEntityByName" parameterType="inks.service.sa.som.domain.pojo.AppWorkgroupPojo"
            resultType="inks.service.sa.som.domain.pojo.AppWorkgroupPojo">
        <include refid="selectAppWorkgroupVo"/>
        where App_Workgroup.id&lt;&gt;#{id} and App_Workgroup.Groupname= #{groupname} and
        App_Workgroup.GroupType=#{grouptype} and App_Workgroup.Tenantid =#{tenantid} LIMIT 1
    </select>

    <!-- 按编码查询实例-->
    <select id="getMaxCode" parameterType="inks.service.sa.som.domain.pojo.AppWorkgroupPojo"
            resultType="String">
        select Groupuid
        From App_Workgroup
        where App_Workgroup.GroupType = #{grouptype}
          and App_Workgroup.Tenantid = #{tenantid}
        Order By App_Workgroup.Groupuid DESC
        LIMIT 1
    </select>

    <!--查询指定行数据-->
    <select id="getPageListBySale" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.AppWorkgroupPojo">
        SELECT * FROM (SELECT
        App_Workgroup.id,
        App_Workgroup.WgGroupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate,
        App_Workgroup.Seller,
        COALESCE((SELECT
        Bus_Account.BillCloseAmount
        FROM Bus_Account
        WHERE Bus_Account.Groupid=App_Workgroup.id
        Order By RowNum Desc LIMIT 1),0) as AccountAmount,
        COALESCE((SELECT
        sum(CASE
        WHEN Bus_Deliery.BillType IN ('发出商品','其他发货','返工补发') THEN Bus_Deliery.BillTaxAmount
        ELSE 0 - Bus_Deliery.BillTaxAmount END)
        FROM Bus_Deliery where Bus_Deliery.Groupid=App_Workgroup.id
        and Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        ),0)-
        COALESCE((SELECT Sum(Bus_Deduction.BillTaxAmount) FROM Bus_Deduction
        where Bus_Deduction.Groupid=App_Workgroup.id
        and Bus_Deduction.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0)
        AS FreeInAmount,
        COALESCE((SELECT
        Sum(Bus_Deposit.BillAmount)
        FROM Bus_Deposit WHERE Bus_Deposit.Groupid=App_Workgroup.id
        and Bus_Deposit.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0)+
        COALESCE((SELECT
        Sum(Bus_Receipt.BillAmount)
        FROM Bus_Receipt WHERE Bus_Receipt.Groupid=App_Workgroup.id
        and Bus_Receipt.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0) as FreeOutAmount,
        COALESCE((SELECT
        Bus_Account.BillCloseAmount
        FROM Bus_Account
        WHERE Bus_Account.Groupid=App_Workgroup.id
        Order By RowNum Desc LIMIT 1),0) +
        COALESCE((SELECT
        sum(CASE
        WHEN Bus_Deliery.BillType IN ('发出商品','其他发货','返工补发') THEN Bus_Deliery.BillTaxAmount
        ELSE 0 - Bus_Deliery.BillTaxAmount END)
        FROM Bus_Deliery where Bus_Deliery.Groupid=App_Workgroup.id
        and Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        ),0)-
        COALESCE((SELECT Sum(Bus_Deduction.BillTaxAmount) FROM Bus_Deduction
        where Bus_Deduction.Groupid=App_Workgroup.id
        and Bus_Deduction.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0)
        -
        COALESCE((SELECT
        Sum(Bus_Deposit.BillAmount)
        FROM Bus_Deposit WHERE Bus_Deposit.Groupid=App_Workgroup.id
        and Bus_Deposit.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0)-
        COALESCE((SELECT
        Sum(Bus_Receipt.BillAmount)
        FROM Bus_Receipt WHERE Bus_Receipt.Groupid=App_Workgroup.id
        and Bus_Receipt.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0) AS TotalAmount
        FROM
        App_Workgroup
        where App_Workgroup.EnabledMark = 1
        and App_Workgroup.DeleteMark = 0
        and GroupType='客户'
        and App_Workgroup.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        ) t
        <if test="SearchPojo != null and SearchPojo.online == 1">
            where TotalAmount!=0
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>

    <!--查询指定行数据-->
    <select id="getPageListByRece" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.AppWorkgroupPojo">
        SELECT
        App_Workgroup.id,
        App_Workgroup.WgGroupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate,
        App_Workgroup.Seller,
        COALESCE((SELECT
        Sum(Bus_Invoice.TaxAmount-Bus_Invoice.Receipted)
        FROM
        Bus_Invoice WHERE Bus_Invoice.Groupid=App_Workgroup.id),0) as InvoRemAmount,
        COALESCE((SELECT
        Sum(Bus_DelieryItem.TaxAmount/Bus_DelieryItem.Quantity*(Bus_DelieryItem.Quantity-Bus_DelieryItem.InvoQty))
        FROM
        Bus_Deliery
        RIGHT JOIN Bus_DelieryItem ON Bus_DelieryItem.Pid = Bus_Deliery.id where Bus_Deliery.Groupid=App_Workgroup.id
        and Bus_DelieryItem.Quantity!=0 ),0)- COALESCE((SELECT
        Sum(Bus_DeductionItem.TaxAmount/Bus_DeductionItem.Quantity*(Bus_DeductionItem.Quantity-Bus_DeductionItem.InvoQty))
        FROM
        Bus_Deduction
        RIGHT JOIN Bus_DeductionItem ON Bus_DeductionItem.Pid = Bus_Deduction.id where
        Bus_Deduction.Groupid=App_Workgroup.id
        and Bus_DeductionItem.Quantity!=0),0) as SaleFreeAmount,
        COALESCE((SELECT
        Sum(Bus_Deposit.BillAmount-Bus_Deposit.OutAmount)
        FROM
        Bus_Deposit WHERE Bus_Deposit.Groupid=App_Workgroup.id),0) as DepoRemAmount
        FROM
        App_Workgroup
        where App_Workgroup.EnabledMark = 1
        and App_Workgroup.DeleteMark = 0
        and GroupType='客户'
        and App_Workgroup.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>

    <!--    刷新销售订单结余额-->
    <update id="updateWorkgroupBusMachRemAmt">
        update App_Workgroup
        SET BusMachRemAmt =(SELECT COALESCE(sum(Bus_Machining.BillTaxAmount - Bus_Machining.AdvaAmount), 0) as Qty
                            FROM Bus_Machining
                            where Bus_Machining.Groupid = #{key}
                              and Bus_Machining.ItemCount
                                &gt; Bus_Machining.FinishCount
                              and Bus_Machining.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <!--    刷新销售发货结余额-->
    <update id="updateWorkgroupBusDeliRemAmt">
        update App_Workgroup
        SET BusDeliRemAmt =(SELECT COALESCE(sum(Bus_DelieryItem.TaxAmount -
                                                (Bus_DelieryItem.Quantity - Bus_DelieryItem.InvoQty) *
                                                Bus_DelieryItem.TaxPrice), 0)
                            FROM Bus_Deliery
                                     RIGHT JOIN Bus_DelieryItem ON Bus_DelieryItem.Pid = Bus_Deliery.id
                            where Bus_Deliery.Groupid = #{key}
                              and Bus_DelieryItem.Quantity
                                &gt; Bus_DelieryItem.InvoQty
                              and Bus_DelieryItem.InvoClosed = 0
                              and Bus_DelieryItem.DisannulMark = 0
                              and Bus_DelieryItem.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新销售发票结余额-->
    <update id="updateWorkgroupBusInvoRemAmt">
        update App_Workgroup
        SET BusInvoRemAmt =(select COALESCE(sum(Bus_InvoiceItem.TaxAmount -
                                                (Bus_InvoiceItem.BillQty - Bus_InvoiceItem.Quantity) *
                                                Bus_InvoiceItem.TaxPrice), 0)
                            from Bus_Invoice
                                     right join Bus_InvoiceItem on Bus_InvoiceItem.Pid = Bus_Invoice.id
                            where Bus_Invoice.Groupid = #{key}
                              and Bus_InvoiceItem.BillQty
                                &gt; Bus_InvoiceItem.Quantity
                              and Bus_Invoice.DisannulMark = 0
                              and Bus_Invoice.Closed = 0
                              and Bus_Invoice.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>



    <!--    刷新销售结转期末额-->
    <update id="updateWorkgroupBusAccoCloseAmt">
        update App_Workgroup
        SET BusAccoCloseAmt =0
        where id = #{key}
    </update>

    <!--    刷新销售结转本期额-->
    <update id="updateWorkgroupBusAccoNowAmt">
        update App_Workgroup
        SET BusAccoNowAmt =0
        where id = #{key}
    </update>

    <!--    刷新采购订单结余额-->
    <update id="updateWorkgroupBuyOrderRemAmt">
        update App_Workgroup
        SET BuyOrderRemAmt =(SELECT COALESCE(sum(Buy_Order.BillTaxAmount -Buy_Order.Prepayments), 0)
                             FROM Buy_Order
                             where Buy_Order.Groupid = #{key}
                               and Buy_Order.ItemCount
                                 &gt; Buy_Order.FinishCount
                               and Buy_Order.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新采购收货结余额 -->
    <update id="updateWorkgroupBuyFiniRemAmt">
        update App_Workgroup
        SET BuyFiniRemAmt =(SELECT COALESCE(sum(Buy_FinishingItem.TaxAmount -
                                                (Buy_FinishingItem.Quantity - Buy_FinishingItem.InvoQty) *
                                                Buy_FinishingItem.TaxPrice), 0)
                            FROM Buy_Finishing
                                     RIGHT JOIN Buy_FinishingItem ON Buy_FinishingItem.Pid = Buy_Finishing.id
                            where Buy_Finishing.Groupid = #{key}
                              and Buy_FinishingItem.Quantity
                                &gt; Buy_FinishingItem.InvoQty
                              and Buy_FinishingItem.InvoClosed = 0
                              and Buy_FinishingItem.DisannulMark = 0
                              and Buy_FinishingItem.Tenantid = #{tid})
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新采购发票结余额-->
    <update id="updateWorkgroupBuyInvoRemAmt">
        update App_Workgroup
        SET BuyInvoRemAmt =(select COALESCE(sum(Buy_InvoiceItem.TaxAmount -
                                                (Buy_InvoiceItem.BillQty - Buy_InvoiceItem.Quantity) *
                                                Buy_InvoiceItem.TaxPrice), 0)
                            from Buy_Invoice
                                     right join Buy_InvoiceItem on Buy_InvoiceItem.Pid = Buy_Invoice.id
                            where Buy_Invoice.Groupid = #{key}
                              and Buy_InvoiceItem.BillQty
                                &gt; Buy_InvoiceItem.Quantity
                              and Buy_Invoice.DisannulMark = 0
                              and Buy_Invoice.Closed = 0
                              and Buy_Invoice.Tenantid = #{tid})
        where id = #{key}
            and Tenantid = #{tid}
    </update>

    <!--    刷新采购结转期末额-->
    <update id="updateWorkgroupBuyAccoCloseAmt">
        update App_Workgroup
        SET BuyAccoCloseAmt =0
        where id = #{key}
    </update>

    <!--    刷新采购结转本期额-->
    <update id="updateWorkgroupBuyAccoNowAmt">
        update App_Workgroup
        SET BuyAccoNowAmt =0
        where id = #{key}
    </update>

    <select id="getProvOrCity" resultType="java.lang.String">
        select distinct (case when #{province} is null or #{province} = '' then Province
                else City end) as name
        FROM App_Workgroup
        where Tenantid = #{tid}
        and Province != ''
        <if test="province != null and province != ''">
            and Province = #{province}
        </if>
    </select>

    <delete id="deleteBusMachiningByGroupId">
        DELETE t1, t2
        FROM Bus_Machining t1
        LEFT JOIN Bus_MachiningItem t2 ON t1.id = t2.Pid
        WHERE t1.Groupid = #{groupid} AND t1.Tenantid=#{tid}
    </delete>

    <delete id="deleteBusDelieryByGroupId">
        DELETE t1, t2
        FROM Bus_Deliery t1
        LEFT JOIN Bus_DelieryItem t2 ON t1.id = t2.Pid
        WHERE t1.Groupid = #{groupid} AND t1.Tenantid=#{tid}
    </delete>

    <delete id="deleteBuyPlanItemByGroupId">
        delete from Buy_PlanItem where Groupid = #{groupid} and Tenantid = #{tid}
    </delete>

    <delete id="deleteBuyOrderByGroupId">
        DELETE t1, t2
        FROM Buy_Order t1
        LEFT JOIN Buy_OrderItem t2 ON t1.id = t2.Pid
        WHERE t1.Groupid = #{groupid} AND t1.Tenantid=#{tid}
    </delete>

    <delete id="deleteBuyFinishingByGroupId">
        DELETE t1, t2
        FROM Buy_Finishing t1
        LEFT JOIN Buy_FinishingItem t2 ON t1.id = t2.Pid
        WHERE t1.Groupid = #{groupid} AND t1.Tenantid=#{tid}
    </delete>

    <delete id="deleteWkWorksheetByGroupId">
        DELETE t1, t2
        FROM Wk_Worksheet t1
        LEFT JOIN Wk_WorksheetItem t2 ON t1.id = t2.Pid
        WHERE t1.Groupid = #{groupid} AND t1.Tenantid=#{tid}
    </delete>

    <delete id="deleteMatAccessByGroupId">
        DELETE t1, t2
        FROM Mat_Access t1
        LEFT JOIN Mat_AccessItem t2 ON t1.id = t2.Pid
        WHERE t1.Groupid = #{groupid} AND t1.Tenantid=#{tid}
    </delete>

    <delete id="deleteWkSubcontractByGroupId">
        DELETE t1, t2
        FROM Wk_Subcontract t1
        LEFT JOIN Wk_SubcontractItem t2 ON t1.id = t2.Pid
        WHERE t1.Groupid = #{groupid} AND t1.Tenantid=#{tid}
    </delete>

    <delete id="deleteBusOrderCostByGroupId">
        DELETE t1, t2
        FROM Bus_OrderCost t1
        LEFT JOIN Bus_OrderCostItem t2 ON t1.id = t2.Pid
        WHERE t1.Groupid = #{groupid} AND t1.Tenantid=#{tid}
    </delete>

    <select id="getGroupIdsBySeller" resultType="java.lang.String">
        select distinct id
        from App_Workgroup
        where Seller = #{seller}
          and Tenantid = #{tid}
    </select>

    <select id="getCouponStatsByGroup" resultType="java.util.Map">
        SELECT App_Workgroup.id                                     AS groupid,
               App_Workgroup.groupname,
               COUNT(Bus_Coupon.id)                                 AS couponsqty,
               SUM(Bus_Coupon.CouponAmount)                         AS totalcouponamount,
               SUM(Bus_Coupon.ActiveAmount)                         AS totalactiveamount,
               SUM(Bus_Coupon.UsedAmount)                           AS totalusedamount,
               SUM(Bus_Coupon.ActiveAmount - Bus_Coupon.UsedAmount) AS totalbalanceamount,
               SUM(Bus_Deposit.BillAmount)                          AS totaldepositamount
        FROM App_Workgroup
                 LEFT JOIN Bus_Coupon ON App_Workgroup.id = Bus_Coupon.Groupid
                 LEFT JOIN Bus_Deposit ON App_Workgroup.id = Bus_Deposit.Groupid and Bus_Deposit.BillType = '其他预收'
        WHERE App_Workgroup.Tenantid = #{tid}
          and App_Workgroup.GroupType = '客户'
        GROUP BY App_Workgroup.id
    </select>

</mapper>

