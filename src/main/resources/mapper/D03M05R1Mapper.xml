<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.D03M05R1Mapper">
    <select id="getItemCountByMonth" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT
            count( Bus_MachiningItem.id )
            VALUE
	,
	'本月订单' NAME
        FROM
            Bus_Machining
            LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
            RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE
            DATE_FORMAT( Bus_Machining.BillDate, '%Y-%m' ) = DATE_FORMAT( CURDATE( ), '%Y-%m' )
          AND Bus_Machining.Tenantid = #{Tenantid}	UNION ALL
        SELECT
            sum( Bus_MachiningItem.quantity )
            VALUE
	,
	'本月数量' NAME
        FROM
            Bus_Machining
            LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
            RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE
            DATE_FORMAT( Bus_Machining.BillDate, '%Y-%m' ) = DATE_FORMAT( CURDATE( ), '%Y-%m' )
          AND Bus_Machining.Tenantid = #{Tenantid}	UNION ALL
        SELECT
            count( Bus_MachiningItem.id )
            VALUE
	,
	'本月完成' NAME
        FROM
            Bus_Machining
            LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
            RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE
            Bus_MachiningItem.OutQuantity >= Bus_MachiningItem.Quantity
          AND DATE_FORMAT( Bus_Machining.BillDate, '%Y-%m' ) = DATE_FORMAT( CURDATE( ), '%Y-%m' )
          AND Bus_Machining.Tenantid = #{Tenantid}	UNION ALL
        SELECT
            sum( Bus_MachiningItem.TaxAmount )
            VALUE
	,
	'本月金额' NAME
        FROM
            Bus_Machining
            LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
            RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE
            DATE_FORMAT( Bus_Machining.BillDate, '%Y-%m' ) = DATE_FORMAT( CURDATE( ), '%Y-%m' )
          AND Bus_Machining.Tenantid = #{Tenantid}	UNION ALL
        SELECT
            count( Bus_MachiningItem.id )
            VALUE
	,
	'逾期交货' NAME
        FROM
            Bus_Machining
            LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
            RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE
            1 = 1
          AND Bus_MachiningItem.OutQuantity >= Bus_MachiningItem.Quantity
          AND DATE_FORMAT( Bus_Machining.BillDate, '%Y-%m' ) = DATE_FORMAT( CURDATE( ), '%Y-%m' )
          AND Bus_Machining.Tenantid = #{Tenantid}
          AND DATE_FORMAT( Bus_MachiningItem.Itemplandate, '%Y-%m' ) > DATE_FORMAT( CURDATE( ), '%Y-%m' )
    </select>
    <select id="sumChartMaxByGroup" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT
            Buy_Invoice.Groupid,
            App_Workgroup.Abbreviate AS NAME,
            sum( Buy_Invoice.amount ) AS
        VALUE
        FROM
            Buy_Invoice
            LEFT OUTER JOIN App_Workgroup ON Buy_Invoice.Groupid = App_Workgroup.id
        WHERE 1=1
        <if test="DateRange != null">
            AND (${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate})
        </if>
          AND Buy_Invoice.Tenantid = #{Tenantid}
        GROUP BY
            Buy_Invoice.Groupid,
            App_Workgroup.Abbreviate
        ORDER BY
            sum( Buy_Invoice.amount ) DESC
    </select>
</mapper>
