<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.FmPayapplyMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.FmPayapplyPojo">
        SELECT Fm_PayApply.id,
               Fm_PayApply.RefNo,
               Fm_PayApply.BillType,
               Fm_PayApply.BillDate,
               Fm_PayApply.BillTitle,
               Fm_PayApply.PayGroupid,
               Fm_PayApply.ApplyGroupid,
               Fm_PayApply.PayAmount,
               Fm_PayApply.Applied,
               Fm_PayApply.Operator,
               Fm_PayApply.Summary,
               Fm_PayApply.CreateBy,
               Fm_PayApply.CreateByid,
               Fm_PayApply.CreateDate,
               Fm_PayApply.Lister,
               Fm_PayApply.Listerid,
               Fm_PayApply.ModifyDate,
               Fm_PayApply.Custom1,
               Fm_PayApply.Custom2,
               Fm_PayApply.Custom3,
               Fm_PayApply.Custom4,
               Fm_PayApply.Custom5,
               Fm_PayApply.Custom6,
               Fm_PayApply.Custom7,
               Fm_PayApply.Custom8,
               Fm_PayApply.Custom9,
               Fm_PayApply.Custom10,
               Fm_PayApply.Tenantid,
               Fm_PayApply.TenantName,
               Fm_PayApply.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Fm_PayApply
                 LEFT JOIN App_Workgroup ON Fm_PayApply.PayGroupid = App_Workgroup.id
        where Fm_PayApply.id = #{key}
          and Fm_PayApply.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Fm_PayApply.id,
               Fm_PayApply.RefNo,
               Fm_PayApply.BillType,
               Fm_PayApply.BillDate,
               Fm_PayApply.BillTitle,
               Fm_PayApply.PayGroupid,
               Fm_PayApply.ApplyGroupid,
               Fm_PayApply.PayAmount,
               Fm_PayApply.Applied,
               Fm_PayApply.Operator,
               Fm_PayApply.Summary,
               Fm_PayApply.CreateBy,
               Fm_PayApply.CreateByid,
               Fm_PayApply.CreateDate,
               Fm_PayApply.Lister,
               Fm_PayApply.Listerid,
               Fm_PayApply.ModifyDate,
               Fm_PayApply.Custom1,
               Fm_PayApply.Custom2,
               Fm_PayApply.Custom3,
               Fm_PayApply.Custom4,
               Fm_PayApply.Custom5,
               Fm_PayApply.Custom6,
               Fm_PayApply.Custom7,
               Fm_PayApply.Custom8,
               Fm_PayApply.Custom9,
               Fm_PayApply.Custom10,
               Fm_PayApply.Tenantid,
               Fm_PayApply.TenantName,
               Fm_PayApply.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Fm_PayApply
                 LEFT JOIN App_Workgroup ON Fm_PayApply.PayGroupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT Fm_PayApply.id,
               Fm_PayApply.RefNo,
               Fm_PayApply.BillType,
               Fm_PayApply.BillDate,
               Fm_PayApply.BillTitle,
               Fm_PayApply.PayGroupid,
               Fm_PayApply.ApplyGroupid,
               Fm_PayApply.PayAmount,
               Fm_PayApply.Applied,
               Fm_PayApply.Operator,
               Fm_PayApply.Summary,
               Fm_PayApply.CreateBy,
               Fm_PayApply.CreateByid,
               Fm_PayApply.CreateDate,
               Fm_PayApply.Lister,
               Fm_PayApply.Listerid,
               Fm_PayApply.ModifyDate,
               Fm_PayApply.Custom1,
               Fm_PayApply.Custom2,
               Fm_PayApply.Custom3,
               Fm_PayApply.Custom4,
               Fm_PayApply.Custom5,
               Fm_PayApply.Custom6,
               Fm_PayApply.Custom7,
               Fm_PayApply.Custom8,
               Fm_PayApply.Custom9,
               Fm_PayApply.Custom10,
               Fm_PayApply.Tenantid,
               Fm_PayApply.TenantName,
               Fm_PayApply.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Fm_PayApply
                 LEFT JOIN App_Workgroup ON Fm_PayApply.PayGroupid = App_Workgroup.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.FmPayapplyitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Fm_PayApply.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Fm_PayApply.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Fm_PayApply.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Fm_PayApply.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Fm_PayApply.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.paygroupid != null ">
            and Fm_PayApply.paygroupid like concat('%', #{SearchPojo.paygroupid}, '%')
        </if>
        <if test="SearchPojo.applygroupid != null ">
            and Fm_PayApply.applygroupid like concat('%', #{SearchPojo.applygroupid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Fm_PayApply.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Fm_PayApply.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Fm_PayApply.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Fm_PayApply.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Fm_PayApply.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Fm_PayApply.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Fm_PayApply.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Fm_PayApply.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Fm_PayApply.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Fm_PayApply.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Fm_PayApply.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Fm_PayApply.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Fm_PayApply.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Fm_PayApply.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Fm_PayApply.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Fm_PayApply.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Fm_PayApply.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Fm_PayApply.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Fm_PayApply.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Fm_PayApply.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.paygroupid != null ">
                or Fm_PayApply.PayGroupid like concat('%', #{SearchPojo.paygroupid}, '%')
            </if>
            <if test="SearchPojo.applygroupid != null ">
                or Fm_PayApply.ApplyGroupid like concat('%', #{SearchPojo.applygroupid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Fm_PayApply.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Fm_PayApply.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Fm_PayApply.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Fm_PayApply.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Fm_PayApply.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Fm_PayApply.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Fm_PayApply.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Fm_PayApply.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Fm_PayApply.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Fm_PayApply.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Fm_PayApply.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Fm_PayApply.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Fm_PayApply.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Fm_PayApply.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Fm_PayApply.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Fm_PayApply.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Fm_PayApply.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.FmPayapplyPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Fm_PayApply.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Fm_PayApply.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Fm_PayApply.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Fm_PayApply.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Fm_PayApply.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.paygroupid != null ">
            and Fm_PayApply.PayGroupid like concat('%', #{SearchPojo.paygroupid}, '%')
        </if>
        <if test="SearchPojo.applygroupid != null ">
            and Fm_PayApply.ApplyGroupid like concat('%', #{SearchPojo.applygroupid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Fm_PayApply.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Fm_PayApply.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Fm_PayApply.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Fm_PayApply.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Fm_PayApply.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Fm_PayApply.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Fm_PayApply.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Fm_PayApply.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Fm_PayApply.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Fm_PayApply.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Fm_PayApply.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Fm_PayApply.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Fm_PayApply.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Fm_PayApply.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Fm_PayApply.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Fm_PayApply.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Fm_PayApply.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Fm_PayApply.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Fm_PayApply.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Fm_PayApply.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.paygroupid != null ">
                or Fm_PayApply.PayGroupid like concat('%', #{SearchPojo.paygroupid}, '%')
            </if>
            <if test="SearchPojo.applygroupid != null ">
                or Fm_PayApply.ApplyGroupid like concat('%', #{SearchPojo.applygroupid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Fm_PayApply.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Fm_PayApply.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Fm_PayApply.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Fm_PayApply.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Fm_PayApply.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Fm_PayApply.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Fm_PayApply.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Fm_PayApply.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Fm_PayApply.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Fm_PayApply.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Fm_PayApply.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Fm_PayApply.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Fm_PayApply.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Fm_PayApply.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Fm_PayApply.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Fm_PayApply.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Fm_PayApply.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Fm_PayApply(id, RefNo, BillType, BillDate, BillTitle, PayGroupid, ApplyGroupid, PayAmount, Applied,
                                Operator, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate,
                                Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9,
                                Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{paygroupid}, #{applygroupid}, #{payamount},
                #{applied}, #{operator}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Fm_PayApply
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="paygroupid != null ">
                PayGroupid =#{paygroupid},
            </if>
            <if test="applygroupid != null ">
                ApplyGroupid =#{applygroupid},
            </if>
            <if test="payamount != null">
                PayAmount =#{payamount},
            </if>
            <if test="applied != null">
                Applied =#{applied},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Fm_PayApply
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.FmPayapplyPojo">
        select
        id
        from Fm_PayApplyItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <!--查询DelListIds-->
    <select id="getDelCashIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.FmPayapplyPojo">
        select
        id
        from Fm_PayApplyCash
        where Pid = #{id}
        <if test="cash !=null and cash.size()>0">
            and id not in
            <foreach collection="cash" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>


    <!--    刷新预付完成数 Eric 20211213
            20230928 加上Fm_DepoTransfer累加-->
    <update id="updateSaleDepoFinish">
        update Bus_Deposit
        SET OutAmount =COALESCE((SELECT SUM(Fm_PayApplyCash.Amount)
                                 FROM Fm_PayApply
                                          RIGHT JOIN Fm_PayApplyCash ON Fm_PayApplyCash.Pid = Fm_PayApply.id
                                 where Fm_PayApplyCash.PayBillid = #{key}
                                   and Fm_PayApplyCash.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
<!--    <update id="updateSaleDepoFinish">-->
<!--        update Bus_Deposit-->
<!--        SET OutAmount =COALESCE((SELECT SUM(Fm_PayApplyCash.Amount)-->
<!--                                 FROM Fm_PayApply-->
<!--                                          RIGHT JOIN Fm_PayApplyCash ON Fm_PayApplyCash.Pid = Fm_PayApply.id-->
<!--                                 where Fm_PayApplyCash.PayBillid = #{key}-->
<!--                                   and Fm_PayApplyCash.Tenantid = #{tid}), 0)-->
<!--            + COALESCE((SELECT SUM(Fm_DepoTransfer.Applied)-->
<!--                        FROM Fm_DepoTransfer-->
<!--                        where Fm_DepoTransfer.PayBillid = #{key}-->
<!--                          and Fm_DepoTransfer.Tenantid = #{tid}), 0)-->
<!--        where id = #{key}-->
<!--          and Tenantid = #{tid}-->
<!--    </update>-->

    <!--查询DelListIds-->
    <select id="getSaleDepoAmount" resultType="java.lang.Double">
        select Bus_Deposit.OutAmount - Bus_Deposit.BillAmount AS Amount
        from Bus_Deposit
        where id = #{key}
          and Tenantid = #{tid}
    </select>

    <!--    刷新发货完成数 Eric 20211213-->
    <!--    记得同时修改sale服务的 updateBusInvoFinish方法 使得SQL相同-->
    <update id="updateBusInvoFinish">
        update Bus_Invoice
        SET Receipted =COALESCE((SELECT SUM(Bus_ReceiptItem.Amount)
                                 FROM Bus_ReceiptItem
                                          LEFT OUTER JOIN Bus_Receipt
                                                          ON Bus_ReceiptItem.pid = Bus_Receipt.id
                                 where Bus_ReceiptItem.InvoBillCode = #{refno}
                                   and Bus_ReceiptItem.Invoid = #{key}
                                   and Bus_ReceiptItem.Tenantid = #{tid}), 0) +
                       COALESCE((SELECT SUM(Fm_PayApplyItem.Amount)
                                 FROM Fm_PayApply
                                          RIGHT JOIN Fm_PayApplyItem ON Fm_PayApplyItem.Pid = Fm_PayApply.id
                                 where Fm_PayApplyItem.InvoBillCode = #{refno}
                                   and Fm_PayApplyItem.InvoBillid = #{key}
                                   and Fm_PayApplyItem.Tenantid = #{tid}), 0),
            FirstAmt=COALESCE((SELECT SUM(Fm_PayApplyItem.Amount)
                               FROM Fm_PayApply
                                        RIGHT JOIN Fm_PayApplyItem ON Fm_PayApplyItem.Pid = Fm_PayApply.id
                               where Fm_PayApplyItem.InvoBillCode = #{refno}
                                 and Fm_PayApplyItem.InvoBillid = #{key}
                                 and Fm_PayApplyItem.Tenantid = #{tid}), 0),
            LastAmt=COALESCE((SELECT SUM(Bus_ReceiptItem.Amount)
                              FROM Bus_ReceiptItem
                                       LEFT OUTER JOIN Bus_Receipt
                                                       ON Bus_ReceiptItem.pid = Bus_Receipt.id
                              where Bus_ReceiptItem.InvoBillCode = #{refno}
                                and Bus_ReceiptItem.Invoid = #{key}
                                and Bus_ReceiptItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <update id="updateBusInvoItemAvgAmt">
        update Bus_InvoiceItem
        SET AvgFirstAmt = COALESCE(
        (SELECT IF(TaxAmount = 0, 0, FirstAmt / TaxAmount) FROM Bus_Invoice where id=#{key} and Tenantid=#{tid}) * Bus_InvoiceItem.TaxAmount,
        0),
        AvgLastAmt = COALESCE(
        (SELECT IF(TaxAmount = 0, 0, LastAmt / TaxAmount) FROM Bus_Invoice where id=#{key} and Tenantid=#{tid}) * Bus_InvoiceItem.TaxAmount,
        0)
        where Pid = #{key}
          and Tenantid = #{tid}
    </update>


    <update id="updateBusMachingItemAvgAmt">
        update Bus_MachiningItem
        SET AvgFirstAmt=COALESCE((SELECT SUM(AvgFirstAmt) FROM Bus_InvoiceItem where MachItemid=Bus_MachiningItem.id),0),
        AvgLastAmt=COALESCE((SELECT SUM(AvgLastAmt) FROM Bus_InvoiceItem where MachItemid=Bus_MachiningItem.id),0)
        where id in(select distinct MachItemid from Bus_InvoiceItem where Pid=#{key} and Tenantid=#{tid})
        and Tenantid = #{tid}
    </update>


    <update id="updateBusMachingFirstLastAmt">
        update Bus_Machining
        SET FirstAmt=COALESCE((SELECT SUM(AvgFirstAmt) FROM Bus_MachiningItem where Pid=Bus_Machining.id),0),
        LastAmt=COALESCE((SELECT SUM(AvgLastAmt) FROM Bus_MachiningItem where Pid=Bus_Machining.id),0)
        where id in(select distinct Pid from Bus_MachiningItem where id in(select distinct MachItemid from Bus_InvoiceItem where Pid=#{key} and Tenantid=#{tid}))
        and Tenantid = #{tid}
    </update>

    <!--查询DelListIds-->
    <select id="getSaleInvoAmount" resultType="java.lang.Double">
        select Bus_Invoice.Receipted - Bus_Invoice.TaxAmount AS Amount
        from Bus_Invoice
        where id = #{key}
          and Tenantid = #{tid}
    </select>

    <!--    刷新预付完成数 Eric 20211213-->
    <update id="updateBuyPrepFinish">
        update Buy_Prepayments
        SET OutAmount =COALESCE((SELECT SUM(Fm_PayApplyCash.Amount)
                                 FROM Fm_PayApply
                                          RIGHT JOIN Fm_PayApplyCash ON Fm_PayApplyCash.Pid = Fm_PayApply.id
                                 where Fm_PayApplyCash.PayBillCode = #{refno}
                                   and Fm_PayApplyCash.PayBillid = #{key}
                                   and Fm_PayApplyCash.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--查询DelListIds-->
    <select id="getBuyPrepAmount" resultType="java.lang.Double">
        select Buy_Prepayments.OutAmount - Buy_Prepayments.BillAmount AS Amount
        from Buy_Prepayments
        where id = #{key}
          and Tenantid = #{tid}
    </select>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateBuyInvoFinish">
        update Buy_Invoice
        SET Paid =COALESCE((SELECT SUM(Buy_VoucherItem.Amount)
                            FROM Buy_VoucherItem
                                     LEFT OUTER JOIN Buy_Voucher
                                                     ON Buy_VoucherItem.pid = Buy_Voucher.id
                            where Buy_VoucherItem.InvoBillCode = #{refno}
                              and Buy_VoucherItem.Invoid = #{key}
                              and Buy_VoucherItem.Tenantid = #{tid}), 0) +
                  COALESCE((SELECT SUM(Fm_PayApplyItem.Amount)
                            FROM Fm_PayApply
                                     RIGHT JOIN Fm_PayApplyItem ON Fm_PayApplyItem.Pid = Fm_PayApply.id
                            where Fm_PayApplyItem.InvoBillCode = #{refno}
                              and Fm_PayApplyItem.InvoBillid = #{key}
                              and Fm_PayApplyItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--查询DelListIds-->
    <select id="getBuyInvoAmount" resultType="java.lang.Double">
        select Buy_Invoice.Paid  - Buy_Invoice.TaxAmount AS Amount
        from Buy_Invoice
        where id = #{key}
          and Tenantid = #{tid}
    </select>

</mapper>

