<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BusDeductionitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BusDeductionitemPojo">
        SELECT
            Bus_DeductionItem.id,
            Bus_DeductionItem.Pid,
            Bus_DeductionItem.Goodsid,
            Bus_DeductionItem.Quantity,
            Bus_DeductionItem.TaxPrice,
            Bus_DeductionItem.TaxAmount,
            Bus_DeductionItem.TaxTotal,
            Bus_DeductionItem.ItemTaxrate,
            Bus_DeductionItem.Price,
            Bus_DeductionItem.Amount,
            Bus_DeductionItem.Remark,
            Bus_DeductionItem.CiteUid,
            Bus_DeductionItem.CiteItemid,
            Bus_DeductionItem.MachUid,
            Bus_DeductionItem.MachItemid,
            Bus_DeductionItem.CustPO,
            Bus_DeductionItem.RowNum,
            Bus_DeductionItem.InvoQty,
            Bus_DeductionItem.InvoClosed,
            Bus_DeductionItem.DisannulMark,
            Bus_DeductionItem.DisannulListerid,
            Bus_DeductionItem.DisannulLister,
            Bus_DeductionItem.DisannulDate,
            Bus_DeductionItem.Custom1,
            Bus_DeductionItem.Custom2,
            Bus_DeductionItem.Custom3,
            Bus_DeductionItem.Custom4,
            Bus_DeductionItem.Custom5,
            Bus_DeductionItem.Custom6,
            Bus_DeductionItem.Custom7,
            Bus_DeductionItem.Custom8,
            Bus_DeductionItem.Custom9,
            Bus_DeductionItem.Custom10,
            Bus_DeductionItem.Tenantid,
            Bus_DeductionItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10
        FROM
            Bus_DeductionItem
                LEFT JOIN Mat_Goods ON Bus_DeductionItem.Goodsid = Mat_Goods.id
        where Bus_DeductionItem.id = #{key}
          and Bus_DeductionItem.Tenantid = #{tid}
    </select>
    <sql id="selectBusDeductionitemVo">
        SELECT
            Bus_DeductionItem.id,
            Bus_DeductionItem.Pid,
            Bus_DeductionItem.Goodsid,
            Bus_DeductionItem.Quantity,
            Bus_DeductionItem.TaxPrice,
            Bus_DeductionItem.TaxAmount,
            Bus_DeductionItem.TaxTotal,
            Bus_DeductionItem.ItemTaxrate,
            Bus_DeductionItem.Price,
            Bus_DeductionItem.Amount,
            Bus_DeductionItem.Remark,
            Bus_DeductionItem.CiteUid,
            Bus_DeductionItem.CiteItemid,
            Bus_DeductionItem.MachUid,
            Bus_DeductionItem.MachItemid,
            Bus_DeductionItem.CustPO,
            Bus_DeductionItem.RowNum,
            Bus_DeductionItem.InvoQty,
            Bus_DeductionItem.InvoClosed,
            Bus_DeductionItem.DisannulMark,
            Bus_DeductionItem.DisannulListerid,
            Bus_DeductionItem.DisannulLister,
            Bus_DeductionItem.DisannulDate,
            Bus_DeductionItem.Custom1,
            Bus_DeductionItem.Custom2,
            Bus_DeductionItem.Custom3,
            Bus_DeductionItem.Custom4,
            Bus_DeductionItem.Custom5,
            Bus_DeductionItem.Custom6,
            Bus_DeductionItem.Custom7,
            Bus_DeductionItem.Custom8,
            Bus_DeductionItem.Custom9,
            Bus_DeductionItem.Custom10,
            Bus_DeductionItem.Tenantid,
            Bus_DeductionItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10
        FROM
            Bus_DeductionItem
                LEFT JOIN Mat_Goods ON Bus_DeductionItem.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusDeductionitemPojo">
        <include refid="selectBusDeductionitemVo"/>
        where 1 = 1 and Bus_DeductionItem.Tenantid =#{tenantid}
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_DeductionItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Bus_DeductionItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Bus_DeductionItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Bus_DeductionItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Bus_DeductionItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Bus_DeductionItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Bus_DeductionItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Bus_DeductionItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Bus_DeductionItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Bus_DeductionItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Bus_DeductionItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_DeductionItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_DeductionItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_DeductionItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_DeductionItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_DeductionItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_DeductionItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_DeductionItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_DeductionItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_DeductionItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_DeductionItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Bus_DeductionItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Bus_DeductionItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Bus_DeductionItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Bus_DeductionItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Bus_DeductionItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Bus_DeductionItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Bus_DeductionItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Bus_DeductionItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Bus_DeductionItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Bus_DeductionItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Bus_DeductionItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Bus_DeductionItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Bus_DeductionItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Bus_DeductionItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Bus_DeductionItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Bus_DeductionItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Bus_DeductionItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Bus_DeductionItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Bus_DeductionItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Bus_DeductionItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.BusDeductionitemPojo">
        SELECT
            Bus_DeductionItem.id,
            Bus_DeductionItem.Pid,
            Bus_DeductionItem.Goodsid,
            Bus_DeductionItem.Quantity,
            Bus_DeductionItem.TaxPrice,
            Bus_DeductionItem.TaxAmount,
            Bus_DeductionItem.TaxTotal,
            Bus_DeductionItem.ItemTaxrate,
            Bus_DeductionItem.Price,
            Bus_DeductionItem.Amount,
            Bus_DeductionItem.Remark,
            Bus_DeductionItem.CiteUid,
            Bus_DeductionItem.CiteItemid,
            Bus_DeductionItem.MachUid,
            Bus_DeductionItem.MachItemid,
            Bus_DeductionItem.CustPO,
            Bus_DeductionItem.RowNum,
            Bus_DeductionItem.InvoQty,
            Bus_DeductionItem.InvoClosed,
            Bus_DeductionItem.DisannulMark,
            Bus_DeductionItem.DisannulListerid,
            Bus_DeductionItem.DisannulLister,
            Bus_DeductionItem.DisannulDate,
            Bus_DeductionItem.Custom1,
            Bus_DeductionItem.Custom2,
            Bus_DeductionItem.Custom3,
            Bus_DeductionItem.Custom4,
            Bus_DeductionItem.Custom5,
            Bus_DeductionItem.Custom6,
            Bus_DeductionItem.Custom7,
            Bus_DeductionItem.Custom8,
            Bus_DeductionItem.Custom9,
            Bus_DeductionItem.Custom10,
            Bus_DeductionItem.Tenantid,
            Bus_DeductionItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10
        FROM
            Bus_DeductionItem
                LEFT JOIN Mat_Goods ON Bus_DeductionItem.Goodsid = Mat_Goods.id
        where Bus_DeductionItem.Pid = #{Pid}
          and Bus_DeductionItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_DeductionItem(id, Pid, Goodsid, Quantity, TaxPrice, TaxAmount, TaxTotal, ItemTaxrate, Price,
                                      Amount, Remark, CiteUid, CiteItemid, MachUid, MachItemid, CustPO, RowNum, InvoQty,
                                      InvoClosed, DisannulMark, DisannulListerid, DisannulLister, DisannulDate, Custom1,
                                      Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10,
                                      Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{quantity}, #{taxprice}, #{taxamount}, #{taxtotal}, #{itemtaxrate},
                #{price}, #{amount}, #{remark}, #{citeuid}, #{citeitemid}, #{machuid}, #{machitemid}, #{custpo},
                #{rownum}, #{invoqty}, #{invoclosed}, #{disannulmark}, #{disannullisterid}, #{disannullister},
                #{disannuldate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_DeductionItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="invoqty != null">
                InvoQty = #{invoqty},
            </if>
            <if test="invoclosed != null">
                InvoClosed = #{invoclosed},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_DeductionItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

