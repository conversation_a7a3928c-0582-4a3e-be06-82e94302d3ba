<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BusMachiningMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BusMachiningPojo">
        SELECT Bus_Machining.id,
               Bus_Machining.RefNo,
               Bus_Machining.BillType,
               Bus_Machining.BillTitle,
               Bus_Machining.BillDate,
               Bus_Machining.BillDate,
               Bus_Machining.Projectid,
               Bus_Machining.ProjCode,
               Bus_Machining.ProjCode,
               Bus_Machining.Groupid,
               Bus_Machining.CustOrderid,
               Bus_Machining.LogisticsMode,
               Bus_Machining.LogisticsPort,
               Bus_Machining.Country,
               Bus_Machining.AdvaAmount,
               Bus_Machining.Salesman,
               Bus_Machining.Salesmanid,
               Bus_Machining.Taxrate,
               Bus_Machining.Summary,
               Bus_Machining.CreateBy,
               Bus_Machining.CreateByid,
               Bus_Machining.CreateDate,
               Bus_Machining.Lister,
               Bus_Machining.Listerid,
               Bus_Machining.ModifyDate,
               Bus_Machining.Assessor,
               Bus_Machining.Assessorid,
               Bus_Machining.AssessDate,
               Bus_Machining.BillTaxAmount,
               Bus_Machining.BillTaxTotal,
               Bus_Machining.BillAmount,
               Bus_Machining.BillStateCode,
               Bus_Machining.BillStateDate,
               Bus_Machining.BillPlanDate,
               Bus_Machining.BillWkWpid,
               Bus_Machining.BillWkWpCode,
               Bus_Machining.BillWkWpName,
               Bus_Machining.GroupCode,
               Bus_Machining.DisannulCount,
               Bus_Machining.ItemCount,
               Bus_Machining.PickCount,
               Bus_Machining.FinishCount,
               Bus_Machining.PrintCount,
               Bus_Machining.WkFinishCount,
               Bus_Machining.WkItemCount,
               Bus_Machining.WkWipCount,
               Bus_Machining.Payment,
               Bus_Machining.OaFlowMark,
               Bus_Machining.BillCostBudgetAmt,
               Bus_Machining.FirstAmt,
               Bus_Machining.LastAmt,
               Bus_Machining.InvoAmt,
               Bus_Machining.InvoCount,
               Bus_Machining.Moneyid,
               Bus_Machining.MoneyName,
               Bus_Machining.MainPlanCount,
               Bus_Machining.BillSubTotal,
               Bus_Machining.Couponids,
               Bus_Machining.CouponNames,
               Bus_Machining.FinishAmount,
               Bus_Machining.FinishTaxAmount,
               Bus_Machining.Custom1,
               Bus_Machining.Custom2,
               Bus_Machining.Custom3,
               Bus_Machining.Custom4,
               Bus_Machining.Custom5,
               Bus_Machining.Custom6,
               Bus_Machining.Custom7,
               Bus_Machining.Custom8,
               Bus_Machining.Custom9,
               Bus_Machining.Custom10,
               Bus_Machining.Deptid,
               Bus_Machining.Tenantid,
               Bus_Machining.TenantName,
               Bus_Machining.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.Linkman   AS GroupLink,
               App_Workgroup.Telephone As GroupTel,
               App_Workgroup.PaymentMethod,
               App_Workgroup.GroupAdd,
               App_Workgroup.GroupLevel
        FROM App_Workgroup
                 RIGHT JOIN Bus_Machining ON App_Workgroup.id = Bus_Machining.Groupid
        where Bus_Machining.id = #{key}
          and Bus_Machining.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Bus_Machining.id,
               Bus_Machining.RefNo,
               Bus_Machining.BillType,
               Bus_Machining.BillTitle,
               Bus_Machining.BillDate,
               Bus_Machining.Groupid,
               Bus_Machining.Projectid,
               Bus_Machining.ProjCode,
               Bus_Machining.ProjCode,
               Bus_Machining.CustOrderid,
               Bus_Machining.LogisticsMode,
               Bus_Machining.LogisticsPort,
               Bus_Machining.Country,
               Bus_Machining.AdvaAmount,
               Bus_Machining.Salesman,
               Bus_Machining.Salesmanid,
               Bus_Machining.Taxrate,
               Bus_Machining.Summary,
               Bus_Machining.CreateBy,
               Bus_Machining.CreateByid,
               Bus_Machining.CreateDate,
               Bus_Machining.Lister,
               Bus_Machining.Listerid,
               Bus_Machining.ModifyDate,
               Bus_Machining.Assessor,
               Bus_Machining.Assessorid,
               Bus_Machining.AssessDate,
               Bus_Machining.BillTaxAmount,
               Bus_Machining.BillTaxTotal,
               Bus_Machining.BillAmount,
               Bus_Machining.BillStateCode,
               Bus_Machining.BillStateDate,
               Bus_Machining.BillPlanDate,
               Bus_Machining.BillWkWpid,
               Bus_Machining.BillWkWpCode,
               Bus_Machining.BillWkWpName,
               Bus_Machining.GroupCode,
               Bus_Machining.DisannulCount,
               Bus_Machining.ItemCount,
               Bus_Machining.PickCount,
               Bus_Machining.FinishCount,
               Bus_Machining.PrintCount,
               Bus_Machining.WkFinishCount,
               Bus_Machining.WkItemCount,
               Bus_Machining.WkWipCount,
               Bus_Machining.Payment,
               Bus_Machining.OaFlowMark,
               Bus_Machining.BillCostBudgetAmt,
               Bus_Machining.FirstAmt,
               Bus_Machining.LastAmt,
               Bus_Machining.InvoAmt,
               Bus_Machining.InvoCount,
               Bus_Machining.Moneyid,
               Bus_Machining.MoneyName,
               Bus_Machining.MainPlanCount,
               Bus_Machining.BillSubTotal,
               Bus_Machining.Couponids,
               Bus_Machining.CouponNames,
               Bus_Machining.FinishAmount,
               Bus_Machining.FinishTaxAmount,
               Bus_Machining.Custom1,
               Bus_Machining.Custom2,
               Bus_Machining.Custom3,
               Bus_Machining.Custom4,
               Bus_Machining.Custom5,
               Bus_Machining.Custom6,
               Bus_Machining.Custom7,
               Bus_Machining.Custom8,
               Bus_Machining.Custom9,
               Bus_Machining.Custom10,
               Bus_Machining.Deptid,
               Bus_Machining.Tenantid,
               Bus_Machining.TenantName,
               Bus_Machining.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.Linkman   AS GroupLink,
               App_Workgroup.Telephone As GroupTel,
               App_Workgroup.GroupAdd,
               App_Workgroup.PaymentMethod,
               App_Workgroup.GroupLevel
        FROM App_Workgroup
                 RIGHT JOIN Bus_Machining ON App_Workgroup.id = Bus_Machining.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.GroupLevel,
               App_Workgroup.PaymentMethod,
               Bus_MachiningItem.id,
               Bus_MachiningItem.Pid,
               Bus_MachiningItem.MachBatch,
               Bus_MachiningItem.Goodsid,
               Bus_MachiningItem.Quantity,
               Bus_MachiningItem.TaxPrice,
               Bus_MachiningItem.TaxAmount,
               Bus_MachiningItem.ItemTaxrate,
               Bus_MachiningItem.TaxTotal,
               Bus_MachiningItem.Price,
               Bus_MachiningItem.Amount,
               Bus_MachiningItem.ItemOrgDate,
               Bus_MachiningItem.ItemPlanDate,
               Bus_MachiningItem.WkQty,
               Bus_MachiningItem.StoQty,
               Bus_MachiningItem.RowNum,
               Bus_MachiningItem.Remark,
               Bus_MachiningItem.EngStateText,
               Bus_MachiningItem.EngStateDate,
               Bus_MachiningItem.WkStateText,
               Bus_MachiningItem.WkStateDate,
               Bus_MachiningItem.BusStateText,
               Bus_MachiningItem.BusStateDate,
               Bus_MachiningItem.BuyQuantity,
               Bus_MachiningItem.WkQuantity,
               Bus_MachiningItem.InQuantity,
               Bus_MachiningItem.PickQty,
               Bus_MachiningItem.FinishQty,
               Bus_MachiningItem.OutQuantity,
               Bus_MachiningItem.OutSecQty,
               Bus_MachiningItem.EditionInfo,
               Bus_MachiningItem.ItemCompDate,
               Bus_MachiningItem.VirtualItem,
               Bus_MachiningItem.Closed,
               Bus_MachiningItem.StdPrice,
               Bus_MachiningItem.StdAmount,
               Bus_MachiningItem.Rebate,
               Bus_MachiningItem.MrpUid,
               Bus_MachiningItem.Mrpid,
               Bus_MachiningItem.MaxQty,
               Bus_MachiningItem.Location,
               Bus_MachiningItem.BatchNo,
               Bus_MachiningItem.DisannulMark,
               Bus_MachiningItem.WipUsed,
               Bus_MachiningItem.WkWpid,
               Bus_MachiningItem.WkWpCode,
               Bus_MachiningItem.WkWpName,
               Bus_MachiningItem.WkRowNum,
               Bus_MachiningItem.OrderCostUid,
               Bus_MachiningItem.OrderCostItemid,
               Bus_MachiningItem.BomType,
               Bus_MachiningItem.Bomid,
               Bus_MachiningItem.BomUid,
               Bus_MachiningItem.BomState,
               Bus_MachiningItem.AttributeJson,
               Bus_MachiningItem.AttributeStr,
               Bus_MachiningItem.MatCode,
               Bus_MachiningItem.MatUsed,
               Bus_MachiningItem.MatCostAmt,
               Bus_MachiningItem.LaborCostAmt,
               Bus_MachiningItem.DirectCostAmt,
               Bus_MachiningItem.IndirectCostAmt,
               Bus_MachiningItem.CostItemJson,
               Bus_MachiningItem.CostGroupJson,
               Bus_MachiningItem.CostBudgetAmt,
               Bus_MachiningItem.Specid,
               Bus_MachiningItem.SpecUid,
               Bus_MachiningItem.SpecState,
               Bus_MachiningItem.MatBuyQty,
               Bus_MachiningItem.MatUseQty,
               Bus_MachiningItem.AvgLastAmt,
               Bus_MachiningItem.AvgFirstAmt,
               Bus_MachiningItem.AvgInvoAmt,
               Bus_MachiningItem.InvoQty,
               Bus_MachiningItem.InvoClosed,
               Bus_MachiningItem.MainPlanQty,
               Bus_MachiningItem.MainPlanClosed,
               Bus_MachiningItem.InvoAmt,
               Bus_MachiningItem.Custom1,
               Bus_MachiningItem.Custom2,
               Bus_MachiningItem.Custom3,
               Bus_MachiningItem.Custom4,
               Bus_MachiningItem.Custom5,
               Bus_MachiningItem.Custom6,
               Bus_MachiningItem.Custom7,
               Bus_MachiningItem.Custom8,
               Bus_MachiningItem.Custom9,
               Bus_MachiningItem.Custom10,
               Bus_MachiningItem.Custom11,
               Bus_MachiningItem.Custom12,
               Bus_MachiningItem.Custom13,
               Bus_MachiningItem.Custom14,
               Bus_MachiningItem.Custom15,
               Bus_MachiningItem.Custom16,
               Bus_MachiningItem.Custom17,
               Bus_MachiningItem.Custom18,
               Bus_MachiningItem.Tenantid,
               Bus_MachiningItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Surface,
               Mat_Goods.Material,
               Mat_Goods.Drawing,
               Mat_Goods.BrandName,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10,
               Mat_Goods.Material as GoodsMaterial,
               Bus_Machining.RefNo,
               Bus_Machining.BillType,
               Bus_Machining.BillTitle,
               Bus_Machining.BillDate,
               Bus_Machining.Projectid,
               Bus_Machining.ProjCode,
               Bus_Machining.ProjCode,
               Bus_Machining.Groupid,
               Bus_Machining.CustOrderid,
               Bus_Machining.LogisticsMode,
               Bus_Machining.LogisticsPort,
               Bus_Machining.Country,
               Bus_Machining.AdvaAmount,
               Bus_Machining.Salesman,
               Bus_Machining.Taxrate,
               Bus_Machining.Summary,
               Bus_Machining.CreateBy,
               Bus_Machining.Lister,
               Bus_Machining.Assessor,
               Bus_Machining.ItemCount,
               Bus_Machining.PickCount,
               Bus_Machining.FinishCount,
               Bus_Machining.WkFinishCount,
               Bus_Machining.WkItemCount,
               Bus_Machining.WkWipCount,
               Bus_Machining.Payment,
               Bus_Machining.PrintCount,
               Bus_Machining.OaFlowMark,
               Bus_Machining.BillCostBudgetAmt,
               Bus_Machining.FirstAmt,
               Bus_Machining.LastAmt,
               Bus_Machining.InvoCount,
               Bus_Machining.Moneyid,
               Bus_Machining.MoneyName,
               Bus_Machining.MainPlanCount,
               Bus_Machining.BillSubTotal,
               Bus_Machining.Couponids,
               Bus_Machining.CouponNames,
               Bus_Machining.FinishAmount,
               Bus_Machining.FinishTaxAmount
        FROM App_Workgroup
                 RIGHT JOIN Bus_Machining ON App_Workgroup.id = Bus_Machining.Groupid
                 RIGHT JOIN Bus_MachiningItem ON Bus_MachiningItem.Pid = Bus_Machining.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Bus_MachiningItem.Goodsid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusMachiningitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
          and Bus_Machining.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and (Bus_Machining.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate})
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and (${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Bus_Machining.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Bus_Machining.billtype like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Bus_Machining.billtitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Bus_Machining.groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.custorderid != null">
            and Bus_Machining.custorderid like concat('%',
                #{SearchPojo.custorderid}, '%')
        </if>
        <if test="SearchPojo.logisticsmode != null">
            and Bus_Machining.logisticsmode like concat('%',
                #{SearchPojo.logisticsmode}, '%')
        </if>
        <if test="SearchPojo.logisticsport != null">
            and Bus_Machining.logisticsport like concat('%',
                #{SearchPojo.logisticsport}, '%')
        </if>
        <if test="SearchPojo.country != null">
            and Bus_Machining.country like concat('%',
                #{SearchPojo.country}, '%')
        </if>
        <if test="SearchPojo.salesman != null">
            and Bus_Machining.salesman like concat('%',
                #{SearchPojo.salesman}, '%')
        </if>
        <if test="SearchPojo.salesmanid != null">
            and Bus_Machining.salesmanid like concat('%',
                #{SearchPojo.salesmanid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Bus_Machining.summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Bus_Machining.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Bus_Machining.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Bus_Machining.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Bus_Machining.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Bus_Machining.assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Bus_Machining.assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null">
            and Bus_Machining.billstatecode like concat('%',
                #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.groupcode != null">
            and Bus_Machining.groupcode like concat('%',
                #{SearchPojo.groupcode}, '%')
        </if>
        <if test="SearchPojo.billwkwpid != null">
            and Bus_Machining.BillWkWpid like concat('%',
                #{SearchPojo.billwkwpid}, '%')
        </if>
        <if test="SearchPojo.billwkwpcode != null">
            and Bus_Machining.BillWkWpCode like concat('%',
                #{SearchPojo.billwkwpcode}, '%')
        </if>
        <if test="SearchPojo.billwkwpname != null">
            and Bus_Machining.BillWkWpName like concat('%',
                #{SearchPojo.billwkwpname}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Bus_Machining.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Bus_Machining.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Bus_Machining.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Bus_Machining.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Bus_Machining.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Bus_Machining.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Bus_Machining.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Bus_Machining.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Bus_Machining.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Bus_Machining.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Bus_Machining.tenantname like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Bus_MachiningItem.goodsid=
                #{SearchPojo.goodsid}
        </if>
        <if test="SearchPojo.matcode != null and SearchPojo.matcode != ''">
            and Bus_MachiningItem.matcode=#{SearchPojo.matcode}
        </if>
        <if test="SearchPojo.quotuid != null and SearchPojo.quotuid != ''">
            and Bus_MachiningItem.quotuid=#{SearchPojo.quotuid}
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
        <if test="SearchPojo.ordercostuid != null and SearchPojo.ordercostuid != ''">
            and Bus_MachiningItem.ordercostuid=
                #{SearchPojo.ordercostuid}
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Bus_Machining.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Bus_Machining.BillType like concat('%',
                    #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Bus_Machining.BillTitle like concat('%',
                    #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Bus_Machining.Groupid like concat('%',
                    #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.custorderid != null">
                or Bus_Machining.CustOrderid like concat('%',
                    #{SearchPojo.custorderid}, '%')
            </if>
            <if test="SearchPojo.logisticsmode != null">
                or Bus_Machining.LogisticsMode like concat('%',
                    #{SearchPojo.logisticsmode}, '%')
            </if>
            <if test="SearchPojo.logisticsport != null">
                or Bus_Machining.LogisticsPort like concat('%',
                    #{SearchPojo.logisticsport}, '%')
            </if>
            <if test="SearchPojo.country != null">
                or Bus_Machining.Country like concat('%',
                    #{SearchPojo.country}, '%')
            </if>
            <if test="SearchPojo.salesman != null">
                or Bus_Machining.Salesman like concat('%',
                    #{SearchPojo.salesman}, '%')
            </if>
            <if test="SearchPojo.salesmanid != null">
                or Bus_Machining.Salesmanid like concat('%',
                    #{SearchPojo.salesmanid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Bus_Machining.Summary like concat('%',
                    #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Bus_Machining.CreateBy like concat('%',
                    #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Bus_Machining.CreateByid like concat('%',
                    #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Bus_Machining.Lister like concat('%',
                    #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Bus_Machining.Listerid like concat('%',
                    #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Bus_Machining.Assessor like concat('%',
                    #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Bus_Machining.Assessorid like concat('%',
                    #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null">
                or Bus_Machining.BillStateCode like concat('%',
                    #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.groupcode != null">
                or Bus_Machining.GroupCode like concat('%',
                    #{SearchPojo.groupcode}, '%')
            </if>
            <if test="SearchPojo.billwkwpid != null">
                or Bus_Machining.BillWkWpid like concat('%',
                    #{SearchPojo.billwkwpid}, '%')
            </if>
            <if test="SearchPojo.billwkwpcode != null">
                or Bus_Machining.BillWkWpCode like concat('%',
                    #{SearchPojo.billwkwpcode}, '%')
            </if>
            <if test="SearchPojo.billwkwpname != null">
                or Bus_Machining.BillWkWpName like concat('%',
                    #{SearchPojo.billwkwpname}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Bus_Machining.Custom1 like concat('%',
                    #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Bus_Machining.Custom2 like concat('%',
                    #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Bus_Machining.Custom3 like concat('%',
                    #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Bus_Machining.Custom4 like concat('%',
                    #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Bus_Machining.Custom5 like concat('%',
                    #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Bus_Machining.Custom6 like concat('%',
                    #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Bus_Machining.Custom7 like concat('%',
                    #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Bus_Machining.Custom8 like concat('%',
                    #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Bus_Machining.Custom9 like concat('%',
                    #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Bus_Machining.Custom10 like concat('%',
                    #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Bus_Machining.TenantName like concat('%',
                    #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Bus_MachiningItem.goodsid=
                    #{SearchPojo.goodsid}
            </if>
            <if test="SearchPojo.matcode != null and SearchPojo.matcode != ''">
                or Bus_MachiningItem.matcode=#{SearchPojo.matcode}
            </if>
            <if test="SearchPojo.quotuid != null and SearchPojo.quotuid != ''">
                or Bus_MachiningItem.quotuid=#{SearchPojo.quotuid}
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
            <if test="SearchPojo.ordercostuid != null and SearchPojo.ordercostuid != ''">
                or Bus_MachiningItem.ordercostuid=
                    #{SearchPojo.ordercostuid}
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusMachiningPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_Machining.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Bus_Machining.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Bus_Machining.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Bus_Machining.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Bus_Machining.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Bus_Machining.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.custorderid != null">
            and Bus_Machining.CustOrderid like concat('%', #{SearchPojo.custorderid}, '%')
        </if>
        <if test="SearchPojo.logisticsmode != null">
            and Bus_Machining.LogisticsMode like concat('%', #{SearchPojo.logisticsmode}, '%')
        </if>
        <if test="SearchPojo.logisticsport != null">
            and Bus_Machining.LogisticsPort like concat('%', #{SearchPojo.logisticsport}, '%')
        </if>
        <if test="SearchPojo.country != null">
            and Bus_Machining.Country like concat('%', #{SearchPojo.country}, '%')
        </if>
        <if test="SearchPojo.salesman != null">
            and Bus_Machining.Salesman like concat('%', #{SearchPojo.salesman}, '%')
        </if>
        <if test="SearchPojo.salesmanid != null">
            and Bus_Machining.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Bus_Machining.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Bus_Machining.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Bus_Machining.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Bus_Machining.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Bus_Machining.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Bus_Machining.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Bus_Machining.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null">
            and Bus_Machining.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.groupcode != null">
            and Bus_Machining.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
        </if>
        <if test="SearchPojo.billwkwpid != null">
            and Bus_Machining.BillWkWpid like concat('%', #{SearchPojo.billwkwpid}, '%')
        </if>
        <if test="SearchPojo.billwkwpcode != null">
            and Bus_Machining.BillWkWpCode like concat('%', #{SearchPojo.billwkwpcode}, '%')
        </if>
        <if test="SearchPojo.billwkwpname != null">
            and Bus_Machining.BillWkWpName like concat('%', #{SearchPojo.billwkwpname}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Bus_Machining.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Bus_Machining.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Bus_Machining.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Bus_Machining.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Bus_Machining.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Bus_Machining.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Bus_Machining.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Bus_Machining.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Bus_Machining.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Bus_Machining.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Bus_Machining.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Bus_Machining.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Bus_Machining.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Bus_Machining.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Bus_Machining.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.custorderid != null">
                or Bus_Machining.CustOrderid like concat('%', #{SearchPojo.custorderid}, '%')
            </if>
            <if test="SearchPojo.logisticsmode != null">
                or Bus_Machining.LogisticsMode like concat('%', #{SearchPojo.logisticsmode}, '%')
            </if>
            <if test="SearchPojo.logisticsport != null">
                or Bus_Machining.LogisticsPort like concat('%', #{SearchPojo.logisticsport}, '%')
            </if>
            <if test="SearchPojo.country != null">
                or Bus_Machining.Country like concat('%', #{SearchPojo.country}, '%')
            </if>
            <if test="SearchPojo.salesman != null">
                or Bus_Machining.Salesman like concat('%', #{SearchPojo.salesman}, '%')
            </if>
            <if test="SearchPojo.salesmanid != null">
                or Bus_Machining.Salesmanid like concat('%', #{SearchPojo.salesmanid}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Bus_Machining.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Bus_Machining.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Bus_Machining.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Bus_Machining.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Bus_Machining.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Bus_Machining.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Bus_Machining.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null">
                or Bus_Machining.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.groupcode != null">
                or Bus_Machining.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
            </if>
            <if test="SearchPojo.billwkwpid != null">
                or Bus_Machining.BillWkWpid like concat('%', #{SearchPojo.billwkwpid}, '%')
            </if>
            <if test="SearchPojo.billwkwpcode != null">
                or Bus_Machining.BillWkWpCode like concat('%', #{SearchPojo.billwkwpcode}, '%')
            </if>
            <if test="SearchPojo.billwkwpname != null">
                or Bus_Machining.BillWkWpName like concat('%', #{SearchPojo.billwkwpname}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Bus_Machining.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Bus_Machining.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Bus_Machining.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Bus_Machining.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Bus_Machining.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Bus_Machining.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Bus_Machining.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Bus_Machining.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Bus_Machining.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Bus_Machining.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Bus_Machining.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
            <if test="SearchPojo.billtaxamount != null">
                or Bus_Machining.BillTaxAmount= #{SearchPojo.billtaxamount}
            </if>
            <if test="SearchPojo.billamount != null">
                or Bus_Machining.BillAmount= #{SearchPojo.billamount}
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Bus_Machining(id, RefNo, BillType, BillTitle, BillDate, Projectid, ProjCode, ProjName, Groupid, CustOrderid, LogisticsMode, LogisticsPort, Country, AdvaAmount, Salesman, Salesmanid, Taxrate, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, BillTaxAmount, BillTaxTotal, BillAmount, BillStateCode, BillStateDate, BillPlanDate, BillWkWpid, BillWkWpCode, BillWkWpName, GroupCode, ItemCount, PickCount, FinishCount, DisannulCount, PrintCount, WkItemCount, WkFinishCount, WkWipCount, Payment, OaFlowMark, BillAttaCount, BillCostBudgetAmt, FirstAmt, LastAmt, InvoAmt, InvoCount, Moneyid, MoneyName, MainPlanCount, BillSubTotal, Couponids, CouponNames, FinishTaxAmount, FinishAmount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{groupid}, #{custorderid}, #{logisticsmode}, #{logisticsport}, #{country}, #{advaamount}, #{salesman}, #{salesmanid}, #{taxrate}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{billtaxamount}, #{billtaxtotal}, #{billamount}, #{billstatecode}, #{billstatedate}, #{billplandate}, #{billwkwpid}, #{billwkwpcode}, #{billwkwpname}, #{groupcode}, #{itemcount}, #{pickcount}, #{finishcount}, #{disannulcount}, #{printcount}, #{wkitemcount}, #{wkfinishcount}, #{wkwipcount}, #{payment}, #{oaflowmark}, #{billattacount}, #{billcostbudgetamt}, #{firstamt}, #{lastamt}, #{invoamt}, #{invocount}, #{moneyid}, #{moneyname}, #{mainplancount}, #{billsubtotal}, #{couponids}, #{couponnames}, #{finishtaxamount}, #{finishamount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_Machining
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null">
                ProjName =#{projname},
            </if>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="custorderid != null">
                CustOrderid =#{custorderid},
            </if>
            <if test="logisticsmode != null">
                LogisticsMode =#{logisticsmode},
            </if>
            <if test="logisticsport != null">
                LogisticsPort =#{logisticsport},
            </if>
            <if test="country != null">
                Country =#{country},
            </if>
            <if test="advaamount != null">
                AdvaAmount =#{advaamount},
            </if>
            <if test="salesman != null">
                Salesman =#{salesman},
            </if>
            <if test="salesmanid != null">
                Salesmanid =#{salesmanid},
            </if>
            <if test="taxrate != null">
                Taxrate =#{taxrate},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billstatecode != null">
                BillStateCode =#{billstatecode},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="billplandate != null">
                BillPlanDate =#{billplandate},
            </if>
            <if test="billwkwpid != null">
                BillWkWpid =#{billwkwpid},
            </if>
            <if test="billwkwpcode != null">
                BillWkWpCode =#{billwkwpcode},
            </if>
            <if test="billwkwpname != null">
                BillWkWpName =#{billwkwpname},
            </if>
            <if test="groupcode != null">
                GroupCode =#{groupcode},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="pickcount != null">
                PickCount =#{pickcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="wkfinishcount != null">
                WkFinishCount =#{wkfinishcount},
            </if>
            <if test="wkitemcount != null">
                WkItemCount =#{wkitemcount},
            </if>
            <if test="wkwipcount != null">
                WkWipCount =#{wkwipcount},
            </if>
            <if test="payment != null">
                Payment =#{payment},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="billcostbudgetamt != null">
                BillCostBudgetAmt =#{billcostbudgetamt},
            </if>
            <if test="firstamt != null">
                FirstAmt =#{firstamt},
            </if>
            <if test="lastamt != null">
                LastAmt =#{lastamt},
            </if>
            <if test="invoamt != null">
                InvoAmt =#{invoamt},
            </if>
            <if test="invocount != null">
                InvoCount =#{invocount},
            </if>
            <if test="moneyid != null">
                Moneyid =#{moneyid},
            </if>
            <if test="moneyname != null">
                MoneyName =#{moneyname},
            </if>
            <if test="mainplancount != null">
                MainPlanCount =#{mainplancount},
            </if>
            <if test="billsubtotal != null">
                BillSubTotal =#{billsubtotal},
            </if>
            <if test="couponids != null ">
            Couponids =#{couponids},
        </if>
            <if test="couponnames != null ">
            CouponNames =#{couponnames},
        </if>
            <if test="finishtaxamount != null">
                FinishTaxAmount =#{finishtaxamount},
            </if>
            <if test="finishamount != null">
                FinishAmount =#{finishamount},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_Machining
        where id = #{key}
        and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Bus_Machining
        SET Assessor = #{assessor},
        Assessorid = #{assessorid},
        AssessDate = #{assessdate},
        Revision=Revision + 1
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BusMachiningPojo">
        select id
        from Bus_MachiningItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <!--    刷新发货完成数 Eric 20220722-->
    <update id="updateDisannulCount">
        update Bus_Machining
        SET DisannulCount =COALESCE((SELECT COUNT(0)
        FROM Bus_MachiningItem
        where Bus_MachiningItem.Pid = #{key}
        and Bus_MachiningItem.Tenantid = #{tid}
        and Bus_MachiningItem.DisannulMark = 1), 0)
        where id = #{key}
        and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成行数 Eric 20211213-->
    <update id="updateFinishCount">
        update Bus_Machining
        SET FinishCount =COALESCE((SELECT COUNT(0)
        FROM Bus_MachiningItem
        where Bus_MachiningItem.Pid = #{key}
        and Bus_MachiningItem.Tenantid = #{tid}
        and (Bus_MachiningItem.Closed = 1
        or Bus_MachiningItem.FinishQty >= Bus_MachiningItem.Quantity)), 0)
        where id = #{key}
        and Tenantid = #{tid}
    </update>

    <!--  查询销售订单是否被引用  -->
    <select id="getItemCiteBillName" resultType="string">
        (SELECT '送货单' as billname
        From Bus_DelieryItem
        where (MachItemid = #{key} or CiteItemid = #{key})
        and Tenantid = #{tid}
        LIMIT 1)
        UNION ALL
        (SELECT '采购计划' as billname From Buy_PlanItem where MachItemid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '采购合同' as billname From Buy_OrderItem where MachItemid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '领料单' as billname From Mat_RequisitionItem where MachItemid = #{key} and Tenantid = #{tid} LIMIT 1)
    </select>

    <!--    刷新订单发货单价 Eric 20220708-->
    <update id="updateMachDeliPrice" parameterType="inks.service.sa.som.domain.pojo.BusMachiningitemPojo">
        update Bus_DelieryItem
        SET Price=#{price},
        Amount= Quantity * #{price},
        TaxPrice=#{taxprice},
        TaxAmount=Quantity * #{taxprice},
        ItemTaxrate=#{itemtaxrate},
        TaxTotal=Quantity * #{taxprice} - Quantity * #{price}
        where Machitemid = #{id}
        and Tenantid = #{tenantid}
    </update>
    <!--    刷新订单发货主表金额 nanno 20230420-->
    <update id="updateMachDeliBillAmt">
        UPDATE Bus_Deliery d
        SET d.BillAmount = (SELECT COALESCE(SUM(Amount), 0) BillAmount
        FROM Bus_DelieryItem
        WHERE Bus_DelieryItem.Pid = d.id
        AND Bus_DelieryItem.Tenantid = #{tenantid}),
        d.BillTaxAmount = (SELECT COALESCE(SUM(TaxAmount), 0) as BillTaxAmount
        FROM Bus_DelieryItem
        WHERE Bus_DelieryItem.Pid = d.id
        AND Bus_DelieryItem.Tenantid = #{tenantid}),
        d.BillTaxTotal =(SELECT COALESCE(SUM(TaxTotal), 0) as BillTaxTotal
        FROM Bus_DelieryItem
        WHERE Bus_DelieryItem.Pid = d.id
        AND Bus_DelieryItem.Tenantid = #{tenantid})
        WHERE d.id in (SELECT pid FROM Bus_DelieryItem WHERE Machitemid = #{id})
        AND d.Tenantid = #{tenantid}
    </update>


    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateOrderCostItemFinish">
        update Bus_OrderCostItem
        SET MachMark =COALESCE((SELECT COUNT(0)
        FROM Bus_MachiningItem
        LEFT OUTER JOIN Bus_Machining
        ON Bus_MachiningItem.pid = Bus_Machining.id
        where Bus_MachiningItem.ordercostuid = #{refno}
        and Bus_MachiningItem.ordercostitemid = #{key}
        and Bus_MachiningItem.DisannulMark = 0
        and Bus_MachiningItem.Tenantid = #{tid}), 0)
        where id = #{key}
        and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateOrderCostFinishCount">
        update Bus_OrderCost
        SET FinishCount =COALESCE((SELECT COUNT(0)
        FROM Bus_OrderCostItem
        where Bus_OrderCostItem.Pid = (SELECT Pid FROM Bus_OrderCostItem where id = #{key})
        and Bus_OrderCostItem.Tenantid = #{tid}
        and Bus_OrderCostItem.MachMark = 1), 0)
        where id = (SELECT Pid FROM Bus_OrderCostItem where id = #{key})
        and Tenantid = #{tid}
    </update>

    <update id="updateQuotItemFinish">
        update Bus_QuotationItem
        SET MachMark =COALESCE((SELECT COUNT(0)
        FROM Bus_MachiningItem
        LEFT OUTER JOIN Bus_Machining
        ON Bus_MachiningItem.pid = Bus_Machining.id
        where Bus_MachiningItem.QuotItemid = #{key}
        and Bus_MachiningItem.DisannulMark = 0
        and Bus_MachiningItem.Tenantid = #{tid}), 0)
        where id = #{key}
        and Tenantid = #{tid}
    </update>
    <update id="updateQuotFinishCount">
        update Bus_Quotation
        SET FinishCount =COALESCE((SELECT COUNT(0)
        FROM Bus_QuotationItem
        where Bus_QuotationItem.Pid = (SELECT Pid FROM Bus_QuotationItem where id = #{key})
        and Bus_QuotationItem.Tenantid = #{tid}
        and Bus_QuotationItem.MachMark = 1), 0)
        where id = (SELECT Pid FROM Bus_QuotationItem where id = #{key})
        and Tenantid = #{tid}
    </update>
    <!--查询List-->
    <select id="getItemListByIds" resultType="inks.service.sa.som.domain.pojo.BusMachiningitemPojo">
        SELECT Bus_MachiningItem.id,
        Bus_MachiningItem.Pid,
        Bus_MachiningItem.MachBatch,
        Bus_MachiningItem.Goodsid,
        Bus_MachiningItem.Quantity,
        Bus_MachiningItem.TaxPrice,
        Bus_MachiningItem.TaxAmount,
        Bus_MachiningItem.ItemTaxrate,
        Bus_MachiningItem.TaxTotal,
        Bus_MachiningItem.Price,
        Bus_MachiningItem.Amount,
        Bus_MachiningItem.ItemOrgDate,
        Bus_MachiningItem.ItemPlanDate,
        Bus_MachiningItem.WkQty,
        Bus_MachiningItem.StoQty,
        Bus_MachiningItem.RowNum,
        Bus_MachiningItem.Remark,
        Bus_MachiningItem.EngStateText,
        Bus_MachiningItem.EngStateDate,
        Bus_MachiningItem.WkStateText,
        Bus_MachiningItem.WkStateDate,
        Bus_MachiningItem.BusStateText,
        Bus_MachiningItem.BusStateDate,
        Bus_MachiningItem.BuyQuantity,
        Bus_MachiningItem.WkQuantity,
        Bus_MachiningItem.InQuantity,
        Bus_MachiningItem.PickQty,
        Bus_MachiningItem.FinishQty,
        Bus_MachiningItem.OutQuantity,
        Bus_MachiningItem.OutSecQty,
        Bus_MachiningItem.EditionInfo,
        Bus_MachiningItem.ItemCompDate,
        Bus_MachiningItem.VirtualItem,
        Bus_MachiningItem.Closed,
        Bus_MachiningItem.StdPrice,
        Bus_MachiningItem.StdAmount,
        Bus_MachiningItem.Rebate,
        Bus_MachiningItem.MrpUid,
        Bus_MachiningItem.Mrpid,
        Bus_MachiningItem.MaxQty,
        Bus_MachiningItem.Location,
        Bus_MachiningItem.BatchNo,
        Bus_MachiningItem.DisannulMark,
        Bus_MachiningItem.WipUsed,
        Bus_MachiningItem.WkWpid,
        Bus_MachiningItem.WkWpCode,
        Bus_MachiningItem.WkWpName,
        Bus_MachiningItem.WkRowNum,
        Bus_MachiningItem.OrderCostUid,
        Bus_MachiningItem.OrderCostItemid,
        Bus_MachiningItem.QuotUid,
        Bus_MachiningItem.QuotItemid,
        Bus_MachiningItem.BomType,
        Bus_MachiningItem.Bomid,
        Bus_MachiningItem.BomUid,
        Bus_MachiningItem.BomState,
        Bus_MachiningItem.AttributeJson,
        Bus_MachiningItem.AttributeStr,
        Bus_MachiningItem.MachType,
        Bus_MachiningItem.ReorderMark,
        Bus_MachiningItem.MatCode,
        Bus_MachiningItem.MatUsed,
        Bus_MachiningItem.CostItemJson,
        Bus_MachiningItem.CostGroupJson,
        Bus_MachiningItem.Specid,
        Bus_MachiningItem.SpecUid,
        Bus_MachiningItem.SpecState,
        Bus_MachiningItem.MatBuyQty,
        Bus_MachiningItem.MatUseQty,
        Bus_MachiningItem.MainPlanQty,
        Bus_MachiningItem.MainPlanClosed,
        Bus_MachiningItem.Custom1,
        Bus_MachiningItem.Custom2,
        Bus_MachiningItem.Custom3,
        Bus_MachiningItem.Custom4,
        Bus_MachiningItem.Custom5,
        Bus_MachiningItem.Custom6,
        Bus_MachiningItem.Custom7,
        Bus_MachiningItem.Custom8,
        Bus_MachiningItem.Custom9,
        Bus_MachiningItem.Custom10,
        Bus_MachiningItem.Custom11,
        Bus_MachiningItem.Custom12,
        Bus_MachiningItem.Custom13,
        Bus_MachiningItem.Custom14,
        Bus_MachiningItem.Custom15,
        Bus_MachiningItem.Custom16,
        Bus_MachiningItem.Custom17,
        Bus_MachiningItem.Custom18,
        Bus_MachiningItem.Tenantid,
        Bus_MachiningItem.Revision,
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
        Mat_Goods.Material as GoodsMaterial,
        Mat_Goods.Custom1 AS GoodsCustom1,
        Mat_Goods.Custom2 AS GoodsCustom2,
        Mat_Goods.Custom3 AS GoodsCustom3,
        Mat_Goods.Custom4 AS GoodsCustom4,
        Mat_Goods.Custom5 AS GoodsCustom5,
        Mat_Goods.Custom6 AS GoodsCustom6,
        Mat_Goods.Custom7 AS GoodsCustom7,
        Mat_Goods.Custom8 AS GoodsCustom8,
        Mat_Goods.Custom9 AS GoodsCustom9,
        Mat_Goods.Custom10 AS GoodsCustom10
        FROM Bus_MachiningItem
        LEFT JOIN Mat_Goods ON Bus_MachiningItem.Goodsid = Mat_Goods.id
        where Bus_MachiningItem.Pid = #{pid}
        and Bus_MachiningItem.id in (${ids})
        and Bus_MachiningItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--  根据料号获取货品信息  -->
    <select id="getGoodsidByGoodsUid" resultType="java.lang.String">
        SELECT id
        FROM Mat_Goods
        WHERE Mat_Goods.Tenantid = #{tid}
        and Mat_Goods.GoodsUid = #{goodsuid}
        ORDER BY CreateDate desc
        limit 0,1
    </select>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateGoodsRequRemQty">
        update Mat_Goods
        SET RequRemQty = COALESCE((SELECT SUM(CASE
        WHEN Mat_Requisition.BillType IN ('生产领料', '其他领料', '领料单')
        THEN Mat_RequisitionItem.Quantity - Mat_RequisitionItem.FinishQty
        ELSE Mat_RequisitionItem.FinishQty - Mat_RequisitionItem.Quantity END)
        FROM Mat_RequisitionItem
        LEFT OUTER JOIN Mat_Requisition ON Mat_RequisitionItem.pid =
        Mat_Requisition.id
        where Mat_RequisitionItem.Goodsid = #{key}
        and Mat_RequisitionItem.Closed = 0
        and Mat_RequisitionItem.DisannulMark = 0
        and Mat_Requisition.Tenantid = #{tid}), 0) +
        COALESCE((SELECT SUM(Bus_MachiningItem.WkQty)
        FROM Bus_MachiningItem
        LEFT OUTER JOIN Bus_Machining ON Bus_MachiningItem.pid =
        Bus_Machining.id
        where Bus_MachiningItem.MatCode = #{goodsuid}
        and Bus_MachiningItem.MatUsed<![CDATA[ !=]]> 1
        and Bus_MachiningItem.Closed = 0
        and Bus_MachiningItem.DisannulMark = 0
        and Bus_Machining.Tenantid = #{tid}), 0)
        where id = #{key}
        and Tenantid = #{tid}
    </update>
    <update id="updatePrintcount" parameterType="inks.service.sa.som.domain.pojo.BusMachiningPojo">
        update Bus_Machining
        SET PrintCount = #{printcount}
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>

    <update id="updateOaflowmark" parameterType="inks.service.sa.som.domain.pojo.BusMachiningPojo">
        update Bus_Machining
        SET OaFlowMark = #{oaflowmark}
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>

    <select id="getLastAmtByGroupId" resultType="java.util.Map">
        SELECT SUM(Bus_MachiningItem.AvgLastAmt) AS lastamt,
        SUM(Bus_MachiningItem.TaxAmount) AS taxamount
        FROM Bus_MachiningItem
        LEFT JOIN Bus_Machining ON Bus_MachiningItem.pid = Bus_Machining.id
        WHERE Bus_Machining.Groupid = #{groupid}
        and Bus_MachiningItem.Closed = 0
        and Bus_MachiningItem.DisannulMark = 0
        AND Bus_MachiningItem.Tenantid = #{tid}
    </select>

    <select id="getOnlineMachTaxAmountByGroupId" resultType="java.lang.Double">
        SELECT SUM(Bus_MachiningItem.TaxAmount) AS TaxAmount
        FROM Bus_MachiningItem
                 LEFT JOIN Bus_Machining ON Bus_MachiningItem.pid = Bus_Machining.id
        WHERE Bus_Machining.Groupid = #{groupid}
          and Bus_MachiningItem.Closed = 0
          and Bus_MachiningItem.DisannulMark = 0
          and Bus_MachiningItem.FinishQty <![CDATA[<]]> Bus_MachiningItem.Quantity
          AND Bus_MachiningItem.Tenantid = #{tid}
    </select>

    <!--    采购额 BuytaxAmt=sum(采购合同item.taxamount) where machitemid-->
    <select id="getBuyTaxAmt" resultType="java.lang.Double">
        SELECT IFNULL(SUM(Buy_OrderItem.TaxAmount),0) AS BuytaxAmt
        FROM Buy_OrderItem
        where Buy_OrderItem.Machitemid = #{machitemid}
    </select>

    <update id="updateFinishAmount">
        WITH ItemPid AS (SELECT Pid
                         FROM Bus_MachiningItem
                         WHERE id = #{machitemid}
                         LIMIT 1),
             ItemSum AS (SELECT SUM(Amount)    AS totalAmount,
                                SUM(TaxAmount) AS totalTaxAmount
                         FROM Bus_MachiningItem
                         WHERE Pid = (SELECT Pid FROM ItemPid)
                           AND (FinishQty >= Quantity OR Closed = 1))
        UPDATE Bus_Machining
        SET FinishAmount    = COALESCE((SELECT totalAmount FROM ItemSum), 0),
            FinishTaxAmount = COALESCE((SELECT totalTaxAmount FROM ItemSum), 0)
        WHERE id = (SELECT Pid FROM ItemPid)
          AND Tenantid = #{tid}
    </update>

    <select id="getEntityByitemid" resultType="inks.service.sa.som.domain.pojo.BusMachiningPojo">
        SELECT Bus_Machining.*
        FROM Bus_Machining
        join Bus_MachiningItem on Bus_Machining.id = Bus_MachiningItem.pid
        WHERE Bus_MachiningItem.id = #{machitemid}
        AND Bus_MachiningItem.Tenantid = #{tid}
    </select>
</mapper>

