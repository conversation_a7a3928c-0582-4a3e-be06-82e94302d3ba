<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.D03MBIR1Mapper">

    <select id="getSumAmtByGroupMax" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select (case
                    when App_Workgroup.Abbreviate is null or
                         App_Workgroup.Abbreviate = '' then App_Workgroup.GroupName
                    else App_Workgroup.Abbreviate end)           as name,
               App_Workgroup.GroupUid                          as code,
               sum(CASE
                       WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_FinishingItem.TaxAmount
                       ELSE 0 - Buy_FinishingItem.TaxAmount END) as value
        FROM Buy_Finishing
                 RIGHT JOIN Buy_FinishingItem
                            ON Buy_FinishingItem.Pid = Buy_Finishing.id
                 LEFT JOIN App_Workgroup ON Buy_Finishing.Groupid = App_Workgroup.id
        where Buy_FinishingItem.disannulmark = 0
          and Buy_Finishing.Tenantid = #{tenantid}
          and (Buy_Finishing.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        <if test="filterstr != null">
            ${filterstr}
        </if>
        group by App_Workgroup.Abbreviate, App_Workgroup.GroupName, App_Workgroup.GroupUid
        order by value desc
    </select>
    <select id="getSumAmtByGoodsMax" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select Mat_Goods.GoodsName as name,
        Mat_Goods.GoodsUid as code,
        Mat_Goods.GoodsSpec as spec,
        Mat_Goods.GoodsUnit as unit,
               sum(CASE
                       WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_FinishingItem.TaxAmount
                       ELSE 0 - Buy_FinishingItem.TaxAmount END) as value
        FROM Buy_Finishing
            RIGHT JOIN Buy_FinishingItem ON Buy_Finishing.id = Buy_FinishingItem.Pid
            LEFT JOIN Mat_Goods ON Buy_FinishingItem.Goodsid = Mat_Goods.id
        where Buy_FinishingItem.disannulmark=0
          and Buy_Finishing.Tenantid =#{tenantid}
          and (Buy_Finishing.BillDate BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
        group by Buy_FinishingItem.Goodsid,Mat_Goods.GoodsName,Mat_Goods.GoodsUid,Mat_Goods.GoodsSpec,Mat_Goods.GoodsUnit
        order by value desc
    </select>

    <select id="getSumAmtByYear" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select date_format(Buy_Finishing.BillDate, '%Y-%m') name,
               sum(CASE
                       WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_FinishingItem.TaxAmount
                       ELSE 0 - Buy_FinishingItem.TaxAmount END) as value
        FROM Buy_Finishing
            RIGHT JOIN Buy_FinishingItem
        ON Buy_FinishingItem.Pid = Buy_Finishing.id
            LEFT JOIN App_Workgroup ON Buy_Finishing.Groupid = App_Workgroup.id
        where Buy_FinishingItem.disannulmark=0
          and Buy_Finishing.Tenantid =#{tenantid}
          and (Buy_Finishing.BillDate BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
        group by date_format(Buy_Finishing.BillDate, '%Y-%m')
    </select>
    <select id="getSumAmtByMonth" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select date_format(Buy_Finishing.BillDate, '%Y-%m-%d') name,
               sum(CASE
                       WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_FinishingItem.TaxAmount
                       ELSE 0 - Buy_FinishingItem.TaxAmount END) as value
        FROM Buy_Finishing
            RIGHT JOIN Buy_FinishingItem
        ON Buy_FinishingItem.Pid = Buy_Finishing.id
            LEFT JOIN App_Workgroup ON Buy_Finishing.Groupid = App_Workgroup.id
        where Buy_FinishingItem.disannulmark=0
          and Buy_Finishing.Tenantid =#{tenantid}
          and (Buy_Finishing.BillDate BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
        group by date_format(Buy_Finishing.BillDate, '%Y-%m-%d')
    </select>
    <select id="getSumAmtByDay" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select date_format(Buy_Finishing.BillDate, '%Y-%m-%d') name,
               sum(CASE
                       WHEN Buy_Finishing.BillType IN ('发出商品','其他发货','返工补发') THEN Buy_FinishingItem.TaxAmount
                       ELSE 0 - Buy_FinishingItem.TaxAmount END) as value
        FROM Buy_Finishing
            RIGHT JOIN Buy_FinishingItem
        ON Buy_FinishingItem.Pid = Buy_Finishing.id
            LEFT JOIN App_Workgroup ON Buy_Finishing.Groupid = App_Workgroup.id
        where Buy_FinishingItem.disannulmark=0
          and Buy_Finishing.Tenantid =#{tenantid}
          and (Buy_Finishing.BillDate BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
        group by date_format(Buy_Finishing.BillDate, '%Y-%m-%d')
    </select>

    <select id="getTagSumAmtQtyByDate" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select sum(CASE
                       WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_FinishingItem.TaxAmount
                       ELSE 0 - Buy_FinishingItem.TaxAmount END) as value,
        sum(CASE
                        WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_FinishingItem.Quantity
                        ELSE 0 - Buy_FinishingItem.Quantity END) as valueb,
        count(0) as valuec
        FROM Buy_Finishing
            RIGHT JOIN Buy_FinishingItem
        ON Buy_FinishingItem.Pid = Buy_Finishing.id
            LEFT JOIN App_Workgroup ON Buy_Finishing.Groupid = App_Workgroup.id
        where Buy_FinishingItem.disannulmark=0
          and Buy_Finishing.Tenantid =#{tenantid}
          and (Buy_Finishing.BillDate BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
    </select>



    <!--    <select id="getItemCountSumAmtGroupMonth" parameterType="java.lang.String"-->
<!--            resultType="inks.common.core.domain.ChartPojo">-->
<!--        SELECT NAME-->
<!--                ,-->
<!--               sum(Amount) AS valueb-->
<!--        FROM (-->
<!--                 SELECT (CASE-->
<!--                             WHEN App_Workgroup.Abbreviate IS NULL OR App_Workgroup.Abbreviate = ''-->
<!--                                 THEN App_Workgroup.GroupName-->
<!--                             ELSE App_Workgroup.Abbreviate END) AS NAME,-->
<!--                        Buy_Prepayments.Amount                  AS Amount-->
<!--                 FROM Buy_Prepayments-->
<!--                          INNER JOIN App_Workgroup ON Buy_Prepayments.Groupid = App_Workgroup.id-->
<!--                 WHERE DATE_FORMAT(BillDate, '%Y-%m') = DATE_FORMAT(now(), '%Y-%m')-->
<!--                   and Buy_Voucher.Tenantid = #{tid}-->
<!--                 UNION ALL-->
<!--                 SELECT (CASE-->
<!--                             WHEN App_Workgroup.Abbreviate IS NULL OR App_Workgroup.Abbreviate = ''-->
<!--                                 THEN App_Workgroup.GroupName-->
<!--                             ELSE App_Workgroup.Abbreviate END) AS NAME,-->
<!--                        Buy_Voucher.Amount                      AS valueb-->
<!--                 FROM Buy_Voucher-->
<!--                          INNER JOIN App_Workgroup ON Buy_Voucher.Groupid = App_Workgroup.id-->
<!--                 WHERE DATE_FORMAT(BillDate, '%Y-%m') = DATE_FORMAT(now(), '%Y-%m')-->
<!--                   and Buy_Voucher.Tenantid = #{tid}-->
<!--             ) Buy_Voucher-->
<!--        GROUP BY Buy_Voucher.NAME-->
<!--    </select>-->
<!--    <select id="getList" parameterType="java.lang.String"-->
<!--            resultType="java.util.Map">-->
<!--        SELECT Buy_PlanItem.*,-->
<!--               Buy_Plan.RefNo,-->
<!--               Buy_Plan.Lister,-->
<!--               Buy_Plan.Assessor,-->
<!--               Buy_Plan.id AS Mid,-->
<!--               Mat_Goods.GoodsName,-->
<!--               Mat_Goods.GoodsSpec,-->
<!--               Mat_Goods.GoodsUid,-->
<!--               Mat_Goods.GoodsUnit,-->
<!--               App_Workgroup.GroupName-->
<!--        FROM Buy_PlanItem-->
<!--                 LEFT OUTER JOIN App_Workgroup ON Buy_PlanItem.Groupid = App_Workgroup.id-->
<!--                 LEFT OUTER JOIN Mat_Goods ON Buy_PlanItem.Goodsid = Mat_Goods.id-->
<!--                 LEFT OUTER JOIN Buy_Plan ON Buy_PlanItem.Pid = Buy_Plan.id-->
<!--        WHERE 1 = 1-->
<!--          AND Buy_Plan.DisannulMark = 0-->
<!--          AND Buy_PlanItem.Quantity > Buy_PlanItem.BuyQty-->
<!--          AND DATE_FORMAT(Buy_Plan.BillPlanDate, '%Y-%m-%d') &lt; DATE_FORMAT(now(), '%Y-%m-%d')-->
<!--          AND Buy_PlanItem.Tenantid = #{tid}-->
<!--    </select>-->


<!--    <select id="getItemCountByMonth" parameterType="inks.common.core.domain.QueryParam"-->
<!--            resultType="inks.common.core.domain.ChartPojo">-->
<!--        SELECT count(Bus_MachiningItem.id)-->
<!--                      VALUEB-->
<!--                ,-->
<!--               '本月订单' NAME-->
<!--        FROM Bus_Machining-->
<!--                 LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id-->
<!--                 RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid-->
<!--        WHERE DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')-->
<!--          AND Bus_Machining.Tenantid = #{Tenantid}-->
<!--        UNION ALL-->
<!--        SELECT sum(Bus_MachiningItem.quantity)-->
<!--                      VALUEB-->
<!--                ,-->
<!--               '本月数量' NAME-->
<!--        FROM Bus_Machining-->
<!--                 LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id-->
<!--                 RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid-->
<!--        WHERE DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')-->
<!--          AND Bus_Machining.Tenantid = #{Tenantid}-->
<!--        UNION ALL-->
<!--        SELECT count(Bus_MachiningItem.id)-->
<!--                      VALUEB-->
<!--                ,-->
<!--               '本月完成' NAME-->
<!--        FROM Bus_Machining-->
<!--                 LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id-->
<!--                 RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid-->
<!--        WHERE Bus_MachiningItem.OutQuantity >= Bus_MachiningItem.Quantity-->
<!--          AND DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')-->
<!--          AND Bus_Machining.Tenantid = #{Tenantid}-->
<!--        UNION ALL-->
<!--        SELECT sum(Bus_MachiningItem.TaxAmount)-->
<!--                      VALUEB-->
<!--                ,-->
<!--               '本月金额' NAME-->
<!--        FROM Bus_Machining-->
<!--                 LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id-->
<!--                 RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid-->
<!--        WHERE DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')-->
<!--          AND Bus_Machining.Tenantid = #{Tenantid}-->
<!--        UNION ALL-->
<!--        SELECT count(Bus_MachiningItem.id)-->
<!--                      VALUEB-->
<!--                ,-->
<!--               '逾期交货' NAME-->
<!--        FROM Bus_Machining-->
<!--                 LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id-->
<!--                 RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid-->
<!--        WHERE 1 = 1-->
<!--          AND Bus_MachiningItem.OutQuantity >= Bus_MachiningItem.Quantity-->
<!--          AND DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')-->
<!--          AND Bus_Machining.Tenantid = #{Tenantid}-->
<!--          AND DATE_FORMAT(Bus_MachiningItem.Itemplandate, '%Y-%m') > DATE_FORMAT(CURDATE(), '%Y-%m')-->
<!--    </select>-->

</mapper>
