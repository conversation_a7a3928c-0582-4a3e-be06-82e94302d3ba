<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatInvenoteitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatInvenoteitemPojo">
        SELECT Mat_InveNoteItem.id,
               Mat_InveNoteItem.Pid,
               Mat_InveNoteItem.Inveid,
               Mat_InveNoteItem.Goodsid,
               Mat_InveNoteItem.ItemCode,
               Mat_InveNoteItem.ItemName,
               Mat_InveNoteItem.ItemSpec,
               Mat_InveNoteItem.ItemUnit,
               Mat_InveNoteItem.Quantity,
               Mat_InveNoteItem.Price,
               Mat_InveNoteItem.Amount,
               Mat_InveNoteItem.Location,
               Mat_InveNoteItem.BatchNo,
               Mat_InveNoteItem.PackSn,
               Mat_InveNoteItem.Skuid,
               Mat_InveNoteItem.ExpiDate,
               Mat_InveNoteItem.RowNum,
               Mat_InveNoteItem.EndUid,
               Mat_InveNoteItem.EndInUid,
               Mat_InveNoteItem.EndInDate,
               Mat_InveNoteItem.EndOutUid,
               Mat_InveNoteItem.EndOutDate,
               Mat_InveNoteItem.CurrQty,
               Mat_InveNoteItem.CurrAmt,
               Mat_InveNoteItem.OverFlowQty,
               Mat_InveNoteItem.OverFlowAmt,
               Mat_InveNoteItem.FinishQty,
               Mat_InveNoteItem.FinishAmt,
               Mat_InveNoteItem.Remark,
               Mat_InveNoteItem.Custom1,
               Mat_InveNoteItem.Custom2,
               Mat_InveNoteItem.Custom3,
               Mat_InveNoteItem.Custom4,
               Mat_InveNoteItem.Custom5,
               Mat_InveNoteItem.Custom6,
               Mat_InveNoteItem.Custom7,
               Mat_InveNoteItem.Custom8,
               Mat_InveNoteItem.Custom9,
               Mat_InveNoteItem.Custom10,
               Mat_InveNoteItem.Tenantid,
               Mat_InveNoteItem.TenantidName,
               Mat_InveNoteItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_InveNoteItem
                 LEFT JOIN Mat_Goods ON Mat_InveNoteItem.Goodsid = Mat_Goods.id
        where Mat_InveNoteItem.id = #{key}
          and Mat_InveNoteItem.Tenantid = #{tid}
    </select>
    <sql id="selectMatInvenoteitemVo">
        SELECT Mat_InveNoteItem.id,
               Mat_InveNoteItem.Pid,
               Mat_InveNoteItem.Inveid,
               Mat_InveNoteItem.Goodsid,
               Mat_InveNoteItem.ItemCode,
               Mat_InveNoteItem.ItemName,
               Mat_InveNoteItem.ItemSpec,
               Mat_InveNoteItem.ItemUnit,
               Mat_InveNoteItem.Quantity,
               Mat_InveNoteItem.Price,
               Mat_InveNoteItem.Amount,
               Mat_InveNoteItem.Location,
               Mat_InveNoteItem.BatchNo,
               Mat_InveNoteItem.PackSn,
               Mat_InveNoteItem.Skuid,
               Mat_InveNoteItem.ExpiDate,
               Mat_InveNoteItem.RowNum,
               Mat_InveNoteItem.EndUid,
               Mat_InveNoteItem.EndInUid,
               Mat_InveNoteItem.EndInDate,
               Mat_InveNoteItem.EndOutUid,
               Mat_InveNoteItem.EndOutDate,
               Mat_InveNoteItem.CurrQty,
               Mat_InveNoteItem.CurrAmt,
               Mat_InveNoteItem.OverFlowQty,
               Mat_InveNoteItem.OverFlowAmt,
               Mat_InveNoteItem.FinishQty,
               Mat_InveNoteItem.FinishAmt,
               Mat_InveNoteItem.Remark,
               Mat_InveNoteItem.Custom1,
               Mat_InveNoteItem.Custom2,
               Mat_InveNoteItem.Custom3,
               Mat_InveNoteItem.Custom4,
               Mat_InveNoteItem.Custom5,
               Mat_InveNoteItem.Custom6,
               Mat_InveNoteItem.Custom7,
               Mat_InveNoteItem.Custom8,
               Mat_InveNoteItem.Custom9,
               Mat_InveNoteItem.Custom10,
               Mat_InveNoteItem.Tenantid,
               Mat_InveNoteItem.TenantidName,
               Mat_InveNoteItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_InveNoteItem
                 LEFT JOIN Mat_Goods ON Mat_InveNoteItem.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatInvenoteitemPojo">
        <include refid="selectMatInvenoteitemVo"/>
        where 1 = 1 and Mat_InveNoteItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_InveNoteItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Mat_InveNoteItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.inveid != null and SearchPojo.inveid != ''">
            and Mat_InveNoteItem.inveid like concat('%', #{SearchPojo.inveid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_InveNoteItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Mat_InveNoteItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Mat_InveNoteItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Mat_InveNoteItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Mat_InveNoteItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Mat_InveNoteItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Mat_InveNoteItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.packsn != null and SearchPojo.packsn != ''">
            and Mat_InveNoteItem.packsn like concat('%', #{SearchPojo.packsn}, '%')
        </if>
        <if test="SearchPojo.skuid != null and SearchPojo.skuid != ''">
            and Mat_InveNoteItem.skuid like concat('%', #{SearchPojo.skuid}, '%')
        </if>
        <if test="SearchPojo.enduid != null and SearchPojo.enduid != ''">
            and Mat_InveNoteItem.enduid like concat('%', #{SearchPojo.enduid}, '%')
        </if>
        <if test="SearchPojo.endinuid != null and SearchPojo.endinuid != ''">
            and Mat_InveNoteItem.endinuid like concat('%', #{SearchPojo.endinuid}, '%')
        </if>
        <if test="SearchPojo.endoutuid != null and SearchPojo.endoutuid != ''">
            and Mat_InveNoteItem.endoutuid like concat('%', #{SearchPojo.endoutuid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Mat_InveNoteItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_InveNoteItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_InveNoteItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_InveNoteItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_InveNoteItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_InveNoteItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_InveNoteItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_InveNoteItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_InveNoteItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_InveNoteItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_InveNoteItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantidname != null and SearchPojo.tenantidname != ''">
            and Mat_InveNoteItem.tenantidname like concat('%', #{SearchPojo.tenantidname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Mat_InveNoteItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.inveid != null and SearchPojo.inveid != ''">
                or Mat_InveNoteItem.Inveid like concat('%', #{SearchPojo.inveid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_InveNoteItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Mat_InveNoteItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Mat_InveNoteItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Mat_InveNoteItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Mat_InveNoteItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Mat_InveNoteItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Mat_InveNoteItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.packsn != null and SearchPojo.packsn != ''">
                or Mat_InveNoteItem.PackSn like concat('%', #{SearchPojo.packsn}, '%')
            </if>
            <if test="SearchPojo.skuid != null and SearchPojo.skuid != ''">
                or Mat_InveNoteItem.Skuid like concat('%', #{SearchPojo.skuid}, '%')
            </if>
            <if test="SearchPojo.enduid != null and SearchPojo.enduid != ''">
                or Mat_InveNoteItem.EndUid like concat('%', #{SearchPojo.enduid}, '%')
            </if>
            <if test="SearchPojo.endinuid != null and SearchPojo.endinuid != ''">
                or Mat_InveNoteItem.EndInUid like concat('%', #{SearchPojo.endinuid}, '%')
            </if>
            <if test="SearchPojo.endoutuid != null and SearchPojo.endoutuid != ''">
                or Mat_InveNoteItem.EndOutUid like concat('%', #{SearchPojo.endoutuid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Mat_InveNoteItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_InveNoteItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_InveNoteItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_InveNoteItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_InveNoteItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_InveNoteItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_InveNoteItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_InveNoteItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_InveNoteItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_InveNoteItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_InveNoteItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantidname != null and SearchPojo.tenantidname != ''">
                or Mat_InveNoteItem.TenantidName like concat('%', #{SearchPojo.tenantidname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.MatInvenoteitemPojo">
        SELECT Mat_InveNoteItem.id,
               Mat_InveNoteItem.Pid,
               Mat_InveNoteItem.Inveid,
               Mat_InveNoteItem.Goodsid,
               Mat_InveNoteItem.ItemCode,
               Mat_InveNoteItem.ItemName,
               Mat_InveNoteItem.ItemSpec,
               Mat_InveNoteItem.ItemUnit,
               Mat_InveNoteItem.Quantity,
               Mat_InveNoteItem.Price,
               Mat_InveNoteItem.Amount,
               Mat_InveNoteItem.Location,
               Mat_InveNoteItem.BatchNo,
               Mat_InveNoteItem.PackSn,
               Mat_InveNoteItem.Skuid,
               Mat_InveNoteItem.ExpiDate,
               Mat_InveNoteItem.RowNum,
               Mat_InveNoteItem.EndUid,
               Mat_InveNoteItem.EndInUid,
               Mat_InveNoteItem.EndInDate,
               Mat_InveNoteItem.EndOutUid,
               Mat_InveNoteItem.EndOutDate,
               Mat_InveNoteItem.CurrQty,
               Mat_InveNoteItem.CurrAmt,
               Mat_InveNoteItem.OverFlowQty,
               Mat_InveNoteItem.OverFlowAmt,
               Mat_InveNoteItem.FinishQty,
               Mat_InveNoteItem.FinishAmt,
               Mat_InveNoteItem.Remark,
               Mat_InveNoteItem.Custom1,
               Mat_InveNoteItem.Custom2,
               Mat_InveNoteItem.Custom3,
               Mat_InveNoteItem.Custom4,
               Mat_InveNoteItem.Custom5,
               Mat_InveNoteItem.Custom6,
               Mat_InveNoteItem.Custom7,
               Mat_InveNoteItem.Custom8,
               Mat_InveNoteItem.Custom9,
               Mat_InveNoteItem.Custom10,
               Mat_InveNoteItem.Tenantid,
               Mat_InveNoteItem.TenantidName,
               Mat_InveNoteItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_InveNoteItem
                 LEFT JOIN Mat_Goods ON Mat_InveNoteItem.Goodsid = Mat_Goods.id
        where Mat_InveNoteItem.Pid = #{Pid}
          and Mat_InveNoteItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_InveNoteItem(id, Pid, Inveid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, Price,
                                     Amount,
                                     Location, BatchNo, PackSn, Skuid, ExpiDate, RowNum, EndUid, EndInUid, EndInDate,
                                     EndOutUid, EndOutDate, CurrQty, CurrAmt, OverFlowQty, OverFlowAmt, FinishQty,
                                     FinishAmt, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7,
                                     Custom8, Custom9, Custom10, Tenantid, TenantidName, Revision)
        values (#{id}, #{pid}, #{inveid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity},
                #{price},
                #{amount}, #{location}, #{batchno}, #{packsn}, #{skuid}, #{expidate}, #{rownum}, #{enduid}, #{endinuid},
                #{endindate}, #{endoutuid}, #{endoutdate}, #{currqty}, #{curramt}, #{overflowqty}, #{overflowamt},
                #{finishqty}, #{finishamt}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
                #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantidname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_InveNoteItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="inveid != null ">
                Inveid = #{inveid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="packsn != null ">
                PackSn = #{packsn},
            </if>
            <if test="skuid != null ">
                Skuid = #{skuid},
            </if>
            <if test="expidate != null">
                ExpiDate = #{expidate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="enduid != null ">
                EndUid = #{enduid},
            </if>
            <if test="endinuid != null ">
                EndInUid = #{endinuid},
            </if>
            <if test="endindate != null">
                EndInDate = #{endindate},
            </if>
            <if test="endoutuid != null ">
                EndOutUid = #{endoutuid},
            </if>
            <if test="endoutdate != null">
                EndOutDate = #{endoutdate},
            </if>
            <if test="currqty != null">
                CurrQty = #{currqty},
            </if>
            <if test="curramt != null">
                CurrAmt = #{curramt},
            </if>
            <if test="overflowqty != null">
                OverFlowQty = #{overflowqty},
            </if>
            <if test="overflowamt != null">
                OverFlowAmt = #{overflowamt},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="finishamt != null">
                FinishAmt = #{finishamt},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="tenantidname != null ">
                TenantidName = #{tenantidname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_InveNoteItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询List-->
    <select id="getListByAlter" resultType="inks.service.sa.som.domain.pojo.MatInvenoteitemPojo">
        SELECT Mat_InveNoteItem.id,
               Mat_InveNoteItem.Pid,
               Mat_InveNoteItem.Inveid,
               Mat_InveNoteItem.Goodsid,
               Mat_InveNoteItem.ItemCode,
               Mat_InveNoteItem.ItemName,
               Mat_InveNoteItem.ItemSpec,
               Mat_InveNoteItem.ItemUnit,
               Mat_InveNoteItem.Quantity,
               Mat_InveNoteItem.Price,
               Mat_InveNoteItem.Amount,
               Mat_InveNoteItem.Location,
               Mat_InveNoteItem.BatchNo,
               Mat_InveNoteItem.PackSn,
               Mat_InveNoteItem.Skuid,
               Mat_InveNoteItem.ExpiDate,
               Mat_InveNoteItem.RowNum,
               Mat_InveNoteItem.EndUid,
               Mat_InveNoteItem.EndInUid,
               Mat_InveNoteItem.EndInDate,
               Mat_InveNoteItem.EndOutUid,
               Mat_InveNoteItem.EndOutDate,
               Mat_InveNoteItem.CurrQty,
               Mat_InveNoteItem.CurrAmt,
               Mat_InveNoteItem.OverFlowQty,
               Mat_InveNoteItem.OverFlowAmt,
               Mat_InveNoteItem.FinishQty,
               Mat_InveNoteItem.FinishAmt,
               Mat_InveNoteItem.Remark,
               Mat_InveNoteItem.Custom1,
               Mat_InveNoteItem.Custom2,
               Mat_InveNoteItem.Custom3,
               Mat_InveNoteItem.Custom4,
               Mat_InveNoteItem.Custom5,
               Mat_InveNoteItem.Custom6,
               Mat_InveNoteItem.Custom7,
               Mat_InveNoteItem.Custom8,
               Mat_InveNoteItem.Custom9,
               Mat_InveNoteItem.Custom10,
               Mat_InveNoteItem.Tenantid,
               Mat_InveNoteItem.TenantidName,
               Mat_InveNoteItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_InveNoteItem
                 LEFT JOIN Mat_Goods ON Mat_InveNoteItem.Goodsid = Mat_Goods.id
        where Mat_InveNoteItem.Pid = #{Pid}
          and Mat_InveNoteItem.Tenantid = #{tid}
          and Mat_InveNoteItem.Quantity != Mat_InveNoteItem.CurrQty
        order by RowNum
    </select>

    <select id="getItemListByIds" resultType="inks.service.sa.som.domain.pojo.MatInvenoteitemPojo">
        <include refid="selectMatInvenoteitemVo"/>
        where Mat_InveNoteItem.Pid = #{pid}
        and Mat_InveNoteItem.id in (${ids})
        and Mat_InveNoteItem.Tenantid = #{tid}
        order by RowNum
    </select>
</mapper>

