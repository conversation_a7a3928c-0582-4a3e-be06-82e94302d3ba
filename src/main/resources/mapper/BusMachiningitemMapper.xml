<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BusMachiningitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BusMachiningitemPojo">
        <include refid="selectBusMachiningitemVo"/>
        where Bus_MachiningItem.id = #{key}
          and Bus_MachiningItem.Tenantid = #{tid}
    </select>
    <sql id="selectBusMachiningitemVo">
        SELECT Bus_MachiningItem.id,
               Bus_MachiningItem.Pid,
               Bus_MachiningItem.MachBatch,
               Bus_MachiningItem.Goodsid,
               Bus_MachiningItem.Quantity,
               Bus_MachiningItem.TaxPrice,
               Bus_MachiningItem.TaxAmount,
               Bus_MachiningItem.ItemTaxrate,
               Bus_MachiningItem.TaxTotal,
               Bus_MachiningItem.Price,
               Bus_MachiningItem.Amount,
               Bus_MachiningItem.ItemOrgDate,
               Bus_MachiningItem.ItemPlanDate,
               Bus_MachiningItem.WkQty,
               Bus_MachiningItem.StoQty,
               Bus_MachiningItem.RowNum,
               Bus_MachiningItem.Remark,
               Bus_MachiningItem.EngStateText,
               Bus_MachiningItem.EngStateDate,
               Bus_MachiningItem.WkStateText,
               Bus_MachiningItem.WkStateDate,
               Bus_MachiningItem.BusStateText,
               Bus_MachiningItem.BusStateDate,
               Bus_MachiningItem.BuyQuantity,
               Bus_MachiningItem.WkQuantity,
               Bus_MachiningItem.InQuantity,
               Bus_MachiningItem.PickQty,
               Bus_MachiningItem.FinishQty,
               Bus_MachiningItem.OutQuantity,
               Bus_MachiningItem.OutSecQty,
               Bus_MachiningItem.EditionInfo,
               Bus_MachiningItem.ItemCompDate,
               Bus_MachiningItem.VirtualItem,
               Bus_MachiningItem.Closed,
               Bus_MachiningItem.StdPrice,
               Bus_MachiningItem.StdAmount,
               Bus_MachiningItem.Rebate,
               Bus_MachiningItem.MrpUid,
               Bus_MachiningItem.Mrpid,
               Bus_MachiningItem.MaxQty,
               Bus_MachiningItem.Location,
               Bus_MachiningItem.BatchNo,
               Bus_MachiningItem.DisannulMark,
               Bus_MachiningItem.WipUsed,
               Bus_MachiningItem.WkWpid,
               Bus_MachiningItem.WkWpCode,
               Bus_MachiningItem.WkWpName,
               Bus_MachiningItem.WkRowNum,
               Bus_MachiningItem.OrderCostUid,
               Bus_MachiningItem.OrderCostItemid,
               Bus_MachiningItem.QuotUid,
               Bus_MachiningItem.QuotItemid,
               Bus_MachiningItem.BomType,
               Bus_MachiningItem.Bomid,
               Bus_MachiningItem.BomUid,
               Bus_MachiningItem.BomState,
               Bus_MachiningItem.AttributeJson,
               Bus_MachiningItem.AttributeStr,
               Bus_MachiningItem.MachType,
               Bus_MachiningItem.ReorderMark,
               Bus_MachiningItem.MatCode,
               Bus_MachiningItem.MatUsed,
               Bus_MachiningItem.Matid,
               Bus_MachiningItem.CostItemJson,
               Bus_MachiningItem.CostGroupJson,
               Bus_MachiningItem.MatCostAmt,
               Bus_MachiningItem.LaborCostAmt,
               Bus_MachiningItem.DirectCostAmt,
               Bus_MachiningItem.IndirectCostAmt,
               Bus_MachiningItem.SourceType,
               Bus_MachiningItem.CostBudgetAmt,
               Bus_MachiningItem.Specid,
               Bus_MachiningItem.SpecUid,
               Bus_MachiningItem.SpecState,
               Bus_MachiningItem.MatBuyQty,
               Bus_MachiningItem.MatUseQty,
               Bus_MachiningItem.AvgFirstAmt,
               Bus_MachiningItem.AvgLastAmt,
               Bus_MachiningItem.AvgInvoAmt,
               Bus_MachiningItem.InvoQty,
               Bus_MachiningItem.InvoClosed,
               Bus_MachiningItem.MainPlanQty,
               Bus_MachiningItem.MainPlanClosed,
               Bus_MachiningItem.WkMergeMark,
               Bus_MachiningItem.WkMergeItemid,
               Bus_MachiningItem.BpMergeMark,
               Bus_MachiningItem.BpMergeItemid,
               Bus_MachiningItem.InvoAmt,
               Bus_MachiningItem.SubPrice,
               Bus_MachiningItem.SubAmount,
               Bus_MachiningItem.Custom1,
               Bus_MachiningItem.Custom2,
               Bus_MachiningItem.Custom3,
               Bus_MachiningItem.Custom4,
               Bus_MachiningItem.Custom5,
               Bus_MachiningItem.Custom6,
               Bus_MachiningItem.Custom7,
               Bus_MachiningItem.Custom8,
               Bus_MachiningItem.Custom9,
               Bus_MachiningItem.Custom10,
               Bus_MachiningItem.Custom11,
               Bus_MachiningItem.Custom12,
               Bus_MachiningItem.Custom13,
               Bus_MachiningItem.Custom14,
               Bus_MachiningItem.Custom15,
               Bus_MachiningItem.Custom16,
               Bus_MachiningItem.Custom17,
               Bus_MachiningItem.Custom18,
               Bus_MachiningItem.Tenantid,
               Bus_MachiningItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Bus_MachiningItem
                 LEFT JOIN Mat_Goods ON Bus_MachiningItem.Goodsid = Mat_Goods.id
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.BusMachiningitemPojo">
        <include refid="selectBusMachiningitemVo"/>
        where Bus_MachiningItem.Pid = #{Pid}
        order by RowNum
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusMachiningitemPojo">
        <include refid="selectBusMachiningitemVo"/>
        where 1 = 1 and Bus_MachiningItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_MachiningItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Bus_MachiningItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.machbatch != null and SearchPojo.machbatch != ''">
            and Bus_MachiningItem.machbatch like concat('%', #{SearchPojo.machbatch}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Bus_MachiningItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Bus_MachiningItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.engstatetext != null and SearchPojo.engstatetext != ''">
            and Bus_MachiningItem.engstatetext like concat('%', #{SearchPojo.engstatetext}, '%')
        </if>
        <if test="SearchPojo.wkstatetext != null and SearchPojo.wkstatetext != ''">
            and Bus_MachiningItem.wkstatetext like concat('%', #{SearchPojo.wkstatetext}, '%')
        </if>
        <if test="SearchPojo.busstatetext != null and SearchPojo.busstatetext != ''">
            and Bus_MachiningItem.busstatetext like concat('%', #{SearchPojo.busstatetext}, '%')
        </if>
        <if test="SearchPojo.editioninfo != null and SearchPojo.editioninfo != ''">
            and Bus_MachiningItem.editioninfo like concat('%', #{SearchPojo.editioninfo}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Bus_MachiningItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpid != null and SearchPojo.mrpid != ''">
            and Bus_MachiningItem.mrpid like concat('%', #{SearchPojo.mrpid}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Bus_MachiningItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Bus_MachiningItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.wkwpid != null and SearchPojo.wkwpid != ''">
            and Bus_MachiningItem.wkwpid like concat('%', #{SearchPojo.wkwpid}, '%')
        </if>
        <if test="SearchPojo.wkwpcode != null and SearchPojo.wkwpcode != ''">
            and Bus_MachiningItem.wkwpcode like concat('%', #{SearchPojo.wkwpcode}, '%')
        </if>
        <if test="SearchPojo.wkwpname != null and SearchPojo.wkwpname != ''">
            and Bus_MachiningItem.wkwpname like concat('%', #{SearchPojo.wkwpname}, '%')
        </if>
        <if test="SearchPojo.ordercostuid != null and SearchPojo.ordercostuid != ''">
            and Bus_MachiningItem.ordercostuid like concat('%', #{SearchPojo.ordercostuid}, '%')
        </if>
        <if test="SearchPojo.ordercostitemid != null and SearchPojo.ordercostitemid != ''">
            and Bus_MachiningItem.ordercostitemid like concat('%', #{SearchPojo.ordercostitemid}, '%')
        </if>
        <if test="SearchPojo.quotuid != null and SearchPojo.quotuid != ''">
            and Bus_MachiningItem.quotuid like concat('%', #{SearchPojo.quotuid}, '%')
        </if>
        <if test="SearchPojo.quotitemid != null and SearchPojo.quotitemid != ''">
            and Bus_MachiningItem.quotitemid like concat('%', #{SearchPojo.quotitemid}, '%')
        </if>
        <if test="SearchPojo.bomid != null and SearchPojo.bomid != ''">
            and Bus_MachiningItem.bomid like concat('%', #{SearchPojo.bomid}, '%')
        </if>
        <if test="SearchPojo.bomuid != null and SearchPojo.bomuid != ''">
            and Bus_MachiningItem.bomuid like concat('%', #{SearchPojo.bomuid}, '%')
        </if>
        <if test="SearchPojo.bomstate != null and SearchPojo.bomstate != ''">
            and Bus_MachiningItem.bomstate like concat('%', #{SearchPojo.bomstate}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Bus_MachiningItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.attributestr != null and SearchPojo.attributestr != ''">
            and Bus_MachiningItem.attributestr like concat('%', #{SearchPojo.attributestr}, '%')
        </if>
        <if test="SearchPojo.machtype != null and SearchPojo.machtype != ''">
            and Bus_MachiningItem.machtype like concat('%', #{SearchPojo.machtype}, '%')
        </if>
        <if test="SearchPojo.matcode != null and SearchPojo.matcode != ''">
            and Bus_MachiningItem.matcode like concat('%', #{SearchPojo.matcode}, '%')
        </if>
        <if test="SearchPojo.specid != null and SearchPojo.specid != ''">
            and Bus_MachiningItem.Specid like concat('%', #{SearchPojo.specid}, '%')
        </if>
        <if test="SearchPojo.specuid != null and SearchPojo.specuid != ''">
            and Bus_MachiningItem.SpecUid like concat('%', #{SearchPojo.specuid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_MachiningItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_MachiningItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_MachiningItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_MachiningItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_MachiningItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_MachiningItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_MachiningItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_MachiningItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_MachiningItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_MachiningItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.custom11 != null and SearchPojo.custom11 != ''">
            and Bus_MachiningItem.custom11 like concat('%', #{SearchPojo.custom11}, '%')
        </if>
        <if test="SearchPojo.custom12 != null and SearchPojo.custom12 != ''">
            and Bus_MachiningItem.custom12 like concat('%', #{SearchPojo.custom12}, '%')
        </if>
        <if test="SearchPojo.custom13 != null and SearchPojo.custom13 != ''">
            and Bus_MachiningItem.custom13 like concat('%', #{SearchPojo.custom13}, '%')
        </if>
        <if test="SearchPojo.custom14 != null and SearchPojo.custom14 != ''">
            and Bus_MachiningItem.custom14 like concat('%', #{SearchPojo.custom14}, '%')
        </if>
        <if test="SearchPojo.custom15 != null and SearchPojo.custom15 != ''">
            and Bus_MachiningItem.custom15 like concat('%', #{SearchPojo.custom15}, '%')
        </if>
        <if test="SearchPojo.custom16 != null and SearchPojo.custom16 != ''">
            and Bus_MachiningItem.custom16 like concat('%', #{SearchPojo.custom16}, '%')
        </if>
        <if test="SearchPojo.custom17 != null and SearchPojo.custom17 != ''">
            and Bus_MachiningItem.custom17 like concat('%', #{SearchPojo.custom17}, '%')
        </if>
        <if test="SearchPojo.custom18 != null and SearchPojo.custom18 != ''">
            and Bus_MachiningItem.custom18 like concat('%', #{SearchPojo.custom18}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Bus_MachiningItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.machbatch != null and SearchPojo.machbatch != ''">
                or Bus_MachiningItem.MachBatch like concat('%', #{SearchPojo.machbatch}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Bus_MachiningItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Bus_MachiningItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.engstatetext != null and SearchPojo.engstatetext != ''">
                or Bus_MachiningItem.EngStateText like concat('%', #{SearchPojo.engstatetext}, '%')
            </if>
            <if test="SearchPojo.wkstatetext != null and SearchPojo.wkstatetext != ''">
                or Bus_MachiningItem.WkStateText like concat('%', #{SearchPojo.wkstatetext}, '%')
            </if>
            <if test="SearchPojo.busstatetext != null and SearchPojo.busstatetext != ''">
                or Bus_MachiningItem.BusStateText like concat('%', #{SearchPojo.busstatetext}, '%')
            </if>
            <if test="SearchPojo.editioninfo != null and SearchPojo.editioninfo != ''">
                or Bus_MachiningItem.EditionInfo like concat('%', #{SearchPojo.editioninfo}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Bus_MachiningItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpid != null and SearchPojo.mrpid != ''">
                or Bus_MachiningItem.Mrpid like concat('%', #{SearchPojo.mrpid}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Bus_MachiningItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Bus_MachiningItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.wkwpid != null and SearchPojo.wkwpid != ''">
                or Bus_MachiningItem.WkWpid like concat('%', #{SearchPojo.wkwpid}, '%')
            </if>
            <if test="SearchPojo.wkwpcode != null and SearchPojo.wkwpcode != ''">
                or Bus_MachiningItem.WkWpCode like concat('%', #{SearchPojo.wkwpcode}, '%')
            </if>
            <if test="SearchPojo.wkwpname != null and SearchPojo.wkwpname != ''">
                or Bus_MachiningItem.WkWpName like concat('%', #{SearchPojo.wkwpname}, '%')
            </if>
            <if test="SearchPojo.ordercostuid != null and SearchPojo.ordercostuid != ''">
                or Bus_MachiningItem.OrderCostUid like concat('%', #{SearchPojo.ordercostuid}, '%')
            </if>
            <if test="SearchPojo.ordercostitemid != null and SearchPojo.ordercostitemid != ''">
                or Bus_MachiningItem.OrderCostItemid like concat('%', #{SearchPojo.ordercostitemid}, '%')
            </if>
            <if test="SearchPojo.quotuid != null and SearchPojo.quotuid != ''">
                or Bus_MachiningItem.QuotUid like concat('%', #{SearchPojo.quotuid}, '%')
            </if>
            <if test="SearchPojo.quotitemid != null and SearchPojo.quotitemid != ''">
                or Bus_MachiningItem.QuotItemid like concat('%', #{SearchPojo.quotitemid}, '%')
            </if>
            <if test="SearchPojo.bomid != null and SearchPojo.bomid != ''">
                or Bus_MachiningItem.Bomid like concat('%', #{SearchPojo.bomid}, '%')
            </if>
            <if test="SearchPojo.bomuid != null and SearchPojo.bomuid != ''">
                or Bus_MachiningItem.BomUid like concat('%', #{SearchPojo.bomuid}, '%')
            </if>
            <if test="SearchPojo.bomstate != null and SearchPojo.bomstate != ''">
                or Bus_MachiningItem.BomState like concat('%', #{SearchPojo.bomstate}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Bus_MachiningItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.attributestr != null and SearchPojo.attributestr != ''">
                or Bus_MachiningItem.AttributeStr like concat('%', #{SearchPojo.attributestr}, '%')
            </if>
            <if test="SearchPojo.machtype != null and SearchPojo.machtype != ''">
                or Bus_MachiningItem.MachType like concat('%', #{SearchPojo.machtype}, '%')
            </if>
            <if test="SearchPojo.matcode != null and SearchPojo.matcode != ''">
                or Bus_MachiningItem.MatCode like concat('%', #{SearchPojo.matcode}, '%')
            </if>
            <if test="SearchPojo.specid != null and SearchPojo.specid != ''">
                or Bus_MachiningItem.Specid like concat('%', #{SearchPojo.specid}, '%')
            </if>
            <if test="SearchPojo.specuid != null and SearchPojo.specuid != ''">
                or Bus_MachiningItem.SpecUid like concat('%', #{SearchPojo.specuid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Bus_MachiningItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Bus_MachiningItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Bus_MachiningItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Bus_MachiningItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Bus_MachiningItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Bus_MachiningItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Bus_MachiningItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Bus_MachiningItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Bus_MachiningItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Bus_MachiningItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.custom11 != null and SearchPojo.custom11 != ''">
                or Bus_MachiningItem.Custom11 like concat('%', #{SearchPojo.custom11}, '%')
            </if>
            <if test="SearchPojo.custom12 != null and SearchPojo.custom12 != ''">
                or Bus_MachiningItem.Custom12 like concat('%', #{SearchPojo.custom12}, '%')
            </if>
            <if test="SearchPojo.custom13 != null and SearchPojo.custom13 != ''">
                or Bus_MachiningItem.Custom13 like concat('%', #{SearchPojo.custom13}, '%')
            </if>
            <if test="SearchPojo.custom14 != null and SearchPojo.custom14 != ''">
                or Bus_MachiningItem.Custom14 like concat('%', #{SearchPojo.custom14}, '%')
            </if>
            <if test="SearchPojo.custom15 != null and SearchPojo.custom15 != ''">
                or Bus_MachiningItem.Custom15 like concat('%', #{SearchPojo.custom15}, '%')
            </if>
            <if test="SearchPojo.custom16 != null and SearchPojo.custom16 != ''">
                or Bus_MachiningItem.Custom16 like concat('%', #{SearchPojo.custom16}, '%')
            </if>
            <if test="SearchPojo.custom17 != null and SearchPojo.custom17 != ''">
                or Bus_MachiningItem.Custom17 like concat('%', #{SearchPojo.custom17}, '%')
            </if>
            <if test="SearchPojo.custom18 != null and SearchPojo.custom18 != ''">
                or Bus_MachiningItem.Custom18 like concat('%', #{SearchPojo.custom18}, '%')
            </if>
        </trim>
    </sql>


    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_MachiningItem(id, Pid, MachBatch, Goodsid, Quantity, TaxPrice, TaxAmount, ItemTaxrate, TaxTotal, Price, Amount, ItemOrgDate, ItemPlanDate, WkQty, StoQty, RowNum, Remark, EngStateText, EngStateDate, WkStateText, WkStateDate, BusStateText, BusStateDate, BuyQuantity, WkQuantity, InQuantity, PickQty, FinishQty, OutQuantity, OutSecQty, EditionInfo, ItemCompDate, VirtualItem, Closed, StdPrice, StdAmount, Rebate, MrpUid, Mrpid, MaxQty, Location, BatchNo, DisannulMark, WipUsed, WkWpid, WkWpCode, WkWpName, WkRowNum, OrderCostUid, OrderCostItemid, QuotUid, QuotItemid, BomType, Bomid, BomUid, BomState, AttributeJson, AttributeStr, MachType, ReorderMark, MatCode, MatUsed, Matid, CostItemJson, CostGroupJson, MatCostAmt, LaborCostAmt, DirectCostAmt, IndirectCostAmt, SourceType, AttaCount, CostBudgetAmt, Specid, SpecUid, SpecState, MatBuyQty, MatUseQty, AvgFirstAmt, AvgLastAmt, AvgInvoAmt, InvoQty, InvoClosed, MainPlanQty, MainPlanClosed, WkMergeMark, WkMergeItemid, BpMergeMark, BpMergeItemid, InvoAmt, SubPrice, SubAmount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Custom11, Custom12, Custom13, Custom14, Custom15, Custom16, Custom17, Custom18, Tenantid, Revision)
        values (#{id}, #{pid}, #{machbatch}, #{goodsid}, #{quantity}, #{taxprice}, #{taxamount}, #{itemtaxrate}, #{taxtotal}, #{price}, #{amount}, #{itemorgdate}, #{itemplandate}, #{wkqty}, #{stoqty}, #{rownum}, #{remark}, #{engstatetext}, #{engstatedate}, #{wkstatetext}, #{wkstatedate}, #{busstatetext}, #{busstatedate}, #{buyquantity}, #{wkquantity}, #{inquantity}, #{pickqty}, #{finishqty}, #{outquantity}, #{outsecqty}, #{editioninfo}, #{itemcompdate}, #{virtualitem}, #{closed}, #{stdprice}, #{stdamount}, #{rebate}, #{mrpuid}, #{mrpid}, #{maxqty}, #{location}, #{batchno}, #{disannulmark}, #{wipused}, #{wkwpid}, #{wkwpcode}, #{wkwpname}, #{wkrownum}, #{ordercostuid}, #{ordercostitemid}, #{quotuid}, #{quotitemid}, #{bomtype}, #{bomid}, #{bomuid}, #{bomstate}, #{attributejson}, #{attributestr}, #{machtype}, #{reordermark}, #{matcode}, #{matused}, #{matid}, #{costitemjson}, #{costgroupjson}, #{matcostamt}, #{laborcostamt}, #{directcostamt}, #{indirectcostamt}, #{sourcetype}, #{attacount}, #{costbudgetamt}, #{specid}, #{specuid}, #{specstate}, #{matbuyqty}, #{matuseqty}, #{avgfirstamt}, #{avglastamt}, #{avginvoamt}, #{invoqty}, #{invoclosed}, #{mainplanqty}, #{mainplanclosed}, #{wkmergemark}, #{wkmergeitemid}, #{bpmergemark}, #{bpmergeitemid}, #{invoamt}, #{subprice}, #{subamount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{custom11}, #{custom12}, #{custom13}, #{custom14}, #{custom15}, #{custom16}, #{custom17}, #{custom18}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_MachiningItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="machbatch != null ">
                MachBatch = #{machbatch},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="itemorgdate != null">
                ItemOrgDate = #{itemorgdate},
            </if>
            <if test="itemplandate != null">
                ItemPlanDate = #{itemplandate},
            </if>
            <if test="wkqty != null">
                WkQty = #{wkqty},
            </if>
            <if test="stoqty != null">
                StoQty = #{stoqty},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="engstatetext != null ">
                EngStateText = #{engstatetext},
            </if>
            <if test="engstatedate != null">
                EngStateDate = #{engstatedate},
            </if>
            <if test="wkstatetext != null ">
                WkStateText = #{wkstatetext},
            </if>
            <if test="wkstatedate != null">
                WkStateDate = #{wkstatedate},
            </if>
            <if test="busstatetext != null ">
                BusStateText = #{busstatetext},
            </if>
            <if test="busstatedate != null">
                BusStateDate = #{busstatedate},
            </if>
            <if test="buyquantity != null">
                BuyQuantity = #{buyquantity},
            </if>
            <if test="wkquantity != null">
                WkQuantity = #{wkquantity},
            </if>
            <if test="inquantity != null">
                InQuantity = #{inquantity},
            </if>
            <if test="pickqty != null">
                PickQty = #{pickqty},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="outquantity != null">
                OutQuantity = #{outquantity},
            </if>
            <if test="outsecqty != null">
                OutSecQty = #{outsecqty},
            </if>
            <if test="editioninfo != null ">
                EditionInfo = #{editioninfo},
            </if>
            <if test="itemcompdate != null">
                ItemCompDate = #{itemcompdate},
            </if>
            <if test="virtualitem != null">
                VirtualItem = #{virtualitem},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="stdprice != null">
                StdPrice = #{stdprice},
            </if>
            <if test="stdamount != null">
                StdAmount = #{stdamount},
            </if>
            <if test="rebate != null">
                Rebate = #{rebate},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpid != null ">
                Mrpid = #{mrpid},
            </if>
            <if test="maxqty != null">
                MaxQty = #{maxqty},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="wipused != null">
                WipUsed = #{wipused},
            </if>
            <if test="wkwpid != null ">
                WkWpid = #{wkwpid},
            </if>
            <if test="wkwpcode != null ">
                WkWpCode = #{wkwpcode},
            </if>
            <if test="wkwpname != null ">
                WkWpName = #{wkwpname},
            </if>
            <if test="wkrownum != null">
                WkRowNum = #{wkrownum},
            </if>
            <if test="ordercostuid != null ">
                OrderCostUid = #{ordercostuid},
            </if>
            <if test="ordercostitemid != null ">
                OrderCostItemid = #{ordercostitemid},
            </if>
            <if test="quotuid != null ">
                QuotUid = #{quotuid},
            </if>
            <if test="quotitemid != null ">
                QuotItemid = #{quotitemid},
            </if>
            <if test="bomtype != null">
                BomType = #{bomtype},
            </if>
            <if test="bomid != null ">
                Bomid = #{bomid},
            </if>
            <if test="bomuid != null ">
                BomUid = #{bomuid},
            </if>
            <if test="bomstate != null ">
                BomState = #{bomstate},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="attributestr != null ">
                AttributeStr = #{attributestr},
            </if>
            <if test="machtype != null ">
                MachType = #{machtype},
            </if>
            <if test="reordermark != null">
                ReorderMark = #{reordermark},
            </if>
            <if test="matcode != null ">
                MatCode = #{matcode},
            </if>
            <if test="matused != null">
                MatUsed = #{matused},
            </if>
            <if test="matid != null ">
                Matid = #{matid},
            </if>
            <if test="costitemjson != null ">
                CostItemJson = #{costitemjson},
            </if>
            <if test="costgroupjson != null ">
                CostGroupJson = #{costgroupjson},
            </if>
            <if test="matcostamt != null">
                MatCostAmt = #{matcostamt},
            </if>
            <if test="laborcostamt != null">
                LaborCostAmt = #{laborcostamt},
            </if>
            <if test="directcostamt != null">
                DirectCostAmt = #{directcostamt},
            </if>
            <if test="indirectcostamt != null">
                IndirectCostAmt = #{indirectcostamt},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="attacount != null">
                AttaCount = #{attacount},
            </if>
            <if test="costbudgetamt != null">
                CostBudgetAmt = #{costbudgetamt},
            </if>
            <if test="specid != null ">
                Specid = #{specid},
            </if>
            <if test="specuid != null ">
                SpecUid = #{specuid},
            </if>
            <if test="specstate != null">
                SpecState = #{specstate},
            </if>
            <if test="matbuyqty != null">
                MatBuyQty = #{matbuyqty},
            </if>
            <if test="matuseqty != null">
                MatUseQty = #{matuseqty},
            </if>
            <if test="avgfirstamt != null">
                AvgFirstAmt = #{avgfirstamt},
            </if>
            <if test="avglastamt != null">
                AvgLastAmt = #{avglastamt},
            </if>
            <if test="avginvoamt != null">
                AvgInvoAmt = #{avginvoamt},
            </if>
            <if test="invoqty != null">
                InvoQty = #{invoqty},
            </if>
            <if test="invoclosed != null">
                InvoClosed = #{invoclosed},
            </if>
            <if test="mainplanqty != null">
                MainPlanQty = #{mainplanqty},
            </if>
            <if test="mainplanclosed != null">
                MainPlanClosed = #{mainplanclosed},
            </if>
            <if test="wkmergemark != null">
                WkMergeMark =#{wkmergemark},
            </if>
            <if test="wkmergeitemid != null ">
                WkMergeItemid =#{wkmergeitemid},
            </if>
            <if test="bpmergemark != null">
                BpMergeMark =#{bpmergemark},
            </if>
            <if test="bpmergeitemid != null ">
                BpMergeItemid =#{bpmergeitemid},
            </if>
            <if test="invoamt != null">
                InvoAmt = #{invoamt},
            </if>
            <if test="subprice != null">
                SubPrice = #{subprice},
            </if>
            <if test="subamount != null">
                SubAmount = #{subamount},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="custom11 != null ">
                Custom11 = #{custom11},
            </if>
            <if test="custom12 != null ">
                Custom12 = #{custom12},
            </if>
            <if test="custom13 != null ">
                Custom13 = #{custom13},
            </if>
            <if test="custom14 != null ">
                Custom14 = #{custom14},
            </if>
            <if test="custom15 != null ">
                Custom15 = #{custom15},
            </if>
            <if test="custom16 != null ">
                Custom16 = #{custom16},
            </if>
            <if test="custom17 != null ">
                Custom17 = #{custom17},
            </if>
            <if test="custom18 != null ">
                Custom18 = #{custom18},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_MachiningItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <update id="updateLaborCostAmt">
        update Bus_MachiningItem
        set LaborCostAmt = #{laborcost}
        where id = #{machitemid} and Tenantid =#{tid}
    </update>

    <select id="getSpecOrderItemList" resultType="inks.service.sa.som.domain.pojo.MatSpecorderitemPojo">
        select  * from Mat_SpecOrderItem
        where Mat_SpecOrderItem.Pid = #{specid}
        and Mat_SpecOrderItem.Tenantid = #{tenantid}
    </select>

    <select id="getSpecOrder" resultType="java.util.Map">
        select PnlX,PnlY,Pnl2Pcs
        from Mat_SpecOrder
        where Mat_SpecOrder.id = #{specid}
        and Mat_SpecOrder.Tenantid = #{tenantid}
    </select>

    <select id="getAllByTid" resultType="java.util.Map">
        select id,AttributeJson from Bus_MachiningItem
        where Tenantid = #{tid}
    </select>

    <update id="upateAttrStr">
        update Bus_MachiningItem
        set AttributeStr = #{attrStr}
        where id = #{id} and Tenantid =#{tid}
    </update>

    <select id="getGroupidByItemid" resultType="java.lang.String">
        select Groupid from Bus_Machining
        where id =(select Pid from Bus_MachiningItem where id = #{itemid} and Tenantid = #{tid})
    </select>
</mapper>

