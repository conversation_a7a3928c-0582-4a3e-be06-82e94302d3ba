<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatRequisitionitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatRequisitionitemPojo">
        SELECT Mat_RequisitionItem.id,
               Mat_RequisitionItem.Pid,
               Mat_RequisitionItem.Goodsid,
               Mat_RequisitionItem.Quantity,
               Mat_RequisitionItem.PlanDate,
               Mat_RequisitionItem.Remark,
               Mat_RequisitionItem.RowNum,
               Mat_RequisitionItem.PickQty,
               Mat_RequisitionItem.FinishQty,
               Mat_RequisitionItem.FinishCost,
               Mat_RequisitionItem.StateCode,
               Mat_RequisitionItem.StateDate,
               Mat_RequisitionItem.CiteUid,
               Mat_RequisitionItem.CiteItemid,
               Mat_RequisitionItem.Customer,
               Mat_RequisitionItem.CustPO,
               Mat_RequisitionItem.BackQty,
               Mat_RequisitionItem.BackCost,
               Mat_RequisitionItem.Closed,
               Mat_RequisitionItem.StdQty,
               Mat_RequisitionItem.Storeid,
               Mat_RequisitionItem.Location,
               Mat_RequisitionItem.BatchNo,
               Mat_RequisitionItem.MachUid,
               Mat_RequisitionItem.MachItemid,
               Mat_RequisitionItem.MachBatch,
               Mat_RequisitionItem.MachItemGoodid,
               Mat_RequisitionItem.MachGroupid,
               Mat_RequisitionItem.MainPlanUid,
               Mat_RequisitionItem.MainPlanItemid,
               Mat_RequisitionItem.MainPlanItemGoodid,
               Mat_RequisitionItem.MrpUid,
               Mat_RequisitionItem.MrpItemid,
               Mat_RequisitionItem.MrpItemGoodid,
               Mat_RequisitionItem.WkBillType,
               Mat_RequisitionItem.WorkUid,
               Mat_RequisitionItem.WorkItemid,
               Mat_RequisitionItem.WorkItemGoodid,
               Mat_RequisitionItem.WorkItemMatid,
               Mat_RequisitionItem.ParentGoodsid,
               Mat_RequisitionItem.DisannulMark,
               Mat_RequisitionItem.SourceType,
               Mat_RequisitionItem.AttributeJson,
               Mat_RequisitionItem.Custom1,
               Mat_RequisitionItem.Custom2,
               Mat_RequisitionItem.Custom3,
               Mat_RequisitionItem.Custom4,
               Mat_RequisitionItem.Custom5,
               Mat_RequisitionItem.Custom6,
               Mat_RequisitionItem.Custom7,
               Mat_RequisitionItem.Custom8,
               Mat_RequisitionItem.Custom9,
               Mat_RequisitionItem.Custom10,
               Mat_RequisitionItem.Tenantid,
               Mat_RequisitionItem.TenantName,
               Mat_RequisitionItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Surface,
               Mat_Goods.Drawing,
               Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_RequisitionItem
                 LEFT JOIN Mat_Goods ON Mat_RequisitionItem.Goodsid = Mat_Goods.id
        where Mat_RequisitionItem.id = #{key}
          and Mat_RequisitionItem.Tenantid = #{tid}
    </select>
    <sql id="selectMatRequisitionitemVo">
        SELECT Mat_RequisitionItem.id,
               Mat_RequisitionItem.Pid,
               Mat_RequisitionItem.Goodsid,
               Mat_RequisitionItem.Quantity,
               Mat_RequisitionItem.PlanDate,
               Mat_RequisitionItem.Remark,
               Mat_RequisitionItem.RowNum,
               Mat_RequisitionItem.PickQty,
               Mat_RequisitionItem.FinishQty,
               Mat_RequisitionItem.FinishCost,
               Mat_RequisitionItem.StateCode,
               Mat_RequisitionItem.StateDate,
               Mat_RequisitionItem.CiteUid,
               Mat_RequisitionItem.CiteItemid,
               Mat_RequisitionItem.Customer,
               Mat_RequisitionItem.CustPO,
               Mat_RequisitionItem.BackQty,
               Mat_RequisitionItem.BackCost,
               Mat_RequisitionItem.Closed,
               Mat_RequisitionItem.StdQty,
               Mat_RequisitionItem.Storeid,
               Mat_RequisitionItem.Location,
               Mat_RequisitionItem.BatchNo,
               Mat_RequisitionItem.MachUid,
               Mat_RequisitionItem.MachItemid,
               Mat_RequisitionItem.MachBatch,
               Mat_RequisitionItem.MachItemGoodid,
               Mat_RequisitionItem.MachGroupid,
               Mat_RequisitionItem.MainPlanUid,
               Mat_RequisitionItem.MainPlanItemid,
               Mat_RequisitionItem.MainPlanItemGoodid,
               Mat_RequisitionItem.MrpUid,
               Mat_RequisitionItem.MrpItemid,
               Mat_RequisitionItem.MrpItemGoodid,
               Mat_RequisitionItem.WkBillType,
               Mat_RequisitionItem.WorkUid,
               Mat_RequisitionItem.WorkItemid,
               Mat_RequisitionItem.WorkItemGoodid,
               Mat_RequisitionItem.WorkItemMatid,
               Mat_RequisitionItem.ParentGoodsid,
               Mat_RequisitionItem.DisannulMark,
               Mat_RequisitionItem.SourceType,
               Mat_RequisitionItem.AttributeJson,
               Mat_RequisitionItem.Custom1,
               Mat_RequisitionItem.Custom2,
               Mat_RequisitionItem.Custom3,
               Mat_RequisitionItem.Custom4,
               Mat_RequisitionItem.Custom5,
               Mat_RequisitionItem.Custom6,
               Mat_RequisitionItem.Custom7,
               Mat_RequisitionItem.Custom8,
               Mat_RequisitionItem.Custom9,
               Mat_RequisitionItem.Custom10,
               Mat_RequisitionItem.Tenantid,
               Mat_RequisitionItem.TenantName,
               Mat_RequisitionItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_RequisitionItem
                 LEFT JOIN Mat_Goods ON Mat_RequisitionItem.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatRequisitionitemPojo">
        <include refid="selectMatRequisitionitemVo"/>
        where 1 = 1 and Mat_RequisitionItem.Tenantid =#{tenantid}
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_RequisitionItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Mat_RequisitionItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_RequisitionItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Mat_RequisitionItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Mat_RequisitionItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Mat_RequisitionItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Mat_RequisitionItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Mat_RequisitionItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Mat_RequisitionItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.storeid != null and SearchPojo.storeid != ''">
            and Mat_RequisitionItem.storeid like concat('%', #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Mat_RequisitionItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Mat_RequisitionItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Mat_RequisitionItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Mat_RequisitionItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machitemgoodid != null and SearchPojo.machitemgoodid != ''">
            and Mat_RequisitionItem.machitemgoodid like concat('%', #{SearchPojo.machitemgoodid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Mat_RequisitionItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Mat_RequisitionItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Mat_RequisitionItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemgoodid != null and SearchPojo.mainplanitemgoodid != ''">
            and Mat_RequisitionItem.mainplanitemgoodid like concat('%', #{SearchPojo.mainplanitemgoodid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Mat_RequisitionItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Mat_RequisitionItem.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.mrpitemgoodid != null and SearchPojo.mrpitemgoodid != ''">
            and Mat_RequisitionItem.mrpitemgoodid like concat('%', #{SearchPojo.mrpitemgoodid}, '%')
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            and Mat_RequisitionItem.workuid like concat('%', #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
            and Mat_RequisitionItem.workitemid like concat('%', #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.workitemgoodid != null and SearchPojo.workitemgoodid != ''">
            and Mat_RequisitionItem.workitemgoodid like concat('%', #{SearchPojo.workitemgoodid}, '%')
        </if>
        <if test="SearchPojo.workitemmatid != null and SearchPojo.workitemmatid != ''">
            and Mat_RequisitionItem.workitemmatid like concat('%', #{SearchPojo.workitemmatid}, '%')
        </if>
        <if test="SearchPojo.parentgoodsid != null and SearchPojo.parentgoodsid != ''">
            and Mat_RequisitionItem.parentgoodsid like concat('%', #{SearchPojo.parentgoodsid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_RequisitionItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_RequisitionItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_RequisitionItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_RequisitionItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_RequisitionItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_RequisitionItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_RequisitionItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_RequisitionItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_RequisitionItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_RequisitionItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            and Mat_RequisitionItem.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Mat_RequisitionItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_RequisitionItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Mat_RequisitionItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Mat_RequisitionItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Mat_RequisitionItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Mat_RequisitionItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Mat_RequisitionItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Mat_RequisitionItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.storeid != null and SearchPojo.storeid != ''">
                or Mat_RequisitionItem.Storeid like concat('%', #{SearchPojo.storeid}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Mat_RequisitionItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Mat_RequisitionItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Mat_RequisitionItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Mat_RequisitionItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machitemgoodid != null and SearchPojo.machitemgoodid != ''">
                or Mat_RequisitionItem.MachItemGoodid like concat('%', #{SearchPojo.machitemgoodid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Mat_RequisitionItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Mat_RequisitionItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Mat_RequisitionItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemgoodid != null and SearchPojo.mainplanitemgoodid != ''">
                or Mat_RequisitionItem.MainPlanItemGoodid like concat('%', #{SearchPojo.mainplanitemgoodid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Mat_RequisitionItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Mat_RequisitionItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.mrpitemgoodid != null and SearchPojo.mrpitemgoodid != ''">
                or Mat_RequisitionItem.MrpItemGoodid like concat('%', #{SearchPojo.mrpitemgoodid}, '%')
            </if>
            <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
                or Mat_RequisitionItem.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
            </if>
            <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
                or Mat_RequisitionItem.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
            </if>
            <if test="SearchPojo.workitemgoodid != null and SearchPojo.workitemgoodid != ''">
                or Mat_RequisitionItem.WorkItemGoodid like concat('%', #{SearchPojo.workitemgoodid}, '%')
            </if>
            <if test="SearchPojo.workitemmatid != null and SearchPojo.workitemmatid != ''">
                or Mat_RequisitionItem.WorkItemMatid like concat('%', #{SearchPojo.workitemmatid}, '%')
            </if>
            <if test="SearchPojo.parentgoodsid != null and SearchPojo.parentgoodsid != ''">
                or Mat_RequisitionItem.ParentGoodsid like concat('%', #{SearchPojo.parentgoodsid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_RequisitionItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_RequisitionItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_RequisitionItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_RequisitionItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_RequisitionItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_RequisitionItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_RequisitionItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_RequisitionItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_RequisitionItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_RequisitionItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
                or Mat_RequisitionItem.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.MatRequisitionitemPojo">
        SELECT Mat_RequisitionItem.id,
               Mat_RequisitionItem.Pid,
               Mat_RequisitionItem.Goodsid,
               Mat_RequisitionItem.Quantity,
               Mat_RequisitionItem.PlanDate,
               Mat_RequisitionItem.Remark,
               Mat_RequisitionItem.RowNum,
               Mat_RequisitionItem.PickQty,
               Mat_RequisitionItem.FinishQty,
               Mat_RequisitionItem.FinishCost,
               Mat_RequisitionItem.StateCode,
               Mat_RequisitionItem.StateDate,
               Mat_RequisitionItem.CiteUid,
               Mat_RequisitionItem.CiteItemid,
               Mat_RequisitionItem.Customer,
               Mat_RequisitionItem.CustPO,
               Mat_RequisitionItem.BackQty,
               Mat_RequisitionItem.BackCost,
               Mat_RequisitionItem.Closed,
               Mat_RequisitionItem.StdQty,
               Mat_RequisitionItem.Storeid,
               Mat_RequisitionItem.Location,
               Mat_RequisitionItem.BatchNo,
               Mat_RequisitionItem.MachUid,
               Mat_RequisitionItem.MachItemid,
        Mat_RequisitionItem.MachBatch,
               Mat_RequisitionItem.MachItemGoodid,
               Mat_RequisitionItem.MachGroupid,
               Mat_RequisitionItem.MainPlanUid,
               Mat_RequisitionItem.MainPlanItemid,
               Mat_RequisitionItem.MainPlanItemGoodid,
               Mat_RequisitionItem.MrpUid,
               Mat_RequisitionItem.MrpItemid,
               Mat_RequisitionItem.MrpItemGoodid,
               Mat_RequisitionItem.WkBillType,
               Mat_RequisitionItem.WorkUid,
               Mat_RequisitionItem.WorkItemid,
               Mat_RequisitionItem.WorkItemGoodid,
               Mat_RequisitionItem.WorkItemMatid,
               Mat_RequisitionItem.ParentGoodsid,
               Mat_RequisitionItem.DisannulMark,
               Mat_RequisitionItem.SourceType,
               Mat_RequisitionItem.AttributeJson,
               Mat_RequisitionItem.Custom1,
               Mat_RequisitionItem.Custom2,
               Mat_RequisitionItem.Custom3,
               Mat_RequisitionItem.Custom4,
               Mat_RequisitionItem.Custom5,
               Mat_RequisitionItem.Custom6,
               Mat_RequisitionItem.Custom7,
               Mat_RequisitionItem.Custom8,
               Mat_RequisitionItem.Custom9,
               Mat_RequisitionItem.Custom10,
               Mat_RequisitionItem.Tenantid,
               Mat_RequisitionItem.TenantName,
               Mat_RequisitionItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_RequisitionItem
                 LEFT JOIN Mat_Goods ON Mat_RequisitionItem.Goodsid = Mat_Goods.id
        where Mat_RequisitionItem.Pid = #{Pid}
          and Mat_RequisitionItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_RequisitionItem(id, Pid, Goodsid, Quantity, PlanDate, Remark, RowNum, PickQty, FinishQty,
                                        FinishCost, StateCode, StateDate, CiteUid, CiteItemid, Customer, CustPO,
                                        BackQty, BackCost, Closed, StdQty, Storeid, Location, BatchNo, MachUid,
                                        MachItemid, MachBatch, MachItemGoodid, MachGroupid, MainPlanUid, MainPlanItemid,
                                        MainPlanItemGoodid, MrpUid, MrpItemid, MrpItemGoodid, WkBillType, WorkUid,
                                        WorkItemid,
                                        WorkItemGoodid, WorkItemMatid, ParentGoodsid, DisannulMark, SourceType,
                                        AttributeJson, Custom1,
                                        Custom2,
                                        Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10,
                                        Tenantid, TenantName, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{quantity}, #{plandate}, #{remark}, #{rownum}, #{pickqty}, #{finishqty},
                #{finishcost}, #{statecode}, #{statedate}, #{citeuid}, #{citeitemid}, #{customer}, #{custpo},
                #{backqty}, #{backcost}, #{closed}, #{stdqty}, #{storeid}, #{location}, #{batchno}, #{machuid},
                #{machitemid}, #{machbatch}, #{machitemgoodid}, #{machgroupid}, #{mainplanuid}, #{mainplanitemid},
                #{mainplanitemgoodid}, #{mrpuid}, #{mrpitemid}, #{mrpitemgoodid}, #{wkbilltype}, #{workuid},
                #{workitemid},
                #{workitemgoodid}, #{workitemmatid}, #{parentgoodsid}, #{disannulmark}, #{sourcetype}, #{attributejson},
                #{custom1},
                #{custom2},
                #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10},
                #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_RequisitionItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="pickqty != null">
                PickQty = #{pickqty},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="finishcost != null">
                FinishCost = #{finishcost},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="backqty != null">
                BackQty = #{backqty},
            </if>
            <if test="backcost != null">
                BackCost = #{backcost},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="stdqty != null">
                StdQty = #{stdqty},
            </if>
            <if test="storeid != null ">
                Storeid = #{storeid},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machbatch != null ">
                MachBatch = #{machbatch},
            </if>
            <if test="machitemgoodid != null ">
                MachItemGoodid = #{machitemgoodid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="mainplanitemgoodid != null ">
                MainPlanItemGoodid = #{mainplanitemgoodid},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null ">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="mrpitemgoodid != null ">
                MrpItemGoodid = #{mrpitemgoodid},
            </if>
            <if test="wkbilltype != null ">
                WkBillType = #{wkbilltype},
            </if>
            <if test="workuid != null ">
                WorkUid = #{workuid},
            </if>
            <if test="workitemid != null ">
                WorkItemid = #{workitemid},
            </if>
            <if test="workitemgoodid != null ">
                WorkItemGoodid = #{workitemgoodid},
            </if>
            <if test="workitemmatid != null ">
                WorkItemMatid = #{workitemmatid},
            </if>
            <if test="parentgoodsid != null ">
                ParentGoodsid = #{parentgoodsid},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="attributejson != null">
                AttributeJson = #{attributejson},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_RequisitionItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询List-->
    <select id="getListByWorkitemid" resultType="inks.service.sa.som.domain.pojo.MatRequisitionitemPojo">
        SELECT Mat_RequisitionItem.id,
               Mat_RequisitionItem.Pid,
               Mat_RequisitionItem.Goodsid,
               Mat_RequisitionItem.Quantity,
               Mat_RequisitionItem.PlanDate,
               Mat_RequisitionItem.Remark,
               Mat_RequisitionItem.RowNum,
               Mat_RequisitionItem.PickQty,
               Mat_RequisitionItem.FinishQty,
               Mat_RequisitionItem.FinishCost,
               Mat_RequisitionItem.StateCode,
               Mat_RequisitionItem.StateDate,
               Mat_RequisitionItem.CiteUid,
               Mat_RequisitionItem.CiteItemid,
               Mat_RequisitionItem.Customer,
               Mat_RequisitionItem.CustPO,
               Mat_RequisitionItem.BackQty,
               Mat_RequisitionItem.BackCost,
               Mat_RequisitionItem.Closed,
               Mat_RequisitionItem.StdQty,
               Mat_RequisitionItem.Storeid,
               Mat_RequisitionItem.Location,
               Mat_RequisitionItem.BatchNo,
               Mat_RequisitionItem.MachUid,
               Mat_RequisitionItem.MachItemid,
               Mat_RequisitionItem.MachBatch,
               Mat_RequisitionItem.MachItemGoodid,
               Mat_RequisitionItem.MachGroupid,
               Mat_RequisitionItem.MainPlanUid,
               Mat_RequisitionItem.MainPlanItemid,
               Mat_RequisitionItem.MainPlanItemGoodid,
               Mat_RequisitionItem.MrpUid,
               Mat_RequisitionItem.MrpItemid,
               Mat_RequisitionItem.MrpItemGoodid,
               Mat_RequisitionItem.WkBillType,
               Mat_RequisitionItem.WorkUid,
               Mat_RequisitionItem.WorkItemid,
               Mat_RequisitionItem.WorkItemGoodid,
               Mat_RequisitionItem.WorkItemMatid,
               Mat_RequisitionItem.ParentGoodsid,
               Mat_RequisitionItem.DisannulMark,
               Mat_RequisitionItem.SourceType,
               Mat_RequisitionItem.AttributeJson,
               Mat_RequisitionItem.Custom1,
               Mat_RequisitionItem.Custom2,
               Mat_RequisitionItem.Custom3,
               Mat_RequisitionItem.Custom4,
               Mat_RequisitionItem.Custom5,
               Mat_RequisitionItem.Custom6,
               Mat_RequisitionItem.Custom7,
               Mat_RequisitionItem.Custom8,
               Mat_RequisitionItem.Custom9,
               Mat_RequisitionItem.Custom10,
               Mat_RequisitionItem.Tenantid,
               Mat_RequisitionItem.TenantName,
               Mat_RequisitionItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Surface,
               Mat_Goods.Drawing,
               Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_RequisitionItem
                 LEFT JOIN Mat_Goods ON Mat_RequisitionItem.Goodsid = Mat_Goods.id
        where Mat_RequisitionItem.WorkItemid = #{key}
          and Mat_RequisitionItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <select id="getMachListInMachitemids" resultType="java.util.Map">
        <include refid="inks.service.sa.som.mapper.BusMachiningMapper.selectdetailVo"/>
        where Bus_MachiningItem.id in
        <foreach collection="machitemids" item="machitemid" open="(" separator="," close=")">
            #{machitemid}
        </foreach>
        and Bus_MachiningItem.Tenantid = #{tid}
    </select>
</mapper>

