<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyInvoiceitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyInvoiceitemPojo">
        SELECT Buy_InvoiceItem.id,
               Buy_InvoiceItem.Pid,
               Buy_InvoiceItem.FinishUid,
               Buy_InvoiceItem.FinishDate,
               Buy_InvoiceItem.FinishType,
               Buy_InvoiceItem.FinishItemid,
               Buy_InvoiceItem.Goodsid,
               Buy_InvoiceItem.BillQty,
               Buy_InvoiceItem.Quantity,
               Buy_InvoiceItem.TaxPrice,
               Buy_InvoiceItem.TaxAmount,
               Buy_InvoiceItem.Price,
               Buy_InvoiceItem.Amount,
               Buy_InvoiceItem.TaxTotal,
               Buy_InvoiceItem.RowNum,
               Buy_InvoiceItem.Remark,
               Buy_InvoiceItem.OrderNo,
               Buy_InvoiceItem.OrderUid,
               Buy_InvoiceItem.OrderItemid,
               Buy_InvoiceItem.MachUid,
               Buy_InvoiceItem.MachItemid,
               Buy_InvoiceItem.MachGroupid,
               Buy_InvoiceItem.CustPO,
               Buy_InvoiceItem.Customer,
               Buy_InvoiceItem.MrpUid,
               Buy_InvoiceItem.MrpItemid,
               Buy_InvoiceItem.ItemTaxrate,
               Buy_InvoiceItem.AvgFirstAmt,
               Buy_InvoiceItem.AvgLastAmt,
               Buy_InvoiceItem.Custom1,
               Buy_InvoiceItem.Custom2,
               Buy_InvoiceItem.Custom3,
               Buy_InvoiceItem.Custom4,
               Buy_InvoiceItem.Custom5,
               Buy_InvoiceItem.Custom6,
               Buy_InvoiceItem.Custom7,
               Buy_InvoiceItem.Custom8,
               Buy_InvoiceItem.Custom9,
               Buy_InvoiceItem.Custom10,
               Buy_InvoiceItem.Custom11,
               Buy_InvoiceItem.Custom12,
               Buy_InvoiceItem.Custom13,
               Buy_InvoiceItem.Custom14,
               Buy_InvoiceItem.Custom15,
               Buy_InvoiceItem.Custom16,
               Buy_InvoiceItem.Custom17,
               Buy_InvoiceItem.Custom18,
               Buy_InvoiceItem.Tenantid,
               Buy_InvoiceItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_Goods
                 RIGHT JOIN Buy_InvoiceItem ON Mat_Goods.id = Buy_InvoiceItem.Goodsid

        where Buy_InvoiceItem.id = #{key}
          and Buy_InvoiceItem.Tenantid = #{tid}
    </select>
    <sql id="selectBuyInvoiceitemVo">
        SELECT Buy_InvoiceItem.id,
               Buy_InvoiceItem.Pid,
               Buy_InvoiceItem.FinishUid,
               Buy_InvoiceItem.FinishDate,
               Buy_InvoiceItem.FinishType,
               Buy_InvoiceItem.FinishItemid,
               Buy_InvoiceItem.Goodsid,
               Buy_InvoiceItem.BillQty,
               Buy_InvoiceItem.Quantity,
               Buy_InvoiceItem.TaxPrice,
               Buy_InvoiceItem.TaxAmount,
               Buy_InvoiceItem.Price,
               Buy_InvoiceItem.Amount,
               Buy_InvoiceItem.TaxTotal,
               Buy_InvoiceItem.RowNum,
               Buy_InvoiceItem.Remark,
               Buy_InvoiceItem.OrderNo,
               Buy_InvoiceItem.OrderUid,
               Buy_InvoiceItem.OrderItemid,
               Buy_InvoiceItem.MachUid,
               Buy_InvoiceItem.MachItemid,
               Buy_InvoiceItem.MachGroupid,
               Buy_InvoiceItem.CustPO,
               Buy_InvoiceItem.Customer,
               Buy_InvoiceItem.MrpUid,
               Buy_InvoiceItem.MrpItemid,
               Buy_InvoiceItem.ItemTaxrate,
               Buy_InvoiceItem.AvgFirstAmt,
               Buy_InvoiceItem.AvgLastAmt,
               Buy_InvoiceItem.Custom1,
               Buy_InvoiceItem.Custom2,
               Buy_InvoiceItem.Custom3,
               Buy_InvoiceItem.Custom4,
               Buy_InvoiceItem.Custom5,
               Buy_InvoiceItem.Custom6,
               Buy_InvoiceItem.Custom7,
               Buy_InvoiceItem.Custom8,
               Buy_InvoiceItem.Custom9,
               Buy_InvoiceItem.Custom10,
               Buy_InvoiceItem.Custom11,
               Buy_InvoiceItem.Custom12,
               Buy_InvoiceItem.Custom13,
               Buy_InvoiceItem.Custom14,
               Buy_InvoiceItem.Custom15,
               Buy_InvoiceItem.Custom16,
               Buy_InvoiceItem.Custom17,
               Buy_InvoiceItem.Custom18,
               Buy_InvoiceItem.Tenantid,
               Buy_InvoiceItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_Goods
                 RIGHT JOIN Buy_InvoiceItem ON Mat_Goods.id = Buy_InvoiceItem.Goodsid

    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyInvoiceitemPojo">
        <include refid="selectBuyInvoiceitemVo"/>
        where 1 = 1 and Buy_InvoiceItem.Tenantid =#{tenantid}
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_InvoiceItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Buy_InvoiceItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.finishuid != null and SearchPojo.finishuid != ''">
            and Buy_InvoiceItem.finishuid like concat('%', #{SearchPojo.finishuid}, '%')
        </if>
        <if test="SearchPojo.finishtype != null and SearchPojo.finishtype != ''">
            and Buy_InvoiceItem.finishtype like concat('%', #{SearchPojo.finishtype}, '%')
        </if>
        <if test="SearchPojo.finishitemid != null and SearchPojo.finishitemid != ''">
            and Buy_InvoiceItem.finishitemid like concat('%', #{SearchPojo.finishitemid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Buy_InvoiceItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Buy_InvoiceItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Buy_InvoiceItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Buy_InvoiceItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Buy_InvoiceItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Buy_InvoiceItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Buy_InvoiceItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Buy_InvoiceItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Buy_InvoiceItem.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Buy_InvoiceItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Buy_InvoiceItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Buy_InvoiceItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Buy_InvoiceItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Buy_InvoiceItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Buy_InvoiceItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Buy_InvoiceItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Buy_InvoiceItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Buy_InvoiceItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Buy_InvoiceItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.custom11 != null and SearchPojo.custom11 != ''">
            and Buy_InvoiceItem.custom11 like concat('%', #{SearchPojo.custom11}, '%')
        </if>
        <if test="SearchPojo.custom12 != null and SearchPojo.custom12 != ''">
            and Buy_InvoiceItem.custom12 like concat('%', #{SearchPojo.custom12}, '%')
        </if>
        <if test="SearchPojo.custom13 != null and SearchPojo.custom13 != ''">
            and Buy_InvoiceItem.custom13 like concat('%', #{SearchPojo.custom13}, '%')
        </if>
        <if test="SearchPojo.custom14 != null and SearchPojo.custom14 != ''">
            and Buy_InvoiceItem.custom14 like concat('%', #{SearchPojo.custom14}, '%')
        </if>
        <if test="SearchPojo.custom15 != null and SearchPojo.custom15 != ''">
            and Buy_InvoiceItem.custom15 like concat('%', #{SearchPojo.custom15}, '%')
        </if>
        <if test="SearchPojo.custom16 != null and SearchPojo.custom16 != ''">
            and Buy_InvoiceItem.custom16 like concat('%', #{SearchPojo.custom16}, '%')
        </if>
        <if test="SearchPojo.custom17 != null and SearchPojo.custom17 != ''">
            and Buy_InvoiceItem.custom17 like concat('%', #{SearchPojo.custom17}, '%')
        </if>
        <if test="SearchPojo.custom18 != null and SearchPojo.custom18 != ''">
            and Buy_InvoiceItem.custom18 like concat('%', #{SearchPojo.custom18}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            or Buy_InvoiceItem.Pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.finishuid != null and SearchPojo.finishuid != ''">
            or Buy_InvoiceItem.FinishUid like concat('%', #{SearchPojo.finishuid}, '%')
        </if>
        <if test="SearchPojo.finishtype != null and SearchPojo.finishtype != ''">
            or Buy_InvoiceItem.FinishType like concat('%', #{SearchPojo.finishtype}, '%')
        </if>
        <if test="SearchPojo.finishitemid != null and SearchPojo.finishitemid != ''">
            or Buy_InvoiceItem.FinishItemid like concat('%', #{SearchPojo.finishitemid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            or Buy_InvoiceItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or Buy_InvoiceItem.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            or Buy_InvoiceItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            or Buy_InvoiceItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            or Buy_InvoiceItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            or Buy_InvoiceItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            or Buy_InvoiceItem.Customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            or Buy_InvoiceItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            or Buy_InvoiceItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Buy_InvoiceItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Buy_InvoiceItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Buy_InvoiceItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Buy_InvoiceItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or Buy_InvoiceItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            or Buy_InvoiceItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            or Buy_InvoiceItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            or Buy_InvoiceItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            or Buy_InvoiceItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            or Buy_InvoiceItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.custom11 != null and SearchPojo.custom11 != ''">
            or Buy_InvoiceItem.Custom11 like concat('%', #{SearchPojo.custom11}, '%')
        </if>
        <if test="SearchPojo.custom12 != null and SearchPojo.custom12 != ''">
            or Buy_InvoiceItem.Custom12 like concat('%', #{SearchPojo.custom12}, '%')
        </if>
        <if test="SearchPojo.custom13 != null and SearchPojo.custom13 != ''">
            or Buy_InvoiceItem.Custom13 like concat('%', #{SearchPojo.custom13}, '%')
        </if>
        <if test="SearchPojo.custom14 != null and SearchPojo.custom14 != ''">
            or Buy_InvoiceItem.Custom14 like concat('%', #{SearchPojo.custom14}, '%')
        </if>
        <if test="SearchPojo.custom15 != null and SearchPojo.custom15 != ''">
            or Buy_InvoiceItem.Custom15 like concat('%', #{SearchPojo.custom15}, '%')
        </if>
        <if test="SearchPojo.custom16 != null and SearchPojo.custom16 != ''">
            or Buy_InvoiceItem.Custom16 like concat('%', #{SearchPojo.custom16}, '%')
        </if>
        <if test="SearchPojo.custom17 != null and SearchPojo.custom17 != ''">
            or Buy_InvoiceItem.Custom17 like concat('%', #{SearchPojo.custom17}, '%')
        </if>
        <if test="SearchPojo.custom18 != null and SearchPojo.custom18 != ''">
            or Buy_InvoiceItem.Custom18 like concat('%', #{SearchPojo.custom18}, '%')
        </if>
        )
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.BuyInvoiceitemPojo">
        SELECT Buy_InvoiceItem.id,
               Buy_InvoiceItem.Pid,
               Buy_InvoiceItem.FinishUid,
               Buy_InvoiceItem.FinishDate,
               Buy_InvoiceItem.FinishType,
               Buy_InvoiceItem.FinishItemid,
               Buy_InvoiceItem.Goodsid,
               Buy_InvoiceItem.BillQty,
               Buy_InvoiceItem.Quantity,
               Buy_InvoiceItem.TaxPrice,
               Buy_InvoiceItem.TaxAmount,
               Buy_InvoiceItem.Price,
               Buy_InvoiceItem.Amount,
               Buy_InvoiceItem.TaxTotal,
               Buy_InvoiceItem.RowNum,
               Buy_InvoiceItem.Remark,
               Buy_InvoiceItem.OrderNo,
               Buy_InvoiceItem.OrderUid,
               Buy_InvoiceItem.OrderItemid,
               Buy_InvoiceItem.MachUid,
               Buy_InvoiceItem.MachItemid,
               Buy_InvoiceItem.MachGroupid,
               Buy_InvoiceItem.CustPO,
               Buy_InvoiceItem.Customer,
               Buy_InvoiceItem.MrpUid,
               Buy_InvoiceItem.MrpItemid,
               Buy_InvoiceItem.ItemTaxrate,
               Buy_InvoiceItem.AvgFirstAmt,
               Buy_InvoiceItem.AvgLastAmt,
               Buy_InvoiceItem.Custom1,
               Buy_InvoiceItem.Custom2,
               Buy_InvoiceItem.Custom3,
               Buy_InvoiceItem.Custom4,
               Buy_InvoiceItem.Custom5,
               Buy_InvoiceItem.Custom6,
               Buy_InvoiceItem.Custom7,
               Buy_InvoiceItem.Custom8,
               Buy_InvoiceItem.Custom9,
               Buy_InvoiceItem.Custom10,
               Buy_InvoiceItem.Custom11,
               Buy_InvoiceItem.Custom12,
               Buy_InvoiceItem.Custom13,
               Buy_InvoiceItem.Custom14,
               Buy_InvoiceItem.Custom15,
               Buy_InvoiceItem.Custom16,
               Buy_InvoiceItem.Custom17,
               Buy_InvoiceItem.Custom18,
               Buy_InvoiceItem.Tenantid,
               Buy_InvoiceItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_Goods
                 RIGHT JOIN Buy_InvoiceItem ON Mat_Goods.id = Buy_InvoiceItem.Goodsid

        where Buy_InvoiceItem.Pid = #{Pid}
          and Buy_InvoiceItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Buy_InvoiceItem(id, Pid, FinishUid, FinishDate, FinishType, FinishItemid, Goodsid, BillQty,
                                    Quantity, TaxPrice, TaxAmount, Price, Amount, TaxTotal, RowNum, Remark, OrderNo,
                                    OrderUid, OrderItemid, MachUid, MachItemid, MachGroupid, CustPO, Customer, MrpUid,
                                    MrpItemid, ItemTaxrate, AvgFirstAmt, AvgLastAmt, Custom1, Custom2, Custom3, Custom4,
                                    Custom5, Custom6,
                                    Custom7, Custom8, Custom9, Custom10, Custom11, Custom12, Custom13, Custom14,
                                    Custom15, Custom16, Custom17, Custom18, Tenantid, Revision)
        values (#{id}, #{pid}, #{finishuid}, #{finishdate}, #{finishtype}, #{finishitemid}, #{goodsid}, #{billqty},
                #{quantity}, #{taxprice}, #{taxamount}, #{price}, #{amount}, #{taxtotal}, #{rownum}, #{remark},
                #{orderno}, #{orderuid}, #{orderitemid}, #{machuid}, #{machitemid}, #{machgroupid}, #{custpo},
                #{customer}, #{mrpuid}, #{mrpitemid}, #{itemtaxrate}, #{avgfirstamt}, #{avglastamt},
                #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{custom11}, #{custom12},
                #{custom13}, #{custom14}, #{custom15}, #{custom16}, #{custom17}, #{custom18}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_InvoiceItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="finishuid != null ">
                FinishUid = #{finishuid},
            </if>
            <if test="finishdate != null">
                FinishDate = #{finishdate},
            </if>
            <if test="finishtype != null ">
                FinishType = #{finishtype},
            </if>
            <if test="finishitemid != null ">
                FinishItemid = #{finishitemid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="billqty != null">
                BillQty = #{billqty},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="orderno != null">
                OrderNo = #{orderno},
            </if>
            <if test="orderuid != null">
                OrderUid = #{orderuid},
            </if>
            <if test="orderitemid != null">
                OrderItemid = #{orderitemid},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null ">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="itemtaxrate != null ">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="avgfirstamt != null ">
                AvgFirstAmt = #{avgfirstamt},
            </if>
            <if test="avglastamt != null ">
                AvgLastAmt = #{avglastamt},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="custom11 != null ">
                Custom11 = #{custom11},
            </if>
            <if test="custom12 != null ">
                Custom12 = #{custom12},
            </if>
            <if test="custom13 != null ">
                Custom13 = #{custom13},
            </if>
            <if test="custom14 != null ">
                Custom14 = #{custom14},
            </if>
            <if test="custom15 != null ">
                Custom15 = #{custom15},
            </if>
            <if test="custom16 != null ">
                Custom16 = #{custom16},
            </if>
            <if test="custom17 != null ">
                Custom17 = #{custom17},
            </if>
            <if test="custom18 != null ">
                Custom18 = #{custom18},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_InvoiceItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

