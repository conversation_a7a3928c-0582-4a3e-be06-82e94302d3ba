<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatCargospaceMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatCargospacePojo">
        select id,
               SpaceCode,
               SpaceName,
               Storeid,
               StoreCode,
               StoreName,
               Operator,
               Remark,
               RowNum,
               AllowEdit,
               AllowDelete,
               EnabledMark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               DeleteMark,
               DeleteLister,
               DeleteListerid,
               DeleteDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Mat_CargoSpace
        where Mat_CargoSpace.id = #{key}
          and Mat_CargoSpace.Tenantid = #{tid}
    </select>
    <sql id="selectMatCargospaceVo">
        select id,
               SpaceCode,
               SpaceName,
               Storeid,
               StoreCode,
               StoreName,
               Operator,
               Remark,
               RowNum,
               AllowEdit,
               AllowDelete,
               EnabledMark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               DeleteMark,
               DeleteLister,
               DeleteListerid,
               DeleteDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Mat_CargoSpace
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatCargospacePojo">
        <include refid="selectMatCargospaceVo"/>
        where 1 = 1 and Mat_CargoSpace.Tenantid =#{tenantid}
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_CargoSpace.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.spacecode != null and SearchPojo.spacecode  != ''">
            and Mat_CargoSpace.SpaceCode like concat('%', #{SearchPojo.spacecode}, '%')
        </if>
        <if test="SearchPojo.spacename != null and SearchPojo.spacename  != ''">
            and Mat_CargoSpace.SpaceName like concat('%', #{SearchPojo.spacename}, '%')
        </if>
        <if test="SearchPojo.storeid != null and SearchPojo.storeid  != ''">
            and Mat_CargoSpace.Storeid like concat('%', #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.storecode != null and SearchPojo.storecode  != ''">
            and Mat_CargoSpace.StoreCode like concat('%', #{SearchPojo.storecode}, '%')
        </if>
        <if test="SearchPojo.storename != null and SearchPojo.storename  != ''">
            and Mat_CargoSpace.StoreName like concat('%', #{SearchPojo.storename}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator  != ''">
            and Mat_CargoSpace.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and Mat_CargoSpace.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
            and Mat_CargoSpace.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid  != ''">
            and Mat_CargoSpace.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and Mat_CargoSpace.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid  != ''">
            and Mat_CargoSpace.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister  != ''">
            and Mat_CargoSpace.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null and SearchPojo.deletelisterid  != ''">
            and Mat_CargoSpace.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1  != ''">
            and Mat_CargoSpace.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2  != ''">
            and Mat_CargoSpace.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3  != ''">
            and Mat_CargoSpace.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4  != ''">
            and Mat_CargoSpace.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5  != ''">
            and Mat_CargoSpace.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6  != ''">
            and Mat_CargoSpace.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7  != ''">
            and Mat_CargoSpace.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8  != ''">
            and Mat_CargoSpace.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9  != ''">
            and Mat_CargoSpace.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10  != ''">
            and Mat_CargoSpace.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.spacecode != null and SearchPojo.spacecode != ''">
            or Mat_CargoSpace.SpaceCode like concat('%', #{SearchPojo.spacecode}, '%')
        </if>
        <if test="SearchPojo.spacename != null and SearchPojo.spacename != ''">
            or Mat_CargoSpace.SpaceName like concat('%', #{SearchPojo.spacename}, '%')
        </if>
        <if test="SearchPojo.storeid != null and SearchPojo.storeid != ''">
            or Mat_CargoSpace.Storeid like concat('%', #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.storecode != null and SearchPojo.storecode != ''">
            or Mat_CargoSpace.StoreCode like concat('%', #{SearchPojo.storecode}, '%')
        </if>
        <if test="SearchPojo.storename != null and SearchPojo.storename != ''">
            or Mat_CargoSpace.StoreName like concat('%', #{SearchPojo.storename}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            or Mat_CargoSpace.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or Mat_CargoSpace.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or Mat_CargoSpace.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or Mat_CargoSpace.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or Mat_CargoSpace.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or Mat_CargoSpace.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister != ''">
            or Mat_CargoSpace.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null and SearchPojo.deletelisterid != ''">
            or Mat_CargoSpace.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Mat_CargoSpace.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Mat_CargoSpace.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Mat_CargoSpace.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Mat_CargoSpace.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or Mat_CargoSpace.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            or Mat_CargoSpace.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            or Mat_CargoSpace.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            or Mat_CargoSpace.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            or Mat_CargoSpace.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            or Mat_CargoSpace.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_CargoSpace(id, SpaceCode, SpaceName, Storeid, StoreCode, StoreName, Operator, Remark, RowNum,
                                   AllowEdit, AllowDelete, EnabledMark, CreateBy, CreateByid, CreateDate, Lister,
                                   Listerid, ModifyDate, DeleteMark, DeleteLister, DeleteListerid, DeleteDate, Custom1,
                                   Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10,
                                   Tenantid, Revision)
        values (#{id}, #{spacecode}, #{spacename}, #{storeid}, #{storecode}, #{storename}, #{operator}, #{remark},
                #{rownum}, #{allowedit}, #{allowdelete}, #{enabledmark}, #{createby}, #{createbyid}, #{createdate},
                #{lister}, #{listerid}, #{modifydate}, #{deletemark}, #{deletelister}, #{deletelisterid}, #{deletedate},
                #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8},
                #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_CargoSpace
        <set>
            <if test="spacecode != null ">
                SpaceCode =#{spacecode},
            </if>
            <if test="spacename != null ">
                SpaceName =#{spacename},
            </if>
            <if test="storeid != null ">
                Storeid =#{storeid},
            </if>
            <if test="storecode != null ">
                StoreCode =#{storecode},
            </if>
            <if test="storename != null ">
                StoreName =#{storename},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="allowedit != null">
                AllowEdit =#{allowedit},
            </if>
            <if test="allowdelete != null">
                AllowDelete =#{allowdelete},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletelisterid != null ">
                DeleteListerid =#{deletelisterid},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_CargoSpace
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
<!--    检查是否存在相同的货位编码或货位名称-->
    <select id="checkCodeOrName" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM Mat_CargoSpace
        WHERE (SpaceCode = #{spacecode} OR SpaceName = #{spacename})
          AND tenantid = #{tid}
        LIMIT 1
    </select>
</mapper>

