<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyOrderMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyOrderPojo">
        <include refid="selectbillVo"/>
        where Buy_Order.id = #{key}
          and Buy_Order.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Buy_Order.id,
               Buy_Order.RefNo,
               Buy_Order.BillType,
               Buy_Order.BillTitle,
               Buy_Order.BillDate,
               Buy_Order.Groupid,
               Buy_Order.Payment,
               Buy_Order.OrderNo,
               Buy_Order.Arrivaladd,
               Buy_Order.TranSport,
               Buy_Order.LinkMan,
               Buy_Order.LinkTel,
               Buy_Order.Taxrate,
               Buy_Order.Operator,
               Buy_Order.Prepayments,
               Buy_Order.Summary,
               Buy_Order.CreateBy,
               Buy_Order.CreateByid,
               Buy_Order.CreateDate,
               Buy_Order.Lister,
               Buy_Order.Listerid,
               Buy_Order.ModifyDate,
               Buy_Order.Assessor,
               Buy_Order.Assessorid,
               Buy_Order.AssessDate,
               Buy_Order.BillTaxAmount,
               Buy_Order.BillTaxTotal,
               Buy_Order.BillAmount,
               Buy_Order.BillStateCode,
               Buy_Order.BillStateDate,
               Buy_Order.BillPlanDate,
               Buy_Order.ItemCount,
               Buy_Order.FinishCount,
               Buy_Order.DisannulCount,
               Buy_Order.PrintCount,
               Buy_Order.OaFlowMark,
               Buy_Order.InvoAmt,
               Buy_Order.InvoCount,
               Buy_Order.FirstAmt,
               Buy_Order.LastAmt,
               Buy_Order.Projectid,
               Buy_Order.ProjCode,
               Buy_Order.ProjName,
               Buy_Order.Custom1,
               Buy_Order.Custom2,
               Buy_Order.Custom3,
               Buy_Order.Custom4,
               Buy_Order.Custom5,
               Buy_Order.Custom6,
               Buy_Order.Custom7,
               Buy_Order.Custom8,
               Buy_Order.Custom9,
               Buy_Order.Custom10,
               Buy_Order.Deptid,
               Buy_Order.Tenantid,
               Buy_Order.TenantName,
               Buy_Order.Revision
        FROM App_Workgroup
                 RIGHT JOIN Buy_Order ON Buy_Order.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Storeid,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10,
               Buy_OrderItem.id,
               Buy_OrderItem.Pid,
               Buy_OrderItem.Goodsid,
               Buy_OrderItem.ItemCode,
               Buy_OrderItem.ItemName,
               Buy_OrderItem.ItemSpec,
               Buy_OrderItem.ItemUnit,
               Buy_OrderItem.Quantity,
               Buy_OrderItem.TaxPrice,
               Buy_OrderItem.TaxAmount,
               Buy_OrderItem.ItemTaxrate,
               Buy_OrderItem.TaxTotal,
               Buy_OrderItem.Price,
               Buy_OrderItem.Amount,
               Buy_OrderItem.PlanDate,
               Buy_OrderItem.MaxQty,
               Buy_OrderItem.FinishQty,
               Buy_OrderItem.Remark,
               Buy_OrderItem.CiteUid,
               Buy_OrderItem.CiteItemid,
               Buy_OrderItem.StateCode,
               Buy_OrderItem.StateDate,
               Buy_OrderItem.Closed,
               Buy_OrderItem.RowNum,
               Buy_OrderItem.OrgGoodsid,
               Buy_OrderItem.SubRate,
               Buy_OrderItem.MachUid,
               Buy_OrderItem.MachItemid,
               Buy_OrderItem.MachBatch,
               Buy_OrderItem.MachGroupid,
               Buy_OrderItem.MainPlanUid,
               Buy_OrderItem.MainPlanItemid,
               Buy_OrderItem.MrpUid,
               Buy_OrderItem.MrpItemid,
               Buy_OrderItem.VirtualItem,
               Buy_OrderItem.Customer,
               Buy_OrderItem.CustPO,
               Buy_OrderItem.BatchNo,
               Buy_OrderItem.DisannulMark,
               Buy_OrderItem.DisannulListerid,
               Buy_OrderItem.DisannulLister,
               Buy_OrderItem.DisannulDate,
               Buy_OrderItem.AttributeJson,
               Buy_OrderItem.SourceType,
               Buy_OrderItem.StdPrice,
               Buy_OrderItem.StdAmount,
               Buy_OrderItem.Rebate,
               Buy_OrderItem.PassedQty,
               Buy_OrderItem.InStoreQty,
               Buy_OrderItem.InvoQty,
               Buy_OrderItem.InvoClosed,
               Buy_OrderItem.AvgInvoAmt,
               Buy_OrderItem.AvgFirstAmt,
               Buy_OrderItem.AvgLastAmt,
               Buy_OrderItem.InvoAmt,
               Buy_OrderItem.Custom1,
               Buy_OrderItem.Custom2,
               Buy_OrderItem.Custom3,
               Buy_OrderItem.Custom4,
               Buy_OrderItem.Custom5,
               Buy_OrderItem.Custom6,
               Buy_OrderItem.Custom7,
               Buy_OrderItem.Custom8,
               Buy_OrderItem.Custom9,
               Buy_OrderItem.Custom10,
               Buy_OrderItem.Custom11,
               Buy_OrderItem.Custom12,
               Buy_OrderItem.Custom13,
               Buy_OrderItem.Custom14,
               Buy_OrderItem.Custom15,
               Buy_OrderItem.Custom16,
               Buy_OrderItem.Custom17,
               Buy_OrderItem.Custom18,
               Buy_OrderItem.Tenantid,
               Buy_OrderItem.Revision,
               Buy_Order.RefNo,
               Buy_Order.BillType,
               Buy_Order.BillTitle,
               Buy_Order.BillDate,
               Buy_Order.Payment,
               Buy_Order.OrderNo,
               Buy_Order.Arrivaladd,
               Buy_Order.TranSport,
               Buy_Order.LinkMan,
               Buy_Order.LinkTel,
               Buy_Order.Operator,
               Buy_Order.Prepayments,
               Buy_Order.CreateBy,
               Buy_Order.CreateDate,
               Buy_Order.ModifyDate,
               Buy_Order.Lister,
               Buy_Order.Assessor,
               Buy_Order.AssessDate,
               Buy_Order.Groupid,
               g2.GoodsName                               as MrpObjGoodsName,
               g2.GoodsUid                                as MrpObjGoodsUid
        FROM App_Workgroup
                 RIGHT JOIN Buy_Order ON Buy_Order.Groupid = App_Workgroup.id
                 RIGHT JOIN Buy_OrderItem ON Buy_OrderItem.Pid = Buy_Order.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Buy_OrderItem.Goodsid
                 LEFT JOIN Mat_Goods g2 ON Buy_OrderItem.MrpObjGoodsid = g2.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyOrderitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Buy_Order.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Buy_Order.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Buy_Order.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Buy_Order.billtype like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Buy_Order.billtitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Buy_Order.groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.payment != null">
            and Buy_Order.payment like concat('%',
                #{SearchPojo.payment}, '%')
        </if>
        <if test="SearchPojo.orderno != null">
            and Buy_Order.orderno like concat('%',
                #{SearchPojo.orderno}, '%')
        </if>
        <if test="SearchPojo.arrivaladd != null">
            and Buy_Order.arrivaladd like concat('%',
                #{SearchPojo.arrivaladd}, '%')
        </if>
        <if test="SearchPojo.transport != null">
            and Buy_Order.transport like concat('%',
                #{SearchPojo.transport}, '%')
        </if>
        <if test="SearchPojo.linkman != null">
            and Buy_Order.linkman like concat('%',
                #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.linktel != null">
            and Buy_Order.linktel like concat('%',
                #{SearchPojo.linktel}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Buy_Order.operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Buy_Order.summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Buy_Order.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Buy_Order.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Buy_Order.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Buy_Order.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Buy_Order.assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Buy_Order.assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null">
            and Buy_Order.billstatecode like concat('%',
                #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Buy_Order.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Buy_Order.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Buy_Order.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Buy_Order.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Buy_Order.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Buy_Order.custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Buy_Order.custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Buy_Order.custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Buy_Order.custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Buy_Order.custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Buy_Order.deptid like concat('%',
                #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Buy_Order.tenantname like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Buy_OrderItem.goodsid=
                #{SearchPojo.goodsid}
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Buy_OrderItem.itemcode like concat('%',
                #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Buy_OrderItem.itemname like concat('%',
                #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Buy_OrderItem.itemspec like concat('%',
                #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Buy_OrderItem.itemunit like concat('%',
                #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Buy_OrderItem.remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Buy_OrderItem.citeuid like concat('%',
                #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Buy_OrderItem.citeitemid like concat('%',
                #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Buy_OrderItem.statecode like concat('%',
                #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.orggoodsid != null and SearchPojo.orggoodsid != ''">
            and Buy_OrderItem.orggoodsid like concat('%',
                #{SearchPojo.orggoodsid}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Buy_OrderItem.machuid like concat('%',
                #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Buy_OrderItem.machitemid like concat('%',
                #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Buy_OrderItem.machgroupid like concat('%',
                #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Buy_OrderItem.mainplanuid like concat('%',
                #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Buy_OrderItem.mainplanitemid like concat('%',
                #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Buy_OrderItem.mrpuid like concat('%',
                #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Buy_OrderItem.mrpitemid like concat('%',
                #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Buy_OrderItem.customer like concat('%',
                #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Buy_OrderItem.custpo like concat('%',
                #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Buy_OrderItem.batchno like concat('%',
                #{SearchPojo.batchno}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Buy_Order.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Buy_Order.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Buy_Order.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Buy_Order.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.payment != null">
                or Buy_Order.Payment like concat('%', #{SearchPojo.payment}, '%')
            </if>
            <if test="SearchPojo.orderno != null">
                or Buy_Order.OrderNo like concat('%', #{SearchPojo.orderno}, '%')
            </if>
            <if test="SearchPojo.arrivaladd != null">
                or Buy_Order.Arrivaladd like concat('%', #{SearchPojo.arrivaladd}, '%')
            </if>
            <if test="SearchPojo.transport != null">
                or Buy_Order.TranSport like concat('%', #{SearchPojo.transport}, '%')
            </if>
            <if test="SearchPojo.linkman != null">
                or Buy_Order.LinkMan like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.linktel != null">
                or Buy_Order.LinkTel like concat('%', #{SearchPojo.linktel}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Buy_Order.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Buy_Order.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Buy_Order.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Buy_Order.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Buy_Order.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Buy_Order.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Buy_Order.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Buy_Order.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null">
                or Buy_Order.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Buy_Order.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Buy_Order.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Buy_Order.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Buy_Order.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Buy_Order.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Buy_Order.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Buy_Order.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Buy_Order.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Buy_Order.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Buy_Order.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Buy_Order.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Buy_Order.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Buy_OrderItem.goodsid=#{SearchPojo.goodsid}
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Buy_OrderItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Buy_OrderItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Buy_OrderItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Buy_OrderItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Buy_OrderItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Buy_OrderItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Buy_OrderItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Buy_OrderItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.orggoodsid != null and SearchPojo.orggoodsid != ''">
                or Buy_OrderItem.OrgGoodsid like concat('%', #{SearchPojo.orggoodsid}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Buy_OrderItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Buy_OrderItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Buy_OrderItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Buy_OrderItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Buy_OrderItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Buy_OrderItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Buy_OrderItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Buy_OrderItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Buy_OrderItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Buy_OrderItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyOrderPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Buy_Order.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Buy_Order.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Buy_Order.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Buy_Order.BillType like concat('%',
                #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Buy_Order.BillTitle like concat('%',
                #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Buy_Order.Groupid like concat('%',
                #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.payment != null">
            and Buy_Order.Payment like concat('%',
                #{SearchPojo.payment}, '%')
        </if>
        <if test="SearchPojo.orderno != null">
            and Buy_Order.OrderNo like concat('%',
                #{SearchPojo.orderno}, '%')
        </if>
        <if test="SearchPojo.arrivaladd != null">
            and Buy_Order.Arrivaladd like concat('%',
                #{SearchPojo.arrivaladd}, '%')
        </if>
        <if test="SearchPojo.transport != null">
            and Buy_Order.TranSport like concat('%',
                #{SearchPojo.transport}, '%')
        </if>
        <if test="SearchPojo.linkman != null">
            and Buy_Order.LinkMan like concat('%',
                #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.linktel != null">
            and Buy_Order.LinkTel like concat('%',
                #{SearchPojo.linktel}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Buy_Order.Operator like concat('%',
                #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Buy_Order.Summary like concat('%',
                #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Buy_Order.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Buy_Order.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Buy_Order.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Buy_Order.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Buy_Order.Assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Buy_Order.Assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null">
            and Buy_Order.BillStateCode like concat('%',
                #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Buy_Order.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Buy_Order.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Buy_Order.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Buy_Order.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Buy_Order.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Buy_Order.Custom6 like concat('%',
                #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Buy_Order.Custom7 like concat('%',
                #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Buy_Order.Custom8 like concat('%',
                #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Buy_Order.Custom9 like concat('%',
                #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Buy_Order.Custom10 like concat('%',
                #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Buy_Order.Deptid like concat('%',
                #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Buy_Order.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Buy_Order.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Buy_Order.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Buy_Order.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Buy_Order.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.payment != null">
                or Buy_Order.Payment like concat('%', #{SearchPojo.payment}, '%')
            </if>
            <if test="SearchPojo.orderno != null">
                or Buy_Order.OrderNo like concat('%', #{SearchPojo.orderno}, '%')
            </if>
            <if test="SearchPojo.arrivaladd != null">
                or Buy_Order.Arrivaladd like concat('%', #{SearchPojo.arrivaladd}, '%')
            </if>
            <if test="SearchPojo.transport != null">
                or Buy_Order.TranSport like concat('%', #{SearchPojo.transport}, '%')
            </if>
            <if test="SearchPojo.linkman != null">
                or Buy_Order.LinkMan like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.linktel != null">
                or Buy_Order.LinkTel like concat('%', #{SearchPojo.linktel}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Buy_Order.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Buy_Order.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Buy_Order.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Buy_Order.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Buy_Order.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Buy_Order.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Buy_Order.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Buy_Order.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null">
                or Buy_Order.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Buy_Order.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Buy_Order.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Buy_Order.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Buy_Order.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Buy_Order.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Buy_Order.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Buy_Order.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Buy_Order.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Buy_Order.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Buy_Order.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Buy_Order.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Buy_Order.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Buy_Order(id, RefNo, BillType, BillTitle, BillDate, Groupid, Payment, OrderNo, Arrivaladd,
                              TranSport, LinkMan, LinkTel, Taxrate, Operator, Prepayments, Summary, CreateBy,
                              CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate,
                              BillTaxAmount, BillTaxTotal, BillAmount, BillStateCode, BillStateDate, BillPlanDate,
                              ItemCount, FinishCount, DisannulCount, PrintCount, OaFlowMark, InvoAmt,InvoCount, FirstAmt, LastAmt,
                              Projectid, ProjCode, ProjName, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6,
                              Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{groupid}, #{payment}, #{orderno},
                #{arrivaladd}, #{transport}, #{linkman}, #{linktel}, #{taxrate}, #{operator}, #{prepayments},
                #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
                #{assessor}, #{assessorid}, #{assessdate}, #{billtaxamount}, #{billtaxtotal}, #{billamount},
                #{billstatecode}, #{billstatedate}, #{billplandate}, #{itemcount}, #{finishcount}, #{disannulcount},
                #{printcount}, #{oaflowmark}, #{invoamt}, #{invocount}, #{firstamt}, #{lastamt}, #{projectid}, #{projcode},
                #{projname}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_Order
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="payment != null">
                Payment =#{payment},
            </if>
            <if test="orderno != null">
                OrderNo =#{orderno},
            </if>
            <if test="arrivaladd != null">
                Arrivaladd =#{arrivaladd},
            </if>
            <if test="transport != null">
                TranSport =#{transport},
            </if>
            <if test="linkman != null">
                LinkMan =#{linkman},
            </if>
            <if test="linktel != null">
                LinkTel =#{linktel},
            </if>
            <if test="taxrate != null">
                Taxrate =#{taxrate},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="prepayments != null">
                Prepayments =#{prepayments},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billstatecode != null">
                BillStateCode =#{billstatecode},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="billplandate != null">
                BillPlanDate =#{billplandate},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="invoamt != null">
                InvoAmt =#{invoamt},
            </if>
            <if test="invocount != null">
                InvoCount =#{invocount},
            </if>
            <if test="firstamt != null">
                FirstAmt =#{firstamt},
            </if>
            <if test="lastamt != null">
                LastAmt =#{lastamt},
            </if>
            <if test="projectid != null">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null">
                ProjName =#{projname},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_Order
        where id = #{key}
        and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Buy_Order
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>
    <!--    刷新发货完成数 Eric 20220722-->
    <update id="updateDisannulCount">
        update Buy_Order
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Buy_OrderItem
                                     where Buy_OrderItem.Pid = #{key}
                                       and Buy_OrderItem.Tenantid = #{tid}
                                       and Buy_OrderItem.DisannulMark = 1), 0)
        where id = #{key}
        and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateFinishCount">
        update Buy_Order
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Buy_OrderItem
                                   where Buy_OrderItem.Pid = #{key}
                                     and Buy_OrderItem.Tenantid = #{tid}
                                     and (Buy_OrderItem.Closed = 1
                                       or Buy_OrderItem.FinishQty >= Buy_OrderItem.Quantity)), 0)
        where id = #{key}
        and Tenantid = #{tid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BuyOrderPojo">
        select
        id
        from Buy_OrderItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updatePlanItemBuyQtyAndFinishQty">
        update Buy_PlanItem
        SET BuyQty    =COALESCE((SELECT SUM(Buy_OrderItem.quantity)
                                 FROM Buy_OrderItem
                                 where Buy_OrderItem.citeitemid = #{key}
                                   and Buy_OrderItem.DisannulMark = 0
                                   and Buy_OrderItem.Tenantid = #{tid}), 0)
          , FinishQty =COALESCE((SELECT SUM(Buy_OrderItem.FinishQty)
                                 FROM Buy_OrderItem
                                 where Buy_OrderItem.citeitemid = #{key}
                                   and Buy_OrderItem.DisannulMark = 0
                                   and Buy_OrderItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updatePlanBuyCountAndFinishCount">
        update Buy_Plan
        SET BuyCount    =COALESCE((SELECT COUNT(0)
                                   FROM Buy_PlanItem
                                   where Buy_PlanItem.Pid = (SELECT Pid FROM Buy_PlanItem where id = #{key})
                                     and Buy_PlanItem.Tenantid = #{tid}
                                     and (Buy_PlanItem.Closed = 1
                                       or Buy_PlanItem.BuyQty >= Buy_PlanItem.Quantity)), 0)
          , FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Buy_PlanItem
                                   where Buy_PlanItem.Pid = (SELECT Pid FROM Buy_PlanItem where id = #{key})
                                     and Buy_PlanItem.Tenantid = #{tid}
                                     and (Buy_PlanItem.Closed = 1
                                       or Buy_PlanItem.FinishQty >= Buy_PlanItem.Quantity)), 0)
        where id = (SELECT Pid FROM Buy_PlanItem where id = #{key})
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateMrpBuyOrderFinish">
        update Wk_MrpItem
        SET BuyOrderQty =COALESCE((SELECT SUM(Buy_OrderItem.quantity)
                                   FROM Buy_OrderItem
                                            LEFT OUTER JOIN Buy_Order
                                                            ON Buy_OrderItem.pid = Buy_Order.id
                                   where Buy_Order.BillType = 'MRP需求'
                                     and Buy_OrderItem.MrpUid = #{refno}
                                     and Buy_OrderItem.MrpItemid = #{key}
                                     and Buy_OrderItem.DisannulMark = 0
                                     and Buy_OrderItem.Closed = 0
                                     and Buy_OrderItem.Tenantid = #{tid}), 0)
        where id = #{key}
        and Tenantid = #{tid}
    </update>

    <!--  查询往来单位是否被引用  -->
    <select id="getItemCiteBillName" resultType="string">
        (SELECT '采购验收' as billname From Buy_FinishingItem where OrderItemid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '采购预付' as billname
         From Buy_PrepaymentsItem
         where OrderBillid = #{pid}
           and Tenantid = #{tid}
         LIMIT 1)
    </select>


    <!--    刷新销售待出数 Eric 20211213-->
    <update id="updateGoodsBusRemQty">
        update Mat_Goods
        SET BusRemQty = COALESCE((SELECT SUM(Bus_MachiningItem.Quantity - Bus_MachiningItem.FinishQty)
                                  FROM Bus_MachiningItem
                                           LEFT OUTER JOIN Bus_Machining ON Bus_MachiningItem.pid =
                                                                            Bus_Machining.id
                                  where Bus_MachiningItem.Goodsid = #{key}
                                    and Bus_MachiningItem.Quantity &gt; Bus_MachiningItem.FinishQty
                                    and Bus_MachiningItem.Closed = 0
                                    and Bus_MachiningItem.DisannulMark = 0
                                    and Bus_Machining.Tenantid = #{tid}), 0) +
                        COALESCE((SELECT SUM(CASE
                                                 WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货')
                                                     THEN Bus_DelieryItem.Quantity - Bus_DelieryItem.FinishQty
                                                 ELSE Bus_DelieryItem.FinishQty - Bus_DelieryItem.Quantity END)
                                  FROM Bus_DelieryItem
                                           LEFT OUTER JOIN Bus_Deliery ON Bus_DelieryItem.pid =
                                                                          Bus_Deliery.id
                                  where Bus_Deliery.BillType IN ('发出商品', '其他发货', '订单退货', '其他退货')
                                    and Bus_DelieryItem.Goodsid = #{key}
                                    and Bus_DelieryItem.Quantity &gt; Bus_DelieryItem.FinishQty
                                    and Bus_DelieryItem.FinishClosed = 0
                                    and Bus_DelieryItem.DisannulMark = 0
                                    and Bus_Deliery.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <update id="updateMachingBuyQuantity">
        update Bus_MachiningItem
        SET BuyQuantity =COALESCE((SELECT SUM(Buy_OrderItem.quantity)
                                   FROM Buy_OrderItem
                                            LEFT OUTER JOIN Buy_Order
                                                            ON Buy_OrderItem.pid = Buy_Order.id
                                   where Buy_Order.BillType = '销售订单'
                                     and Buy_OrderItem.MachItemid = #{key}
                                     and Buy_OrderItem.DisannulMark = 0
                                     and Buy_OrderItem.Tenantid = #{tid}), 0)
        where id = #{key}
        and Tenantid = #{tid}
    </update>



    <update id="updateMachingMatBuyQty">
        update Bus_MachiningItem
        SET MatBuyQty =COALESCE((SELECT SUM(Buy_OrderItem.quantity)
        FROM Buy_OrderItem
        LEFT OUTER JOIN Buy_Order
        ON Buy_OrderItem.pid = Buy_Order.id
        where Buy_OrderItem.MachItemid = #{key}
        and Buy_OrderItem.DisannulMark = 0
        and Buy_OrderItem.Tenantid = #{tid}), 0)
        where id = #{key}
        and Tenantid = #{tid}
    </update>

    <select id="getWorkgroupInfo" resultType="java.util.Map">
        SELECT
        App_Workgroup.GroupClass,
        App_Workgroup.Linkman,
        App_Workgroup.Telephone,
        App_Workgroup.GroupFax,
        App_Workgroup.GroupAdd,
        App_Workgroup.Remark,
        App_Workgroup.InvalidDate,
        App_Workgroup.GroupType,
        App_Workgroup.CreditDUint,
        App_Workgroup.CreditDQuantity,
        App_Workgroup.CreditCUint,
        App_Workgroup.CreditCQuantity,
        App_Workgroup.Mobile,
        App_Workgroup.LinkmanS,
        App_Workgroup.TelephoneS,
        App_Workgroup.MobileS,
        App_Workgroup.Country,
        App_Workgroup.Province,
        App_Workgroup.GroupZip,
        App_Workgroup.DeliverAdd,
        App_Workgroup.InvoiceAdd,
        App_Workgroup.Seller,
        App_Workgroup.GroupLabel,
        App_Workgroup.GroupLevel,
        App_Workgroup.GroupState,
        App_Workgroup.Source,
        App_Workgroup.Credit,
        App_Workgroup.PaymentMethod,
        App_Workgroup.CreditCode,
        App_Workgroup.DepositBank,
        App_Workgroup.BankAccount,
        App_Workgroup.City,
        App_Workgroup.County,
        App_Workgroup.Street,
        App_Workgroup.LocalAdd
        FROM App_Workgroup
        where App_Workgroup.id = #{groupid}
        and App_Workgroup.Tenantid = #{tid}
    </select>

    <update id="updateOaflowmark">
        update Buy_Order
        SET OaFlowMark = #{oaflowmark}
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>
</mapper>

