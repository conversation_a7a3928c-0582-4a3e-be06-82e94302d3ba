<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.D01MBIR1Mapper">
    <!--    /*客户发货单金额排名*/ count表示该客户下的发货单数(主表)-->
    <select id="getSumAmtByGroupMax" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select App_Workgroup.GroupName                                         as name,
               App_Workgroup.GroupUid                                          as code,
               sum(CASE
                       WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发') THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END)                 as value,
               count(distinct Bus_Deliery.id)                                  as valueb,
               sum(IF(Mat_Goods.VirtualItem = 0, Bus_DelieryItem.Quantity, 0)) as valuec
        FROM Bus_Deliery
                 RIGHT JOIN Bus_DelieryItem
                            ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods on Mat_Goods.id = Bus_DelieryItem.Goodsid
        where Bus_DelieryItem.disannulmark = 0
        <if test="filterstr != null">
            ${filterstr}


        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
            </if>
            <if test="SearchType == 1">
                <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
                    <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
                    <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
                </trim>
            </if>
        </if>
        and Bus_Deliery.Tenantid = #{tenantid}
        and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        and Bus_Deliery.BillType IN ('发出商品', '订单退货', '其他发货', '其他退货', '退货返工', '返工补发')
        group by App_Workgroup.GroupUid, App_Workgroup.GroupName
        order by value desc
    </select>

    <select id="getSumAmtByGroupMaxFirstUid" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select LEFT(App_Workgroup.GroupUid, 1)                                 as code,
               sum(CASE
                       WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发') THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END)                 as value,
               count(distinct Bus_Deliery.id)                                  as valueb,
               sum(IF(Mat_Goods.VirtualItem = 0, Bus_DelieryItem.Quantity, 0)) as valuec
        FROM Bus_Deliery
                 RIGHT JOIN Bus_DelieryItem
                            ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods on Mat_Goods.id = Bus_DelieryItem.Goodsid
        where Bus_DelieryItem.disannulmark = 0
        <if test="filterstr != null">
            ${filterstr}


        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
            </if>
            <if test="SearchType == 1">
                <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
                    <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
                    <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
                </trim>
            </if>
        </if>
        and Bus_Deliery.Tenantid = #{tenantid}
        and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        and Bus_Deliery.BillType IN ('发出商品', '订单退货', '其他发货', '其他退货', '退货返工', '返工补发')
        AND CHAR_LENGTH(App_Workgroup.GroupUid) > 0
        group by code
        order by value desc
    </select>


    <!--    /*客户销售订单金额排名*/  count表示该客户下的订单数(主表)-->
    <select id="getSumAmtByGroupMaxMach" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select App_Workgroup.GroupName                                           as name,
               App_Workgroup.GroupUid                                            as code,
               sum(Bus_MachiningItem.TaxAmount)                                  as value,
               count(distinct Bus_Machining.id)                                  as valueb,
               sum(IF(Mat_Goods.VirtualItem = 0, Bus_MachiningItem.Quantity, 0)) as valuec
        FROM Bus_Machining
                 RIGHT JOIN Bus_MachiningItem ON Bus_MachiningItem.Pid = Bus_Machining.id
                 LEFT JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods on Mat_Goods.id = Bus_MachiningItem.Goodsid
        where Bus_MachiningItem.disannulmark = 0
        <if test="filterstr != null">
            ${filterstr}


        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
            </if>
            <if test="SearchType == 1">
                <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
                    <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
                    <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
                </trim>
            </if>
        </if>
        and Bus_MachiningItem.Closed = 0
        and Bus_Machining.Tenantid = #{tenantid}
        and (Bus_Machining.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        group by App_Workgroup.Abbreviate, App_Workgroup.GroupName, App_Workgroup.GroupUid
        order by value desc
    </select>

    <!--    /*客户销售订单金额排名*/  count表示该客户下的订单数(主表)-->
    <select id="getSumAmtByGroupMaxMachFirstUid" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select LEFT(App_Workgroup.GroupUid, 1)                                   as code,
               sum(Bus_MachiningItem.TaxAmount)                                  as value,
               count(distinct Bus_Machining.id)                                  as valueb,
               sum(IF(Mat_Goods.VirtualItem = 0, Bus_MachiningItem.Quantity, 0)) as valuec
        FROM Bus_Machining
                 RIGHT JOIN Bus_MachiningItem
                            ON Bus_MachiningItem.Pid = Bus_Machining.id
                 LEFT JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods on Mat_Goods.id = Bus_MachiningItem.Goodsid
        where Bus_MachiningItem.disannulmark = 0
        <if test="filterstr != null">
            ${filterstr}


        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
            </if>
            <if test="SearchType == 1">
                <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
                    <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
                    <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
                </trim>
            </if>
        </if>
        and Bus_MachiningItem.Closed = 0
        and Bus_Machining.Tenantid = #{tenantid}
        and (Bus_Machining.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        AND CHAR_LENGTH(App_Workgroup.GroupUid) > 0
        group by code
        order by value desc
    </select>


    <select id="getSumAmtByGroupMaxFirstUid6Month" resultType="inks.common.core.domain.ChartPojo">
        select DATE_FORMAT(Bus_Deliery.BillDate, '%Y-%m')                      AS name,
               LEFT(App_Workgroup.GroupUid, 1)                                 as code,
               sum(CASE
                       WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发') THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END)                 as value,
               count(distinct Bus_Deliery.id)                                  as valueb,
               sum(IF(Mat_Goods.VirtualItem = 0, Bus_DelieryItem.Quantity, 0)) as valuec
        FROM Bus_Deliery
                 RIGHT JOIN Bus_DelieryItem
                            ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods on Mat_Goods.id = Bus_DelieryItem.Goodsid
        where Bus_DelieryItem.disannulmark = 0
          and Bus_Deliery.Tenantid = #{queryParam.tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{queryParam.DateRange.StartDate}
            and #{queryParam.DateRange.EndDate})
          and Bus_Deliery.BillType IN ('发出商品', '订单退货', '其他发货', '其他退货', '退货返工', '返工补发')
          AND CHAR_LENGTH(App_Workgroup.GroupUid) > 0
          AND Bus_Deliery.BillDate >= DATE_SUB(CURRENT_DATE, INTERVAL 5 MONTH)
        <if test="firstuid != null and firstuid != ''">
            AND LEFT(App_Workgroup.GroupUid, 1) = #{firstuid}
        </if>
        group by name, code
        order by name desc
    </select>

    <select id="getSumAmtByGroupMaxMachFirstUid6Month" resultType="inks.common.core.domain.ChartPojo">
        select DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m')                      AS name,
               LEFT(App_Workgroup.GroupUid, 1)                                   as code,
               sum(Bus_MachiningItem.TaxAmount)                                  as value,
               count(distinct Bus_Machining.id)                                  as valueb,
               sum(IF(Mat_Goods.VirtualItem = 0, Bus_MachiningItem.Quantity, 0)) as valuec
        FROM Bus_Machining
                 RIGHT JOIN Bus_MachiningItem
                            ON Bus_MachiningItem.Pid = Bus_Machining.id
                 LEFT JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods on Mat_Goods.id = Bus_MachiningItem.Goodsid
        where Bus_MachiningItem.disannulmark = 0
          and Bus_MachiningItem.Closed = 0
          and Bus_Machining.Tenantid = #{queryParam.tenantid}
          and (Bus_Machining.BillDate BETWEEN #{queryParam.DateRange.StartDate}
            and #{queryParam.DateRange.EndDate})
          AND CHAR_LENGTH(App_Workgroup.GroupUid) > 0
          AND Bus_Machining.BillDate >= DATE_SUB(CURRENT_DATE, INTERVAL 5 MONTH)
        <if test="firstuid != null and firstuid != ''">
            AND LEFT(App_Workgroup.GroupUid, 1) = #{firstuid}
        </if>
        group by name, code
        order by name desc
    </select>


    <select id="getSumAmtBySalesman12Month" resultType="inks.common.core.domain.ChartPojo">
        select DATE_FORMAT(Bus_Deliery.BillDate, '%Y-%m')                      AS name,
               Bus_Deliery.Salesman                                            as code,
               sum(CASE
                       WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发') THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END)                 as value,
               count(distinct Bus_Deliery.id)                                  as valueb,
               sum(IF(Mat_Goods.VirtualItem = 0, Bus_DelieryItem.Quantity, 0)) as valuec
        FROM Bus_Deliery
                 RIGHT JOIN Bus_DelieryItem
                            ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods on Mat_Goods.id = Bus_DelieryItem.Goodsid
        where Bus_DelieryItem.disannulmark = 0
          and Bus_Deliery.Tenantid = #{queryParam.tenantid}
          and Bus_Deliery.BillType IN ('发出商品', '订单退货', '其他发货', '其他退货', '退货返工', '返工补发')
          AND Bus_Deliery.BillDate >= CONCAT(YEAR(CURRENT_DATE), '-01-01')
        group by name, code
        order by name desc
    </select>

    <select id="getSumAmtBySalesmanMach12Month" resultType="inks.common.core.domain.ChartPojo">
        select DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m')                      AS name,
               Bus_Machining.Salesman                                            as code,
               sum(Bus_MachiningItem.TaxAmount)                                  as value,
               count(distinct Bus_Machining.id)                                  as valueb,
               sum(IF(Mat_Goods.VirtualItem = 0, Bus_MachiningItem.Quantity, 0)) as valuec
        FROM Bus_Machining
                 RIGHT JOIN Bus_MachiningItem
                            ON Bus_MachiningItem.Pid = Bus_Machining.id
                 LEFT JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods on Mat_Goods.id = Bus_MachiningItem.Goodsid
        where Bus_MachiningItem.disannulmark = 0
          and Bus_MachiningItem.Closed = 0
          and Bus_Machining.Tenantid = #{queryParam.tenantid}
          AND Bus_Machining.BillDate >= CONCAT(YEAR(CURRENT_DATE), '-01-01')
        group by name, code
        order by name desc
    </select>


    <!--    获取【销售收款单+预收款】客户金额排名 UNION ALL将两个查询的结果合并成一个结果集，再结果集group by name累加value-->
    <select id="getSumAmtByDepositAndReceipt" resultType="inks.common.core.domain.ChartPojo">
        SELECT name, SUM(value) AS value
        FROM (SELECT App_Workgroup.GroupName     AS name,
                     SUM(Bus_DepositItem.Amount) AS value
              FROM Bus_Deposit
                       RIGHT JOIN Bus_DepositItem ON Bus_DepositItem.Pid = Bus_Deposit.id
                       LEFT JOIN App_Workgroup ON Bus_Deposit.Groupid = App_Workgroup.id
              WHERE Bus_Deposit.Tenantid = #{queryParam.tenantid}
                AND (Bus_Deposit.BillDate BETWEEN #{queryParam.DateRange.StartDate} AND #{queryParam.DateRange.EndDate})
              GROUP BY App_Workgroup.Abbreviate, App_Workgroup.GroupName
              UNION ALL
              SELECT App_Workgroup.GroupName     AS name,
                     SUM(Bus_ReceiptItem.Amount) AS value
              FROM Bus_Receipt
                       RIGHT JOIN Bus_ReceiptItem ON Bus_ReceiptItem.Pid = Bus_Receipt.id
                       LEFT JOIN App_Workgroup ON Bus_Receipt.Groupid = App_Workgroup.id
              WHERE Bus_Receipt.Tenantid = #{queryParam.tenantid}
                AND (Bus_Receipt.BillDate BETWEEN #{queryParam.DateRange.StartDate} AND #{queryParam.DateRange.EndDate})
              GROUP BY App_Workgroup.Abbreviate, App_Workgroup.GroupName) AS combined_results
        GROUP BY name
        ORDER BY value DESC
    </select>

    <!--    获取发货单货品金额排名-->
    <select id="getSumAmtByGoodsMax" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select Mat_Goods.GoodsName                             as name,
               sum(CASE
                       WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发') THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as value,
               Mat_Goods.id                                    as code
        FROM Bus_Deliery
                 RIGHT JOIN Bus_DelieryItem ON Bus_Deliery.id = Bus_DelieryItem.Pid
                 LEFT JOIN Mat_Goods ON Bus_DelieryItem.Goodsid = Mat_Goods.id
        where Bus_DelieryItem.disannulmark = 0
          and Bus_Deliery.Tenantid = #{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
          and Bus_Deliery.BillType IN ('发出商品', '订单退货', '其他发货', '其他退货', '退货返工', '返工补发')
        group by Bus_DelieryItem.Goodsid, Mat_Goods.GoodsName
        order by value desc
    </select>
    <select id="getSumAmtByGoodsMaxOnlyMach" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select Mat_Goods.GoodsName              as name,
               sum(Bus_MachiningItem.TaxAmount) as value,
               Mat_Goods.GoodsUid               as code
        FROM Bus_Machining
                 RIGHT JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid
                 LEFT JOIN Mat_Goods ON Bus_MachiningItem.Goodsid = Mat_Goods.id
        where Bus_MachiningItem.disannulmark = 0
          and Bus_Machining.Tenantid = #{tenantid}
          and (Bus_Machining.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        group by Bus_MachiningItem.Goodsid, Mat_Goods.GoodsName
        order by value desc
    </select>


    <!--    获取指定一个销售单货品的金额-->
    <select id="getSumAmtByGoodsMach" resultType="inks.common.core.domain.ChartPojo">
        select sum(Bus_MachiningItem.TaxAmount) as value
        FROM Bus_Machining
                 RIGHT JOIN Bus_MachiningItem ON Bus_MachiningItem.pid = Bus_Machining.id
                 LEFT JOIN Mat_Goods ON Bus_MachiningItem.Goodsid = Mat_Goods.id
        where Bus_MachiningItem.DisannulMark = 0
          and Bus_MachiningItem.Tenantid = #{tid}
          and Bus_MachiningItem.Goodsid = #{goodsid}
    </select>


    <select id="getSumAmtBySalesman" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT Bus_Deliery.Salesman                            AS name,
               sum(CASE
                       WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发') THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as value
        FROM Bus_Deliery
                 RIGHT JOIN Bus_DelieryItem
                            ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
        where Bus_DelieryItem.disannulmark = 0
          and Bus_Deliery.Tenantid = #{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
          and Bus_Deliery.BillType IN ('发出商品', '订单退货', '其他发货', '其他退货', '退货返工', '返工补发')
        GROUP BY Bus_Deliery.Salesman
        ORDER BY value
    </select>
    <select id="getSumAmtBySalesmanMach" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT Bus_Machining.Salesman           AS name,
               sum(Bus_MachiningItem.TaxAmount) as value
        FROM Bus_Machining
                 RIGHT JOIN Bus_MachiningItem
                            ON Bus_MachiningItem.Pid = Bus_Machining.id
                 LEFT JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
        where Bus_MachiningItem.disannulmark = 0
          and Bus_MachiningItem.Closed = 0
          and Bus_Machining.Tenantid = #{tenantid}
          and (Bus_Machining.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        GROUP BY Bus_Machining.Salesman
        ORDER BY value
    </select>


    <select id="getSumAmtByYear" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select date_format(Bus_Deliery.BillDate, '%Y-%m')         name,
               sum(CASE
                       WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发') THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as value
        FROM Bus_Deliery
                 RIGHT JOIN Bus_DelieryItem
                            ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
        where Bus_DelieryItem.disannulmark = 0
          and Bus_Deliery.Tenantid = #{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
          and Bus_Deliery.BillType IN ('发出商品', '订单退货', '其他发货', '其他退货', '退货返工', '返工补发')
        group by date_format(Bus_Deliery.BillDate, '%Y-%m')
    </select>
    <select id="getSumAmtByMonth" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select date_format(Bus_Deliery.BillDate, '%Y-%m-%d')      name,
               sum(CASE
                       WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发') THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as value
        FROM Bus_Deliery
                 RIGHT JOIN Bus_DelieryItem
                            ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
        where Bus_DelieryItem.disannulmark = 0
          and Bus_Deliery.Tenantid = #{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
          and Bus_Deliery.BillType IN ('发出商品', '订单退货', '其他发货', '其他退货', '退货返工', '返工补发')
        group by date_format(Bus_Deliery.BillDate, '%Y-%m-%d')
    </select>


    <select id="getSumAmtByMonthMach" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select date_format(Bus_Machining.BillDate, '%Y-%m-%d') name,
               sum(Bus_MachiningItem.TaxAmount) as             value
        FROM Bus_Machining
                 RIGHT JOIN Bus_MachiningItem
                            ON Bus_MachiningItem.Pid = Bus_Machining.id
                 LEFT JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
        where Bus_MachiningItem.disannulmark = 0
          and Bus_MachiningItem.Closed = 0
          and Bus_Machining.Tenantid = #{tenantid}
          and (Bus_Machining.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        group by date_format(Bus_Machining.BillDate, '%Y-%m-%d')
    </select>


    <select id="getSumAmtByDay" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select date_format(Bus_Deliery.BillDate, '%Y-%m-%d')      name,
               sum(CASE
                       WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发') THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as value
        FROM Bus_Deliery
                 RIGHT JOIN Bus_DelieryItem
                            ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
        where Bus_DelieryItem.disannulmark = 0
          and Bus_Deliery.Tenantid = #{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
          and Bus_Deliery.BillType IN ('发出商品', '订单退货', '其他发货', '其他退货', '退货返工', '返工补发')
        group by date_format(Bus_Deliery.BillDate, '%Y-%m-%d')
    </select>
    <select id="getTagSumAmtQtyByDate" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select sum(CASE
                       WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发') THEN Bus_DelieryItem.TaxAmount
                       ELSE 0 - Bus_DelieryItem.TaxAmount END) as value,
               sum(CASE
                       WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货', '返工补发') THEN Bus_DelieryItem.Quantity
                       ELSE 0 - Bus_DelieryItem.Quantity END)  as valueb,
               count(0)                                        as valuec
        FROM Bus_Deliery
                 RIGHT JOIN Bus_DelieryItem
                            ON Bus_DelieryItem.Pid = Bus_Deliery.id
                 LEFT JOIN App_Workgroup ON Bus_Deliery.Groupid = App_Workgroup.id
        where Bus_DelieryItem.disannulmark = 0
          and Bus_Deliery.Tenantid = #{tenantid}
          and (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
          and Bus_Deliery.BillType IN ('发出商品', '订单退货', '其他发货', '其他退货', '退货返工', '返工补发')
    </select>
    <select id="getTagSumAmtQtyByDateMach" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select sum(Bus_MachiningItem.TaxAmount) as value,
               sum(Bus_MachiningItem.Quantity)  as valueb,
               count(0)                         as valuec
        FROM Bus_Machining
                 RIGHT JOIN Bus_MachiningItem
                            ON Bus_MachiningItem.Pid = Bus_Machining.id
                 LEFT JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
        where Bus_MachiningItem.disannulmark = 0
          and Bus_MachiningItem.Closed = 0
          and Bus_Machining.Tenantid = #{tenantid}
          and (Bus_Machining.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
    </select>


    <select id="getTagSumAmtQtyByMonth" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT DATE_FORMAT(Bus_Machining.BillDate, '%Y-%m') AS NAME,
               IFNULL(SUM(Bus_MachiningItem.TaxAmount), 0)  AS
                                                               VALUE
        FROM Bus_Machining
                 LEFT JOIN Bus_MachiningItem
                           ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE DATE_FORMAT(Bus_Machining.BillDate
                  , '%Y-%m') = DATE_FORMAT(CURDATE()
                  , '%Y-%m')
          and Bus_MachiningItem.Tenantid = #{tenantid}
        GROUP BY DATE_FORMAT(
                         Bus_Machining.BillDate,
                         '%Y-%m'
                 )
    </select>
    <select id="getTagSumAmtByMonth"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT IFNULL(sum(Bus_Invoice.amount), 0) AS
                                                     valueb
             , (
            CASE
                WHEN App_Workgroup.Abbreviate IS NULL
                    OR App_Workgroup.Abbreviate = '' THEN
                    App_Workgroup.GroupName
                ELSE Abbreviate
                END
            )                                     AS NAME
        FROM Bus_Invoice
                 LEFT OUTER JOIN App_Workgroup ON Bus_Invoice.Groupid = App_Workgroup.id
        WHERE DATE_FORMAT(Bus_Invoice.BillDate, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
          AND DisannulMark != 1
          AND Bus_Invoice.Tenantid = #{tid}
        GROUP BY App_Workgroup.Abbreviate,
                 App_Workgroup.GroupName
    </select>

    <select id="getPageList"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT DATE_FORMAT(now(), '%Y-%m-%d') AS NAME,
               count(1)                       AS
                                                 VALUE
        FROM Bus_Machining
                 LEFT JOIN Bus_MachiningItem
                           ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE Bus_MachiningItem.Quantity
            > Bus_MachiningItem.OutMatQty
          AND DATE_FORMAT(Bus_MachiningItem.ItemOrgDate
                  , '%Y-%m-%d') &gt;= DATE_FORMAT(
                      now()
                  , '%Y-%m-%d')
          and Bus_MachiningItem.Tenantid = #{tid}
    </select>
    <select id="getSumByGoodsMax" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT Mat_Goods.GoodsName              AS NAME,
               SUM(Bus_MachiningItem.TaxAmount) AS
                                                   valueb
        FROM Mat_Goods
                 RIGHT OUTER JOIN App_Workgroup
                 RIGHT OUTER JOIN Bus_Machining ON App_Workgroup.id = Bus_Machining.Groupid
                 RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid
                                  ON Mat_Goods.id = Bus_MachiningItem.Goodsid
        WHERE Bus_MachiningItem.Tenantid = #{Tenantid}
        GROUP BY Mat_Goods.GoodsName
        ORDER BY VALUE desc
        LIMIT 0,#{PageSize}
    </select>


    <select id="getCountMachItemOnline" resultType="java.lang.Integer">
        SELECT count(1)
        FROM Bus_MachiningItem
        WHERE Bus_MachiningItem.Quantity > Bus_MachiningItem.OutQuantity
          AND Bus_MachiningItem.Closed = 0
          AND Bus_MachiningItem.DisannulMark = 0
          AND Bus_MachiningItem.Tenantid = #{tid}
    </select>

    <!--    订单完成率-->
    <select id="getMachFinishRate" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT count(1)                                     as value,
               (select count(1)
                FROM Bus_Machining
                WHERE FinishCount = ItemCount
                  and Bus_Machining.Tenantid = #{tenantid}) as valueb
        FROM Bus_Machining
        WHERE Bus_Machining.Tenantid = #{tenantid}
    </select>

    <!--    发货完成率-->
    <select id="getDeliFinishRate2" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT count(1)                                   as value,
               (select count(1)
                FROM Bus_Deliery
                WHERE FinishCount = ItemCount
                  and Bus_Deliery.Tenantid = #{tenantid}) as valueb
        FROM Bus_Deliery
        WHERE Bus_Deliery.Tenantid = #{tenantid}
    </select>
    <!--    发货完成率-->
    <select id="getDeliFinishRate" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT COUNT(*) AS value, COUNT(Bus_Deliery.Id) AS valueb
        FROM Bus_Deliery
                 LEFT JOIN (SELECT Id
                            FROM Bus_Deliery
                            WHERE FinishCount = ItemCount
                              AND Tenantid = #{tenantid}) AS finished_deliveries
                           ON Bus_Deliery.Id = finished_deliveries.Id
        WHERE Bus_Deliery.Tenantid = #{tenantid};
    </select>

    <!--    收款完成率-->
    <select id="getReceiptFinishRate" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT count(1)                                                                                      as value,
               (select count(1) FROM Bus_Receipt WHERE FmDocMark = 1 and Bus_Receipt.Tenantid = #{tenantid}) as valueb
        FROM Bus_Receipt
        WHERE Bus_Receipt.Tenantid = #{tenantid}
    </select>

    <select id="getAuthByCode" resultType="java.util.HashMap">
        SELECT UserName,
               UserPassword,
               Tenantid
        FROM PiAuthCode
        WHERE AuthCode = #{auth}
    </select>


    <select id="getGroupDistribution" resultType="inks.common.core.domain.ChartPojo">
        SELECT Country  AS name,
               COUNT(1) AS value
        FROM App_Workgroup
        WHERE Tenantid = #{tenantid}
          and GroupType = '客户'
        GROUP BY Country
    </select>


    <!--    SUM(IF(spu.spukey = 'spuzongzhongliang', spu.spuvalue, 0)) AS 总重量,-->
    <!--    SUM(IF(spu.spukey = 'sputuitongxiezongzhong', spu.spuvalue, 0)) AS 退铜屑重量,-->
    <!--    SUM(IF(spu.spukey = 'sputuijiatouzongzhong', spu.spuvalue, 0)) AS 退夹头重量-->
    <select id="getSpuWeightGroupByGroup" resultType="inks.common.core.domain.ChartPojo">
        SELECT w.id,
               w.GroupName                                                     as name,
               w.GroupUid                                                      as code,
               SUM(IF(spu.spukey = 'spuzongzhongliang', spu.spuvalue, 0))      AS value,
               SUM(IF(spu.spukey = 'sputuitongxiezongzhong', spu.spuvalue, 0)) AS valueb,
               SUM(IF(spu.spukey = 'sputuijiatouzongzhong', spu.spuvalue, 0))  AS valuec
        FROM Bus_MachiningItem as mi
                 CROSS JOIN JSON_TABLE(
                mi.AttributeJson,
                "$[*]"
                COLUMNS (
                    spukey VARCHAR(255) PATH "$.key",
                    spuvalue DECIMAL(18, 4) PATH "$.value" )
                            ) AS spu
                 LEFT JOIN Bus_Machining m ON mi.Pid = m.id
                 LEFT JOIN App_Workgroup w ON m.Groupid = w.id
                 LEFT JOIN Mat_Goods g ON mi.Goodsid = g.id
        WHERE JSON_VALID(mi.AttributeJson)
          AND mi.Tenantid = #{tenantid}
          AND (m.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        <if test="filterstr != null">
            ${filterstr}
        </if>
        GROUP BY w.id, w.GroupName
        order by value desc
    </select>

    <select id="getSpuWeightGroupByCaiZhi" resultType="inks.common.core.domain.ChartPojo">
        SELECT 材质        as name,
               SUM(总重量) AS value
        FROM (SELECT id,
                     MAX(IF(spukey = 'spucaizhi', spuvalue, null))                                 AS 材质,
                     MAX(IF(spukey = 'spuzongzhongliang', CAST(spuvalue AS DECIMAL(18, 4)), null)) AS 总重量
              FROM (SELECT bi.id,
                           jt.spukey,
                           jt.spuvalue
                    FROM Bus_MachiningItem as bi
                             CROSS JOIN JSON_TABLE(
                            bi.AttributeJson,
                            "$[*]"
                            COLUMNS (
                                spukey VARCHAR(255) PATH "$.key",
                                spuvalue VARCHAR(255) PATH "$.value"
                                )
                                        ) AS jt
                             LEFT JOIN Bus_Machining b ON bi.Pid = b.id
                    WHERE JSON_VALID(bi.AttributeJson)
                      AND jt.spukey IN ('spuzongzhongliang', 'spucaizhi')
                      AND (b.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
                      AND bi.Tenantid = #{tenantid}) AS subquery
              GROUP BY id) AS subquery2
        GROUP BY 材质
    </select>

    <select id="getSpuWeightGroupByGongYi" resultType="inks.common.core.domain.ChartPojo">
        SELECT 工艺        as name,
               SUM(总重量) AS value
        FROM (SELECT id,
                     MAX(IF(spukey = 'spuzhuzaogongyi', spuvalue, null))                           AS 工艺,
                     MAX(IF(spukey = 'spuzongzhongliang', CAST(spuvalue AS DECIMAL(18, 4)), null)) AS 总重量
              FROM (SELECT bi.id,
                           jt.spukey,
                           jt.spuvalue
                    FROM Bus_MachiningItem as bi
                             CROSS JOIN JSON_TABLE(
                            bi.AttributeJson,
                            "$[*]"
                            COLUMNS (
                                spukey VARCHAR(255) PATH "$.key",
                                spuvalue VARCHAR(255) PATH "$.value"
                                )
                                        ) AS jt
                             LEFT JOIN Bus_Machining b ON bi.Pid = b.id
                    WHERE JSON_VALID(bi.AttributeJson)
                      AND jt.spukey IN ('spuzongzhongliang', 'spuzhuzaogongyi')
                      AND (b.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
                      AND bi.Tenantid = #{tenantid}) AS subquery
              GROUP BY id) AS subquery2
        GROUP BY 工艺
    </select>

    <select id="getSpuWeightGroupByGuiGe" resultType="java.util.Map">
        SELECT SUM(总重量) as value,
               goodsid,
               goodsname,
               spuff,
               spufd,
               spufdn,
               spul,
               spuh,
               spub
        FROM (SELECT id,
                     Goodsid,
                     GoodsName,
                     MAX(IF(spukey = 'spuzongzhongliang', CAST(spuvalue AS DECIMAL(18, 4)), null)) AS 总重量,
                     MAX(IF(spukey = 'spuff', spuvalue, null))                                     AS spuff,
                     MAX(IF(spukey = 'spufd', spuvalue, null))                                     AS spufd,
                     MAX(IF(spukey = 'spufdn', spuvalue, null))                                    AS spufdn,
                     MAX(IF(spukey = 'spul', spuvalue, null))                                      AS spul,
                     MAX(IF(spukey = 'spuh', spuvalue, null))                                      AS spuh,
                     MAX(IF(spukey = 'spub', spuvalue, null))                                      AS spub
              FROM (SELECT bi.id,
                           bi.Goodsid,
                           g.GoodsName,
                           jt.spukey,
                           jt.spuvalue
                    FROM Bus_MachiningItem as bi
                             CROSS JOIN JSON_TABLE(
                            bi.AttributeJson,
                            "$[*]"
                            COLUMNS (
                                spukey VARCHAR(255) PATH "$.key",
                                spuvalue VARCHAR(255) PATH "$.value"
                                )
                                        ) AS jt
                             LEFT JOIN Bus_Machining b ON bi.Pid = b.id
                             Left Join Mat_Goods g on bi.Goodsid = g.id
                    WHERE JSON_VALID(bi.AttributeJson)
                      AND jt.spukey IN ('spuzongzhongliang', 'spuff', 'spufd', 'spufdn', 'spul', 'spuh', 'spub')
                      AND (b.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
                      AND bi.Tenantid = #{tenantid}) AS subquery
              GROUP BY id) AS subquery2
        GROUP BY Goodsid, spuff, spufd, spufdn, spul, spuh, spub
    </select>

    <select id="getSumMachAmtGroupByGoodsUid" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT LEFT(Mat_Goods.GoodsUid, 2)      AS name,
               sum(Bus_MachiningItem.Amount)    as value,
               sum(Bus_MachiningItem.TaxAmount) as valueb,
               sum(Bus_MachiningItem.StdAmount) as valuec
        FROM Bus_Machining
                 RIGHT JOIN Bus_MachiningItem
                            ON Bus_MachiningItem.Pid = Bus_Machining.id
                 LEFT JOIN Mat_Goods on Mat_Goods.id = Bus_MachiningItem.Goodsid
        WHERE Bus_MachiningItem.disannulmark = 0
          AND Bus_MachiningItem.Closed = 0
          AND Bus_Machining.Tenantid = #{tenantid}
          AND (Bus_Machining.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
          AND LEFT(Mat_Goods.GoodsUid, 2) IN ('C1', 'C2')
        GROUP BY LEFT(Mat_Goods.GoodsUid, 2)
        ORDER BY value DESC
    </select>


    <select id="getAllSalesmenNameMach" resultType="java.lang.String">
        SELECT DISTINCT Salesman
        FROM Bus_Machining
        WHERE Tenantid = #{tenantid}
    </select>
    <select id="getAllSalesmenNameDeli" resultType="java.lang.String">
        SELECT DISTINCT Salesman
        FROM Bus_Deliery
        WHERE Tenantid = #{tenantid}
    </select>

</mapper>
