<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.D01M05R1Mapper">
    <select id="getTagSumAmtByMonth"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT
            IFNULL(sum( Bus_Invoice.amount ),0) AS
                valueb
	    ,(
	    CASE
			WHEN App_Workgroup.Abbreviate IS NULL
			OR App_Workgroup.Abbreviate = '' THEN
				App_Workgroup.GroupName ELSE Abbreviate
			END
			) AS NAME
        FROM
            Bus_Invoice
            LEFT OUTER JOIN App_Workgroup ON Bus_Invoice.Groupid = App_Workgroup.id
        WHERE  DATE_FORMAT(Bus_Invoice.BillDate,'%Y-%m') = DATE_FORMAT( CURDATE( ), '%Y-%m' )
          AND DisannulMark !=1 AND Bus_Invoice.Tenantid = #{tid}
        GROUP BY
            App_Workgroup.Abbreviate,
            App_Workgroup.GroupName
    </select>
</mapper>
