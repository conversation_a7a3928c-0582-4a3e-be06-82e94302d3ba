<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatAccessitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatAccessitemPojo">
        SELECT Mat_AccessItem.id,
               Mat_AccessItem.Pid,
               Mat_AccessItem.Goodsid,
               Mat_AccessItem.Quantity,
               Mat_AccessItem.Price,
               Mat_AccessItem.Amount,
               Mat_AccessItem.TaxPrice,
               Mat_AccessItem.TaxAmount,
               Mat_AccessItem.ItemTaxrate,
               Mat_AccessItem.TaxTotal,
               Mat_AccessItem.Remark,
               Mat_AccessItem.CiteUid,
               Mat_AccessItem.CiteItemid,
               Mat_AccessItem.StateCode,
               Mat_AccessItem.StateDate,
               Mat_AccessItem.RowNum,
               Mat_AccessItem.Location,
               Mat_AccessItem.BatchNo,
               Mat_AccessItem.PackSn,
               Mat_AccessItem.ExpiDate,
               Mat_AccessItem.Customer,
               Mat_AccessItem.CustPO,
               Mat_AccessItem.MachUid,
               Mat_AccessItem.MachItemid,
               Mat_AccessItem.MachGroupid,
               Mat_AccessItem.MainPlanUid,
               Mat_AccessItem.MainPlanItemid,
               Mat_AccessItem.MrpUid,
               Mat_AccessItem.MrpItemid,
               Mat_AccessItem.Inveid,
               Mat_AccessItem.Skuid,
               Mat_AccessItem.AttributeJson,
               Mat_AccessItem.WkQtyid,
               Mat_AccessItem.LabelCodes,
               Mat_AccessItem.LabelQty,
               Mat_AccessItem.SourceType,
               Mat_AccessItem.OrderUid,
               Mat_AccessItem.OrderItemid,
               Mat_AccessItem.OrderNo,
               Mat_AccessItem.WorkUid,
               Mat_AccessItem.WorkItemid,
               Mat_AccessItem.SubcUid,
               Mat_AccessItem.SubcItemid,
               Mat_AccessItem.CustUid,
               Mat_AccessItem.CuistItemid,
               Mat_AccessItem.Custom1,
               Mat_AccessItem.Custom2,
               Mat_AccessItem.Custom3,
               Mat_AccessItem.Custom4,
               Mat_AccessItem.Custom5,
               Mat_AccessItem.Custom6,
               Mat_AccessItem.Custom7,
               Mat_AccessItem.Custom8,
               Mat_AccessItem.Custom9,
               Mat_AccessItem.Custom10,
               Mat_AccessItem.Tenantid,
               Mat_AccessItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_AccessItem
                 LEFT JOIN Mat_Goods ON Mat_AccessItem.Goodsid = Mat_Goods.id
        where Mat_AccessItem.id = #{key}
          and Mat_AccessItem.Tenantid = #{tid}
    </select>
    <sql id="selectMatAccessitemVo">
        SELECT Mat_AccessItem.id,
               Mat_AccessItem.Pid,
               Mat_AccessItem.Goodsid,
               Mat_AccessItem.Quantity,
               Mat_AccessItem.Price,
               Mat_AccessItem.Amount,
               Mat_AccessItem.TaxPrice,
               Mat_AccessItem.TaxAmount,
               Mat_AccessItem.ItemTaxrate,
               Mat_AccessItem.TaxTotal,
               Mat_AccessItem.Remark,
               Mat_AccessItem.CiteUid,
               Mat_AccessItem.CiteItemid,
               Mat_AccessItem.StateCode,
               Mat_AccessItem.StateDate,
               Mat_AccessItem.RowNum,
               Mat_AccessItem.Location,
               Mat_AccessItem.BatchNo,
               Mat_AccessItem.PackSn,
               Mat_AccessItem.ExpiDate,
               Mat_AccessItem.Customer,
               Mat_AccessItem.CustPO,
               Mat_AccessItem.MachUid,
               Mat_AccessItem.MachItemid,
               Mat_AccessItem.MachGroupid,
               Mat_AccessItem.MainPlanUid,
               Mat_AccessItem.MainPlanItemid,
               Mat_AccessItem.MrpUid,
               Mat_AccessItem.MrpItemid,
               Mat_AccessItem.Inveid,
               Mat_AccessItem.Skuid,
               Mat_AccessItem.AttributeJson,
               Mat_AccessItem.WkQtyid,
               Mat_AccessItem.LabelCodes,
               Mat_AccessItem.LabelQty,
               Mat_AccessItem.SourceType,
               Mat_AccessItem.OrderUid,
               Mat_AccessItem.OrderItemid,
               Mat_AccessItem.OrderNo,
               Mat_AccessItem.WorkUid,
               Mat_AccessItem.WorkItemid,
               Mat_AccessItem.SubcUid,
               Mat_AccessItem.SubcItemid,
               Mat_AccessItem.CustUid,
               Mat_AccessItem.CuistItemid,
               Mat_AccessItem.Custom1,
               Mat_AccessItem.Custom2,
               Mat_AccessItem.Custom3,
               Mat_AccessItem.Custom4,
               Mat_AccessItem.Custom5,
               Mat_AccessItem.Custom6,
               Mat_AccessItem.Custom7,
               Mat_AccessItem.Custom8,
               Mat_AccessItem.Custom9,
               Mat_AccessItem.Custom10,
               Mat_AccessItem.Tenantid,
               Mat_AccessItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_AccessItem
                 LEFT JOIN Mat_Goods ON Mat_AccessItem.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatAccessitemPojo">
        <include refid="selectMatAccessitemVo"/>
        where 1 = 1 and Mat_AccessItem.Tenantid =#{tenantid}
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_AccessItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Mat_AccessItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_AccessItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Mat_AccessItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Mat_AccessItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Mat_AccessItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Mat_AccessItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Mat_AccessItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Mat_AccessItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.packsn != null and SearchPojo.packsn != ''">
            and Mat_AccessItem.packsn like concat('%', #{SearchPojo.packsn}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Mat_AccessItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Mat_AccessItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Mat_AccessItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Mat_AccessItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Mat_AccessItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Mat_AccessItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Mat_AccessItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Mat_AccessItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Mat_AccessItem.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.inveid != null and SearchPojo.inveid != ''">
            and Mat_AccessItem.inveid like concat('%', #{SearchPojo.inveid}, '%')
        </if>
        <if test="SearchPojo.skuid != null and SearchPojo.skuid != ''">
            and Mat_AccessItem.skuid like concat('%', #{SearchPojo.skuid}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Mat_AccessItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.orderuid != null and SearchPojo.orderuid != ''">
            and Mat_AccessItem.orderuid like concat('%',
            #{SearchPojo.orderuid}, '%')
        </if>
        <if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid != ''">
            and Mat_AccessItem.orderitemid like concat('%',
            #{SearchPojo.orderitemid}, '%')
        </if>
        <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
            and Mat_AccessItem.workuid like concat('%',
            #{SearchPojo.workuid}, '%')
        </if>
        <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
            and Mat_AccessItem.workitemid like concat('%',
            #{SearchPojo.workitemid}, '%')
        </if>
        <if test="SearchPojo.subcuid != null and SearchPojo.subcuid != ''">
            and Mat_AccessItem.subcuid like concat('%',
            #{SearchPojo.subcuid}, '%')
        </if>
        <if test="SearchPojo.subcitemid != null and SearchPojo.subcitemid != ''">
            and Mat_AccessItem.subcitemid like concat('%',
            #{SearchPojo.subcitemid}, '%')
        </if>
        <if test="SearchPojo.custuid != null and SearchPojo.custuid != ''">
            and Mat_AccessItem.custuid like concat('%',
            #{SearchPojo.custuid}, '%')
        </if>
        <if test="SearchPojo.cuistitemid != null and SearchPojo.cuistitemid != ''">
            and Mat_AccessItem.cuistitemid like concat('%',
            #{SearchPojo.cuistitemid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_AccessItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_AccessItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_AccessItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_AccessItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_AccessItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_AccessItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_AccessItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_AccessItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_AccessItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_AccessItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Mat_AccessItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_AccessItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Mat_AccessItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Mat_AccessItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Mat_AccessItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Mat_AccessItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Mat_AccessItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Mat_AccessItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.packsn != null and SearchPojo.packsn != ''">
                or Mat_AccessItem.PackSn like concat('%', #{SearchPojo.packsn}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Mat_AccessItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Mat_AccessItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Mat_AccessItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Mat_AccessItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Mat_AccessItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Mat_AccessItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Mat_AccessItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Mat_AccessItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Mat_AccessItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.inveid != null and SearchPojo.inveid != ''">
                or Mat_AccessItem.Inveid like concat('%', #{SearchPojo.inveid}, '%')
            </if>
            <if test="SearchPojo.skuid != null and SearchPojo.skuid != ''">
                or Mat_AccessItem.Skuid like concat('%', #{SearchPojo.skuid}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Mat_AccessItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.orderuid != null and SearchPojo.orderuid != ''">
                or Mat_AccessItem.OrderUid like concat('%', #{SearchPojo.orderuid}, '%')
            </if>
            <if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid != ''">
                or Mat_AccessItem.OrderItemid like concat('%', #{SearchPojo.orderitemid}, '%')
            </if>
            <if test="SearchPojo.workuid != null and SearchPojo.workuid != ''">
                or Mat_AccessItem.WorkUid like concat('%', #{SearchPojo.workuid}, '%')
            </if>
            <if test="SearchPojo.workitemid != null and SearchPojo.workitemid != ''">
                or Mat_AccessItem.WorkItemid like concat('%', #{SearchPojo.workitemid}, '%')
            </if>
            <if test="SearchPojo.subcuid != null and SearchPojo.subcuid != ''">
                or Mat_AccessItem.SubcUid like concat('%', #{SearchPojo.subcuid}, '%')
            </if>
            <if test="SearchPojo.subcitemid != null and SearchPojo.subcitemid != ''">
                or Mat_AccessItem.SubcItemid like concat('%', #{SearchPojo.subcitemid}, '%')
            </if>
            <if test="SearchPojo.custuid != null and SearchPojo.custuid != ''">
                or Mat_AccessItem.CustUid like concat('%', #{SearchPojo.custuid}, '%')
            </if>
            <if test="SearchPojo.cuistitemid != null and SearchPojo.cuistitemid != ''">
                or Mat_AccessItem.CuistItemid like concat('%', #{SearchPojo.cuistitemid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_AccessItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_AccessItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_AccessItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_AccessItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_AccessItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_AccessItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_AccessItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_AccessItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_AccessItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_AccessItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.MatAccessitemPojo">
        SELECT Mat_AccessItem.id,
               Mat_AccessItem.Pid,
               Mat_AccessItem.Goodsid,
               Mat_AccessItem.Quantity,
               Mat_AccessItem.Price,
               Mat_AccessItem.Amount,
               Mat_AccessItem.TaxPrice,
               Mat_AccessItem.TaxAmount,
               Mat_AccessItem.ItemTaxrate,
               Mat_AccessItem.TaxTotal,
               Mat_AccessItem.Remark,
               Mat_AccessItem.CiteUid,
               Mat_AccessItem.CiteItemid,
               Mat_AccessItem.StateCode,
               Mat_AccessItem.StateDate,
               Mat_AccessItem.RowNum,
               Mat_AccessItem.Location,
               Mat_AccessItem.BatchNo,
               Mat_AccessItem.PackSn,
               Mat_AccessItem.ExpiDate,
               Mat_AccessItem.Customer,
               Mat_AccessItem.CustPO,
               Mat_AccessItem.MachUid,
               Mat_AccessItem.MachItemid,
               Mat_AccessItem.MachGroupid,
               Mat_AccessItem.MainPlanUid,
               Mat_AccessItem.MainPlanItemid,
               Mat_AccessItem.MrpUid,
               Mat_AccessItem.MrpItemid,
               Mat_AccessItem.Inveid,
               Mat_AccessItem.Skuid,
               Mat_AccessItem.AttributeJson,
               Mat_AccessItem.WkQtyid,
               Mat_AccessItem.LabelCodes,
               Mat_AccessItem.LabelQty,
               Mat_AccessItem.SourceType,
               Mat_AccessItem.OrderUid,
               Mat_AccessItem.OrderItemid,
               Mat_AccessItem.OrderNo,
               Mat_AccessItem.WorkUid,
               Mat_AccessItem.WorkItemid,
               Mat_AccessItem.SubcUid,
               Mat_AccessItem.SubcItemid,
               Mat_AccessItem.CustUid,
               Mat_AccessItem.CuistItemid,
               Mat_AccessItem.Custom1,
               Mat_AccessItem.Custom2,
               Mat_AccessItem.Custom3,
               Mat_AccessItem.Custom4,
               Mat_AccessItem.Custom5,
               Mat_AccessItem.Custom6,
               Mat_AccessItem.Custom7,
               Mat_AccessItem.Custom8,
               Mat_AccessItem.Custom9,
               Mat_AccessItem.Custom10,
               Mat_AccessItem.Tenantid,
               Mat_AccessItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_AccessItem
                 LEFT JOIN Mat_Goods ON Mat_AccessItem.Goodsid = Mat_Goods.id
        where Mat_AccessItem.Pid = #{Pid}
          and Mat_AccessItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_AccessItem(id, Pid, Goodsid, Quantity, Price, Amount, TaxPrice, TaxAmount, ItemTaxrate,
                                   TaxTotal, Remark, CiteUid, CiteItemid, StateCode, StateDate, RowNum, Location,
                                   BatchNo, PackSn, ExpiDate, Customer, CustPO, MachUid, MachItemid, MachGroupid,
                                   MainPlanUid, MainPlanItemid, MrpUid, MrpItemid, Inveid, Skuid, AttributeJson,
                                   WkQtyid, LabelCodes, LabelQty, SourceType, OrderUid, OrderItemid,OrderNo, WorkUid,
                                   WorkItemid, SubcUid, SubcItemid, CustUid, CuistItemid, Custom1, Custom2, Custom3,
                                   Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{quantity}, #{price}, #{amount}, #{taxprice}, #{taxamount}, #{itemtaxrate},
                #{taxtotal}, #{remark}, #{citeuid}, #{citeitemid}, #{statecode}, #{statedate}, #{rownum}, #{location},
                #{batchno}, #{packsn}, #{expidate}, #{customer}, #{custpo}, #{machuid}, #{machitemid}, #{machgroupid},
                #{mainplanuid}, #{mainplanitemid}, #{mrpuid}, #{mrpitemid}, #{inveid}, #{skuid}, #{attributejson},
                #{wkqtyid}, #{labelcodes}, #{labelqty}, #{sourcetype}, #{orderuid}, #{orderitemid}, #{orderno},
                #{workuid},
                #{workitemid}, #{subcuid}, #{subcitemid}, #{custuid}, #{cuistitemid}, #{custom1}, #{custom2},
                #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10},
                #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_AccessItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="location != null ">
                Location = #{location},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="packsn != null ">
                PackSn = #{packsn},
            </if>
            <if test="expidate != null">
                ExpiDate = #{expidate},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null ">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="inveid != null ">
                Inveid = #{inveid},
            </if>
            <if test="skuid != null ">
                Skuid = #{skuid},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="wkqtyid != null ">
                WkQtyid = #{wkqtyid},
            </if>
            <if test="labelcodes != null ">
                LabelCodes = #{labelcodes},
            </if>
            <if test="labelqty != null">
                LabelQty = #{labelqty},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="orderuid != null">
                OrderUid = #{orderuid},
            </if>
            <if test="orderitemid != null">
                OrderItemid = #{orderitemid},
            </if>
            <if test="orderno != null">
                OrderNo = #{orderno},
            </if>
            <if test="workuid != null">
                WorkUid = #{workuid},
            </if>
            <if test="workitemid != null">
                WorkItemid = #{workitemid},
            </if>
            <if test="subcuid != null">
                SubcUid = #{subcuid},
            </if>
            <if test="subcitemid != null">
                SubcItemid = #{subcitemid},
            </if>
            <if test="custuid != null">
                CustUid = #{custuid},
            </if>
            <if test="cuistitemid != null">
                CuistItemid = #{cuistitemid},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_AccessItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

