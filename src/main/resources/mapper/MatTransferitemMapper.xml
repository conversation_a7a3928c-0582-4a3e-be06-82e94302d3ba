<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatTransferitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatTransferitemPojo">
        SELECT
            Mat_TransferItem.id,
            Mat_TransferItem.Pid,
            Mat_TransferItem.Inveid,
            Mat_TransferItem.Goodsid,
            Mat_TransferItem.ItemCode,
            Mat_TransferItem.ItemName,
            Mat_TransferItem.ItemSpec,
            Mat_TransferItem.ItemUnit,
            Mat_TransferItem.BatchNo,
            Mat_TransferItem.PackSn,
            Mat_TransferItem.ExpiDate,
            Mat_TransferItem.OutLocation,
            Mat_TransferItem.InLocation,
            Mat_TransferItem.Quantity,
            Mat_TransferItem.Remark,
            Mat_TransferItem.RowNum,
            Mat_TransferItem.Custom1,
            Mat_TransferItem.Custom2,
            Mat_TransferItem.Custom3,
            Mat_TransferItem.Custom4,
            Mat_TransferItem.Custom5,
            Mat_TransferItem.Custom6,
            Mat_TransferItem.Custom7,
            Mat_TransferItem.Custom8,
            Mat_TransferItem.Custom9,
            Mat_TransferItem.Custom10,
            Mat_TransferItem.Tenantid,
            Mat_TransferItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10
        FROM
            Mat_Goods
                RIGHT JOIN Mat_TransferItem ON Mat_TransferItem.Goodsid = Mat_Goods.id
        where Mat_TransferItem.id = #{key}
          and Mat_TransferItem.Tenantid = #{tid}
    </select>
    <sql id="selectMatTransferitemVo">
        SELECT
            Mat_TransferItem.id,
            Mat_TransferItem.Pid,
            Mat_TransferItem.Inveid,
            Mat_TransferItem.Goodsid,
            Mat_TransferItem.ItemCode,
            Mat_TransferItem.ItemName,
            Mat_TransferItem.ItemSpec,
            Mat_TransferItem.ItemUnit,
            Mat_TransferItem.BatchNo,
            Mat_TransferItem.PackSn,
            Mat_TransferItem.ExpiDate,
            Mat_TransferItem.OutLocation,
            Mat_TransferItem.InLocation,
            Mat_TransferItem.Quantity,
            Mat_TransferItem.Remark,
            Mat_TransferItem.RowNum,
            Mat_TransferItem.Custom1,
            Mat_TransferItem.Custom2,
            Mat_TransferItem.Custom3,
            Mat_TransferItem.Custom4,
            Mat_TransferItem.Custom5,
            Mat_TransferItem.Custom6,
            Mat_TransferItem.Custom7,
            Mat_TransferItem.Custom8,
            Mat_TransferItem.Custom9,
            Mat_TransferItem.Custom10,
            Mat_TransferItem.Tenantid,
            Mat_TransferItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10
        FROM
            Mat_Goods
                RIGHT JOIN Mat_TransferItem ON Mat_TransferItem.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatTransferitemPojo">
        <include refid="selectMatTransferitemVo"/>
        where 1 = 1 and Mat_TransferItem.Tenantid =#{tenantid}
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_TransferItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Mat_TransferItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.inveid != null and SearchPojo.inveid != ''">
            and Mat_TransferItem.inveid like concat('%', #{SearchPojo.inveid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Mat_TransferItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Mat_TransferItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Mat_TransferItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Mat_TransferItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Mat_TransferItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Mat_TransferItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.packsn != null and SearchPojo.packsn != ''">
            and Mat_TransferItem.packsn like concat('%', #{SearchPojo.packsn}, '%')
        </if>
        <if test="SearchPojo.outlocation != null and SearchPojo.outlocation != ''">
            and Mat_TransferItem.outlocation like concat('%', #{SearchPojo.outlocation}, '%')
        </if>
        <if test="SearchPojo.inlocation != null and SearchPojo.inlocation != ''">
            and Mat_TransferItem.inlocation like concat('%', #{SearchPojo.inlocation}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Mat_TransferItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Mat_TransferItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Mat_TransferItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Mat_TransferItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Mat_TransferItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Mat_TransferItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Mat_TransferItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Mat_TransferItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Mat_TransferItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Mat_TransferItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Mat_TransferItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Mat_TransferItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.inveid != null and SearchPojo.inveid != ''">
                or Mat_TransferItem.Inveid like concat('%', #{SearchPojo.inveid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_TransferItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Mat_TransferItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Mat_TransferItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Mat_TransferItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Mat_TransferItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Mat_TransferItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.packsn != null and SearchPojo.packsn != ''">
                or Mat_TransferItem.PackSn like concat('%', #{SearchPojo.packsn}, '%')
            </if>
            <if test="SearchPojo.outlocation != null and SearchPojo.outlocation != ''">
                or Mat_TransferItem.OutLocation like concat('%', #{SearchPojo.outlocation}, '%')
            </if>
            <if test="SearchPojo.inlocation != null and SearchPojo.inlocation != ''">
                or Mat_TransferItem.InLocation like concat('%', #{SearchPojo.inlocation}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Mat_TransferItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_TransferItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_TransferItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_TransferItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_TransferItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_TransferItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_TransferItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_TransferItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_TransferItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_TransferItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_TransferItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.MatTransferitemPojo">
        SELECT
            Mat_TransferItem.id,
            Mat_TransferItem.Pid,
            Mat_TransferItem.Inveid,
            Mat_TransferItem.Goodsid,
            Mat_TransferItem.ItemCode,
            Mat_TransferItem.ItemName,
            Mat_TransferItem.ItemSpec,
            Mat_TransferItem.ItemUnit,
            Mat_TransferItem.BatchNo,
            Mat_TransferItem.PackSn,
            Mat_TransferItem.ExpiDate,
            Mat_TransferItem.OutLocation,
            Mat_TransferItem.InLocation,
            Mat_TransferItem.Quantity,
            Mat_TransferItem.Remark,
            Mat_TransferItem.RowNum,
            Mat_TransferItem.Custom1,
            Mat_TransferItem.Custom2,
            Mat_TransferItem.Custom3,
            Mat_TransferItem.Custom4,
            Mat_TransferItem.Custom5,
            Mat_TransferItem.Custom6,
            Mat_TransferItem.Custom7,
            Mat_TransferItem.Custom8,
            Mat_TransferItem.Custom9,
            Mat_TransferItem.Custom10,
            Mat_TransferItem.Tenantid,
            Mat_TransferItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10
        FROM
            Mat_Goods
                RIGHT JOIN Mat_TransferItem ON Mat_TransferItem.Goodsid = Mat_Goods.id
        where Mat_TransferItem.Pid = #{Pid}
          and Mat_TransferItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_TransferItem(id, Pid, Inveid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, BatchNo, PackSn,
                                     ExpiDate, OutLocation, InLocation, Quantity, Remark, RowNum, Custom1, Custom2,
                                     Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                     Revision)
        values (#{id}, #{pid}, #{inveid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{batchno},
                #{packsn}, #{expidate}, #{outlocation}, #{inlocation}, #{quantity}, #{remark}, #{rownum}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_TransferItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="inveid != null ">
                Inveid = #{inveid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="packsn != null ">
                PackSn = #{packsn},
            </if>
            <if test="expidate != null">
                ExpiDate = #{expidate},
            </if>
            <if test="outlocation != null ">
                OutLocation = #{outlocation},
            </if>
            <if test="inlocation != null ">
                InLocation = #{inlocation},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_TransferItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

