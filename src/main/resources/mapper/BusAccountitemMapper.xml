<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BusAccountitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BusAccountitemPojo">
        select id,
               Pid,
               Direction,
               ModuleCode,
               BillType,
               BillDate,
               BillTitle,
               BillUid,
               Billid,
               OpenAmount,
               InAmount,
               OutAmount,
               CloseAmount,
               RowNum,
               Remark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Bus_AccountItem
        where Bus_AccountItem.id = #{key}
          and Bus_AccountItem.Tenantid = #{tid}
    </select>
    <sql id="selectBusAccountitemVo">
        select id,
               Pid,
               Direction,
               ModuleCode,
               BillType,
               BillDate,
               BillTitle,
               BillUid,
               Billid,
               OpenAmount,
               InAmount,
               OutAmount,
               CloseAmount,
               RowNum,
               Remark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Bus_AccountItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusAccountitemPojo">
        <include refid="selectBusAccountitemVo"/>
        where 1 = 1 and Bus_AccountItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_AccountItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Bus_AccountItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.direction != null and SearchPojo.direction != ''">
            and Bus_AccountItem.direction like concat('%', #{SearchPojo.direction}, '%')
        </if>
        <if test="SearchPojo.modulecode != null and SearchPojo.modulecode != ''">
            and Bus_AccountItem.modulecode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Bus_AccountItem.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Bus_AccountItem.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.billuid != null and SearchPojo.billuid != ''">
            and Bus_AccountItem.billuid like concat('%', #{SearchPojo.billuid}, '%')
        </if>
        <if test="SearchPojo.billid != null and SearchPojo.billid != ''">
            and Bus_AccountItem.billid like concat('%', #{SearchPojo.billid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Bus_AccountItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Bus_AccountItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Bus_AccountItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Bus_AccountItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Bus_AccountItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Bus_AccountItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Bus_AccountItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Bus_AccountItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Bus_AccountItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Bus_AccountItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Bus_AccountItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Bus_AccountItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.direction != null and SearchPojo.direction != ''">
                or Bus_AccountItem.Direction like concat('%', #{SearchPojo.direction}, '%')
            </if>
            <if test="SearchPojo.modulecode != null and SearchPojo.modulecode != ''">
                or Bus_AccountItem.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Bus_AccountItem.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Bus_AccountItem.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.billuid != null and SearchPojo.billuid != ''">
                or Bus_AccountItem.BillUid like concat('%', #{SearchPojo.billuid}, '%')
            </if>
            <if test="SearchPojo.billid != null and SearchPojo.billid != ''">
                or Bus_AccountItem.Billid like concat('%', #{SearchPojo.billid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Bus_AccountItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Bus_AccountItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Bus_AccountItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Bus_AccountItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Bus_AccountItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Bus_AccountItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Bus_AccountItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Bus_AccountItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Bus_AccountItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Bus_AccountItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Bus_AccountItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.BusAccountitemPojo">
        select id,
               Pid,
               Direction,
               ModuleCode,
               BillType,
               BillDate,
               BillTitle,
               BillUid,
               Billid,
               OpenAmount,
               InAmount,
               OutAmount,
               CloseAmount,
               RowNum,
               Remark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Bus_AccountItem
        where Bus_AccountItem.Pid = #{Pid}
          and Bus_AccountItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_AccountItem(id, Pid, Direction, ModuleCode, BillType, BillDate, BillTitle, BillUid, Billid,
                                    OpenAmount, InAmount, OutAmount, CloseAmount, RowNum, Remark, Custom1, Custom2,
                                    Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                    Revision)
        values (#{id}, #{pid}, #{direction}, #{modulecode}, #{billtype}, #{billdate}, #{billtitle}, #{billuid},
                #{billid}, #{openamount}, #{inamount}, #{outamount}, #{closeamount}, #{rownum}, #{remark}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_AccountItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="direction != null ">
                Direction = #{direction},
            </if>
            <if test="modulecode != null ">
                ModuleCode = #{modulecode},
            </if>
            <if test="billtype != null ">
                BillType = #{billtype},
            </if>
            <if test="billdate != null">
                BillDate = #{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle = #{billtitle},
            </if>
            <if test="billuid != null ">
                BillUid = #{billuid},
            </if>
            <if test="billid != null ">
                Billid = #{billid},
            </if>
            <if test="openamount != null">
                OpenAmount = #{openamount},
            </if>
            <if test="inamount != null">
                InAmount = #{inamount},
            </if>
            <if test="outamount != null">
                OutAmount = #{outamount},
            </if>
            <if test="closeamount != null">
                CloseAmount = #{closeamount},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_AccountItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

