<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatStorageMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatStoragePojo">
        select id,
               Projectid,
               ProjName,
               ProjCode,
               StoreCode,
               StoreName,
               StoreAdd,
               Operator,
               StoreTel,
               Remark,
               RowNum,
               AllowEdit,
               AllowDelete,
               EnabledMark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               DeleteMark,
               DeleteLister,
               DeleteListerid,
               DeleteDate,
               MachMark,
               StoreType,
               UsableMark,
               DmsMark,
               ScmMark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Mat_Storage
        where Mat_Storage.id = #{key}
          and Mat_Storage.Tenantid = #{tid}
    </select>
    <sql id="selectMatStorageVo">
        select id,
               Projectid,
               ProjName,
               ProjCode,
               StoreCode,
               StoreName,
               StoreAdd,
               Operator,
               StoreTel,
               Remark,
               RowNum,
               AllowEdit,
               AllowDelete,
               EnabledMark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               DeleteMark,
               DeleteLister,
               DeleteListerid,
               DeleteDate,
               MachMark,
               StoreType,
               UsableMark,
               DmsMark,
               ScmMark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Mat_Storage
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatStoragePojo">
        <include refid="selectMatStorageVo"/>
        where 1 = 1 and Mat_Storage.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Storage.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.storecode != null ">
            and Mat_Storage.StoreCode like concat('%', #{SearchPojo.storecode}, '%')
        </if>
        <if test="SearchPojo.storename != null ">
            and Mat_Storage.StoreName like concat('%', #{SearchPojo.storename}, '%')
        </if>
        <if test="SearchPojo.storeadd != null ">
            and Mat_Storage.StoreAdd like concat('%', #{SearchPojo.storeadd}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Mat_Storage.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.storetel != null ">
            and Mat_Storage.StoreTel like concat('%', #{SearchPojo.storetel}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and Mat_Storage.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Mat_Storage.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Mat_Storage.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Mat_Storage.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Mat_Storage.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null ">
            and Mat_Storage.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null ">
            and Mat_Storage.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Mat_Storage.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Mat_Storage.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Mat_Storage.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Mat_Storage.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Mat_Storage.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Mat_Storage.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Mat_Storage.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Mat_Storage.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Mat_Storage.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Mat_Storage.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.storecode != null ">
                or Mat_Storage.StoreCode like concat('%', #{SearchPojo.storecode}, '%')
            </if>
            <if test="SearchPojo.storename != null ">
                or Mat_Storage.StoreName like concat('%', #{SearchPojo.storename}, '%')
            </if>
            <if test="SearchPojo.storeadd != null ">
                or Mat_Storage.StoreAdd like concat('%', #{SearchPojo.storeadd}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Mat_Storage.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.storetel != null ">
                or Mat_Storage.StoreTel like concat('%', #{SearchPojo.storetel}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or Mat_Storage.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Mat_Storage.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Mat_Storage.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Mat_Storage.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Mat_Storage.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.deletelister != null ">
                or Mat_Storage.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
            <if test="SearchPojo.deletelisterid != null ">
                or Mat_Storage.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Mat_Storage.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Mat_Storage.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Mat_Storage.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Mat_Storage.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Mat_Storage.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Mat_Storage.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Mat_Storage.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Mat_Storage.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Mat_Storage.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Mat_Storage.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Storage(id, Projectid, ProjCode, ProjName, StoreCode, StoreName, StoreAdd, Operator, StoreTel, Remark, RowNum, AllowEdit,
                                AllowDelete, EnabledMark, CreateBy, CreateByid, CreateDate, Lister, Listerid,
                                ModifyDate, DeleteMark, DeleteLister, DeleteListerid, DeleteDate, MachMark, StoreType,
                                UsableMark, DmsMark,ScmMark, Custom1,
                                Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10,
                                Tenantid, Revision)
        values (#{id}, #{projectid}, #{projcode}, #{projname}, #{storecode}, #{storename}, #{storeadd}, #{operator}, #{storetel}, #{remark}, #{rownum},
                #{allowedit}, #{allowdelete}, #{enabledmark}, #{createby}, #{createbyid}, #{createdate}, #{lister},
                #{listerid}, #{modifydate}, #{deletemark}, #{deletelister}, #{deletelisterid}, #{deletedate},
                #{machmark}, #{storetype}, #{usablemark}, #{dmsmark}, #{scmmark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
                #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Storage
        <set>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="storecode != null ">
                StoreCode =#{storecode},
            </if>
            <if test="storename != null ">
                StoreName =#{storename},
            </if>
            <if test="storeadd != null ">
                StoreAdd =#{storeadd},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="storetel != null ">
                StoreTel =#{storetel},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="allowedit != null">
                AllowEdit =#{allowedit},
            </if>
            <if test="allowdelete != null">
                AllowDelete =#{allowdelete},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletelisterid != null ">
                DeleteListerid =#{deletelisterid},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="machmark != null">
                MachMark =#{machmark},
            </if>
            <if test="storetype != null">
                StoreType =#{storetype},
            </if>
            <if test="usablemark != null">
                UsableMark =#{usablemark},
            </if>
            <if test="dmsmark != null">
                DmsMark =#{dmsmark},
            </if>
            <if test="scmmark != null">
                ScmMark =#{scmmark},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Storage
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询指定行数据-->
    <select id="getMachList" resultType="inks.service.sa.som.domain.pojo.MatStoragePojo">
        <include refid="selectMatStorageVo"/>
        where MachMark=1 and Mat_Storage.Tenantid =#{tid}
        order by RowNum
    </select>

    <!--  查询仓库是否被引用  FIND_IN_SET(str, strlist)函数可以用来判断一个字符串是否在逗号分隔的另一个字符串中出现-->
    <select id="getItemCiteBillName" resultType="java.lang.String">
        (SELECT '出入库单' as billname From Mat_Access where Storeid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '货品默认仓' as billname From Mat_Goods where Storeid = #{key} and Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '货品授权仓' as billname
         From Mat_Goods
         where FIND_IN_SET(#{key}, StoreListGuid) and Tenantid = #{tid}
         LIMIT 1)
    </select>
    <!-- 检查是否存在相同的仓库编码或仓库名称 -->
    <select id="checkCodeOrName" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM Mat_Storage
        WHERE (StoreCode = #{storecode} OR StoreName = #{storename})
        AND tenantid = #{tid}
        LIMIT 1
    </select>
</mapper>

