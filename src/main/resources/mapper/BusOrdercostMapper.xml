<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BusOrdercostMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BusOrdercostPojo">
        SELECT Bus_OrderCost.id,
               Bus_OrderCost.RefNo,
               Bus_OrderCost.BillType,
               Bus_OrderCost.BillTitle,
               Bus_OrderCost.BillDate,
               Bus_OrderCost.Projectid,
               Bus_OrderCost.ProjName,
               Bus_OrderCost.ProjCode,
               Bus_OrderCost.Probability,
               Bus_OrderCost.Groupid,
               Bus_OrderCost.TraderCode,
               Bus_OrderCost.TraderName,
               Bus_OrderCost.CustAddress,
               Bus_OrderCost.<PERSON>,
               Bus_OrderCost.CustTel,
               Bus_OrderCost.CustFax,
               Bus_OrderCost.Periods,
               Bus_OrderCost.ValidityDate,
               Bus_OrderCost.Currency,
               Bus_OrderCost.Delivery,
               Bus_OrderCost.Payment,
               Bus_OrderCost.BillClause,
               Bus_OrderCost.Operator,
               Bus_OrderCost.BillTaxAmount,
               Bus_OrderCost.BillAmount,
               Bus_OrderCost.BillTaxTotal,
               Bus_OrderCost.Summary,
               Bus_OrderCost.CreateBy,
               Bus_OrderCost.CreateByid,
               Bus_OrderCost.CreateDate,
               Bus_OrderCost.Lister,
               Bus_OrderCost.Listerid,
               Bus_OrderCost.ModifyDate,
               Bus_OrderCost.Submitterid,
               Bus_OrderCost.Submitter,
               Bus_OrderCost.SubmitDate,
               Bus_OrderCost.Assessor,
               Bus_OrderCost.Assessorid,
               Bus_OrderCost.AssessDate,
               Bus_OrderCost.ItemCount,
               Bus_OrderCost.FinishCount,
               Bus_OrderCost.DisannulCount,
               Bus_OrderCost.PrintCount,
               Bus_OrderCost.Salesman,
               Bus_OrderCost.Salesmanid,
               Bus_OrderCost.Custom1,
               Bus_OrderCost.Custom2,
               Bus_OrderCost.Custom3,
               Bus_OrderCost.Custom4,
               Bus_OrderCost.Custom5,
               Bus_OrderCost.Custom6,
               Bus_OrderCost.Custom7,
               Bus_OrderCost.Custom8,
               Bus_OrderCost.Custom9,
               Bus_OrderCost.Custom10,
               Bus_OrderCost.Deptid,
               Bus_OrderCost.Tenantid,
               Bus_OrderCost.TenantName,
               Bus_OrderCost.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Bus_OrderCost ON App_Workgroup.id = Bus_OrderCost.Groupid
        where Bus_OrderCost.id = #{key}
          and Bus_OrderCost.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Bus_OrderCost.id,
               Bus_OrderCost.RefNo,
               Bus_OrderCost.BillType,
               Bus_OrderCost.BillTitle,
               Bus_OrderCost.BillDate,
               Bus_OrderCost.Projectid,
               Bus_OrderCost.ProjName,
               Bus_OrderCost.ProjCode,
               Bus_OrderCost.Probability,
               Bus_OrderCost.Groupid,
               Bus_OrderCost.TraderCode,
               Bus_OrderCost.TraderName,
               Bus_OrderCost.CustAddress,
               Bus_OrderCost.Linkman,
               Bus_OrderCost.CustTel,
               Bus_OrderCost.CustFax,
               Bus_OrderCost.Periods,
               Bus_OrderCost.ValidityDate,
               Bus_OrderCost.Currency,
               Bus_OrderCost.Delivery,
               Bus_OrderCost.Payment,
               Bus_OrderCost.BillClause,
               Bus_OrderCost.Operator,
               Bus_OrderCost.BillTaxAmount,
               Bus_OrderCost.BillAmount,
               Bus_OrderCost.BillTaxTotal,
               Bus_OrderCost.Summary,
               Bus_OrderCost.CreateBy,
               Bus_OrderCost.CreateByid,
               Bus_OrderCost.CreateDate,
               Bus_OrderCost.Lister,
               Bus_OrderCost.Listerid,
               Bus_OrderCost.ModifyDate,
               Bus_OrderCost.Submitterid,
               Bus_OrderCost.Submitter,
               Bus_OrderCost.SubmitDate,
               Bus_OrderCost.Assessor,
               Bus_OrderCost.Assessorid,
               Bus_OrderCost.AssessDate,
               Bus_OrderCost.ItemCount,
               Bus_OrderCost.FinishCount,
               Bus_OrderCost.DisannulCount,
               Bus_OrderCost.PrintCount,
               Bus_OrderCost.Salesman,
               Bus_OrderCost.Salesmanid,
               Bus_OrderCost.Custom1,
               Bus_OrderCost.Custom2,
               Bus_OrderCost.Custom3,
               Bus_OrderCost.Custom4,
               Bus_OrderCost.Custom5,
               Bus_OrderCost.Custom6,
               Bus_OrderCost.Custom7,
               Bus_OrderCost.Custom8,
               Bus_OrderCost.Custom9,
               Bus_OrderCost.Custom10,
               Bus_OrderCost.Deptid,
               Bus_OrderCost.Tenantid,
               Bus_OrderCost.TenantName,
               Bus_OrderCost.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM App_Workgroup
                 RIGHT JOIN Bus_OrderCost ON App_Workgroup.id = Bus_OrderCost.Groupid
    </sql>
    <sql id="selectdetailVo">
        SELECT Bus_OrderCostItem.id,
               Bus_OrderCostItem.Pid,
               Bus_OrderCostItem.Goodsid,
               Bus_OrderCostItem.ItemCode,
               Bus_OrderCostItem.ItemName,
               Bus_OrderCostItem.ItemSpec,
               Bus_OrderCostItem.ItemUnit,
               Bus_OrderCostItem.Quantity,
               Bus_OrderCostItem.StdPrice,
               Bus_OrderCostItem.StdAmount,
               Bus_OrderCostItem.Rebate,
               Bus_OrderCostItem.RebateSec,
               Bus_OrderCostItem.Price,
               Bus_OrderCostItem.Amount,
               Bus_OrderCostItem.ItemTaxrate,
               Bus_OrderCostItem.TaxPrice,
               Bus_OrderCostItem.TaxTotal,
               Bus_OrderCostItem.TaxAmount,
               Bus_OrderCostItem.PlanDate,
               Bus_OrderCostItem.RowNum,
               Bus_OrderCostItem.Remark,
               Bus_OrderCostItem.DisannulMark,
               Bus_OrderCostItem.DisannulListerid,
               Bus_OrderCostItem.DisannulLister,
               Bus_OrderCostItem.DisannulDate,
               Bus_OrderCostItem.VirtualItem,
               Bus_OrderCostItem.AttributeJson,
               Bus_OrderCostItem.CostItemJson,
               Bus_OrderCostItem.CostGroupJson,
               Bus_OrderCostItem.Closed,
               Bus_OrderCostItem.MachMark,
               Bus_OrderCostItem.CostItem1,
               Bus_OrderCostItem.CostItem2,
               Bus_OrderCostItem.CostItem3,
               Bus_OrderCostItem.CostItem4,
               Bus_OrderCostItem.CostItem5,
               Bus_OrderCostItem.CostItem6,
               Bus_OrderCostItem.CostItem7,
               Bus_OrderCostItem.CostItem8,
               Bus_OrderCostItem.CostItem9,
               Bus_OrderCostItem.CostItem10,
               Bus_OrderCostItem.Tenantid,
               Bus_OrderCostItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Bus_OrderCost.RefNo,
               Bus_OrderCost.BillType,
               Bus_OrderCost.BillTitle,
               Bus_OrderCost.BillDate,
               Bus_OrderCost.Projectid,
               Bus_OrderCost.ProjName,
               Bus_OrderCost.ProjCode,
               Bus_OrderCost.Probability,
               Bus_OrderCost.TraderCode,
               Bus_OrderCost.TraderName,
               Bus_OrderCost.Salesman,
               Bus_OrderCost.Salesmanid,
               Bus_OrderCost.Lister,
               Bus_OrderCost.CreateBy,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Bus_OrderCost
                 RIGHT JOIN Bus_OrderCostItem ON Bus_OrderCost.id = Bus_OrderCostItem.Pid
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Bus_OrderCostItem.Goodsid
                 LEFT JOIN App_Workgroup ON Bus_OrderCost.Groupid = App_Workgroup.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusOrdercostitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_OrderCost.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_OrderCost.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Bus_OrderCost.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_OrderCost.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_OrderCost.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.probability != null ">
            and Bus_OrderCost.probability like concat('%', #{SearchPojo.probability}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_OrderCost.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.tradercode != null ">
            and Bus_OrderCost.tradercode like concat('%', #{SearchPojo.tradercode}, '%')
        </if>
        <if test="SearchPojo.tradername != null ">
            and Bus_OrderCost.tradername like concat('%', #{SearchPojo.tradername}, '%')
        </if>
        <if test="SearchPojo.custaddress != null ">
            and Bus_OrderCost.custaddress like concat('%', #{SearchPojo.custaddress}, '%')
        </if>
        <if test="SearchPojo.linkman != null ">
            and Bus_OrderCost.linkman like concat('%', #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.custtel != null ">
            and Bus_OrderCost.custtel like concat('%', #{SearchPojo.custtel}, '%')
        </if>
        <if test="SearchPojo.custfax != null ">
            and Bus_OrderCost.custfax like concat('%', #{SearchPojo.custfax}, '%')
        </if>
        <if test="SearchPojo.periods != null ">
            and Bus_OrderCost.periods like concat('%', #{SearchPojo.periods}, '%')
        </if>
        <if test="SearchPojo.validitydate != null ">
            and Bus_OrderCost.validitydate like concat('%', #{SearchPojo.validitydate}, '%')
        </if>
        <if test="SearchPojo.currency != null ">
            and Bus_OrderCost.currency like concat('%', #{SearchPojo.currency}, '%')
        </if>
        <if test="SearchPojo.delivery != null ">
            and Bus_OrderCost.delivery like concat('%', #{SearchPojo.delivery}, '%')
        </if>
        <if test="SearchPojo.payment != null ">
            and Bus_OrderCost.payment like concat('%', #{SearchPojo.payment}, '%')
        </if>
        <if test="SearchPojo.billclause != null ">
            and Bus_OrderCost.billclause like concat('%', #{SearchPojo.billclause}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_OrderCost.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_OrderCost.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_OrderCost.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_OrderCost.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_OrderCost.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_OrderCost.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.submitterid != null ">
            and Bus_OrderCost.submitterid like concat('%', #{SearchPojo.submitterid}, '%')
        </if>
        <if test="SearchPojo.submitter != null ">
            and Bus_OrderCost.submitter like concat('%', #{SearchPojo.submitter}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Bus_OrderCost.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Bus_OrderCost.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_OrderCost.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_OrderCost.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_OrderCost.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_OrderCost.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_OrderCost.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_OrderCost.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_OrderCost.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_OrderCost.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_OrderCost.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_OrderCost.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Bus_OrderCost.deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_OrderCost.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
        <if test="SearchPojo.id != null and SearchPojo.id != ''">
            and Bus_OrderCostItem.id= #{SearchPojo.id}
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_OrderCost.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_OrderCost.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_OrderCost.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.probability != null ">
                or Bus_OrderCost.Probability like concat('%', #{SearchPojo.probability}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_OrderCost.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.tradercode != null ">
                or Bus_OrderCost.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
            </if>
            <if test="SearchPojo.tradername != null ">
                or Bus_OrderCost.TraderName like concat('%', #{SearchPojo.tradername}, '%')
            </if>
            <if test="SearchPojo.custaddress != null ">
                or Bus_OrderCost.CustAddress like concat('%', #{SearchPojo.custaddress}, '%')
            </if>
            <if test="SearchPojo.linkman != null ">
                or Bus_OrderCost.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.custtel != null ">
                or Bus_OrderCost.CustTel like concat('%', #{SearchPojo.custtel}, '%')
            </if>
            <if test="SearchPojo.custfax != null ">
                or Bus_OrderCost.CustFax like concat('%', #{SearchPojo.custfax}, '%')
            </if>
            <if test="SearchPojo.periods != null ">
                or Bus_OrderCost.Periods like concat('%', #{SearchPojo.periods}, '%')
            </if>
            <if test="SearchPojo.validitydate != null ">
                or Bus_OrderCost.ValidityDate like concat('%', #{SearchPojo.validitydate}, '%')
            </if>
            <if test="SearchPojo.currency != null ">
                or Bus_OrderCost.Currency like concat('%', #{SearchPojo.currency}, '%')
            </if>
            <if test="SearchPojo.delivery != null ">
                or Bus_OrderCost.Delivery like concat('%', #{SearchPojo.delivery}, '%')
            </if>
            <if test="SearchPojo.payment != null ">
                or Bus_OrderCost.Payment like concat('%', #{SearchPojo.payment}, '%')
            </if>
            <if test="SearchPojo.billclause != null ">
                or Bus_OrderCost.BillClause like concat('%', #{SearchPojo.billclause}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_OrderCost.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_OrderCost.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_OrderCost.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_OrderCost.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_OrderCost.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_OrderCost.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.submitterid != null ">
                or Bus_OrderCost.Submitterid like concat('%', #{SearchPojo.submitterid}, '%')
            </if>
            <if test="SearchPojo.submitter != null ">
                or Bus_OrderCost.Submitter like concat('%', #{SearchPojo.submitter}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Bus_OrderCost.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Bus_OrderCost.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_OrderCost.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_OrderCost.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_OrderCost.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_OrderCost.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_OrderCost.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_OrderCost.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_OrderCost.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_OrderCost.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_OrderCost.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_OrderCost.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Bus_OrderCost.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_OrderCost.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
            <if test="SearchPojo.id != null and SearchPojo.id != ''">
                or Bus_OrderCostItem.id= #{SearchPojo.id}
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusOrdercostPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_OrderCost.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_OrderCost.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Bus_OrderCost.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_OrderCost.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_OrderCost.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.probability != null ">
            and Bus_OrderCost.Probability like concat('%', #{SearchPojo.probability}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_OrderCost.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.tradercode != null ">
            and Bus_OrderCost.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
        </if>
        <if test="SearchPojo.tradername != null ">
            and Bus_OrderCost.TraderName like concat('%', #{SearchPojo.tradername}, '%')
        </if>
        <if test="SearchPojo.custaddress != null ">
            and Bus_OrderCost.CustAddress like concat('%', #{SearchPojo.custaddress}, '%')
        </if>
        <if test="SearchPojo.linkman != null ">
            and Bus_OrderCost.Linkman like concat('%', #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.custtel != null ">
            and Bus_OrderCost.CustTel like concat('%', #{SearchPojo.custtel}, '%')
        </if>
        <if test="SearchPojo.custfax != null ">
            and Bus_OrderCost.CustFax like concat('%', #{SearchPojo.custfax}, '%')
        </if>
        <if test="SearchPojo.periods != null ">
            and Bus_OrderCost.Periods like concat('%', #{SearchPojo.periods}, '%')
        </if>
        <if test="SearchPojo.validitydate != null ">
            and Bus_OrderCost.ValidityDate like concat('%', #{SearchPojo.validitydate}, '%')
        </if>
        <if test="SearchPojo.currency != null ">
            and Bus_OrderCost.Currency like concat('%', #{SearchPojo.currency}, '%')
        </if>
        <if test="SearchPojo.delivery != null ">
            and Bus_OrderCost.Delivery like concat('%', #{SearchPojo.delivery}, '%')
        </if>
        <if test="SearchPojo.payment != null ">
            and Bus_OrderCost.Payment like concat('%', #{SearchPojo.payment}, '%')
        </if>
        <if test="SearchPojo.billclause != null ">
            and Bus_OrderCost.BillClause like concat('%', #{SearchPojo.billclause}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_OrderCost.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_OrderCost.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_OrderCost.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_OrderCost.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_OrderCost.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_OrderCost.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.submitterid != null ">
            and Bus_OrderCost.Submitterid like concat('%', #{SearchPojo.submitterid}, '%')
        </if>
        <if test="SearchPojo.submitter != null ">
            and Bus_OrderCost.Submitter like concat('%', #{SearchPojo.submitter}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Bus_OrderCost.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Bus_OrderCost.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_OrderCost.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_OrderCost.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_OrderCost.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_OrderCost.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_OrderCost.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_OrderCost.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_OrderCost.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_OrderCost.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_OrderCost.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_OrderCost.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Bus_OrderCost.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_OrderCost.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_OrderCost.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_OrderCost.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_OrderCost.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.probability != null ">
                or Bus_OrderCost.Probability like concat('%', #{SearchPojo.probability}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_OrderCost.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.tradercode != null ">
                or Bus_OrderCost.TraderCode like concat('%', #{SearchPojo.tradercode}, '%')
            </if>
            <if test="SearchPojo.tradername != null ">
                or Bus_OrderCost.TraderName like concat('%', #{SearchPojo.tradername}, '%')
            </if>
            <if test="SearchPojo.custaddress != null ">
                or Bus_OrderCost.CustAddress like concat('%', #{SearchPojo.custaddress}, '%')
            </if>
            <if test="SearchPojo.linkman != null ">
                or Bus_OrderCost.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.custtel != null ">
                or Bus_OrderCost.CustTel like concat('%', #{SearchPojo.custtel}, '%')
            </if>
            <if test="SearchPojo.custfax != null ">
                or Bus_OrderCost.CustFax like concat('%', #{SearchPojo.custfax}, '%')
            </if>
            <if test="SearchPojo.periods != null ">
                or Bus_OrderCost.Periods like concat('%', #{SearchPojo.periods}, '%')
            </if>
            <if test="SearchPojo.validitydate != null ">
                or Bus_OrderCost.ValidityDate like concat('%', #{SearchPojo.validitydate}, '%')
            </if>
            <if test="SearchPojo.currency != null ">
                or Bus_OrderCost.Currency like concat('%', #{SearchPojo.currency}, '%')
            </if>
            <if test="SearchPojo.delivery != null ">
                or Bus_OrderCost.Delivery like concat('%', #{SearchPojo.delivery}, '%')
            </if>
            <if test="SearchPojo.payment != null ">
                or Bus_OrderCost.Payment like concat('%', #{SearchPojo.payment}, '%')
            </if>
            <if test="SearchPojo.billclause != null ">
                or Bus_OrderCost.BillClause like concat('%', #{SearchPojo.billclause}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_OrderCost.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_OrderCost.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_OrderCost.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_OrderCost.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_OrderCost.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_OrderCost.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.submitterid != null ">
                or Bus_OrderCost.Submitterid like concat('%', #{SearchPojo.submitterid}, '%')
            </if>
            <if test="SearchPojo.submitter != null ">
                or Bus_OrderCost.Submitter like concat('%', #{SearchPojo.submitter}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Bus_OrderCost.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Bus_OrderCost.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_OrderCost.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_OrderCost.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_OrderCost.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_OrderCost.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_OrderCost.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_OrderCost.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_OrderCost.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_OrderCost.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_OrderCost.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_OrderCost.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Bus_OrderCost.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_OrderCost.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_OrderCost(id, RefNo, BillType, BillTitle, BillDate, Projectid, ProjCode, ProjName, Probability, Groupid, TraderCode,
                                  TraderName, CustAddress, Linkman, CustTel, CustFax, Periods, ValidityDate, Currency,
                                  Delivery, Payment, BillClause, Operator, BillTaxAmount, BillAmount, BillTaxTotal,
                                  Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Submitterid,
                                  Submitter, SubmitDate, Assessor, Assessorid, AssessDate, ItemCount, FinishCount,
                                  DisannulCount, PrintCount, Salesman, Salesmanid, Custom1, Custom2, Custom3, Custom4,
                                  Custom5, Custom6,
                                  Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{probability}, #{groupid}, #{tradercode},
                #{tradername}, #{custaddress}, #{linkman}, #{custtel}, #{custfax}, #{periods}, #{validitydate},
                #{currency}, #{delivery}, #{payment}, #{billclause}, #{operator}, #{billtaxamount}, #{billamount},
                #{billtaxtotal}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{submitterid}, #{submitter}, #{submitdate}, #{assessor}, #{assessorid}, #{assessdate},
                #{itemcount}, #{finishcount}, #{disannulcount}, #{printcount}, #{salesman}, #{salesmanid}, #{custom1},
                #{custom2}, #{custom3},
                #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid},
                #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_OrderCost
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="probability != null ">
                Probability =#{probability},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="tradercode != null ">
                TraderCode =#{tradercode},
            </if>
            <if test="tradername != null ">
                TraderName =#{tradername},
            </if>
            <if test="custaddress != null ">
                CustAddress =#{custaddress},
            </if>
            <if test="linkman != null ">
                Linkman =#{linkman},
            </if>
            <if test="custtel != null ">
                CustTel =#{custtel},
            </if>
            <if test="custfax != null ">
                CustFax =#{custfax},
            </if>
            <if test="periods != null ">
                Periods =#{periods},
            </if>
            <if test="validitydate != null ">
                ValidityDate =#{validitydate},
            </if>
            <if test="currency != null ">
                Currency =#{currency},
            </if>
            <if test="delivery != null ">
                Delivery =#{delivery},
            </if>
            <if test="payment != null ">
                Payment =#{payment},
            </if>
            <if test="billclause != null ">
                BillClause =#{billclause},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="submitterid != null ">
                Submitterid =#{submitterid},
            </if>
            <if test="submitter != null ">
                Submitter =#{submitter},
            </if>
            <if test="submitdate != null">
                SubmitDate =#{submitdate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="salesman != null ">
                Salesman =#{salesman},
            </if>
            <if test="salesmanid != null ">
                Salesmanid =#{salesmanid},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_OrderCost
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Bus_OrderCost
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BusOrdercostPojo">
        select
        id
        from Bus_OrderCostItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <update id="updateDisannulCount">
        update Bus_OrderCost
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Bus_OrderCostItem
                                     where Bus_OrderCostItem.Pid = #{key}
                                       and Bus_OrderCostItem.Tenantid = #{tid}
                                       and Bus_OrderCostItem.DisannulMark = 1), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <update id="updateFinishCount">
        update Bus_OrderCost
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Bus_OrderCostItem
                                   where Bus_OrderCostItem.Pid = #{key}
                                     and Bus_OrderCostItem.Tenantid = #{tid}
                                     and Bus_OrderCostItem.Closed = 1), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--  查询核价单是否被引用  -->
    <select id="getItemCiteBillName" resultType="java.lang.String">
        (SELECT '销售订单' as billname
         From Bus_MachiningItem
         where (OrderCostItemid = #{key} or QuotItemid=#{key}) and Tenantid = #{tid}
         LIMIT 1)
    </select>

</mapper>

