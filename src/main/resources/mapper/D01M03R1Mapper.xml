<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.D01M03R1Mapper">
    <!--    /*客户订单金额排名*/-->
    <select id="getSumAmtByGroupMax" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        select IFNULL(SUM(Bus_MachiningItem.TaxAmount),0) as valueb,(case when App_Workgroup.Abbreviate is null or
        App_Workgroup.Abbreviate = '' then App_Workgroup.GroupName else Abbreviate end) as name
        FROM Bus_Machining
            RIGHT JOIN Bus_MachiningItem
        ON Bus_MachiningItem.Pid = Bus_Machining.id
            LEFT JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
            LEFT JOIN Mat_Goods ON Bus_MachiningItem.Goodsid = Mat_Goods.id
        where Bus_MachiningItem.disannulmark=0
          and Bus_Machining.Tenantid =#{tenantid}
          and (${DateRange.DateColumn} BETWEEN #{DateRange.StartDate}
          and #{DateRange.EndDate})
        group by App_Workgroup.Abbreviate, App_Workgroup.GroupName
        order by value desc
    </select>
    <select id="getPageList"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT
        DATE_FORMAT( now(), '%Y-%m-%d' ) AS NAME,
        count( 1 ) AS
        VALUE

        FROM
        Bus_Machining
        LEFT JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE
        Bus_MachiningItem.Quantity > Bus_MachiningItem.OutMatQty
        AND DATE_FORMAT( Bus_MachiningItem.ItemOrgDate, '%Y-%m-%d' ) &gt;= DATE_FORMAT(
        now(),
        '%Y-%m-%d') and
        Bus_MachiningItem.Tenantid = #{tid}
    </select>
    <select id="getSumAmtByGoodsMax" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT
            Mat_Goods.GoodsName AS NAME,
            SUM(Bus_MachiningItem.TaxAmount) AS
        valueb

        FROM
            Mat_Goods
            RIGHT OUTER JOIN App_Workgroup
            RIGHT OUTER JOIN Bus_Machining ON App_Workgroup.id = Bus_Machining.Groupid
            RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid ON Mat_Goods.id = Bus_MachiningItem.Goodsid
        WHERE Bus_MachiningItem.Tenantid = #{Tenantid}
        GROUP BY Mat_Goods.GoodsName
        ORDER BY VALUE desc
            LIMIT 0,#{PageSize}
    </select>
    <select id="getSumAmtBySalesman" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT
            Bus_Machining.Salesman AS NAME,
            SUM( Bus_MachiningItem.TaxAmount ) AS valueb
        FROM
            Bus_Machining
            LEFT OUTER JOIN App_Workgroup ON Bus_Machining.Groupid = App_Workgroup.id
            RIGHT OUTER JOIN Bus_MachiningItem ON Bus_Machining.id = Bus_MachiningItem.Pid
        WHERE
            Bus_MachiningItem.Tenantid = #{Tenantid}
        GROUP BY
            Bus_Machining.Salesman
        ORDER BY
            VALUE
    </select>
</mapper>

