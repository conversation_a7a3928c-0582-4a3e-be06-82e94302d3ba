<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.WkMrplogMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.WkMrplogPojo">
        select
          id, Pid, RemarkJson, OperTitle, BusinessType, Method, RequestMethod, OperatorType, OperUserid, OperName, DeptName, OperUrl, OperIp, OperLocation, OperParam, JsonResult, Status, ErrorMsg, OperTime, Tenantid        from Wk_MrpLog
        where Wk_MrpLog.id = #{key} and Wk_MrpLog.Tenantid=#{tid}
    </select>
    <sql id="selectWkMrplogVo">
         select
          id, Pid, <PERSON>mark<PERSON><PERSON>, OperTitle, BusinessType, Method, RequestMethod, OperatorType, OperUserid, OperName, DeptName, OperUrl, OperIp, OperLocation, OperParam, JsonResult, Status, ErrorMsg, OperTime, Tenantid        from Wk_MrpLog
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.som.domain.pojo.WkMrplogPojo">
        <include refid="selectWkMrplogVo"/>
         where 1 = 1 and Wk_MrpLog.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Wk_MrpLog.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Wk_MrpLog.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.remarkjson != null and SearchPojo.remarkjson != ''">
   and Wk_MrpLog.remarkjson like concat('%', #{SearchPojo.remarkjson}, '%')
</if>
<if test="SearchPojo.opertitle != null and SearchPojo.opertitle != ''">
   and Wk_MrpLog.opertitle like concat('%', #{SearchPojo.opertitle}, '%')
</if>
<if test="SearchPojo.method != null and SearchPojo.method != ''">
   and Wk_MrpLog.method like concat('%', #{SearchPojo.method}, '%')
</if>
<if test="SearchPojo.requestmethod != null and SearchPojo.requestmethod != ''">
   and Wk_MrpLog.requestmethod like concat('%', #{SearchPojo.requestmethod}, '%')
</if>
<if test="SearchPojo.operuserid != null and SearchPojo.operuserid != ''">
   and Wk_MrpLog.operuserid like concat('%', #{SearchPojo.operuserid}, '%')
</if>
<if test="SearchPojo.opername != null and SearchPojo.opername != ''">
   and Wk_MrpLog.opername like concat('%', #{SearchPojo.opername}, '%')
</if>
<if test="SearchPojo.deptname != null and SearchPojo.deptname != ''">
   and Wk_MrpLog.deptname like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.operurl != null and SearchPojo.operurl != ''">
   and Wk_MrpLog.operurl like concat('%', #{SearchPojo.operurl}, '%')
</if>
<if test="SearchPojo.operip != null and SearchPojo.operip != ''">
   and Wk_MrpLog.operip like concat('%', #{SearchPojo.operip}, '%')
</if>
<if test="SearchPojo.operlocation != null and SearchPojo.operlocation != ''">
   and Wk_MrpLog.operlocation like concat('%', #{SearchPojo.operlocation}, '%')
</if>
<if test="SearchPojo.operparam != null and SearchPojo.operparam != ''">
   and Wk_MrpLog.operparam like concat('%', #{SearchPojo.operparam}, '%')
</if>
<if test="SearchPojo.jsonresult != null and SearchPojo.jsonresult != ''">
   and Wk_MrpLog.jsonresult like concat('%', #{SearchPojo.jsonresult}, '%')
</if>
<if test="SearchPojo.errormsg != null and SearchPojo.errormsg != ''">
   and Wk_MrpLog.errormsg like concat('%', #{SearchPojo.errormsg}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Wk_MrpLog.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.remarkjson != null and SearchPojo.remarkjson != ''">
   or Wk_MrpLog.RemarkJson like concat('%', #{SearchPojo.remarkjson}, '%')
</if>
<if test="SearchPojo.opertitle != null and SearchPojo.opertitle != ''">
   or Wk_MrpLog.OperTitle like concat('%', #{SearchPojo.opertitle}, '%')
</if>
<if test="SearchPojo.method != null and SearchPojo.method != ''">
   or Wk_MrpLog.Method like concat('%', #{SearchPojo.method}, '%')
</if>
<if test="SearchPojo.requestmethod != null and SearchPojo.requestmethod != ''">
   or Wk_MrpLog.RequestMethod like concat('%', #{SearchPojo.requestmethod}, '%')
</if>
<if test="SearchPojo.operuserid != null and SearchPojo.operuserid != ''">
   or Wk_MrpLog.OperUserid like concat('%', #{SearchPojo.operuserid}, '%')
</if>
<if test="SearchPojo.opername != null and SearchPojo.opername != ''">
   or Wk_MrpLog.OperName like concat('%', #{SearchPojo.opername}, '%')
</if>
<if test="SearchPojo.deptname != null and SearchPojo.deptname != ''">
   or Wk_MrpLog.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.operurl != null and SearchPojo.operurl != ''">
   or Wk_MrpLog.OperUrl like concat('%', #{SearchPojo.operurl}, '%')
</if>
<if test="SearchPojo.operip != null and SearchPojo.operip != ''">
   or Wk_MrpLog.OperIp like concat('%', #{SearchPojo.operip}, '%')
</if>
<if test="SearchPojo.operlocation != null and SearchPojo.operlocation != ''">
   or Wk_MrpLog.OperLocation like concat('%', #{SearchPojo.operlocation}, '%')
</if>
<if test="SearchPojo.operparam != null and SearchPojo.operparam != ''">
   or Wk_MrpLog.OperParam like concat('%', #{SearchPojo.operparam}, '%')
</if>
<if test="SearchPojo.jsonresult != null and SearchPojo.jsonresult != ''">
   or Wk_MrpLog.JsonResult like concat('%', #{SearchPojo.jsonresult}, '%')
</if>
<if test="SearchPojo.errormsg != null and SearchPojo.errormsg != ''">
   or Wk_MrpLog.ErrorMsg like concat('%', #{SearchPojo.errormsg}, '%')
</if>
</trim>
     </sql>
     
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.WkMrplogPojo">
        select
          id, Pid, RemarkJson, OperTitle, BusinessType, Method, RequestMethod, OperatorType, OperUserid, OperName, DeptName, OperUrl, OperIp, OperLocation, OperParam, JsonResult, Status, ErrorMsg, OperTime, Tenantid        from Wk_MrpLog
        where Wk_MrpLog.Pid = #{Pid} and Wk_MrpLog.Tenantid=#{tid}
        order by OperTime
    </select>
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Wk_MrpLog(id, Pid, RemarkJson, OperTitle, BusinessType, Method, RequestMethod, OperatorType, OperUserid, OperName, DeptName, OperUrl, OperIp, OperLocation, OperParam, JsonResult, Status, ErrorMsg, OperTime, Tenantid)
        values (#{id}, #{pid}, #{remarkjson}, #{opertitle}, #{businesstype}, #{method}, #{requestmethod}, #{operatortype}, #{operuserid}, #{opername}, #{deptname}, #{operurl}, #{operip}, #{operlocation}, #{operparam}, #{jsonresult}, #{status}, #{errormsg}, #{opertime}, #{tenantid})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_MrpLog
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="remarkjson != null ">
                RemarkJson = #{remarkjson},
            </if>
            <if test="opertitle != null ">
                OperTitle = #{opertitle},
            </if>
            <if test="businesstype != null">
                BusinessType = #{businesstype},
            </if>
            <if test="method != null ">
                Method = #{method},
            </if>
            <if test="requestmethod != null ">
                RequestMethod = #{requestmethod},
            </if>
            <if test="operatortype != null">
                OperatorType = #{operatortype},
            </if>
            <if test="operuserid != null ">
                OperUserid = #{operuserid},
            </if>
            <if test="opername != null ">
                OperName = #{opername},
            </if>
            <if test="deptname != null ">
                DeptName = #{deptname},
            </if>
            <if test="operurl != null ">
                OperUrl = #{operurl},
            </if>
            <if test="operip != null ">
                OperIp = #{operip},
            </if>
            <if test="operlocation != null ">
                OperLocation = #{operlocation},
            </if>
            <if test="operparam != null ">
                OperParam = #{operparam},
            </if>
            <if test="jsonresult != null ">
                JsonResult = #{jsonresult},
            </if>
            <if test="status != null">
                Status = #{status},
            </if>
            <if test="errormsg != null ">
                ErrorMsg = #{errormsg},
            </if>
            <if test="opertime != null">
                OperTime = #{opertime},
            </if>
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Wk_MrpLog where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

