<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.FmIncomeitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.FmIncomeitemPojo">
        SELECT
            Fm_IncomeItem.id,
            Fm_IncomeItem.Pid,
            Fm_IncomeItem.CostTypeid,
            Fm_IncomeItem.ItemName,
            Fm_IncomeItem.ItemDepict,
            Fm_IncomeItem.Amount,
            Fm_IncomeItem.RowNum,
            Fm_IncomeItem.Remark,
            Fm_IncomeItem.Custom1,
            Fm_IncomeItem.Custom2,
            Fm_IncomeItem.Custom3,
            Fm_IncomeItem.Custom4,
            Fm_IncomeItem.Custom5,
            Fm_IncomeItem.Custom6,
            Fm_IncomeItem.Custom7,
            Fm_IncomeItem.Custom8,
            Fm_IncomeItem.Custom9,
            Fm_IncomeItem.Custom10,
            Fm_IncomeItem.Tenantid,
            Fm_IncomeItem.Revision,
            Fm_CostType.CostCode,
            Fm_CostType.CostName
        FROM
            Fm_IncomeItem
                LEFT JOIN Fm_CostType ON Fm_IncomeItem.CostTypeid = Fm_CostType.id
        where Fm_IncomeItem.id = #{key}
          and Fm_IncomeItem.Tenantid = #{tid}
    </select>
    <sql id="selectFmIncomeitemVo">
        SELECT
            Fm_IncomeItem.id,
            Fm_IncomeItem.Pid,
            Fm_IncomeItem.CostTypeid,
            Fm_IncomeItem.ItemName,
            Fm_IncomeItem.ItemDepict,
            Fm_IncomeItem.Amount,
            Fm_IncomeItem.RowNum,
            Fm_IncomeItem.Remark,
            Fm_IncomeItem.Custom1,
            Fm_IncomeItem.Custom2,
            Fm_IncomeItem.Custom3,
            Fm_IncomeItem.Custom4,
            Fm_IncomeItem.Custom5,
            Fm_IncomeItem.Custom6,
            Fm_IncomeItem.Custom7,
            Fm_IncomeItem.Custom8,
            Fm_IncomeItem.Custom9,
            Fm_IncomeItem.Custom10,
            Fm_IncomeItem.Tenantid,
            Fm_IncomeItem.Revision,
            Fm_CostType.CostCode,
            Fm_CostType.CostName
        FROM
            Fm_IncomeItem
                LEFT JOIN Fm_CostType ON Fm_IncomeItem.CostTypeid = Fm_CostType.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.FmIncomeitemPojo">
        <include refid="selectFmIncomeitemVo"/>
        where 1 = 1 and Fm_IncomeItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Fm_IncomeItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Fm_IncomeItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.costtypeid != null and SearchPojo.costtypeid != ''">
            and Fm_IncomeItem.costtypeid like concat('%', #{SearchPojo.costtypeid}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Fm_IncomeItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemdepict != null and SearchPojo.itemdepict != ''">
            and Fm_IncomeItem.itemdepict like concat('%', #{SearchPojo.itemdepict}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Fm_IncomeItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Fm_IncomeItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Fm_IncomeItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Fm_IncomeItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Fm_IncomeItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Fm_IncomeItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Fm_IncomeItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Fm_IncomeItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Fm_IncomeItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Fm_IncomeItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Fm_IncomeItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Fm_IncomeItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.costtypeid != null and SearchPojo.costtypeid != ''">
                or Fm_IncomeItem.CostTypeid like concat('%', #{SearchPojo.costtypeid}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Fm_IncomeItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemdepict != null and SearchPojo.itemdepict != ''">
                or Fm_IncomeItem.ItemDepict like concat('%', #{SearchPojo.itemdepict}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Fm_IncomeItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Fm_IncomeItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Fm_IncomeItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Fm_IncomeItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Fm_IncomeItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Fm_IncomeItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Fm_IncomeItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Fm_IncomeItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Fm_IncomeItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Fm_IncomeItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Fm_IncomeItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.FmIncomeitemPojo">
        SELECT
            Fm_IncomeItem.id,
            Fm_IncomeItem.Pid,
            Fm_IncomeItem.CostTypeid,
            Fm_IncomeItem.ItemName,
            Fm_IncomeItem.ItemDepict,
            Fm_IncomeItem.Amount,
            Fm_IncomeItem.RowNum,
            Fm_IncomeItem.Remark,
            Fm_IncomeItem.Custom1,
            Fm_IncomeItem.Custom2,
            Fm_IncomeItem.Custom3,
            Fm_IncomeItem.Custom4,
            Fm_IncomeItem.Custom5,
            Fm_IncomeItem.Custom6,
            Fm_IncomeItem.Custom7,
            Fm_IncomeItem.Custom8,
            Fm_IncomeItem.Custom9,
            Fm_IncomeItem.Custom10,
            Fm_IncomeItem.Tenantid,
            Fm_IncomeItem.Revision,
            Fm_CostType.CostCode,
            Fm_CostType.CostName
        FROM
            Fm_IncomeItem
                LEFT JOIN Fm_CostType ON Fm_IncomeItem.CostTypeid = Fm_CostType.id
        where Fm_IncomeItem.Pid = #{Pid}
          and Fm_IncomeItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Fm_IncomeItem(id, Pid, CostTypeid, ItemName, ItemDepict, Amount, RowNum, Remark, Custom1, Custom2,
                                  Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid,
                                  Revision)
        values (#{id}, #{pid}, #{costtypeid}, #{itemname}, #{itemdepict}, #{amount}, #{rownum}, #{remark}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Fm_IncomeItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="costtypeid != null ">
                CostTypeid = #{costtypeid},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemdepict != null ">
                ItemDepict = #{itemdepict},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Fm_IncomeItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

