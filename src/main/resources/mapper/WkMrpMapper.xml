<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.WkMrpMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.WkMrpPojo">
        select id,
               RefNo,
               BillType,
               BillDate,
               BillTitle,
               MrpDate,
               Operator,
               Operatorid,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               ObjCount,
               ItemCount,
               FinishCount,
               PrintCount,
               WkItemCount,
               WkFinishCount,
               WsItemCount,
               WsFinishCount,
               BuyItemCount,
               BuyFinishCount,
               CsItemCount,
               CsFinishCount,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Wk_Mrp
        where Wk_Mrp.id = #{key}
          and Wk_Mrp.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               RefNo,
               BillType,
               BillDate,
               BillTitle,
               MrpDate,
               Operator,
               Operatorid,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               ObjCount,
               ItemCount,
               FinishCount,
               PrintCount,
               WkItemCount,
               WkFinishCount,
               WsItemCount,
               WsFinishCount,
               BuyItemCount,
               BuyFinishCount,
               CsItemCount,
               CsFinishCount,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Wk_Mrp
    </sql>
    <sql id="selectdetailVo">
        SELECT
            Wk_MrpItem.id,
            Wk_MrpItem.Pid,
            Wk_MrpItem.ItemParentid,
            Wk_MrpItem.MrpObjid,
            Wk_MrpItem.LevelNum,
            Wk_MrpItem.LevelSymbol,
            Wk_MrpItem.Goodsid,
            Wk_MrpItem.ItemCode,
            Wk_MrpItem.ItemName,
            Wk_MrpItem.ItemSpec,
            Wk_MrpItem.ItemUnit,
            Wk_MrpItem.Bomid,
            Wk_MrpItem.BomType,
            Wk_MrpItem.BomItemid,
            Wk_MrpItem.SubQty,
            Wk_MrpItem.MainQty,
            Wk_MrpItem.LossRate,
            Wk_MrpItem.AttrCode,
            Wk_MrpItem.FlowCode,
            Wk_MrpItem.BomQty,
            Wk_MrpItem.StoQty,
            Wk_MrpItem.SafeStock,
            Wk_MrpItem.NeedQty,
            Wk_MrpItem.RealQty,
            Wk_MrpItem.WorkDate,
            Wk_MrpItem.PlanDate,
            Wk_MrpItem.Remark,
            Wk_MrpItem.EnabledMark,
            Wk_MrpItem.Closed,
            Wk_MrpItem.RowNum,
            Wk_MrpItem.BuyPlanQty,
            Wk_MrpItem.BuyOrderQty,
            Wk_MrpItem.CustSuppQty,
            Wk_MrpItem.WkWsQty,
            Wk_MrpItem.WkScQty,
            Wk_MrpItem.OtherQty,
            Wk_MrpItem.MatReqQty,
            Wk_MrpItem.MatCompQty,
            Wk_MrpItem.MatIvQty,
            Wk_MrpItem.BuyRemQty,
            Wk_MrpItem.WkWsRemQty,
            Wk_MrpItem.WkScRemQty,
            Wk_MrpItem.BusRemQty,
            Wk_MrpItem.MrpRemQty,
            Wk_MrpItem.FreeReqRemQty,
            Wk_MrpItem.ReqRemQty,
            Wk_MrpItem.Groupid,
            Wk_MrpItem.GroupName,
            Wk_MrpItem.MatReqRtQty,
            Wk_MrpItem.MatCompRtQty,
            Wk_MrpItem.WkFinishQty,
            Wk_MrpItem.BuyFinishQty,
            Wk_MrpItem.CustFinishQty,
            Wk_MrpItem.MachUid,
            Wk_MrpItem.MachItemid,
            Wk_MrpItem.MachBatch,
            Wk_MrpItem.MachGroupid,
            Wk_MrpItem.MainPlanUid,
            Wk_MrpItem.MainPlanItemid,
            Wk_MrpItem.BomRound,
            Wk_MrpItem.BomDate,
            Wk_MrpItem.BomMark,
            Wk_MrpItem.AttributeJson,
        Wk_MrpItem.MatType,
            Wk_MrpItem.Custom1,
            Wk_MrpItem.Custom2,
            Wk_MrpItem.Custom3,
            Wk_MrpItem.Custom4,
            Wk_MrpItem.Custom5,
            Wk_MrpItem.Custom6,
            Wk_MrpItem.Custom7,
            Wk_MrpItem.Custom8,
            Wk_MrpItem.Custom9,
            Wk_MrpItem.Custom10,
            Wk_MrpItem.Tenantid,
            Wk_MrpItem.TenantName,
            Wk_MrpItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Wk_Mrp.RefNo,
            Wk_Mrp.BillDate,
            Wk_Mrp.BillType,
            Wk_Mrp.BillTitle,
            Wk_Mrp.Operator,
            Wk_Mrp.CreateBy,
            Wk_Mrp.Lister
        FROM
            Mat_Goods
                RIGHT JOIN Wk_MrpItem ON Wk_MrpItem.Goodsid = Mat_Goods.id
                Left Join Wk_Mrp on Wk_MrpItem.Pid = Wk_Mrp.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.WkMrpitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Wk_Mrp.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_Mrp.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>



    <select id="getObjPageList" resultType="inks.service.sa.som.domain.pojo.WkMrpobjdetailPojo">
        SELECT
        Wk_MrpObj.*,
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.selectGoods"/>
        Wk_Mrp.RefNo,
        Wk_Mrp.BillDate,
        Wk_Mrp.BillType,
        Wk_Mrp.BillTitle,
        Wk_Mrp.Operator,
        Wk_Mrp.CreateBy,
        Wk_Mrp.Lister
        FROM
        Mat_Goods
        RIGHT JOIN Wk_MrpObj ON Wk_MrpObj.Goodsid = Mat_Goods.id
        Left Join Wk_Mrp on Wk_MrpObj.Pid = Wk_Mrp.id
        where 1 = 1 and Wk_Mrp.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Wk_Mrp.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>

    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Wk_Mrp.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_Mrp.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_Mrp.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Wk_Mrp.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Wk_Mrp.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_Mrp.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_Mrp.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_Mrp.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_Mrp.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_Mrp.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_Mrp.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_Mrp.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_Mrp.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_Mrp.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_Mrp.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_Mrp.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_Mrp.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_Mrp.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_Mrp.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_Mrp.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_Mrp.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_Mrp.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_Mrp.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_Mrp.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Wk_Mrp.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Wk_Mrp.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_Mrp.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_Mrp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_Mrp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_Mrp.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_Mrp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_Mrp.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_Mrp.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_Mrp.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_Mrp.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_Mrp.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_Mrp.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_Mrp.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_Mrp.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_Mrp.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_Mrp.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_Mrp.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.WkMrpPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Wk_Mrp.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_Mrp.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Wk_Mrp.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Wk_Mrp.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Wk_Mrp.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Wk_Mrp.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Wk_Mrp.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Wk_Mrp.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Wk_Mrp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Wk_Mrp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Wk_Mrp.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Wk_Mrp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Wk_Mrp.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Wk_Mrp.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Wk_Mrp.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Wk_Mrp.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Wk_Mrp.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Wk_Mrp.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Wk_Mrp.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Wk_Mrp.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Wk_Mrp.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Wk_Mrp.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Wk_Mrp.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Wk_Mrp.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Wk_Mrp.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Wk_Mrp.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Wk_Mrp.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Wk_Mrp.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Wk_Mrp.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Wk_Mrp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Wk_Mrp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Wk_Mrp.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Wk_Mrp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Wk_Mrp.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Wk_Mrp.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Wk_Mrp.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Wk_Mrp.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Wk_Mrp.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Wk_Mrp.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Wk_Mrp.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Wk_Mrp.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Wk_Mrp.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Wk_Mrp.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Wk_Mrp.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_Mrp(id, RefNo, BillType, BillDate, BillTitle, MrpDate, Operator, Operatorid, Summary, CreateBy,
                           CreateByid, CreateDate, Lister, Listerid, ModifyDate, ObjCount, ItemCount, FinishCount,
                           PrintCount, WkItemCount, WkFinishCount, WsItemCount, WsFinishCount, BuyItemCount,
                           BuyFinishCount, CsItemCount, CsFinishCount, Custom1, Custom2, Custom3, Custom4, Custom5,
                           Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{mrpdate}, #{operator}, #{operatorid},
                #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
                #{objcount}, #{itemcount}, #{finishcount}, #{printcount}, #{wkitemcount}, #{wkfinishcount},
                #{wsitemcount}, #{wsfinishcount}, #{buyitemcount}, #{buyfinishcount}, #{csitemcount}, #{csfinishcount},
                #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8},
                #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_Mrp
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="mrpdate != null">
                MrpDate =#{mrpdate},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="objcount != null">
                ObjCount =#{objcount},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="wkitemcount != null">
                WkItemCount =#{wkitemcount},
            </if>
            <if test="wkfinishcount != null">
                WkFinishCount =#{wkfinishcount},
            </if>
            <if test="wsitemcount != null">
                WsItemCount =#{wsitemcount},
            </if>
            <if test="wsfinishcount != null">
                WsFinishCount =#{wsfinishcount},
            </if>
            <if test="buyitemcount != null">
                BuyItemCount =#{buyitemcount},
            </if>
            <if test="buyfinishcount != null">
                BuyFinishCount =#{buyfinishcount},
            </if>
            <if test="csitemcount != null">
                CsItemCount =#{csitemcount},
            </if>
            <if test="csfinishcount != null">
                CsFinishCount =#{csfinishcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_Mrp
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.WkMrpPojo">
        select
        id
        from Wk_MrpItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

    <select id="getDelObjIds" resultType="java.lang.String">
        select
        id
        from Wk_MrpObj
        where Pid = #{id} and id not in
        <foreach collection="obj" open="(" close=")" separator="," item="obj">
            <if test="obj.id != null">
                #{obj.id}
            </if>
            <if test="obj.id == null">
                ''
            </if>
        </foreach>
    </select>

    <!--查询List-->
    <select id="getItemListByParentid" resultType="inks.service.sa.som.domain.pojo.WkMrpitemPojo">
        SELECT Wk_MrpItem.id,
               Wk_MrpItem.Pid,
               Wk_MrpItem.ItemParentid,
               Wk_MrpItem.MrpObjid,
               Wk_MrpItem.LevelNum,
               Wk_MrpItem.LevelSymbol,
               Wk_MrpItem.Goodsid,
               Wk_MrpItem.ItemCode,
               Wk_MrpItem.ItemName,
               Wk_MrpItem.ItemSpec,
               Wk_MrpItem.ItemUnit,
               Wk_MrpItem.Bomid,
               Wk_MrpItem.BomType,
               Wk_MrpItem.BomItemid,
               Wk_MrpItem.SubQty,
               Wk_MrpItem.MainQty,
               Wk_MrpItem.LossRate,
               Wk_MrpItem.AttrCode,
               Wk_MrpItem.FlowCode,
               Wk_MrpItem.BomQty,
               Wk_MrpItem.StoQty,
               Wk_MrpItem.SafeStock,
               Wk_MrpItem.NeedQty,
               Wk_MrpItem.RealQty,
               Wk_MrpItem.WorkDate,
               Wk_MrpItem.PlanDate,
               Wk_MrpItem.Remark,
               Wk_MrpItem.EnabledMark,
               Wk_MrpItem.Closed,
               Wk_MrpItem.RowNum,
               Wk_MrpItem.BuyPlanQty,
               Wk_MrpItem.BuyOrderQty,
               Wk_MrpItem.CustSuppQty,
               Wk_MrpItem.WkWsQty,
               Wk_MrpItem.WkScQty,
               Wk_MrpItem.OtherQty,
               Wk_MrpItem.MatReqQty,
               Wk_MrpItem.MatCompQty,
               Wk_MrpItem.MatIvQty,
               Wk_MrpItem.BuyRemQty,
               Wk_MrpItem.WkWsRemQty,
               Wk_MrpItem.WkScRemQty,
               Wk_MrpItem.BusRemQty,
               Wk_MrpItem.MrpRemQty,
               Wk_MrpItem.FreeReqRemQty,
               Wk_MrpItem.Groupid,
               Wk_MrpItem.GroupName,
               Wk_MrpItem.MatReqRtQty,
               Wk_MrpItem.MatCompRtQty,
               Wk_MrpItem.WkFinishQty,
               Wk_MrpItem.BuyFinishQty,
               Wk_MrpItem.CustFinishQty,
               Wk_MrpItem.MachUid,
               Wk_MrpItem.MachItemid,
               Wk_MrpItem.MachBatch,
               Wk_MrpItem.MachGroupid,
               Wk_MrpItem.MainPlanUid,
               Wk_MrpItem.MainPlanItemid,
               Wk_MrpItem.BomRound,
               Wk_MrpItem.BomDate,
               Wk_MrpItem.BomMark,
        Wk_MrpItem.AttributeJson,
        Wk_MrpItem.MatType,
               Wk_MrpItem.Custom1,
               Wk_MrpItem.Custom2,
               Wk_MrpItem.Custom3,
               Wk_MrpItem.Custom4,
               Wk_MrpItem.Custom5,
               Wk_MrpItem.Custom6,
               Wk_MrpItem.Custom7,
               Wk_MrpItem.Custom8,
               Wk_MrpItem.Custom9,
               Wk_MrpItem.Custom10,
               Wk_MrpItem.Tenantid,
               Wk_MrpItem.TenantName,
               Wk_MrpItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Surface,
               Mat_Goods.Drawing,
               Mat_Goods.BrandName
        FROM Mat_Goods
                 RIGHT JOIN Wk_MrpItem ON Wk_MrpItem.Goodsid = Mat_Goods.id
        where Wk_MrpItem.ItemParentid = #{Pid}
          and Wk_MrpItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--  销售订单 Eric 20220429-->
    <update id="updateMachMrpUid">
        update Bus_MachiningItem
        SET MrpUid =#{refno},
            Mrpid=#{mrpid}
        where id = #{key}
          and Tenantid = #{tid}
    </update>


    <!--  生产主计划 Eric 20220429-->
    <update id="updateMpMrpUid">
        update Wk_MainPlanItem
        SET MrpUid =#{refno},
            Mrpid=#{mrpid}
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <select id="getGoodsInveQtyInStoreids" resultType="java.lang.Double">
        select COALESCE(sum(Quantity),0) as Qty
        from Mat_Inventory
        where Goodsid = #{key}
          and Tenantid = #{tid}
        <if test="storeIdSet != null and storeIdSet.size() > 0">
            and Storeid in
            <foreach collection="storeIdSet" item="storeid" open="(" close=")" separator="," >
                #{storeid}
            </foreach>
        </if>
    </select>
    <select id="getBuyPlanRemQty" resultType="java.lang.Double">
        SELECT COALESCE(sum(Buy_PlanItem.Quantity - Buy_PlanItem.BuyQty), 0) as Qty
        FROM Buy_Plan
                 RIGHT JOIN Buy_PlanItem ON Buy_PlanItem.Pid = Buy_Plan.id
        where Buy_PlanItem.Goodsid = #{key}
          and Buy_PlanItem.MrpItemid <![CDATA[ != ]]> #{mrpitemid}
          and Buy_PlanItem.DisannulMark = 0
          and Buy_PlanItem.Closed = 0
          and Buy_Plan.Tenantid = #{tid}
    </select>
    <select id="getBuyOrderRemQty" resultType="java.lang.Double">
        SELECT COALESCE(sum(Buy_OrderItem.Quantity - Buy_OrderItem.FinishQty), 0) as Qty
        FROM Buy_Order
                 RIGHT JOIN Buy_OrderItem ON Buy_OrderItem.Pid = Buy_Order.id
        where Buy_OrderItem.Goodsid = #{key}
          and Buy_OrderItem.MrpItemid <![CDATA[ != ]]> #{mrpitemid}
          and Buy_OrderItem.DisannulMark = 0
          and Buy_OrderItem.Closed = 0
          and Buy_Order.Tenantid = #{tid}
    </select>
    <select id="getWkWsRemQty" resultType="java.lang.Double">
        SELECT COALESCE(sum(Wk_WorksheetItem.Quantity - Wk_WorksheetItem.FinishQty),0) as Qty
        FROM Wk_Worksheet
                 RIGHT JOIN Wk_WorksheetItem ON Wk_WorksheetItem.Pid = Wk_Worksheet.id
        where Wk_WorksheetItem.Goodsid = #{key}
          and Wk_WorksheetItem.MrpItemid <![CDATA[ != ]]> #{mrpitemid}
        and Wk_WorksheetItem.DisannulMark = 0
        and Wk_WorksheetItem.Closed = 0
          and Wk_Worksheet.Tenantid = #{tid}
    </select>
    <select id="getWkScRemQty" resultType="java.lang.Double">
        SELECT COALESCE(sum(Wk_SubcontractItem.Quantity - Wk_SubcontractItem.FinishQty),0) as Qty
        FROM Wk_Subcontract
                 RIGHT JOIN Wk_SubcontractItem ON Wk_SubcontractItem.Pid = Wk_Subcontract.id
        where Wk_SubcontractItem.Goodsid = #{key}
          and Wk_SubcontractItem.MrpItemid <![CDATA[ != ]]> #{mrpitemid}
        and Wk_SubcontractItem.DisannulMark = 0
        and Wk_SubcontractItem.Closed = 0
          and Wk_Subcontract.Tenantid = #{tid}
    </select>
    <!--    销售待发,排除自己-->
    <select id="getBusMachRemQty" resultType="java.lang.Double">
        SELECT COALESCE(SUM(Bus_MachiningItem.Quantity - Bus_MachiningItem.FinishQty), 0)
        FROM Bus_MachiningItem
                 LEFT OUTER JOIN Bus_Machining ON Bus_MachiningItem.pid =
                                                  Bus_Machining.id
        where Bus_MachiningItem.Goodsid = #{key}
          and Bus_MachiningItem.id <![CDATA[ != ]]> #{machitemid}
          and Bus_MachiningItem.Quantity
         &gt; Bus_MachiningItem.FinishQty
          and Bus_MachiningItem.Closed = 0
          and Bus_MachiningItem.DisannulMark = 0
          and Bus_Machining.Tenantid = #{tid}
    </select>
    <!--    发货待出,排除自己-->
    <select id="getBusDeliRemQty" resultType="java.lang.Double">
        SELECT COALESCE(SUM(CASE
                                WHEN Bus_Deliery.BillType IN ('发出商品', '其他发货')
                                    THEN Bus_DelieryItem.Quantity - Bus_DelieryItem.FinishQty
                                ELSE Bus_DelieryItem.FinishQty - Bus_DelieryItem.Quantity END), 0)
        FROM Bus_DelieryItem
                 LEFT OUTER JOIN Bus_Deliery ON Bus_DelieryItem.pid =
                                                Bus_Deliery.id
        where Bus_Deliery.BillType IN ('发出商品', '其他发货', '订单退货', '其他退货')
          and Bus_DelieryItem.Goodsid = #{key}
          and Bus_DelieryItem.Quantity &gt; Bus_DelieryItem.FinishQty
          and Bus_DelieryItem.MachItemid <![CDATA[ != ]]> #{machitemid}
          and Bus_DelieryItem.FinishClosed = 0
          and Bus_DelieryItem.DisannulMark = 0
          and Bus_Deliery.Tenantid = #{tid}
    </select>
<!--    <select id="getMrpRemQty" resultType="java.lang.Double">-->
<!--        SELECT sum(Wk_MrpItem.NeedQty - Wk_MrpItem.MatCompQty) as Qty-->
<!--        FROM Wk_Mrp-->
<!--                 RIGHT JOIN Wk_MrpItem ON Wk_MrpItem.Pid = Wk_Mrp.id-->
<!--        where Wk_MrpItem.Goodsid = #{key}-->
<!--          and Wk_Mrp.id <![CDATA[ != ]]> #{mrpid}-->
<!--          and Wk_Mrp.CreateDate-->
<!--         &lt;#{mrpdate}-->
<!--          and Wk_Mrp.Tenantid = #{tid}-->
<!--    </select>-->


    <select id="getWkWsMatRemQty" resultType="java.lang.Double">
        SELECT COALESCE(sum(Wk_WorksheetMat.Quantity - Wk_WorksheetMat.FinishQty),0) as Qty
        FROM Wk_WorksheetMat
                 LEFT JOIN Wk_WorksheetItem ON Wk_WorksheetMat.Itemid = Wk_WorksheetItem.id
        where Wk_WorksheetMat.Goodsid = #{key}
          and Wk_WorksheetMat.MrpItemid <![CDATA[ != ]]> #{mrpitemid}
          and Wk_WorksheetMat.Quantity
         &gt; Wk_WorksheetMat.FinishQty
          and Wk_WorksheetMat.Closed=0
          and Wk_WorksheetItem.Closed=0
          and Wk_WorksheetItem.DisannulMark=0
          and Wk_WorksheetMat.Tenantid = #{tid}
    </select>

    <select id="getWkScMatRemQty" resultType="java.lang.Double">
        SELECT COALESCE(sum(Wk_SubcontractMat.Quantity - Wk_SubcontractMat.FinishQty),0) as Qty
        FROM Wk_SubcontractMat
                 LEFT JOIN Wk_SubcontractItem ON Wk_SubcontractMat.Itemid = Wk_SubcontractItem.id
        where Wk_SubcontractMat.Goodsid = #{key}
          and Wk_SubcontractMat.MrpItemid <![CDATA[ != ]]> #{mrpitemid}
          and Wk_SubcontractMat.Quantity
         &gt; Wk_SubcontractMat.FinishQty
          and Wk_SubcontractMat.Closed=0
          and Wk_SubcontractItem.Closed=0
          and Wk_SubcontractItem.DisannulMark=0
          and Wk_SubcontractMat.Tenantid = #{tid}
    </select>


    <select id="getReqRemQty" resultType="java.lang.Double">
        SELECT   COALESCE(SUM(CASE
                             WHEN Mat_Requisition.BillType IN ('领料单', '生产领料')
                                 THEN Mat_RequisitionItem.Quantity - Mat_RequisitionItem.FinishQty
                             ELSE Mat_RequisitionItem.FinishQty-Mat_RequisitionItem.Quantity   END), 0) as Qty
        FROM Mat_Requisition
                 RIGHT JOIN Mat_RequisitionItem ON Mat_RequisitionItem.Pid = Mat_Requisition.id
        where Mat_RequisitionItem.Goodsid = #{key}
          and Mat_RequisitionItem.MrpItemid <![CDATA[ != ]]> #{mrpitemid}
          and Mat_RequisitionItem.Closed=0
          and Mat_RequisitionItem.DisannulMark=0
          and Mat_RequisitionItem.Quantity
         &gt; Mat_RequisitionItem.FinishQty
          and Mat_Requisition.Tenantid = #{tid}
    </select>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateFinishCount">
        update Wk_Mrp
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_MrpItem
                                   where Wk_MrpItem.Pid =
                                         (SELECT Pid FROM Wk_MrpItem where id = #{key})
                                     and Wk_MrpItem.Tenantid = #{tid}
                                     and Wk_MrpItem.BuyPlanQty + Wk_MrpItem.BuyOrderQty + Wk_MrpItem.CustSuppQty +
                                         Wk_MrpItem.WkWsQty + Wk_MrpItem.WkScQty + Wk_MrpItem.OtherQty >=
                                         Wk_MrpItem.NeedQty), 0)
        where id = (SELECT Pid FROM Wk_MrpItem where id = #{key})
          and Tenantid = #{tid}
    </update>

<!--    Wk_MrpItem.AttrCode属性 厂制/委制/外购/客供-->
    <update id="syncMrpCount">
        UPDATE Wk_Mrp m
            LEFT JOIN (SELECT Pid,
                              Tenantid,
                              COUNT(*)                         AS total,
                              SUM(IF(AttrCode = '厂制', 1, 0)) AS wk,
                              SUM(IF(AttrCode = '委制', 1, 0)) AS ws,
                              SUM(IF(AttrCode = '外购', 1, 0)) AS buy,
                              SUM(IF(AttrCode = '客供', 1, 0)) AS cs
                       FROM Wk_MrpItem
                       WHERE Pid = #{key}
                         AND Tenantid = #{tid}
                       GROUP BY Pid, Tenantid) t ON m.id = t.Pid AND m.Tenantid = t.Tenantid
        SET m.ItemCount    = COALESCE(t.total, 0),
            m.WkItemCount  = COALESCE(t.wk, 0),
            m.WsItemCount  = COALESCE(t.ws, 0),
            m.BuyItemCount = COALESCE(t.buy, 0),
            m.CsItemCount  = COALESCE(t.cs, 0)
        WHERE m.id = #{key}
          AND m.Tenantid = #{tid}
    </update>

    <select id="getSafestock" resultType="java.lang.Double">
        SELECT SafeStock
        FROM Mat_Goods
        where id = #{goodsid}
        and Tenantid = #{tid}
    </select>

    <select id="getItemIds" resultType="java.lang.String">
        SELECT id from Wk_MrpItem where Pid = #{mrpid} and Tenantid = #{tid}
    </select>

    <update id="syncMrpItemMergeMarkAndGrossQty">
        UPDATE Wk_MrpItem
        set MergeMark = 0,
            GrossQty  = BomQty
        where Pid = #{mrpid}
        and MergeMark != 0
    </update>
</mapper>

