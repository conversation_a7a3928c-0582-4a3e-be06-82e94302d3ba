<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyPlanitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyPlanitemPojo">
        <include refid="selectBuyPlanitemVo"/>
        where Buy_PlanItem.id = #{key}
          and Buy_PlanItem.Tenantid = #{tid}
    </select>
    <sql id="selectBuyPlanitemVo">
        SELECT
            Buy_PlanItem.id,
            Buy_PlanItem.Pid,
            Buy_PlanItem.ItemType,
            Buy_PlanItem.Goodsid,
            Buy_PlanItem.ItemCode,
            Buy_PlanItem.ItemName,
            Buy_PlanItem.ItemSpec,
            Buy_PlanItem.ItemUnit,
            Buy_PlanItem.Quantity,
            Buy_PlanItem.TaxPrice,
            Buy_PlanItem.TaxAmount,
            Buy_PlanItem.TaxTotal,
            Buy_PlanItem.ItemTaxrate,
            Buy_PlanItem.Price,
            Buy_PlanItem.Amount,
            Buy_PlanItem.PlanDate,
            Buy_PlanItem.Groupid,
            Buy_PlanItem.Remark,
            Buy_PlanItem.CiteUid,
            Buy_PlanItem.CiteItemid,
            Buy_PlanItem.StateCode,
            Buy_PlanItem.StateDate,
            Buy_PlanItem.Closed,
            Buy_PlanItem.BuyQty,
            Buy_PlanItem.FinishQty,
            Buy_PlanItem.RowNum,
            Buy_PlanItem.MachUid,
            Buy_PlanItem.MachItemid,
            Buy_PlanItem.MachBatch,
            Buy_PlanItem.MachGroupid,
            Buy_PlanItem.MainPlanUid,
            Buy_PlanItem.MainPlanItemid,
            Buy_PlanItem.MrpUid,
            Buy_PlanItem.MrpItemid,
            Buy_PlanItem.Customer,
            Buy_PlanItem.CustPO,
            Buy_PlanItem.BatchNo,
            Buy_PlanItem.DisannulMark,
            Buy_PlanItem.DisannulListerid,
            Buy_PlanItem.DisannulLister,
            Buy_PlanItem.DisannulDate,
            Buy_PlanItem.AttributeJson,
            Buy_PlanItem.SourceType,
            Buy_PlanItem.Mergeid,
            Buy_PlanItem.MergeBuyQty,
            Buy_PlanItem.MergeFinishQty,
            Buy_PlanItem.MergeMark,
            Buy_PlanItem.MergeItems,
            Buy_PlanItem.MrpObjGoodsid,
            Buy_PlanItem.PlanQty,
            Buy_PlanItem.Custom1,
            Buy_PlanItem.Custom2,
            Buy_PlanItem.Custom3,
            Buy_PlanItem.Custom4,
            Buy_PlanItem.Custom5,
            Buy_PlanItem.Custom6,
            Buy_PlanItem.Custom7,
            Buy_PlanItem.Custom8,
            Buy_PlanItem.Custom9,
            Buy_PlanItem.Custom10,
            Buy_PlanItem.Tenantid,
            Buy_PlanItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10,
            App_Workgroup.GroupUid,
            App_Workgroup.GroupName,
            App_Workgroup.Abbreviate,
            g2.GoodsName as MrpObjGoodsName,
            g2.GoodsUid as MrpObjGoodsUid
        FROM App_Workgroup
                 RIGHT JOIN Buy_PlanItem ON Buy_PlanItem.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods ON Buy_PlanItem.Goodsid = Mat_Goods.id
                 LEFT JOIN Mat_Goods g2 ON Buy_PlanItem.MrpObjGoodsid = g2.id
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.BuyPlanitemPojo">
        <include refid="selectBuyPlanitemVo"/>
        where Buy_PlanItem.Pid = #{Pid} and Buy_PlanItem.Tenantid=#{tid}
        order by RowNum
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyPlanitemPojo">
        <include refid="selectBuyPlanitemVo"/>
        where 1 = 1 and Buy_PlanItem.Tenantid =#{tenantid}
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_PlanItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Buy_PlanItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
            and Buy_PlanItem.itemtype like concat('%', #{SearchPojo.itemtype}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Buy_PlanItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Buy_PlanItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Buy_PlanItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Buy_PlanItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Buy_PlanItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Buy_PlanItem.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Buy_PlanItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Buy_PlanItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Buy_PlanItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Buy_PlanItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Buy_PlanItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Buy_PlanItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Buy_PlanItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Buy_PlanItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Buy_PlanItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Buy_PlanItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Buy_PlanItem.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Buy_PlanItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Buy_PlanItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Buy_PlanItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
            and Buy_PlanItem.disannullisterid like concat('%', #{SearchPojo.disannullisterid}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and Buy_PlanItem.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Buy_PlanItem.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Buy_PlanItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Buy_PlanItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Buy_PlanItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Buy_PlanItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Buy_PlanItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Buy_PlanItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Buy_PlanItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Buy_PlanItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Buy_PlanItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Buy_PlanItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Buy_PlanItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
                or Buy_PlanItem.ItemType like concat('%', #{SearchPojo.itemtype}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Buy_PlanItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Buy_PlanItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Buy_PlanItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Buy_PlanItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Buy_PlanItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Buy_PlanItem.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Buy_PlanItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Buy_PlanItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Buy_PlanItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Buy_PlanItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Buy_PlanItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Buy_PlanItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Buy_PlanItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Buy_PlanItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Buy_PlanItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Buy_PlanItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Buy_PlanItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Buy_PlanItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Buy_PlanItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Buy_PlanItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.disannullisterid != null and SearchPojo.disannullisterid != ''">
                or Buy_PlanItem.DisannulListerid like concat('%', #{SearchPojo.disannullisterid}, '%')
            </if>
            <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
                or Buy_PlanItem.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Buy_PlanItem.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Buy_PlanItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Buy_PlanItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Buy_PlanItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Buy_PlanItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Buy_PlanItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Buy_PlanItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Buy_PlanItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Buy_PlanItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Buy_PlanItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Buy_PlanItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>



    <!--新增所有列-->
    <insert id="insert" >
        insert into Buy_PlanItem(id, Pid, ItemType, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, TaxPrice, TaxAmount, TaxTotal, ItemTaxrate, Price, Amount, PlanDate, Groupid, Remark, CiteUid, CiteItemid, StateCode, StateDate, Closed, BuyQty, FinishQty, RowNum, MachUid, MachItemid, MachBatch, MachGroupid, MainPlanUid, MainPlanItemid, MrpUid, MrpItemid, MrpObjGoodsid, Customer, CustPO, BatchNo, DisannulMark, DisannulListerid, DisannulLister, DisannulDate, AttributeJson, SourceType, Mergeid, MergeBuyQty, MergeFinishQty, MergeMark, MergeItems, PlanQty, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{itemtype}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{taxprice}, #{taxamount}, #{taxtotal}, #{itemtaxrate}, #{price}, #{amount}, #{plandate}, #{groupid}, #{remark}, #{citeuid}, #{citeitemid}, #{statecode}, #{statedate}, #{closed}, #{buyqty}, #{finishqty}, #{rownum}, #{machuid}, #{machitemid}, #{machbatch}, #{machgroupid}, #{mainplanuid}, #{mainplanitemid}, #{mrpuid}, #{mrpitemid}, #{mrpobjgoodsid}, #{customer}, #{custpo}, #{batchno}, #{disannulmark}, #{disannullisterid}, #{disannullister}, #{disannuldate}, #{attributejson}, #{sourcetype}, #{mergeid}, #{mergebuyqty}, #{mergefinishqty}, #{mergemark}, #{mergeitems}, #{planqty}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_PlanItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="itemtype != null ">
                ItemType = #{itemtype},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="taxtotal != null">
                TaxTotal = #{taxtotal},
            </if>
            <if test="itemtaxrate != null">
                ItemTaxrate = #{itemtaxrate},
            </if>
            <if test="price != null">
                Price = #{price},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="groupid != null ">
                Groupid = #{groupid},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="citeuid != null ">
                CiteUid = #{citeuid},
            </if>
            <if test="citeitemid != null ">
                CiteItemid = #{citeitemid},
            </if>
            <if test="statecode != null ">
                StateCode = #{statecode},
            </if>
            <if test="statedate != null">
                StateDate = #{statedate},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="buyqty != null">
                BuyQty = #{buyqty},
            </if>
            <if test="finishqty != null">
                FinishQty = #{finishqty},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machbatch != null ">
                MachBatch = #{machbatch},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="mrpuid != null ">
                MrpUid = #{mrpuid},
            </if>
            <if test="mrpitemid != null ">
                MrpItemid = #{mrpitemid},
            </if>
            <if test="mrpobjgoodsid != null ">
                MrpObjGoodsid = #{mrpobjgoodsid},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="batchno != null ">
                BatchNo = #{batchno},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="disannullisterid != null ">
                DisannulListerid = #{disannullisterid},
            </if>
            <if test="disannullister != null ">
                DisannulLister = #{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate = #{disannuldate},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="sourcetype != null">
                SourceType = #{sourcetype},
            </if>
            <if test="mergeid != null ">
                Mergeid = #{mergeid},
            </if>
            <if test="mergebuyqty != null">
                MergeBuyQty = #{mergebuyqty},
            </if>
            <if test="mergefinishqty != null">
                MergeFinishQty = #{mergefinishqty},
            </if>
            <if test="mergemark != null">
                MergeMark = #{mergemark},
            </if>
            <if test="mergeitems != null ">
                MergeItems = #{mergeitems},
            </if>
            <if test="planqty != null">
                PlanQty = #{planqty},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_PlanItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <select id="getListByMergeid" resultType="inks.service.sa.som.domain.pojo.BuyPlanitemPojo">
        <include refid="selectBuyPlanitemVo"/>
        where 1 = 1 and Buy_PlanItem.Tenantid =#{tid}
        and Buy_PlanItem.Mergeid = #{mergeid}
        order by RowNum
    </select>

    <update id="updateMergeid">
        update Buy_PlanItem
        set Mergeid = #{mergeidInsert}
        where id = #{id}
        and Tenantid = #{tid}
    </update>


    <update id="syncPriceFromOrderItem">
        update Buy_PlanItem
        set Buy_PlanItem.TaxPrice    = #{latestOrderItem.TaxPrice},
        Buy_PlanItem.Price       = #{latestOrderItem.Price},
        Buy_PlanItem.ItemTaxrate = #{latestOrderItem.ItemTaxrate},
        Buy_PlanItem.Amount      = #{latestOrderItem.Price} * Buy_PlanItem.Quantity,
        Buy_PlanItem.TaxAmount   = #{latestOrderItem.TaxPrice} * Buy_PlanItem.Quantity,
        Buy_PlanItem.TaxTotal    = (#{latestOrderItem.TaxPrice} - #{latestOrderItem.Price}) * Buy_PlanItem.Quantity
        where Buy_PlanItem.id = #{planitemid}
        and Buy_PlanItem.Tenantid = #{tid}
    </update>

    <update id="syncMergePriceFromOrderItem">
        update Buy_PlanMerge
        set Buy_PlanMerge.TaxPrice    = #{latestOrderItem.TaxPrice},
        Buy_PlanMerge.Price       = #{latestOrderItem.Price},
        Buy_PlanMerge.ItemTaxrate = #{latestOrderItem.ItemTaxrate},
        Buy_PlanMerge.Amount      = #{latestOrderItem.Price} * Buy_PlanMerge.Quantity,
        Buy_PlanMerge.TaxAmount   = #{latestOrderItem.TaxPrice} * Buy_PlanMerge.Quantity,
        Buy_PlanMerge.TaxTotal    = (#{latestOrderItem.TaxPrice}- #{latestOrderItem.Price}) * Buy_PlanMerge.Quantity
        where Buy_PlanMerge.id = #{mergeid}
        and Buy_PlanMerge.Tenantid = #{tid}
    </update>

    <select id="getLatestOrderItemPrice" resultType="java.util.Map">
        select TaxPrice, Price, ItemTaxrate
        from Buy_OrderItem
        Left join Buy_Order on Buy_OrderItem.Pid = Buy_Order.id
        where Buy_OrderItem.Goodsid = #{goodsid}
        <if test="groupid != null and groupid != ''">
            and Buy_Order.Groupid = #{groupid}
        </if>
        and Buy_OrderItem.Tenantid = #{tid}
        order by Buy_Order.CreateDate DESC
        limit 1
    </select>

    <select id="getGoodsPrice" resultType="java.util.Map">
        select COALESCE(InTaxPrice, 0) as TaxPrice,
        COALESCE(InPrice, 0)    as Price,
        COALESCE(Taxrate , 0) as ItemTaxrate
        from Mat_Goods
        where id = #{goodsid}
        and Tenantid = #{tid}
    </select>

    <select id="getMachItemListInMachitemids" resultType="java.util.Map">
        SELECT App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate,
        App_Workgroup.GroupLevel,
        App_Workgroup.PaymentMethod,
        Bus_MachiningItem.id,
        Bus_MachiningItem.Pid,
        Bus_MachiningItem.MachBatch,
        Bus_MachiningItem.Goodsid,
        Bus_MachiningItem.Quantity,
        Bus_MachiningItem.TaxPrice,
        Bus_MachiningItem.TaxAmount,
        Bus_MachiningItem.ItemTaxrate,
        Bus_MachiningItem.TaxTotal,
        Bus_MachiningItem.Price,
        Bus_MachiningItem.Amount,
        Bus_MachiningItem.ItemOrgDate,
        Bus_MachiningItem.ItemPlanDate,
        Bus_MachiningItem.WkQty,
        Bus_MachiningItem.StoQty,
        Bus_MachiningItem.RowNum,
        Bus_MachiningItem.Remark,
        Bus_MachiningItem.EngStateText,
        Bus_MachiningItem.EngStateDate,
        Bus_MachiningItem.WkStateText,
        Bus_MachiningItem.WkStateDate,
        Bus_MachiningItem.BusStateText,
        Bus_MachiningItem.BusStateDate,
        Bus_MachiningItem.BuyQuantity,
        Bus_MachiningItem.WkQuantity,
        Bus_MachiningItem.InQuantity,
        Bus_MachiningItem.PickQty,
        Bus_MachiningItem.FinishQty,
        Bus_MachiningItem.OutQuantity,
        Bus_MachiningItem.OutSecQty,
        Bus_MachiningItem.EditionInfo,
        Bus_MachiningItem.ItemCompDate,
        Bus_MachiningItem.VirtualItem,
        Bus_MachiningItem.Closed,
        Bus_MachiningItem.StdPrice,
        Bus_MachiningItem.StdAmount,
        Bus_MachiningItem.Rebate,
        Bus_MachiningItem.MrpUid,
        Bus_MachiningItem.Mrpid,
        Bus_MachiningItem.MaxQty,
        Bus_MachiningItem.Location,
        Bus_MachiningItem.BatchNo,
        Bus_MachiningItem.DisannulMark,
        Bus_MachiningItem.WipUsed,
        Bus_MachiningItem.WkWpid,
        Bus_MachiningItem.WkWpCode,
        Bus_MachiningItem.WkWpName,
        Bus_MachiningItem.WkRowNum,
        Bus_MachiningItem.OrderCostUid,
        Bus_MachiningItem.OrderCostItemid,
        Bus_MachiningItem.BomType,
        Bus_MachiningItem.Bomid,
        Bus_MachiningItem.BomUid,
        Bus_MachiningItem.BomState,
        Bus_MachiningItem.AttributeJson,
        Bus_MachiningItem.AttributeStr,
        Bus_MachiningItem.MatCode,
        Bus_MachiningItem.MatUsed,
        Bus_MachiningItem.MatCostAmt,
        Bus_MachiningItem.LaborCostAmt,
        Bus_MachiningItem.DirectCostAmt,
        Bus_MachiningItem.IndirectCostAmt,
        Bus_MachiningItem.CostItemJson,
        Bus_MachiningItem.CostGroupJson,
        Bus_MachiningItem.CostBudgetAmt,
        Bus_MachiningItem.Specid,
        Bus_MachiningItem.SpecUid,
        Bus_MachiningItem.SpecState,
        Bus_MachiningItem.MatBuyQty,
        Bus_MachiningItem.MatUseQty,
        Bus_MachiningItem.AvgLastAmt,
        Bus_MachiningItem.AvgFirstAmt,
        Bus_MachiningItem.AvgInvoAmt,
        Bus_MachiningItem.InvoQty,
        Bus_MachiningItem.InvoClosed,
        Bus_MachiningItem.MainPlanQty,
        Bus_MachiningItem.MainPlanClosed,
        Bus_MachiningItem.Custom1,
        Bus_MachiningItem.Custom2,
        Bus_MachiningItem.Custom3,
        Bus_MachiningItem.Custom4,
        Bus_MachiningItem.Custom5,
        Bus_MachiningItem.Custom6,
        Bus_MachiningItem.Custom7,
        Bus_MachiningItem.Custom8,
        Bus_MachiningItem.Custom9,
        Bus_MachiningItem.Custom10,
        Bus_MachiningItem.Custom11,
        Bus_MachiningItem.Custom12,
        Bus_MachiningItem.Custom13,
        Bus_MachiningItem.Custom14,
        Bus_MachiningItem.Custom15,
        Bus_MachiningItem.Custom16,
        Bus_MachiningItem.Custom17,
        Bus_MachiningItem.Custom18,
        Bus_MachiningItem.Tenantid,
        Bus_MachiningItem.Revision,
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.Partid,
        Mat_Goods.Surface,
        Mat_Goods.Material,
        Mat_Goods.Drawing,
        Mat_Goods.BrandName,
        Mat_Goods.Custom1  AS GoodsCustom1,
        Mat_Goods.Custom2  AS GoodsCustom2,
        Mat_Goods.Custom3  AS GoodsCustom3,
        Mat_Goods.Custom4  AS GoodsCustom4,
        Mat_Goods.Custom5  AS GoodsCustom5,
        Mat_Goods.Custom6  AS GoodsCustom6,
        Mat_Goods.Custom7  AS GoodsCustom7,
        Mat_Goods.Custom8  AS GoodsCustom8,
        Mat_Goods.Custom9  AS GoodsCustom9,
        Mat_Goods.Custom10 AS GoodsCustom10,
        Mat_Goods.Material as GoodsMaterial,
        Bus_Machining.RefNo,
        Bus_Machining.BillType,
        Bus_Machining.BillTitle,
        Bus_Machining.BillDate,
        Bus_Machining.BillPlanDate,
        Bus_Machining.Groupid,
        Bus_Machining.CustOrderid,
        Bus_Machining.LogisticsMode,
        Bus_Machining.LogisticsPort,
        Bus_Machining.Country,
        Bus_Machining.AdvaAmount,
        Bus_Machining.Salesman,
        Bus_Machining.Taxrate,
        Bus_Machining.Summary,
        Bus_Machining.CreateBy,
        Bus_Machining.Lister,
        Bus_Machining.Assessor,
        Bus_Machining.ItemCount,
        Bus_Machining.PickCount,
        Bus_Machining.FinishCount,
        Bus_Machining.WkFinishCount,
        Bus_Machining.WkItemCount,
        Bus_Machining.WkWipCount,
        Bus_Machining.Payment,
        Bus_Machining.PrintCount,
        Bus_Machining.OaFlowMark,
        Bus_Machining.BillCostBudgetAmt,
        Bus_Machining.FirstAmt,
        Bus_Machining.LastAmt,
        Bus_Machining.InvoAmt,
        Bus_Machining.InvoCount,
        Bus_Machining.Moneyid,
        Bus_Machining.MoneyName,
        Bus_Machining.MainPlanCount
        FROM App_Workgroup
        RIGHT JOIN Bus_Machining ON App_Workgroup.id = Bus_Machining.Groupid
        RIGHT JOIN Bus_MachiningItem ON Bus_MachiningItem.Pid = Bus_Machining.id
        LEFT JOIN Mat_Goods ON Mat_Goods.id = Bus_MachiningItem.Goodsid
        where Bus_MachiningItem.id in
        <foreach collection="machitemids" item="machitemid" open="(" separator="," close=")">
            #{machitemid}
        </foreach>
    </select>

    <update id="syncMergeMarkInIds">
        update Buy_PlanItem
        set MergeMark = #{mergeMark}
        where id in
        <foreach collection="mainPlanItemIds" item="mainPlanItemId" open="(" separator="," close=")">
            #{mainPlanItemId}
        </foreach>
    </update>

    <select id="getMachItemidsInPlanItemids" resultType="java.lang.String">
        select MachItemid
        from Buy_PlanItem
        where id in
        <foreach collection="mergeItemIds" item="planItemId" open="(" separator="," close=")">
            #{planItemId}
        </foreach>
        and Tenantid = #{tid}
    </select>
</mapper>

