<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.WkMrpobjMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.WkMrpobjPojo">
        SELECT
            Wk_MrpObj.id,
            Wk_MrpObj.Pid,
            Wk_MrpObj.Goodsid,
            Wk_MrpObj.ItemCode,
            Wk_MrpObj.ItemName,
            Wk_MrpObj.ItemSpec,
            Wk_MrpObj.ItemUnit,
            Wk_MrpObj.Quantity,
            Wk_MrpObj.PlanDate,
            Wk_MrpObj.MachUid,
            Wk_MrpObj.<PERSON>h<PERSON><PERSON><PERSON>,
            Wk_MrpObj.<PERSON>,
            Wk_MrpObj.<PERSON>h<PERSON>roup<PERSON>,
            Wk_MrpObj.MainPlanUid,
            Wk_MrpObj.MainPlanItemid,
            Wk_MrpObj.Customer,
            Wk_MrpObj.RowNum,
            Wk_MrpObj.Remark,
            Wk_MrpObj.CustPO,
            Wk_MrpObj.AttributeJson,
            Wk_MrpObj.Custom1,
            Wk_MrpObj.Custom2,
            Wk_MrpObj.Custom3,
            Wk_MrpObj.Custom4,
            Wk_MrpObj.Custom5,
            Wk_MrpObj.Custom6,
            Wk_MrpObj.Custom7,
            Wk_MrpObj.Custom8,
            Wk_MrpObj.Custom9,
            Wk_MrpObj.Custom10,
            Wk_MrpObj.Tenantid,
            Wk_MrpObj.TenantName,
            Wk_MrpObj.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10
        FROM
            Wk_MrpObj
                LEFT JOIN Mat_Goods ON Wk_MrpObj.Goodsid = Mat_Goods.id
        where Wk_MrpObj.id = #{key}
          and Wk_MrpObj.Tenantid = #{tid}
    </select>
    <sql id="selectWkMrpobjVo">
        SELECT
            Wk_MrpObj.id,
            Wk_MrpObj.Pid,
            Wk_MrpObj.Goodsid,
            Wk_MrpObj.ItemCode,
            Wk_MrpObj.ItemName,
            Wk_MrpObj.ItemSpec,
            Wk_MrpObj.ItemUnit,
            Wk_MrpObj.Quantity,
            Wk_MrpObj.PlanDate,
            Wk_MrpObj.MachUid,
            Wk_MrpObj.MachItemid,
            Wk_MrpObj.MachBatch,
            Wk_MrpObj.MachGroupid,
            Wk_MrpObj.MainPlanUid,
            Wk_MrpObj.MainPlanItemid,
            Wk_MrpObj.Customer,
            Wk_MrpObj.RowNum,
            Wk_MrpObj.Remark,
            Wk_MrpObj.CustPO,
            Wk_MrpObj.AttributeJson,
            Wk_MrpObj.Custom1,
            Wk_MrpObj.Custom2,
            Wk_MrpObj.Custom3,
            Wk_MrpObj.Custom4,
            Wk_MrpObj.Custom5,
            Wk_MrpObj.Custom6,
            Wk_MrpObj.Custom7,
            Wk_MrpObj.Custom8,
            Wk_MrpObj.Custom9,
            Wk_MrpObj.Custom10,
            Wk_MrpObj.Tenantid,
            Wk_MrpObj.TenantName,
            Wk_MrpObj.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10
        FROM
            Wk_MrpObj
                LEFT JOIN Mat_Goods ON Wk_MrpObj.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.WkMrpobjPojo">
        <include refid="selectWkMrpobjVo"/>
        where 1 = 1 and Wk_MrpObj.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Wk_MrpObj.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Wk_MrpObj.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Wk_MrpObj.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Wk_MrpObj.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Wk_MrpObj.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Wk_MrpObj.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Wk_MrpObj.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Wk_MrpObj.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Wk_MrpObj.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Wk_MrpObj.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Wk_MrpObj.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Wk_MrpObj.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Wk_MrpObj.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Wk_MrpObj.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Wk_MrpObj.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Wk_MrpObj.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Wk_MrpObj.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Wk_MrpObj.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Wk_MrpObj.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Wk_MrpObj.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Wk_MrpObj.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Wk_MrpObj.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Wk_MrpObj.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Wk_MrpObj.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Wk_MrpObj.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Wk_MrpObj.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            and Wk_MrpObj.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Wk_MrpObj.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Wk_MrpObj.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Wk_MrpObj.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Wk_MrpObj.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Wk_MrpObj.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Wk_MrpObj.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Wk_MrpObj.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Wk_MrpObj.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Wk_MrpObj.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Wk_MrpObj.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Wk_MrpObj.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Wk_MrpObj.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Wk_MrpObj.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Wk_MrpObj.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Wk_MrpObj.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Wk_MrpObj.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Wk_MrpObj.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Wk_MrpObj.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Wk_MrpObj.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Wk_MrpObj.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Wk_MrpObj.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Wk_MrpObj.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Wk_MrpObj.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Wk_MrpObj.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Wk_MrpObj.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
                or Wk_MrpObj.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.WkMrpobjPojo">
        SELECT Wk_MrpObj.id,
               Wk_MrpObj.Pid,
               Wk_MrpObj.Goodsid,
               Wk_MrpObj.ItemCode,
               Wk_MrpObj.ItemName,
               Wk_MrpObj.ItemSpec,
               Wk_MrpObj.ItemUnit,
               Wk_MrpObj.Quantity,
               Wk_MrpObj.PlanDate,
               Wk_MrpObj.MachUid,
               Wk_MrpObj.MachItemid,
               Wk_MrpObj.MachBatch,
               Wk_MrpObj.MachGroupid,
               Wk_MrpObj.MainPlanUid,
               Wk_MrpObj.MainPlanItemid,
               Wk_MrpObj.Customer,
               Wk_MrpObj.RowNum,
               Wk_MrpObj.Remark,
               Wk_MrpObj.CustPO,
               Wk_MrpObj.AttributeJson,
               Wk_MrpObj.Custom1,
               Wk_MrpObj.Custom2,
               Wk_MrpObj.Custom3,
               Wk_MrpObj.Custom4,
               Wk_MrpObj.Custom5,
               Wk_MrpObj.Custom6,
               Wk_MrpObj.Custom7,
               Wk_MrpObj.Custom8,
               Wk_MrpObj.Custom9,
               Wk_MrpObj.Custom10,
               Wk_MrpObj.Tenantid,
               Wk_MrpObj.TenantName,
               Wk_MrpObj.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Surface,
               Mat_Goods.Drawing,
               Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Wk_MrpObj
                 LEFT JOIN Mat_Goods ON Wk_MrpObj.Goodsid = Mat_Goods.id
        where Wk_MrpObj.Pid = #{Pid}
          and Wk_MrpObj.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Wk_MrpObj(id, Pid, Goodsid, ItemCode, ItemName, ItemSpec, ItemUnit, Quantity, PlanDate, MachUid,
                              MachItemid, MachBatch, MachGroupid, MainPlanUid, MainPlanItemid, Customer, RowNum, Remark,
                              CustPO,
                              AttributeJson, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                              Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{pid}, #{goodsid}, #{itemcode}, #{itemname}, #{itemspec}, #{itemunit}, #{quantity}, #{plandate},
                #{machuid}, #{machitemid}, #{machbatch}, #{machgroupid}, #{mainplanuid}, #{mainplanitemid}, #{customer},
                #{rownum},
                #{remark}, #{custpo}, #{attributejson}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
                #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Wk_MrpObj
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="goodsid != null ">
                Goodsid = #{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="itemspec != null ">
                ItemSpec = #{itemspec},
            </if>
            <if test="itemunit != null ">
                ItemUnit = #{itemunit},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="machuid != null ">
                MachUid = #{machuid},
            </if>
            <if test="machitemid != null ">
                MachItemid = #{machitemid},
            </if>
            <if test="machbatch != null ">
                MachBatch = #{machbatch},
            </if>
            <if test="machgroupid != null ">
                MachGroupid = #{machgroupid},
            </if>
            <if test="mainplanuid != null ">
                MainPlanUid = #{mainplanuid},
            </if>
            <if test="mainplanitemid != null ">
                MainPlanItemid = #{mainplanitemid},
            </if>
            <if test="customer != null ">
                Customer = #{customer},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custpo != null ">
                CustPO = #{custpo},
            </if>
            <if test="attributejson != null ">
                AttributeJson = #{attributejson},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Wk_MrpObj
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <select id="getMachbatch" resultType="java.lang.String">
        SELECT MachBatch
        FROM Wk_MrpObj
        WHERE id = #{mrpobjid}
          AND Tenantid = #{tid}
    </select>

    <select id="getListInids" resultType="inks.service.sa.som.domain.pojo.WkMrpobjPojo">
        <include refid="selectWkMrpobjVo"/>
        where Wk_MrpObj.id in
        <foreach collection="objids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getEntityDetail" resultType="inks.service.sa.som.domain.pojo.WkMrpobjdetailPojo">
        SELECT Wk_MrpObj.*,
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.selectGoods"/>
        Wk_Mrp.RefNo,
        Wk_Mrp.BillDate,
        Wk_Mrp.BillType,
        Wk_Mrp.BillTitle
        FROM Mat_Goods
        RIGHT JOIN Wk_MrpObj ON Wk_MrpObj.Goodsid = Mat_Goods.id
        Left Join Wk_Mrp on Wk_MrpObj.Pid = Wk_Mrp.id
        LEFT JOIN Bus_MachiningItem ON Wk_MrpObj.MachItemid = Bus_MachiningItem.id
        LEFT JOIN Bus_Machining ON Bus_MachiningItem.Pid = Bus_Machining.id
        where Wk_MrpObj.id = #{key}
        and Wk_MrpObj.Tenantid = #{tid}
    </select>

    <select id="getObjidsByMrpids" resultType="java.lang.String">
        SELECT distinct id
        FROM Wk_MrpObj
        WHERE Pid in
        <foreach collection="mrpids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>

