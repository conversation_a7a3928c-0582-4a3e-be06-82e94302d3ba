<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyVoucherMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyVoucherPojo">
        SELECT App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate,
        Buy_Voucher.id,
        Buy_Voucher.RefNo,
        Buy_Voucher.BillType,
        Buy_Voucher.BillTitle,
        Buy_Voucher.BillDate,
        Buy_Voucher.Projectid,
        Buy_Voucher.ProjName,
        Buy_Voucher.ProjCode,
        Buy_Voucher.Groupid,
        Buy_Voucher.BillAmount,
        Buy_Voucher.Operator,
        Buy_Voucher.CiteCode,
        Buy_Voucher.ReturnUid,
        Buy_Voucher.OrgUid,
        Buy_Voucher.Summary,
        Buy_Voucher.CreateBy,
        Buy_Voucher.CreateByid,
        Buy_Voucher.CreateDate,
        Buy_Voucher.Lister,
        Buy_Voucher.Listerid,
        Buy_Voucher.ModifyDate,
        Buy_Voucher.Assessor,
        Buy_Voucher.Assessorid,
        Buy_Voucher.AssessDate,
        Buy_Voucher.Custom1,
        Buy_Voucher.Custom2,
        Buy_Voucher.Custom3,
        Buy_Voucher.Custom4,
        Buy_Voucher.Custom5,
        Buy_Voucher.Custom6,
        Buy_Voucher.Custom7,
        Buy_Voucher.Custom8,
        Buy_Voucher.Custom9,
        Buy_Voucher.Custom10,
        Buy_Voucher.Tenantid,
        Buy_Voucher.Revision
        FROM App_Workgroup
        RIGHT JOIN Buy_Voucher ON App_Workgroup.id = Buy_Voucher.Groupid

        where Buy_Voucher.id = #{key}
        and Buy_Voucher.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Buy_Voucher.id,
               Buy_Voucher.RefNo,
               Buy_Voucher.BillType,
               Buy_Voucher.BillTitle,
               Buy_Voucher.BillDate,
               Buy_Voucher.Projectid,
               Buy_Voucher.ProjName,
               Buy_Voucher.ProjCode,
               Buy_Voucher.Groupid,
               Buy_Voucher.BillAmount,
               Buy_Voucher.Operator,
               Buy_Voucher.CiteCode,
               Buy_Voucher.ReturnUid,
               Buy_Voucher.OrgUid,
               Buy_Voucher.Summary,
               Buy_Voucher.CreateBy,
               Buy_Voucher.CreateByid,
               Buy_Voucher.CreateDate,
               Buy_Voucher.Lister,
               Buy_Voucher.Listerid,
               Buy_Voucher.ModifyDate,
               Buy_Voucher.Assessor,
               Buy_Voucher.Assessorid,
               Buy_Voucher.AssessDate,
               Buy_Voucher.Custom1,
               Buy_Voucher.Custom2,
               Buy_Voucher.Custom3,
               Buy_Voucher.Custom4,
               Buy_Voucher.Custom5,
               Buy_Voucher.Custom6,
               Buy_Voucher.Custom7,
               Buy_Voucher.Custom8,
               Buy_Voucher.Custom9,
               Buy_Voucher.Custom10,
               Buy_Voucher.Tenantid,
               Buy_Voucher.Revision
        FROM App_Workgroup
                 RIGHT JOIN Buy_Voucher ON App_Workgroup.id = Buy_Voucher.Groupid
    </sql>
    <sql id="selectdetailVo">
        select id,
               RefNo,
               BillType,
               BillTitle,
               BillDate,
               Projectid,
               ProjName,
               ProjCode,
               Groupid,
               BillAmount,
               Operator,
               CiteCode,
               ReturnUid,
               OrgUid,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Assessor,
               Assessorid,
               AssessDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Buy_Voucher
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyVoucheritemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Buy_Voucher.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Buy_Voucher.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Buy_Voucher.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Buy_Voucher.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Buy_Voucher.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Buy_Voucher.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Buy_Voucher.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
            and Buy_Voucher.citecode like concat('%', #{SearchPojo.citecode}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            and Buy_Voucher.returnuid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            and Buy_Voucher.orguid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Buy_Voucher.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Buy_Voucher.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Buy_Voucher.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Buy_Voucher.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Buy_Voucher.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Buy_Voucher.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Buy_Voucher.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Buy_Voucher.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Buy_Voucher.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Buy_Voucher.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Buy_Voucher.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Buy_Voucher.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Buy_Voucher.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Buy_Voucher.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Buy_Voucher.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Buy_Voucher.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Buy_Voucher.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or Buy_Voucher.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            or Buy_Voucher.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            or Buy_Voucher.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            or Buy_Voucher.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            or Buy_Voucher.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
            or Buy_Voucher.CiteCode like concat('%', #{SearchPojo.citecode}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            or Buy_Voucher.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            or Buy_Voucher.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or Buy_Voucher.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or Buy_Voucher.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or Buy_Voucher.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or Buy_Voucher.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or Buy_Voucher.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            or Buy_Voucher.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            or Buy_Voucher.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Buy_Voucher.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Buy_Voucher.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Buy_Voucher.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Buy_Voucher.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or Buy_Voucher.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            or Buy_Voucher.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            or Buy_Voucher.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            or Buy_Voucher.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            or Buy_Voucher.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            or Buy_Voucher.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        )
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyVoucherPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Buy_Voucher.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Buy_Voucher.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Buy_Voucher.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Buy_Voucher.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Buy_Voucher.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Buy_Voucher.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            and Buy_Voucher.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
            and Buy_Voucher.CiteCode like concat('%', #{SearchPojo.citecode}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            and Buy_Voucher.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            and Buy_Voucher.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and Buy_Voucher.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Buy_Voucher.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Buy_Voucher.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Buy_Voucher.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Buy_Voucher.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and Buy_Voucher.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            and Buy_Voucher.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Buy_Voucher.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Buy_Voucher.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Buy_Voucher.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Buy_Voucher.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Buy_Voucher.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Buy_Voucher.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Buy_Voucher.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Buy_Voucher.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Buy_Voucher.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Buy_Voucher.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="thor">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or Buy_Voucher.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            or Buy_Voucher.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            or Buy_Voucher.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            or Buy_Voucher.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null and SearchPojo.operator != ''">
            or Buy_Voucher.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.citecode != null and SearchPojo.citecode != ''">
            or Buy_Voucher.CiteCode like concat('%', #{SearchPojo.citecode}, '%')
        </if>
        <if test="SearchPojo.returnuid != null and SearchPojo.returnuid != ''">
            or Buy_Voucher.ReturnUid like concat('%', #{SearchPojo.returnuid}, '%')
        </if>
        <if test="SearchPojo.orguid != null and SearchPojo.orguid != ''">
            or Buy_Voucher.OrgUid like concat('%', #{SearchPojo.orguid}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or Buy_Voucher.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or Buy_Voucher.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or Buy_Voucher.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or Buy_Voucher.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or Buy_Voucher.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            or Buy_Voucher.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null and SearchPojo.assessorid != ''">
            or Buy_Voucher.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or Buy_Voucher.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or Buy_Voucher.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or Buy_Voucher.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or Buy_Voucher.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or Buy_Voucher.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            or Buy_Voucher.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            or Buy_Voucher.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            or Buy_Voucher.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            or Buy_Voucher.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            or Buy_Voucher.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        )
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Buy_Voucher(id, RefNo, BillType, BillTitle, BillDate, Projectid, ProjCode, ProjName, Groupid,
        BillAmount, Operator, CiteCode, ReturnUid, OrgUid, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid,
        ModifyDate, Assessor, Assessorid, AssessDate, FmDocMark, FmDocCode, Custom1, Custom2, Custom3, Custom4, Custom5,
        Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{projectid}, #{projcode}, #{projname},
        #{groupid}, #{billamount}, #{operator}, #{citecode}, #{returnuid}, #{orguid}, #{summary}, #{createby},
        #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate},
        #{fmdocmark}, #{fmdoccode}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
        #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_Voucher
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null">
                ProjName =#{projname},
            </if>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="citecode != null">
                CiteCode =#{citecode},
            </if>
            <if test="returnuid != null">
                ReturnUid =#{returnuid},
            </if>
            <if test="orguid != null">
                OrgUid =#{orguid},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="fmdocmark != null">
                FmDocMark =#{fmdocmark},
            </if>
            <if test="fmdoccode != null">
                FmDocCode =#{fmdoccode},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_Voucher
        where id = #{key}
        and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Buy_Voucher
        SET Assessor = #{assessor},
        AssessDate = #{assessdate},
        Revision=Revision + 1
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BuyVoucherPojo">
        select
        id
        from Buy_VoucherItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--查询DelListIds-->
    <select id="getDelCashIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BuyVoucherPojo">
        select
        id
        from Buy_VoucherCash
        where Pid = #{id}
        <if test="cash != null and cash.size() > 0">
            and id not in
            <foreach collection="cash" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--    刷新发货完成数 Eric ********-->
    <update id="updateBuyInvoFinish">
        update Buy_Invoice
        SET Paid    =COALESCE((SELECT SUM(Buy_VoucherItem.Amount)
                               FROM Buy_VoucherItem
                                        LEFT OUTER JOIN Buy_Voucher
                                                        ON Buy_VoucherItem.pid = Buy_Voucher.id
                               where Buy_VoucherItem.InvoBillCode = #{refno}
                                 and Buy_VoucherItem.Invoid = #{key}
                                 and Buy_VoucherItem.Tenantid = #{tid}), 0) +
                     COALESCE((SELECT SUM(Fm_PayApplyItem.Amount)
                               FROM Fm_PayApply
                                        RIGHT JOIN Fm_PayApplyItem ON Fm_PayApplyItem.Pid = Fm_PayApply.id
                               where Fm_PayApplyItem.InvoBillCode = #{refno}
                                 and Fm_PayApplyItem.InvoBillid = #{key}
                                 and Fm_PayApplyItem.Tenantid = #{tid}), 0),
            LastAmt =COALESCE((SELECT SUM(Buy_VoucherItem.Amount)
                               FROM Buy_VoucherItem
                                        LEFT OUTER JOIN Buy_Voucher
                                                        ON Buy_VoucherItem.pid = Buy_Voucher.id
                               where Buy_VoucherItem.InvoBillCode = #{refno}
                                 and Buy_VoucherItem.Invoid = #{key}
                                 and Buy_VoucherItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--    <update id="updateBuyInvoFinish原始">-->
    <!--        update Buy_Invoice-->
    <!--        SET Paid     =COALESCE((SELECT SUM(Buy_VoucherItem.Amount)-->
    <!--                                FROM Buy_VoucherItem-->
    <!--                                         LEFT OUTER JOIN Buy_Voucher-->
    <!--                                                         ON Buy_VoucherItem.pid = Buy_Voucher.id-->
    <!--                                where Buy_VoucherItem.InvoBillCode = #{refno}-->
    <!--                                  and Buy_VoucherItem.Invoid = #{key}-->
    <!--                                  and Buy_VoucherItem.Tenantid = #{tid}), 0) +-->
    <!--                      COALESCE((SELECT SUM(Fm_PayApplyItem.Amount)-->
    <!--                                FROM Fm_PayApply-->
    <!--                                         RIGHT JOIN Fm_PayApplyItem ON Fm_PayApplyItem.Pid = Fm_PayApply.id-->
    <!--                                where Fm_PayApplyItem.InvoBillCode = #{refno}-->
    <!--                                  and Fm_PayApplyItem.InvoBillid = #{key}-->
    <!--                                  and Fm_PayApplyItem.Tenantid = #{tid}), 0),-->
    <!--            FirstAmt =COALESCE((SELECT SUM(Fm_PayApplyItem.Amount)-->
    <!--                                FROM Fm_PayApply-->
    <!--                                         RIGHT JOIN Fm_PayApplyItem ON Fm_PayApplyItem.Pid = Fm_PayApply.id-->
    <!--                                where Fm_PayApplyItem.InvoBillCode = #{refno}-->
    <!--                                  and Fm_PayApplyItem.InvoBillid = #{key}-->
    <!--                                  and Fm_PayApplyItem.Tenantid = #{tid}), 0),-->
    <!--            LastAmt  =COALESCE((SELECT SUM(Buy_VoucherItem.Amount)-->
    <!--                                FROM Buy_VoucherItem-->
    <!--                                         LEFT OUTER JOIN Buy_Voucher-->
    <!--                                                         ON Buy_VoucherItem.pid = Buy_Voucher.id-->
    <!--                                where Buy_VoucherItem.InvoBillCode = #{refno}-->
    <!--                                  and Buy_VoucherItem.Invoid = #{key}-->
    <!--                                  and Buy_VoucherItem.Tenantid = #{tid}), 0)-->
    <!--        where id = #{key}-->
    <!--          and Tenantid = #{tid}-->
    <!--    </update>-->

    <update id="updateBuyInvoItemAvgAmt">
        update Buy_InvoiceItem
        SET AvgFirstAmt=COALESCE((SELECT FirstAmt/TaxAmount FROM Buy_Invoice where id=#{key} and
        Tenantid=#{tid})*Buy_InvoiceItem.TaxAmount,0),
        AvgLastAmt=COALESCE((SELECT LastAmt/TaxAmount FROM Buy_Invoice where id=#{key} and
        Tenantid=#{tid})*Buy_InvoiceItem.TaxAmount,0)
        where Pid = #{key}
        and Tenantid = #{tid}
    </update>

    <update id="updateBuyOrderItemAvgAmt">
        update Buy_OrderItem
        SET AvgFirstAmt=COALESCE((SELECT SUM(AvgFirstAmt) FROM Buy_InvoiceItem where OrderItemid = Buy_OrderItem.id),
        0),
        AvgLastAmt=COALESCE((SELECT SUM(AvgLastAmt) FROM Buy_InvoiceItem where OrderItemid = Buy_OrderItem.id), 0)
        where id in (select distinct OrderItemid from Buy_InvoiceItem where Pid = #{key} and Tenantid = #{tid})
        and Tenantid = #{tid}
    </update>

    <update id="updateBuyOrderFirstLastAmt">
        update Buy_Order
        SET FirstAmt=COALESCE((SELECT SUM(AvgFirstAmt) FROM Buy_OrderItem where Pid = Buy_Order.id), 0),
        LastAmt=COALESCE((SELECT SUM(AvgLastAmt) FROM Buy_OrderItem where Pid = Buy_Order.id), 0)
        where id in (select distinct Pid
        from Buy_OrderItem
        where id in
        (select distinct OrderItemid from Buy_InvoiceItem where Pid = #{key} and Tenantid = #{tid}))
        and Tenantid = #{tid}
    </update>


    <!--    刷新发货完成数 Eric ********-->
    <update id="updateFiniFinish">
        update Buy_Finishing
        SET BillPaid =COALESCE((SELECT SUM(Buy_VoucherItem.Amount)
        FROM Buy_VoucherItem
        LEFT OUTER JOIN Buy_Voucher
        ON Buy_VoucherItem.pid = Buy_Voucher.id
        where Buy_VoucherItem.InvoBillCode = #{refno}
        and Buy_VoucherItem.Invoid = #{key}
        and Buy_VoucherItem.Tenantid = #{tid}), 0)
        where id = #{key}
        and Tenantid = #{tid}
    </update>


    <!--    刷新发货完成数 Eric ********-->
    <update id="updateCashAmount">
        update Fm_CashAccount
        SET CurrentAmt =CurrentAmt + #{amount}
        where id = #{key}
        and Tenantid = #{tid}
    </update>

    <update id="updateOrgReturn">
        update Buy_Voucher
        SET ReturnUid = #{redUid}
        where RefNo= #{orgUid}
        and Tenantid = #{tid}
    </update>
</mapper>

