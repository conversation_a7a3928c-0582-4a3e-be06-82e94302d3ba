<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatSkuMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatSkuPojo">
        SELECT Mat_Sku.id,
               Mat_Sku.SkuCode,
               Mat_Sku.SkuNum,
               Mat_Sku.Goodsid,
               Mat_Sku.ItemCode,
               Mat_Sku.ItemName,
               Mat_Sku.AttributeJson,
               Mat_Sku.BarCode,
               Mat_Sku.SafeStock,
               Mat_Sku.InPrice,
               Mat_Sku.OutPrice,
               Mat_Sku.IvQuantity,
               Mat_Sku.IvAmount,
               Mat_Sku.AgePrice,
               Mat_Sku.<PERSON>ku<PERSON>,
               Mat_<PERSON><PERSON>.Remark,
               Mat_<PERSON><PERSON>.<PERSON>,
               <PERSON>_<PERSON><PERSON>,
               <PERSON>_<PERSON><PERSON><PERSON>,
               <PERSON>_<PERSON><PERSON><PERSON>,
               <PERSON>_<PERSON><PERSON>.<PERSON>er,
               Mat_S<PERSON>.<PERSON>erid,
               Mat_<PERSON>ku.ModifyDate,
               Mat_Sku.Custom1,
               Mat_Sku.Custom2,
               Mat_Sku.Custom3,
               Mat_Sku.Custom4,
               Mat_Sku.Custom5,
               Mat_Sku.Custom6,
               Mat_Sku.Custom7,
               Mat_Sku.Custom8,
               Mat_Sku.Custom9,
               Mat_Sku.Custom10,
               Mat_Sku.Tenantid,
               Mat_Sku.TenantName,
               Mat_Sku.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_Goods
                 RIGHT JOIN Mat_Sku ON Mat_Sku.Goodsid = Mat_Goods.id
        where Mat_Sku.id = #{key}
          and Mat_Sku.Tenantid = #{tid}
    </select>
    <sql id="selectMatSkuVo">
        SELECT Mat_Sku.id,
               Mat_Sku.SkuCode,
               Mat_Sku.SkuNum,
               Mat_Sku.Goodsid,
               Mat_Sku.ItemCode,
               Mat_Sku.ItemName,
               Mat_Sku.AttributeJson,
               Mat_Sku.BarCode,
               Mat_Sku.SafeStock,
               Mat_Sku.InPrice,
               Mat_Sku.OutPrice,
               Mat_Sku.IvQuantity,
               Mat_Sku.IvAmount,
               Mat_Sku.AgePrice,
               Mat_Sku.SkuPhoto,
               Mat_Sku.Remark,
               Mat_Sku.RowNum,
               Mat_Sku.CreateBy,
               Mat_Sku.CreateByid,
               Mat_Sku.CreateDate,
               Mat_Sku.Lister,
               Mat_Sku.Listerid,
               Mat_Sku.ModifyDate,
               Mat_Sku.Custom1,
               Mat_Sku.Custom2,
               Mat_Sku.Custom3,
               Mat_Sku.Custom4,
               Mat_Sku.Custom5,
               Mat_Sku.Custom6,
               Mat_Sku.Custom7,
               Mat_Sku.Custom8,
               Mat_Sku.Custom9,
               Mat_Sku.Custom10,
               Mat_Sku.Tenantid,
               Mat_Sku.TenantName,
               Mat_Sku.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_Goods
                 RIGHT JOIN Mat_Sku ON Mat_Sku.Goodsid = Mat_Goods.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatSkuPojo">
        <include refid="selectMatSkuVo"/>
        where 1 = 1 and Mat_Sku.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Sku.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.skucode != null ">
            and Mat_Sku.SkuCode like concat('%', #{SearchPojo.skucode}, '%')
        </if>
        <if test="SearchPojo.goodsid != null ">
            and Mat_Sku.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null ">
            and Mat_Sku.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null ">
            and Mat_Sku.ItemName like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.attributejson != null ">
            and Mat_Sku.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
        <if test="SearchPojo.barcode != null ">
            and Mat_Sku.BarCode like concat('%', #{SearchPojo.barcode}, '%')
        </if>
        <if test="SearchPojo.skuphoto != null ">
            and Mat_Sku.SkuPhoto like concat('%', #{SearchPojo.skuphoto}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and Mat_Sku.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Mat_Sku.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Mat_Sku.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Mat_Sku.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Mat_Sku.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Mat_Sku.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Mat_Sku.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Mat_Sku.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Mat_Sku.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Mat_Sku.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Mat_Sku.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Mat_Sku.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Mat_Sku.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Mat_Sku.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Mat_Sku.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Mat_Sku.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.skucode != null ">
                or Mat_Sku.SkuCode like concat('%', #{SearchPojo.skucode}, '%')
            </if>
            <if test="SearchPojo.goodsid != null ">
                or Mat_Sku.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null ">
                or Mat_Sku.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null ">
                or Mat_Sku.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.attributejson != null ">
                or Mat_Sku.AttributeJson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
            <if test="SearchPojo.barcode != null ">
                or Mat_Sku.BarCode like concat('%', #{SearchPojo.barcode}, '%')
            </if>
            <if test="SearchPojo.skuphoto != null ">
                or Mat_Sku.SkuPhoto like concat('%', #{SearchPojo.skuphoto}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or Mat_Sku.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Mat_Sku.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Mat_Sku.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Mat_Sku.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Mat_Sku.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Mat_Sku.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Mat_Sku.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Mat_Sku.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Mat_Sku.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Mat_Sku.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Mat_Sku.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Mat_Sku.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Mat_Sku.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Mat_Sku.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Mat_Sku.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Mat_Sku.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Sku(id, SkuCode, SkuNum, Goodsid, ItemCode, ItemName, AttributeJson, BarCode, SafeStock,
                            InPrice, OutPrice, IvQuantity, IvAmount, AgePrice, SkuPhoto, Remark, RowNum, CreateBy,
                            CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4,
                            Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{skucode}, #{skunum}, #{goodsid}, #{itemcode}, #{itemname}, #{attributejson}, #{barcode},
                #{safestock}, #{inprice}, #{outprice}, #{ivquantity}, #{ivamount}, #{ageprice}, #{skuphoto}, #{remark},
                #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Sku
        <set>
            <if test="skucode != null ">
                SkuCode =#{skucode},
            </if>
            <if test="skunum != null">
                SkuNum =#{skunum},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="itemcode != null ">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName =#{itemname},
            </if>
            <if test="attributejson != null ">
                AttributeJson =#{attributejson},
            </if>
            <if test="barcode != null ">
                BarCode =#{barcode},
            </if>
            <if test="safestock != null">
                SafeStock =#{safestock},
            </if>
            <if test="inprice != null">
                InPrice =#{inprice},
            </if>
            <if test="outprice != null">
                OutPrice =#{outprice},
            </if>
            <if test="ivquantity != null">
                IvQuantity =#{ivquantity},
            </if>
            <if test="ivamount != null">
                IvAmount =#{ivamount},
            </if>
            <if test="ageprice != null">
                AgePrice =#{ageprice},
            </if>
            <if test="skuphoto != null ">
                SkuPhoto =#{skuphoto},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Sku
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询单个-->
    <select id="getListByGoodsid" resultType="inks.service.sa.som.domain.pojo.MatSkuPojo">
        select id,
               SkuCode,
               SkuNum,
               Goodsid,
               ItemCode,
               ItemName,
               AttributeJson,
               BarCode,
               SafeStock,
               InPrice,
               OutPrice,
               IvQuantity,
               IvAmount,
               AgePrice,
               SkuPhoto,
               Remark,
               RowNum,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_Sku
        where Mat_Sku.Goodsid = #{goodsid}
          and Mat_Sku.Tenantid = #{tid}
    </select>

    <!--查询单个-->
    <select id="getListByGoodsAttr" resultType="inks.service.sa.som.domain.pojo.MatSkuPojo">
        SELECT
        Mat_Sku.id,
        Mat_Sku.SkuCode,
        Mat_Sku.SkuNum,
        Mat_Sku.Goodsid,
        Mat_Sku.ItemCode,
        Mat_Sku.ItemName,
        Mat_Sku.AttributeJson,
        Mat_Sku.BarCode,
        Mat_Sku.SafeStock,
        Mat_Sku.InPrice,
        Mat_Sku.OutPrice,
        Mat_Sku.IvQuantity,
        Mat_Sku.IvAmount,
        Mat_Sku.AgePrice,
        Mat_Sku.SkuPhoto,
        Mat_Sku.Remark,
        Mat_Sku.RowNum,
        Mat_Sku.CreateBy,
        Mat_Sku.CreateByid,
        Mat_Sku.CreateDate,
        Mat_Sku.Lister,
        Mat_Sku.Listerid,
        Mat_Sku.ModifyDate,
        Mat_Sku.Custom1,
        Mat_Sku.Custom2,
        Mat_Sku.Custom3,
        Mat_Sku.Custom4,
        Mat_Sku.Custom5,
        Mat_Sku.Custom6,
        Mat_Sku.Custom7,
        Mat_Sku.Custom8,
        Mat_Sku.Custom9,
        Mat_Sku.Custom10,
        Mat_Sku.Tenantid,
        Mat_Sku.TenantName,
        Mat_Sku.Revision,
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
        Mat_Goods.Material as GoodsMaterial,
        Mat_Goods.Custom1 AS GoodsCustom1,
        Mat_Goods.Custom2 AS GoodsCustom2,
        Mat_Goods.Custom3 AS GoodsCustom3,
        Mat_Goods.Custom4 AS GoodsCustom4,
        Mat_Goods.Custom5 AS GoodsCustom5,
        Mat_Goods.Custom6 AS GoodsCustom6,
        Mat_Goods.Custom7 AS GoodsCustom7,
        Mat_Goods.Custom8 AS GoodsCustom8,
        Mat_Goods.Custom9 AS GoodsCustom9,
        Mat_Goods.Custom10 AS GoodsCustom10
        FROM
        Mat_Goods
        RIGHT JOIN Mat_Sku ON Mat_Sku.Goodsid = Mat_Goods.id
        where Mat_Sku.Goodsid = #{goodsid}
        and Mat_Sku.Tenantid = #{tid}
        <foreach collection="lst" item="item">
            and AttributeJson like '%{"key":"${item.key}","value":"${item.value}"}%'
        </foreach>
        Order by AttributeJson
    </select>
    <!--查询单个-->
    <select id="getEntityByGoodsMax" resultType="inks.service.sa.som.domain.pojo.MatSkuPojo">
        SELECT Mat_Sku.id,
               Mat_Sku.SkuCode,
               Mat_Sku.SkuNum,
               Mat_Sku.Goodsid,
               Mat_Sku.ItemCode,
               Mat_Sku.ItemName,
               Mat_Sku.AttributeJson,
               Mat_Sku.BarCode,
               Mat_Sku.SafeStock,
               Mat_Sku.InPrice,
               Mat_Sku.OutPrice,
               Mat_Sku.IvQuantity,
               Mat_Sku.IvAmount,
               Mat_Sku.AgePrice,
               Mat_Sku.SkuPhoto,
               Mat_Sku.Remark,
               Mat_Sku.RowNum,
               Mat_Sku.CreateBy,
               Mat_Sku.CreateByid,
               Mat_Sku.CreateDate,
               Mat_Sku.Lister,
               Mat_Sku.Listerid,
               Mat_Sku.ModifyDate,
               Mat_Sku.Custom1,
               Mat_Sku.Custom2,
               Mat_Sku.Custom3,
               Mat_Sku.Custom4,
               Mat_Sku.Custom5,
               Mat_Sku.Custom6,
               Mat_Sku.Custom7,
               Mat_Sku.Custom8,
               Mat_Sku.Custom9,
               Mat_Sku.Custom10,
               Mat_Sku.Tenantid,
               Mat_Sku.TenantName,
               Mat_Sku.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10
        FROM Mat_Goods
                 RIGHT JOIN Mat_Sku ON Mat_Sku.Goodsid = Mat_Goods.id
        where Mat_Sku.Goodsid = #{goodsid}
          and Mat_Sku.Tenantid = #{tid}
        Order by SkuNum Desc Limit 1
    </select>

    <select id="getAttrList" resultType="inks.service.sa.som.domain.pojo.MatAttributePojo">
        SELECT id, AttrKey, AttrName, SkuMark
        FROM Mat_Attribute
        WHERE Tenantid = #{tid}
    </select>
</mapper>

