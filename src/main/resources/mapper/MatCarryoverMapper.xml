<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatCarryoverMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatCarryoverPojo">
        select id,
               RefNo,
               BillType,
               BillDate,
               Projectid,
               ProjName,
               ProjCode,
               BillTitle,
               Storeid,
               StoreCode,
               StoreName,
               CarryYear,
               CarryMonth,
               StartDate,
               EndDate,
               Operator,
               Operatorid,
               RowNum,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               BillOpenAmount,
               BillInAmount,
               BillOutAmount,
               BillCloseAmount,
               ItemCount,
               PrintCount,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_Carryover
        where Mat_Carryover.id = #{key}
          and Mat_Carryover.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               RefNo,
               BillType,
               BillDate,
               BillTitle,
               Projectid,
               ProjName,
               ProjCode,
               Storeid,
               StoreCode,
               StoreName,
               CarryYear,
               CarryMonth,
               StartDate,
               EndDate,
               Operator,
               Operatorid,
               RowNum,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               BillOpenAmount,
               BillInAmount,
               BillOutAmount,
               BillCloseAmount,
               ItemCount,
               PrintCount,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_Carryover
    </sql>
    <sql id="selectdetailVo">
        select id,
               RefNo,
               BillType,
               BillDate,
               BillTitle,
               Projectid,
               ProjName,
               ProjCode,
               Storeid,
               StoreCode,
               StoreName,
               CarryYear,
               CarryMonth,
               StartDate,
               EndDate,
               Operator,
               Operatorid,
               RowNum,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               BillOpenAmount,
               BillInAmount,
               BillOutAmount,
               BillCloseAmount,
               ItemCount,
               PrintCount,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_Carryover
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatCarryoveritemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Mat_Carryover.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Carryover.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Mat_Carryover.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Mat_Carryover.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Mat_Carryover.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.storeid != null ">
            and Mat_Carryover.storeid like concat('%', #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.storecode != null ">
            and Mat_Carryover.storecode like concat('%', #{SearchPojo.storecode}, '%')
        </if>
        <if test="SearchPojo.storename != null ">
            and Mat_Carryover.storename like concat('%', #{SearchPojo.storename}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Mat_Carryover.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Mat_Carryover.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Mat_Carryover.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Mat_Carryover.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Mat_Carryover.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Mat_Carryover.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Mat_Carryover.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Mat_Carryover.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Mat_Carryover.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Mat_Carryover.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Mat_Carryover.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Mat_Carryover.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Mat_Carryover.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Mat_Carryover.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Mat_Carryover.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Mat_Carryover.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Mat_Carryover.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Mat_Carryover.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Mat_Carryover.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Mat_Carryover.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Mat_Carryover.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.storeid != null ">
                or Mat_Carryover.Storeid like concat('%', #{SearchPojo.storeid}, '%')
            </if>
            <if test="SearchPojo.storecode != null ">
                or Mat_Carryover.StoreCode like concat('%', #{SearchPojo.storecode}, '%')
            </if>
            <if test="SearchPojo.storename != null ">
                or Mat_Carryover.StoreName like concat('%', #{SearchPojo.storename}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Mat_Carryover.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Mat_Carryover.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Mat_Carryover.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Mat_Carryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Mat_Carryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Mat_Carryover.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Mat_Carryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Mat_Carryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Mat_Carryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Mat_Carryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Mat_Carryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Mat_Carryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Mat_Carryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Mat_Carryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Mat_Carryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Mat_Carryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Mat_Carryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Mat_Carryover.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatCarryoverPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Mat_Carryover.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Carryover.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Mat_Carryover.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Mat_Carryover.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Mat_Carryover.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.storeid != null ">
            and Mat_Carryover.Storeid like concat('%', #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.storecode != null ">
            and Mat_Carryover.StoreCode like concat('%', #{SearchPojo.storecode}, '%')
        </if>
        <if test="SearchPojo.storename != null ">
            and Mat_Carryover.StoreName like concat('%', #{SearchPojo.storename}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Mat_Carryover.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Mat_Carryover.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Mat_Carryover.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Mat_Carryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Mat_Carryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Mat_Carryover.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Mat_Carryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Mat_Carryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Mat_Carryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Mat_Carryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Mat_Carryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Mat_Carryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Mat_Carryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Mat_Carryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Mat_Carryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Mat_Carryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Mat_Carryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Mat_Carryover.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Mat_Carryover.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Mat_Carryover.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Mat_Carryover.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.storeid != null ">
                or Mat_Carryover.Storeid like concat('%', #{SearchPojo.storeid}, '%')
            </if>
            <if test="SearchPojo.storecode != null ">
                or Mat_Carryover.StoreCode like concat('%', #{SearchPojo.storecode}, '%')
            </if>
            <if test="SearchPojo.storename != null ">
                or Mat_Carryover.StoreName like concat('%', #{SearchPojo.storename}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Mat_Carryover.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Mat_Carryover.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Mat_Carryover.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Mat_Carryover.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Mat_Carryover.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Mat_Carryover.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Mat_Carryover.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Mat_Carryover.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Mat_Carryover.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Mat_Carryover.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Mat_Carryover.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Mat_Carryover.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Mat_Carryover.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Mat_Carryover.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Mat_Carryover.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Mat_Carryover.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Mat_Carryover.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Mat_Carryover.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Carryover(id, RefNo, BillType, BillDate, Projectid, ProjCode, ProjName, BillTitle, Storeid, StoreCode, StoreName, CarryYear,
                                  CarryMonth, StartDate, EndDate, Operator, Operatorid, RowNum, Summary, CreateBy,
                                  CreateByid, CreateDate, Lister, Listerid, ModifyDate, BillOpenAmount, BillInAmount,
                                  BillOutAmount, BillCloseAmount, ItemCount, PrintCount, Custom1, Custom2, Custom3,
                                  Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName,
                                  Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{billtitle}, #{storeid}, #{storecode}, #{storename},
                #{carryyear}, #{carrymonth}, #{startdate}, #{enddate}, #{operator}, #{operatorid}, #{rownum},
                #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
                #{billopenamount}, #{billinamount}, #{billoutamount}, #{billcloseamount}, #{itemcount}, #{printcount},
                #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8},
                #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Carryover
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="storeid != null ">
                Storeid =#{storeid},
            </if>
            <if test="storecode != null ">
                StoreCode =#{storecode},
            </if>
            <if test="storename != null ">
                StoreName =#{storename},
            </if>
            <if test="carryyear != null">
                CarryYear =#{carryyear},
            </if>
            <if test="carrymonth != null">
                CarryMonth =#{carrymonth},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="billopenamount != null">
                BillOpenAmount =#{billopenamount},
            </if>
            <if test="billinamount != null">
                BillInAmount =#{billinamount},
            </if>
            <if test="billoutamount != null">
                BillOutAmount =#{billoutamount},
            </if>
            <if test="billcloseamount != null">
                BillCloseAmount =#{billcloseamount},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Carryover
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.MatCarryoverPojo">
        select
        id
        from Mat_CarryoverItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--查询指定行数据-->
    <select id="getGoodsAcceList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatCarryoveritemPojo">
        SELECT Mat_Goods.id as Goodsid,Mat_Goods.GoodsUid,Mat_Goods.GoodsName,Mat_Goods.GoodsSpec,Mat_Goods.GoodsUnit,
        SUM(CASE
        WHEN Mat_Access.BillType IN ('收货入库','购退红冲','生产入库','加工入库','其他入库') THEN Mat_AccessItem.Quantity
        WHEN Mat_Access.BillType IN ('收货红冲','购退出库','生产红冲','加工红冲','他入红冲') THEN 0 - Mat_AccessItem.Quantity END) as InQty,
        SUM(CASE
        WHEN Mat_Access.BillType IN ('收货入库','购退红冲','生产入库','加工入库','其他入库') THEN Mat_AccessItem.Amount
        WHEN Mat_Access.BillType IN ('收货红冲','购退出库','生产红冲','加工红冲','他入红冲') THEN 0 - Mat_AccessItem.Amount END) as
        InAmount,
        SUM(CASE
        WHEN Mat_Access.BillType IN ('发货出库','客退红冲','领料出库','退料红冲','其他出库','报废出库','盘盈红冲','盘亏出库') THEN
        Mat_AccessItem.Quantity
        WHEN Mat_Access.BillType IN ('发货红冲','客退入库','领料红冲','退料入库','他出红冲','报废红冲','盘盈入库','盘亏红冲') THEN 0 -
        Mat_AccessItem.Quantity END) as OutQty,
        SUM(CASE
        WHEN Mat_Access.BillType IN ('发货出库','客退红冲','领料出库','退料红冲','其他出库','报废出库','盘盈红冲','盘亏出库') THEN Mat_AccessItem.Amount
        WHEN Mat_Access.BillType IN ('发货红冲','客退入库','领料红冲','退料入库','他出红冲','报废红冲','盘盈入库','盘亏红冲') THEN 0 -
        Mat_AccessItem.Amount END) as OutAmount
        FROM Mat_Access
        RIGHT JOIN Mat_AccessItem ON Mat_Access.id = Mat_AccessItem.Pid
        LEFT JOIN Mat_Goods ON Mat_AccessItem.Goodsid = Mat_Goods.id
        where Mat_Access.Tenantid =#{tenantid}
        and Mat_Access.BillDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        Group by Mat_Goods.id,Mat_Goods.GoodsUid,Mat_Goods.GoodsName,Mat_Goods.GoodsSpec,Mat_Goods.GoodsUnit
        Order by Mat_Goods.GoodsUid
    </select>

    <!--查询单个-->
    <select id="getStoreList" resultType="inks.service.sa.som.domain.pojo.MatStoragePojo">
        select id,
               StoreCode,
               StoreName
        from Mat_Storage
        where Mat_Storage.EnabledMark = 1
          and Mat_Storage.Tenantid = #{tid}
        Order By Mat_Storage.RowNum
    </select>

    <!--查询单个-->
    <select id="getMaxEntityByStore" resultType="inks.service.sa.som.domain.pojo.MatCarryoverPojo">
        <include refid="selectbillVo"/>
        where Mat_Carryover.Storeid = #{key}
        and Mat_Carryover.Tenantid = #{tid}
        Order by EndDate Desc LIMIT 1
    </select>

    <!--查询指定行数据-->
    <select id="getMultItemList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatCarryoveritemPojo">
        <include refid="selectdetailVo"/>
        where Mat_Carryover.Tenantid =#{tenantid}
        and Buy_AccountItem.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        ${filterstr}
        order by Buy_AccountItem.BillDate
    </select>

    <!--查询指定行数据-->
    <select id="getNowPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatCarryoverPojo">
        SELECT * FROM (SELECT
        App_Workgroup.id,
        App_Workgroup.WgGroupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate,
        App_Workgroup.Seller,
        COALESCE((SELECT
        Buy_Account.BillCloseAmount
        FROM Buy_Account
        WHERE Buy_Account.Groupid=App_Workgroup.id
        Order By RowNum Desc LIMIT 1),0) as BillOpenAmount,

        COALESCE((SELECT
        sum(CASE
        WHEN Buy_Finishing.BillType IN ('采购验收','其他收货') THEN Buy_Finishing.BillTaxAmount
        ELSE 0 - Buy_Finishing.BillTaxAmount END)
        FROM Buy_Finishing where Buy_Finishing.Groupid=App_Workgroup.id
        and Buy_Finishing.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        ),0)-
        COALESCE((SELECT Sum(Buy_Deduction.BillTaxAmount) FROM Buy_Deduction
        where Buy_Deduction.Groupid=App_Workgroup.id
        and Buy_Deduction.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0)
        AS BillInAmount,

        COALESCE((SELECT
        Sum(Buy_Prepayments.BillAmount)
        FROM Buy_Prepayments WHERE Buy_Prepayments.Groupid=App_Workgroup.id
        and Buy_Prepayments.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0)+
        COALESCE((SELECT
        Sum(Buy_Voucher.BillAmount)
        FROM Buy_Voucher WHERE Buy_Voucher.Groupid=App_Workgroup.id
        and Buy_Voucher.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0) as BillOutAmount,

        COALESCE((SELECT
        Buy_Account.BillCloseAmount
        FROM Buy_Account
        WHERE Buy_Account.Groupid=App_Workgroup.id
        Order By RowNum Desc LIMIT 1),0) +
        COALESCE((SELECT
        sum(CASE
        WHEN Buy_Finishing.BillType IN ('采购验收','其他收货') THEN Buy_Finishing.BillTaxAmount
        ELSE 0 - Buy_Finishing.BillTaxAmount END)
        FROM Buy_Finishing where Buy_Finishing.Groupid=App_Workgroup.id
        and Buy_Finishing.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        ),0)-
        COALESCE((SELECT Sum(Buy_Deduction.BillTaxAmount) FROM Buy_Deduction
        where Buy_Deduction.Groupid=App_Workgroup.id
        and Buy_Deduction.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0)
        -
        COALESCE((SELECT
        Sum(Buy_Prepayments.BillAmount)
        FROM Buy_Prepayments WHERE Buy_Prepayments.Groupid=App_Workgroup.id
        and Buy_Prepayments.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0)-
        COALESCE((SELECT
        Sum(Buy_Voucher.BillAmount)
        FROM Buy_Voucher WHERE Buy_Voucher.Groupid=App_Workgroup.id
        and Buy_Voucher.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0) AS BillCloseAmount

        FROM
        App_Workgroup
        where App_Workgroup.EnabledMark = 1
        and App_Workgroup.DeleteMark = 0
        and (GroupType='供应商' OR GroupType='外协厂商')
        and App_Workgroup.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        ) t
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>

    <!--通过主键删除-->
    <delete id="deleteByMonth">
        delete
        from Mat_Carryover
        where CarryYear = #{year}
          and CarryMonth = #{nonth}
          and Tenantid = #{tid}
    </delete>
</mapper>

