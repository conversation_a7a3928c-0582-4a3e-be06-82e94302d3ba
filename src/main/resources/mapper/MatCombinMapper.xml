<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatCombinMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatCombinPojo">
        select id,
               RefNo,
               BillType,
               BillDate,
               Projectid,
               ProjName,
               ProjCode,
               BillTitle,
               OutStoreid,
               OutStoreCode,
               OutStoreName,
               InStoreid,
               InStoreCode,
               InStoreName,
               Operator,
               Operatorid,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_Combin
        where Mat_Combin.id = #{key}
          and Mat_Combin.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               RefNo,
               BillType,
               BillDate,
               Projectid,
               ProjName,
               ProjCode,
               BillTitle,
               OutStoreid,
               OutStoreCode,
               OutStoreName,
               InStoreid,
               InStoreCode,
               InStoreName,
               Operator,
               Operatorid,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Mat_Combin
    </sql>
    <sql id="selectdetailVo">
        SELECT
            Mat_CombinItem.id,
            Mat_CombinItem.Pid,
            Mat_CombinItem.AccessType,
            Mat_CombinItem.Goodsid,
            Mat_CombinItem.ItemCode,
            Mat_CombinItem.ItemName,
            Mat_CombinItem.ItemSpec,
            Mat_CombinItem.ItemUnit,
            Mat_CombinItem.Quantity,
            Mat_CombinItem.Price,
            Mat_CombinItem.Amount,
            Mat_CombinItem.Remark,
            Mat_CombinItem.RowNum,
            Mat_CombinItem.Location,
            Mat_CombinItem.BatchNo,
            Mat_CombinItem.PackSn,
            Mat_CombinItem.ExpiDate,
            Mat_CombinItem.Inveid,
            Mat_CombinItem.Custom1,
            Mat_CombinItem.Custom2,
            Mat_CombinItem.Custom3,
            Mat_CombinItem.Custom4,
            Mat_CombinItem.Custom5,
            Mat_CombinItem.Custom6,
            Mat_CombinItem.Custom7,
            Mat_CombinItem.Custom8,
            Mat_CombinItem.Custom9,
            Mat_CombinItem.Custom10,
            Mat_CombinItem.Tenantid,
            Mat_CombinItem.TenantName,
            Mat_CombinItem.Revision,
            Mat_Goods.GoodsUid,
            Mat_Goods.GoodsName,
            Mat_Goods.GoodsSpec,
            Mat_Goods.GoodsUnit,
            Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
            Mat_Goods.Material as GoodsMaterial,
            Mat_Goods.Custom1  AS GoodsCustom1,
            Mat_Goods.Custom2  AS GoodsCustom2,
            Mat_Goods.Custom3  AS GoodsCustom3,
            Mat_Goods.Custom4  AS GoodsCustom4,
            Mat_Goods.Custom5  AS GoodsCustom5,
            Mat_Goods.Custom6  AS GoodsCustom6,
            Mat_Goods.Custom7  AS GoodsCustom7,
            Mat_Goods.Custom8  AS GoodsCustom8,
            Mat_Goods.Custom9  AS GoodsCustom9,
            Mat_Goods.Custom10 AS GoodsCustom10,
            Mat_Combin.RefNo,
            Mat_Combin.BillType,
            Mat_Combin.BillDate,
            Mat_Combin.Projectid,
            Mat_Combin.ProjName,
            Mat_Combin.ProjCode,
            Mat_Combin.BillTitle,
            Mat_Combin.OutStoreCode,
            Mat_Combin.OutStoreName,
            Mat_Combin.InStoreCode,
            Mat_Combin.InStoreName,
            Mat_Combin.Operator,
            Mat_Combin.Summary,
            Mat_Combin.Lister
        FROM
            Mat_Goods
                RIGHT JOIN Mat_CombinItem ON Mat_Goods.id = Mat_CombinItem.Goodsid
                LEFT JOIN Mat_Combin ON Mat_CombinItem.Pid = Mat_Combin.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatCombinitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Mat_Combin.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Combin.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Mat_Combin.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Mat_Combin.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Mat_Combin.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.outstoreid != null ">
            and Mat_Combin.outstoreid like concat('%', #{SearchPojo.outstoreid}, '%')
        </if>
        <if test="SearchPojo.outstorecode != null ">
            and Mat_Combin.outstorecode like concat('%', #{SearchPojo.outstorecode}, '%')
        </if>
        <if test="SearchPojo.outstorename != null ">
            and Mat_Combin.outstorename like concat('%', #{SearchPojo.outstorename}, '%')
        </if>
        <if test="SearchPojo.instoreid != null ">
            and Mat_Combin.instoreid like concat('%', #{SearchPojo.instoreid}, '%')
        </if>
        <if test="SearchPojo.instorecode != null ">
            and Mat_Combin.instorecode like concat('%', #{SearchPojo.instorecode}, '%')
        </if>
        <if test="SearchPojo.instorename != null ">
            and Mat_Combin.instorename like concat('%', #{SearchPojo.instorename}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Mat_Combin.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Mat_Combin.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Mat_Combin.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Mat_Combin.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Mat_Combin.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Mat_Combin.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Mat_Combin.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Mat_Combin.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Mat_Combin.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Mat_Combin.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Mat_Combin.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Mat_Combin.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Mat_Combin.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Mat_Combin.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Mat_Combin.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Mat_Combin.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Mat_Combin.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Mat_Combin.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Mat_Combin.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Mat_Combin.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Mat_Combin.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.outstoreid != null ">
                or Mat_Combin.OutStoreid like concat('%', #{SearchPojo.outstoreid}, '%')
            </if>
            <if test="SearchPojo.outstorecode != null ">
                or Mat_Combin.OutStoreCode like concat('%', #{SearchPojo.outstorecode}, '%')
            </if>
            <if test="SearchPojo.outstorename != null ">
                or Mat_Combin.OutStoreName like concat('%', #{SearchPojo.outstorename}, '%')
            </if>
            <if test="SearchPojo.instoreid != null ">
                or Mat_Combin.InStoreid like concat('%', #{SearchPojo.instoreid}, '%')
            </if>
            <if test="SearchPojo.instorecode != null ">
                or Mat_Combin.InStoreCode like concat('%', #{SearchPojo.instorecode}, '%')
            </if>
            <if test="SearchPojo.instorename != null ">
                or Mat_Combin.InStoreName like concat('%', #{SearchPojo.instorename}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Mat_Combin.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Mat_Combin.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Mat_Combin.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Mat_Combin.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Mat_Combin.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Mat_Combin.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Mat_Combin.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Mat_Combin.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Mat_Combin.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Mat_Combin.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Mat_Combin.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Mat_Combin.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Mat_Combin.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Mat_Combin.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Mat_Combin.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Mat_Combin.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Mat_Combin.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Mat_Combin.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatCombinPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Mat_Combin.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Combin.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Mat_Combin.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Mat_Combin.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Mat_Combin.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.outstoreid != null ">
            and Mat_Combin.OutStoreid like concat('%', #{SearchPojo.outstoreid}, '%')
        </if>
        <if test="SearchPojo.outstorecode != null ">
            and Mat_Combin.OutStoreCode like concat('%', #{SearchPojo.outstorecode}, '%')
        </if>
        <if test="SearchPojo.outstorename != null ">
            and Mat_Combin.OutStoreName like concat('%', #{SearchPojo.outstorename}, '%')
        </if>
        <if test="SearchPojo.instoreid != null ">
            and Mat_Combin.InStoreid like concat('%', #{SearchPojo.instoreid}, '%')
        </if>
        <if test="SearchPojo.instorecode != null ">
            and Mat_Combin.InStoreCode like concat('%', #{SearchPojo.instorecode}, '%')
        </if>
        <if test="SearchPojo.instorename != null ">
            and Mat_Combin.InStoreName like concat('%', #{SearchPojo.instorename}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Mat_Combin.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Mat_Combin.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Mat_Combin.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Mat_Combin.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Mat_Combin.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Mat_Combin.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Mat_Combin.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Mat_Combin.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Mat_Combin.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Mat_Combin.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Mat_Combin.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Mat_Combin.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Mat_Combin.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Mat_Combin.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Mat_Combin.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Mat_Combin.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Mat_Combin.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Mat_Combin.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Mat_Combin.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Mat_Combin.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Mat_Combin.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.outstoreid != null ">
                or Mat_Combin.OutStoreid like concat('%', #{SearchPojo.outstoreid}, '%')
            </if>
            <if test="SearchPojo.outstorecode != null ">
                or Mat_Combin.OutStoreCode like concat('%', #{SearchPojo.outstorecode}, '%')
            </if>
            <if test="SearchPojo.outstorename != null ">
                or Mat_Combin.OutStoreName like concat('%', #{SearchPojo.outstorename}, '%')
            </if>
            <if test="SearchPojo.instoreid != null ">
                or Mat_Combin.InStoreid like concat('%', #{SearchPojo.instoreid}, '%')
            </if>
            <if test="SearchPojo.instorecode != null ">
                or Mat_Combin.InStoreCode like concat('%', #{SearchPojo.instorecode}, '%')
            </if>
            <if test="SearchPojo.instorename != null ">
                or Mat_Combin.InStoreName like concat('%', #{SearchPojo.instorename}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Mat_Combin.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Mat_Combin.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Mat_Combin.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Mat_Combin.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Mat_Combin.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Mat_Combin.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Mat_Combin.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Mat_Combin.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Mat_Combin.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Mat_Combin.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Mat_Combin.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Mat_Combin.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Mat_Combin.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Mat_Combin.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Mat_Combin.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Mat_Combin.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Mat_Combin.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Mat_Combin.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Combin(id, RefNo, BillType, BillDate, Projectid, ProjCode, ProjName, BillTitle, OutStoreid, OutStoreCode, OutStoreName,
                               InStoreid, InStoreCode, InStoreName, Operator, Operatorid, Summary, CreateBy, CreateByid,
                               CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5,
                               Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{billtitle}, #{outstoreid}, #{outstorecode},
                #{outstorename}, #{instoreid}, #{instorecode}, #{instorename}, #{operator}, #{operatorid}, #{summary},
                #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
                #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Combin
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="outstoreid != null ">
                OutStoreid =#{outstoreid},
            </if>
            <if test="outstorecode != null ">
                OutStoreCode =#{outstorecode},
            </if>
            <if test="outstorename != null ">
                OutStoreName =#{outstorename},
            </if>
            <if test="instoreid != null ">
                InStoreid =#{instoreid},
            </if>
            <if test="instorecode != null ">
                InStoreCode =#{instorecode},
            </if>
            <if test="instorename != null ">
                InStoreName =#{instorename},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Combin
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.MatCombinPojo">
        select
        id
        from Mat_CombinItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

</mapper>

