<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyFinishingMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyFinishingPojo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Buy_Finishing.id,
               Buy_Finishing.RefNo,
               Buy_Finishing.BillType,
               Buy_Finishing.BillTitle,
               Buy_Finishing.BillDate,
               Buy_Finishing.Projectid,
               Buy_Finishing.ProjName,
               Buy_Finishing.ProjCode,
               Buy_Finishing.Groupid,
               Buy_Finishing.Operator,
               Buy_Finishing.Arrivaladd,
               Buy_Finishing.TranSport,
               Buy_Finishing.Summary,
               Buy_Finishing.CreateBy,
               Buy_Finishing.CreateByid,
               Buy_Finishing.CreateDate,
               Buy_Finishing.Lister,
               Buy_Finishing.Listerid,
               Buy_Finishing.ModifyDate,
               Buy_Finishing.Assessor,
               Buy_Finishing.Assessorid,
               Buy_Finishing.AssessDate,
               Buy_Finishing.BillStateCode,
               Buy_Finishing.BillStateDate,
               Buy_Finishing.BillTaxAmount,
               Buy_Finishing.BillAmount,
               Buy_Finishing.BillTaxTotal,
               Buy_Finishing.BillPaid,
               Buy_Finishing.ItemCount,
               Buy_Finishing.FinishCount,
               Buy_Finishing.DisannulCount,
               Buy_Finishing.InvoCount,
               Buy_Finishing.PrintCount,
               Buy_Finishing.OaFlowMark,
               Buy_Finishing.Custom1,
               Buy_Finishing.Custom2,
               Buy_Finishing.Custom3,
               Buy_Finishing.Custom4,
               Buy_Finishing.Custom5,
               Buy_Finishing.Custom6,
               Buy_Finishing.Custom7,
               Buy_Finishing.Custom8,
               Buy_Finishing.Custom9,
               Buy_Finishing.Custom10,
               Buy_Finishing.Deptid,
               Buy_Finishing.Tenantid,
               Buy_Finishing.TenantName,
               Buy_Finishing.Revision
        FROM App_Workgroup
                 RIGHT JOIN Buy_Finishing ON Buy_Finishing.Groupid = App_Workgroup.id
        where Buy_Finishing.id = #{key}
          and Buy_Finishing.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Buy_Finishing.id,
               Buy_Finishing.RefNo,
               Buy_Finishing.BillType,
               Buy_Finishing.BillTitle,
               Buy_Finishing.BillDate,
               Buy_Finishing.Projectid,
               Buy_Finishing.ProjName,
               Buy_Finishing.ProjCode,
               Buy_Finishing.Groupid,
               Buy_Finishing.Operator,
               Buy_Finishing.Arrivaladd,
               Buy_Finishing.TranSport,
               Buy_Finishing.Summary,
               Buy_Finishing.CreateBy,
               Buy_Finishing.CreateByid,
               Buy_Finishing.CreateDate,
               Buy_Finishing.Lister,
               Buy_Finishing.Listerid,
               Buy_Finishing.ModifyDate,
               Buy_Finishing.Assessor,
               Buy_Finishing.Assessorid,
               Buy_Finishing.AssessDate,
               Buy_Finishing.BillStateCode,
               Buy_Finishing.BillStateDate,
               (CASE
                    WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_Finishing.BillTaxAmount
                    ELSE 0 - Buy_Finishing.BillTaxAmount END) as BillTaxAmount,
               (CASE
                    WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_Finishing.BillTaxTotal
                    ELSE 0 - Buy_Finishing.BillTaxTotal END) as BillTaxTotal,
               (CASE
                    WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_Finishing.BillAmount
                    ELSE 0 - Buy_Finishing.BillAmount END) as BillAmount,
               Buy_Finishing.BillPaid,
               Buy_Finishing.ItemCount,
               Buy_Finishing.FinishCount,
               Buy_Finishing.DisannulCount,
               Buy_Finishing.InvoCount,
               Buy_Finishing.PrintCount,
               Buy_Finishing.OaFlowMark,
               Buy_Finishing.Custom1,
               Buy_Finishing.Custom2,
               Buy_Finishing.Custom3,
               Buy_Finishing.Custom4,
               Buy_Finishing.Custom5,
               Buy_Finishing.Custom6,
               Buy_Finishing.Custom7,
               Buy_Finishing.Custom8,
               Buy_Finishing.Custom9,
               Buy_Finishing.Custom10,
               Buy_Finishing.Deptid,
               Buy_Finishing.Tenantid,
               Buy_Finishing.TenantName,
               Buy_Finishing.Revision
        FROM App_Workgroup
                 RIGHT JOIN Buy_Finishing ON Buy_Finishing.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10,
               Buy_FinishingItem.id,
               Buy_FinishingItem.Pid,
               Buy_FinishingItem.Goodsid,
               Buy_FinishingItem.ItemCode,
               Buy_FinishingItem.ItemName,
               Buy_FinishingItem.ItemSpec,
               Buy_FinishingItem.ItemUnit,
               Buy_FinishingItem.TaxPrice,
               Buy_FinishingItem.Price,
               (CASE
                    WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_FinishingItem.Quantity
                    ELSE 0 - Buy_FinishingItem.Quantity END) as Quantity,
               (CASE
                    WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_FinishingItem.TaxAmount
                    ELSE 0 - Buy_FinishingItem.TaxAmount END) as TaxAmount,
               (CASE
                    WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_FinishingItem.Amount
                    ELSE 0 - Buy_FinishingItem.Amount END) as Amount,
               (CASE
                    WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_FinishingItem.TaxTotal
                    ELSE 0 - Buy_FinishingItem.TaxTotal END) as TaxTotal,
               Buy_FinishingItem.ItemTaxrate,
               Buy_FinishingItem.Remark,
               Buy_FinishingItem.OrderNo,
               Buy_FinishingItem.OrderUid,
               Buy_FinishingItem.OrderItemid,
               Buy_FinishingItem.StateCode,
               Buy_FinishingItem.StateDate,
               Buy_FinishingItem.Inspectid,
               Buy_FinishingItem.InspectUid,
               Buy_FinishingItem.PassedQty,
               (CASE
                    WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货') THEN Buy_FinishingItem.FinishQty
                    ELSE 0 - Buy_FinishingItem.FinishQty END) as FinishQty,
               Buy_FinishingItem.Closed,
               Buy_FinishingItem.RowNum,
               Buy_FinishingItem.InvoQty,
               Buy_FinishingItem.InvoClosed,
               Buy_FinishingItem.VirtualItem,
               Buy_FinishingItem.Customer,
               Buy_FinishingItem.CustPO,
               Buy_FinishingItem.Location,
               Buy_FinishingItem.BatchNo,
               Buy_FinishingItem.MachUid,
               Buy_FinishingItem.MachItemid,
               Buy_FinishingItem.MachBatch,
               Buy_FinishingItem.MachGroupid,
               Buy_FinishingItem.MainPlanUid,
               Buy_FinishingItem.MainPlanItemid,
               Buy_FinishingItem.MrpUid,
               Buy_FinishingItem.MrpItemid,
               Buy_FinishingItem.DeliQty,
               Buy_FinishingItem.DisannulMark,
               Buy_FinishingItem.DisannulListerid,
               Buy_FinishingItem.DisannulLister,
               Buy_FinishingItem.DisannulDate,
               Buy_FinishingItem.AttributeJson,
               Buy_FinishingItem.SourceType,
               Buy_FinishingItem.Custom1,
               Buy_FinishingItem.Custom2,
               Buy_FinishingItem.Custom3,
               Buy_FinishingItem.Custom4,
               Buy_FinishingItem.Custom5,
               Buy_FinishingItem.Custom6,
               Buy_FinishingItem.Custom7,
               Buy_FinishingItem.Custom8,
               Buy_FinishingItem.Custom9,
               Buy_FinishingItem.Custom10,
               Buy_FinishingItem.Custom11,
               Buy_FinishingItem.Custom12,
               Buy_FinishingItem.Custom13,
               Buy_FinishingItem.Custom14,
               Buy_FinishingItem.Custom15,
               Buy_FinishingItem.Custom16,
               Buy_FinishingItem.Custom17,
               Buy_FinishingItem.Custom18,
               Buy_FinishingItem.Tenantid,
               Buy_FinishingItem.Revision,
               Buy_Finishing.RefNo,
               Buy_Finishing.BillType,
               Buy_Finishing.BillTitle,
               Buy_Finishing.BillDate,
               Buy_Finishing.Projectid,
               Buy_Finishing.ProjName,
               Buy_Finishing.ProjCode,
               Buy_Finishing.Operator,
               Buy_Finishing.Arrivaladd,
               Buy_Finishing.TranSport,
               Buy_Finishing.CreateBy,
               Buy_Finishing.CreateDate,
               Buy_Finishing.Lister,
               Buy_Finishing.ModifyDate,
               Buy_Finishing.Assessor,
               Buy_Finishing.AssessDate,
               g2.GoodsName as MrpObjGoodsName,
               g2.GoodsUid as MrpObjGoodsUid
        FROM App_Workgroup
                 RIGHT JOIN Buy_Finishing ON Buy_Finishing.Groupid = App_Workgroup.id
                 RIGHT JOIN Buy_FinishingItem ON Buy_FinishingItem.Pid = Buy_Finishing.id
                 LEFT JOIN Mat_Goods ON Buy_FinishingItem.Goodsid = Mat_Goods.id
                 LEFT JOIN Mat_Goods g2 ON Buy_FinishingItem.MrpObjGoodsid = g2.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyFinishingitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Buy_Finishing.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_Finishing.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Buy_Finishing.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Buy_Finishing.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Buy_Finishing.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Buy_Finishing.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Buy_Finishing.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.arrivaladd != null ">
            and Buy_Finishing.arrivaladd like concat('%', #{SearchPojo.arrivaladd}, '%')
        </if>
        <if test="SearchPojo.transport != null ">
            and Buy_Finishing.transport like concat('%', #{SearchPojo.transport}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Buy_Finishing.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Buy_Finishing.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Buy_Finishing.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Buy_Finishing.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Buy_Finishing.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Buy_Finishing.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Buy_Finishing.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null ">
            and Buy_Finishing.billstatecode like concat('%', #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Buy_Finishing.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Buy_Finishing.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Buy_Finishing.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Buy_Finishing.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Buy_Finishing.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Buy_Finishing.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Buy_Finishing.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Buy_Finishing.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Buy_Finishing.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Buy_Finishing.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Buy_Finishing.deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Buy_Finishing.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Buy_FinishingItem.goodsid= #{SearchPojo.goodsid}
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Buy_FinishingItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Buy_FinishingItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Buy_FinishingItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Buy_FinishingItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Buy_FinishingItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.orderno != null and SearchPojo.orderno != ''">
            and Buy_FinishingItem.orderno like concat('%', #{SearchPojo.orderno}, '%')
        </if>
        <if test="SearchPojo.orderuid != null and SearchPojo.orderuid != ''">
            and Buy_FinishingItem.orderuid like concat('%', #{SearchPojo.orderuid}, '%')
        </if>
        <if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid != ''">
            and Buy_FinishingItem.orderitemid like concat('%', #{SearchPojo.orderitemid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Buy_FinishingItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.inspectid != null and SearchPojo.inspectid != ''">
            and Buy_FinishingItem.inspectid like concat('%', #{SearchPojo.inspectid}, '%')
        </if>
        <if test="SearchPojo.inspectuid != null and SearchPojo.inspectuid != ''">
            and Buy_FinishingItem.inspectuid like concat('%', #{SearchPojo.inspectuid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Buy_FinishingItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Buy_FinishingItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location != ''">
            and Buy_FinishingItem.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Buy_FinishingItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Buy_FinishingItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Buy_FinishingItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Buy_FinishingItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Buy_FinishingItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Buy_FinishingItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Buy_FinishingItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Buy_FinishingItem.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Buy_Finishing.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Buy_Finishing.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Buy_Finishing.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Buy_Finishing.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Buy_Finishing.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.arrivaladd != null ">
                or Buy_Finishing.Arrivaladd like concat('%', #{SearchPojo.arrivaladd}, '%')
            </if>
            <if test="SearchPojo.transport != null ">
                or Buy_Finishing.TranSport like concat('%', #{SearchPojo.transport}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Buy_Finishing.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Buy_Finishing.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Buy_Finishing.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Buy_Finishing.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Buy_Finishing.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Buy_Finishing.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Buy_Finishing.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null ">
                or Buy_Finishing.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Buy_Finishing.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Buy_Finishing.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Buy_Finishing.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Buy_Finishing.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Buy_Finishing.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Buy_Finishing.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Buy_Finishing.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Buy_Finishing.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Buy_Finishing.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Buy_Finishing.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Buy_Finishing.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Buy_Finishing.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Buy_FinishingItem.Goodsid =#{SearchPojo.goodsid}
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Buy_FinishingItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Buy_FinishingItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Buy_FinishingItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Buy_FinishingItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Buy_FinishingItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.orderno != null and SearchPojo.orderno != ''">
                or Buy_FinishingItem.OrderNo like concat('%', #{SearchPojo.orderno}, '%')
            </if>
            <if test="SearchPojo.orderuid != null and SearchPojo.orderuid != ''">
                or Buy_FinishingItem.OrderUid like concat('%', #{SearchPojo.orderuid}, '%')
            </if>
            <if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid != ''">
                or Buy_FinishingItem.OrderItemid like concat('%', #{SearchPojo.orderitemid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Buy_FinishingItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.inspectid != null and SearchPojo.inspectid != ''">
                or Buy_FinishingItem.Inspectid like concat('%', #{SearchPojo.inspectid}, '%')
            </if>
            <if test="SearchPojo.inspectuid != null and SearchPojo.inspectuid != ''">
                or Buy_FinishingItem.InspectUid like concat('%', #{SearchPojo.inspectuid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Buy_FinishingItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Buy_FinishingItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Buy_FinishingItem.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Buy_FinishingItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Buy_FinishingItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Buy_FinishingItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Buy_FinishingItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Buy_FinishingItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Buy_FinishingItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Buy_FinishingItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Buy_FinishingItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyFinishingPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Buy_Finishing.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_Finishing.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Buy_Finishing.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Buy_Finishing.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Buy_Finishing.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Buy_Finishing.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Buy_Finishing.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.arrivaladd != null ">
            and Buy_Finishing.Arrivaladd like concat('%', #{SearchPojo.arrivaladd}, '%')
        </if>
        <if test="SearchPojo.transport != null ">
            and Buy_Finishing.TranSport like concat('%', #{SearchPojo.transport}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Buy_Finishing.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Buy_Finishing.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Buy_Finishing.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Buy_Finishing.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Buy_Finishing.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Buy_Finishing.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Buy_Finishing.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null ">
            and Buy_Finishing.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Buy_Finishing.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Buy_Finishing.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Buy_Finishing.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Buy_Finishing.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Buy_Finishing.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Buy_Finishing.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Buy_Finishing.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Buy_Finishing.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Buy_Finishing.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Buy_Finishing.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Buy_Finishing.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Buy_Finishing.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Buy_Finishing.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Buy_Finishing.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Buy_Finishing.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Buy_Finishing.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Buy_Finishing.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.arrivaladd != null ">
                or Buy_Finishing.Arrivaladd like concat('%', #{SearchPojo.arrivaladd}, '%')
            </if>
            <if test="SearchPojo.transport != null ">
                or Buy_Finishing.TranSport like concat('%', #{SearchPojo.transport}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Buy_Finishing.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Buy_Finishing.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Buy_Finishing.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Buy_Finishing.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Buy_Finishing.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Buy_Finishing.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Buy_Finishing.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null ">
                or Buy_Finishing.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Buy_Finishing.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Buy_Finishing.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Buy_Finishing.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Buy_Finishing.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Buy_Finishing.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Buy_Finishing.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Buy_Finishing.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Buy_Finishing.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Buy_Finishing.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Buy_Finishing.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Buy_Finishing.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Buy_Finishing.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Buy_Finishing(id, RefNo, BillType, BillTitle, BillDate, Projectid, ProjCode, ProjName, Groupid, Operator, Arrivaladd, TranSport,
                                  Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor,
                                  Assessorid, AssessDate, BillStateCode, BillStateDate, BillTaxAmount, BillAmount,
                                  BillTaxTotal, BillPaid, ItemCount, FinishCount, DisannulCount, InvoCount, PrintCount,OaFlowMark,
                                  Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9,
                                  Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{groupid}, #{operator}, #{arrivaladd},
                #{transport}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{billstatecode}, #{billstatedate},
                #{billtaxamount}, #{billamount}, #{billtaxtotal}, #{billpaid}, #{itemcount}, #{finishcount},
                #{disannulcount}, #{invocount}, #{printcount},#{oaflowmark}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid},
                #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_Finishing
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="arrivaladd != null ">
                Arrivaladd =#{arrivaladd},
            </if>
            <if test="transport != null ">
                TranSport =#{transport},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="billstatecode != null ">
                BillStateCode =#{billstatecode},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="billpaid != null">
                BillPaid =#{billpaid},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="invocount != null">
                InvoCount =#{invocount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_Finishing
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Buy_Finishing
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--刷新打印次数-->
    <update id="updatePrintcount">
        update Buy_Finishing
        SET PrintCount = #{printcount}
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <!--    刷新发货完成数 Eric 20220722-->
    <update id="updateDisannulCountAndAmount">
        update Buy_Finishing
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Buy_FinishingItem
                                     where Buy_FinishingItem.Pid = #{key}
                                       and Buy_FinishingItem.Tenantid = #{tid}
                                       and Buy_FinishingItem.DisannulMark = 1), 0),
            BillTaxAmount = COALESCE((SELECT Sum(TaxAmount)
                                      FROM Buy_FinishingItem
                                      where Buy_FinishingItem.Pid = #{key}
                                        and Buy_FinishingItem.Tenantid = #{tid}
                                        and Buy_FinishingItem.DisannulMark = 0), 0),
            BillAmount    =COALESCE((SELECT Sum(Amount)
                                     FROM Buy_FinishingItem
                                     where Buy_FinishingItem.Pid = #{key}
                                       and Buy_FinishingItem.Tenantid = #{tid}
                                       and Buy_FinishingItem.DisannulMark = 0), 0),
            BillTaxTotal=COALESCE((SELECT Sum(TaxTotal)
                                   FROM Buy_FinishingItem
                                   where Buy_FinishingItem.Pid = #{key}
                                     and Buy_FinishingItem.Tenantid = #{tid}
                                     and Buy_FinishingItem.DisannulMark = 0), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成行数 Eric ********-->
    <update id="updateFinishCount">
        update Buy_Finishing
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Buy_FinishingItem
                                   where Buy_FinishingItem.Pid =#{key}
                                     and Buy_FinishingItem.Tenantid = #{tid}
                                     and (Buy_FinishingItem.FinishQty >= Buy_FinishingItem.Quantity or
                                          Buy_FinishingItem.Closed = 1)), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BuyFinishingPojo">
        select
        id
        from Buy_FinishingItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <!--    刷新发货完成数 Eric ********-->
    <update id="updateOrderFinishQty">
        update Buy_OrderItem
        SET FinishQty =COALESCE((SELECT sum(CASE
                                                WHEN Buy_Finishing.BillType IN ('采购验收')
                                                    THEN Buy_FinishingItem.quantity
                                                ELSE 0 - Buy_FinishingItem.quantity END)
                                 FROM Buy_FinishingItem
                                          LEFT OUTER JOIN Buy_Finishing
                                                          ON Buy_FinishingItem.pid = Buy_Finishing.id
                                 where Buy_Finishing.BillType IN ('采购验收', '采购退货')
                                   and Buy_FinishingItem.OrderUid = #{refno}
                                   and Buy_FinishingItem.OrderItemid = #{key}
                                   and Buy_FinishingItem.DisannulMark = 0
                                   and Buy_FinishingItem.Tenantid = #{tid}), 0)

        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric ********-->
    <update id="updateOrderFinishCount">
        update Buy_Order
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Buy_OrderItem
                                   where Buy_OrderItem.Pid = (SELECT Pid FROM Buy_OrderItem where id = #{key})
                                     and Buy_OrderItem.Tenantid = #{tid}
                                     and (Buy_OrderItem.Closed = 1
                                       or Buy_OrderItem.FinishQty >= Buy_OrderItem.Quantity)), 0)
        where id = (SELECT Pid FROM Buy_OrderItem where id = #{key})
          and Tenantid = #{tid}
    </update>

    <!--  查询往来单位是否被引用  -->
    <select id="getItemCiteBillName" resultType="string">
        (SELECT '出入库单' as billname
         From Mat_AccessItem
                JOIN Mat_Access ON Mat_AccessItem.pid =  Mat_Access.id
         where Mat_AccessItem.CiteItemid = #{key} AND Mat_Access.ReturnUid ='' AND Mat_Access.OrgUid =''
           and Mat_Access.Tenantid = #{tid} LIMIT 1)
        UNION ALL
        (SELECT '采购开票' as billname From Buy_InvoiceItem where FinishItemid= #{key} and Tenantid=#{tid} LIMIT 1)
        UNION ALL
        (SELECT '采购账单' as billname From Buy_AccountItem where Billid=#{pid} and Tenantid=#{tid} LIMIT 1)
    </select>

    <!--    刷新收货待入数 Eric ********-->
    <update id="updateGoodsBuyRemQty">
        update Mat_Goods
        SET BuyRemQty = COALESCE((SELECT SUM(Buy_OrderItem.Quantity - Buy_OrderItem.FinishQty)
                                  FROM Buy_OrderItem
                                           LEFT OUTER JOIN Buy_Order ON Buy_OrderItem.pid =
                                                                            Buy_Order.id
                                  where Buy_OrderItem.Goodsid = #{key}
                                    and Buy_OrderItem.Quantity &gt; Buy_OrderItem.FinishQty
                                    and Buy_OrderItem.Closed = 0
                                    and Buy_OrderItem.DisannulMark = 0
                                    and Buy_Order.Tenantid = #{tid}), 0) +
                        COALESCE((SELECT SUM(CASE
                                                 WHEN Buy_Finishing.BillType IN ('采购验收', '其他收货')
                                                     THEN Buy_FinishingItem.Quantity - Buy_FinishingItem.FinishQty
                                                 ELSE Buy_FinishingItem.FinishQty - Buy_FinishingItem.Quantity END)
                                  FROM Buy_FinishingItem
                                           LEFT OUTER JOIN Buy_Finishing ON Buy_FinishingItem.pid =
                                                                          Buy_Finishing.id
                                  where Buy_Finishing.BillType IN ('采购验收', '其他收货', '采购退货', '其他退货')
                                    and Buy_FinishingItem.Goodsid = #{key}
                                    and Buy_FinishingItem.Quantity &gt; Buy_FinishingItem.FinishQty
                                    and Buy_FinishingItem.Closed = 0
                                    and Buy_FinishingItem.DisannulMark = 0
                                    and Buy_Finishing.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <select id="getPlanitemidsByOrderitemids" resultType="java.lang.String">
        SELECT DISTINCT Buy_OrderItem.CiteItemid
        FROM Buy_OrderItem
        join Buy_Order on Buy_OrderItem.pid = Buy_Order.id
        WHERE Buy_OrderItem.id IN
        <foreach collection="orderitemidLst" item="orderitemid" open="(" close=")" separator=",">
            #{orderitemid}
        </foreach>
        AND Buy_Order.BillType = '采购计划'
        AND Buy_OrderItem.CiteItemid != ''
        AND Buy_OrderItem.Tenantid = #{tid}
    </select>
</mapper>

