<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.D04MBIR1Mapper">
    <select id="getSumAmtByGoodsMax" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatInventoryPojo">
        SELECT Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               sum(Mat_Inventory.Amount)   AS Amount,
               sum(Mat_Inventory.Quantity) AS Quantity
        FROM Mat_Inventory
                 LEFT OUTER JOIN Mat_Goods
                                 ON Mat_Inventory.Goodsid = Mat_Goods.id
        WHERE Mat_Inventory.Quantity  <![CDATA[!=]]> 0
          AND Mat_Inventory.Tenantid = #{tenantid}
        group by Mat_Goods.GoodsUid, Mat_Goods.GoodsName, Mat_Goods.GoodsSpec, Mat_Goods.GoodsUnit
        ORDER BY Amount desc
    </select>

    <select id="getSumAmtQtyByStore" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT Mat_Storage.StoreName AS name,
               sum(Mat_Inventory.Amount) AS value,
               sum(Mat_Inventory.Quantity) AS valueb,
	Count(0) AS valuec
        FROM Mat_Inventory
            LEFT JOIN Mat_Storage
        ON Mat_Inventory.Storeid = Mat_Storage.id
        WHERE Mat_Inventory.Quantity  <![CDATA[!=]]> 0
          AND Mat_Inventory.Tenantid=#{tenantid}
        GROUP BY Mat_Storage.StoreName
    </select>

<!--    出入库趋势图月-->
    <select id="getSumQtyByMonth" resultType="java.util.Map" parameterType="inks.common.core.domain.QueryParam">
        SELECT DATE_FORMAT(Mat_Access.BillDate, '%Y-%m') AS BillMonth,
               SUM(CASE
                       WHEN Mat_Access.BillType IN ('收货入库', '购退红冲', '生产入库', '加工入库', '其他入库')
                           THEN Mat_AccessItem.Quantity
                       WHEN Mat_Access.BillType IN ('收货红冲', '购退出库', '生产红冲', '加工红冲', '他入红冲')
                           THEN 0 - Mat_AccessItem.Quantity
                   END)                                  as SumInQty,
               SUM(CASE
                       WHEN Mat_Access.BillType IN ('收货入库', '购退红冲', '生产入库', '加工入库', '其他入库')
                           THEN Mat_AccessItem.Amount
                       WHEN Mat_Access.BillType IN ('收货红冲', '购退出库', '生产红冲', '加工红冲', '他入红冲')
                           THEN 0 - Mat_AccessItem.Amount
                   END)                                  as SumInAmount,
               SUM(CASE
                       WHEN Mat_Access.BillType IN ('发货出库', '客退红冲', '领料出库', '退料红冲',
                                                    '其他出库', '报废出库', '盘盈红冲', '盘亏出库')
                           THEN Mat_AccessItem.Quantity
                       WHEN Mat_Access.BillType IN ('发货红冲', '客退入库', '领料红冲', '退料入库',
                                                    '他出红冲', '报废红冲', '盘盈入库', '盘亏红冲')
                           THEN 0 - Mat_AccessItem.Quantity
                   END)                                  as SumOutQty,
               SUM(CASE
                       WHEN Mat_Access.BillType IN ('发货出库', '客退红冲', '领料出库', '退料红冲',
                                                    '其他出库', '报废出库', '盘盈红冲', '盘亏出库')
                           THEN Mat_AccessItem.Amount
                       WHEN Mat_Access.BillType IN ('发货红冲', '客退入库', '领料红冲', '退料入库',
                                                    '他出红冲', '报废红冲', '盘盈入库', '盘亏红冲')
                           THEN 0 - Mat_AccessItem.Amount
                   END)                                  as SumOutAmount
        FROM Mat_Access
                 RIGHT JOIN Mat_AccessItem ON Mat_Access.id = Mat_AccessItem.Pid
                 LEFT JOIN Mat_Goods ON Mat_AccessItem.Goodsid = Mat_Goods.id
        where Mat_AccessItem.Tenantid = #{tenantid}
          and  DATE_FORMAT(Mat_Access.BillDate, '%Y-%m') BETWEEN DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 11 MONTH), '%Y-%m') AND DATE_FORMAT(NOW(), '%Y-%m')
        GROUP BY BillMonth
        ORDER BY BillMonth
    </select>



    <select id="getSumOutQtyOneMonth" resultType="java.util.Map" parameterType="inks.common.core.domain.QueryParam">
        SELECT DATE_FORMAT(Mat_Access.BillDate, '%Y-%m')                                               AS billmonth,
               SUM(CASE
                       WHEN Mat_Access.BillType = '发货出库' THEN Mat_AccessItem.Quantity
                       ELSE 0 END)                                                                     AS 发货出库,
               SUM(CASE
                       WHEN Mat_Access.BillType = '购退出库' THEN Mat_AccessItem.Quantity
                       ELSE 0 END)                                                                     AS 购退出库,
               SUM(CASE
                       WHEN Mat_Access.BillType = '领料出库' THEN Mat_AccessItem.Quantity
                       ELSE 0 END)                                                                     AS 领料出库,
               SUM(CASE
                       WHEN Mat_Access.BillType = '其他出库' THEN Mat_AccessItem.Quantity
                       ELSE 0 END)                                                                     AS 其他出库,
               SUM(CASE
                       WHEN Mat_Access.BillType = '报废出库' THEN Mat_AccessItem.Quantity
                       ELSE 0 END)                                                                     AS 报废出库,
               SUM(CASE
                       WHEN Mat_Access.BillType = '盘亏出库' THEN Mat_AccessItem.Quantity
                       ELSE 0 END)                                                                     AS 盘亏出库
        FROM Mat_Access
                 RIGHT JOIN Mat_AccessItem ON Mat_Access.id = Mat_AccessItem.Pid
                 LEFT JOIN Mat_Goods ON Mat_AccessItem.Goodsid = Mat_Goods.id
        where Mat_Access.Tenantid = #{tenantid}
          and (Mat_Access.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        GROUP BY BillMonth
        ORDER BY BillMonth
    </select>




    <select id="getSumInQtyOneMonth" resultType="java.util.Map" parameterType="inks.common.core.domain.QueryParam">
        SELECT DATE_FORMAT(Mat_Access.BillDate, '%Y-%m')                                               AS billmonth,
               SUM(CASE
                       WHEN Mat_Access.BillType = '收货入库' THEN Mat_AccessItem.Quantity
                       ELSE 0 END)                                                                     AS 收货入库,
               SUM(CASE
                       WHEN Mat_Access.BillType = '客退入库' THEN Mat_AccessItem.Quantity
                       ELSE 0 END)                                                                     as 客退入库,
               SUM(CASE
                       WHEN Mat_Access.BillType = '退料入库' THEN Mat_AccessItem.Quantity
                       ELSE 0 END)                                                                     AS 退料入库,
               SUM(CASE
                       WHEN Mat_Access.BillType = '生产入库' THEN Mat_AccessItem.Quantity
                       ELSE 0 END)                                                                     AS 生产入库,
               SUM(CASE
                       WHEN Mat_Access.BillType = '加工入库' THEN Mat_AccessItem.Quantity
                       ELSE 0 END)                                                                     AS 加工入库,
               SUM(CASE WHEN Mat_Access.BillType = '其他入库' THEN Mat_AccessItem.Quantity ELSE 0 END) AS 其他入库,
               SUM(CASE
                       WHEN Mat_Access.BillType = '盘盈入库' THEN Mat_AccessItem.Quantity
                       ELSE 0 END)                                                                     AS 盘盈入库
        FROM Mat_Access
                 RIGHT JOIN Mat_AccessItem ON Mat_Access.id = Mat_AccessItem.Pid
                 LEFT JOIN Mat_Goods ON Mat_AccessItem.Goodsid = Mat_Goods.id
        where Mat_Access.Tenantid = #{tenantid}
          and (Mat_Access.BillDate BETWEEN #{DateRange.StartDate}
            and #{DateRange.EndDate})
        GROUP BY BillMonth
        ORDER BY BillMonth
    </select>

    <select id="getWorkshopProdValueByDay" resultType="java.util.Map">
        select DATE(Mat_Access.BillDate)                                              as date,
               COALESCE(sum(Bus_MachiningItem.Price * Mat_AccessItem.Quantity), 0)    as value,
               COALESCE(sum(Bus_MachiningItem.TaxPrice * Mat_AccessItem.Quantity), 0) as valueb
        from Mat_Access
                 Right Join Mat_AccessItem on Mat_AccessItem.Pid = Mat_Access.id
                 Left Join Bus_MachiningItem on Bus_MachiningItem.id = Mat_AccessItem.MachItemid
        where Mat_Access.Tenantid = #{tenantid}
          and Mat_Access.BillType = '生产入库'
          and Mat_Access.BillDate between #{startDate} and #{endDate}
          and Mat_Access.Groupid = #{workshopid}
        group by DATE(Mat_Access.BillDate)
        order by date desc
    </select>

    <select id="getAllWorkshop" resultType="java.util.Map">
        select id,GroupUid,GroupName from App_Workgroup where Tenantid = #{tenantid} and GroupType = '生产车间'
    </select>

    <select id="getWorkshopProdValueByMonth" resultType="java.util.Map">
        select DATE_FORMAT(Mat_Access.BillDate, '%Y-%m')                              as date,
               COALESCE(sum(Bus_MachiningItem.Price * Mat_AccessItem.Quantity), 0)    as value,
               COALESCE(sum(Bus_MachiningItem.TaxPrice * Mat_AccessItem.Quantity), 0) as valueb
        from Mat_Access
                 Right Join Mat_AccessItem on Mat_AccessItem.Pid = Mat_Access.id
                 Left Join Bus_MachiningItem on Bus_MachiningItem.id = Mat_AccessItem.MachItemid
        where Mat_Access.Tenantid = #{tenantid}
          and Mat_Access.BillType = '生产入库'
          and YEAR(Mat_Access.BillDate) = YEAR(CURDATE()) -- 过滤当前年份的数据
          and Mat_Access.Groupid = #{workshopid}
        group by DATE_FORMAT(Mat_Access.BillDate, '%Y-%m')
        order by date desc
    </select>

    <select id="getWorkshopProdValueIn12Month" resultType="java.util.Map">
        select
        App_Workgroup.id as Groupid,
        App_Workgroup.GroupName,
        COALESCE(SUM(IF(MONTH(Mat_Access.BillDate) = 1, Bus_MachiningItem.Price * Mat_AccessItem.Quantity, 0)), 0) as `1`,
        COALESCE(SUM(IF(MONTH(Mat_Access.BillDate) = 2, Bus_MachiningItem.Price * Mat_AccessItem.Quantity, 0)), 0) as `2`,
        COALESCE(SUM(IF(MONTH(Mat_Access.BillDate) = 3, Bus_MachiningItem.Price * Mat_AccessItem.Quantity, 0)), 0) as `3`,
        COALESCE(SUM(IF(MONTH(Mat_Access.BillDate) = 4, Bus_MachiningItem.Price * Mat_AccessItem.Quantity, 0)), 0) as `4`,
        COALESCE(SUM(IF(MONTH(Mat_Access.BillDate) = 5, Bus_MachiningItem.Price * Mat_AccessItem.Quantity, 0)), 0) as `5`,
        COALESCE(SUM(IF(MONTH(Mat_Access.BillDate) = 6, Bus_MachiningItem.Price * Mat_AccessItem.Quantity, 0)), 0) as `6`,
        COALESCE(SUM(IF(MONTH(Mat_Access.BillDate) = 7, Bus_MachiningItem.Price * Mat_AccessItem.Quantity, 0)), 0) as `7`,
        COALESCE(SUM(IF(MONTH(Mat_Access.BillDate) = 8, Bus_MachiningItem.Price * Mat_AccessItem.Quantity, 0)), 0) as `8`,
        COALESCE(SUM(IF(MONTH(Mat_Access.BillDate) = 9, Bus_MachiningItem.Price * Mat_AccessItem.Quantity, 0)), 0) as `9`,
        COALESCE(SUM(IF(MONTH(Mat_Access.BillDate) = 10, Bus_MachiningItem.Price * Mat_AccessItem.Quantity, 0)), 0) as `10`,
        COALESCE(SUM(IF(MONTH(Mat_Access.BillDate) = 11, Bus_MachiningItem.Price * Mat_AccessItem.Quantity, 0)), 0) as `11`,
        COALESCE(SUM(IF(MONTH(Mat_Access.BillDate) = 12, Bus_MachiningItem.Price * Mat_AccessItem.Quantity, 0)), 0) as `12`
        from App_Workgroup
        LEFT JOIN Mat_Access ON Mat_Access.Groupid = App_Workgroup.id
        LEFT JOIN Mat_AccessItem ON Mat_AccessItem.Pid = Mat_Access.id
        LEFT JOIN Bus_MachiningItem ON Bus_MachiningItem.id = Mat_AccessItem.MachItemid
        where Mat_Access.Tenantid = #{tenantid}
        and Mat_Access.BillType = '生产入库'
        and YEAR(Mat_Access.BillDate) = #{year}
        group by App_Workgroup.id, App_Workgroup.GroupName
        order by App_Workgroup.GroupName
    </select>


    <select id="getWorkshopReqFinishCostIn12Month" resultType="java.util.Map">
        select
        Mat_Requisition.Groupid,
        App_Workgroup.GroupName,
        SUM(IF(MONTH(Mat_Requisition.BillDate) = 1, Mat_RequisitionItem.FinishCost, 0)) as `1`,
        SUM(IF(MONTH(Mat_Requisition.BillDate) = 2, Mat_RequisitionItem.FinishCost, 0)) as `2`,
        SUM(IF(MONTH(Mat_Requisition.BillDate) = 3, Mat_RequisitionItem.FinishCost, 0)) as `3`,
        SUM(IF(MONTH(Mat_Requisition.BillDate) = 4, Mat_RequisitionItem.FinishCost, 0)) as `4`,
        SUM(IF(MONTH(Mat_Requisition.BillDate) = 5, Mat_RequisitionItem.FinishCost, 0)) as `5`,
        SUM(IF(MONTH(Mat_Requisition.BillDate) = 6, Mat_RequisitionItem.FinishCost, 0)) as `6`,
        SUM(IF(MONTH(Mat_Requisition.BillDate) = 7, Mat_RequisitionItem.FinishCost, 0)) as `7`,
        SUM(IF(MONTH(Mat_Requisition.BillDate) = 8, Mat_RequisitionItem.FinishCost, 0)) as `8`,
        SUM(IF(MONTH(Mat_Requisition.BillDate) = 9, Mat_RequisitionItem.FinishCost, 0)) as `9`,
        SUM(IF(MONTH(Mat_Requisition.BillDate) = 10, Mat_RequisitionItem.FinishCost, 0)) as `10`,
        SUM(IF(MONTH(Mat_Requisition.BillDate) = 11, Mat_RequisitionItem.FinishCost, 0)) as `11`,
        SUM(IF(MONTH(Mat_Requisition.BillDate) = 12, Mat_RequisitionItem.FinishCost, 0)) as `12`
        from Mat_Requisition
        Right Join Mat_RequisitionItem on Mat_RequisitionItem.Pid = Mat_Requisition.id
        Left Join App_Workgroup on Mat_Requisition.Groupid = App_Workgroup.id
        where Mat_RequisitionItem.Tenantid = #{tenantid}
        and YEAR(Mat_Requisition.BillDate) = #{year}
        group by Mat_Requisition.Groupid, App_Workgroup.GroupName
        order by App_Workgroup.GroupName
    </select>

    <select id="getWorkshopFmIn12Month" resultType="java.util.Map">
        WITH MonthlyData AS (SELECT Groupid,
        MONTH(BillDate) AS Month,
        -SUM(Amount)    AS Income,
        0               AS Cost
        FROM Fm_Income
        WHERE YEAR(BillDate) = #{year}
        and Tenantid = #{tenantid}
        GROUP BY Groupid, MONTH(BillDate)

        UNION ALL

        SELECT Groupid,
        MONTH(BillDate) AS Month,
        0               AS Income,
        SUM(Amount)     AS Cost
        FROM Fm_Cost
        WHERE YEAR(BillDate) = #{year}
        and Tenantid = #{tenantid}
        GROUP BY Groupid, MONTH(BillDate))
        SELECT md.Groupid,
        aw.GroupName,
        SUM(IF(Month = 1, Income, 0))         AS 1in,
        SUM(IF(Month = 1, Cost, 0))           AS 1out,
        SUM(IF(Month = 1, Income + Cost, 0))  AS 1all,
        SUM(IF(Month = 2, Income, 0))         AS 2in,
        SUM(IF(Month = 2, Cost, 0))           AS 2out,
        SUM(IF(Month = 2, Income + Cost, 0))  AS 2all,
        SUM(IF(Month = 3, Income, 0))         AS 3in,
        SUM(IF(Month = 3, Cost, 0))           AS 3out,
        SUM(IF(Month = 3, Income + Cost, 0))  AS 3all,
        SUM(IF(Month = 4, Income, 0))         AS 4in,
        SUM(IF(Month = 4, Cost, 0))           AS 4out,
        SUM(IF(Month = 4, Income + Cost, 0))  AS 4all,
        SUM(IF(Month = 5, Income, 0))         AS 5in,
        SUM(IF(Month = 5, Cost, 0))           AS 5out,
        SUM(IF(Month = 5, Income + Cost, 0))  AS 5all,
        SUM(IF(Month = 6, Income, 0))         AS 6in,
        SUM(IF(Month = 6, Cost, 0))           AS 6out,
        SUM(IF(Month = 6, Income + Cost, 0))  AS 6all,
        SUM(IF(Month = 7, Income, 0))         AS 7in,
        SUM(IF(Month = 7, Cost, 0))           AS 7out,
        SUM(IF(Month = 7, Income + Cost, 0))  AS 7all,
        SUM(IF(Month = 8, Income, 0))         AS 8in,
        SUM(IF(Month = 8, Cost, 0))           AS 8out,
        SUM(IF(Month = 8, Income + Cost, 0))  AS 8all,
        SUM(IF(Month = 9, Income, 0))         AS 9in,
        SUM(IF(Month = 9, Cost, 0))           AS 9out,
        SUM(IF(Month = 9, Income + Cost, 0))  AS 9all,
        SUM(IF(Month = 10, Income, 0))        AS 10in,
        SUM(IF(Month = 10, Cost, 0))          AS 10out,
        SUM(IF(Month = 10, Income + Cost, 0)) AS 10all,
        SUM(IF(Month = 11, Income, 0))        AS 11in,
        SUM(IF(Month = 11, Cost, 0))          AS 11out,
        SUM(IF(Month = 11, Income + Cost, 0)) AS 11all,
        SUM(IF(Month = 12, Income, 0))        AS 12in,
        SUM(IF(Month = 12, Cost, 0))          AS 12out,
        SUM(IF(Month = 12, Income + Cost, 0)) AS 12all
        FROM MonthlyData md
        LEFT JOIN App_Workgroup aw ON md.Groupid = aw.id
        GROUP BY md.Groupid, aw.GroupName
        ORDER BY aw.GroupName
    </select>
</mapper>
