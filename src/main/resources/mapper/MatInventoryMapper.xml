<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.MatInventoryMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.MatInventoryPojo">
        SELECT Mat_Inventory.id,
               Mat_Inventory.Storeid,
               Mat_Inventory.Goodsid,
               Mat_Inventory.Quantity,
               Mat_Inventory.Amount,
               Mat_Inventory.EndUid,
               Mat_Inventory.EndInUid,
               Mat_Inventory.EndInDate,
               Mat_Inventory.EndOutUid,
               Mat_Inventory.EndOutDate,
               Mat_Inventory.BatchNo,
               Mat_Inventory.Location,
               Mat_Inventory.PackSn,
               Mat_Inventory.Skuid,
               Mat_Inventory.Attribute<PERSON><PERSON>,
               Mat_Inventory.ExpiDate,
               Mat_Inventory.CreateBy,
               Mat_Inventory.CreateByid,
               Mat_Inventory.CreateDate,
               Mat_Inventory.Lister,
               Mat_Inventory.Listerid,
               Mat_Inventory.ModifyDate,
               Mat_Inventory.Custom1,
               Mat_Inventory.Custom2,
               Mat_Inventory.Custom3,
               Mat_Inventory.Custom4,
               Mat_Inventory.Custom5,
               Mat_Inventory.Custom6,
               Mat_Inventory.Custom7,
               Mat_Inventory.Custom8,
               Mat_Inventory.Custom9,
               Mat_Inventory.Custom10,
               Mat_Inventory.Tenantid,
               Mat_Inventory.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.IvQuantity,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Storage.StoreCode,
               Mat_Storage.StoreName
        FROM Mat_Goods
                 RIGHT JOIN Mat_Inventory ON Mat_Goods.id = Mat_Inventory.Goodsid
                 LEFT JOIN Mat_Storage ON Mat_Storage.id = Mat_Inventory.Storeid

        where Mat_Inventory.id = #{key}
          and Mat_Inventory.Tenantid = #{tid}
    </select>
    <sql id="selectMatInventoryVo">
        SELECT Mat_Inventory.id,
               Mat_Inventory.Storeid,
               Mat_Inventory.Goodsid,
               Mat_Inventory.Quantity,
               Mat_Inventory.Amount,
               Mat_Inventory.EndUid,
               Mat_Inventory.EndInUid,
               Mat_Inventory.EndInDate,
               Mat_Inventory.EndOutUid,
               Mat_Inventory.EndOutDate,
               Mat_Inventory.BatchNo,
               Mat_Inventory.Location,
               Mat_Inventory.PackSn,
               Mat_Inventory.Skuid,
               Mat_Inventory.AttributeJson,
               Mat_Inventory.ExpiDate,
               Mat_Inventory.CreateBy,
               Mat_Inventory.CreateByid,
               Mat_Inventory.CreateDate,
               Mat_Inventory.Lister,
               Mat_Inventory.Listerid,
               Mat_Inventory.ModifyDate,
               Mat_Inventory.Custom1,
               Mat_Inventory.Custom2,
               Mat_Inventory.Custom3,
               Mat_Inventory.Custom4,
               Mat_Inventory.Custom5,
               Mat_Inventory.Custom6,
               Mat_Inventory.Custom7,
               Mat_Inventory.Custom8,
               Mat_Inventory.Custom9,
               Mat_Inventory.Custom10,
               Mat_Inventory.Tenantid,
               Mat_Inventory.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.IvQuantity,
               Mat_Storage.StoreCode,
               Mat_Storage.StoreName
        FROM Mat_Goods
                 RIGHT JOIN Mat_Inventory ON Mat_Goods.id = Mat_Inventory.Goodsid
                 LEFT JOIN Mat_Storage ON Mat_Storage.id = Mat_Inventory.Storeid

    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatInventoryPojo">
        <include refid="selectMatInventoryVo"/>
        where 1 = 1 and Mat_Inventory.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Inventory.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.storeid != null and SearchPojo.storeid  != ''">
            and Mat_Inventory.Storeid like concat('%', #{SearchPojo.storeid}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid  != ''">
            and Mat_Inventory.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.enduid != null and SearchPojo.enduid  != ''">
            and Mat_Inventory.EndUid like concat('%', #{SearchPojo.enduid}, '%')
        </if>
        <if test="SearchPojo.endinuid != null and SearchPojo.endinuid  != ''">
            and Mat_Inventory.EndInUid like concat('%', #{SearchPojo.endinuid}, '%')
        </if>
        <if test="SearchPojo.endoutuid != null and SearchPojo.endoutuid  != ''">
            and Mat_Inventory.EndOutUid like concat('%', #{SearchPojo.endoutuid}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno  != ''">
            and Mat_Inventory.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.location != null and SearchPojo.location  != ''">
            and Mat_Inventory.Location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
            and Mat_Inventory.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and Mat_Inventory.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1  != ''">
            and Mat_Inventory.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2  != ''">
            and Mat_Inventory.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3  != ''">
            and Mat_Inventory.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4  != ''">
            and Mat_Inventory.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5  != ''">
            and Mat_Inventory.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6  != ''">
            and Mat_Inventory.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7  != ''">
            and Mat_Inventory.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8  != ''">
            and Mat_Inventory.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9  != ''">
            and Mat_Inventory.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10  != ''">
            and Mat_Inventory.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.packsn != null and SearchPojo.packsn  != ''">
            and Mat_Inventory.PackSn=#{SearchPojo.packsn}
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid  != ''">
            and Mat_Inventory.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid  != ''">
            and Mat_Inventory.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.storecode != null and SearchPojo.storecode  != ''">
            and Mat_Storage.StoreCode like concat('%', #{SearchPojo.storecode}, '%')
        </if>
        <if test="SearchPojo.storename != null and SearchPojo.storename  != ''">
            and Mat_Storage.StoreName like concat('%', #{SearchPojo.storename}, '%')
        </if>
        <if test="SearchPojo.uidgroupguid != null and SearchPojo.uidgroupguid  != ''">
            and Mat_Goods.UidGroupGuid =#{SearchPojo.uidgroupguid}
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
        <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
            and Mat_Inventory.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">

            <if test="SearchPojo.storeid != null and SearchPojo.storeid != ''">
                or Mat_Inventory.Storeid like concat('%', #{SearchPojo.storeid}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Mat_Inventory.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.enduid != null and SearchPojo.enduid != ''">
                or Mat_Inventory.EndUid like concat('%', #{SearchPojo.enduid}, '%')
            </if>
            <if test="SearchPojo.endinuid != null and SearchPojo.endinuid != ''">
                or Mat_Inventory.EndInUid like concat('%', #{SearchPojo.endinuid}, '%')
            </if>
            <if test="SearchPojo.endoutuid != null and SearchPojo.endoutuid != ''">
                or Mat_Inventory.EndOutUid like concat('%', #{SearchPojo.endoutuid}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Mat_Inventory.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.location != null and SearchPojo.location != ''">
                or Mat_Inventory.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Mat_Inventory.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Mat_Inventory.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Mat_Inventory.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Mat_Inventory.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Mat_Inventory.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Mat_Inventory.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Mat_Inventory.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Mat_Inventory.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Mat_Inventory.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Mat_Inventory.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Mat_Inventory.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Mat_Inventory.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.packsn != null and SearchPojo.packsn != ''">
                or Mat_Inventory.PackSn = #{SearchPojo.packsn}
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Mat_Inventory.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Mat_Inventory.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.storecode != null and SearchPojo.storecode != ''">
                or Mat_Storage.StoreCode like concat('%', #{SearchPojo.storecode}, '%')
            </if>
            <if test="SearchPojo.storename != null and SearchPojo.storename != ''">
                or Mat_Storage.StoreName like concat('%', #{SearchPojo.storename}, '%')
            </if>
            <if test="SearchPojo.uidgroupguid != null and SearchPojo.uidgroupguid != ''">
                or Mat_Goods.UidGroupGuid =#{SearchPojo.uidgroupguid}
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
            <if test="SearchPojo.attributejson != null and SearchPojo.attributejson != ''">
                or Mat_Inventory.attributejson like concat('%', #{SearchPojo.attributejson}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Mat_Inventory(id, Storeid, Goodsid, Quantity, Amount, EndUid, EndInUid, EndInDate, EndOutUid,
                                  EndOutDate, BatchNo, Location, CreateBy, CreateDate, Lister, ModifyDate, Custom1,
                                  Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10,
                                  Tenantid, Revision, Skuid, AttributeJson, PackSn, ExpiDate, CreateByid, Listerid)
        values (#{id}, #{storeid}, #{goodsid}, #{quantity}, #{amount}, #{enduid}, #{endinuid}, #{endindate},
                #{endoutuid}, #{endoutdate}, #{batchno}, #{location}, #{createby}, #{createdate}, #{lister},
                #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7},
                #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision}, #{skuid}, #{attributejson}, #{packsn},
                #{expidate},
                #{createbyid},
                #{listerid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Mat_Inventory
        <set>
            <if test="storeid != null ">
                Storeid =#{storeid},
            </if>
            <if test="goodsid != null ">
                Goodsid =#{goodsid},
            </if>
            <if test="quantity != null">
                Quantity =#{quantity},
            </if>
            <if test="amount != null">
                Amount =#{amount},
            </if>
            <if test="enduid != null ">
                EndUid =#{enduid},
            </if>
            <if test="endinuid != null ">
                EndInUid =#{endinuid},
            </if>
            <if test="endindate != null">
                EndInDate =#{endindate},
            </if>
            <if test="endoutuid != null ">
                EndOutUid =#{endoutuid},
            </if>
            <if test="endoutdate != null">
                EndOutDate =#{endoutdate},
            </if>
            <if test="batchno != null ">
                BatchNo =#{batchno},
            </if>
            <if test="location != null ">
                Location =#{location},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="packsn != null ">
                PackSn =#{packsn},
            </if>
            <if test="skuid != null ">
                Skuid =#{skuid},
            </if>
            <if test="attributejson != null ">
                AttributeJson =#{attributejson},
            </if>
            <if test="expidate != null">
                ExpiDate =#{expidate},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Mat_Inventory
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateGoodsIvQty">
        update Mat_Goods
        SET IvQuantity =COALESCE((SELECT SUM(Mat_Inventory.quantity)
                                  FROM Mat_Inventory
                                  where Mat_Inventory.Goodsid = #{key}
                                    and Mat_Inventory.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--  根据货品名称规格外部编码获取实例  -->
    <select id="getEntityBySn" resultType="inks.service.sa.som.domain.pojo.MatInventoryPojo">
        select *
        from Mat_Inventory
        WHERE Tenantid = #{tenantid}
          and Goodsid = #{goodsid}
          and Storeid = #{storeid}
          and batchno = #{batchno}
          and location = #{location}
          and packsn = #{packsn}
        ORDER BY CreateDate desc limit 0,1
    </select>


    <select id="getEntityBySnNoSku" resultType="inks.service.sa.som.domain.pojo.MatInventoryPojo">
        select *
        from Mat_Inventory
        WHERE Tenantid = #{tenantid}
        and Goodsid = #{goodsid}
        and Storeid = #{storeid}
        and batchno = #{batchno}
        and location = #{location}
        and packsn = #{packsn}
        and (AttributeJson='' or AttributeJson is null)
        ORDER BY CreateDate desc limit 0,1
    </select>

    <select id="getEntityBySku" resultType="inks.service.sa.som.domain.pojo.MatInventoryPojo">
        select *
        from Mat_Inventory
        WHERE Tenantid = #{tenantid}
          and Goodsid = #{goodsid}
          and Storeid = #{storeid}
          and batchno = #{batchno}
          and location = #{location}
          and packsn = #{packsn}
          and Skuid = #{skuid}
        ORDER BY Quantity desc limit 0,1
    </select>

    <select id="getEntityByIf" resultType="inks.service.sa.som.domain.pojo.MatInventoryPojo">
        select *
        from Mat_Inventory
        WHERE Tenantid = #{tenantid}
        and Goodsid = #{goodsid}
        and Storeid = #{storeid}
        <if test="batchno != null and batchno != ''">
            and batchno = #{batchno}
        </if>
        <if test="location != null and location != ''">
            and location = #{location}
        </if>
        <if test="packsn != null and packsn != ''">
            and packsn = #{packsn}
        </if>
        <if test="skuid != null and skuid != ''">
            and Skuid = #{skuid}
        </if>
        ORDER BY Quantity desc limit 0,1
    </select>

    <!--  根据货品名称规格外部编码获取实例  -->
    <select id="getEntityByBatch" resultType="inks.service.sa.som.domain.pojo.MatInventoryPojo">
        select *
        from Mat_Inventory
        WHERE Tenantid = #{tenantid}
          and Goodsid = #{goodsid}
          and batchno = #{batchno}
        ORDER BY CreateDate desc limit 0,1
    </select>


    <!--查询指定行数据-->
    <select id="getQtyPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatInventoryQtyPojo">
        <include refid="selectMatInventoryVo"/>
        where 1 = 1 and Mat_Inventory.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Inventory.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>

    <!--查询指定行数据-->
    <select id="getQtyPageListByGoods" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatGoodsInveQtyPojo">
        SELECT
        Mat_Goods.id,
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.Material as goodsmaterial,
        Mat_Goods.IvQuantity as Quantity,
        Mat_Goods.Partid,
        Mat_Goods.BusRemQty,
        Mat_Goods.Requremqty,
        Mat_Goods.Buyremqty,
        Mat_Goods.Wkwsremqty,
        Mat_Goods.Wkscremqty,
        Mat_Goods.PackQty,
        App_Workgroup.GroupName,
        IvQuantity+ Buyremqty+Wkwsremqty+Wkscremqty-Requremqty- BusRemQty as stoqty
        FROM
        Mat_Goods
        left join inkssaas.App_Workgroup on Mat_Goods.groupid = App_Workgroup.id
        where 1 = 1 and Mat_Goods.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Goods.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
            </if>
            <if test="SearchType==1">
                <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
                    <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
                </trim>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>


    <!--查询指定行数据-->
    <select id="getQtyPageListByGoods_backup" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.MatGoodsInveQtyPojo">
        SELECT
        Mat_Goods.id,
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.IvQuantity as Quantity,
        Mat_Goods.Partid,
        COALESCE((SELECT SUM(Bus_MachiningItem.Quantity-Bus_MachiningItem.FinishQty)
        FROM Bus_MachiningItem
        LEFT OUTER JOIN Bus_Machining ON Bus_MachiningItem.pid =
        Bus_Machining.id
        where Bus_MachiningItem.Goodsid=Mat_Goods.id
        and Bus_MachiningItem.Quantity &gt; Bus_MachiningItem.FinishQty
        and Bus_MachiningItem.Closed=0
        and Bus_MachiningItem.DisannulMark=0
        and Bus_Machining.Tenantid = #{tenantid}), 0)+
        COALESCE((SELECT SUM(Bus_DelieryItem.Quantity-Bus_DelieryItem.FinishQty)
        FROM Bus_DelieryItem
        LEFT OUTER JOIN Bus_Deliery ON Bus_DelieryItem.pid =
        Bus_Deliery.id
        where Bus_DelieryItem.Goodsid=Mat_Goods.id
        and Bus_DelieryItem.Quantity &gt; Bus_DelieryItem.FinishQty
        and Bus_DelieryItem.FinishClosed=0
        and Bus_DelieryItem.DisannulMark=0
        and Bus_Deliery.Tenantid = #{tenantid}), 0)+
        COALESCE((SELECT SUM(Bus_MachiningItem.WkQty)
        FROM Bus_MachiningItem
        LEFT OUTER JOIN Bus_Machining ON Bus_MachiningItem.pid =
        Bus_Machining.id
        where Bus_MachiningItem.MatCode=Mat_Goods.GoodsUid
        and Bus_MachiningItem.MatUsed<![CDATA[!=]]>1
        and Bus_MachiningItem.Closed=0
        and Bus_MachiningItem.DisannulMark=0
        and Bus_Machining.Tenantid = #{tenantid}), 0) as Busremqty,
        COALESCE((SELECT SUM(Mat_RequisitionItem.Quantity-Mat_RequisitionItem.FinishQty)
        FROM Mat_RequisitionItem
        LEFT OUTER JOIN Mat_Requisition ON Mat_RequisitionItem.pid =
        Mat_Requisition.id
        where Mat_RequisitionItem.Goodsid=Mat_Goods.id
        and Mat_RequisitionItem.Closed=0
        and Mat_RequisitionItem.DisannulMark=0
        and Mat_Requisition.Tenantid = #{tenantid}), 0) as Requremqty,
        COALESCE((SELECT SUM(Buy_OrderItem.Quantity-Buy_OrderItem.FinishQty)
        FROM Buy_OrderItem
        LEFT OUTER JOIN Buy_Order ON Buy_OrderItem.pid =
        Buy_Order.id
        where Buy_OrderItem.Goodsid=Mat_Goods.id
        and Buy_OrderItem.quantity &gt; Buy_OrderItem.FinishQty
        and Buy_OrderItem.Closed=0
        and Buy_OrderItem.DisannulMark=0
        and Buy_Order.Tenantid = #{tenantid}), 0)+
        COALESCE((SELECT SUM(Buy_FinishingItem.Quantity-Buy_FinishingItem.FinishQty)
        FROM Buy_FinishingItem
        LEFT OUTER JOIN Buy_Finishing ON Buy_FinishingItem.pid =
        Buy_Finishing.id
        where Buy_FinishingItem.Goodsid=Mat_Goods.id
        and Buy_FinishingItem.quantity &gt; Buy_FinishingItem.FinishQty
        and Buy_FinishingItem.Closed=0
        and Buy_FinishingItem.DisannulMark=0
        and Buy_Finishing.Tenantid = #{tenantid}), 0) as Buyremqty,
        COALESCE((SELECT SUM(Wk_WorksheetItem.Quantity-Wk_WorksheetItem.FinishQty)
        FROM Wk_WorksheetItem
        LEFT OUTER JOIN Wk_Worksheet ON Wk_WorksheetItem.pid =
        Wk_Worksheet.id
        where Wk_WorksheetItem.Goodsid=Mat_Goods.id
        and Wk_WorksheetItem.quantity &gt; Wk_WorksheetItem.FinishQty
        and Wk_WorksheetItem.Closed=0
        and Wk_WorksheetItem.DisannulMark=0
        and Wk_Worksheet.Tenantid = #{tenantid}), 0) as Wkwsremqty,
        COALESCE((SELECT SUM(Wk_SubcontractItem.Quantity-Wk_SubcontractItem.FinishQty)
        FROM Wk_SubcontractItem
        LEFT OUTER JOIN Wk_Subcontract ON Wk_SubcontractItem.pid =
        Wk_Subcontract.id
        where Wk_SubcontractItem.Goodsid=Mat_Goods.id
        and Wk_SubcontractItem.quantity &gt; Wk_SubcontractItem.FinishQty
        and Wk_SubcontractItem.Closed=0
        and Wk_SubcontractItem.DisannulMark=0
        and Wk_Subcontract.Tenantid = #{tenantid}), 0) as Wkscremqty
        FROM
        Mat_Goods
        where 1 = 1 and Mat_Goods.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Goods.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
            </if>
            <if test="SearchType==1">
                <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
                    <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
                </trim>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>

    <!--查询指定行数据-->
    <update id="updateGoodsIvQty_backup" >
        UPDATE Mat_Goods
        Mat_Goods.id,
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.IvQuantity as Quantity,
        Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
        COALESCE((SELECT SUM(Bus_MachiningItem.Quantity-Bus_MachiningItem.FinishQty)
        FROM Bus_MachiningItem
        LEFT OUTER JOIN Bus_Machining ON Bus_MachiningItem.pid =
        Bus_Machining.id
        where Bus_MachiningItem.Goodsid=Mat_Goods.id
        and Bus_MachiningItem.Quantity &gt; Bus_MachiningItem.FinishQty
        and Bus_MachiningItem.Closed=0
        and Bus_MachiningItem.DisannulMark=0
        and Bus_Machining.Tenantid = #{tenantid}), 0)+
        COALESCE((SELECT SUM(Bus_DelieryItem.Quantity-Bus_DelieryItem.FinishQty)
        FROM Bus_DelieryItem
        LEFT OUTER JOIN Bus_Deliery ON Bus_DelieryItem.pid =
        Bus_Deliery.id
        where Bus_DelieryItem.Goodsid=Mat_Goods.id
        and Bus_DelieryItem.Quantity &gt; Bus_DelieryItem.FinishQty
        and Bus_DelieryItem.FinishClosed=0
        and Bus_DelieryItem.DisannulMark=0
        and Bus_Deliery.Tenantid = #{tenantid}), 0)+
        COALESCE((SELECT SUM(Bus_MachiningItem.WkQty)
        FROM Bus_MachiningItem
        LEFT OUTER JOIN Bus_Machining ON Bus_MachiningItem.pid =
        Bus_Machining.id
        where Bus_MachiningItem.MatCode=Mat_Goods.GoodsUid
        and Bus_MachiningItem.MatUsed<![CDATA[!=]]>1
        and Bus_MachiningItem.Closed=0
        and Bus_MachiningItem.DisannulMark=0
        and Bus_Machining.Tenantid = #{tenantid}), 0) as Busremqty,
        COALESCE((SELECT SUM(Mat_RequisitionItem.Quantity-Mat_RequisitionItem.FinishQty)
        FROM Mat_RequisitionItem
        LEFT OUTER JOIN Mat_Requisition ON Mat_RequisitionItem.pid =
        Mat_Requisition.id
        where Mat_RequisitionItem.Goodsid=Mat_Goods.id
        and Mat_RequisitionItem.Closed=0
        and Mat_RequisitionItem.DisannulMark=0
        and Mat_Requisition.Tenantid = #{tenantid}), 0) as Requremqty,
        COALESCE((SELECT SUM(Buy_OrderItem.Quantity-Buy_OrderItem.FinishQty)
        FROM Buy_OrderItem
        LEFT OUTER JOIN Buy_Order ON Buy_OrderItem.pid =
        Buy_Order.id
        where Buy_OrderItem.Goodsid=Mat_Goods.id
        and Buy_OrderItem.quantity &gt; Buy_OrderItem.FinishQty
        and Buy_OrderItem.Closed=0
        and Buy_OrderItem.DisannulMark=0
        and Buy_Order.Tenantid = #{tenantid}), 0)+
        COALESCE((SELECT SUM(Buy_FinishingItem.Quantity-Buy_FinishingItem.FinishQty)
        FROM Buy_FinishingItem
        LEFT OUTER JOIN Buy_Finishing ON Buy_FinishingItem.pid =
        Buy_Finishing.id
        where Buy_FinishingItem.Goodsid=Mat_Goods.id
        and Buy_FinishingItem.quantity &gt; Buy_FinishingItem.FinishQty
        and Buy_FinishingItem.Closed=0
        and Buy_FinishingItem.DisannulMark=0
        and Buy_Finishing.Tenantid = #{tenantid}), 0) as Buyremqty,
        COALESCE((SELECT SUM(Wk_WorksheetItem.Quantity-Wk_WorksheetItem.FinishQty)
        FROM Wk_WorksheetItem
        LEFT OUTER JOIN Wk_Worksheet ON Wk_WorksheetItem.pid =
        Wk_Worksheet.id
        where Wk_WorksheetItem.Goodsid=Mat_Goods.id
        and Wk_WorksheetItem.quantity &gt; Wk_WorksheetItem.FinishQty
        and Wk_WorksheetItem.Closed=0
        and Wk_WorksheetItem.DisannulMark=0
        and Wk_Worksheet.Tenantid = #{tenantid}), 0) as Wkwsremqty,
        COALESCE((SELECT SUM(Wk_SubcontractItem.Quantity-Wk_SubcontractItem.FinishQty)
        FROM Wk_SubcontractItem
        LEFT OUTER JOIN Wk_Subcontract ON Wk_SubcontractItem.pid =
        Wk_Subcontract.id
        where Wk_SubcontractItem.Goodsid=Mat_Goods.id
        and Wk_SubcontractItem.quantity &gt; Wk_SubcontractItem.FinishQty
        and Wk_SubcontractItem.Closed=0
        and Wk_SubcontractItem.DisannulMark=0
        and Wk_Subcontract.Tenantid = #{tenantid}), 0) as Wkscremqty
        FROM
        Mat_Goods
        where 1 = 1 and Mat_Goods.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Goods.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
            </if>
            <if test="SearchType==1">
                <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
                    <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
                </trim>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </update>
    <!--查询指定行数据-->
    <select id="getListByGoods" resultType="inks.service.sa.som.domain.pojo.MatInventoryPojo">
        <include refid="selectMatInventoryVo"/>
        where Mat_Inventory.Tenantid =#{tid} and Mat_Inventory.Goodsid =#{key} and Mat_Inventory.Quantity
        <![CDATA[!=]]> 0
        order by Mat_Inventory.Location
    </select>


    <!--查询指定行数据-->
    <select id="getOnlineListByStore"
            resultType="inks.service.sa.som.domain.pojo.MatInventoryPojo">
        <include refid="selectMatInventoryVo"/>
        where Mat_Inventory.Storeid=#{key} and Mat_Inventory.Tenantid =#{tid} and Mat_Inventory.Quantity
        <![CDATA[!=]]> 0
        order by Mat_Goods.GoodsUid
    </select>


    <!--查询指定行数据-->
    <select id="getMachMatQtyPageListByGoods"  resultType="inks.service.sa.som.domain.pojo.MatGoodsInveQtyPojo">
        SELECT
        Mat_Goods.id,
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.IvQuantity as quantity,
        Mat_Goods.Partid,
        COALESCE((SELECT SUM(Bus_MachiningItem.quantity-Bus_MachiningItem.FinishQty)
        FROM Bus_MachiningItem
        LEFT OUTER JOIN Bus_Machining ON Bus_MachiningItem.pid =
        Bus_Machining.id
        where Bus_MachiningItem.Goodsid=Mat_Goods.id
        and Bus_MachiningItem.Quantity &gt; Bus_MachiningItem.FinishQty
        and Bus_MachiningItem.Closed=0
        and Bus_MachiningItem.DisannulMark=0
        and Bus_Machining.Tenantid = #{queryParam.tenantid}), 0)+
        COALESCE((SELECT SUM(Bus_MachiningItem.WkQty)
        FROM Bus_MachiningItem
        LEFT OUTER JOIN Bus_Machining ON Bus_MachiningItem.pid =
        Bus_Machining.id
        where Bus_MachiningItem.MatCode=Mat_Goods.GoodsUid
        <if test="itemid != null and itemid != ''">
             and Bus_MachiningItem.id<![CDATA[!=]]>#{itemid}
        </if>
        and Bus_MachiningItem.MatUsed<![CDATA[!=]]>1
        and Bus_MachiningItem.Closed=0
        and Bus_MachiningItem.DisannulMark=0
        and Bus_Machining.Tenantid = #{queryParam.tenantid}), 0) as Busremqty,
        COALESCE((SELECT SUM(Mat_RequisitionItem.quantity-Mat_RequisitionItem.FinishQty)
        FROM Mat_RequisitionItem
        LEFT OUTER JOIN Mat_Requisition ON Mat_RequisitionItem.pid =
        Mat_Requisition.id
        where Mat_RequisitionItem.Goodsid=Mat_Goods.id
        and Mat_RequisitionItem.quantity &gt; Mat_RequisitionItem.FinishQty
        and Mat_RequisitionItem.Closed=0
        and Mat_RequisitionItem.DisannulMark=0
        and Mat_Requisition.Tenantid = #{queryParam.tenantid}), 0) as Requremqty,
        COALESCE((SELECT SUM(Buy_OrderItem.quantity-Buy_OrderItem.FinishQty)
        FROM Buy_OrderItem
        LEFT OUTER JOIN Buy_Order ON Buy_OrderItem.pid =
        Buy_Order.id
        where Buy_OrderItem.Goodsid=Mat_Goods.id
        and Buy_OrderItem.quantity &gt; Buy_OrderItem.FinishQty
        and Buy_OrderItem.Closed=0
        and Buy_OrderItem.DisannulMark=0
        and Buy_Order.Tenantid = #{queryParam.tenantid}), 0) as Buyremqty
        FROM
        Mat_Goods
        where 1 = 1 and Mat_Goods.Tenantid =#{queryParam.tenantid}
        <if test="queryParam.filterstr != null ">
            ${queryParam.filterstr}
        </if>
        <if test="queryParam.DateRange != null ">
            <if test="queryParam.DateRange.DateColumn ==null or queryParam.DateRange.DateColumn == ''">
                and Mat_Goods.CreateDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
            </if>
            <if test="queryParam.DateRange.DateColumn !=null and queryParam.DateRange.DateColumn != ''">
                and ${queryParam.DateRange.DateColumn} BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
            </if>
        </if>
        <if test="queryParam.SearchPojo != null ">
            <if test="queryParam.SearchType==0">
                <!--货品查询 -->
                <if test="queryParam.SearchPojo.goodsuid != null and queryParam.SearchPojo.goodsuid  != ''">
                    and Mat_Goods.GoodsUid like concat('%', #{queryParam.SearchPojo.goodsuid}, '%')
                </if>
                <if test="queryParam.SearchPojo.goodsname != null and queryParam.SearchPojo.goodsname  != ''">
                    and Mat_Goods.GoodsName like concat('%', #{queryParam.SearchPojo.goodsname}, '%')
                </if>
                <if test="queryParam.SearchPojo.goodsspec != null and queryParam.SearchPojo.goodsspec  != ''">
                    and Mat_Goods.GoodsSpec like concat('%', #{queryParam.SearchPojo.goodsspec}, '%')
                </if>
                <if test="queryParam.SearchPojo.partid != null and queryParam.SearchPojo.partid  != ''">
                    and Mat_Goods.Partid like concat('%', #{queryParam.SearchPojo.partid}, '%')
                </if>
            </if>
            <if test="queryParam.SearchType==1">
                <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
                    <!--货品查询 -->
                    <if test="queryParam.SearchPojo.goodsuid != null and queryParam.SearchPojo.goodsuid != ''">
                        or Mat_Goods.GoodsUid like concat('%', #{queryParam.SearchPojo.goodsuid}, '%')
                    </if>
                    <if test="queryParam.SearchPojo.goodsname != null and queryParam.SearchPojo.goodsname != ''">
                        or Mat_Goods.GoodsName like concat('%', #{queryParam.SearchPojo.goodsname}, '%')
                    </if>
                    <if test="queryParam.SearchPojo.goodsspec != null and queryParam.SearchPojo.goodsspec != ''">
                        or Mat_Goods.GoodsSpec like concat('%', #{queryParam.SearchPojo.goodsspec}, '%')
                    </if>
                    <if test="queryParam.SearchPojo.partid != null and queryParam.SearchPojo.partid != ''">
                        or Mat_Goods.Partid like concat('%', #{queryParam.SearchPojo.partid}, '%')
                    </if>
                </trim>
            </if>
        </if>
        order by ${queryParam.orderBy}
        <if test="queryParam.OrderType==0">asc</if>
        <if test="queryParam.OrderType==1">desc</if>
    </select>
    <select id="getEntityByNameSpecPart" resultType="inks.service.sa.som.domain.pojo.MatInventoryPojo">
        <include refid="selectMatInventoryVo"/>
        WHERE Mat_Goods.Tenantid = #{tid}
        and Mat_Goods.GoodsName = #{name}
        and Mat_Goods.GoodsSpec = #{goodsspec}
        <if test="partid != null ">
            and Mat_Goods.Partid = #{partid}
        </if>
        ORDER BY Mat_Goods.CreateDate desc limit 0,1
    </select>
    <select id="getEntityByStoreGoods" resultType="inks.service.sa.som.domain.pojo.MatInventoryPojo">
        SELECT Mat_Inventory.id,
               Mat_Inventory.Storeid,
               Mat_Inventory.Goodsid,
               Mat_Inventory.Quantity,
               Mat_Inventory.Amount,
               Mat_Inventory.EndUid,
               Mat_Inventory.EndInUid,
               Mat_Inventory.EndInDate,
               Mat_Inventory.EndOutUid,
               Mat_Inventory.EndOutDate,
               Mat_Inventory.BatchNo,
               Mat_Inventory.Location,
               Mat_Inventory.PackSn,
               Mat_Inventory.Skuid,
               Mat_Inventory.AttributeJson,
               Mat_Inventory.ExpiDate,
               Mat_Inventory.CreateBy,
               Mat_Inventory.CreateByid,
               Mat_Inventory.CreateDate,
               Mat_Inventory.Lister,
               Mat_Inventory.Listerid,
               Mat_Inventory.ModifyDate,
               Mat_Inventory.Custom1,
               Mat_Inventory.Custom2,
               Mat_Inventory.Custom3,
               Mat_Inventory.Custom4,
               Mat_Inventory.Custom5,
               Mat_Inventory.Custom6,
               Mat_Inventory.Custom7,
               Mat_Inventory.Custom8,
               Mat_Inventory.Custom9,
               Mat_Inventory.Custom10,
               Mat_Inventory.Tenantid,
               Mat_Inventory.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,Mat_Goods.Surface,Mat_Goods.Drawing,Mat_Goods.BrandName,
               Mat_Goods.IvQuantity,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Storage.StoreCode,
               Mat_Storage.StoreName
        FROM Mat_Goods
                 RIGHT JOIN Mat_Inventory ON Mat_Goods.id = Mat_Inventory.Goodsid
                 LEFT JOIN Mat_Storage ON Mat_Storage.id = Mat_Inventory.Storeid
        where Mat_Inventory.Goodsid = #{goodsid}
          and Mat_Inventory.Storeid = #{storeid}
          and Mat_Inventory.Tenantid = #{tid} limit 1
    </select>


    <select id="getGoodsPageList" resultType="inks.service.sa.som.domain.pojo.MatGoodsPojo">
        SELECT Mat_Goods.id,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.GoodsState,
               Mat_Goods.GoodsPinyin,
               Mat_Goods.VersionNum,
               Mat_Goods.Material,
               Mat_Goods.Surface,
               Mat_Goods.BarCode,
               Mat_Goods.SafeStock,
               Mat_Goods.InPrice,
               Mat_Goods.OutPrice,
               Mat_Goods.Groupid,
               Mat_Goods.FileGuid,
               Mat_Goods.Drawing,
               Mat_Goods.Storeid,
               Mat_Goods.StoreListName,
               Mat_Goods.StoreListGuid,
               Mat_Goods.IvQuantity,
               Mat_Goods.AgePrice,
               Mat_Goods.UidGroupGuid,
               Mat_Goods.UidGroupCode,
               Mat_Goods.UidGroupName,
               Mat_Goods.UidGroupNum,
               Mat_Goods.Partid,
               Mat_Goods.Pid,
               Mat_Goods.PUid,
               Mat_Goods.EnabledMark,
               Mat_Goods.GoodsPhoto1,
               Mat_Goods.GoodsPhoto2,
               Mat_Goods.Remark,
               Mat_Goods.CreateBy,
               Mat_Goods.CreateByid,
               Mat_Goods.CreateDate,
               Mat_Goods.Lister,
               Mat_Goods.Listerid,
               Mat_Goods.ModifyDate,
               Mat_Goods.DeleteMark,
               Mat_Goods.DeleteLister,
               Mat_Goods.DeleteListerid,
               Mat_Goods.DeleteDate,
               Mat_Goods.BatchMg,
               Mat_Goods.BatchOnly,
               Mat_Goods.SkuMark,
               Mat_Goods.PackSnMark,
               Mat_Goods.VirtualItem,
               Mat_Goods.Bomid,
               Mat_Goods.QuickCode,
               Mat_Goods.BrandName,
               Mat_Goods.BuyRemQty,
               Mat_Goods.WkWsRemQty,
               Mat_Goods.WkScRemQty,
               Mat_Goods.BusRemQty,
               Mat_Goods.MrpRemQty,
               Mat_Goods.AlertsQty,
               Mat_Goods.IntQtyMark,
               Mat_Goods.WeightQty,
               Mat_Goods.WeightUnit,
               Mat_Goods.LengthQty,
               Mat_Goods.LengthUnit,
               Mat_Goods.AreaQty,
               Mat_Goods.AreaUnit,
               Mat_Goods.VolumeQty,
               Mat_Goods.VolumeUnit,
               Mat_Goods.PackQty,
               Mat_Goods.PackUnit,
               Mat_Goods.MatQtyUnit,
               Mat_Goods.OverflowQty,
               Mat_Goods.Custom1,
               Mat_Goods.Custom2,
               Mat_Goods.Custom3,
               Mat_Goods.Custom4,
               Mat_Goods.Custom5,
               Mat_Goods.Custom6,
               Mat_Goods.Custom7,
               Mat_Goods.Custom8,
               Mat_Goods.Custom9,
               Mat_Goods.Custom10,
               Mat_Goods.Deptid,
               Mat_Goods.Tenantid,
               Mat_Goods.TenantName,
               Mat_Goods.Revision
        FROM Mat_Goods
        where 1 = 1 and Mat_Goods.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Goods.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
<!--   通过物料Code获取订单占用数量-->
    <select id="getRequremqtyByGoodsUid" resultType="java.lang.Double">
        select ifnull(sum(Bus_MachiningItem.Quantity),0) AS requremqty
        from Bus_MachiningItem
        where Bus_MachiningItem.Quantity > Bus_MachiningItem.FinishQty
          AND Bus_MachiningItem.MatCode=#{goodsuid}
          AND Bus_MachiningItem.Closed = 0
          AND Bus_MachiningItem.DisannulMark = 0
          AND Bus_MachiningItem.Tenantid = #{tid}
    </select>
    <!--   通过物料Goodsid获取订单占用数量-->
    <select id="getRequremqtyByGoodsid" resultType="java.lang.Double">
        select ifnull(sum(Bus_MachiningItem.Quantity),0) AS requremqty
        from Bus_MachiningItem
        where Bus_MachiningItem.Quantity > Bus_MachiningItem.FinishQty
          AND Bus_MachiningItem.Goodsid=#{goodsid}
          AND Bus_MachiningItem.Closed = 0
          AND Bus_MachiningItem.DisannulMark = 0
          AND Bus_MachiningItem.Tenantid = #{tid}
    </select>



    <select id="getSumPageListByGoods" resultType="java.util.Map" parameterType="inks.common.core.domain.QueryParam">
        SELECT
        Mat_Goods.GoodsUid as goodsuid,
        Mat_Goods.GoodsName as goodsname,
        Mat_Goods.GoodsSpec as goodsspec,
        Mat_Goods.GoodsUnit as goodsunit,
        sum(Mat_Inventory.Quantity) as sumqty,
        count(*) as count
        FROM Mat_Goods
        RIGHT JOIN Mat_Inventory ON Mat_Goods.id = Mat_Inventory.Goodsid
        where Mat_Inventory.Tenantid = #{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Mat_Inventory.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
            </if>
            <if test="SearchType == 1">
                <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
                <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
                </trim>
            </if>
        </if>
        group by
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit
        HAVING sumqty <![CDATA[!=]]> 0
        order by sumqty
    </select>


</mapper>

