<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BuyPlanMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BuyPlanPojo">
        <include refid="selectbillVo"/>
        where Buy_Plan.id = #{key}
          and Buy_Plan.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT Buy_Plan.id,
               Buy_Plan.RefNo,
               Buy_Plan.BillType,
               Buy_Plan.BillTitle,
               Buy_Plan.BillDate,
               Buy_Plan.Projectid,
               Buy_Plan.ProjName,
               Buy_Plan.ProjCode,
               Buy_Plan.Groupid,
               Buy_Plan.BranthName,
               Buy_Plan.Taxrate,
               Buy_Plan.Arrivaladd,
               Buy_Plan.Operator,
               Buy_Plan.Summary,
               Buy_Plan.CreateBy,
               Buy_Plan.CreateByid,
               Buy_Plan.CreateDate,
               Buy_Plan.Lister,
               Buy_Plan.Listerid,
               Buy_Plan.ModifyDate,
               Buy_Plan.Assessor,
               Buy_Plan.Assessorid,
               Buy_Plan.AssessDate,
               Buy_Plan.BillTaxAmount,
               Buy_Plan.BillTaxTotal,
               Buy_Plan.BillAmount,
               Buy_Plan.BillStateCode,
               Buy_Plan.BillStateDate,
               Buy_Plan.BillPlanDate,
               Buy_Plan.ItemCount,
               Buy_Plan.BuyCount,
               Buy_Plan.FinishCount,
               Buy_Plan.DisannulCount,
               Buy_Plan.PrintCount,
               Buy_Plan.MergeCount,
               Buy_Plan.MergeMark,
               Buy_Plan.OaFlowMark,
               Buy_Plan.Trimor,
               Buy_Plan.Trimdate,
               Buy_Plan.Custom1,
               Buy_Plan.Custom3,
               Buy_Plan.Custom2,
               Buy_Plan.Custom4,
               Buy_Plan.Custom5,
               Buy_Plan.Custom6,
               Buy_Plan.Custom7,
               Buy_Plan.Custom8,
               Buy_Plan.Custom9,
               Buy_Plan.Custom10,
               Buy_Plan.Deptid,
               Buy_Plan.Tenantid,
               Buy_Plan.TenantName,
               Buy_Plan.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Buy_Plan
                 LEFT JOIN App_Workgroup ON Buy_Plan.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT Buy_PlanItem.id,
               Buy_PlanItem.Pid,
               Buy_PlanItem.ItemType,
               Buy_PlanItem.Goodsid,
               Buy_PlanItem.ItemCode,
               Buy_PlanItem.ItemName,
               Buy_PlanItem.ItemSpec,
               Buy_PlanItem.ItemUnit,
               Buy_PlanItem.Quantity,
               Buy_PlanItem.TaxPrice,
               Buy_PlanItem.TaxAmount,
               Buy_PlanItem.TaxTotal,
               Buy_PlanItem.ItemTaxrate,
               Buy_PlanItem.Price,
               Buy_PlanItem.Amount,
               Buy_PlanItem.PlanDate,
               Buy_PlanItem.Groupid,
               Buy_PlanItem.Remark,
               Buy_PlanItem.CiteUid,
               Buy_PlanItem.CiteItemid,
               Buy_PlanItem.StateCode,
               Buy_PlanItem.StateDate,
               Buy_PlanItem.Closed,
               Buy_PlanItem.BuyQty,
               Buy_PlanItem.FinishQty,
               Buy_PlanItem.RowNum,
               Buy_PlanItem.MachUid,
               Buy_PlanItem.MachItemid,
               Buy_PlanItem.MachBatch,
               Buy_PlanItem.MachGroupid,
               Buy_PlanItem.MainPlanUid,
               Buy_PlanItem.MainPlanItemid,
               Buy_PlanItem.MrpUid,
               Buy_PlanItem.MrpItemid,
               Buy_PlanItem.Customer,
               Buy_PlanItem.CustPO,
               Buy_PlanItem.BatchNo,
               Buy_PlanItem.DisannulMark,
               Buy_PlanItem.DisannulListerid,
               Buy_PlanItem.DisannulLister,
               Buy_PlanItem.DisannulDate,
               Buy_PlanItem.AttributeJson,
               Buy_PlanItem.SourceType,
               Buy_PlanItem.Mergeid,
               Buy_PlanItem.MergeBuyQty,
               Buy_PlanItem.MergeFinishQty,
               Buy_PlanItem.MergeMark,
               Buy_PlanItem.MergeItems,
               Buy_PlanItem.PlanQty,
               Buy_PlanItem.Custom1,
               Buy_PlanItem.Custom2,
               Buy_PlanItem.Custom3,
               Buy_PlanItem.Custom4,
               Buy_PlanItem.Custom5,
               Buy_PlanItem.Custom6,
               Buy_PlanItem.Custom7,
               Buy_PlanItem.Custom8,
               Buy_PlanItem.Custom9,
               Buy_PlanItem.Custom10,
               Buy_PlanItem.Tenantid,
               Buy_PlanItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid,
               Mat_Goods.Surface,
               Mat_Goods.Drawing,
               Mat_Goods.BrandName,
               Mat_Goods.Material as GoodsMaterial,
               Mat_Goods.Custom1  AS GoodsCustom1,
               Mat_Goods.Custom2  AS GoodsCustom2,
               Mat_Goods.Custom3  AS GoodsCustom3,
               Mat_Goods.Custom4  AS GoodsCustom4,
               Mat_Goods.Custom5  AS GoodsCustom5,
               Mat_Goods.Custom6  AS GoodsCustom6,
               Mat_Goods.Custom7  AS GoodsCustom7,
               Mat_Goods.Custom8  AS GoodsCustom8,
               Mat_Goods.Custom9  AS GoodsCustom9,
               Mat_Goods.Custom10 AS GoodsCustom10,
               Buy_Plan.RefNo,
               Buy_Plan.BillTitle,
               Buy_Plan.BillType,
               Buy_Plan.BillDate,
               Buy_Plan.Projectid,
               Buy_Plan.ProjName,
               Buy_Plan.ProjCode,
               Buy_Plan.Taxrate,
               Buy_Plan.Operator,
               Buy_Plan.Summary,
               Buy_Plan.CreateBy,
               Buy_Plan.CreateDate,
               Buy_Plan.Lister,
               Buy_Plan.ModifyDate,
               Buy_Plan.Assessor,
               Buy_Plan.AssessDate,
               Buy_Plan.Groupid,
               Buy_Plan.BranthName,
               App_Workgroup.GroupName as itemgroupname,
               App_Workgroup.GroupUid,
               App_Workgroup.Abbreviate,
               g2.GoodsName                               as MrpObjGoodsName,
               g2.GoodsUid                                as MrpObjGoodsUid
        FROM Buy_Plan
                 RIGHT JOIN Buy_PlanItem ON Buy_PlanItem.Pid = Buy_Plan.id
                 LEFT JOIN App_Workgroup ON Buy_PlanItem.Groupid = App_Workgroup.id
                 LEFT JOIN Mat_Goods ON Buy_PlanItem.Goodsid = Mat_Goods.id
                 LEFT JOIN Mat_Goods g2 ON Buy_PlanItem.MrpObjGoodsid = g2.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyPlanitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Buy_Plan.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_Plan.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Buy_Plan.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Buy_Plan.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Buy_Plan.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Buy_Plan.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.branthname != null ">
            and Buy_Plan.branthname like concat('%', #{SearchPojo.branthname}, '%')
        </if>
        <if test="SearchPojo.arrivaladd != null ">
            and Buy_Plan.arrivaladd like concat('%', #{SearchPojo.arrivaladd}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Buy_Plan.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Buy_Plan.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Buy_Plan.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Buy_Plan.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Buy_Plan.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Buy_Plan.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Buy_Plan.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Buy_Plan.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null ">
            and Buy_Plan.billstatecode like concat('%', #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Buy_Plan.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Buy_Plan.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Buy_Plan.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Buy_Plan.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Buy_Plan.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Buy_Plan.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Buy_Plan.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Buy_Plan.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Buy_Plan.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Buy_Plan.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Buy_Plan.deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Buy_Plan.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
            and Buy_PlanItem.goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Buy_PlanItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Buy_PlanItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
            and Buy_PlanItem.itemspec like concat('%', #{SearchPojo.itemspec}, '%')
        </if>
        <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
            and Buy_PlanItem.itemunit like concat('%', #{SearchPojo.itemunit}, '%')
        </if>
        <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
            and Buy_PlanItem.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Buy_PlanItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
            and Buy_PlanItem.citeuid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
            and Buy_PlanItem.citeitemid like concat('%', #{SearchPojo.citeitemid}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and Buy_PlanItem.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
            and Buy_PlanItem.machuid like concat('%', #{SearchPojo.machuid}, '%')
        </if>
        <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
            and Buy_PlanItem.machitemid like concat('%', #{SearchPojo.machitemid}, '%')
        </if>
        <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
            and Buy_PlanItem.machgroupid like concat('%', #{SearchPojo.machgroupid}, '%')
        </if>
        <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
            and Buy_PlanItem.mainplanuid like concat('%', #{SearchPojo.mainplanuid}, '%')
        </if>
        <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
            and Buy_PlanItem.mainplanitemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
        </if>
        <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
            and Buy_PlanItem.mrpuid like concat('%', #{SearchPojo.mrpuid}, '%')
        </if>
        <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
            and Buy_PlanItem.mrpitemid like concat('%', #{SearchPojo.mrpitemid}, '%')
        </if>
        <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
            and Buy_PlanItem.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
            and Buy_PlanItem.custpo like concat('%', #{SearchPojo.custpo}, '%')
        </if>
        <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
            and Buy_PlanItem.batchno like concat('%', #{SearchPojo.batchno}, '%')
        </if>
        <if test="SearchPojo.branthname != null and SearchPojo.branthname != ''">
            and Buy_Plan.branthname like concat('%', #{SearchPojo.branthname}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Buy_Plan.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Buy_Plan.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Buy_Plan.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Buy_Plan.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.branthname != null ">
                or Buy_Plan.BranthName like concat('%', #{SearchPojo.branthname}, '%')
            </if>
            <if test="SearchPojo.arrivaladd != null ">
                or Buy_Plan.Arrivaladd like concat('%', #{SearchPojo.arrivaladd}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Buy_Plan.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Buy_Plan.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Buy_Plan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Buy_Plan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Buy_Plan.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Buy_Plan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Buy_Plan.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Buy_Plan.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null ">
                or Buy_Plan.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Buy_Plan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Buy_Plan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Buy_Plan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Buy_Plan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Buy_Plan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Buy_Plan.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Buy_Plan.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Buy_Plan.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Buy_Plan.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Buy_Plan.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Buy_Plan.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Buy_Plan.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.goodsid != null and SearchPojo.goodsid != ''">
                or Buy_PlanItem.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Buy_PlanItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Buy_PlanItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemspec != null and SearchPojo.itemspec != ''">
                or Buy_PlanItem.ItemSpec like concat('%', #{SearchPojo.itemspec}, '%')
            </if>
            <if test="SearchPojo.itemunit != null and SearchPojo.itemunit != ''">
                or Buy_PlanItem.ItemUnit like concat('%', #{SearchPojo.itemunit}, '%')
            </if>
            <if test="SearchPojo.groupid != null and SearchPojo.groupid != ''">
                or Buy_PlanItem.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Buy_PlanItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.citeuid != null and SearchPojo.citeuid != ''">
                or Buy_PlanItem.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.citeitemid != null and SearchPojo.citeitemid != ''">
                or Buy_PlanItem.CiteItemid like concat('%', #{SearchPojo.citeitemid}, '%')
            </if>
            <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
                or Buy_PlanItem.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.machuid != null and SearchPojo.machuid != ''">
                or Buy_PlanItem.MachUid like concat('%', #{SearchPojo.machuid}, '%')
            </if>
            <if test="SearchPojo.machitemid != null and SearchPojo.machitemid != ''">
                or Buy_PlanItem.MachItemid like concat('%', #{SearchPojo.machitemid}, '%')
            </if>
            <if test="SearchPojo.machgroupid != null and SearchPojo.machgroupid != ''">
                or Buy_PlanItem.MachGroupid like concat('%', #{SearchPojo.machgroupid}, '%')
            </if>
            <if test="SearchPojo.mainplanuid != null and SearchPojo.mainplanuid != ''">
                or Buy_PlanItem.MainPlanUid like concat('%', #{SearchPojo.mainplanuid}, '%')
            </if>
            <if test="SearchPojo.mainplanitemid != null and SearchPojo.mainplanitemid != ''">
                or Buy_PlanItem.MainPlanItemid like concat('%', #{SearchPojo.mainplanitemid}, '%')
            </if>
            <if test="SearchPojo.mrpuid != null and SearchPojo.mrpuid != ''">
                or Buy_PlanItem.MrpUid like concat('%', #{SearchPojo.mrpuid}, '%')
            </if>
            <if test="SearchPojo.mrpitemid != null and SearchPojo.mrpitemid != ''">
                or Buy_PlanItem.MrpItemid like concat('%', #{SearchPojo.mrpitemid}, '%')
            </if>
            <if test="SearchPojo.customer != null and SearchPojo.customer != ''">
                or Buy_PlanItem.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.custpo != null and SearchPojo.custpo != ''">
                or Buy_PlanItem.CustPO like concat('%', #{SearchPojo.custpo}, '%')
            </if>
            <if test="SearchPojo.batchno != null and SearchPojo.batchno != ''">
                or Buy_PlanItem.BatchNo like concat('%', #{SearchPojo.batchno}, '%')
            </if>
            <if test="SearchPojo.branthname != null and SearchPojo.branthname != ''">
                or Buy_Plan.BranthName like concat('%', #{SearchPojo.branthname}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BuyPlanPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Buy_Plan.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Buy_Plan.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Buy_Plan.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Buy_Plan.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Buy_Plan.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Buy_Plan.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.branthname != null ">
            and Buy_Plan.BranthName like concat('%', #{SearchPojo.branthname}, '%')
        </if>
        <if test="SearchPojo.arrivaladd != null ">
            and Buy_Plan.Arrivaladd like concat('%', #{SearchPojo.arrivaladd}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Buy_Plan.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Buy_Plan.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Buy_Plan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Buy_Plan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Buy_Plan.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Buy_Plan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null ">
            and Buy_Plan.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null ">
            and Buy_Plan.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.billstatecode != null ">
            and Buy_Plan.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Buy_Plan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Buy_Plan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Buy_Plan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Buy_Plan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Buy_Plan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Buy_Plan.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Buy_Plan.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Buy_Plan.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Buy_Plan.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Buy_Plan.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and Buy_Plan.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Buy_Plan.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsandfilter"/>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Buy_Plan.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Buy_Plan.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Buy_Plan.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Buy_Plan.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.branthname != null ">
                or Buy_Plan.BranthName like concat('%', #{SearchPojo.branthname}, '%')
            </if>
            <if test="SearchPojo.arrivaladd != null ">
                or Buy_Plan.Arrivaladd like concat('%', #{SearchPojo.arrivaladd}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Buy_Plan.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Buy_Plan.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Buy_Plan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Buy_Plan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Buy_Plan.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Buy_Plan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null ">
                or Buy_Plan.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null ">
                or Buy_Plan.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.billstatecode != null ">
                or Buy_Plan.BillStateCode like concat('%', #{SearchPojo.billstatecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Buy_Plan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Buy_Plan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Buy_Plan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Buy_Plan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Buy_Plan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Buy_Plan.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Buy_Plan.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Buy_Plan.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Buy_Plan.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Buy_Plan.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or Buy_Plan.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Buy_Plan.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.goodsorfilter"/>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Buy_Plan(id, RefNo, BillType, BillTitle, BillDate, Projectid, ProjCode, ProjName, Groupid, BranthName, Taxrate, Arrivaladd, Operator, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, BillTaxAmount, BillTaxTotal, BillAmount, BillStateCode, BillStateDate, BillPlanDate, ItemCount, BuyCount, FinishCount, DisannulCount, PrintCount, OaFlowMark, MergeCount, MergeMark, Trimor, Trimdate, Custom1, Custom3, Custom2, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{projectid}, #{projcode}, #{projname}, #{groupid}, #{branthname}, #{taxrate}, #{arrivaladd}, #{operator}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{billtaxamount}, #{billtaxtotal}, #{billamount}, #{billstatecode}, #{billstatedate}, #{billplandate}, #{itemcount}, #{buycount}, #{finishcount}, #{disannulcount}, #{printcount}, #{oaflowmark}, #{mergecount}, #{mergemark}, #{trimor}, #{trimdate}, #{custom1}, #{custom3}, #{custom2}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Buy_Plan
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="branthname != null ">
                BranthName =#{branthname},
            </if>
            <if test="taxrate != null">
                Taxrate =#{taxrate},
            </if>
            <if test="arrivaladd != null ">
                Arrivaladd =#{arrivaladd},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="billtaxtotal != null">
                BillTaxTotal =#{billtaxtotal},
            </if>
            <if test="billamount != null">
                BillAmount =#{billamount},
            </if>
            <if test="billstatecode != null ">
                BillStateCode =#{billstatecode},
            </if>
            <if test="billstatedate != null">
                BillStateDate =#{billstatedate},
            </if>
            <if test="billplandate != null">
                BillPlanDate =#{billplandate},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="buycount != null">
                BuyCount =#{buycount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="mergecount != null">
                MergeCount =#{mergecount},
            </if>
            <if test="mergemark != null">
                MergeMark =#{mergemark},
            </if>
            <if test="trimor != null ">
                Trimor =#{trimor},
            </if>
            <if test="trimdate != null">
                Trimdate =#{trimdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Buy_Plan
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Buy_Plan
        SET Assessor   = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>
    <update id="updatePrintcount">
        update Buy_Plan
        SET PrintCount = #{printcount}
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--    刷新发货完成数 Eric 20220722-->
    <update id="updateDisannulCount">
        update Buy_Plan
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Buy_PlanItem
                                     where Buy_PlanItem.Pid = #{key}
                                       and Buy_PlanItem.Tenantid = #{tid}
                                       and Buy_PlanItem.DisannulMark = 1), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateBuyFinishCount">
        update Buy_Plan
        SET BuyCount    =COALESCE((SELECT COUNT(0)
                                   FROM Buy_PlanItem
                                   where Buy_PlanItem.Pid = #{key}
                                     and Buy_PlanItem.Tenantid = #{tid}
                                     and (Buy_PlanItem.Closed = 1
                                       or Buy_PlanItem.BuyQty >= Buy_PlanItem.Quantity)), 0)
          , FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Buy_PlanItem
                                   where Buy_PlanItem.Pid = #{key}
                                     and Buy_PlanItem.Tenantid = #{tid}
                                     and (Buy_PlanItem.Closed = 1
                                       or Buy_PlanItem.FinishQty >= Buy_PlanItem.Quantity)), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BuyPlanPojo">
        select
        id
        from Buy_PlanItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateMrpBuyPlanFinish">
        update Wk_MrpItem
        SET BuyPlanQty =COALESCE((SELECT SUM(Buy_PlanItem.PlanQty)
                                  FROM Buy_PlanItem
                                           LEFT OUTER JOIN Buy_Plan
                                                           ON Buy_PlanItem.pid = Buy_Plan.id
                                  where Buy_Plan.BillType = 'MRP需求'
                                    and Buy_PlanItem.MrpUid = #{refno}
                                    and Buy_PlanItem.MrpItemid = #{key}
                                    and Buy_PlanItem.DisannulMark = 0
                                    and Buy_PlanItem.Tenantid = #{tid}), 0)
        where id = #{key}
          and Tenantid = #{tid}
    </update>

    <!--    刷新发货完成数 Eric 20211213-->
    <update id="updateMrpFinishCount">
        update Wk_Mrp
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Wk_MrpItem
                                   where Wk_MrpItem.Pid =
                                         (SELECT Pid FROM Wk_MrpItem where id = #{key})
                                     and Wk_MrpItem.Tenantid = #{tid}
                                     and Wk_MrpItem.BuyPlanQty + Wk_MrpItem.BuyOrderQty + Wk_MrpItem.CustSuppQty +
                                         Wk_MrpItem.WkWsQty+Wk_MrpItem.WkScQty+Wk_MrpItem.OtherQty >= Wk_MrpItem.NeedQty), 0)
        where id = (SELECT Pid FROM Wk_MrpItem where id = #{key})
          and Tenantid = #{tid}
    </update>

    <!--  查询往来单位是否被引用  -->
    <select id="getItemCiteBillName" resultType="string">
        (SELECT '采购合同' as billname From Buy_OrderItem where CiteItemid = #{key} and Tenantid = #{tid} LIMIT 1)
    </select>

    <!--查询单个-->
    <select id="getMrpItemEntity" resultType="inks.service.sa.som.domain.pojo.WkMrpitemPojo">
        SELECT Wk_MrpItem.id,
               Wk_MrpItem.Pid,
               Wk_MrpItem.ItemParentid,
               Wk_MrpItem.MrpObjid,
               Wk_MrpItem.LevelNum,
               Wk_MrpItem.LevelSymbol,
               Wk_MrpItem.Goodsid,
               Wk_MrpItem.ItemCode,
               Wk_MrpItem.ItemName,
               Wk_MrpItem.ItemSpec,
               Wk_MrpItem.ItemUnit,
               Wk_MrpItem.Bomid,
               Wk_MrpItem.BomType,
               Wk_MrpItem.BomItemid,
               Wk_MrpItem.SubQty,
               Wk_MrpItem.MainQty,
               Wk_MrpItem.LossRate,
               Wk_MrpItem.AttrCode,
               Wk_MrpItem.FlowCode,
               Wk_MrpItem.BomQty,
               Wk_MrpItem.StoQty,
               Wk_MrpItem.SafeStock,
               Wk_MrpItem.NeedQty,
               Wk_MrpItem.RealQty,
               Wk_MrpItem.WorkDate,
               Wk_MrpItem.PlanDate,
               Wk_MrpItem.Remark,
               Wk_MrpItem.EnabledMark,
               Wk_MrpItem.Closed,
               Wk_MrpItem.RowNum,
               Wk_MrpItem.BuyPlanQty,
               Wk_MrpItem.BuyOrderQty,
               Wk_MrpItem.CustSuppQty,
               Wk_MrpItem.WkWsQty,
               Wk_MrpItem.WkScQty,
               Wk_MrpItem.OtherQty,
               Wk_MrpItem.MatReqQty,
               Wk_MrpItem.MatCompQty,
               Wk_MrpItem.MatIvQty,
               Wk_MrpItem.BuyRemQty,
               Wk_MrpItem.WkWsRemQty,
               Wk_MrpItem.WkScRemQty,
               Wk_MrpItem.BusRemQty,
               Wk_MrpItem.MrpRemQty,
               Wk_MrpItem.FreeReqRemQty,
               Wk_MrpItem.Groupid,
               Wk_MrpItem.GroupName,
               Wk_MrpItem.MatReqRtQty,
               Wk_MrpItem.MatCompRtQty,
               Wk_MrpItem.WkFinishQty,
               Wk_MrpItem.BuyFinishQty,
               Wk_MrpItem.CustFinishQty,
               Wk_MrpItem.MachUid,
               Wk_MrpItem.MachItemid,
               Wk_MrpItem.MachBatch,
               Wk_MrpItem.MachGroupid,
               Wk_MrpItem.MainPlanUid,
               Wk_MrpItem.MainPlanItemid,
               Wk_MrpItem.Custom1,
               Wk_MrpItem.Custom2,
               Wk_MrpItem.Custom3,
               Wk_MrpItem.Custom4,
               Wk_MrpItem.Custom5,
               Wk_MrpItem.Custom6,
               Wk_MrpItem.Custom7,
               Wk_MrpItem.Custom8,
               Wk_MrpItem.Custom9,
               Wk_MrpItem.Custom10,
               Wk_MrpItem.Tenantid,
               Wk_MrpItem.TenantName,
               Wk_MrpItem.Revision,
               Mat_Goods.GoodsUid,
               Mat_Goods.GoodsName,
               Mat_Goods.GoodsSpec,
               Mat_Goods.GoodsUnit,
               Mat_Goods.Partid
        FROM Wk_MrpItem
                 LEFT JOIN Mat_Goods ON Wk_MrpItem.Goodsid = Mat_Goods.id
        where Wk_MrpItem.id = #{key}
          and Wk_MrpItem.Tenantid = #{tid}
    </select>

    <select id="getMrpItemListByMrpUid" resultType="inks.service.sa.som.domain.pojo.WkMrpitemPojo">
        SELECT Wk_MrpItem.id,
        Wk_MrpItem.Pid,
        Wk_MrpItem.ItemParentid,
        Wk_MrpItem.MrpObjid,
        Wk_MrpItem.LevelNum,
        Wk_MrpItem.LevelSymbol,
        Wk_MrpItem.Goodsid,
        Wk_MrpItem.ItemCode,
        Wk_MrpItem.ItemName,
        Wk_MrpItem.ItemSpec,
        Wk_MrpItem.ItemUnit,
        Wk_MrpItem.Bomid,
        Wk_MrpItem.BomType,
        Wk_MrpItem.BomItemid,
        Wk_MrpItem.SubQty,
        Wk_MrpItem.MainQty,
        Wk_MrpItem.LossRate,
        Wk_MrpItem.AttrCode,
        Wk_MrpItem.FlowCode,
        Wk_MrpItem.BomQty,
        Wk_MrpItem.StoQty,
        Wk_MrpItem.SafeStock,
        Wk_MrpItem.NeedQty,
        Wk_MrpItem.RealQty,
        Wk_MrpItem.WorkDate,
        Wk_MrpItem.PlanDate,
        Wk_MrpItem.Remark,
        Wk_MrpItem.EnabledMark,
        Wk_MrpItem.Closed,
        Wk_MrpItem.RowNum,
        Wk_MrpItem.BuyPlanQty,
        Wk_MrpItem.BuyOrderQty,
        Wk_MrpItem.CustSuppQty,
        Wk_MrpItem.WkWsQty,
        Wk_MrpItem.WkScQty,
        Wk_MrpItem.OtherQty,
        Wk_MrpItem.MatReqQty,
        Wk_MrpItem.MatCompQty,
        Wk_MrpItem.MatIvQty,
        Wk_MrpItem.BuyRemQty,
        Wk_MrpItem.WkWsRemQty,
        Wk_MrpItem.WkScRemQty,
        Wk_MrpItem.BusRemQty,
        Wk_MrpItem.MrpRemQty,
        Wk_MrpItem.FreeReqRemQty,
        Wk_MrpItem.Groupid,
        Wk_MrpItem.GroupName,
        Wk_MrpItem.MatReqRtQty,
        Wk_MrpItem.MatCompRtQty,
        Wk_MrpItem.WkFinishQty,
        Wk_MrpItem.BuyFinishQty,
        Wk_MrpItem.CustFinishQty,
        Wk_MrpItem.MachUid,
        Wk_MrpItem.MachItemid,
        Wk_MrpItem.MachBatch,
        Wk_MrpItem.MachGroupid,
        Wk_MrpItem.MainPlanUid,
        Wk_MrpItem.MainPlanItemid,
        Wk_MrpItem.Custom1,
        Wk_MrpItem.Custom2,
        Wk_MrpItem.Custom3,
        Wk_MrpItem.Custom4,
        Wk_MrpItem.Custom5,
        Wk_MrpItem.Custom6,
        Wk_MrpItem.Custom7,
        Wk_MrpItem.Custom8,
        Wk_MrpItem.Custom9,
        Wk_MrpItem.Custom10,
        Wk_MrpItem.Tenantid,
        Wk_MrpItem.TenantName,
        Wk_MrpItem.Revision,
        Mat_Goods.GoodsUid,
        Mat_Goods.GoodsName,
        Mat_Goods.GoodsSpec,
        Mat_Goods.GoodsUnit,
        Mat_Goods.Partid
        FROM Wk_MrpItem
        LEFT JOIN Mat_Goods ON Wk_MrpItem.Goodsid = Mat_Goods.id
        LEFT JOIN Wk_Mrp on Wk_MrpItem.Pid = Wk_Mrp.id
        where Wk_Mrp.RefNo = #{mrpUid}
        and Wk_MrpItem.AttrCode ='外购'
        and Wk_MrpItem.Tenantid = #{tid}
    </select>

    <update id="updateMergeCount">
        update Buy_Plan
        SET MergeCount = #{size}
        where id = #{id}
        and Tenantid = #{tid}
    </update>

    <update id="updateItemCount">
        update Buy_Plan
        SET ItemCount = (SELECT COUNT(*) FROM Buy_PlanItem where Pid = #{planid} and Tenantid = #{tid})
        where id = #{planid}
        and Tenantid = #{tid}
    </update>

    <select id="getMainPlanItem" resultType="inks.service.sa.som.domain.pojo.BuyPlanitemdetailPojo">
        <include refid="selectdetailVo"/>
        where Buy_PlanItem.Tenantid = #{tid}
        and Buy_PlanItem.id in
        <foreach collection="buyPlanItemIds" item="buyPlanItemId" open="(" close=")" separator=",">
            #{buyPlanItemId}
        </foreach>
    </select>

    <update id="syncMachingItemWkMergeInIds">
        update Bus_MachiningItem
        set BpMergeMark  = #{wkMergeMark},
        BpMergeItemid=#{insertPlanItemid}
        where Tenantid = #{tid}
        <if test="salveMachItemids != null and salveMachItemids.size() > 0">
            and id in
            <foreach collection="salveMachItemids" item="machItemId" open="(" close=")" separator=",">
                #{machItemId}
            </foreach>
        </if>
    </update>

    <update id="updateMrpBuyFinishCount">
        update Wk_Mrp
        SET BuyFinishCount =COALESCE((SELECT COUNT(0)
        FROM Wk_MrpItem
        where Wk_MrpItem.Pid =
        (SELECT Pid FROM Wk_MrpItem where id = #{key})
        and Wk_MrpItem.Tenantid = #{tid}
        and Wk_MrpItem.BuyPlanQty + Wk_MrpItem.BuyOrderQty >= Wk_MrpItem.NeedQty), 0)
        where id = (SELECT Pid FROM Wk_MrpItem where id = #{key})
        and Tenantid = #{tid}
    </update>

    <update id="updateOaflowmark">
        update Buy_Plan
        SET OaFlowMark = #{oaflowmark}
        where id = #{id}
        and Tenantid = #{tenantid}
    </update>

<!--    主表的billtaxamount，billamount，billtaxtotal-->
    <update id="syncBillAmount">
        UPDATE Buy_Plan bp
            JOIN (SELECT Pid,
                         SUM(Amount)    AS totalAmount,
                         SUM(TaxAmount) AS totalTaxAmount,
                         SUM(TaxTotal)  AS totalTax
                  FROM Buy_PlanItem
                  WHERE Pid = #{key}
                  AND Tenantid = #{tid}
                  GROUP BY Pid) bi ON bp.id = bi.Pid
        SET bp.billamount    = COALESCE(bi.totalAmount, 0),
            bp.billtaxamount = COALESCE(bi.totalTaxAmount, 0),
            bp.billtaxtotal  = COALESCE(bi.totalTax, 0)
        WHERE bp.id = #{key}
        AND bp.Tenantid = #{tid}
    </update>


</mapper>

