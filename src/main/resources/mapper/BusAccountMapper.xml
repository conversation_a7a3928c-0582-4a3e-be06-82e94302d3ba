<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.BusAccountMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.BusAccountPojo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               Bus_Account.id,
               Bus_Account.RefNo,
               Bus_Account.BillType,
               Bus_Account.BillDate,
               Bus_Account.BillTitle,
               Bus_Account.Projectid,
               Bus_Account.ProjName,
               Bus_Account.ProjCode,
               Bus_Account.Groupid,
               Bus_Account.CarryYear,
               Bus_Account.CarryMonth,
               Bus_Account.StartDate,
               Bus_Account.EndDate,
               Bus_Account.Operator,
               Bus_Account.Operatorid,
               Bus_Account.RowNum,
               Bus_Account.Summary,
               Bus_Account.CreateBy,
               Bus_Account.CreateByid,
               Bus_Account.CreateDate,
               Bus_Account.Lister,
               Bus_Account.Listerid,
               Bus_Account.ModifyDate,
               Bus_Account.BillOpenAmount,
               Bus_Account.BillInAmount,
               Bus_Account.BillOutAmount,
               Bus_Account.BillCloseAmount,
               Bus_Account.InvoOpenAmount,
               Bus_Account.InvoInAmount,
               Bus_Account.InvoOutAmount,
               Bus_Account.InvoCloseAmount,
               Bus_Account.ArapOpenAmount,
               Bus_Account.ArapInAmount,
               Bus_Account.ArapOutAmount,
               Bus_Account.ArapCloseAmount,
               Bus_Account.PrintCount,
               Bus_Account.Custom1,
               Bus_Account.Custom2,
               Bus_Account.Custom3,
               Bus_Account.Custom4,
               Bus_Account.Custom5,
               Bus_Account.Custom6,
               Bus_Account.Custom7,
               Bus_Account.Custom8,
               Bus_Account.Custom9,
               Bus_Account.Custom10,
               Bus_Account.Tenantid,
               Bus_Account.TenantName,
               Bus_Account.Revision
        FROM App_Workgroup
                 RIGHT JOIN Bus_Account ON Bus_Account.Groupid = App_Workgroup.id
        where Bus_Account.id = #{key}
          and Bus_Account.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate,
               App_Workgroup.Seller,
               Bus_Account.id,
               Bus_Account.RefNo,
               Bus_Account.BillType,
               Bus_Account.BillDate,
               Bus_Account.BillTitle,
               Bus_Account.Projectid,
               Bus_Account.ProjName,
               Bus_Account.ProjCode,
               Bus_Account.Groupid,
               Bus_Account.CarryYear,
               Bus_Account.CarryMonth,
               Bus_Account.StartDate,
               Bus_Account.EndDate,
               Bus_Account.Operator,
               Bus_Account.Operatorid,
               Bus_Account.RowNum,
               Bus_Account.Summary,
               Bus_Account.CreateBy,
               Bus_Account.CreateByid,
               Bus_Account.CreateDate,
               Bus_Account.Lister,
               Bus_Account.Listerid,
               Bus_Account.ModifyDate,
               Bus_Account.BillOpenAmount,
               Bus_Account.BillInAmount,
               Bus_Account.BillOutAmount,
               Bus_Account.BillCloseAmount,
               Bus_Account.InvoOpenAmount,
               Bus_Account.InvoInAmount,
               Bus_Account.InvoOutAmount,
               Bus_Account.InvoCloseAmount,
               Bus_Account.ArapOpenAmount,
               Bus_Account.ArapInAmount,
               Bus_Account.ArapOutAmount,
               Bus_Account.ArapCloseAmount,
               Bus_Account.PrintCount,
               Bus_Account.Custom1,
               Bus_Account.Custom2,
               Bus_Account.Custom3,
               Bus_Account.Custom4,
               Bus_Account.Custom5,
               Bus_Account.Custom6,
               Bus_Account.Custom7,
               Bus_Account.Custom8,
               Bus_Account.Custom9,
               Bus_Account.Custom10,
               Bus_Account.Tenantid,
               Bus_Account.TenantName,
               Bus_Account.Revision
        FROM App_Workgroup
                 RIGHT JOIN Bus_Account ON Bus_Account.Groupid = App_Workgroup.id
    </sql>
    <sql id="selectdetailVo">
        SELECT Bus_AccountItem.id,
               Bus_AccountItem.Pid,
               Bus_AccountItem.Direction,
               Bus_AccountItem.ModuleCode,
               Bus_AccountItem.BillType,
               Bus_AccountItem.BillDate,
               Bus_AccountItem.BillTitle,
               Bus_AccountItem.BillUid,
               Bus_AccountItem.Billid,
               Bus_AccountItem.OpenAmount,
               Bus_AccountItem.InAmount,
               Bus_AccountItem.OutAmount,
               Bus_AccountItem.CloseAmount,
               Bus_AccountItem.RowNum,
               Bus_AccountItem.Remark,
               Bus_AccountItem.Custom1,
               Bus_AccountItem.Custom2,
               Bus_AccountItem.Custom3,
               Bus_AccountItem.Custom4,
               Bus_AccountItem.Custom5,
               Bus_AccountItem.Custom6,
               Bus_AccountItem.Custom7,
               Bus_AccountItem.Custom8,
               Bus_AccountItem.Custom9,
               Bus_AccountItem.Custom10,
               Bus_AccountItem.Tenantid,
               Bus_AccountItem.Revision,
               Bus_Account.RefNo,
               Bus_Account.BillType,
               Bus_Account.BillDate,
               Bus_Account.BillTitle,
               Bus_Account.Projectid,
               Bus_Account.ProjName,
               Bus_Account.ProjCode,
               Bus_Account.CarryYear,
               Bus_Account.CarryMonth,
               Bus_Account.StartDate,
               Bus_Account.EndDate,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        FROM Bus_Account
                 RIGHT JOIN Bus_AccountItem ON Bus_AccountItem.Pid = Bus_Account.id
                 LEFT JOIN App_Workgroup ON App_Workgroup.id = Bus_Account.Groupid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusAccountitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and Bus_Account.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Account.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null ">
            and Bus_Account.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_Account.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_Account.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_Account.groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_Account.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Bus_Account.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_Account.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_Account.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_Account.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_Account.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_Account.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_Account.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_Account.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_Account.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_Account.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_Account.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_Account.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_Account.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_Account.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_Account.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_Account.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_Account.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_Account.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_Account.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_Account.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_Account.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_Account.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Bus_Account.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_Account.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_Account.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_Account.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_Account.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_Account.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_Account.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_Account.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_Account.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_Account.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_Account.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_Account.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_Account.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_Account.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_Account.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_Account.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_Account.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusAccountPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and Bus_Account.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Bus_Account.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null ">
            and Bus_Account.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null ">
            and Bus_Account.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null ">
            and Bus_Account.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null ">
            and Bus_Account.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.operator != null ">
            and Bus_Account.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null ">
            and Bus_Account.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Bus_Account.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Bus_Account.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Bus_Account.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Bus_Account.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Bus_Account.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Bus_Account.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Bus_Account.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Bus_Account.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Bus_Account.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Bus_Account.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and Bus_Account.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and Bus_Account.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and Bus_Account.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and Bus_Account.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and Bus_Account.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Bus_Account.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgroupandfilter"/>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null ">
                or Bus_Account.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null ">
                or Bus_Account.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null ">
                or Bus_Account.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null ">
                or Bus_Account.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.operator != null ">
                or Bus_Account.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null ">
                or Bus_Account.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Bus_Account.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Bus_Account.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Bus_Account.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Bus_Account.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Bus_Account.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Bus_Account.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Bus_Account.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Bus_Account.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Bus_Account.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Bus_Account.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or Bus_Account.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or Bus_Account.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or Bus_Account.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or Bus_Account.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or Bus_Account.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Bus_Account.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <include refid="inks.service.sa.som.mapper.ExtFilterMapper.workgrouporfilter"/>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Bus_Account(id, RefNo, BillType, BillDate,Projectid, ProjCode, ProjName,  BillTitle, Groupid, CarryYear, CarryMonth, StartDate,
                                EndDate, Operator, Operatorid, RowNum, Summary, CreateBy, CreateByid, CreateDate,
                                Lister,
                                Listerid, ModifyDate, BillOpenAmount, BillInAmount, BillOutAmount, BillCloseAmount,
                                InvoOpenAmount, InvoInAmount, InvoOutAmount, InvoCloseAmount,
                                ArapOpenAmount, ArapInAmount, ArapOutAmount, ArapCloseAmount,
                                PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
                                Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate},#{projectid}, #{projcode}, #{projname},  #{billtitle}, #{groupid}, #{carryyear}, #{carrymonth},
                #{startdate}, #{enddate}, #{operator}, #{operatorid}, #{rownum}, #{summary}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{billopenamount}, #{billinamount},
                #{billoutamount}, #{billcloseamount}, #{invoopenamount}, #{invoinamount}, #{invooutamount},
                #{invocloseamount}, #{arapopenamount}, #{arapinamount}, #{arapoutamount}, #{arapcloseamount},
                #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Bus_Account
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projcode != null ">
                ProjCode =#{projcode},
            </if>
            <if test="projname != null ">
                ProjName =#{projname},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="carryyear != null">
                CarryYear =#{carryyear},
            </if>
            <if test="carrymonth != null">
                CarryMonth =#{carrymonth},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="rownum != null ">
                RowNum =#{rownum},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="billopenamount != null">
                BillOpenAmount =#{billopenamount},
            </if>
            <if test="billinamount != null">
                BillInAmount =#{billinamount},
            </if>
            <if test="billoutamount != null">
                BillOutAmount =#{billoutamount},
            </if>
            <if test="billcloseamount != null">
                BillCloseAmount =#{billcloseamount},
            </if>
            <if test="invoopenamount != null">
                InvoOpenAmount =#{invoopenamount},
            </if>
            <if test="invoinamount != null">
                InvoInAmount =#{invoinamount},
            </if>
            <if test="invooutamount != null">
                InvoOutAmount =#{invooutamount},
            </if>
            <if test="invocloseamount != null">
                InvoCloseAmount =#{invocloseamount},
            </if>
            <if test="arapopenamount != null">
                ArapOpenAmount =#{arapopenamount},
            </if>
            <if test="arapinamount != null">
                ArapInAmount =#{arapinamount},
            </if>
            <if test="arapoutamount != null">
                ArapOutAmount =#{arapoutamount},
            </if>
            <if test="arapcloseamount != null">
                ArapCloseAmount =#{arapcloseamount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Bus_Account
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--通过主键删除-->
    <delete id="deleteByMonth">
        delete
        from Bus_Account
        where CarryYear = #{year}
          and CarryMonth = #{nonth}
          and Tenantid = #{tid}
    </delete>

    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BusAccountPojo">
        select
        id
        from Bus_AccountItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <!--查询DelInvoIds-->
    <select id="getDelInvoIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BusAccountPojo">
        select
        id
        from Bus_AccountInvo
        where Pid = #{id}
        <if test="invo !=null and invo.size()>0">
            and id not in
            <foreach collection="invo" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>
    <!--查询DelArapIds-->
    <select id="getDelArapIds" resultType="java.lang.String"
            parameterType="inks.service.sa.som.domain.pojo.BusAccountPojo">
        select
        id
        from Bus_AccountArap
        where Pid = #{id}
        <if test="arap !=null and arap.size()>0">
            and id not in
            <foreach collection="arap" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>


    <!--查询单个-->
    <select id="getCustomerIds" resultType="java.lang.String">
        select id
        from App_Workgroup
        where  App_Workgroup.DeleteMark = 0
          and GroupType = '客户'
          and App_Workgroup.Tenantid = #{tid}
    </select>

    <!--查询单个-->
    <select id="getMaxEntityByGroup" resultType="inks.service.sa.som.domain.pojo.BusAccountPojo">
        <include refid="selectbillVo"/>
        where Bus_Account.Groupid = #{key}
        and Bus_Account.Tenantid = #{tid}
        Order by EndDate Desc LIMIT 1
    </select>

    <!--查询指定行数据-->
    <select id="getMultItemList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusAccountitemPojo">
        <include refid="selectdetailVo"/>
        where Bus_Account.Tenantid =#{tenantid}
        and Bus_AccountItem.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        ${filterstr}
        order by Bus_AccountItem.BillDate
    </select>

    <!--查询指定行数据-->
    <select id="getNowPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusAccountPojo">
        SELECT * FROM (SELECT
        App_Workgroup.id,
        App_Workgroup.WgGroupid,
        App_Workgroup.GroupUid,
        App_Workgroup.GroupName,
        App_Workgroup.Abbreviate,
        App_Workgroup.Seller,
        COALESCE((SELECT
        Bus_Account.BillCloseAmount
        FROM Bus_Account
        WHERE Bus_Account.Groupid=App_Workgroup.id
        Order By RowNum Desc LIMIT 1),0) as BillOpenAmount,
        COALESCE((SELECT
        sum(CASE
        WHEN Bus_Deliery.BillType IN ('发出商品','其他发货','返工补发') THEN Bus_Deliery.BillTaxAmount
        ELSE 0 - Bus_Deliery.BillTaxAmount END)
        FROM Bus_Deliery where Bus_Deliery.Groupid=App_Workgroup.id
        and Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        ),0)-
        COALESCE((SELECT Sum(Bus_Deduction.BillTaxAmount) FROM Bus_Deduction
        where Bus_Deduction.Groupid=App_Workgroup.id
        and Bus_Deduction.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0)
        AS BillInAmount,
        COALESCE((SELECT
        Sum(Bus_Deposit.BillAmount)
        FROM Bus_Deposit WHERE Bus_Deposit.Groupid=App_Workgroup.id
        and Bus_Deposit.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0)+
        COALESCE((SELECT
        Sum(Bus_Receipt.BillAmount)
        FROM Bus_Receipt WHERE Bus_Receipt.Groupid=App_Workgroup.id
        and Bus_Receipt.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0) as BillOutAmount,
        COALESCE((SELECT
        Bus_Account.BillCloseAmount
        FROM Bus_Account
        WHERE Bus_Account.Groupid=App_Workgroup.id
        Order By RowNum Desc LIMIT 1),0) +
        COALESCE((SELECT
        sum(CASE
        WHEN Bus_Deliery.BillType IN ('发出商品','其他发货','返工补发') THEN Bus_Deliery.BillTaxAmount
        ELSE 0 - Bus_Deliery.BillTaxAmount END)
        FROM Bus_Deliery where Bus_Deliery.Groupid=App_Workgroup.id
        and Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        ),0)-
        COALESCE((SELECT Sum(Bus_Deduction.BillTaxAmount) FROM Bus_Deduction
        where Bus_Deduction.Groupid=App_Workgroup.id
        and Bus_Deduction.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0)
        -
        COALESCE((SELECT
        Sum(Bus_Deposit.BillAmount)
        FROM Bus_Deposit WHERE Bus_Deposit.Groupid=App_Workgroup.id
        and Bus_Deposit.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0)-
        COALESCE((SELECT
        Sum(Bus_Receipt.BillAmount)
        FROM Bus_Receipt WHERE Bus_Receipt.Groupid=App_Workgroup.id
        and Bus_Receipt.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}),0) AS BillCloseAmount
        FROM
        App_Workgroup
        where App_Workgroup.EnabledMark = 1
        and App_Workgroup.DeleteMark = 0
        and GroupType='客户'
        and App_Workgroup.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        ) t
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>

    <!--查询指定行数据-->
    <select id="pullItemList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.som.domain.pojo.BusAccountitemPojo">
        SELECT
        'in' as Direction,
        'D01M09' as ModuleCode,
        Bus_Deduction.id as Billid,
        Bus_Deduction.RefNo as BillUid,
        Bus_Deduction.BillType,
        Bus_Deduction.BillTitle,
        Bus_Deduction.BillDate,
        0.0 as OpenAmount,
        0 - Bus_Deduction.BillTaxAmount as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount
        FROM Bus_Deduction
        where (Bus_Deduction.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Deduction.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'in' as Direction,
        'D01M06' as ModuleCode,
        Bus_Deliery.id as Billid,
        Bus_Deliery.RefNo as BillUid,
        Bus_Deliery.BillType,
        Bus_Deliery.BillTitle,
        Bus_Deliery.BillDate,
        0.0 as OpenAmount,
        (CASE WHEN Bus_Deliery.BillType IN ('发出商品','其他发货','返工补发') THEN Bus_Deliery.BillTaxAmount
        ELSE 0 - Bus_Deliery.BillTaxAmount END) as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount
        FROM Bus_Deliery
        where (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Deliery.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D01M08' as ModuleCode,
        Bus_Receipt.id as Billid,
        Bus_Receipt.RefNo as BillUid,
        Bus_Receipt.BillType,
        Bus_Receipt.BillTitle,
        Bus_Receipt.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Bus_Receipt.BillAmount as OutAmount,
        0.0 as CloseAmount
        FROM Bus_Receipt
        where (Bus_Receipt.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Receipt.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D01M08DEP' as ModuleCode,
        Bus_Deposit.id as Billid,
        Bus_Deposit.RefNo as BillUid,
        Bus_Deposit.BillType,
        Bus_Deposit.BillTitle,
        Bus_Deposit.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Bus_Deposit.BillAmount as OutAmount,
        0.0 as CloseAmount
        FROM Bus_Deposit
        where (Bus_Deposit.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Deposit.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
    </select>


    <select id="pullInvoList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.som.domain.pojo.BusAccountinvoPojo">
        SELECT
        'in' as Direction,
        'D01M09' as ModuleCode,
        Bus_Deduction.id as Billid,
        Bus_Deduction.RefNo as BillUid,
        Bus_Deduction.BillType,
        Bus_Deduction.BillTitle,
        Bus_Deduction.BillDate,
        0.0 as OpenAmount,
        0 - Bus_Deduction.BillTaxAmount as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount
        FROM Bus_Deduction
        where (Bus_Deduction.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Deduction.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'in' as Direction,
        'D01M06' as ModuleCode,
        Bus_Deliery.id as Billid,
        Bus_Deliery.RefNo as BillUid,
        Bus_Deliery.BillType,
        Bus_Deliery.BillTitle,
        Bus_Deliery.BillDate,
        0.0 as OpenAmount,
        (CASE WHEN Bus_Deliery.BillType IN ('发出商品','其他发货','返工补发') THEN Bus_Deliery.BillTaxAmount
        ELSE 0 - Bus_Deliery.BillTaxAmount END) as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount
        FROM Bus_Deliery
        where (Bus_Deliery.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Deliery.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D01M05' as ModuleCode,
        Bus_Invoice.id as Billid,
        Bus_Invoice.RefNo as BillUid,
        Bus_Invoice.BillType,
        Bus_Invoice.BillTitle,
        Bus_Invoice.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Bus_Invoice.TaxAmount as OutAmount,
        0.0 as CloseAmount
        FROM Bus_Invoice
        where (Bus_Invoice.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Invoice.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
    </select>


    <select id="pullArapList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.som.domain.pojo.BusAccountarapPojo">
        SELECT
        'in' as Direction,
        'D01M05' as ModuleCode,
        Bus_Invoice.id as Billid,
        Bus_Invoice.RefNo as BillUid,
        Bus_Invoice.BillType,
        Bus_Invoice.BillTitle,
        Bus_Invoice.BillDate,
        0.0 as OpenAmount,
        Bus_Invoice.TaxAmount as InAmount,
        0.0 as OutAmount,
        0.0 as CloseAmount
        FROM Bus_Invoice
        where (Bus_Invoice.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Invoice.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D01M08' as ModuleCode,
        Bus_Receipt.id as Billid,
        Bus_Receipt.RefNo as BillUid,
        Bus_Receipt.BillType,
        Bus_Receipt.BillTitle,
        Bus_Receipt.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Bus_Receipt.BillAmount as OutAmount,
        0.0 as CloseAmount
        FROM Bus_Receipt
        where (Bus_Receipt.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Receipt.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        UNION ALL
        SELECT
        'out' as Direction,
        'D01M08DEP' as ModuleCode,
        Bus_Deposit.id as Billid,
        Bus_Deposit.RefNo as BillUid,
        Bus_Deposit.BillType,
        Bus_Deposit.BillTitle,
        Bus_Deposit.BillDate,
        0.0 as OpenAmount,
        0.0 as InAmount,
        Bus_Deposit.BillAmount as OutAmount,
        0.0 as CloseAmount
        FROM Bus_Deposit
        where (Bus_Deposit.BillDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
        and Bus_Deposit.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
    </select>


</mapper>

