<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.som.mapper.FmDepotransferitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.som.domain.pojo.FmDepotransferitemPojo">
        <include refid="selectFmDepotransferitemVo"/>
        where Fm_DepoTransferItem.id = #{key} 
    </select>
    <sql id="selectFmDepotransferitemVo">
        select Fm_DepoTransferItem.id,
               Fm_DepoTransferItem.Pid,
               Fm_DepoTransferItem.PayBillid,
               Fm_DepoTransferItem.PayRefNo,
               Fm_DepoTransferItem.PayGroupid,
               Fm_DepoTransferItem.PayAmount,
               Fm_DepoTransferItem.Amount,
               Fm_DepoTransferItem.RowNum,
               Fm_DepoTransferItem.Remark,
               Fm_DepoTransferItem.Custom1,
               Fm_DepoTransferItem.Custom2,
               Fm_DepoTransferItem.Custom3,
               Fm_DepoTransferItem.Custom4,
               Fm_DepoTransferItem.Custom5,
               Fm_DepoTransferItem.Custom6,
               Fm_DepoTransferItem.Custom7,
               Fm_DepoTransferItem.Custom8,
               Fm_DepoTransferItem.Custom9,
               Fm_DepoTransferItem.Custom10,
               Fm_DepoTransferItem.Tenantid,
               Fm_DepoTransferItem.Revision,
               App_Workgroup.GroupUid,
               App_Workgroup.GroupName,
               App_Workgroup.Abbreviate
        from Fm_DepoTransferItem
                 left join App_Workgroup on Fm_DepoTransferItem.PayGroupid = App_Workgroup.id
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.som.domain.pojo.FmDepotransferitemPojo">
        <include refid="selectFmDepotransferitemVo"/>
        where Fm_DepoTransferItem.Pid = #{Pid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.som.domain.pojo.FmDepotransferitemPojo">
        <include refid="selectFmDepotransferitemVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Fm_DepoTransferItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Fm_DepoTransferItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.paybillid != null and SearchPojo.paybillid != ''">
   and Fm_DepoTransferItem.paybillid like concat('%', #{SearchPojo.paybillid}, '%')
</if>
<if test="SearchPojo.payrefno != null and SearchPojo.payrefno != ''">
   and Fm_DepoTransferItem.payrefno like concat('%', #{SearchPojo.payrefno}, '%')
</if>
<if test="SearchPojo.paygroupid != null and SearchPojo.paygroupid != ''">
   and Fm_DepoTransferItem.paygroupid like concat('%', #{SearchPojo.paygroupid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Fm_DepoTransferItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Fm_DepoTransferItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Fm_DepoTransferItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Fm_DepoTransferItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Fm_DepoTransferItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Fm_DepoTransferItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Fm_DepoTransferItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Fm_DepoTransferItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Fm_DepoTransferItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Fm_DepoTransferItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Fm_DepoTransferItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Fm_DepoTransferItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.paybillid != null and SearchPojo.paybillid != ''">
   or Fm_DepoTransferItem.PayBillid like concat('%', #{SearchPojo.paybillid}, '%')
</if>
<if test="SearchPojo.payrefno != null and SearchPojo.payrefno != ''">
   or Fm_DepoTransferItem.PayRefNo like concat('%', #{SearchPojo.payrefno}, '%')
</if>
<if test="SearchPojo.paygroupid != null and SearchPojo.paygroupid != ''">
   or Fm_DepoTransferItem.PayGroupid like concat('%', #{SearchPojo.paygroupid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Fm_DepoTransferItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Fm_DepoTransferItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Fm_DepoTransferItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Fm_DepoTransferItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Fm_DepoTransferItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Fm_DepoTransferItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Fm_DepoTransferItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Fm_DepoTransferItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Fm_DepoTransferItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Fm_DepoTransferItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Fm_DepoTransferItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Fm_DepoTransferItem(id, Pid, PayBillid, PayRefNo, PayGroupid, PayAmount, Amount, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{paybillid}, #{payrefno}, #{paygroupid}, #{payamount}, #{amount}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Fm_DepoTransferItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="paybillid != null ">
                PayBillid = #{paybillid},
            </if>
            <if test="payrefno != null ">
                PayRefNo = #{payrefno},
            </if>
            <if test="paygroupid != null ">
                PayGroupid = #{paygroupid},
            </if>
            <if test="payamount != null">
                PayAmount = #{payamount},
            </if>
            <if test="amount != null">
                Amount = #{amount},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Fm_DepoTransferItem where id = #{key} 
    </delete>

</mapper>

