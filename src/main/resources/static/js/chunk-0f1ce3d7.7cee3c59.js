(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0f1ce3d7"],{"113a":function(t,e,a){},"3ace":function(t,e,a){"use strict";a("113a")},"3bb4":function(t,e,a){},"4c0c":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selStore",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?a("el-table-column",{attrs:{fixed:"left",type:"selection",width:"55"}}):a("el-table-column",{attrs:{label:"",width:"40",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index,size:"small"},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" ")+" ")])]}}])}),a("el-table-column",{attrs:{label:"仓库编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storecode))])]}}])}),a("el-table-column",{attrs:{label:"仓库名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storename))])]}}])}),a("el-table-column",{attrs:{label:"仓库地址",align:"center","min-width":"150","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storeadd))])]}}])}),a("el-table-column",{attrs:{label:"仓管员",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.operator))])]}}])}),a("el-table-column",{attrs:{label:"电话",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storetel))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},o=[],s=(a("e9c4"),a("b775")),n=a("333d"),l={components:{Pagination:n["a"]},props:["multi","storeid"],data:function(){return{title:"选择仓库",listLoading:!0,lst:[],strfilter:"",radio:"",selrows:"",total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},watch:{},mounted:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.$emit("singleSel",t),this.selrows=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,this.storeid&&(this.queryParams.SearchPojo={storeid:this.storeid}),s["a"].post("/D04M21S1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={storename:t,storecode:t,operator:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},r=l,c=(a("6dd7"),a("2877")),d=Object(c["a"])(r,i,o,!1,null,"655e468e",null);e["a"]=d.exports},"57a0":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:t.multi?"415px":"352px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsuid))])]}}])}),a("el-table-column",{attrs:{label:"名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(" "+t._s(e.row.goodsname))])]}}])}),a("el-table-column",{attrs:{label:"规格",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsspec))])]}}])}),a("el-table-column",{attrs:{label:"单位",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsunit))])]}}])}),a("el-table-column",{attrs:{label:"外部编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.partid))])]}}])}),a("el-table-column",{attrs:{label:"数量",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.quantity))])]}}])}),a("el-table-column",{attrs:{label:"仓库名称",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storename))])]}}])}),a("el-table-column",{attrs:{label:"库位编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.location))])]}}])}),a("el-table-column",{attrs:{label:"入库时间",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormats")(e.row.endindata)))])]}}])}),a("el-table-column",{attrs:{label:"批号",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.batchno))])]}}])}),t._l(t.customList,(function(t,e){return[a("el-table-column",{key:e,attrs:{"header-align":"center",align:"center",prop:t.attrkey,label:t.attrname}})]}))],2)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},o=[],s=a("c7eb"),n=a("1da1"),l=(a("e9c4"),a("b64b"),a("b775")),r=a("333d"),c={components:{Pagination:r["a"]},props:["multi","storeid"],data:function(){return{title:"库存信息",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},customList:[]}},created:function(){this.searchstr=""},mounted:function(){this.bindData()},methods:{getCurrentRow:function(t){console.log(t),this.$forceUpdate(),this.selrows=t,this.$emit("singleSel")},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;return Object(n["a"])(Object(s["a"])().mark((function e(){var a;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a="/D04M04B1/getPageList",t.storeid&&(t.queryParams.SearchType=1,a="/D04M04B1/getOnlinePageList?storeid="+t.storeid),t.listLoading=!0,e.next=5,l["a"].post(a,JSON.stringify(t.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total?e.data.data.total:0;for(var a=0;a<t.lst.length;a++)for(var i=t.lst[a],o=i.attributejson?JSON.parse(i.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[a],o[s].key,o[s].value)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 5:return e.next=7,l["a"].get("/D91M01S2/getListByShow").then((function(e){console.log(e,"ss"),200==e.data.code?t.customList=e.data.data?e.data.data:[]:t.$message.warning(e.data.msg||"获取spu失败")})).catch((function(e){t.$message.error("请求错误")}));case 7:case"end":return e.stop()}}),e)})))()},search:function(t){""!=t?this.queryParams.SearchPojo={packsn:t,goodsname:t,goodsspec:t,goodsuid:t,location:t,batchno:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},d=c,u=(a("61a7"),a("2877")),m=Object(u["a"])(d,i,o,!1,null,"3911a3bc",null);e["a"]=m.exports},"5cc6":function(t,e,a){var i=a("74e8");i("Uint8",(function(t){return function(e,a,i){return t(this,e,a,i)}}))},"5e89":function(t,e,a){},"5f1f":function(t,e,a){},"61a7":function(t,e,a){"use strict";a("e793")},"6dd7":function(t,e,a){"use strict";a("5f1f")},7859:function(t,e,a){},"7ada":function(t,e,a){"use strict";a("7859")},"7e49":function(t,e,a){},8885:function(t,e,a){},9888:function(t,e,a){"use strict";a("7e49")},"9bda":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")]),a("div",{staticStyle:{float:"right"}},[a("el-radio-group",{attrs:{size:"small"},on:{change:function(e){return t.getStatus()}},model:{value:t.goodsType,callback:function(e){t.goodsType=e},expression:"goodsType"}},[a("el-radio-button",{attrs:{label:"物料"}}),a("el-radio-button",{attrs:{label:"半成品"}}),a("el-radio-button",{attrs:{label:"成品"}})],1)],1)],1),a("div",{staticStyle:{"margin-bottom":"10px"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectgoods",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",height:"380px",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsuid))])]}}])}),a("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsname))])]}}])}),a("el-table-column",{attrs:{label:"货品规格",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsspec))])]}}])}),a("el-table-column",{attrs:{label:"货品状态",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsstate))])]}}])}),a("el-table-column",{attrs:{label:"单位",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsunit))])]}}])}),a("el-table-column",{attrs:{label:"外部编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.partid))])]}}])}),a("el-table-column",{attrs:{label:"价格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.outprice))])]}}])}),a("el-table-column",{attrs:{label:"当前库存",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.ivquantity))])]}}])})],1)],1),a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],s=(a("e9c4"),a("b775")),n=a("333d"),l={components:{Pagination:n["a"]},props:["multi","groupid","goodsstate"],data:function(){return{title:"货品信息",listLoading:!1,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},goodsType:"成品",goodsVal:"p"}},created:function(){this.searchstr="",this.multi&&this.bindData()},methods:{getCurrentRow:function(t){console.log(t),this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.groupid){var e={groupid:this.groupid};this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e}if(this.goodsstate){e={goodsstate:this.goodsstate};this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e}s["a"].post("/D91M01B1/getOnlinePageList?state="+this.goodsVal,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={goodsuid:t,goodsname:t,goodsunit:t,groupid:t,goodsspec:t,partid:t,goodsstate:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},getStatus:function(){this.strfilter="",this.$delete(this.queryParams,"SearchPojo"),"成品"==this.goodsType?this.goodsVal="p":"半成品"==this.goodsType?this.goodsVal="s":"物料"==this.goodsType&&(this.goodsVal="m"),this.bindData()},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},r=l,c=(a("ee58"),a("2877")),d=Object(c["a"])(r,i,o,!1,null,"b16f8594",null);e["a"]=d.exports},"9d7d":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):a("div",{ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,btnHelp:t.btnHelp,btnExport:t.btnExport,bindColumn:t.getColumn}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:t.showhelp?20:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"table-container",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"1px 0px 1px 0px"},height:t.tableMaxHeight},on:{"sort-change":t.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["refno"==e.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showForm(i.row.id)}}},[t._v(t._s(i.row.refno?i.row.refno:"单据编号"))]):"billdate"==e.itemcode||"createdate"==e.itemcode||"modifydate"==e.itemcode||"assessordate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))]):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1),a("el-col",{attrs:{span:t.showhelp?4:0}},[a("helpmodel",{ref:"helpmodel",attrs:{code:"D04M12B1"}})],1)],1)],1)],1)])},o=[],s=(a("99af"),a("d81d"),a("e9c4"),a("d3b7"),a("25f0"),a("4d90"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",{staticStyle:{display:"flex"}},[a("div",{staticClass:"flex infoForm"},[a("span",{staticClass:"infoForm-Title"},[t._v("时间范围")]),a("el-date-picker",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"unlink-panels":"",size:"mini","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:t.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(e){t.iShow=!t.iShow}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-help",title:"帮助"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"编码"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入编码",size:"small"},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单据标题"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入单据标题",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"调入仓库"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入调入仓库",size:"small"},model:{value:t.formdata.instorename,callback:function(e){t.$set(t.formdata,"instorename",e)},expression:"formdata.instorename"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"调出仓库"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入调出仓库",size:"small"},model:{value:t.formdata.outstorename,callback:function(e){t.$set(t.formdata,"outstorename",e)},expression:"formdata.outstorename"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.advancedSearch()}}},[t._v(" 搜索 ")])],1)],1)],1)],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("Setcolums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)}),n=[],l=a("b893"),r={name:"Listheader",props:["tableForm"],components:{},data:function(){return{strfilter:"",iShow:!1,formdata:{},dateRange:Object(l["d"])(),pickerOptions:Object(l["h"])(),setColumsVisible:!1,code:"D04M12B1Th"}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(){this.iShow=!1;var t={dateRange:this.dateRange,formdata:this.formdata};this.$emit("advancedSearch",t)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){var t={dateRange:this.dateRange,strfilter:this.strfilter};this.$emit("btnSearch",t)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")}}},c=r,d=(a("7ada"),a("2877")),u=Object(d["a"])(c,s,n,!1,null,"1c314ec8",null),m=u.exports,f=a("333d"),h=a("b775"),p=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{disabled:!t.formdata.id,type:"primary",size:"small"},nativeOn:{click:function(e){return t.printButton()}}},[t._v("打 印")]),a("el-dropdown",{staticStyle:{"margin-left":"10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!0},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")])],1)],1),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("过程"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"})],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("div",{staticClass:"flex j-end p-a",staticStyle:{top:"5px",right:"0"}},[a("div",{staticClass:"refNo flex j-end"},[a("div",{staticClass:"refNo-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),a("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini"},model:{value:t.formdata.billdate,callback:function(e){t.$set(t.formdata,"billdate",e)},expression:"formdata.billdate"}})],1),a("div",[a("span",[t._v("NO：")]),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini",readonly:!!t.idx},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)]),a("div",{staticStyle:{display:"flex","justify-content":"flex-end"}},[a("div",{staticStyle:{"margin-right":"10px",width:"100px"}},[t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount?a("svg-icon",{staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"finish1"}}):t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount?a("svg-icon",{staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"revoke"}}):t._e()],1),a("div",{staticStyle:{"margin-right":"10px",width:"90px"}},[a("svg-icon",{directives:[{name:"show",rawName:"v-show",value:!!t.formdata.assessor,expression:"!!formdata.assessor"}],staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"approve"}})],1)])])]),a("el-form",{ref:"formdata",staticClass:"custInfo",staticStyle:{"min-height":"110px"},attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{"allow-create":"",filterable:"","default-first-option":"",disabled:!!t.idx,placeholder:"请选择单据类型",size:"small"},on:{change:t.changeBilltype},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},t._l(t.options,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("outstoreid")}}},[a("el-form-item",{attrs:{label:"调出仓库",prop:"outstoreid"}},[a("el-popover",{attrs:{placement:"bottom-start",trigger:"click"},on:{show:function(e){return t.$refs.selOutStore.bindData()}},model:{value:t.selVisible4Out,callback:function(e){t.selVisible4Out=e},expression:"selVisible4Out"}},[a("selstore",{ref:"selOutStore",staticStyle:{width:"600px",height:"420px"},attrs:{multi:t.multi},on:{singleSel:t.SelOutStore}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{staticStyle:{"min-width":"140px"},attrs:{placeholder:"调出仓库",size:"small",readonly:""},on:{change:t.handleBlur},model:{value:t.formdata.outstorename,callback:function(e){t.$set(t.formdata,"outstorename",e)},expression:"formdata.outstorename"}},[a("i",{staticClass:"el-icon-circle-plus-outline",staticStyle:{"font-size":"15px",color:"rgb(64, 158, 255)"},attrs:{slot:"suffix"},slot:"suffix"})])],1)],1)],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("instoreid")}}},[a("el-form-item",{attrs:{label:"调入仓库",prop:"instoreid"}},[a("el-popover",{attrs:{placement:"bottom-start",trigger:"click"},on:{show:function(e){return t.$refs.selInStore.bindData()}},model:{value:t.selVisible4In,callback:function(e){t.selVisible4In=e},expression:"selVisible4In"}},[a("selstore",{ref:"selInStore",staticStyle:{width:"600px",height:"420px"},attrs:{multi:t.multi},on:{singleSel:t.SelInStore}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{staticStyle:{"min-width":"140px"},attrs:{placeholder:"调入仓库",size:"small",readonly:""},on:{change:t.handleBlur},model:{value:t.formdata.instorename,callback:function(e){t.$set(t.formdata,"instorename",e)},expression:"formdata.instorename"}},[a("i",{staticClass:"el-icon-circle-plus-outline",staticStyle:{"font-size":"15px",color:"rgb(64, 158, 255)"},attrs:{slot:"suffix"},slot:"suffix"})])],1)],1)],1)],1)])],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[t._v("发货明细")])],1),a("div",{staticClass:"form-body form f-1"},[a("elitemByOut",{ref:"outitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.outitem,formdata:t.formdata,idx:t.idx,istotal:t.istotal}})],1),a("div",{staticClass:"form"},[a("el-divider",{attrs:{"content-position":"left"}},[t._v("入货明细")])],1),a("div",{staticClass:"form-body form f-1"},[a("elitem",{ref:"initem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.initem,formdata:t.formdata,idx:t.idx,istotal:t.istotal},on:{settingData:t.settingData,copydata:t.copydata}})],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:18}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.summary,callback:function(e){t.$set(t.formdata,"summary",e)},expression:"formdata.summary"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"经办人员"}},[a("el-input",{attrs:{placeholder:"请输入经办人员",clearable:"",size:"small"},model:{value:t.formdata.operator,callback:function(e){t.$set(t.formdata,"operator",e)},expression:"formdata.operator"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"审核"}},[a("span",{staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.assessor))])])],1),a("el-col",{directives:[{name:"show",rawName:"v-show",value:t.formdata.assessor,expression:"formdata.assessor"}],attrs:{span:4}},[a("el-form-item",{attrs:{label:"审核日期"}},[a("span",{staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.assessdate)))])])],1)],1)],1)],1)]),a("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D04M12B1Edit",commonurl:"/D04M12B1/printBill",weburl:"/D04M12B1/printWebBill"}})],1)},g=[],b=a("c7eb"),w=a("1da1"),v=a("2909");a("b64b");const y={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/D04M12B1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},addItem(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/D04M12B1/createItem",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/D04M12B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{h["a"].get("/D04M12B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},deleteItem(t){return new Promise((e,a)=>{h["a"].get("/D04M12B1/deleteItem?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var S=y,x=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[a("el-row",[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getselPwProcess(1)}}},[a("i",{staticClass:"el-icon-circle-plus-outline"}),t._v(" 添 加")]),a("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveUp()}}},[a("i",{staticClass:"el-icon-top"}),t._v(" 上 移")]),a("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveDown()}}},[a("i",{staticClass:"el-icon-bottom"}),t._v(" 下 移")]),a("el-button",{attrs:{disabled:!t.selected,type:"danger",size:"mini"},nativeOn:{click:function(e){return t.delItem()}}},[a("i",{staticClass:"el-icon-delete"}),t._v(" 删 除")]),a("el-button",{attrs:{disabled:!!t.formdata.assessor||1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.copyRow()}}},[a("i",{staticClass:"el-icon-document-copy"}),t._v(" 复 制")]),a("el-button",{attrs:{disabled:0==t.lst.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.settingData()}}},[a("i",{staticClass:"el-icon-setting"}),t._v(" 同步数据")])],1)],1)],1),a("div",{staticClass:"table-container f-1 table-position"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",attrs:{"summary-method":t.getSummaries,"show-summary":"",data:t.lst,"element-loading-text":"Loading",height:t.tableHeight,border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange,"row-click":t.saveRow}},[a("el-table-column",{attrs:{type:"selection",width:"60",align:"center"}}),a("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsuid))])]}}])}),a("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsname))])]}}])}),a("el-table-column",{attrs:{label:"规格",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsspec))])]}}])}),a("el-table-column",{attrs:{label:"外部编码",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.partid))])]}}])}),a("el-table-column",{attrs:{label:"单位",align:"center","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsunit))])]}}])}),a("el-table-column",{attrs:{label:"数量",align:"center","min-width":"100","show-overflow-tooltip":"",prop:"quantity"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"请输入数量"},model:{value:e.row.quantity,callback:function(a){t.$set(e.row,"quantity",a)},expression:"scope.row.quantity"}}):a("span",[t._v(t._s(e.row.quantity))])]}}])}),a("el-table-column",{attrs:{label:"库位编码",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.location))])]}}])}),a("el-table-column",{attrs:{label:"SN",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.packsn))])]}}])}),a("el-table-column",{attrs:{label:"批号",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.batchno))])]}}])})],1)],1),t.PwProcessFormVisible?a("el-dialog",{attrs:{title:"货品信息","append-to-body":!0,visible:t.PwProcessFormVisible,width:"70vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.PwProcessFormVisible=e}}},[a("selPwProcess",{ref:"selPwProcess",attrs:{multi:t.multi}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selPwProcess()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.PwProcessFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},_=[],P=(a("7db0"),a("13d5"),a("a434"),a("a9e3"),a("159b"),a("9bda")),k=a("da92"),$={name:"Elitem",title:"入库明细",components:{selPwProcess:P["a"]},props:["formdata","lstitem","idx","istotal"],data:function(){return{formLabelWidth:"100px",listLoading:!1,PwProcessFormVisible:!1,lst:[],multi:0,multipleSelection:[],isEditOk:!0,selected:!1,tableHeight:0}},watch:{lstitem:function(t,e){this.lst=this.lstitem},"formdata.assessor":function(t,e){this.formdata.assessor?this.isEditOk=!1:this.isEditOk=!0},lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){this.lst=[]},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},mounted:function(){this.catchHight()},methods:{getSummaries:function(t){var e=t.columns,a=t.data,i=["quantity"],o=[];return e.forEach((function(t,e){if(0!==e){var s=!1;i.length>0&&void 0!=i.find((function(e){return e==t.property}))&&(s=!0);var n=a.map((function(e){return Number(e[t.property])}));!n.every((function(t){return isNaN(t)}))&&s?o[e]=n.reduce((function(t,e){var a=Number(e);return isNaN(a)?Number(t):k["a"].plus(Number(t),Number(e))}),0):o[e]=""}else o[e]="合计"})),this.istotal.initemtotal=o[6],o},selPwProcess:function(){this.PwProcessFormVisible=!1;for(var t=this.$refs.selPwProcess.$refs.selectgoods.selection,e=0;e<t.length;e++){var a={goodsid:t[e].id,goodsuid:t[e].goodsuid,goodsname:t[e].goodsname,goodsunit:t[e].goodsunit,goodsspec:t[e].goodsspec,partid:t[e].partid,accesstype:1,quantity:0,batchno:t[e].batchno,location:t[e].location,packsn:t[e].packsn,remark:"",rownum:0};0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}},numFormat:function(t){var e=t.lastIndexOf(".")?t.lastIndexOf("."):0,a=t.length,i=t.substring(e,a);return i>0?t:t.substring(0,e)},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},deleteRows:function(t,e){var a=this,i=this.multipleSelection;i&&i.forEach((function(t,e){a.lst.forEach((function(e,i){t.goodsid===e.goodsid&&t.rownum===e.rownum&&a.lst.splice(i,1)}))})),this.$refs.multipleTable.clearSelection(),this.selected=!1},settingData:function(){this.$emit("settingData")},getselPwProcess:function(t){"拆分数量"==this.formdata.billtype?this.$emit("copydata"):(this.PwProcessFormVisible=!0,this.multi=t)},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},handleSelectionChange:function(t){t.length>0?this.selected=!0:this.selected=!1;for(var e=0;e<this.lst.length;e++)this.lst[e].rownum=e;this.multipleSelection=t},copyRow:function(){var t=Object.assign({},this.multipleSelection[0]);this.$delete(t,"id"),this.lst.push(t),this.$refs.multipleTable.clearSelection()},saveRow:function(t){for(var e=0;e<this.lst.length;e++)this.lst[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0)),this.$forceUpdate()},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0"),l=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(s,":").concat(n,":").concat(l)}}}},C=$,O=(a("3ace"),Object(d["a"])(C,x,_,!1,null,"daf60036",null)),D=O.exports,q=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[a("el-row",[a("el-button-group",[a("el-button",{attrs:{disabled:t.isneedAdd,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getselPwProcess(1)}}},[a("i",{staticClass:"el-icon-circle-plus-outline"}),t._v(" 添 加")]),a("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveUp()}}},[a("i",{staticClass:"el-icon-top"}),t._v(" 上 移")]),a("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveDown()}}},[a("i",{staticClass:"el-icon-bottom"}),t._v(" 下 移")]),a("el-button",{attrs:{disabled:!t.selected,type:"danger",size:"mini"},nativeOn:{click:function(e){return t.delItem()}}},[a("i",{staticClass:"el-icon-delete"}),t._v(" 删 除")]),a("el-button",{attrs:{disabled:!!t.formdata.assessor||1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.copyRow()}}},[a("i",{staticClass:"el-icon-document-copy"}),t._v(" 复 制")])],1)],1)],1),a("div",{staticClass:"table-container f-1 table-position"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",attrs:{"summary-method":t.getSummaries,"show-summary":"",data:t.lst,"element-loading-text":"Loading",height:t.tableHeight,border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange,"row-click":t.saveRow}},[a("el-table-column",{attrs:{type:"selection",width:"60",align:"center"}}),a("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsuid))])]}}])}),a("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsname))])]}}])}),a("el-table-column",{attrs:{label:"规格",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsspec))])]}}])}),a("el-table-column",{attrs:{label:"外部编码",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.partid))])]}}])}),a("el-table-column",{attrs:{label:"单位",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsunit))])]}}])}),a("el-table-column",{attrs:{label:"数量",align:"center","min-width":"100","show-overflow-tooltip":"",prop:"quantity"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"请输入数量"},model:{value:e.row.quantity,callback:function(a){t.$set(e.row,"quantity",a)},expression:"scope.row.quantity"}}):a("span",[t._v(t._s(e.row.quantity))])]}}])}),a("el-table-column",{attrs:{label:"库位编码",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.location))])]}}])}),a("el-table-column",{attrs:{label:"SN",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.packsn))])]}}])}),a("el-table-column",{attrs:{label:"批号",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.batchno))])]}}])})],1)],1),t.PwProcessFormVisible?a("el-dialog",{attrs:{title:"库存信息","append-to-body":!0,visible:t.PwProcessFormVisible,width:"70vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.PwProcessFormVisible=e}}},[a("selPwProcess",{ref:"selPwProcess",attrs:{multi:t.multi,storeid:t.formdata.outstoreid}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selPwProcess()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.PwProcessFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},F=[],N=a("57a0"),z={name:"Elitem",title:"出库明细",components:{selPwProcess:N["a"]},props:["formdata","lstitem","idx","istotal"],data:function(){return{formLabelWidth:"100px",listLoading:!1,PwProcessFormVisible:!1,lst:[],multi:0,multipleSelection:[],isEditOk:!0,selected:!1,tableHeight:0,isneedAdd:!1}},watch:{lstitem:function(t,e){this.lst=this.lstitem},"formdata.assessor":function(t,e){this.formdata.assessor?this.isEditOk=!1:this.isEditOk=!0},lst:function(t,e){void 0==t&&(this.lst=[]),1==t.length&&"拆分数量"==this.formdata.billtype?this.isneedAdd=!0:this.isneedAdd=!1;for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){this.lst=[]},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},mounted:function(){this.catchHight()},methods:{getSummaries:function(t){var e=t.columns,a=t.data,i=["quantity"],o=[];return e.forEach((function(t,e){if(0!==e){var s=!1;i.length>0&&void 0!=i.find((function(e){return e==t.property}))&&(s=!0);var n=a.map((function(e){return Number(e[t.property])}));!n.every((function(t){return isNaN(t)}))&&s?o[e]=n.reduce((function(t,e){var a=Number(e);return isNaN(a)?Number(t):k["a"].plus(Number(t),Number(e))}),0):o[e]=""}else o[e]="合计"})),this.istotal.outitemtotal=o[6],o},selPwProcess:function(){var t=this.$refs.selPwProcess.$refs.selectVal.selection;if(1==t.length){console.log("oldiLst",t),this.PwProcessFormVisible=!1;for(var e=0;e<t.length;e++){var a={goodsid:t[e].goodsid,goodsuid:t[e].goodsuid,goodsname:t[e].goodsname,goodsunit:t[e].goodsunit,goodsspec:t[e].goodsspec,partid:t[e].partid,accesstype:0,quantity:t[e].quantity,batchno:t[e].batchno,location:t[e].location,packsn:t[e].packsn,inveid:t[e].id,price:0,amount:0,remark:"",rownum:0};0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("只能选择一条库存信息")},numFormat:function(t){t+="";var e=t.lastIndexOf(".")?t.lastIndexOf("."):0,a=t.length,i=t.substring(e,a);return i>0?t:t.substring(0,e)},changeInput:function(t,e,a){},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},deleteRows:function(t,e){var a=this,i=this.multipleSelection;console.log("选中数据",i),i&&i.forEach((function(t,e){a.lst.forEach((function(e,i){t.goodsid===e.goodsid&&t.goodsname===e.goodsname&&a.lst.splice(i,1)}))})),this.$refs.multipleTable.clearSelection(),this.selected=!1},getselPwProcess:function(t){this.formdata.outstoreid?(this.PwProcessFormVisible=!0,this.multi=t):this.$message.warning("请先选择调出仓库")},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},handleSelectionChange:function(t){t.length>0?this.selected=!0:this.selected=!1;for(var e=0;e<this.lst.length;e++)this.lst[e].rownum=e;this.multipleSelection=t},copyRow:function(){var t=Object.assign({},this.multipleSelection[0]);this.$delete(t,"id"),this.lst.push(t),this.$refs.multipleTable.clearSelection()},saveRow:function(t){for(var e=0;e<this.lst.length;e++)this.lst[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0)),this.$forceUpdate()},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0"),l=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(s,":").concat(n,":").concat(l)}}}},L=z,j=(a("9888"),Object(d["a"])(L,q,F,!1,null,"4faa58a4",null)),B=j.exports,E=a("4c0c"),M={name:"Formedit",components:{elitemByOut:B,elitem:D,selstore:E["a"]},props:["idx"],data:function(){return{title:"拆装单",formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,billdate:new Date,billtitle:"",billtype:"拆分数量",instorecode:"",instoreid:"",instorename:"",operator:"",outstorecode:"",outstoreid:"",outstorename:"",refno:"",summary:"",item:[],initem:[],outitem:[]},formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],outstoreid:[{required:!0,trigger:"blur",message:"调出仓库为必填项"}],instoreid:[{required:!0,trigger:"blur",message:"调入仓库为必填项"}]},multi:0,selVisible:!1,formLabelWidth:"100px",formheight:"500px",selVisible4In:!1,selVisible4Out:!1,istotal:{initemtotal:0,outitemtotal:0},options:[{value:"拆分数量",label:"拆分数量"},{value:"合并数量",label:"合并数量"},{value:"分解物料",label:"分解物料"},{value:"简单组装",label:"简单组装"}]}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},destroyed:function(){document.onkeydown=function(){},this.$setWs.wsClose()},methods:{bindData:function(){var t=this;0!=this.idx&&(this.listLoading=!0,h["a"].get("/D04M12B1/getBillEntity?key=".concat(this.idx)).then((function(e){if(console.log("============",e),200==e.data.code){t.formdata=e.data.data,t.formdata.initem=[],t.formdata.outitem=[];for(var a=0;a<t.formdata.item.length;a++){var i=t.formdata.item[a];0==i.accesstype?t.formdata.outitem.push(i):t.formdata.initem.push(i)}console.log(t.formdata)}else t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}});t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")})))},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.initem.lst.length)if(0!=this.$refs.outitem.lst.length)if(console.log("this.initemtotal!=this.outitemtotal",this.istotal.initemtotal,this.istotal.outitemtotal),this.istotal.initemtotal==this.istotal.outitemtotal){var e=[].concat(Object(v["a"])(this.$refs.outitem.lst),Object(v["a"])(this.$refs.initem.lst));this.formdata.item=e,0==this.idx?S.add(this.formdata).then((function(e){console.log("res",e),t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.bindData()})).catch((function(e){t.$message.warning("保存失败")})):S.update(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.bindData()})).catch((function(e){t.$message.warning("保存失败")}))}else this.$message.warning("发货数量与入货数量不相等");else this.$message.warning("发货明细不能为空");else this.$message.warning("入货明细不能为空")},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){S.delete(t).then((function(){e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},approval:function(){var t=this;this.formdata.id?this.approvalRequest(this.formdata.id):this.$refs["formdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;0!=t.$refs.elitem.lst.length?(t.formdata.item=t.$refs.elitem.lst,S.add(t.formdata).then((function(e){t.formdata=e.data,t.$emit("changeIdx",t.formdata.id),t.approvalRequest(t.formdata.id)})).catch((function(e){t.$message.warning("保存失败")}))):t.$message.warning("单据内容不能为空")}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},DeApproval:function(){var t=this;h["a"].get("/D01M03B1/approval?key="+this.formdata.id).then((function(e){200==e.data.code?(t.$message.success("反审核成功"),t.formdata=e.data.data):t.$message.warning("反审核失败")}))},approvalRequest:function(t){var e=this;return Object(w["a"])(Object(b["a"])().mark((function t(){return Object(b["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,h["a"].get("/D01M03B1/approval?key="+e.formdata.id).then((function(t){200==t.data.code?(e.$message.success("审核成功"),e.formdata=t.data.data):e.$message.warning("审核失败")}));case 2:case"end":return t.stop()}}),t)})))()},printButton:function(){this.$refs.PrintServer.printButton(0,1)},changeIdx:function(t){this.idx=t},changeBilltype:function(){this.formdata.initem=[],this.formdata.outitem=[]},settingData:function(){var t=this.$refs.initem.lst,e=this.$refs.outitem.lst[0];console.log(t,e);for(var a=0;a<t.length;a++){var i=t[a];"拆分数量"==this.formdata.billtype||(i.quantity=e.quantity,i.location=e.location,i.batchno=e.batchno),i.packsn=e.packsn+"-"+(a+1)}},copydata:function(){var t=this.$refs.initem.lst,e=this.$refs.outitem.lst[0],a=Object.assign({},e);a.quantity=0,a.accesstype=1,a.inveid="",t.push(a);for(var i=0;i<t.length;i++){var o=t[i];o.packsn=e.packsn+"-"+(i+1)}},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},SelOutStore:function(t){var e=this.$refs.selOutStore.selrows;console.log("选中",e),this.formdata.outstorename=t.storename,this.formdata.outstorecode=t.storecode,this.formdata.outstoreid=t.id,this.selVisible4Out=!1,this.cleValidate("outstoreid")},SelInStore:function(t){var e=this.$refs.selInStore.selrows;console.log("选中",e),this.formdata.instorename=t.storename,this.formdata.instorecode=t.storecode,this.formdata.instoreid=t.id,this.selVisible4In=!1,this.cleValidate("instoreid")}},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0"),l=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(s,":").concat(n,":").concat(l)}}},R=M,V=(a("d3d7"),Object(d["a"])(R,p,g,!1,null,"882390b0",null)),T=V.exports,I=a("48da"),H={formcode:"D04M012B1Th",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Mat_Combin.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Combin.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Combin.billdate"},{itemcode:"outstorename",itemname:"调出仓库",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Combin.outstorename"},{itemcode:"instorename",itemname:"调入仓库",minwidth:"70",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Combin.instorename"},{itemcode:"operator",itemname:"经办人员",minwidth:"70",displaymark:1,overflow:1,datasheet:"Mat_Combin.operator"},{itemcode:"summary",itemname:"备注",minwidth:"70",displaymark:1,overflow:1,datasheet:"Mat_Combin.summary"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Combin.lister"},{itemcode:"createdate",itemname:"新建日期",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Combin.createdate"},{itemcode:"modifydate",itemname:"修改日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Combin.modifydate"}]},J=a("0521"),U={name:"D04M12B1",components:{Pagination:f["a"],listheader:m,formedit:T,helpmodel:J["a"]},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0");return"".concat(a,"/").concat(i,"/").concat(o)}}},data:function(){return{title:"拆装单",lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:H,showhelp:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){this.searchstr="",this.bindData(),this.getColumn()},methods:{bindData:function(){var t=this;this.listLoading=!0,this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(l["d"])()[0],EndDate:Object(l["d"])()[1]}),h["a"].post("/D04M12B1/getPageTh",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getColumn:function(){var t=this;h["a"].get("/SaDgFormat/getBillEntityByCode?code=D04M12B1Th").then((function(e){if(200==e.data.code){if(null==e.data.data)return t.tableForm=H,void t.$emit("sendTableForm",t.tableForm);t.tableForm=e.data.data,t.$emit("sendTableForm",t.tableForm)}})).catch((function(e){t.$message.error("请求出错")}))},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},btnExport:function(){var t=this;Promise.resolve().then(function(){var e=["单据编码","单据类型","单据日期","调出仓库","调入仓库","备注","创建者","制表","新建日期","修改日期","审核员","审核日期"],a=["refno","billtype","billdate","outstorename","instorename","summary","createby","lister","createdate","modifydate","assessor","assessdate"],i=t.lst,o=t.formatJson(a,i);Object(I["a"])(e,o,"拆装单")}.bind(null,a)).catch(a.oe)},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){t.dateRange?this.queryParams.DateRange={StartDate:Object(l["c"])(t.dateRange[0]),EndDate:Object(l["b"])(t.dateRange[1])}:this.$delete(this.queryParams,"DateRange");var e=t.strfilter;""!=e?this.queryParams.SearchPojo={instorename:e,outstorename:e,refno:e,billtitle:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){t.dateRange?this.queryParams.DateRange={StartDate:Object(l["c"])(t.dateRange[0]),EndDate:Object(l["b"])(t.dateRange[1])}:this.$delete(this.queryParams,"DateRange"),this.queryParams.SearchPojo=t.formdata,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showForm:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeIdx:function(t){this.idx=t},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(t,this.tableForm),this.bindData()}}},A=U,W=(a("d549"),Object(d["a"])(A,i,o,!1,null,"3ab6ca2d",null));e["default"]=W.exports},bf19:function(t,e,a){"use strict";var i=a("23e7");i({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return URL.prototype.toString.call(this)}})},c19f:function(t,e,a){"use strict";var i=a("23e7"),o=a("da84"),s=a("621a"),n=a("2626"),l="ArrayBuffer",r=s[l],c=o[l];i({global:!0,forced:c!==r},{ArrayBuffer:r}),n(l)},d3d7:function(t,e,a){"use strict";a("5e89")},d549:function(t,e,a){"use strict";a("3bb4")},e793:function(t,e,a){},ee58:function(t,e,a){"use strict";a("8885")}}]);