(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3eedf49a"],{"25d6":function(a,t,e){"use strict";e("a007")},"41a0":function(a,t,e){},a007:function(a,t,e){},c50f:function(a,t,e){"use strict";e.r(t);var o=function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("div",[e("div",{ref:"index",staticClass:"index"},[e("div",{staticClass:"page-container"},[e("el-row",[e("el-col",{attrs:{span:24}},[e("TableList",{ref:"tableList"})],1)],1)],1)])])},r=[],n=function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("div",{staticClass:"Company",staticStyle:{padding:"20px 20px 20px 20px"}},[e("div",{staticClass:"saveBtn"},[e("el-button",{attrs:{type:"primary"},on:{click:a.submitForm}},[a._v("保存")])],1),e("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:a.formdata,"label-width":"100px",rules:a.formRules}},[e("el-row",[e("el-col",{attrs:{span:6}},[e("div",{on:{click:function(t){return a.cleValidate("name")}}},[e("el-form-item",{attrs:{label:"公司名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"请输入公司名称",clearable:"",size:"small"},model:{value:a.formdata.name,callback:function(t){a.$set(a.formdata,"name",t)},expression:"formdata.name"}})],1)],1)]),e("el-col",{attrs:{span:6}},[e("div",{on:{click:function(t){return a.cleValidate("englishname")}}},[e("el-form-item",{attrs:{label:"英文名称",prop:"englishname"}},[e("el-input",{attrs:{placeholder:"请输入英文名称",clearable:"",size:"small"},model:{value:a.formdata.englishname,callback:function(t){a.$set(a.formdata,"englishname",t)},expression:"formdata.englishname"}})],1)],1)])],1),e("el-row",[e("el-col",{attrs:{span:6}},[e("div",{on:{click:function(t){return a.cleValidate("creditcode")}}},[e("el-form-item",{attrs:{label:"信用代码",prop:"creditcode"}},[e("el-input",{attrs:{placeholder:"请输入英文名称",clearable:"",size:"small"},model:{value:a.formdata.creditcode,callback:function(t){a.$set(a.formdata,"creditcode",t)},expression:"formdata.creditcode"}})],1)],1)]),e("el-col",{attrs:{span:6}},[e("div",{on:{click:function(t){return a.cleValidate("bankofdeposit")}}},[e("el-form-item",{attrs:{label:"开户银行",prop:"bankofdeposit"}},[e("el-input",{attrs:{placeholder:"请输入开户银行",clearable:"",size:"small"},model:{value:a.formdata.bankofdeposit,callback:function(t){a.$set(a.formdata,"bankofdeposit",t)},expression:"formdata.bankofdeposit"}})],1)],1)]),e("el-col",{attrs:{span:6}},[e("div",{on:{click:function(t){return a.cleValidate("bankaccount")}}},[e("el-form-item",{attrs:{label:"银行账号",prop:"bankaccount"}},[e("el-input",{attrs:{placeholder:"请输入银行账号",clearable:"",size:"small"},model:{value:a.formdata.bankaccount,callback:function(t){a.$set(a.formdata,"bankaccount",t)},expression:"formdata.bankaccount"}})],1)],1)])],1),e("el-row",[e("el-col",{attrs:{span:6}},[e("div",{on:{click:function(t){return a.cleValidate("contactperson")}}},[e("el-form-item",{attrs:{label:"联系人",prop:"contactperson"}},[e("el-input",{attrs:{placeholder:"请输入联系人",clearable:"",size:"small"},model:{value:a.formdata.contactperson,callback:function(t){a.$set(a.formdata,"contactperson",t)},expression:"formdata.contactperson"}})],1)],1)]),e("el-col",{attrs:{span:6}},[e("div",{on:{click:function(t){return a.cleValidate("tel")}}},[e("el-form-item",{attrs:{label:"联系电话",prop:"tel"}},[e("el-input",{attrs:{placeholder:"请输入联系电话",clearable:"",size:"small"},model:{value:a.formdata.tel,callback:function(t){a.$set(a.formdata,"tel",t)},expression:"formdata.tel"}})],1)],1)])],1),e("el-row",{staticStyle:{"margin-top":"0px"}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"公司地址","label-position":"right","label-width":"100px"}},[e("el-input",{attrs:{type:"textarea",placeholder:"请输入公司地址",clearable:""},model:{value:a.formdata.address,callback:function(t){a.$set(a.formdata,"address",t)},expression:"formdata.address"}})],1)],1)],1)],1),e("el-form",{staticClass:"footFormContent",attrs:{"label-width":a.formLabelWidth}},[e("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"备 注","label-position":"right","label-width":"100px"}},[e("el-input",{attrs:{placeholder:"请输入备注",clearable:"",size:"small"},model:{value:a.formdata.remark,callback:function(t){a.$set(a.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:4}},[e("el-form-item",{attrs:{label:"创建人"}},[e("span",{directives:[{name:"show",rawName:"v-show",value:a.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[a._v(a._s(a.formdata.createby))])])],1),e("el-col",{attrs:{span:4}},[e("el-form-item",{attrs:{label:"创建日期"}},[e("span",{directives:[{name:"show",rawName:"v-show",value:a.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[a._v(a._s(a._f("dateFormat")(a.formdata.createdate)))])])],1),e("el-col",{attrs:{span:4}},[e("el-form-item",{attrs:{label:"制表"}},[e("span",{directives:[{name:"show",rawName:"v-show",value:a.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[a._v(a._s(a.formdata.lister))])])],1),e("el-col",{attrs:{span:4}},[e("el-form-item",{attrs:{label:"修改日期"}},[e("span",{directives:[{name:"show",rawName:"v-show",value:a.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[a._v(a._s(a._f("dateFormat")(a.formdata.modifydate)))])])],1)],1)],1)],1)},l=[],s=(e("99af"),e("e9c4"),e("b64b"),e("d3b7"),e("25f0"),e("4d90"),e("b775"));const c={add(a){return new Promise((t,e)=>{var o=JSON.stringify(a);s["a"].post("/SaCompany/save",o).then(a=>{200==a.data.code?t(a.data):e(a.data.msg)}).catch(a=>{e(a)})})},update(a){return new Promise((t,e)=>{var o=JSON.stringify(a);s["a"].post("/SaCompany/update",o).then(a=>{200==a.data.code?t(a.data):e(a.data.msg)}).catch(a=>{e(a)})})},delete(a){return new Promise((t,e)=>{s["a"].get("/SaCompany/delete?key="+a).then(a=>{200==a.data.code?t(a.data):e(a.data.msg)}).catch(a=>{e(a)})})}};var i=c,d=e("ae30"),m={data:function(){return{title:"公司信息",formdata:{id:"",name:"",englishname:"",creditcode:"",address:"",bankaccount:"",bankofdeposit:"",contactperson:"",tel:"",createdate:"",modifydate:new Date,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,remark:""},formRules:{name:[{required:!0,trigger:"blur",message:"公司名称名称不能为空"}],creditcode:[{required:!0,trigger:"blur",message:"信用编码不能为空"}],bankaccount:[{required:!0,trigger:"blur",message:"银行账号不能为空"}]},currentId:1,formLabelWidth:"100px",topic:[]}},created:function(){this.binddata()},methods:{binddata:function(){var a=this;0!=this.currentId&&s["a"].get("/SaCompany/getEntity?key=".concat(this.currentId)).then((function(t){200==t.data.code?(console.log(t,"response"),a.formdata=t.data.data):a.$alert(t.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){a.$router.go(-1)}})})).catch((function(t){a.$message.error("请求错误")}))},submitForm:function(a){var t=this;this.$refs["formdata"].validate((function(a){if(!a)return console.log("error submit!!"),!1;t.saveForm()}))},saveForm:function(){var a=this;i.add(this.formdata).then((function(t){200==t.code&&(a.$message.success("保存成功"),a.closeDialog(),a.sendMqttMsg(t.data))})).catch((function(t){a.$message.warning(t)}))},closeDialog:function(){this.$emit("closeDialog")},cleValidate:function(a){this.$refs.formdata.clearValidate(a)},dateFormats:function(){var a=new Date,t=a.getFullYear(),e=(a.getMonth()+1).toString().padStart(2,"0"),o=a.getDate().toString().padStart(2,"0");return"".concat(t,"-").concat(e,"-").concat(o)},sendMqttMsg:function(a){var t=Object(d["a"])(this.$store.state.app.config),e=JSON.parse(localStorage.getItem("getInfo"));t.publish(JSON.parse(localStorage.getItem("topic")).send,JSON.stringify({msg:{log:{code:"Company",date:this.dateFormats(new Date),content:"姓名:".concat(e.realname,",IP:").concat(e.ipaddr,",公司名称:").concat(e.tenantinfo.company,",联系电话:").concat(e.tenantinfo.companytel,",sn:").concat(e.sn)},msgtype:"log",sn:e.sn},modulecode:"system"}),{qos:2},(function(a,t){a?console.log("发送信息失败"):console.log("发送信息成功")}))}},filters:{dateFormat:function(a){var t=new Date(a),e=t.getFullYear(),o=(t.getMonth()+1).toString().padStart(2,"0"),r=t.getDate().toString().padStart(2,"0"),n=t.getHours().toString().padStart(2,"0"),l=t.getMinutes().toString().padStart(2,"0"),s=t.getSeconds().toString().padStart(2,"0");return"".concat(e,"-").concat(o,"-").concat(r," ").concat(n,":").concat(l,":").concat(s)}}},f=m,p=(e("25d6"),e("2877")),u=Object(p["a"])(f,n,l,!1,null,"8e404aee",null),b=u.exports,g={name:"SaCompany",components:{TableList:b},data:function(){return{title:"公司信息"}},mounted:function(){},methods:{}},h=g,v=(e("d071"),Object(p["a"])(h,o,r,!1,null,"67a1280c",null));t["default"]=v.exports},d071:function(a,t,e){"use strict";e("41a0")}}]);