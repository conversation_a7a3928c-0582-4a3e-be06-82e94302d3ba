(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cb908fea"],{"01d3":function(t,e,i){"use strict";i("eaab")},"0fd8":function(t,e,i){},"11f6":function(t,e,i){},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"190a":function(t,e,i){"use strict";i("34af")},2499:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px",width:"100%"}},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1)]),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"450px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"客户订单号",align:"center",width:"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.custorderid))])]}}])}),i("el-table-column",{attrs:{label:"客户",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),t._l(t.customList,(function(t,e){return[i("el-table-column",{key:e,attrs:{"header-align":"center",align:"center",prop:t.attrkey,label:t.attrname}})]}))],2)],1),i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],n=i("c7eb"),s=i("1da1"),r=(i("c740"),i("e9c4"),i("b64b"),i("b775")),l=i("8daf"),d=i("40d9"),m={props:["multi","groupid","selecturl"],components:{Setcolums:l["a"]},data:function(){return{listLoading:!0,lst:[],strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},customList:[],setColumsVisible:!1,tableForm:d["d"]}},created:function(){this.bindData(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(s["a"])(Object(n["a"])().mark((function e(){return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,r["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code?t.customList=e.data.data?e.data.data:[]:t.$message.warning(e.data.msg||"获取失败")})).catch((function(e){t.$message.error("请求错误")}));case 2:return e.next=4,r["a"].get("/SaDgFormat/getBillEntityByCode?code=D01M03B1Select").then((function(e){if(200==e.data.code){if(null==e.data.data){t.tableForm=d["d"];for(var i=0;i<t.customList.length;i++){var a=t.customList[i],o=t.tableForm.item.findIndex((function(e){return e.itemcode==t.customList[i].attrkey}));if(-1==o){var n={itemcode:a.attrkey,itemname:a.attrname,minwidth:"80",displaymark:a.listshow,overflow:1};t.tableForm.item.push(n)}}return}t.tableForm=e.data.data;for(i=0;i<t.customList.length;i++){a=t.customList[i],o=t.tableForm.item.findIndex((function(e){return e.itemcode==t.customList[i].attrkey}));if(-1==o){n={itemcode:a.attrkey,itemname:a.attrname,minwidth:"80",displaymark:a.listshow,overflow:1};t.tableForm.item.push(n)}}}})).catch((function(e){t.$message.error("请求出错")}));case 4:case"end":return e.stop()}}),e)})))()},getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;return Object(s["a"])(Object(n["a"])().mark((function e(){var i;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.listLoading=!0,t.listLoading=!0,i=t.selecturl?t.selecturl:"/D01M03B1/getPageList",e.next=5,r["a"].post(i,JSON.stringify(t.queryParams)).then((function(e){if(200==e.data.code){console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],n=0;n<o.length;n++)t.$set(t.lst[i],o[n].key,o[n].value)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 5:return e.next=7,r["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code||t.$message.warning(e.data.msg||"获取spu失败")})).catch((function(e){t.$message.error("请求错误")}));case 7:case"end":return e.stop()}}),e)})))()},search:function(t){var e=t.split(",");0!=e.length?1==e.length?(this.queryParams.SearchPojo={goodsuid:e[0],goodsname:e[0],goodsunit:e[0],groupid:e[0],goodsspec:e[0],partid:e[0],refno:e[0],custorderid:e[0],groupname:e[0],attributestr:e[0]},this.$delete(this.queryParams,"scenedata")):e.length>1&&(this.queryParams.scenedata=[{field:"Mat_Goods.goodsuid",fieldtype:0,math:"like",value:"".concat(e[0])},{field:"Mat_Goods.goodsname",fieldtype:0,math:"like",value:"".concat(e[1])}],3==e.length&&this.queryParams.scenedata.push({field:"Mat_Goods.partid",fieldtype:0,math:"like",value:"".concat(e[2])}),this.$delete(this.queryParams,"SearchPojo")):(this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"scenedata")),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},c=m,u=(i("fb056"),i("2877")),h=Object(u["a"])(c,a,o,!1,null,"56660287",null);e["a"]=h.exports},"27b0":function(t,e,i){"use strict";i("d7fb")},"27f6":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},o=[],n=i("c7eb"),s=i("1da1"),r=(i("caad"),i("e9c4"),i("a9e3"),i("b64b"),i("d3b7"),i("2532"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),i("b775")),l=i("5e63"),d=i("b893"),m={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"13df"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:l["b"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={citeuid:this.searchVal};this.queryParams.SearchPojo=e;var i="/D04M01R1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(d["d"])()[0],EndDate:Object(d["d"])()[1]}),r["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],n=0;n<o.length;n++)t.$set(t.lst[i],o[n].key,o[n].value)}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(s["a"])(Object(n["a"])().mark((function e(){return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(l["b"].formcode,l["b"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var n=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return n}if("goodsuid"==t.itemcode){n=a("GoodsInfo",{attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return n}if("billtype"==t.itemcode){n=a("span",[o[t.itemcode]]);return o[t.itemcode].includes("红冲")&&(n=a("span",{style:"color:#f44336"},[o[t.itemcode]])),n}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},c=m,u=(i("b83a"),i("2877")),h=Object(u["a"])(c,a,o,!1,null,"34f0715a",null);e["a"]=h.exports},"34af":function(t,e,i){},"40d9":function(t,e,i){"use strict";i.d(e,"f",(function(){return a})),i.d(e,"e",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r})),i.d(e,"d",(function(){return l}));var a={formcode:"D01M03B1Th",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"150",defwidth:"",displaymark:1,fixed:1,sortable:1,overflow:1,aligntype:"center",datasheet:"Bus_Machining.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billdate"},{itemcode:"custorderid",itemname:"客户订单号",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.custorderid"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"abbreviate",itemname:"简称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.abbreviate"},{itemcode:"groupadd",itemname:"客户地址",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupadd"},{itemcode:"billtaxamount",itemname:"总金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtaxamount"},{itemcode:"advaamount",itemname:"预收款",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.advaamount"},{itemcode:"balance",itemname:"结余",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billcostbudgetamt",itemname:"成本预算",minwidth:"80",displaymark:0,overflow:1},{itemcode:"billplandate",itemname:"计划时间",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billplandate"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.summary"},{itemcode:"salesman",itemname:"业务员",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.salesman"},{itemcode:"billwkwpname",itemname:"最新工序",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.billwkwpname"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Machining.status"},{itemcode:"itemcount",itemname:"款数",minwidth:"60",displaymark:1,overflow:1,datasheet:"Bus_Machining.itemcount"},{itemcode:"printcount",itemname:"打印次数",minwidth:"100",displaymark:0,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Machining.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Bus_Machining.assessor"},{itemcode:"amtstatus",itemname:"收款状态",minwidth:"100",displaymark:1,overflow:1}]},o={formcode:"D01M03B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"150",defwidth:"",displaymark:1,overflow:1,fixed:1,sortable:1,aligntype:"center",datasheet:"Bus_Machining.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billdate"},{itemcode:"custorderid",itemname:"客户订单号",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.custorderid"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"matcode",itemname:"物料编码",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.matcode"},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.quantity"},{itemcode:"taxprice",itemname:"单价",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxprice"},{itemcode:"taxamount",itemname:"金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxamount"},{itemcode:"finishqty",itemname:"完成数",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.finishqty"},{itemcode:"remainder",itemname:"结余数",minwidth:"100",displaymark:0,overflow:1},{itemcode:"buyquantity",itemname:"采购数",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.buyquantity"},{itemcode:"wkwpname",itemname:"工序",minwidth:"100",displaymark:1,overflow:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"matstatus",itemname:"物料状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemplandate",itemname:"评审交期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.itemplandate"},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.remark"}]},n={formcode:"D01M03B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"120",defwidth:"",displaymark:1,fixed:1,sortable:0,overflow:1,aligntype:"center",operationmark:1},{itemcode:"goodsname",itemname:"货品名称",minwidth:"120",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"200",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"120",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"partid",itemname:"外部编码",minwidth:"120",displaymark:1,overflow:1,operationmark:1},{itemcode:"quantity",itemname:"数量",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"itemorgdate",itemname:"原始交期",minwidth:"120",displaymark:1,overflow:1},{itemcode:"itemplandate",itemname:"评审交期",minwidth:"120",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"stoqty",itemname:"库存发货",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"maxqty",itemname:"最大发货数",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"wkqty",itemname:"生产需求",minwidth:"120",displaymark:1,overflow:1},{itemcode:"wkquantity",itemname:"生产数",minwidth:"120",displaymark:1,overflow:1},{itemcode:"buyquantity",itemname:"采购数",minwidth:"100",displaymark:1,overflow:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:0,overflow:1},{itemcode:"finishqty",itemname:"发货数",minwidth:"120",displaymark:1,overflow:1},{itemcode:"outquantity",itemname:"出库数",minwidth:"120",displaymark:1,overflow:1}]},s={formcode:"D01M03B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"150",defwidth:"",displaymark:1,overflow:1,fixed:1,sortable:1,aligntype:"center",datasheet:"Bus_Machining.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billdate"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"matcode",itemname:"物料编码",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.matcode"},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.quantity"},{itemcode:"taxprice",itemname:"单价",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxprice"},{itemcode:"taxamount",itemname:"金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxamount"},{itemcode:"wkwpname",itemname:"工序",minwidth:"100",displaymark:1,overflow:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"matstatus",itemname:"物料状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemplandate",itemname:"评审交期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.itemplandate"},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.remark"}]},r={formcode:"D03M02B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"130",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",atasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,atasheet:"Buy_Order.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Buy_Order.billtitle"},{itemcode:"billdate",itemname:"单据日期",sortable:1,minwidth:"100",displaymark:1,overflow:1,atasheet:"Buy_Order.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"plandate",itemname:"计划完成",minwidth:"100",sortable:1,displaymark:1,overflow:1,atasheet:"Buy_OrderItem.billdate"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"规格",minwidth:"120",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.quantity"},{itemcode:"finishqty",itemname:"已收",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.status"}]},l={formcode:"D01M03B1Select",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"130",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",atasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"custorderid",itemname:"客户订单号",minwidth:"100",displaymark:1,overflow:1},{itemcode:"groupname",itemname:"客户",minwidth:"120",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,overflow:1,operationmark:1},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"120",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1}]}},"460e":function(t,e,i){},"659b":function(t,e,i){"use strict";i("460e")},"687d":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("Formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[i("ListHeader",{attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,changeModelUrl:t.changeModelUrl,btnExport:t.btnExport,changeBalance:t.changeBalance,pagePrint:t.pagePrint,btnPrint:function(e){return t.$refs.tableTh.btnPrint()},bindColumn:function(e){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()}}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{attrs:{span:24}},[i("TableTh",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],ref:"tableTh",attrs:{online:t.online,formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}}),i("TableList",{directives:[{name:"show",rawName:"v-show",value:!t.thorList,expression:"!thorList"}],ref:"tableList",attrs:{online:t.online,formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}})],1)],1)],1)],1)])},o=[],n=(i("b64b"),i("ac1f"),i("841c"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container flex a-c j-s"},[i("div",{staticStyle:{display:"flex"}},[i("div",{staticClass:"flex infoForm a-c"},[i("span",{staticClass:"infoForm-Title"},[t._v("时间范围")]),i("el-date-picker",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"unlink-panels":"",size:"mini","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")]),i("el-button",{attrs:{size:"mini",icon:"el-icon-printer",title:"打印列表"},on:{click:function(e){return t.$emit("pagePrint")}}},[t._v("打印")]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:"el-icon-printer",plain:"",size:"mini",title:"打印单据"},on:{click:function(e){return t.$emit("btnPrint")}}},[t._v(" 单据 ")])],1),i("div",{staticClass:"iShowBtn"},[i("div",{staticStyle:{display:"inline-block"}},[i("el-switch",{staticStyle:{"margin-right":"10px"},attrs:{"inactive-color":"#13ce66"},on:{change:t.changeModelUrl},model:{value:t.thorList,callback:function(e){t.thorList=e},expression:"thorList"}}),i("span",{staticStyle:{"margin-right":"10px","font-size":"14px"}},[t._v(t._s(t.thorList?"单据":"明细"))])],1),i("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"结存",size:"mini",border:""},on:{change:t.changeBalance},model:{value:t.balance,callback:function(e){t.balance=e},expression:"balance"}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),s=[],r=i("b893"),l={name:"ListHeader",props:["tableForm"],data:function(){return{strfilter:"",formdata:{},dateRange:Object(r["d"])(),pickerOptions:Object(r["h"])(),thorList:!0,balance:!1,setColumsVisible:!1,searchVisible:!1}},methods:{advancedSearch:function(t){var e={dateRange:this.dateRange,formdata:t};this.$emit("advancedSearch",e),this.searchVisible=!1},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){var t={dateRange:this.dateRange,strfilter:this.strfilter};this.$emit("btnSearch",t)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList),this.$emit("bindColumn")},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},changeBalance:function(t){var e=0;e=t?1:0,this.$emit("changeBalance",e)}}},d=l,m=(i("01d3"),i("2877")),c=Object(m["a"])(d,n,s,!1,null,"69777683",null),u=c.exports,h=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getSuppGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M03B2Edit",commonurl:"/D03M03B2/printBill",weburl:"/D03M03B2/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M03B2Edit",examineurl:"/D03M03B2/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},["D04M01B1"==t.processModel?i("D04M01B1",{ref:"D04M01B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M03B2"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e()],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D04M01B1"==t.processModel?i("D04M01B1List",{ref:"D04M01B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},f=[],p=i("b775");const g={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);p["a"].post("/D03M03B2/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);p["a"].post("/D03M03B2/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){p["a"].get("/D03M03B2/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,p["a"].get("/D03M03B2/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M03B2/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M03B2/closed?type="+(3==t?1:0);p["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var b=g,y=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","operator","arrivaladd","transport","summary","billtaxamount","billtaxtotal","billamount","disannulcount","custom1","custom3","custom2","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],w=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","partid","itemcode","itemname","itemspec","itemunit","quantity","taxprice","taxamount","taxtotal","itemtaxrate","price","amount","remark","orderno","orderuid","orderitemid","closed","rownum","invoclosed","virtualitem","customer","custpo","location","batchno","machuid","machitemid","machgroupid","mainplanuid","mainplanitemid","mrpuid","mrpitemid","disannulmark","attributejson","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],v={params:y,paramsItem:w},k={amount:0,attributejson:"",batchno:"",closed:0,custom1:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",customer:"",custpo:"",deliqty:0,disannuldate:new Date,disannullister:"",disannullisterid:"",disannulmark:0,finishqty:0,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",id:"",inspectid:"",inspectuid:"",intQtyMark:0,invoclosed:0,invoqty:0,itemcode:"",itemname:"",itemspec:"",itemtaxrate:0,itemunit:"",location:"",machgroupid:"",machitemid:"",machuid:"",mainplanitemid:"",mainplanuid:"",mrpitemid:"",mrpuid:"",orderitemid:"",orderno:"",orderuid:"",partid:"",passedqty:0,pid:"",price:0,quantity:0,remark:"",revision:0,rownum:0,sourcetype:0,statecode:"",statedate:new Date,taxamount:0,taxprice:0,taxtotal:0,virtualitem:0},x=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"收货入库",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D04M01B1",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]}],S=[{show:1,divided:!1,label:"收货入库",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D04M01B1",children:[]}],$=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[]}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"其他收货",value:"其他收货"}}),i("el-option",{attrs:{label:"其他退货",value:"其他退货"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据主题"}},[i("el-input",{attrs:{placeholder:"请输入单据主题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"供应厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("arrivaladd")}}},[i("el-form-item",{attrs:{label:"收货地址"}},[i("el-input",{attrs:{placeholder:"请输入收货地址",clearable:"",size:"small"},model:{value:t.formdata.arrivaladd,callback:function(e){t.$set(t.formdata,"arrivaladd",e)},expression:"formdata.arrivaladd"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("transport")}}},[i("el-form-item",{attrs:{label:"物流方式"}},[i("el-input",{attrs:{placeholder:"请输入物流方式",clearable:"",size:"small"},model:{value:t.formdata.transport,callback:function(e){t.$set(t.formdata,"transport",e)},expression:"formdata.transport"}})],1)],1)])],1)],1)},B=[],_={props:{formdata:{type:Object},title:{type:String}},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"供应商为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},D=_,F=(i("190a"),Object(m["a"])(D,$,B,!1,null,"51f61a94",null)),M=F.exports,O=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection,showcontent:["add","moveup","movedown","delete","copyrow","refresh","billstate"],dummyurl:"/D91M01B1/getVirOnlinePageList"},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,selDummy:t.selDummy,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selgoodsvisible?i("el-dialog",{attrs:{title:"货品信息","append-to-body":!0,visible:t.selgoodsvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selgoodsvisible=e}}},[i("SelGoods",{ref:"selGoods",attrs:{multi:t.multi}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selGoods()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selgoodsvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.saleorderVisible?i("el-dialog",{attrs:{title:"销售订单","append-to-body":!0,visible:t.saleorderVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1,top:"5vh"},on:{"update:visible":function(e){t.saleorderVisible=e}}},[i("SaleOrder",{ref:"SaleOrder",attrs:{multi:0}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.saleOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.saleorderVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},P=[],C=i("c7eb"),q=i("1da1"),L=(i("c740"),i("caad"),i("d81d"),i("e9c4"),i("a9e3"),i("d3b7"),i("2532"),i("5319"),i("c7cd"),i("159b"),{formcode:"D03M03B2Th",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"120",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Finishing.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billtaxamount",itemname:"含税金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billtaxamount"},{itemcode:"billamount",itemname:"未税金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Finishing.billamount"},{itemcode:"billtaxtotal",itemname:"税额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billtaxtotal"},{itemcode:"arrivaladd",itemname:"收货地址",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Finishing.arrivaladd"},{itemcode:"transport",itemname:"物流方式",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Finishing.transport"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_Finishing.status"},{itemcode:"itemcount",itemname:"款数",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.assessor"}]}),I={formcode:"D03M03B2List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"120",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Buy_Finishing.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_FinishingItem.quantity"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_FinishingItem.taxamount"},{itemcode:"finishqty",itemname:"已入库",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_FinishingItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"50",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.status"}]},T={formcode:"D03M03B2Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"60",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"70",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"finishqty",itemname:"出入库数",minwidth:"80",displaymark:1,overflow:1},{itemcode:"machuid",itemname:"销售订单",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},N=i("9bda"),j=i("2499"),z={name:"Elitem",components:{SelGoods:N["a"],SaleOrder:j["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],multi:0,keynum:0,selgoodsvisible:!1,saleorderVisible:!1,setColumsVisible:!1,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,index:0,tableForm:T,customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;if(t.editmarkfiles.includes(a.field)){t.countfiles.includes(a.field)&&t.changeInput("",i,a.field);var o=t.customList.findIndex((function(t){return t.attrkey==a.field}));-1!=o&&t.setAttributeJson(i,i.rownum)}}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],n=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));if(-1!=n){t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[n],Object.keys(o)[1]);var s=t.customList.findIndex((function(t){return t.attrkey==Object.keys(o)[1]}));-1!=s&&t.setAttributeJson(t.lst[n],n)}}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var n=JSON.parse(t[i].attributejson);for(a=0;a<n.length;a++)t[i][n[a].key]=n[a].value}}this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(q["a"])(Object(C["a"])().mark((function e(){var i;return Object(C["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=T,t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn(T.formcode,i,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,operationColumn:!!t.operationmark,renderBodyCell:function(i,a){var o=i.row,n=(i.column,i.rowIndex),s="",r=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));if(-1!=r){var l=e.customList[r].valuejson?e.customList[r].valuejson.split(","):[];return s=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]])]),a("div",{directives:[{name:"show",value:l.length}]},[a("div",{style:"width:0;height:0"},[a("el-select",{class:"wpselect",style:"width:0;height:0;opacity: 0;",attrs:{size:"small",id:t.itemcode+o.rownum+T.formcode},on:{change:function(i){o[t.itemcode]=i,e.setAttributeJson(o,o.rownum)}},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}},[l.map((function(t,e){return a("el-option",{key:e,attrs:{label:t,value:t},style:"max-width:200px"})}))])]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-arrow-down",style:"font-weight:bold;font-size:14px;",on:{click:function(){document.getElementById(t.itemcode+o.rownum+T.formcode).click()}}})])]),s}return"goodsuid"==t.itemcode?(s=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}}),s):"plandate"==t.itemcode?(s=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[e.$options.filters.dateFormat(o[t.itemcode])])]),a("div",[a("div",{style:"opacity: 0;width:0;height:0"},[a("el-date-picker",{style:"width:0;height:0",attrs:{size:"small",id:t.itemcode+n+T.formcode,value:new Date(o[t.itemcode]),type:"date",format:"MM-dd","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},on:{input:function(e){o[t.itemcode]=e}}})]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-date",on:{click:function(){document.getElementById(t.itemcode+n+T.formcode).focus()}}})])]),s):"machuid"==t.itemcode?(s=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("span",{class:o.disannulmark?"textlinethrough":"",style:"flex:1"},[o[t.itemcode]]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-circle-plus-outline",on:{click:function(){e.getOrderSupplier(0,n+e.rowScroll)}}})]),s):(s=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]),s)}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1,this.$forceUpdate()},getSummary:function(){this.$getSummary(this,["goodsuid","amount","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var n=t[o],s=0;s<i.length;s++){var r=i[s];this.lst[e+o][r]=n[r].replace(/^\s*|\s*$/g,""),this.countfiles.includes(r)&&this.changeInput("",this.lst[e+o],r);var l=this.customList.findIndex((function(t){return t.attrkey==r}));-1!=l&&this.setAttributeJson(this.lst[e+o],e+o)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],n={key:o.attrkey,value:t[o.attrkey]?t[o.attrkey].replace(/\s*/g,""):""};""!=n.value&&i.push(n)}0==i.length?this.lst[e].attributejson="":this.lst[e].attributejson=JSON.stringify(i),this.$forceUpdate()},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i),this.getSummary()},getAdd:function(t){this.selgoodsvisible=!0,this.multi=t},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"closed",0),this.$set(e,"finishqty",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},selGoods:function(){var t=this.$refs.selGoods.$refs.selectgoods.selection;if(0!=t.length){this.selgoodsvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=0,o=i.inprice?i.inprice:0,n=a*o,s=i.taxrate?i.taxrate:this.nowitemtaxrate,r=this.$fomatFloat(o*(1+.01*s),2),l=this.$fomatFloat(r*a,2),d=Object.assign({},k);d.goodsid=i.id,d.goodsuid=i.goodsuid,d.goodsname=i.goodsname,d.goodsunit=i.goodsunit,d.goodsspec=i.goodsspec,d.partid=i.partid,d.itemtaxrate=this.nowitemtaxrate,d.price=o,d.amount=n,d.quantity=a,d.taxprice=r,d.taxamount=l,d.attributejson=i.attributejson,d.goodscustom1=i.custom1,d.goodscustom2=i.custom2,d.goodscustom3=i.custom3,d.goodscustom4=i.custom4,d.goodscustom5=i.custom5,d.goodscustom6=i.custom6,d.goodscustom7=i.custom7,d.goodscustom8=i.custom8,d.goodscustom9=i.custom9,d.goodscustom10=i.custom10,0!=this.idx&&(d.pid=this.idx),this.lst.push(d)}}else this.$message.warning("请选择单据内容")},selDummy:function(t){var e=t;if(0!=e.length){this.$refs.itemGroupBtns.DummyVisible=!1;for(var i=0;i<e.length;i++){var a=e[i],o=Object.assign({},k);o.goodsid=a.id,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.goodscustom1=a.custom1,o.goodscustom2=a.custom2,o.goodscustom3=a.custom3,o.goodscustom4=a.custom4,o.goodscustom5=a.custom5,o.goodscustom6=a.custom6,o.goodscustom7=a.custom7,o.goodscustom8=a.custom8,o.goodscustom9=a.custom9,o.goodscustom10=a.custom10,o.price=a.inprice?a.inprice:0,o.itemtaxrate=a.taxrate?a.taxrate:0,o.quantity=1,o.virtualitem=1,0!=this.idx&&(o.pid=this.idx),this.changeInput("",o,"quantity"),this.lst.push(o)}}else this.$message.warning("请选择虚拟品内容")},getOrderSupplier:function(t,e){this.saleorderVisible=!0,this.multi=t,this.index=e},saleOrder:function(){this.saleorderVisible=!1;var t=this.$refs.SaleOrder.selrows;this.lst[this.index].machuid=t.refno,this.lst[this.index].machgroupid=t.groupid,this.lst[this.index].machitemid=t.id}}},E=z,R=(i("659b"),Object(m["a"])(E,O,P,!1,null,"81f9e5d0",null)),V=R.exports,H={header:{type:0,title:"其他收货",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"其他收货",value:"其他收货"},{label:"其他退货",value:"其他退货"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"供应商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"arrivaladd",label:"收货地址",type:"input",methods:"",param:""},{col:5,code:"transport",label:"物流方式",type:"input",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}},G=i("dcb4"),K=i("13df"),A=i("27f6"),J={name:"Formedit",components:{FormTemp:G["a"],EditHeader:M,EditItem:V,D04M01B1:K["default"],D04M01B1List:A["a"]},props:["idx"],data:function(){return{title:"其他收货",operateBar:x,processBar:S,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,arrivaladd:"",assessdate:new Date,assessor:"",billamount:0,billdate:new Date,billpaid:0,billstatecode:"",billstatedate:new Date,billtaxamount:0,billtaxtotal:0,billtitle:"",billtype:"其他收货",disannulmark:0,groupid:"",item:[],operator:"",summary:"",transport:"",groupname:""},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:H,formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData()},methods:{bindTemp:function(){var t=this;p["a"].get("/SaFormCustom/getEntityByCode?key=D03M03B2").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):H,t.formtemplate.footer.type||(t.formtemplate.footer=H.footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&p["a"].get("/D03M03B2/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.$refs.elitem.multipleSelection=[],t.$refs.elitem.checkboxOption.selectedRowKeys=[],t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id},autoClear:function(){this.formdata.groupname="",this.formdata.groupid=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.billamount=i.billamount,this.formdata.billtaxamount=i.billtaxamount,this.formdata.billtaxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(v,a,this.formdata),0==this.idx?b.add(a).then((function(e){t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.formdata=e.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):b.update(a).then((function(e){t.$message.success("保存成功"),t.formdata=e.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){b.delete(t)})).catch((function(){}))},approval:function(){b.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?b.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){b.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){console.log(t,"vallll"),this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t;var e=this.$refs.elitem.multipleSelection;"D04M01B1"===t&&e.length>0&&(this.formdata.item=e)},processBill:function(t){this.processVisible=!0,this.processTitle="D04M01B1"==t?"收货入库":"",this.processModel=t}}},W=J,U=(i("c462"),Object(m["a"])(W,h,f,!1,null,"e3571288",null)),Q=U.exports,X=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableTh",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px",display:"flex","align-items":"center"}},[i("div",{staticStyle:{"margin-right":"10px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)])]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.selectList,printcode:"D03M03B2Edit",commonurl:"/D03M03B2/printBatchBill",weburl:"/D03M03B2/printBatchWebBill"}}),i("PrintServer",{ref:"PrintPageServer",attrs:{formdata:t.lst,queryParams:t.queryParams,printcode:"D03M03B2Th",commonurl:"/D03M03B2/printPageTh",weburl:"/D03M03B2/printWebPageTh"}})],1)},Y=[],Z=i("ade3"),tt=i("f07e"),et={components:{MyPopover:tt["a"]},props:["online","formtemplate"],data:function(){var t=this;return Object(Z["a"])(Object(Z["a"])({lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:L,mypopoverTable:T,mypopoverData:[],mypopoverIndex:0,selectList:[],totalfields:["refno","billamount","billtaxamount","billtaxtotal"],exportitle:"其他收货",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectList=i?t.lst:[]}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}},"cellTotal",0),"cellNum",0)},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},methods:{bindData:function(){var t=this;if(this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(r["d"])()[0],EndDate:Object(r["d"])()[1]}),this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),this.online)var e="/D03M03B2/getOnlinePageTh";else e="/D03M03B2/getPageTh";p["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this,e=L;this.formtemplate.th.type&&(e.item=this.formtemplate.th.content),this.$getColumn(this.tableForm.formcode,e).then((function(e){t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row,n=(i.column,i.rowIndex);if("billdate"==t.itemcode||"itemplandate"==t.itemcode||"billplandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("groupuid"==t.itemcode){var s=a("GroupInfo",{attrs:{scopeVal:o[t.itemcode],searchUrl:"/D01M01B1/getGeneral?key="+o.groupid}});return s}if("status"==t.itemcode){s="";return o.finishcount>0&&o.finishcount+o.disannulcount<o.itemcount?s=a("span",{class:"textborder-blue"},["收货"]):o.finishcount+o.disannulcount==o.itemcount&&o.finishcount>0?s=a("span",{class:"textborder-green"},["完成"]):o.disannulcount>0&&o.disannulmark==o.itemcount&&(s=a("span",{class:"textborder-grey"},["撤销"])),s}if("refno"==t.itemcode){s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("assessor"==t.itemcode){s="";return s=o.oaflowmark&&!o.assessor?a("span",{style:"color:#ff9800"},["审核中"]):a("span",[o.assessor]),s}if("itemcount"==t.itemcode){s="";return s=a("el-popover",{attrs:{placement:"left",trigger:"click",title:"单据明细"},ref:"'popover-' + scope.$index"},[a("div",{style:"position: relative; min-height: 100px",directives:[{name:"show",value:n==e.mypopoverIndex}]},[a(tt["a"],{ref:"mypopover",attrs:{tableForm:e.mypopoverTable,lst:e.mypopoverData}})]),a("span",{slot:"reference",class:"textunderline",on:{click:function(){return e.getBillList(o,n)}}},[o[t.itemcode]])]),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.customData=i,this.keynum+=1},pagePrint:function(){this.$refs.PrintPageServer.printButton(1,1)},btnPrint:function(){this.$refs.PrintServer.printButton(2,1)},countCellData:function(){var t=this.$refs.tableTh.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableTh.getRangeCellSelection().selectionRangeIndexes,i=["billtaxamount","billamount","billtaxtotal"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t,1)},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)},getBillList:function(t,e){var i=this;this.mypopoverIndex=e,p["a"].get("/D03M03B2/getBillEntity?key=".concat(t.id)).then((function(t){200==t.data.code?i.mypopoverData=t.data.data.item:i.mypopoverData=[]}))}}},it=et,at=(i("27b0"),Object(m["a"])(it,X,Y,!1,null,"385fcd38",null)),ot=at.exports,nt=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize,pageSizes:[10,20,100,500]},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.lst,queryParams:t.queryParams,printcode:"D03M02B2List",commonurl:"/D03M02B1/printPageList",weburl:"/D03M02B1/printWebPageList"}})],1)},st=[],rt={components:{},props:["online","formtemplate"],data:function(){var t=this;return{lst:[],total:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:I,customList:[],selectList:[],totalfields:["refno","taxamount","quantity","compcost"],exportitle:"其他收货明细表",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectList=i?t.lst:[]}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},methods:{bindData:function(){var t=this;if(this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(r["d"])()[0],EndDate:Object(r["d"])()[1]}),this.online)var e="/D03M03B2/getOnlinePageList";else e="/D03M03B2/getPageList";p["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++){var a=t.lst[i];a.compcost=a.quantity-a.buyqty;for(var o=a.attributejson?JSON.parse(a.attributejson):[],n=0;n<o.length;n++)t.$set(t.lst[i],o[n].key,o[n].value)}}else t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(q["a"])(Object(C["a"])().mark((function e(){var i;return Object(C["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=I,t.formtemplate.list.type&&(i.item=t.formtemplate.list.content),t.$getColumn(t.tableForm.formcode,i,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var n=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return n}if("goodsuid"==t.itemcode){n=a("GoodsInfo",{attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return n}if("status"==t.itemcode){n="";return 0!=o.finishqty&&o.finishqty<o.quantity?n=a("span",{class:"textborder-blue"},["收货"]):o.finishqty==o.quantity&&0!=o.finishqty?n=a("span",{class:"textborder-green"},["完成"]):o.disannulmark>1&&(n=a("span",{class:"textborder-grey"},["撤销"])),n}if("compcost"==t.itemcode){n="";return n=o.quantity-o.finishqty<0?a("span",{style:"color:#F56C6C"},[o.quantity-o.finishqty]):o.quantity-o.finishqty==0?a("span",{style:"color:#67C23A"},[o.quantity-o.finishqty]):o.quantity-o.finishqty==o.quantity?a("span",[o.quantity-o.finishqty]):a("span",{style:"color:#409EFF"},[o.quantity-o.finishqty]),n}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},pagePrint:function(){this.$refs.PrintServer.printButton(1)},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,i=["quantity","finishqty","taxamount","compcost"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t,1)},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)}}},lt=rt,dt=(i("b83d"),Object(m["a"])(lt,nt,st,!1,null,"2c43990a",null)),mt=dt.exports,ct={name:"D03M03B2",components:{ListHeader:u,Formedit:Q,TableTh:ot,TableList:mt},data:function(){return{idx:0,online:0,formvisible:!1,thorList:!0,tableForm:{},showhelp:!1,formtemplate:H}},mounted:function(){this.bindTemp(),this.bindData()},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D03M03B2").then((function(e){200==e.data.code?(null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):H),t.$nextTick((function(){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()}))):t.$alert(e.data.msg||"获取页面信息失败")})).catch((function(e){t.$message.error("请求错误")}))},bindData:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.tableTh.bindData()})):this.$nextTick((function(){t.$refs.tableList.bindData()}))},pagePrint:function(){this.thorList?this.$refs.tableTh.pagePrint():this.$refs.tableList.pagePrint()},search:function(t){this.thorList?this.$refs.tableTh.search(t):this.$refs.tableList.search(t)},advancedSearch:function(t){this.thorList?this.$refs.tableTh.advancedSearch(t):this.$refs.tableList.advancedSearch(t)},changeModelUrl:function(t){this.thorList=!!t,this.bindData()},changeBalance:function(t){this.online=t,this.bindData()},btnExport:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.tableTh.btnExport()})):this.$nextTick((function(){t.$refs.tableList.btnExport()}))},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},sendTableForm:function(t){this.tableForm=t}}},ut=ct,ht=(i("9b1f"),Object(m["a"])(ut,a,o,!1,null,"1565d351",null));e["default"]=ht.exports},"6ccf":function(t,e,i){},"841c":function(t,e,i){"use strict";var a=i("d784"),o=i("825a"),n=i("1d80"),s=i("129f"),r=i("14c3");a("search",1,(function(t,e,i){return[function(e){var i=n(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,i):new RegExp(e)[t](String(i))},function(t){var a=i(e,t,this);if(a.done)return a.value;var n=o(t),l=String(this),d=n.lastIndex;s(d,0)||(n.lastIndex=0);var m=r(n,l);return s(n.lastIndex,d)||(n.lastIndex=d),null===m?-1:m.index}]}))},"9b1f":function(t,e,i){"use strict";i("0fd8")},b83a:function(t,e,i){"use strict";i("11f6")},b83d:function(t,e,i){"use strict";i("bba5")},bba5:function(t,e,i){},c462:function(t,e,i){"use strict";i("e427")},d7fb:function(t,e,i){},e427:function(t,e,i){},eaab:function(t,e,i){},fb056:function(t,e,i){"use strict";i("6ccf")}}]);