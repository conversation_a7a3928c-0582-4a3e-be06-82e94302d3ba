(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4181429e"],{"0a08":function(e,t,a){},"10d52":function(e,t,a){},"40fb":function(e,t,a){},"4e80":function(e,t,a){"use strict";a("57a3")},"57a3":function(e,t,a){},"8bb8":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{ref:"index",staticClass:"index"},[a("ListHeader",{attrs:{tableForm:e.tableForm},on:{btnAdd:function(t){return e.showDialog(0)},bindData:e.bindData,btnImport:e.btnImport,btnSearch:function(t){return e.$refs.tableList.search(t)},advancedSearch:function(t){return e.$refs.tableList.advancedSearch(t)},btnExport:function(t){return e.$refs.tableList.btnExport()},bindColumn:function(t){return e.$refs.tableList.getColumn()},allDelete:function(t){return e.$refs.tableList.allDelete()}}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:e.showhelp?20:24}},[a("TableList",{ref:"tableList",on:{changeIdx:e.changeIdx,showDialog:e.showDialog,sendTableForm:e.sendTableForm}})],1)],1)],1)],1),a("el-dialog",{attrs:{"destroy-on-close":!0,title:e.title,visible:e.dialogvisible,width:"30%","append-to-body":!0,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogvisible=t},close:e.handleCloseDialog}},[a("FormEdit",{attrs:{idx:e.idx},on:{closeDialog:e.closeDialog}})],1),e.exportvisible?a("el-dialog",{attrs:{title:"仓库导入",visible:e.exportvisible,"append-to-body":!0,width:"50%","close-on-click-modal":!1},on:{"update:visible":function(t){e.exportvisible=t}}},[a("div",[a("Export",{ref:"exportFile",on:{closeImportDialog:function(t){e.exportvisible=!1}}})],1)]):e._e()],1)},i=[],r=(a("d81d"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px"}},[a("div",[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":"100px",rules:e.formRules}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("storecode")}}},[a("el-form-item",{attrs:{label:"仓库编码",prop:"storecode"}},[a("el-input",{attrs:{placeholder:"请输入仓库编码",clearable:""},model:{value:e.formdata.storecode,callback:function(t){e.$set(e.formdata,"storecode",t)},expression:"formdata.storecode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("storename")}}},[a("el-form-item",{attrs:{label:"仓库名称",prop:"storename"}},[a("el-input",{attrs:{placeholder:"请输入仓库名称",clearable:""},model:{value:e.formdata.storename,callback:function(t){e.$set(e.formdata,"storename",t)},expression:"formdata.storename"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("storetype")}}},[a("el-form-item",{attrs:{label:"仓库性质",prop:"storetype"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择仓库性质"},model:{value:e.formdata.storetype,callback:function(t){e.$set(e.formdata,"storetype",t)},expression:"formdata.storetype"}},[a("el-option",{attrs:{label:"存货仓",value:0}}),a("el-option",{attrs:{label:"线边仓",value:1}}),a("el-option",{attrs:{label:"非存货仓",value:2}})],1)],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("operator")}}},[a("el-form-item",{attrs:{label:"仓管员",prop:"operator"}},[a("el-input",{attrs:{placeholder:"请输入仓管员",clearable:""},model:{value:e.formdata.operator,callback:function(t){e.$set(e.formdata,"operator",t)},expression:"formdata.operator"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("storetel")}}},[a("el-form-item",{attrs:{label:"电话",prop:"storetel"}},[a("el-input",{attrs:{placeholder:"请输入电话",clearable:""},model:{value:e.formdata.storetel,callback:function(t){e.$set(e.formdata,"storetel",t)},expression:"formdata.storetel"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("storeadd")}}},[a("el-form-item",{attrs:{label:"仓库地址",prop:"storeadd"}},[a("el-input",{attrs:{placeholder:"请输入仓库地址",clearable:""},model:{value:e.formdata.storeadd,callback:function(t){e.$set(e.formdata,"storeadd",t)},expression:"formdata.storeadd"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",[a("el-checkbox",{attrs:{label:"产品成品仓库","true-label":1,"false-label":0,size:"mini"},model:{value:e.formdata.machmark,callback:function(t){e.$set(e.formdata,"machmark",t)},expression:"formdata.machmark"}}),a("el-checkbox",{attrs:{label:"纳入可用量计算","true-label":1,"false-label":0,size:"mini"},model:{value:e.formdata.usablemark,callback:function(t){e.$set(e.formdata,"usablemark",t)},expression:"formdata.usablemark"}}),a("el-checkbox",{attrs:{label:"有效","true-label":1,"false-label":0,size:"mini"},model:{value:e.formdata.enabledmark,callback:function(t){e.$set(e.formdata,"enabledmark",t)},expression:"formdata.enabledmark"}})],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent"},[a("el-row",{staticStyle:{"margin-top":"0px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入摘要",clearable:""},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}}),a("div",{staticStyle:{color:"#F00","font-size":"14px"}},[e._v('"纳入可用量计算"：出入库后计算该货品在库存中的可用数量')])],1)],1)],1)],1),a("el-row",[a("el-col",{staticStyle:{display:"flex","align-items":"center","justify-content":"flex-end","margin-top":"20px"},attrs:{span:24}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("formdata")}}},[e._v("保存")]),a("el-button",{on:{click:e.closeDialog}},[e._v("取消")])],1)],1)],1)])])}),n=[],s=(a("b64b"),a("b775"));const l={add(e){return new Promise((t,a)=>{var o=JSON.stringify(e);s["a"].post("/D04M21S1/create",o).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var o=JSON.stringify(e);s["a"].post("/D04M21S1/update",o).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){return new Promise((t,a)=>{s["a"].get("/D04M21S1/delete?key="+e).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})}};var c=l,d={name:"addDialog",props:["idx"],data:function(){return{currentId:this.idx,formdata:{allowdelete:1,allowedit:1,usablemark:1,storetype:0,machmark:1,deletelister:"",deletemark:0,enabledmark:1,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,operator:"",remark:"",rownum:0,storeadd:"",storecode:"",storename:"",storetel:""},formRules:{storename:[{required:!0,trigger:"blur",message:"仓库名称不能为空"}],storecode:[{required:!0,trigger:"blur",message:"仓库编码不能为空"}],storetype:[{required:!0,trigger:"blur",message:"仓库性质不能为空"}]}}},watch:{idx:function(e,t){this.currentId=e,this.binddata()}},created:function(){this.binddata()},methods:{binddata:function(){var e=this;0!=this.currentId&&s["a"].get("/D04M21S1/getEntity?key=".concat(this.currentId)).then((function(t){200==t.data.code?e.formdata=t.data.data:e.$alert(t.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){e.closeDialog()}}),e.listLoading=!1})).catch((function(t){e.listLoading=!1,e.$message.error("请求错误")}))},submitForm:function(e){var t=this;this.$refs["formdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.saveForm()}))},saveForm:function(){var e=this;0==this.idx?c.add(this.formdata).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.closeDialog())})).catch((function(t){e.$message.warning(t)})):c.update(this.formdata).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.$emit("changeIdx",t.data.id))})).catch((function(t){e.$message.warning(t),e.closeDialog()}))},closeDialog:function(){this.$emit("closeDialog")},cleValidate:function(e){this.$refs.formdata.clearValidate(e)}}},m=d,u=(a("aaa3"),a("2877")),f=Object(u["a"])(m,r,n,!1,null,"5fccfe80",null),p=f.exports,h=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{class:"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnSearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnSearch},slot:"append"},[e._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:function(t){return e.$emit("btnAdd")}}},[e._v(" 添加 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-upload2",plain:"",size:"mini"},on:{click:e.btnImport}},[e._v(" 导入 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete",plain:"",size:"mini"},on:{click:function(t){return e.$emit("allDelete")}}},[e._v(" 删除 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:e.$store.state.advancedSearch.modulecode==e.tableForm.formcode?"primary":"default"},on:{click:function(t){return e.openSearchForm()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:e.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:e.btnExport}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(t){e.setColumsVisible=!0}}})],1)]),e.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:e.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.setColumsVisible=t}}},[a("SetColums",{ref:"setcolums",attrs:{code:e.tableForm.formcode,tableForm:e.tableForm,baseparam:"/SaDgFormat"},on:{bindData:function(t){return e.$emit("bindColumn")},closeDialog:function(t){e.setColumsVisible=!1}}})],1):e._e(),a("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:e.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.searchVisible=t}}},[a("SearchForm",{ref:"searchForm",attrs:{code:e.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:e.advancedSearch,closedDialog:function(t){e.searchVisible=!1},bindData:e.bindData}})],1)],1)},b=[],g={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",formdata:{},setColumsVisible:!1,searchVisible:!1}},methods:{advancedSearch:function(e){this.$emit("advancedSearch",e),this.searchVisible=!1},openSearchForm:function(){var e=this;this.searchVisible=!0,setTimeout((function(){e.$refs.searchForm.getInit()}),100)},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},bindData:function(){this.$emit("bindData")},btnImport:function(){this.$emit("btnImport")},btnExport:function(){this.$emit("btnExport")}}},v=g,w=(a("f8c8"),Object(u["a"])(v,h,b,!1,null,"b509ef5e",null)),y=w.exports,x=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("ve-table",{key:e.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":e.tableMaxHeight,"scroll-width":e.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:e.customData,"table-data":e.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:e.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":e.virtualScrollOption,"checkbox-option":e.checkboxOption,"footer-data":e.footerData,"fixed-footer":!0,"sort-option":e.sortOption}}),a("div",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("div",{staticClass:"flex a-c"},[a("Pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.getList}})],1),a("div",{staticStyle:{"margin-right":"40px"}},[a("Scene",{ref:"scene",attrs:{code:e.tableForm.formcode},on:{bindData:e.bindData}})],1)])],1)},k=[],S=a("b85c"),D=a("c7eb"),$=a("1da1"),_=(a("e9c4"),a("a9e3"),a("d3b7"),a("3ca3"),a("c7cd"),a("159b"),a("ddb0"),{formcode:"D04M21S1List",item:[{itemcode:"storecode",itemname:"仓库编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Mat_Storage.storecode"},{itemcode:"storename",itemname:"仓库名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Storage.storename"},{itemcode:"storetype",itemname:"仓库性质",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Storage.storetype"},{itemcode:"storeadd",itemname:"仓库地址",minwidth:"120",displaymark:1,overflow:1,datasheet:"Mat_Storage.storeadd"},{itemcode:"operator",itemname:"仓管员",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Storage.operator"},{itemcode:"storetel",itemname:"电话",minwidth:"100",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Storage.remark"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Storage.lister"},{itemcode:"modifydate",itemname:"修改日期",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Storage.modifydate"},{itemcode:"createdate",itemname:"创建日期",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Storage.modifydate"}]}),F={components:{},props:["online"],data:function(){var e=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:_,customList:[],selectList:[],customData:[],columnHidden:[],footerData:[],checkboxOption:{selectedRowKeys:[],selectedRowChange:function(t){t.row,t.isSelected;var a=t.selectedRowKeys;if(e.selectList=[],e.checkboxOption.selectedRowKeys=a,0!=a.length)for(var o=0;o<a.length;o++)e.selectList.push({id:a[o]})},selectedAllChange:function(t){var a=t.isSelected,o=t.selectedRowKeys;if(a){if(e.checkboxOption.selectedRowKeys=o,0!=o.length)for(var i=0;i<o.length;i++)e.selectList.push({id:o[i]})}else e.selectList=[],e.checkboxOption.selectedRowKeys=[]}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(t){var a=t.startRowIndex;e.rowScroll=a}},sortOption:{sortChange:function(t){e.changeSort(t)}}}},computed:{tableMaxHeight:function(){var e=window.innerHeight-160;return e<600&&(e=600),e+"px"},tableMinWidth:function(){var e="calc(100vw - 64px)";if(0!=this.tableForm.item.length){e=0;for(var t=0;t<this.tableForm.item.length;t++){var a=this.tableForm.item[t];a.displaymark&&(e+=Number(a.minwidth))}}return e}},methods:{bindData:function(){var e=this,t="/D04M21S1/getPageList";this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),s["a"].post(t,JSON.stringify(this.queryParams)).then((function(t){200==t.data.code&&(e.lst=t.data.data.list,e.total=t.data.data.total)})).catch((function(t){e.$message.error(t||"请求错误")}))},getColumn:function(){var e=this;return Object($["a"])(Object(D["a"])().mark((function t(){return Object(D["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$getColumn(e.tableForm.formcode,_).then((function(t){e.customList=t.customList,e.tableForm=Object.assign({},t.colList),e.initTable(e.tableForm),e.$emit("sendTableForm",e.tableForm)}));case 1:case"end":return t.stop()}}),t)})))()},initTable:function(e){var t=this,a=(this.$createElement,[]);this.columnHidden=[],e["item"].forEach((function(e,o){var i={field:e.itemcode,key:e.itemcode,title:e.itemname,width:isNaN(e.minwidth)?e.minwidth:Number(e.minwidth),displaymark:e.displaymark,fixed:!!e.fixed&&(1==e.fixed?"left":"right"),ellipsis:!!e.overflow&&{showTitle:!0},align:e.aligntype?e.aligntype:"center",sortBy:!!e.sortable&&"",renderBodyCell:function(a,o){var i=a.row,r=(a.column,a.rowIndex,"");return"createdate"==e.itemcode||"modifydate"==e.itemcode?t.$options.filters.dateFormats(i[e.itemcode]):"storecode"==e.itemcode?(r=o("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return t.showDialog(i.id)}}},[i[e.itemcode]?i[e.itemcode]:"仓库编码"]),r):"enabledmark"==e.itemcode?(r=1==i[e.itemcode]?o("el-tag",{attrs:{size:"medium"}},["正常"]):o("el-tag",{attrs:{type:"warning",size:"medium"}},["停用"]),r):"storetype"==e.itemcode?(r=0==i.storetype?"存货仓":1==i.storetype?"线边仓":"非存货仓",r):i[e.itemcode]}};e.displaymark||t.columnHidden.push(e.itemcode),a.push(i)})),a.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(e,a){e.row,e.column;var o=e.rowIndex;return o+t.rowScroll+1}}),a.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.customData=a,this.keynum+=1},allDelete:function(){var e=this;0!=this.selectList.length?this.$confirm("此操作将永久删除记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.deleteRows()})).catch((function(){})):this.$message.warning("请选择客户内容")},deleteRows:function(e,t){var a=this;return Object($["a"])(Object(D["a"])().mark((function e(){var t,o,i,r,n,l;return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=a,o=a.selectList,!o){e.next=22;break}i=[],r=Object(S["a"])(o),e.prev=5,l=Object(D["a"])().mark((function e(){var t,a;return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=n.value,a=new Promise((function(e,a){s["a"].get("/D04M21S1/delete?key=".concat(t.id)).then((function(o){200==o.data.code?0==o.data.data?a("删除失败,"+t.groupname+"在系统中已使用"):e("删除成功"):a("删除失败")})).catch((function(e){a("删除失败")}))})),i.push(a);case 3:case"end":return e.stop()}}),e)})),r.s();case 8:if((n=r.n()).done){e.next=12;break}return e.delegateYield(l(),"t0",10);case 10:e.next=8;break;case 12:e.next=17;break;case 14:e.prev=14,e.t1=e["catch"](5),r.e(e.t1);case 17:return e.prev=17,r.f(),e.finish(17);case 20:return e.next=22,Promise.all(i).then((function(e){t.$message.success("删除成功")})).catch((function(e){t.$message.warning(e)})).finally((function(){a.selectList=[],a.checkboxOption.selectedRowKeys=[],a.bindData()}));case 22:case"end":return e.stop()}}),e,null,[[5,14,17,20]])})))()},getList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},search:function(e){""!=e?this.queryParams.SearchPojo={storecode:e,storename:e,storeadd:e,operator:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(e){this.$advancedSearch(this,e)},changeSort:function(e){for(var t in e)if(""!=e[t]){var a={prop:t};"desc"==e[t]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(a,this.tableForm),this.bindData();break}},showDialog:function(e){this.$emit("showDialog",e)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,"仓库定义")}}},O=F,C=(a("ec04"),Object(u["a"])(O,x,k,!1,null,"ef30e04e",null)),P=C.exports,I=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",[a("div"),a("div",[a("el-row",[a("el-button-group",[a("el-button",{attrs:{type:"primary",icon:"el-icon-folder-add",size:"mini"},on:{click:e.showImport}},[e._v(" 文件导入 ")])],1)],1),a("el-table",{ref:"multipleTable",staticClass:"tb-edit tableBox",staticStyle:{overflow:"auto"},attrs:{data:e.goodsData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"0px 0px"},"row-style":{height:"40px"},height:"40vh"}},[a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"仓库编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.storecode))])]}}])}),a("el-table-column",{attrs:{label:"仓库名称",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.storename))])]}}])}),a("el-table-column",{attrs:{label:"仓库地址",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.storeadd))])]}}])}),a("el-table-column",{attrs:{label:"仓管员",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.operator))])]}}])}),a("el-table-column",{attrs:{label:"电话",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.storetel))])]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.remark))])]}}])})],1)],1),a("div",{staticStyle:{margin:"15px 0 0 0",height:"45px"}},[a("el-button",{staticStyle:{float:"right","margin-right":"20px"},attrs:{type:"primary"},on:{click:e.submitGoods}},[e._v("确 定")]),a("el-button",{staticStyle:{float:"right","margin-right":"20px"},on:{click:e.closeImportDialog}},[e._v("关 闭")])],1)]),a("el-dialog",{attrs:{title:"仓库信息",width:"400px",visible:e.importVisble,"append-to-body":""},on:{"update:visible":function(t){e.importVisble=t}}},[a("div",[a("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{drag:!0,action:"",multiple:!1,"on-change":e.handleChange,"on-remove":e.handleRemove,"on-preview":e.handlePreview,"on-success":e.handleSuccess,limit:e.limitUpload,"auto-upload":!1,accept:".xlsx,.xls,.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("上传文件")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[a("p",[e._v(" 只能上传xlsx / xls文件"),a("el-button",{attrs:{type:"text"},nativeOn:{click:function(t){return e.modelExport(t)}}},[e._v("下载模板")])],1)])])],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.importf}},[e._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(t){e.importVisble=!1}}},[e._v("取 消")])],1)])],1)},L=[],E=(a("99af"),a("a434"),a("b0c0"),a("25f0"),a("4d90"),a("2b3d"),a("9861"),{data:function(){return{limitUpload:1,fileTemp:{},staffVisible:!1,goodsData:[],formdata:{createdate:new Date,modifydate:new Date,allowdelete:1,allowedit:1,deletelister:"",deletemark:0,enabledmark:1,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,operator:"",remark:"",rownum:0,storeadd:"",storecode:"",storename:"",storetel:""},formLabelWidth:"100px",menuData:[],defaultProps:{children:"children",label:"label",value:"id"},mainData:{matGroup:"",matStore:""},options:[{value:"临时id",label:"仓库"}],importVisble:!1,failcount:0,allowUpload:!1}},created:function(){},methods:{handleChange:function(e,t){this.fileTemp=e.raw;var a=e.raw.name,o=a.substring(a.lastIndexOf(".")+1);this.fileTemp?"xlsx"==o||"xls"==o||this.$message({type:"warning",message:"文件格式错误，请删除后重新上传！"}):this.$message({type:"warning",message:"请上传文件！"})},importf:function(e){var t=this,a=(this.$refs.inputer,this.fileTemp),o=new FormData;o.append("file",a),s["a"].post("/D04M21S1/importExecl",o,{headers:{"Content-Type":"multipart/form-data"}}).then((function(e){200==e.data.code?(t.$message.success("文件导入成功"),t.importVisble=!1,t.goodsData=e.data.data):t.$message.warning("文件导入失败，请重试")}))},handlePreview:function(e){console.log(e)},handleSuccess:function(e,t){console.log("handleSuccess",e)},handleRemove:function(e,t){console.log("handleRemove",e)},setMinWidthEmpty:function(e){var t=document.getElementsByClassName("el-select-dropdown__empty");t.length>0&&(t[0].style["min-width"]=e.srcElement.clientWidth+2+"px")},transData:function(e,t,a,o){for(var i=[],r={},n=t,s=a,l=o,c=0,d=0,m=e.length;c<m;c++)r[e[c][n]]=e[c];for(;d<m;d++){var u=e[d],f=r[u[s]];f?(!f[l]&&(f[l]=[]),f[l].push(u)):i.push(u)}return i},changeFormat:function(e){var t=[];if(!Array.isArray(e))return t;var a={};return e.forEach((function(e){a[e.id]=e})),e.forEach((function(e){var o=a[e.navpid];o?(o.children||(o.children=[])).push(e):t.push(e)})),t},showImport:function(){this.importVisble=!0},modelExport:function(){s["a"].get("/D04M21S1/exportModel",{responseType:"blob"}).then((function(e){console.log(e);var t=document.createElement("a"),a=new Blob([e.data],{type:"application/vnd.ms-excel"});t.style.display="none",t.href=URL.createObjectURL(a),t.download="仓库信息模板.xls",document.body.appendChild(t),t.click()})).catch((function(e){console.log(e)}))},submitGoods:function(){var e=this;return Object($["a"])(Object(D["a"])().mark((function t(){var a,o,i,r;return Object(D["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:console.log("执行逐个保存"),a=e,o=0,[],0,i=Object(D["a"])().mark((function t(){var i;return Object(D["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a.formdata.storecode=a.goodsData[r].storecode,a.formdata.storename=a.goodsData[r].storename,a.formdata.storeadd=a.goodsData[r].storeadd,a.formdata.operator=a.goodsData[r].operator,a.formdata.storetel=a.goodsData[r].storetel,a.formdata.remark=a.goodsData[r].remark,a.formdata.custom1=a.goodsData[r].custom1,a.formdata.custom2=a.goodsData[r].custom2,a.formdata.custom3=a.goodsData[r].custom3,a.formdata.custom4=a.goodsData[r].custom4,a.formdata.custom5=a.goodsData[r].custom5,a.formdata.custom6=a.goodsData[r].custom6,a.formdata.custom7=a.goodsData[r].custom7,a.formdata.custom8=a.goodsData[r].custom8,a.formdata.custom9=a.goodsData[r].custom9,a.formdata.custom10=a.goodsData[r].custom10,a.formdata.enabledmark=a.goodsData[r].enabledmark?a.goodsData[r].enabledmark:1,i=r,t.next=20,c.add(a.formdata).then((function(e){200==e.code?a.goodsData.splice(i,1):console.log("保存失败")})).catch((function(t){o++,console.log(o,"保存失败"),e.$message.warning(t)}));case 20:case"end":return t.stop()}}),t)})),r=a.goodsData.length-1;case 7:if(!(r>=0)){t.next=12;break}return t.delegateYield(i(),"t0",9);case 9:r--,t.next=7;break;case 12:0==a.goodsData.length&&(a.$emit("closeImportDialog"),a.$emit("bindData"));case 13:case"end":return t.stop()}}),t)})))()},closeImportDialog:function(){this.$emit("closeImportDialog")}},filters:{dateFormat:function(e){if(e){var t=new Date(e),a=t.getFullYear(),o=(t.getMonth()+1).toString().padStart(2,"0"),i=t.getDate().toString().padStart(2,"0"),r=t.getHours().toString().padStart(2,"0"),n=t.getMinutes().toString().padStart(2,"0"),s=t.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(o,"-").concat(i," ").concat(r,":").concat(n,":").concat(s)}}}}),z=E,T=(a("9cef"),Object(u["a"])(z,I,L,!1,null,"183e5aa8",null)),M=T.exports,V={name:"D04M21S1",components:{ListHeader:y,FormEdit:p,TableList:P,Export:M},data:function(){return{title:"仓库信息",formvisible:!1,idx:0,tableForm:{},queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},showhelp:!1,dialogvisible:!1,exportvisible:!1}},mounted:function(){this.bindData(),this.$refs.tableList.getColumn()},methods:{bindData:function(){this.$refs.tableList.bindData()},formatJson:function(e,t){return t.map((function(t){return e.map((function(e){return t[e]}))}))},sendTableForm:function(e){this.tableForm=e},showDialog:function(e){this.idx=e,this.dialogvisible=!0},closeDialog:function(){this.dialogvisible=!1,this.changeIdx(0),this.bindData()},btnImport:function(){this.exportvisible=!0},handleCloseDialog:function(){this.bindData(),this.closeDialog()},changeIdx:function(e){this.idx=e}}},j=V,q=(a("4e80"),Object(u["a"])(j,o,i,!1,null,"4d1b6766",null));t["default"]=q.exports},"9cef":function(e,t,a){"use strict";a("10d52")},aaa3:function(e,t,a){"use strict";a("40fb")},ec04:function(e,t,a){"use strict";a("0a08")},f4f4:function(e,t,a){},f8c8:function(e,t,a){"use strict";a("f4f4")}}]);