(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d73a6f66"],{"0232":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[i("ListHeader",{attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:function(e){return t.$refs.tableList.search(e)},advancedSearch:function(e){return t.$refs.tableList.advancedSearch(e)},btnExport:function(e){return t.$refs.tableList.btnExport()},bindColumn:function(e){return t.$refs.tableList.getColumn()},bindData:t.bindData}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{attrs:{span:24}},[i("TableList",{ref:"tableList",on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}})],1)],1)],1)],1)])},r=[],o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container flex j-s a-c"},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")])],1),i("div",{staticClass:"iShowBtn"},[i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}})],1)]),t.setcolumsvisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setcolumsvisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setcolumsvisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setcolumsvisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchvisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchvisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchvisible=!1},bindData:t.bindData}})],1)],1)},n=[],s={name:"ListHeader",components:{},props:["tableForm"],data:function(){return{strfilter:"",setcolumsvisible:!1,searchvisible:!1}},methods:{openDialog:function(){this.setcolumsvisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},btnExport:function(){this.$emit("btnExport")},advancedSearch:function(t){this.$emit("advancedSearch",t),this.searchvisible=!1},openSearchForm:function(){var t=this;this.searchvisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},c=s,l=(i("be04"),i("2877")),d=Object(l["a"])(c,o,n,!1,null,"6fe110b5",null),m=d.exports,u=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["save","print","operate"],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1)]),i("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getNextCode:t.getNextCode}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D01M01B5Edit",commonurl:"/D01M01B5/printBill",weburl:"/D01M01B5/printWebBill"}})],1)},f=[],p=(i("b64b"),i("b775"));const h={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);p["a"].post("/D01M01B5/create",a).then(t=>{console.log(t),200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);p["a"].post("/D01M01B5/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){p["a"].get("/D01M01B5/delete?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||"删除成功"),t.$emit("closeForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})}};var b=h,g=["id","wggroupid","groupuid","groupname","abbreviate","groupclass","linkman","telephone","groupfax","groupadd","remark","invaliddate","grouptype","creditduint","creditdquantity","creditcuint","creditcquantity","rownum","mobile","linkmans","telephones","mobiles","country","province","groupzip","deliveradd","invoiceadd","seller","grouplabel","grouplevel","groupstate","source","credit","paymentmethod","creditcode","depositbank","bankaccount","enabledmark","deletemark","deletelister","deletelisterid","deletedate","fmaccoid","foreaccoid","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],v={params:g},w=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]}],y=[],x=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupuid")}}},[i("el-form-item",{attrs:{label:"部门编码",prop:"groupuid"}},[i("el-input",{attrs:{placeholder:"请输入部门编码",readonly:!!t.formdata.id,size:"small"},model:{value:t.formdata.groupuid,callback:function(e){t.$set(t.formdata,"groupuid","string"===typeof e?e.trim():e)},expression:"formdata.groupuid"}},[i("i",{directives:[{name:"show",rawName:"v-show",value:!t.formdata.id,expression:"!formdata.id"}],staticClass:"el-icon-edit getNextCode",attrs:{slot:"suffix"},on:{click:function(e){return t.$emit("getNextCode")}},slot:"suffix"})])],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"其他部门",prop:"groupname"}},[i("el-input",{attrs:{placeholder:"请输入其他部门",clearable:"",size:"small"},on:{focus:function(t){return t.currentTarget.select()}},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname","string"===typeof e?e.trim():e)},expression:"formdata.groupname"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"主管"}},[i("el-input",{attrs:{placeholder:"请输入主管",clearable:"",size:"small"},on:{focus:function(t){return t.currentTarget.select()}},model:{value:t.formdata.seller,callback:function(e){t.$set(t.formdata,"seller",e)},expression:"formdata.seller"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"部门类型"}},[i("el-popover",{ref:"dictionaryRef",staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.groupclassRef.bindData()}}},[i("SelDict",{ref:"groupclassRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"app_wg_workshop.groupclass"},on:{singleSel:function(e){t.formdata.groupclass=e.dictvalue,t.$refs["dictionaryRef"].doClose()},closedic:function(e){return t.$refs["dictionaryRef"].doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"请选择部门类型",clearable:"",size:"small"},model:{value:t.formdata.groupclass,callback:function(e){t.$set(t.formdata,"groupclass",e)},expression:"formdata.groupclass"}})],1)],1)],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"排序"}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},on:{focus:function(t){return t.currentTarget.select()}},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"状态"}},[i("el-checkbox",{attrs:{label:"有效","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.enabledmark,callback:function(e){t.$set(t.formdata,"enabledmark",e)},expression:"formdata.enabledmark"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"工作内容","label-position":"right","label-width":"100px"}},[i("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入工作内容",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1)],1)},k=[],$={props:{formdata:{type:Object},title:{type:String}},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"名称为必填项"}],groupuid:[{required:!0,trigger:"blur",message:"编码为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},S=$,F=(i("6686"),Object(l["a"])(S,x,k,!1,null,"97c48b36",null)),D=F.exports,C={header:{type:0,title:"其他部门",content:[{type:"form",rowitem:[{col:5,code:"groupuid",label:"部门编码",type:"input",methods:"",param:"",required:!0,iconbtn:{show:"!this.formdata.id",icon:"el-icon-edit",methods:"getNextCode",param:""}},{col:5,code:"groupname",label:"其他部门",type:"input",methods:"",param:"",required:!0}]},{rowitem:[{col:5,code:"seller",label:"主管",type:"input",methods:"",param:""},{col:5,code:"groupclass",label:"部门类型",billcode:"app_wg_workshop.groupclass",type:"dictionary",methods:"",param:""}]},{rowitem:[{col:5,code:"rownum",label:"排序",type:"number",methods:"",param:""},{col:5,code:"enabledmark",label:"有效",type:"checkbox",methods:"",param:""}]},{rowitem:[{col:10,code:"remark",label:"工作内容",type:"textarea",methods:"",param:""}]}]},item:{type:0,content:[]},footer:{type:1,content:[{type:"divider",center:"left",label:""},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"}]}]}},_=i("dcb4"),P={name:"Formedit",components:{EditHeader:D,FormTemp:_["a"]},props:["idx"],data:function(){return{title:"其他部门",operateBar:w,processBar:y,formdata:{groupuid:"",groupname:"",seller:"",remark:"",enabledmark:1,rownum:0,grouptype:"生产车间",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{groupname:[{required:!0,trigger:"blur",message:"名称为必填项"}],groupuid:[{required:!0,trigger:"blur",message:"编码为必填项"}]},formLabelWidth:"100px",submitting:0,groupclassRefsVisible:!1,formtemplate:C}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp(),this.bindData()},methods:{bindTemp:function(){this.formtemplate=C},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&p["a"].get("/D01M01B3/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.id?1:0):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.submitting=1;var e={};e=this.$getParam(v,e,this.formdata),0==this.idx?b.add(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0)})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0})):b.update(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0)})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){b.delete(e)})).catch((function(){}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},clickMethods:function(t){this[t.meth](t.param)},getNextCode:function(){var t=this;this.$request.get("/D01M01B5/getNextCode?type=5").then((function(e){200==e.data.code?t.formdata.groupuid=e.data.data:t.$message.warning("获取编码失败")}))}}},B=P,I=(i("0255"),Object(l["a"])(B,u,f,!1,null,"50d68bb8",null)),O=I.exports,T=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)])],1)},z=[],M=i("b85c"),L=i("c7eb"),A=i("1da1"),q=(i("e9c4"),i("a9e3"),i("d3b7"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),{formcode:"D01M01B5List",item:[{itemcode:"groupuid",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"其他部门",minwidth:"80",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"seller",itemname:"主管",minwidth:"100",displaymark:1,overflow:1},{itemcode:"grouptype",itemname:"组分类",minwidth:"100",displaymark:1,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1},{itemcode:"createdate",itemname:"创建时间",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"App_Workgroup.createdate"}]}),N={components:{},props:["online"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:q,customList:[],selectList:[],customData:[],columnHidden:[],footerData:[],checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],t.checkboxOption.selectedRowKeys=i,0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;if(i){if(t.checkboxOption.selectedRowKeys=a,0!=a.length)for(var r=0;r<a.length;r++)t.selectList.push({id:a[r]})}else t.selectList=[],t.checkboxOption.selectedRowKeys=[]}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},methods:{bindData:function(){var t=this,e="/D01M01B5/getPageList";this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),p["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"获取数据失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(A["a"])(Object(L["a"])().mark((function e(){return Object(L["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(t.tableForm.formcode,q).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var r={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var r=i.row;i.column,i.rowIndex;if("createdate"==t.itemcode||"modifydate"==t.itemcode)return e.$options.filters.dateFormats(r[t.itemcode]);if("groupuid"==t.itemcode){var o=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(r.id)}}},[r[t.itemcode]?r[t.itemcode]:"单据编码"]);return o}if("enabledmark"==t.itemcode){o="";return o=1==r[t.itemcode]?a("el-tag",{attrs:{size:"medium"}},["正常"]):a("el-tag",{attrs:{type:"warning",size:"medium"}},["停用"]),o}return r[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(r)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.customData=i,this.keynum+=1},allDelete:function(){var t=this;0!=this.selectList.length?this.$confirm("此操作将永久删除记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){})):this.$message.warning("请选择客户内容")},deleteRows:function(t,e){var i=this;return Object(A["a"])(Object(L["a"])().mark((function t(){var e,a,r,o,n,s;return Object(L["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=i,a=i.selectList,!a){t.next=22;break}r=[],o=Object(M["a"])(a),t.prev=5,s=Object(L["a"])().mark((function t(){var e,i;return Object(L["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=n.value,i=new Promise((function(t,i){p["a"].get("/D01M01B5/delete?key=".concat(e.id)).then((function(a){200==a.data.code?0==a.data.data?i("删除失败,"+e.groupname+"在系统中已使用"):t("删除成功"):i("删除失败")})).catch((function(t){i("删除失败")}))})),r.push(i);case 3:case"end":return t.stop()}}),t)})),o.s();case 8:if((n=o.n()).done){t.next=12;break}return t.delegateYield(s(),"t0",10);case 10:t.next=8;break;case 12:t.next=17;break;case 14:t.prev=14,t.t1=t["catch"](5),o.e(t.t1);case 17:return t.prev=17,o.f(),t.finish(17);case 20:return t.next=22,Promise.all(r).then((function(t){e.$message.success("删除成功")})).catch((function(t){e.$message.warning(t)})).finally((function(){i.selectList=[],i.checkboxOption.selectedRowKeys=[],i.bindData()}));case 22:case"end":return t.stop()}}),t,null,[[5,14,17,20]])})))()},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={groupuid:t,groupname:t,wggroupid:t,abbreviate:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.$advancedSearch(this,t)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,"生产车间")}}},E=N,H=(i("4da3"),Object(l["a"])(E,T,z,!1,null,"04ea2560",null)),R=H.exports,j={name:"",components:{TableList:R,ListHeader:m,FormEdit:O},data:function(){return{formvisible:!1,idx:0,tableForm:{},showhelp:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},mounted:function(){this.bindData(),this.$refs.tableList.getColumn()},methods:{bindData:function(){this.$refs.tableList.bindData()},sendTableForm:function(t){this.tableForm=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1,this.bindData()},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t}}},V=j,J=(i("e71b"),Object(l["a"])(V,a,r,!1,null,"5c7c4f2f",null));e["default"]=J.exports},"0255":function(t,e,i){"use strict";i("e07b")},"16ec":function(t,e,i){},"3b86":function(t,e,i){},"4da3":function(t,e,i){"use strict";i("3b86")},"5c73":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{},[i("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?i("ul",{},t._l(t.lst,(function(e,a){return i("li",{key:a,class:t.radio==a?"active":"",on:{click:function(i){t.getCurrentRow(e),t.radio=a}}},[i("span",[t._v(t._s(e.dictvalue))])])})),0):i("div",{staticClass:"noData"},[t._v("暂无数据")])]),i("div",{staticClass:"foots"},[i("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),i("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?i("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[i("div",{staticClass:"dialog-body customDialog"},[i("div",{staticClass:"left"},t._l(t.lst,(function(e,a){return i("p",{key:a,class:t.ActiveIndex==a?"isActive":"",on:{click:function(e){t.ActiveIndex=a}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),i("div",{staticClass:"right"},[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),i("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},r=[],o=(i("a434"),i("e9c4"),i("b775")),n=i("333d"),s=i("b0b8"),c={components:{Pagination:n["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],o["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var i=0;i<t.formdata.item.length;i++)t.lst.push(t.formdata.item[i])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.addItem(i)})).catch((function(t){console.log(t)}))},addItem:function(t){s.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:s.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.lst[t.ActiveIndex].dictvalue=i,s.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=s.getFullChars(i)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,o["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},l=c,d=(i("af2b"),i("2877")),m=Object(d["a"])(l,a,r,!1,null,"d2ba3d7a",null);e["a"]=m.exports},6686:function(t,e,i){"use strict";i("cb4f")},"7de4":function(t,e,i){},af2b:function(t,e,i){"use strict";i("7de4")},be04:function(t,e,i){"use strict";i("16ec")},cb4f:function(t,e,i){},e07b:function(t,e,i){},e71b:function(t,e,i){"use strict";i("eaa4")},eaa4:function(t,e,i){}}]);