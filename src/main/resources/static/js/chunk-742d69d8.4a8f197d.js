(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-742d69d8"],{"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"2d06":function(e,t,a){},5424:function(e,t,a){},"5c73":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{},[a("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=e.lst.length?a("ul",{},e._l(e.lst,(function(t,i){return a("li",{key:i,class:e.radio==i?"active":"",on:{click:function(a){e.getCurrentRow(t),e.radio=i}}},[a("span",[e._v(e._s(t.dictvalue))])])})),0):a("div",{staticClass:"noData"},[e._v("暂无数据")])]),a("div",{staticClass:"foots"},[a("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(t){e.SYSM07B1Visible=!0,e.lst=e.formdata.item}}},[e._v("编辑")]),a("span",{on:{click:function(t){return e.$emit("closedic")}}},[e._v("关闭")])])]),e.SYSM07B1Visible?a("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:e.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.SYSM07B1Visible=t}}},[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"left"},e._l(e.lst,(function(t,i){return a("p",{key:i,class:e.ActiveIndex==i?"isActive":"",on:{click:function(t){e.ActiveIndex=i}}},[e._v(" "+e._s(t.dictvalue)+" ")])})),0),a("div",{staticClass:"right"},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.addInput()}}},[e._v("添 加")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==e.ActiveIndex},on:{click:function(t){return e.editInput()}}},[e._v("编 辑")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==e.ActiveIndex},on:{click:function(t){return e.delItem()}}},[e._v("删 除")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==e.ActiveIndex},on:{click:function(t){return e.getMoveUp()}}},[e._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==e.ActiveIndex},on:{click:function(t){return e.getMoveDown()}}},[e._v("下 移")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.submitUpdate}},[e._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(t){e.SYSM07B1Visible=!1,e.bindData()}}},[e._v("取 消")])],1)]):e._e()],1)},r=[],o=(a("a434"),a("e9c4"),a("b775")),l=a("333d"),s=a("b0b8"),n={components:{Pagination:l["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(e,t){void 0==e&&(this.lst=[]);for(var a=0;a<e.length;a++)e[a].rownum=a}},created:function(){},methods:{getCurrentRow:function(e){this.selrows=e,this.$forceUpdate(),this.$emit("singleSel",e)},bindData:function(){var e=this;this.lst=[],o["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(t){if(200==t.data.code){if(null==t.data.data)return e.formdata={item:[]},void(e.listLoading=!1);if(e.formdata=t.data.data,0==e.formdata.item.length)return void(e.lst=[]);for(var a=0;a<e.formdata.item.length;a++)e.lst.push(e.formdata.item[a])}})).catch((function(t){e.$message.error("请求出错")}))},rowIndex:function(e){var t=e.row,a=e.rowIndex;t.row_index=a},rowClick:function(e){this.radio=e.row_index,this.getCurrentRow(e)},addInput:function(){var e=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(e){if(!e)return"输入不能为空"}}).then((function(t){var a=t.value;e.addItem(a)})).catch((function(e){console.log(e)}))},addItem:function(e){s.setOptions({checkPolyphone:!1,charCase:1});var t={cssclass:"",defaultmark:0,dictcode:s.getFullChars(e),dictvalue:e,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(t)},editInput:function(){if(-1!=this.ActiveIndex){var e=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:e.lst[e.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(e){if(!e)return"输入不能为空"}}).then((function(t){var a=t.value;e.lst[e.ActiveIndex].dictvalue=a,s.setOptions({checkPolyphone:!1,charCase:1}),e.lst[e.ActiveIndex].dictcode=s.getFullChars(a)})).catch((function(e){console.log(e)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.lst.splice(e.ActiveIndex,1),e.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var e=this.lst[this.ActiveIndex],t=this.lst[this.ActiveIndex].rownum;this.lst.splice(t,1),this.lst.splice(t-1,0,e),this.ActiveIndex-=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var e=this.lst[this.ActiveIndex],t=this.lst[this.ActiveIndex].rownum;this.lst.splice(t,1),this.lst.splice(t+1,0,e),this.ActiveIndex+=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var e=this;this.formdata.item=this.lst,o["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(t){200==t.data.code?(e.$message.success(t.data.msg||"保存成功"),e.SYSM07B1Visible=!1):e.$message.warning(t.data.msg||"保存失败")})).catch((function(t){e.$message.error("请求错误")}))}}},c=n,d=(a("af2b"),a("2877")),m=Object(d["a"])(c,i,r,!1,null,"d2ba3d7a",null);t["a"]=m.exports},"69d1":function(e,t,a){},"7de4":function(e,t,a){},"841c":function(e,t,a){"use strict";var i=a("d784"),r=a("825a"),o=a("1d80"),l=a("129f"),s=a("14c3");i("search",1,(function(e,t,a){return[function(t){var a=o(this),i=void 0==t?void 0:t[e];return void 0!==i?i.call(t,a):new RegExp(t)[e](String(a))},function(e){var i=a(t,e,this);if(i.done)return i.value;var o=r(e),n=String(this),c=o.lastIndex;l(c,0)||(o.lastIndex=0);var d=s(o,n);return l(o.lastIndex,c)||(o.lastIndex=c),null===d?-1:d.index}]}))},"897b":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.formvisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("FormEdit",e._g({ref:"formedit",attrs:{idx:e.idx},on:{closeForm:e.closeForm}},{compForm:e.compForm,changeIdx:e.changeIdx,bindData:e.bindData}))],1):e._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[a("ListHeader",{attrs:{tableForm:e.tableForm},on:{btnAdd:function(t){return e.showForm(0)},btnSearch:function(t){return e.$refs.tableList.search(t)},bindData:e.bindData,advancedSearch:function(t){return e.$refs.tableList.advancedSearch(t)},btnExport:function(t){return e.$refs.tableList.btnExport()},allDelete:function(t){return e.$refs.tableList.allDelete()},bindColumn:function(t){return e.$refs.tableList.getColumn()}}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{directives:[{name:"show",rawName:"v-show",value:e.treevisble,expression:"treevisble"}],attrs:{span:3}},[a("div",{staticStyle:{"font-size":"14px",color:"#606266"}},[a("div",{staticClass:"groupTitle"},[a("span",[e._v("供应商分组")]),a("i",{staticClass:"el-icon-s-tools",style:{color:e.treeeditable?"#1e80ff":""},on:{click:function(t){return e.treeEdit()}}})]),a("el-tree",{attrs:{data:e.groupdata,"node-key":"id","default-expand-all":""},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.node,r=t.data;return a("span",{staticClass:"custom-tree-node"},[a("span",{on:{click:function(){return e.handleNodeClick(r)}}},[e._v(e._s(i.label)+" ")]),a("span",{directives:[{name:"show",rawName:"v-show",value:e.treeeditable,expression:"treeeditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==r.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-edit"},on:{click:function(){return e.editTreeNode(r)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:e.treeeditable,expression:"treeeditable"}]},[a("el-button",{staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-circle-plus-outline"},on:{click:function(){return e.addTreeChild(r)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:e.treeeditable,expression:"treeeditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==r.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-delete-solid"},on:{click:function(){return e.delTreeNode(r)}}})],1)])}}])})],1)]),a("el-col",{attrs:{span:21}},[a("TableList",{ref:"tableList",on:{changeIdx:e.changeIdx,showForm:e.showForm,sendTableForm:e.sendTableForm}})],1)],1)],1)],1),e.gropuformvisible?a("el-dialog",{attrs:{title:"供应商分类","append-to-body":!0,visible:e.gropuformvisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.gropuformvisible=t}}},[a("Group",e._g({ref:"group",attrs:{idx:e.idx,pid:e.pid}},{compForm:e.compForm,bindData:e.bindTreeData,closeDialog:e.closeDialog}))],1):e._e()],1)},r=[],o=a("2909"),l=(a("99af"),a("d81d"),a("ac1f"),a("841c"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{class:"filter-container flex j-s a-c"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnSearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnSearch},slot:"append"},[e._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:e.btnAdd}},[e._v(" 添加 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete",plain:"",size:"mini"},on:{click:function(t){return e.$emit("allDelete")}}},[e._v(" 删除 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:e.$store.state.advancedSearch.modulecode==e.tableForm.formcode?"primary":"default"},on:{click:function(t){return e.openSearchForm()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:e.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:function(t){return e.$emit("btnExport")}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(t){return e.openDialog()}}})],1)]),e.setcolumsvisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:e.setcolumsvisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.setcolumsvisible=t}}},[a("SetColums",{ref:"setcolums",attrs:{code:e.tableForm.formcode,tableForm:e.tableForm,baseparam:"/SaDgFormat"},on:{bindData:function(t){return e.$emit("bindColumn")},closeDialog:function(t){e.setcolumsvisible=!1}}})],1):e._e(),a("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:e.searchvisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.searchvisible=t}}},[a("SearchForm",{ref:"searchForm",attrs:{code:e.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:e.advancedSearch,closedDialog:function(t){e.searchvisible=!1},bindData:e.bindData}})],1)],1)}),s=[],n={name:"Listheader",components:{},props:["tableForm"],data:function(){return{strfilter:"",setcolumsvisible:!1,searchvisible:!1}},methods:{openDialog:function(){this.setcolumsvisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(e){this.iShow=!1,this.$emit("advancedSearch",e),this.searchvisible=!1},openSearchForm:function(){var e=this;this.searchvisible=!0,setTimeout((function(){e.$refs.searchForm.getInit()}),100)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},c=n,d=(a("fad3"),a("2877")),m=Object(d["a"])(c,l,s,!1,null,"ef789c7e",null),u=m.exports,f=a("b775"),p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["save","print","operate"],formdata:e.formdata,operateBar:e.operateBar,processBar:e.processBar,formstate:e.formstate,submitting:e.submitting},on:{submitForm:e.submitForm,closeForm:e.closeForm,printButton:function(t){return e.$refs.PrintServer.printButton(0,1)},clickMethods:e.clickMethods}})],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:e.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-tabs",{staticStyle:{position:"absolute",top:"10px",width:"100%",background:"white","z-index":"99",height:"50px"},attrs:{type:"card"},on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"基本信息",name:"first"}}),a("el-tab-pane",{attrs:{label:"附加信息",name:"second"}})],1),a("div",{staticStyle:{height:"50px"}}),a("div",{staticClass:"Info",staticStyle:{"margin-top":"10px"}},["first"==e.activeName?a("div",{staticClass:"basicInfo"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":e.formLabelWidth,rules:e.formRules,"auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("groupuid")}}},[a("el-form-item",{attrs:{label:"编码",prop:"groupuid"}},[a("el-input",{attrs:{placeholder:"请输入编码",readonly:!!e.idx,size:"small"},model:{value:e.formdata.groupuid,callback:function(t){e.$set(e.formdata,"groupuid","string"===typeof t?t.trim():t)},expression:"formdata.groupuid"}},[a("i",{directives:[{name:"show",rawName:"v-show",value:!e.idx,expression:"!idx"}],staticClass:"el-icon-edit getNextCode",attrs:{slot:"suffix"},on:{click:e.getNextCode},slot:"suffix"})])],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"客户",prop:"groupname"}},[a("el-input",{attrs:{placeholder:"请输入客户",clearable:"",size:"small"},model:{value:e.formdata.groupname,callback:function(t){e.$set(e.formdata,"groupname",t)},expression:"formdata.groupname"}})],1)],1)]),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"简写"}},[a("el-input",{attrs:{placeholder:"请输入简写",clearable:"",size:"small"},model:{value:e.formdata.abbreviate,callback:function(t){e.$set(e.formdata,"abbreviate",t)},expression:"formdata.abbreviate"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("wggroupid")}}},[a("el-form-item",{attrs:{label:"供应商分组"}},[a("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{options:e.groupnameOptions,props:e.defaultProps,"show-all-levels":!1,placeholder:"请选择分组名称",size:"small"},on:{change:e.handleChangeGroupid},model:{value:e.formdata.wggroupid,callback:function(t){e.$set(e.formdata,"wggroupid",t)},expression:"formdata.wggroupid"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"供应商类型"}},[a("el-popover",{staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click"},on:{show:function(t){return e.$refs.groupclassRef.bindData()}},model:{value:e.groupclassrefsvisible,callback:function(t){e.groupclassrefsvisible=t},expression:"groupclassrefsvisible"}},[a("SelDictionaries",{ref:"groupclassRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"app_wg_supplier.groupclass"},on:{singleSel:function(t){e.formdata.groupclass=t.dictvalue,e.groupclassrefsvisible=!1},closedic:function(t){e.groupclassrefsvisible=!1}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择供应商类型",clearable:"",size:"small"},model:{value:e.formdata.groupclass,callback:function(t){e.$set(e.formdata,"groupclass",t)},expression:"formdata.groupclass"}})],1)],1)],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"状态"}},[a("el-checkbox",{attrs:{label:"有效","true-label":1,"false-label":0,size:"mini"},model:{value:e.formdata.enabledmark,callback:function(t){e.$set(e.formdata,"enabledmark",t)},expression:"formdata.enabledmark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"联系人"}},[a("el-input",{attrs:{placeholder:"请输入联系人",clearable:"",size:"small"},model:{value:e.formdata.linkman,callback:function(t){e.$set(e.formdata,"linkman",t)},expression:"formdata.linkman"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"电话"}},[a("el-input",{attrs:{placeholder:"电话",clearable:"",size:"small"},model:{value:e.formdata.telephone,callback:function(t){e.$set(e.formdata,"telephone",t)},expression:"formdata.telephone"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"手机"}},[a("el-input",{attrs:{placeholder:"请输入手机号码",clearable:"",size:"small"},model:{value:e.formdata.mobile,callback:function(t){e.$set(e.formdata,"mobile",t)},expression:"formdata.mobile"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"邮编"}},[a("el-input",{attrs:{placeholder:"请输入邮编",clearable:"",size:"small"},model:{value:e.formdata.groupzip,callback:function(t){e.$set(e.formdata,"groupzip",t)},expression:"formdata.groupzip"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"传真"}},[a("el-input",{attrs:{placeholder:"请输入传真",clearable:"",size:"small"},model:{value:e.formdata.groupfax,callback:function(t){e.$set(e.formdata,"groupfax",t)},expression:"formdata.groupfax"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"业务员"}},[a("el-input",{attrs:{placeholder:"请输入业务员",clearable:"",size:"small"},model:{value:e.formdata.seller,callback:function(t){e.$set(e.formdata,"seller",t)},expression:"formdata.seller"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"8px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"地址"}},[a("el-input",{attrs:{placeholder:"请输入地址",clearable:"",size:"small",type:"textarea"},model:{value:e.formdata.groupadd,callback:function(t){e.$set(e.formdata,"groupadd",t)},expression:"formdata.groupadd"}})],1)],1)],1)],1)],1):e._e(),"second"==e.activeName?a("div",{staticClass:"otherInfo"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":e.formLabelWidth}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"次联系人"}},[a("el-input",{attrs:{placeholder:"请输入次联系人",clearable:"",size:"small"},model:{value:e.formdata.linkmans,callback:function(t){e.$set(e.formdata,"linkmans",t)},expression:"formdata.linkmans"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"次联系电话"}},[a("el-input",{attrs:{placeholder:"请输入次联系电话",clearable:"",size:"small"},model:{value:e.formdata.telephones,callback:function(t){e.$set(e.formdata,"telephones",t)},expression:"formdata.telephones"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"次联系手机"}},[a("el-input",{attrs:{placeholder:"请输入次联系手机",clearable:"",size:"small"},model:{value:e.formdata.mobiles,callback:function(t){e.$set(e.formdata,"mobiles",t)},expression:"formdata.mobiles"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"厂商状态"}},[a("el-popover",{staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click"},on:{show:function(t){return e.$refs.groupclassRef.bindData()}},model:{value:e.groupstaterefsvisible,callback:function(t){e.groupstaterefsvisible=t},expression:"groupstaterefsvisible"}},[a("SelDictionaries",{ref:"groupclassRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"app_wg_supplier.groupstate"},on:{singleSel:function(t){e.formdata.groupstate=t.dictvalue,e.groupstaterefsvisible=!1},closedic:function(t){e.groupstaterefsvisible=!1}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择厂商状态",clearable:"",size:"small"},model:{value:e.formdata.groupstate,callback:function(t){e.$set(e.formdata,"groupstate",t)},expression:"formdata.groupstate"}})],1)],1)],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"厂商等级"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"厂商等级",size:"small",clearable:""},model:{value:e.formdata.grouplevel,callback:function(t){e.$set(e.formdata,"grouplevel",t)},expression:"formdata.grouplevel"}},e._l(e.levelData,(function(t){return a("el-option",{key:t.label,attrs:{label:t.label,value:t.label}},[a("span",{staticStyle:{float:"left"}},[a("svg-icon",{style:{"font-size":"15px"},attrs:{"icon-class":t.svg}})],1),a("span",{staticStyle:{"padding-left":"6px"}},[e._v(e._s(t.label))])])})),1)],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"厂商来源"}},[a("el-popover",{staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click"},on:{show:function(t){return e.$refs.sourceRef.bindData()}},model:{value:e.sourcerefsvisible,callback:function(t){e.sourcerefsvisible=t},expression:"sourcerefsvisible"}},[a("SelDictionaries",{ref:"sourceRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"app_wg_supplier.source"},on:{singleSel:function(t){e.formdata.source=t.dictvalue,e.sourcerefsvisible=!1},closedic:function(t){e.sourcerefsvisible=!1}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择厂商来源",clearable:"",size:"small"},model:{value:e.formdata.source,callback:function(t){e.$set(e.formdata,"source",t)},expression:"formdata.source"}})],1)],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"厂商标签"}},[a("el-popover",{staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click"},on:{show:function(t){return e.$refs.grouplabelRef.bindData()}},model:{value:e.grouplabelrefsvisible,callback:function(t){e.grouplabelrefsvisible=t},expression:"grouplabelrefsvisible"}},[a("SelDictionaries",{ref:"grouplabelRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"app_wg_supplier.grouplabel"},on:{singleSel:function(t){e.formdata.grouplabel=t.dictvalue,e.grouplabelrefsvisible=!1},closedic:function(t){e.grouplabelrefsvisible=!1}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择厂商标签",clearable:"",size:"small"},model:{value:e.formdata.grouplabel,callback:function(t){e.$set(e.formdata,"grouplabel",t)},expression:"formdata.grouplabel"}})],1)],1)],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"国家"}},[a("el-input",{attrs:{placeholder:"请输入国家",clearable:"",size:"small"},model:{value:e.formdata.country,callback:function(t){e.$set(e.formdata,"country",t)},expression:"formdata.country"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"省份"}},[a("el-input",{attrs:{placeholder:"请输入省份",clearable:"",size:"small"},model:{value:e.formdata.province,callback:function(t){e.$set(e.formdata,"province",t)},expression:"formdata.province"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"有效期"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"请选择有效期"},model:{value:e.formdata.invaliddate,callback:function(t){e.$set(e.formdata,"invaliddate",t)},expression:"formdata.invaliddate"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"送货地址"}},[a("el-input",{attrs:{placeholder:"请输入送货地址",clearable:"",size:"small"},model:{value:e.formdata.deliveradd,callback:function(t){e.$set(e.formdata,"deliveradd",t)},expression:"formdata.deliveradd"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"发票地址"}},[a("el-input",{attrs:{placeholder:"请输入发票地址",clearable:"",size:"small"},model:{value:e.formdata.invoiceadd,callback:function(t){e.$set(e.formdata,"invoiceadd",t)},expression:"formdata.invoiceadd"}})],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[e._v("银行 - 信用")]),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"付款方式"}},[a("el-popover",{staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click"},on:{show:function(t){return e.$refs.paymentmethodRef.bindData()}},model:{value:e.paymentmethodrefsvisible,callback:function(t){e.paymentmethodrefsvisible=t},expression:"paymentmethodrefsvisible"}},[a("SelDictionaries",{ref:"paymentmethodRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"app_wg_supplier.paymentmethod"},on:{singleSel:function(t){e.formdata.paymentmethod=t.dictvalue,e.paymentmethodrefsvisible=!1},closedic:function(t){e.paymentmethodrefsvisible=!1}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择付款方式",clearable:"",size:"small"},model:{value:e.formdata.paymentmethod,callback:function(t){e.$set(e.formdata,"paymentmethod",t)},expression:"formdata.paymentmethod"}})],1)],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"开户银行"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入开户银行",clearable:"",size:"small"},model:{value:e.formdata.depositbank,callback:function(t){e.$set(e.formdata,"depositbank",t)},expression:"formdata.depositbank"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"银行账号"}},[a("el-input",{attrs:{placeholder:"请输入银行账号",clearable:"",size:"small"},model:{value:e.formdata.bankaccount,callback:function(t){e.$set(e.formdata,"bankaccount",t)},expression:"formdata.bankaccount"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"信用代码"}},[a("el-input",{attrs:{placeholder:"请输入信用代码",clearable:"",size:"small"},model:{value:e.formdata.creditcode,callback:function(t){e.$set(e.formdata,"creditcode",t)},expression:"formdata.creditcode"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"信用金额(￥)"}},[a("el-input",{attrs:{placeholder:"请输入信用金额数量",clearable:"",size:"small"},model:{value:e.formdata.creditcquantity,callback:function(t){e.$set(e.formdata,"creditcquantity",t)},expression:"formdata.creditcquantity"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"信用日期数量"}},[a("el-input",{attrs:{placeholder:"请输入信用日期数量",clearable:"",size:"small"},model:{value:e.formdata.creditdquantity,callback:function(t){e.$set(e.formdata,"creditdquantity",t)},expression:"formdata.creditdquantity"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"信用日期单位"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择信用日期单位",size:"small"},model:{value:e.formdata.creditduint,callback:function(t){e.$set(e.formdata,"creditduint",t)},expression:"formdata.creditduint"}},[a("el-option",{attrs:{label:"天",value:"day"}}),a("el-option",{attrs:{label:"月",value:"month"}})],1)],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"8px"}},[a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"信用描述"}},[a("el-input",{attrs:{placeholder:"请输入信用描述",clearable:"",size:"small",type:"textarea"},model:{value:e.formdata.credit,callback:function(t){e.$set(e.formdata,"credit",t)},expression:"formdata.credit"}})],1)],1)],1)],1)],1):e._e()]),a("el-divider")],1),a("el-form",{staticClass:"form",attrs:{"label-width":e.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.modifydate)))])])],1)],1)],1)],1)]),a("PrintServer",{ref:"PrintServer",attrs:{formdata:e.formdata,printcode:"D01M01B2Edit",commonurl:"/D01M01B2/printBill",weburl:"/D01M01B2/printWebBill"}})],1)},h=[],b=a("ade3");a("b64b");const g={add(e){return new Promise((t,a)=>{var i=JSON.stringify(e);f["a"].post("/D01M01B2/create",i).then(e=>{console.log(e),200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var i=JSON.stringify(e);f["a"].post("/D01M01B2/update",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){f["a"].get("/D01M01B2/delete?key="+e.formdata.id).then(t=>{200==t.data.code?(e.$message.success(t.data.msg||"删除成功"),e.$emit("closeForm")):e.$message.warning(t.data.msg||"删除失败")}).catch(t=>{e.$message.error(er||"服务请求错误")})}};var v=g,w=a("5c73"),x=["id","wggroupid","groupuid","groupname","abbreviate","groupclass","linkman","telephone","groupfax","groupadd","remark","invaliddate","grouptype","creditduint","creditdquantity","creditcuint","creditcquantity","rownum","mobile","linkmans","telephones","mobiles","country","province","groupzip","deliveradd","invoiceadd","seller","grouplabel","grouplevel","groupstate","source","credit","paymentmethod","creditcode","depositbank","bankaccount","enabledmark","deletemark","deletelister","deletelisterid","deletedate","fmaccoid","foreaccoid","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],y={params:x},k=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]}],S=[],$={name:"Formedit",components:{selDictionaries:w["a"]},props:["idx","title"],data:function(){var e;return{operateBar:k,processBar:S,formdata:(e={wggroupid:"",groupuid:"",groupclass:"",groupname:"",abbreviate:"",linkman:"",telephone:"",mobile:"",groupadd:"",groupzip:"",groupfax:"",seller:"",remark:"",linkmans:"",telephones:"",mobiles:"",deliveradd:"",invoiceadd:"",creditcquantity:0,invaliddate:"",groupstate:"",grouplevel:"",country:"",source:"",province:"",grouptype:"供应商",enabledmark:1,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,accountAmount:0,credit:""},Object(b["a"])(Object(b["a"])(Object(b["a"])(Object(b["a"])(Object(b["a"])(Object(b["a"])(Object(b["a"])(Object(b["a"])(Object(b["a"])(Object(b["a"])(e,"creditcquantity",0),"creditcuint","人民币"),"creditdquantity",0),"creditduint","day"),"deletemark",0),"grouplabel",""),"overdueInvoice",0),"totalAmount",0),"totalInvoice",0),"foreaccoid",""),Object(b["a"])(Object(b["a"])(Object(b["a"])(Object(b["a"])(Object(b["a"])(e,"foreacconame",""),"foreaccoName",""),"fmaccoid",""),"fmacconame",""),"fmaccoName","")),formRules:{groupname:[{required:!0,trigger:"blur",message:"供应商为必填项"}],linkman:[{required:!0,trigger:"blur",message:"联系人为必填项"}],groupuid:[{required:!0,trigger:"blur",message:"编码为必填项"}],wggroupid:[{required:!0,trigger:"blur",message:"供应商分组为必填项"}]},activeName:"first",formLabelWidth:"100px",groupclassrefsvisible:!1,groupstaterefsvisible:!1,sourcerefsvisible:!1,grouplabelrefsvisible:!1,defaultProps:{children:"children",label:"label",value:"id"},groupnameOptions:[],infovisible:!1,companyURL:"",levelData:[{label:"A",svg:"trophy1"},{label:"B",svg:"trophy2"},{label:"C",svg:"trophy3"},{label:"D",svg:""},{label:"E",svg:""},{label:"F",svg:""}],paymentmethodrefsvisible:!1,fmaccovisible:!1,foreaccovisible:!1,formstate:0,submitting:0}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(e,t){this.bindData()}},created:function(){},mounted:function(){this.bindData(),this.bindDataByuidgroupname()},methods:{bindData:function(){var e=this;this.formstate=0,0!=this.idx&&f["a"].get("/D01M01B2/getEntity?key=".concat(this.idx)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.foreaccoName=e.formdata.foreacconame,e.formdata.fmaccoName=e.formdata.fmacconame,e.formstate=e.formdata.id?1:0):e.$alert(t.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){e.closeForm()}})})).catch((function(t){e.$message.error("请求错误")}))},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.saveForm()}))},handleSelect:function(e){this.cleValidate("groupname"),this.companyURL=e.url},openCompany:function(){var e=this;""!=this.formdata.groupname||this.companyURL?(this.infovisible=!0,this.$nextTick((function(){e.$refs.companyInfo.bindData()}))):this.$message.warning("请填写客户名称")},handleChangeGroupid:function(e){e[e.length-1];this.formdata.wggroupid=e[e.length-1]},bindDataByuidgroupname:function(){var e=this;f["a"].get("/SaBillGroup/getListByModuleCode?Code=D01M01B2").then((function(t){if(200==t.data.code){var a=t.data.data.map((function(e){return{id:e.id,pid:e.parentid,label:e.groupname}})),i=[{id:"0",pid:"root",label:"供应商分组"}],r=[].concat(Object(o["a"])(a),i);e.groupnameOptions=e.transData(r,"id","pid","children")}}))},transData:function(e,t,a,i){for(var r=[],o={},l=t,s=a,n=i,c=0,d=0,m=e.length;c<m;c++)o[e[c][l]]=e[c];for(;d<m;d++){var u=e[d],f=o[u[s]];f?(!f[n]&&(f[n]=[]),f[n].push(u)):r.push(u)}return r},handleClick:function(e,t){console.log(e,t)},saveForm:function(){var e=this;this.submitting=1;var t={};t=this.$getParam(y,t,this.formdata),0==this.idx?v.add(t).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.$emit("changeIdx",t.data.id),e.$emit("bindData"),e.bindData(),e.submitting=0)})).catch((function(t){e.$message.warning("保存失败")})):(v.update(t).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.$emit("bindData"),e.bindData(),e.submitting=0)})).catch((function(t){e.$message.warning("保存失败")})),setTimeout((function(){e.submitting=0}),500))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){v.delete(e),e.closeForm()})).catch((function(){}))},clickMethods:function(e){this[e.meth](e.param)},getNextCode:function(){var e=this;this.formdata.groupuid||f["a"].get("/D01M01B2/getNextCode?type=2").then((function(t){200==t.data.code?e.formdata.groupuid=t.data.data:e.$message.warning("获取编码失败")}))},cleValidate:function(e){this.$refs.formdata.clearValidate(e)}}},D=$,_=(a("e81c"),Object(d["a"])(D,p,h,!1,null,"d0fcafae",null)),C=_.exports,F=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticStyle:{width:"100%"}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":e.formLabelWidth,rules:e.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"分组名称",prop:"groupname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入分组名称",size:"small"},on:{input:e.writeCode},model:{value:e.formdata.groupname,callback:function(t){e.$set(e.formdata,"groupname",t)},expression:"formdata.groupname"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("groupcode")}}},[a("el-form-item",{attrs:{label:"分组编码",prop:"groupcode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入分组编码",size:"small"},model:{value:e.formdata.groupcode,callback:function(t){e.$set(e.formdata,"groupcode",t)},expression:"formdata.groupcode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:e.formdata.rownum,callback:function(t){e.$set(e.formdata,"rownum",t)},expression:"formdata.rownum"}})],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":e.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.lister))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormats")(e.formdata.modifydate)))])])],1)],1)],1)],1)]),a("div",{staticClass:"button-container flex j-end"},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(t){return e.submitForm("formdata")}}},[e._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.$emit("closeDialog")}}},[e._v(" 关 闭")])],1)])},z=[],O=(a("e9c4"),a("b0b8")),B={name:"Formedit",props:["idx","pid"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,parentid:"",modulecode:"D01M01B2",groupcode:"",groupname:"",rownum:0,remark:"",enabledmark:1},formRules:{modulecode:[{required:!0,trigger:"blur",message:"功能编码为必填项"}],groupcode:[{required:!0,trigger:"blur",message:"分组编码为必填项"}],groupname:[{required:!0,trigger:"blur",message:"分组名称为必填项"}]},formLabelWidth:"100px",dialogvisible:!1,delDialogVisible:!1,option:""}},computed:{formcontainHeight:function(){return"300px"}},watch:{idx:function(e,t){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var e=this;0!=this.idx?f["a"].get("/SaBillGroup/getEntity?key=".concat(this.idx)).then((function(t){200==t.data.code&&(e.formdata=t.data.data)})):this.formdata.parentid=this.idx},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.saveForm()}))},saveForm:function(){var e=this;this.pid&&(this.formdata.parentid=this.pid),0==this.idx?f["a"].post("/SaBillGroup/create",JSON.stringify(this.formdata)).then((function(t){e.$emit("bindData"),e.$emit("closeDialog")})):f["a"].post("/SaBillGroup/update",JSON.stringify(this.formdata)).then((function(t){e.$emit("bindData"),e.$emit("closeDialog")}))},closeForm:function(){this.$emit("closeForm")},check:function(){console.log("check")},cleValidate:function(e){this.$refs.formdata.clearValidate(e)},writeCode:function(e){O.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.groupcode=O.getFullChars(e)}}},I=B,N=(a("f29f"),Object(d["a"])(I,F,z,!1,null,"0b88b386",null)),P=N.exports,T=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("ve-table",{key:e.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":e.tableMaxHeight,"scroll-width":e.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:e.customData,"table-data":e.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:e.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":e.virtualScrollOption,"checkbox-option":e.checkboxOption,"footer-data":e.footerData,"fixed-footer":!0,"sort-option":e.sortOption}}),a("div",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("div",{staticClass:"flex a-c"},[a("Pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.getList}})],1),a("div",{staticStyle:{"margin-right":"40px"}},[a("Scene",{ref:"scene",attrs:{code:e.tableForm.formcode},on:{bindData:e.bindData}})],1)])],1)},L=[],A=a("b85c"),j=a("c7eb"),q=a("1da1"),M=(a("a9e3"),a("d3b7"),a("3ca3"),a("c7cd"),a("159b"),a("ddb0"),{formcode:"D01M01B2List",item:[{itemcode:"groupuid",itemname:"供应商编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"供应商",minwidth:"80",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"abbreviate",itemname:"简写",minwidth:"80",displaymark:1,overflow:1,datasheet:"App_Workgroup.abbreviate"},{itemcode:"groupclass",itemname:"类型",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupclass"},{itemcode:"seller",itemname:"业务员",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.seller"},{itemcode:"groupadd",itemname:"地址",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupadd"},{itemcode:"linkman",itemname:"联系人",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.linkman"},{itemcode:"telephone",itemname:"联系电话",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.telephone"},{itemcode:"groupfax",itemname:"传真",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupfax"},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.remark"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"App_Workgroup.lister"},{itemcode:"createdate",itemname:"创建时间",minwidth:"120",displaymark:1,overflow:1,sortable:1,datasheet:"App_Workgroup.createdate"},{itemcode:"operate",itemname:"操作",minwidth:"100",displaymark:1,overflow:1}]}),R={components:{},props:["online"],data:function(){var e=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:M,customList:[],selectList:[],customData:[],columnHidden:[],footerData:[],checkboxOption:{selectedRowKeys:[],selectedRowChange:function(t){t.row,t.isSelected;var a=t.selectedRowKeys;if(e.selectList=[],e.checkboxOption.selectedRowKeys=a,0!=a.length)for(var i=0;i<a.length;i++)e.selectList.push({id:a[i]})},selectedAllChange:function(t){var a=t.isSelected,i=t.selectedRowKeys;if(a){if(e.checkboxOption.selectedRowKeys=i,0!=i.length)for(var r=0;r<i.length;r++)e.selectList.push({id:i[r]})}else e.selectList=[],e.checkboxOption.selectedRowKeys=[]}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(t){var a=t.startRowIndex;e.rowScroll=a}},sortOption:{sortChange:function(t){e.changeSort(t)}}}},computed:{tableMaxHeight:function(){var e=window.innerHeight-160;return e<600&&(e=600),e+"px"},tableMinWidth:function(){var e="calc(100vw - 64px)";if(0!=this.tableForm.item.length){e=0;for(var t=0;t<this.tableForm.item.length;t++){var a=this.tableForm.item[t];a.displaymark&&(e+=Number(a.minwidth))}}return e}},methods:{bindData:function(){var e=this,t="/D01M01B2/getPageList";this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),f["a"].post(t,JSON.stringify(this.queryParams)).then((function(t){200==t.data.code?(e.lst=t.data.data.list,e.total=t.data.data.total):e.$message.warning(t.data.msg||"获取数据失败")})).catch((function(t){e.$message.error(t||"请求错误")}))},getColumn:function(){var e=this;return Object(q["a"])(Object(j["a"])().mark((function t(){return Object(j["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$getColumn(e.tableForm.formcode,M).then((function(t){e.customList=t.customList,e.tableForm=Object.assign({},t.colList),e.initTable(e.tableForm),e.$emit("sendTableForm",e.tableForm)}));case 1:case"end":return t.stop()}}),t)})))()},initTable:function(e){var t=this,a=(this.$createElement,[]);this.columnHidden=[],e["item"].forEach((function(e,i){var r={field:e.itemcode,key:e.itemcode,title:e.itemname,width:isNaN(e.minwidth)?e.minwidth:Number(e.minwidth),displaymark:e.displaymark,fixed:!!e.fixed&&(1==e.fixed?"left":"right"),ellipsis:!!e.overflow&&{showTitle:!0},align:e.aligntype?e.aligntype:"center",sortBy:!!e.sortable&&"",renderBodyCell:function(a,i){var r=a.row;a.column,a.rowIndex;if("billdate"==e.itemcode||"createdate"==e.itemcode)return t.$options.filters.dateFormat(r[e.itemcode]);if("groupuid"==e.itemcode){var o=i("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return t.showForm(r.id)}}},[r[e.itemcode]?r[e.itemcode]:"单据编码"]);return o}if("operate"==e.itemcode){o=i("div",[i("router-link",{attrs:{to:"/D01/M01R2/key="+r.id},style:"color: #409eff"},["详情"])]);return o}return r[e.itemcode]}};e.displaymark||t.columnHidden.push(e.itemcode),a.push(r)})),a.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(e,a){e.row,e.column;var i=e.rowIndex;return i+t.rowScroll+1}}),a.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.customData=a,this.keynum+=1},allDelete:function(){var e=this;0!=this.selectList.length?this.$confirm("此操作将永久删除记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.deleteRows()})).catch((function(){})):this.$message.warning("请选择客户内容")},deleteRows:function(e,t){var a=this;return Object(q["a"])(Object(j["a"])().mark((function e(){var t,i,r,o,l,s;return Object(j["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=a,i=a.selectList,!i){e.next=22;break}r=[],o=Object(A["a"])(i),e.prev=5,s=Object(j["a"])().mark((function e(){var t,a;return Object(j["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=l.value,a=new Promise((function(e,a){f["a"].get("/D01M01B2/delete?key=".concat(t.id)).then((function(i){200==i.data.code?0==i.data.data?a("删除失败,"+t.groupname+"在系统中已使用"):e("删除成功"):a("删除失败")})).catch((function(e){a("删除失败")}))})),r.push(a);case 3:case"end":return e.stop()}}),e)})),o.s();case 8:if((l=o.n()).done){e.next=12;break}return e.delegateYield(s(),"t0",10);case 10:e.next=8;break;case 12:e.next=17;break;case 14:e.prev=14,e.t1=e["catch"](5),o.e(e.t1);case 17:return e.prev=17,o.f(),e.finish(17);case 20:return e.next=22,Promise.all(r).then((function(e){t.$message.success("删除成功")})).catch((function(e){t.$message.warning(e)})).finally((function(){a.selectList=[],a.checkboxOption.selectedRowKeys=[],a.bindData()}));case 22:case"end":return e.stop()}}),e,null,[[5,14,17,20]])})))()},getList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},search:function(e){""!=e?this.queryParams.SearchPojo={groupuid:e,groupname:e,wggroupid:e,abbreviate:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},searchGroup:function(e){""!=e?this.queryParams.SearchPojo={wggroupid:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(e){this.$advancedSearch(this,e)},changeSort:function(e){for(var t in e)if(""!=e[t]){var a={prop:t};"desc"==e[t]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(a,this.tableForm),this.bindData();break}},showForm:function(e){this.$emit("showForm",e)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,"供应商")}}},E=R,W=(a("9254"),Object(d["a"])(E,T,L,!1,null,"342bfeec",null)),V=W.exports,G={name:"",components:{ListHeader:u,FormEdit:C,Group:P,TableList:V},data:function(){return{lst:[],formvisible:!1,treetitle:"供应商分组",gropuformvisible:!1,treevisble:!0,groupdata:[],treeeditable:!1,idx:0,showhelp:!1,tableForm:{}}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){this.bindTreeData()},mounted:function(){this.bindData(),this.$refs.tableList.getColumn()},methods:{bindData:function(){this.$refs.tableList.bindData()},bindTreeData:function(){var e=this;f["a"].get("/SaBillGroup/getListByModuleCode?Code=D01M01B2").then((function(t){if(200==t.data.code){var a=t.data.data.map((function(e){return{id:e.id,pid:e.parentid,label:e.groupname}})),i=[{id:"0",pid:"root",label:e.treetitle}],r=[].concat(Object(o["a"])(a),i);e.groupdata=e.transData(r,"id","pid","children")}}))},sendTableForm:function(e){this.tableForm=e},showForm:function(e){this.idx=e,this.formvisible=!0},closeForm:function(){this.formvisible=!1,this.bindData()},compForm:function(){this.bindData(),this.formvisible=!1},closeDialog:function(){this.gropuformvisible=!1,this.bindTreeData()},handleNodeClick:function(e){if(0==e.id){var t="";this.$refs.tableList.search(t)}else{var a=e.id;this.$refs.tableList.searchGroup(a)}},treeEdit:function(){this.treeeditable=!this.treeeditable},editTreeNode:function(e){this.pid=e.pid,this.showGroupform(e.id)},addTreeChild:function(e){this.pid=e.id,this.showGroupform(0)},delTreeNode:function(e){var t=this;console.log(e),e.children?this.$message({message:"警告,该节点下存在子节点不能删除",type:"warning"}):this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){f["a"].get("/SaBillGroup/delete?key=".concat(e.id)).then((function(){console.log("执行关闭保存"),t.$message.success({message:"删除成功！"}),t.bindTreeData()})).catch((function(){t.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},showGroupform:function(e){this.idx=e,this.gropuformvisible=!0},transData:function(e,t,a,i){for(var r=[],o={},l=t,s=a,n=i,c=0,d=0,m=e.length;c<m;c++)o[e[c][l]]=e[c];for(;d<m;d++){var u=e[d],f=o[u[s]];f?(!f[n]&&(f[n]=[]),f[n].push(u)):r.push(u)}return r},changeIdx:function(e){this.idx=e}}},H=G,J=(a("eab4"),Object(d["a"])(H,i,r,!1,null,"bae3e524",null));t["default"]=J.exports},9254:function(e,t,a){"use strict";a("2d06")},af2b:function(e,t,a){"use strict";a("7de4")},b37d:function(e,t,a){},c46f:function(e,t,a){},e81c:function(e,t,a){"use strict";a("5424")},eab4:function(e,t,a){"use strict";a("b37d")},f29f:function(e,t,a){"use strict";a("69d1")},fad3:function(e,t,a){"use strict";a("c46f")}}]);