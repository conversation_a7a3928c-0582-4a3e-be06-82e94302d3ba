(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-98154d5a"],{"015e":function(t,e,i){"use strict";i("0e1a")},"0377":function(t,e,i){"use strict";i("6947")},"03b9":function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"采购订单",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购计划",value:"采购计划"},{label:"采购计划",value:"采购计划"},{label:"其他采购",value:"其他采购"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""},{col:5,code:"orderno",label:"订单编号",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"供应商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"arrivaladd",label:"收货地址",type:"input",methods:"",param:""},{col:5,code:"transport",label:"物流方式",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"billplandate",label:"单据计划",type:"date",methods:"",param:""},{col:5,code:"payment",label:"付款方式",type:"input",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},"0bdb":function(t,e,i){},"0e1a":function(t,e,i){},"11f6":function(t,e,i){},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},1535:function(t,e,i){},"174a":function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_require__.d(__webpack_exports__,"a",(function(){return btnExpress})),__webpack_require__.d(__webpack_exports__,"b",(function(){return getTgcolumnVal}));var D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("c7eb"),D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("1da1"),core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("caad"),core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_2___default=__webpack_require__.n(core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_2__),core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("d81d"),core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_3___default=__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_3__),core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("ac1f"),core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_4___default=__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_4__),core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__("2532"),core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_5___default=__webpack_require__.n(core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_5__),core_js_modules_es_string_match_js__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__("466d"),core_js_modules_es_string_match_js__WEBPACK_IMPORTED_MODULE_6___default=__webpack_require__.n(core_js_modules_es_string_match_js__WEBPACK_IMPORTED_MODULE_6__),core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__("5319"),core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_7___default=__webpack_require__.n(core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_7__),_utils_request__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__("b775"),_inksyun_utils__WEBPACK_IMPORTED_MODULE_9__=__webpack_require__("ba11"),vue__WEBPACK_IMPORTED_MODULE_10__=__webpack_require__("2b0e"),exprData=[],modulecode="",countfiles=["quantity","price","amount","taxprice","taxamount","itemtaxrate"],tgcolumnVal="";function getFormula(t){return _getFormula.apply(this,arguments)}function _getFormula(){return _getFormula=Object(D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__["a"])(Object(D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__["a"])().mark((function t(e){return Object(D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,_utils_request__WEBPACK_IMPORTED_MODULE_8__["a"].get("/SaBillExpression/getListByCode?key="+e).then((function(t){exprData=200==t.data.code?t.data.data:[]}));case 2:case"end":return t.stop()}}),t)}))),_getFormula.apply(this,arguments)}function transExprTemp(t,e){var i=t.match(/{[^}{]*?}/g);i=i.map((function(t){return t=t.replace("{",""),t=t.replace("}",""),t}));for(var a=t,o=0;o<i.length;o++)a=a.replace("${"+i[o]+"}","row['"+i[o]+"']");return a}function setTgcolumnVal(t){var e=t?t.toLowerCase():"";return e}function btnExpress(t,e,i){return _btnExpress.apply(this,arguments)}function _btnExpress(){return _btnExpress=Object(D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__["a"])(Object(D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__["a"])().mark((function _callee2(code,row,colname){var copyRow,i,Item,orgcolumns;return Object(D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__["a"])().wrap((function _callee2$(_context2){while(1)switch(_context2.prev=_context2.next){case 0:if(modulecode==code){_context2.next=4;break}return modulecode=code,_context2.next=4,getFormula(code);case 4:if(tgcolumnVal="",!exprData.length){_context2.next=11;break}for(copyRow=Object.assign({},row),i=0;i<exprData.length;i++)Item=exprData[i],orgcolumns=Item.orgcolumns.split(","),orgcolumns.includes(colname)&&(1==Item.returntype?vue__WEBPACK_IMPORTED_MODULE_10__["default"].set(copyRow,Item.tgcolumn,eval(transExprTemp(Item.exprtemp))):(vue__WEBPACK_IMPORTED_MODULE_10__["default"].set(copyRow,Item.tgcolumn,_inksyun_utils__WEBPACK_IMPORTED_MODULE_9__["a"].count.fomatFloat(eval(transExprTemp(Item.exprtemp)),Item.decnum)),countfiles.includes(Item.tgcolumn.toLowerCase())&&(tgcolumnVal=setTgcolumnVal(Item.tgcolumn))));return _context2.abrupt("return",copyRow);case 11:return _context2.abrupt("return",row);case 12:case"end":return _context2.stop()}}),_callee2)}))),_btnExpress.apply(this,arguments)}function getTgcolumnVal(){return tgcolumnVal}},"1f11":function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"采购验收",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购验收",value:"采购验收"},{label:"采购退货",value:"采购退货"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"供应商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"arrivaladd",label:"收货地址",type:"input",methods:"",param:""},{col:5,code:"transport",label:"物流方式",type:"input",methods:"",param:""}]}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},"233f":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{},[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",{staticStyle:{"margin-bottom":"10px"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,height:350,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"1px 0px 1px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"55"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"账户名称",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.accountname)+" ")]}}])}),i("el-table-column",{attrs:{label:"账户类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.accounttype))])]}}])}),i("el-table-column",{attrs:{label:"当前金额",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.currentamt))])]}}])}),i("el-table-column",{attrs:{label:"备注",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.remark))])]}}])}),i("el-table-column",{attrs:{label:"创建时间",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormat")(e.row.createdate)))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},o=[],s=(i("e9c4"),i("b775")),r=i("333d"),n={components:{Pagination:r["a"]},props:["multi"],data:function(){return{title:"出纳账户",listLoading:!0,lst:[],strfilter:"",radio:"",selrows:"",total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},watch:{},created:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,s["a"].post("/D07M21B2/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={accountname:t,accounttype:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},l=n,d=i("2877"),m=Object(d["a"])(l,a,o,!1,null,"0e79971c",null);e["a"]=m.exports},2499:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px",width:"100%"}},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1)]),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"450px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"客户订单号",align:"center",width:"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.custorderid))])]}}])}),i("el-table-column",{attrs:{label:"客户",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),t._l(t.customList,(function(t,e){return[i("el-table-column",{key:e,attrs:{"header-align":"center",align:"center",prop:t.attrkey,label:t.attrname}})]}))],2)],1),i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],s=i("c7eb"),r=i("1da1"),n=(i("c740"),i("e9c4"),i("b64b"),i("b775")),l=i("8daf"),d=i("40d9"),m={props:["multi","groupid","selecturl"],components:{Setcolums:l["a"]},data:function(){return{listLoading:!0,lst:[],strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},customList:[],setColumsVisible:!1,tableForm:d["d"]}},created:function(){this.bindData(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,n["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code?t.customList=e.data.data?e.data.data:[]:t.$message.warning(e.data.msg||"获取失败")})).catch((function(e){t.$message.error("请求错误")}));case 2:return e.next=4,n["a"].get("/SaDgFormat/getBillEntityByCode?code=D01M03B1Select").then((function(e){if(200==e.data.code){if(null==e.data.data){t.tableForm=d["d"];for(var i=0;i<t.customList.length;i++){var a=t.customList[i],o=t.tableForm.item.findIndex((function(e){return e.itemcode==t.customList[i].attrkey}));if(-1==o){var s={itemcode:a.attrkey,itemname:a.attrname,minwidth:"80",displaymark:a.listshow,overflow:1};t.tableForm.item.push(s)}}return}t.tableForm=e.data.data;for(i=0;i<t.customList.length;i++){a=t.customList[i],o=t.tableForm.item.findIndex((function(e){return e.itemcode==t.customList[i].attrkey}));if(-1==o){s={itemcode:a.attrkey,itemname:a.attrname,minwidth:"80",displaymark:a.listshow,overflow:1};t.tableForm.item.push(s)}}}})).catch((function(e){t.$message.error("请求出错")}));case 4:case"end":return e.stop()}}),e)})))()},getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){var i;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.listLoading=!0,t.listLoading=!0,i=t.selecturl?t.selecturl:"/D01M03B1/getPageList",e.next=5,n["a"].post(i,JSON.stringify(t.queryParams)).then((function(e){if(200==e.data.code){console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 5:return e.next=7,n["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code||t.$message.warning(e.data.msg||"获取spu失败")})).catch((function(e){t.$message.error("请求错误")}));case 7:case"end":return e.stop()}}),e)})))()},search:function(t){var e=t.split(",");0!=e.length?1==e.length?(this.queryParams.SearchPojo={goodsuid:e[0],goodsname:e[0],goodsunit:e[0],groupid:e[0],goodsspec:e[0],partid:e[0],refno:e[0],custorderid:e[0],groupname:e[0],attributestr:e[0]},this.$delete(this.queryParams,"scenedata")):e.length>1&&(this.queryParams.scenedata=[{field:"Mat_Goods.goodsuid",fieldtype:0,math:"like",value:"".concat(e[0])},{field:"Mat_Goods.goodsname",fieldtype:0,math:"like",value:"".concat(e[1])}],3==e.length&&this.queryParams.scenedata.push({field:"Mat_Goods.partid",fieldtype:0,math:"like",value:"".concat(e[2])}),this.$delete(this.queryParams,"SearchPojo")):(this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"scenedata")),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},c=m,u=(i("fb056"),i("2877")),h=Object(u["a"])(c,a,o,!1,null,"56660287",null);e["a"]=h.exports},"27f6":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},o=[],s=i("c7eb"),r=i("1da1"),n=(i("caad"),i("e9c4"),i("a9e3"),i("b64b"),i("d3b7"),i("2532"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),i("b775")),l=i("5e63"),d=i("b893"),m={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"13df"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:l["b"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={citeuid:this.searchVal};this.queryParams.SearchPojo=e;var i="/D04M01R1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(d["d"])()[0],EndDate:Object(d["d"])()[1]}),n["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(l["b"].formcode,l["b"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=a("GoodsInfo",{attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return s}if("billtype"==t.itemcode){s=a("span",[o[t.itemcode]]);return o[t.itemcode].includes("红冲")&&(s=a("span",{style:"color:#f44336"},[o[t.itemcode]])),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},c=m,u=(i("b83a"),i("2877")),h=Object(u["a"])(c,a,o,!1,null,"34f0715a",null);e["a"]=h.exports},"2b1c":function(t,e,i){"use strict";i("3a84")},3218:function(t,e,i){},"335e":function(t,e,i){},3470:function(t,e,i){},"367a":function(t,e,i){},3949:function(t,e,i){"use strict";i("dea7")},"3a84":function(t,e,i){},"3e09":function(t,e,i){},"3f1b":function(t,e,i){"use strict";i("90fe")},"3fbc":function(t,e,i){"use strict";i("a485")},"40d9":function(t,e,i){"use strict";i.d(e,"f",(function(){return a})),i.d(e,"e",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"b",(function(){return r})),i.d(e,"a",(function(){return n})),i.d(e,"d",(function(){return l}));var a={formcode:"D01M03B1Th",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"150",defwidth:"",displaymark:1,fixed:1,sortable:1,overflow:1,aligntype:"center",datasheet:"Bus_Machining.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billdate"},{itemcode:"custorderid",itemname:"客户订单号",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.custorderid"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"abbreviate",itemname:"简称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.abbreviate"},{itemcode:"groupadd",itemname:"客户地址",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupadd"},{itemcode:"billtaxamount",itemname:"总金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtaxamount"},{itemcode:"advaamount",itemname:"预收款",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.advaamount"},{itemcode:"balance",itemname:"结余",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billcostbudgetamt",itemname:"成本预算",minwidth:"80",displaymark:0,overflow:1},{itemcode:"billplandate",itemname:"计划时间",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billplandate"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.summary"},{itemcode:"salesman",itemname:"业务员",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.salesman"},{itemcode:"billwkwpname",itemname:"最新工序",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.billwkwpname"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Machining.status"},{itemcode:"itemcount",itemname:"款数",minwidth:"60",displaymark:1,overflow:1,datasheet:"Bus_Machining.itemcount"},{itemcode:"printcount",itemname:"打印次数",minwidth:"100",displaymark:0,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Machining.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Bus_Machining.assessor"},{itemcode:"amtstatus",itemname:"收款状态",minwidth:"100",displaymark:1,overflow:1}]},o={formcode:"D01M03B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"150",defwidth:"",displaymark:1,overflow:1,fixed:1,sortable:1,aligntype:"center",datasheet:"Bus_Machining.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billdate"},{itemcode:"custorderid",itemname:"客户订单号",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.custorderid"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"matcode",itemname:"物料编码",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.matcode"},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.quantity"},{itemcode:"taxprice",itemname:"单价",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxprice"},{itemcode:"taxamount",itemname:"金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxamount"},{itemcode:"finishqty",itemname:"完成数",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.finishqty"},{itemcode:"remainder",itemname:"结余数",minwidth:"100",displaymark:0,overflow:1},{itemcode:"buyquantity",itemname:"采购数",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.buyquantity"},{itemcode:"wkwpname",itemname:"工序",minwidth:"100",displaymark:1,overflow:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"matstatus",itemname:"物料状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemplandate",itemname:"评审交期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.itemplandate"},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.remark"}]},s={formcode:"D01M03B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"120",defwidth:"",displaymark:1,fixed:1,sortable:0,overflow:1,aligntype:"center",operationmark:1},{itemcode:"goodsname",itemname:"货品名称",minwidth:"120",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"200",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"120",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"partid",itemname:"外部编码",minwidth:"120",displaymark:1,overflow:1,operationmark:1},{itemcode:"quantity",itemname:"数量",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"itemorgdate",itemname:"原始交期",minwidth:"120",displaymark:1,overflow:1},{itemcode:"itemplandate",itemname:"评审交期",minwidth:"120",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"stoqty",itemname:"库存发货",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"maxqty",itemname:"最大发货数",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"wkqty",itemname:"生产需求",minwidth:"120",displaymark:1,overflow:1},{itemcode:"wkquantity",itemname:"生产数",minwidth:"120",displaymark:1,overflow:1},{itemcode:"buyquantity",itemname:"采购数",minwidth:"100",displaymark:1,overflow:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:0,overflow:1},{itemcode:"finishqty",itemname:"发货数",minwidth:"120",displaymark:1,overflow:1},{itemcode:"outquantity",itemname:"出库数",minwidth:"120",displaymark:1,overflow:1}]},r={formcode:"D01M03B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"150",defwidth:"",displaymark:1,overflow:1,fixed:1,sortable:1,aligntype:"center",datasheet:"Bus_Machining.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billdate"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"matcode",itemname:"物料编码",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.matcode"},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.quantity"},{itemcode:"taxprice",itemname:"单价",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxprice"},{itemcode:"taxamount",itemname:"金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxamount"},{itemcode:"wkwpname",itemname:"工序",minwidth:"100",displaymark:1,overflow:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"matstatus",itemname:"物料状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemplandate",itemname:"评审交期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.itemplandate"},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.remark"}]},n={formcode:"D03M02B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"130",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",atasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,atasheet:"Buy_Order.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Buy_Order.billtitle"},{itemcode:"billdate",itemname:"单据日期",sortable:1,minwidth:"100",displaymark:1,overflow:1,atasheet:"Buy_Order.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"plandate",itemname:"计划完成",minwidth:"100",sortable:1,displaymark:1,overflow:1,atasheet:"Buy_OrderItem.billdate"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"规格",minwidth:"120",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.quantity"},{itemcode:"finishqty",itemname:"已收",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.status"}]},l={formcode:"D01M03B1Select",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"130",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",atasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"custorderid",itemname:"客户订单号",minwidth:"100",displaymark:1,overflow:1},{itemcode:"groupname",itemname:"客户",minwidth:"120",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,overflow:1,operationmark:1},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"120",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1}]}},"425e":function(t,e,i){},"466d":function(t,e,i){"use strict";var a=i("d784"),o=i("825a"),s=i("50c4"),r=i("1d80"),n=i("8aa5"),l=i("14c3");a("match",1,(function(t,e,i){return[function(e){var i=r(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,i):new RegExp(e)[t](String(i))},function(t){var a=i(e,t,this);if(a.done)return a.value;var r=o(t),d=String(this);if(!r.global)return l(r,d);var m=r.unicode;r.lastIndex=0;var c,u=[],h=0;while(null!==(c=l(r,d))){var f=String(c[0]);u[h]=f,""===f&&(r.lastIndex=n(d,s(r.lastIndex),m)),h++}return 0===h?null:u}]}))},"4ac0":function(t,e,i){"use strict";i("6700")},"4b34":function(t,e,i){},"4cf0":function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"a",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"b",(function(){return r}));var a={formcode:"D03M06B1PREList",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Prepayments.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.summary"},{itemcode:"outamount",itemname:"转出金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.outamount"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.status"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.assessor"}]},o={formcode:"D03M06B1PRECash",item:[{itemcode:"cashaccname",itemname:"出纳账户",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"amount",itemname:"金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},s={formcode:"D03M06B1PREItem",item:[{itemcode:"orderbillcode",itemname:"订单单号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"billtaxamount",itemname:"单据金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"金额",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D03M06B1PRECite",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Prepayments.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.summary"},{itemcode:"outamount",itemname:"转出金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.outamount"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.status"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.assessor"}]}},"4d45":function(t,e,i){},"4f5e":function(t,e,i){},"501c":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},o=[],s=i("c7eb"),r=i("1da1"),n=(i("caad"),i("e9c4"),i("a9e3"),i("d3b7"),i("2532"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),i("b775")),l=i("3d5d"),d=i("b893"),m={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"d85f"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:l["c"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={citecode:this.searchVal};this.queryParams.SearchPojo=e;var i="/D03M06B1/getPageTh";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(d["d"])()[0],EndDate:Object(d["d"])()[1]}),n["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total)})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(l["c"].formcode,l["c"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("billtype"==t.itemcode){s=a("span",[o[t.itemcode]]);return o[t.itemcode].includes("红冲")&&(s=a("span",{style:"color:#f44336"},[o[t.itemcode]])),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxamount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){console.log(t,"222"),this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},c=m,u=(i("5880"),i("2877")),h=Object(u["a"])(c,a,o,!1,null,"7c16eaee",null);e["a"]=h.exports},5880:function(t,e,i){"use strict";i("4f5e")},"5b00":function(t,e,i){"use strict";i("66cd")},"65e3":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],s=(i("e9c4"),i("b775")),r=i("333d"),n={components:{Pagination:r["a"]},props:["multi","groupid","selecturl"],data:function(){return{title:"采购订单",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M02B1/getOnlinePageList?groupid="+this.groupid;s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={goodsname:t,goodsuid:t,goodsspec:t,groupuid:t,groupname:t,refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},l=n,d=(i("8da8"),i("2877")),m=Object(d["a"])(l,a,o,!1,null,"ecf1cb7c",null);e["a"]=m.exports},"664a":function(t,e,i){"use strict";i("4b34")},"66cd":function(t,e,i){},6700:function(t,e,i){},6947:function(t,e,i){},"6ccf":function(t,e,i){},"6e4b":function(t,e,i){},7085:function(t,e,i){"use strict";i("8b95")},7922:function(t,e,i){"use strict";i("0bdb")},"7e4c":function(t,e,i){"use strict";i("e149")},"841c":function(t,e,i){"use strict";var a=i("d784"),o=i("825a"),s=i("1d80"),r=i("129f"),n=i("14c3");a("search",1,(function(t,e,i){return[function(e){var i=s(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,i):new RegExp(e)[t](String(i))},function(t){var a=i(e,t,this);if(a.done)return a.value;var s=o(t),l=String(this),d=s.lastIndex;r(d,0)||(s.lastIndex=0);var m=n(s,l);return r(s.lastIndex,d)||(s.lastIndex=d),null===m?-1:m.index}]}))},8679:function(t,e,i){},8913:function(t,e,i){"use strict";i("425e")},8955:function(t,e,i){"use strict";i("8679")},"8b95":function(t,e,i){},"8da8":function(t,e,i){"use strict";i("3e09")},"90fe":function(t,e,i){},9368:function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r}));var a={formcode:"D03M02B1Th",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"130",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Order.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Buy_Order.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Order.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billtaxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Buy_Order.billtaxamount"},{itemcode:"billamount",itemname:"未税金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,atasheet:"Buy_Order.billamount"},{itemcode:"billtaxtotal",itemname:"税额",minwidth:"80",sortable:1,displaymark:1,overflow:1,atasheet:"Buy_Order.billtaxtotal"},{itemcode:"orderno",itemname:"订单编号",minwidth:"100",displaymark:1,sortable:1,overflow:1,atasheet:"Buy_Order.orderno"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,atasheet:"Buy_Order.operator"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,atasheet:"Buy_Order.status"},{itemcode:"itemcount",itemname:"款数",minwidth:"60",sortable:1,displaymark:1,overflow:1,atasheet:"Buy_Order.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,atasheet:"Buy_Order.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,atasheet:"Buy_Order.assessor"}]},o={formcode:"D03M02B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"130",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",atasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,atasheet:"Buy_Order.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Buy_Order.billtitle"},{itemcode:"billdate",itemname:"单据日期",sortable:1,minwidth:"100",displaymark:1,overflow:1,atasheet:"Buy_Order.billdate"},{itemcode:"custpo",itemname:"客户订单号",minwidth:"100",displaymark:0,overflow:1},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"plandate",itemname:"计划完成",minwidth:"100",sortable:1,displaymark:1,overflow:1,atasheet:"Buy_OrderItem.billdate"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"规格",minwidth:"120",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.quantity"},{itemcode:"finishqty",itemname:"已收",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.status"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:0,overflow:1,datasheet:"Buy_Order.assessor"}]},s={formcode:"D03M02B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:1,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,fixed:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,fixed:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"60",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",displaymark:0,overflow:1},{itemcode:"abbreviate",itemname:"客户简称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"groupname",itemname:"客户",minwidth:"120",displaymark:0,overflow:1},{itemcode:"machuid",itemname:"销售订单",minwidth:"100",displaymark:1,overflow:1},{itemcode:"custpo",itemname:"客户订单号",minwidth:"100",displaymark:0,overflow:1},{itemcode:"plandate",itemname:"计划完成",minwidth:"150",displaymark:1,overflow:1},{itemcode:"maxqty",itemname:"最大允收",minwidth:"90",displaymark:1,overflow:1,editmark:1},{itemcode:"finishqty",itemname:"已收货",minwidth:"100",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:0,overflow:1}]},r={formcode:"D03M02B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"130",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",atasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,atasheet:"Buy_Order.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Buy_Order.billtitle"},{itemcode:"billdate",itemname:"单据日期",sortable:1,minwidth:"100",displaymark:1,overflow:1,atasheet:"Buy_Order.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"plandate",itemname:"计划完成",minwidth:"100",sortable:1,displaymark:1,overflow:1,atasheet:"Buy_OrderItem.billdate"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"规格",minwidth:"120",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.quantity"},{itemcode:"finishqty",itemname:"已收",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.status"}]}},"941f":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{writedate:!1,formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getSuppGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,changeBillType:t.changeBillType}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px",height:"100%"}},[i("EditCash",{ref:"cashitem",style:{width:"99%",height:"其他预付"!=t.formdata.billtype?"50%":"100%"},attrs:{lstitem:t.formdata.cash,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate,itemAmount:t.itemAmount},on:{bindData:t.bindData,computerCashAmount:t.computerCashAmount}}),"其他预付"!=t.formdata.billtype?i("EditItem",{ref:"elitem",staticStyle:{width:"99%",height:"48%","margin-top":"10px"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData,computerItemAmount:t.computerItemAmount}}):t._e()],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M06B1PREEdit",commonurl:"/D03M06B1PRE/printBill",weburl:"/D03M06B1PRE/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M06B1PREEdit",examineurl:"/D03M06B1PRE/sendapprovel"}})],1)},o=[],s=i("2909"),r=(i("a9e3"),i("b64b"),i("d3b7"),i("ac1f"),i("6062"),i("3ca3"),i("5319"),i("ddb0"),i("b775"));const n={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D03M06B1PRE/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D03M06B1PRE/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){r["a"].get("/D03M06B1PRE/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,r["a"].get("/D03M06B1PRE/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M06B1PRE/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M06B1PRE/closed?type="+(3==t?1:0);r["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var l=n,d=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){return t.$emit("changeBillType")}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购预付",value:"采购预付"}}),i("el-option",{attrs:{label:"其他预付",value:"其他预付"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},["外协预付"==t.formdata.billtype||"委外预付"==t.formdata.billtype?i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"外协厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B4/getPageList",type:"外协厂商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1):i("div",[i("el-form-item",{attrs:{label:"供应厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"预付金额"}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.billamount))])])],1)],1)],1)},m=[],c={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupname:[{required:!0,trigger:"blur",message:"往来单位为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},u=c,h=(i("d8b2"),i("2877")),f=Object(h["a"])(u,d,m,!1,null,"8c70604e",null),p=f.exports,g=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh","column"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn}}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},editOption:t.editOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.deliveryvisible?i("el-dialog",{attrs:{title:"采购订单","append-to-body":!0,visible:t.deliveryvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.deliveryvisible=e}}},[i("SelDelivery",{ref:"selDelivery",attrs:{multi:1,selecturl:"/D03M02B1/getOnlinePageTh?groupid="+t.formdata.groupid+"&appl=1"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selDelivery()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.deliveryvisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},b=[],y=(i("c740"),i("caad"),i("e9c4"),i("2532"),i("c7cd"),i("159b"),{amount:0,cashaccid:"",cashaccname:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",pid:"",remark:"",rownum:0}),w={amount:0,cashaccid:"",cashaccname:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",pid:"",remark:"",rownum:0},v=i("4cf0"),x=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto","min-height":"352 px",width:"100%"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据日期",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormats")(e.row.billdate)))])]}}])}),i("el-table-column",{attrs:{label:"供应商编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupuid))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"含税金额",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtaxamount))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},k=[],_=i("333d"),D={components:{Pagination:_["a"]},props:["multi","groupid","selecturl"],data:function(){return{title:"采购订单",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M02B1/getPageTh";r["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtype:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},S=D,O=(i("5b00"),Object(h["a"])(S,x,k,!1,null,"892e62f4",null)),B=O.exports,$={name:"Elitem",components:{SelDelivery:B},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{listLoading:!1,deliveryvisible:!1,lst:[],keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:v["c"],customList:[],editmarkfiles:[],countfiles:["billtaxamount","amount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=v["c"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(v["c"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=[];this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["index","amount"]);var t=this.footerData[0].amount;this.$emit("computerItemAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.formdata.groupid?this.deliveryvisible=!0:this.$message.warning("请选择往来单位")},selWaXie:function(){var t=this.$refs.selWaXie.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},y);a.invoamount=0,a.invobillcode=i.refno,a.invocode="",a.invoid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selDelivery:function(){var t=this.$refs.selDelivery.$refs.selectVal.selection;if(0!=t.length){this.deliveryvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},y);a.amount=i.billtaxamount,a.billtaxamount=i.billtaxamount,a.orderbillcode=i.refno,a.orderbillid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},P=$,q=(i("015e"),Object(h["a"])(P,g,b,!1,null,"6a248a27",null)),M=q.exports,C=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh","column"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn}}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"出纳账户","append-to-body":!0,visible:t.selordervisible,width:"1000px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.SelOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},F=[],I=i("233f"),L={name:"Elitem",components:{SelOrder:I["a"]},props:["formdata","lstitem","idx","itemAmount","formstate","formtemplateItem"],data:function(){var t=this;return{listLoading:!1,selordervisible:!1,lst:[],keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:v["a"],customList:[],editmarkfiles:[],countfiles:["amount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=v["a"];this.$getColumn(v["a"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=[];this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["cashaccname","amount"]);var t=this.footerData[0].amount;this.$emit("computerCashAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}this.multipleSelection=[],this.checkboxOption.selectedRowKeys=[]},getAdd:function(t){this.selordervisible=!0},SelOrder:function(){var t=this.$refs.SelOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},w);a.amount=this.itemAmount,a.cashaccid=i.id,a.cashaccname=i.accountname,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},E=L,T=(i("3fbc"),Object(h["a"])(E,C,F,!1,null,"da1038a0",null)),j=T.exports,R={header:{type:0,title:"付款单",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购预付",value:"采购预付"},{label:"委外预付",value:"委外预付"},{label:"外协预付",value:"外协预付"},{label:"其他预付",value:"其他预付"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"供应厂商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"billamount",label:"预付金额",type:"text",methods:"",param:""}]}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},list:{type:0,content:[]},item:{type:0,content:[]}},N=i("dcb4"),K=["id","refno","billtype","billtitle","billdate","groupid","groupid","groupuid","groupname","abbreviate","grouplevel","billamount","operator","citecode","outamount","returnuid","orguid","summary","fmdocmark","fmdoccode","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],V=["id","pid","orderbillid","orderbillcode","finishbillid","finishbillcode","billtaxamount","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],z=["id","pid","cashaccid","cashaccname","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],H={params:K,paramsItem:V,paramsCash:z},A=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]}],G=[],W={name:"Formedit",components:{FormTemp:N["a"],EditHeader:p,EditItem:M,EditCash:j},props:["idx","isDialog","initData","billcode","isprocessDialog"],data:function(){return{title:"预付款",operateBar:A,processBar:G,formdata:{abbreviate:"",billamount:0,assessdate:new Date,assessor:"",billdate:new Date,billtitle:"",billtype:"采购预付",citecode:"",createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,createdate:"",groupid:"",groupname:"",groupuid:"",item:[],cash:[],lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,operator:"",orguid:"",outamount:0,refno:"",returnuid:"",revision:0,summary:""},itemAmount:0,cashAmount:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:R,formstate:0,submitting:0}},computed:{formcontainHeight:function(){return window.innerHeight-50-13-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp(),this.bindData()},mounted:function(){this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;r["a"].get("/SaFormCustom/getEntityByCode?key=D03M06B1PRE").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):R,t.formtemplate.footer.type||(t.formtemplate.footer=R.footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&r["a"].get("/D03M06B1PRE/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1,"其他收款"==t.formdata.billtype&&t.$refs.cashitem.catchHight()):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},computerItemAmount:function(t){this.itemAmount=t},computerCashAmount:function(t){this.cashAmount=t,this.formdata.billamount=Number(this.cashAmount)},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if("其他预付"!=this.formdata.billtype){if(this.formdata.billamount!=this.itemAmount||this.formdata.billamount!=this.cashAmount)return void this.$message.warning("预付金额 与 出纳金额和单据金额不一致");this.formdata.item=this.$refs.elitem.lst,this.formdata.citecode="";for(var e="",i=0;i<this.formdata.item.length;i++){var a=this.formdata.item[i];e+=a.orderbillcode+","}var o=/,$/gi;e=e.replace(o,"");var r=e.split(","),n=Object(s["a"])(new Set(r));for(i=0;i<n.length;i++)this.formdata.citecode+=n[i]+",";this.formdata.citecode=this.formdata.citecode.replace(o,"")}this.submitting=1,this.formdata.cash=this.$refs.cashitem.lst;var d={item:[],cash:[]};d=this.$getParam(H,d,this.formdata),0==this.idx?l.add(d).then((function(e){t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0})):l.update(d).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0}))},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.delete(e)})).catch((function(){}))},approval:function(){l.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?l.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t,console.log(this.processModel,"ss")},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.item=[]},autoClear:function(t){this.formdata.groupname="",this.formdata.groupid=""},changeBillType:function(){this.formdata.item=[],this.formdata.taxamount=0,"其他预付"==this.formdata.billtype&&this.$refs.cashitem.catchHight()},billSwitch:function(t){if(console.log(this.initData),"D03M02B1"==t){this.formdata.billtype="采购预付",this.formdata.billtitle="【"+this.initData.refno+"】采购订单转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billamount=this.initData.billamount,this.formdata.citecode=this.initData.refno,this.formdata.item=[];var e={billtaxamount:this.initData.billtaxamount,amount:this.initData.billtaxamount,finishbillid:"",finishbillcode:"",orderbillcode:this.initData.refno,orderbillid:this.initData.id,remark:"",rownum:0};this.formdata.item.push(e)}}}},J=W,U=(i("af2e"),Object(h["a"])(J,a,o,!1,null,"44f6e4d4",null));e["default"]=U.exports},"95a2":function(t,e,i){"use strict";i("335e")},"992e":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("Formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[i("ListHeader",{attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,changeModelUrl:t.changeModelUrl,btnExport:t.btnExport,changeBalance:t.changeBalance,pagePrint:t.pagePrint,btnPrint:function(e){return t.$refs.tableTh.btnPrint()},setDelivery:function(e){return t.$refs.tableList.setDelivery()},bindColumn:function(e){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()}}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{attrs:{span:24}},[i("TableTh",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],ref:"tableTh",attrs:{online:t.online,formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}}),i("TableList",{directives:[{name:"show",rawName:"v-show",value:!t.thorList,expression:"!thorList"}],ref:"tableList",attrs:{online:t.online,formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}})],1)],1)],1)],1)])},o=[],s=(i("b64b"),i("ac1f"),i("841c"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container flex a-c j-s"},[i("div",{staticStyle:{display:"flex"}},[i("div",{staticClass:"flex infoForm a-c"},[i("span",{staticClass:"infoForm-Title"},[t._v("时间范围")]),i("el-date-picker",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"unlink-panels":"",size:"mini","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")]),i("el-button",{attrs:{size:"mini",icon:"el-icon-printer",title:"打印列表"},on:{click:function(e){return t.$emit("pagePrint")}}},[t._v("打印")]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:"el-icon-printer",plain:"",size:"mini",title:"打印单据"},on:{click:function(e){return t.$emit("btnPrint")}}},[t._v(" 单据 ")]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:!t.thorList,expression:"!thorList"}],staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-folder-opened",plain:"",size:"mini"},on:{click:function(e){return t.$emit("setDelivery")}}},[t._v(" 收货 ")])],1),i("div",{staticClass:"iShowBtn"},[i("div",{staticStyle:{display:"inline-block"}},[i("el-switch",{staticStyle:{"margin-right":"10px"},attrs:{"inactive-color":"#13ce66"},on:{change:t.changeModelUrl},model:{value:t.thorList,callback:function(e){t.thorList=e},expression:"thorList"}}),i("span",{staticStyle:{"margin-right":"10px","font-size":"14px"}},[t._v(t._s(t.thorList?"单据":"明细"))])],1),i("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"结存",size:"mini",border:""},on:{change:t.changeBalance},model:{value:t.balance,callback:function(e){t.balance=e},expression:"balance"}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),r=[],n=i("b893"),l={name:"ListHeader",props:["tableForm"],data:function(){return{strfilter:"",formdata:{},dateRange:Object(n["d"])(),pickerOptions:Object(n["h"])(),thorList:!0,balance:!1,setColumsVisible:!1,searchVisible:!1}},methods:{advancedSearch:function(t){var e={dateRange:this.dateRange,formdata:t};this.$emit("advancedSearch",e),this.searchVisible=!1},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){var t={dateRange:this.dateRange,strfilter:this.strfilter};this.$emit("btnSearch",t)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList),this.$emit("bindColumn")},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},changeBalance:function(t){var e=0;e=t?1:0,this.$emit("changeBalance",e)}}},d=l,m=(i("95a2"),i("2877")),c=Object(m["a"])(d,s,r,!1,null,"4bc26d2e",null),u=c.exports,h=i("9fee"),f=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableTh",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px",display:"flex","align-items":"center"}},[i("div",{staticStyle:{"margin-right":"10px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)])]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.selectList,printcode:"D03M02B1Edit",commonurl:"/D03M02B1/printBatchBill",weburl:"/D03M02B1/printBatchWebBill"}}),i("PrintServer",{ref:"PrintPageServer",attrs:{formdata:t.lst,queryParams:t.queryParams,printcode:"D03M02B1Th",commonurl:"/D03M02B1/printPageTh",weburl:"/D03M02B1/printWebPageTh"}})],1)},p=[],g=(i("e9c4"),i("a9e3"),i("d3b7"),i("c7cd"),i("159b"),i("f07e")),b=i("b775"),y=i("9368"),w={components:{MyPopover:g["a"]},props:["online","formtemplate"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:y["d"],mypopoverTable:y["b"],mypopoverData:[],mypopoverIndex:0,selectList:[],totalfields:["refno","billamount","billtaxamount","billtaxtotal"],exportitle:"采购订单表",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectList=i?t.lst:[]}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},methods:{bindData:function(){var t=this;this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(n["d"])()[0],EndDate:Object(n["d"])()[1]});var e="/D03M02B1/getPageTh";this.online&&(e="/D03M02B1/getOnlinePageTh"),b["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this,e=y["d"];this.formtemplate.th.type&&(e.item=this.formtemplate.th.content),this.$getColumn(this.tableForm.formcode,e).then((function(e){t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex);if("billdate"==t.itemcode||"itemplandate"==t.itemcode||"billplandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("groupuid"==t.itemcode){var r=a("GroupInfo",{attrs:{scopeVal:o[t.itemcode],searchUrl:"/D01M01B1/getGeneral?key="+o.groupid}});return r}if("status"==t.itemcode){r="";return o.finishcount>0&&o.finishcount+o.disannulcount<o.itemcount?r=a("span",{class:"textborder-blue"},["收货"]):o.finishcount+o.disannulcount==o.itemcount&&o.finishcount>0?r=a("span",{class:"textborder-green"},["完成"]):o.disannulcount>0&&o.disannulmark==o.itemcount&&(r=a("span",{class:"textborder-grey"},["撤销"])),r}if("refno"==t.itemcode){r=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return r}if("assessor"==t.itemcode){r="";return r=o.oaflowmark&&!o.assessor?a("span",{style:"color:#ff9800"},["审核中"]):a("span",[o.assessor]),r}if("itemcount"==t.itemcode){r="";return r=a("el-popover",{attrs:{placement:"left",trigger:"click",title:"单据明细"},ref:"'popover-' + scope.$index"},[a("div",{style:"position: relative; min-height: 100px",directives:[{name:"show",value:s==e.mypopoverIndex}]},[a(g["a"],{ref:"mypopover",attrs:{tableForm:e.mypopoverTable,lst:e.mypopoverData}})]),a("span",{slot:"reference",class:"textunderline",on:{click:function(){return e.getBillList(o,s)}}},[o[t.itemcode]])]),r}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:40,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},pagePrint:function(){this.$refs.PrintPageServer.printButton(1,1)},btnPrint:function(){this.$refs.PrintServer.printButton(2,1)},countCellData:function(){var t=this.$refs.tableTh.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableTh.getRangeCellSelection().selectionRangeIndexes,i=["billtaxamount","billamount","billtaxtotal"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t,1)},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,"采购订单")},getBillList:function(t,e){var i=this;this.mypopoverIndex=e,b["a"].get("/D03M02B1/getBillEntity?key=".concat(t.id)).then((function(t){200==t.data.code?i.mypopoverData=t.data.data.item:i.mypopoverData=[]}))},getcolumnItem:function(){var t=this;this.$getColumn(y["b"].formcode,y["b"]).then((function(e){t.mypopoverTable=e.colList}))}}},v=w,x=(i("2b1c"),Object(m["a"])(v,f,p,!1,null,"4c794da5",null)),k=x.exports,_=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize,pageSizes:[10,20,100,500]},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.lst,queryParams:t.queryParams,printcode:"D03M02B1List",commonurl:"/D03M02B1/printPageList",weburl:"/D03M02B1/printWebPageList"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},[i("D03M03B1",{ref:"D03M03B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.selectList,billcode:"D03M02B1List"},on:{closeDialog:function(e){t.operationVisible=!1,t.selectList=[],t.checkboxOption.selectedRowKeys=[],t.bindData()}}})],1):t._e()],1)},D=[],S=i("c7eb"),O=i("1da1"),B=(i("c740"),i("caad"),i("a434"),i("2532"),i("ed33")),$={components:{D03M03B1:B["default"]},props:["online","formtemplate"],data:function(){var t=this;return{lst:[],total:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:y["c"],customList:[],selectList:[],totalfields:["refno","taxamount","quantity","compcost"],exportitle:"采购订单明细表",dialogIdx:0,operationVisible:!1,customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){var i=e.row,a=(e.isSelected,e.selectedRowKeys);if(t.checkboxOption.selectedRowKeys=a,a.includes(i.id))t.selectList.push(i);else{var o=t.selectList.findIndex((function(t){return t.id==i.id}));-1!=o&&t.selectList.splice(o,1)}},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.selectList=t.lst):(t.selectList=[],t.checkboxOption.selectedRowKeys=[])}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},methods:{bindData:function(){var t=this;if(this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(n["d"])()[0],EndDate:Object(n["d"])()[1]}),this.online)var e="/D03M02B1/getOnlinePageList";else e="/D03M02B1/getPageList";b["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++){var a=t.lst[i];a.compcost=a.quantity-a.buyqty;for(var o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}}else t.$message.warning(e.data.msg||"请求失败");t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(O["a"])(Object(S["a"])().mark((function e(){var i;return Object(S["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=y["c"],t.formtemplate.list.type&&(i.item=t.formtemplate.list.content),t.$getColumn(t.tableForm.formcode,i,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=a("GoodsInfo",{attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return s}if("status"==t.itemcode){s="";return 0!=o.finishqty&&o.finishqty<o.quantity?s=a("span",{class:"textborder-blue"},["收货"]):o.finishqty>=o.quantity&&0!=o.finishqty?s=a("span",{class:"textborder-green"},["完成"]):o.disannulmark>1?s=a("span",{class:"textborder-grey"},["撤销"]):o.closed&&(s=a("span",{class:"textborder-grey"},["中止"])),s}if("compcost"==t.itemcode){s="";return s=o.quantity-o.finishqty<0?a("span",{style:"color:#F56C6C"},[o.quantity-o.finishqty]):o.quantity-o.finishqty==0?a("span",{style:"color:#67C23A"},[o.quantity-o.finishqty]):o.quantity-o.finishqty==o.quantity?a("span",[o.quantity-o.finishqty]):a("span",{style:"color:#409EFF"},[o.quantity-o.finishqty]),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},pagePrint:function(){this.$refs.PrintServer.printButton(1)},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,i=["quantity","compcost","finishqty"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t,1)},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,"采购订单明细")},setDelivery:function(){var t=this;if(0!=this.selectList.length){var e=this.selectList.every((function(e){return e.groupuid==t.selectList[0].groupuid}));if(e){var i=this.selectList.every((function(t){return""==t.assessor}));if(i)this.$message.warning("单据未审核");else{var a=this.selectList.every((function(t){return t.finishqty>=t.quantity}));a?this.$message.warning("单据已完成"):this.operationVisible=!0}}else this.$message.warning("请选择相同供应商")}else this.$message.warning("请选择货品内容")}}},P=$,q=(i("4ac0"),Object(m["a"])(P,_,D,!1,null,"35c4280d",null)),M=q.exports,C=i("03b9"),F={name:"D03M02B1",components:{ListHeader:u,Formedit:h["default"],TableTh:k,TableList:M},data:function(){return{idx:0,online:0,formvisible:!1,thorList:!0,tableForm:{},showhelp:!1,formtemplate:C["a"]}},mounted:function(){this.bindTemp(),this.bindData()},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D03M02B1").then((function(e){200==e.data.code?(null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):C["a"]),t.$nextTick((function(){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()}))):t.$alert(e.data.msg||"获取页面信息失败")})).catch((function(e){t.$message.error("请求错误")}))},bindData:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.tableTh.bindData()})):this.$nextTick((function(){t.$refs.tableList.bindData()}))},pagePrint:function(){this.thorList?this.$refs.tableTh.pagePrint():this.$refs.tableList.pagePrint()},search:function(t){this.thorList?this.$refs.tableTh.search(t):this.$refs.tableList.search(t)},advancedSearch:function(t){this.thorList?this.$refs.tableTh.advancedSearch(t):this.$refs.tableList.advancedSearch(t)},changeModelUrl:function(t){this.thorList=!!t,this.bindData()},changeBalance:function(t){this.online=t,this.bindData()},btnExport:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.tableTh.btnExport()})):this.$nextTick((function(){t.$refs.tableList.btnExport()}))},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},sendTableForm:function(t){this.tableForm=t}}},I=F,L=(i("8913"),Object(m["a"])(I,a,o,!1,null,"7eff5a7e",null));e["default"]=L.exports},"9fee":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){t.commonurl="/D03M02B1/printBill",t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%","overflow-y":"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getSuppGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{isDialog:t.isDialog,title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M02B1Edit",commonurl:t.commonurl,weburl:"/D03M02B1/printWebBill"}}),i("PrintServer",{ref:"PrintMultiServer",attrs:{formdata:t.formdata,printcode:"D03M02B1EditMulti",commonurl:"/D03M02B1/printWebBillMulti",weburl:"/D03M02B1/printWebBillMulti"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M02B1Edit",examineurl:"/D03M02B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},["D03M03B1"==t.processModel?i("D03M03B1",{ref:"D03M03B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M02B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e(),"D03M06B1PRE"==t.processModel?i("D03M06B1PRE",{ref:"D03M06B1PRE",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M02B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e()],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D03M06B1PRE"==t.processModel?i("D03M06B1PREList",{ref:"D03M06B1PREList",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e(),"D03M03B1"==t.processModel?i("D03M03B1List",{ref:"D03M03B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=(i("c740"),i("b64b"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M02B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M02B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D03M02B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D03M02B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M02B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M02B1/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=r,l=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","grouplevel","abbreviate","payment","orderno","arrivaladd","transport","linkman","linktel","taxrate","operator","summary","billtaxamount","billtaxtotal","billamount","billstatecode","billstatedate","billplandate","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],d=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","partid","itemcode","itemname","itemspec","itemunit","quantity","taxprice","taxamount","itemtaxrate","taxtotal","price","amount","abbreviate","groupname","groupuid","plandate","maxqty","remark","citeuid","citeitemid","statecode","statedate","closed","rownum","orggoodsid","subrate","machuid","machitemid","machgroupid","mainplanuid","mainplanitemid","mrpuid","mrpitemid","virtualitem","customer","custpo","batchno","disannulmark","passedqty","attributejson","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10","custom11","custom12","custom13","custom14","custom15","custom16","custom17","custom18"],m={params:l,paramsItem:d},c={abbreviate:"",amount:0,attributejson:"",batchno:"",citeitemid:"",citeuid:"",closed:0,custom1:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",customer:"",custpo:"",disannuldate:new Date,disannullister:"",disannullisterid:"",disannulmark:0,finishqty:0,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",groupname:"",groupuid:"",id:"",intqtymark:0,itemcode:"",itemname:"",itemspec:"",itemtaxrate:0,itemunit:"",machgroupid:"",machitemid:"",machuid:"",mainplanitemid:"",mainplanuid:"",maxqty:0,mrpitemid:"",mrpuid:"",orggoodsid:"",partid:"",pid:"",plandate:new Date,price:0,quantity:0,remark:"",rownum:0,sourcetype:0,statecode:"",statedate:new Date,subrate:0,taxamount:0,taxprice:0,taxtotal:0,virtualitem:0},u=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"合并打印",icon:"",disabled:"this.formstate==0",methods:"printButton",param:"merge",children:[]},{show:1,divided:!0,label:"一页两联",icon:"el-icon-printer",disabled:"this.formstate==0",methods:"printMultiServer",children:[]},{show:1,divided:!1,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,divided:!0,label:"采购验收",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D03M03B1",children:[]},{show:1,divided:!1,label:"采购预付",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D03M06B1PRE",children:[]}],h=[{show:1,divided:!1,label:"采购验收",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D03M03B1",children:[]},{show:1,divided:!1,label:"采购预付",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D03M06B1PRE",children:[]}],f=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.isDialog||!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[]}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购计划",value:"采购计划"}}),i("el-option",{attrs:{label:"销售订单",value:"销售订单"}}),i("el-option",{attrs:{label:"其他采购",value:"其他采购"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据主题"}},[i("el-input",{attrs:{placeholder:"请输入单据主题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("orderno")}}},[i("el-form-item",{attrs:{label:"订单编号"}},[i("el-input",{attrs:{placeholder:"请输入订单编号",clearable:"",size:"small"},model:{value:t.formdata.orderno,callback:function(e){t.$set(t.formdata,"orderno",e)},expression:"formdata.orderno"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"供应商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("arrivaladd")}}},[i("el-form-item",{attrs:{label:"收货地址"}},[i("el-input",{attrs:{placeholder:"请输入收货地址",clearable:"",size:"small"},model:{value:t.formdata.arrivaladd,callback:function(e){t.$set(t.formdata,"arrivaladd",e)},expression:"formdata.arrivaladd"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("transport")}}},[i("el-form-item",{attrs:{label:"物流方式"}},[i("el-input",{attrs:{placeholder:"请输入物流方式",clearable:"",size:"small"},model:{value:t.formdata.transport,callback:function(e){t.$set(t.formdata,"transport",e)},expression:"formdata.transport"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据计划"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.billplandate,callback:function(e){t.$set(t.formdata,"billplandate",e)},expression:"formdata.billplandate"}})],1)],1),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("payment")}}},[i("el-form-item",{attrs:{label:"付款方式"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入付款方式",clearable:"",size:"small"},model:{value:t.formdata.payment,callback:function(e){t.$set(t.formdata,"payment",e)},expression:"formdata.payment"}})],1)],1)])],1)],1)},p=[],g={props:{formdata:{type:Object},title:{type:String},isDialog:{type:Boolean}},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"供应商为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},b=g,y=(i("7922"),i("2877")),w=Object(y["a"])(b,f,p,!1,null,"c4b439ae",null),v=w.exports,x=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,showcontent:["add","moveup","movedown","delete","copyrow","refresh","billstate"],lst:t.lst,multipleSelection:t.multipleSelection,dummyurl:"/D91M01B1/getVirOnlinePageList"},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,selDummy:t.selDummy,getColumn:t.getColumn},scopedSlots:t._u([{key:"center",fn:function(){return[i("el-dropdown",{attrs:{trigger:"click",placement:"bottom","hide-on-click":!1}},[i("el-button",{attrs:{size:"mini",type:"primary",disabled:!!t.formdata.assessor}},[t._v("批量操作"),i("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{disabled:0==t.multipleSelection.length},nativeOn:{click:function(e){t.deliverydatevisible=!0,t.dateType="plandate"}}},[t._v("计划完成")])],1)],1)]},proxy:!0},{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}}),i("div",{staticStyle:{display:"inline-block"}},[i("QuickTick",{directives:[{name:"show",rawName:"v-show",value:0!=t.lst.length,expression:"lst.length != 0"}],attrs:{droupList:t.droupList},on:{quickTickBtn:t.quickTickBtn,quickTickSearch:t.quickTickSearch,changeDropdown:t.changeDropdown}})],1)]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selgoodsvisible?i("el-dialog",{attrs:{title:"货品信息","append-to-body":!0,visible:t.selgoodsvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selgoodsvisible=e}}},[i("SelGoods",{ref:"selGoods",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selGoods()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selgoodsvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.buyPlanFormVisible?i("el-dialog",{attrs:{title:"请购单","append-to-body":!0,visible:t.buyPlanFormVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.buyPlanFormVisible=e}}},[i("BuyPlan",{ref:"buyPlan",attrs:{multi:1,selecturl:t.selecturl,groupid:t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.buyPlanBtn()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.buyPlanFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.saleOrderFormVisible?i("el-dialog",{attrs:{title:"订单信息","append-to-body":!0,visible:t.saleOrderFormVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.saleOrderFormVisible=e}}},[i("SaleOrder",{ref:"saleOrder",attrs:{multi:1,selecturl:t.selecturl}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.saleOrderBtn()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.saleOrderFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),i("el-dialog",{attrs:{title:"计划完成","append-to-body":!0,width:"400px",visible:t.deliverydatevisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.deliverydatevisible=e}}},[i("el-form",{ref:"customTimeForm"},[i("el-form-item",{attrs:{label:""}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期"},model:{value:t.deliverydate,callback:function(e){t.deliverydate=e},expression:"deliverydate"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitTime}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.deliverydatevisible=!1}}},[t._v("取 消")])],1)],1),i("el-dialog",{staticStyle:{"min-height":"500px"},attrs:{visible:t.isShowHistory,width:"72vw","append-to-body":!0},on:{"update:visible":function(e){t.isShowHistory=e}}},[i("HistoryOrder",{ref:"historyOrder",attrs:{historyData:t.historyData},on:{getHistoryPrice:t.getHistoryPrice}})],1),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},k=[],_=i("b85c"),D=i("c7eb"),S=i("1da1"),O=(i("caad"),i("d81d"),i("a434"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),i("9368")),B=i("9bda"),$=i("174a"),P=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"352px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"领用部门",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodscode))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},q=[],M=(i("99af"),i("25f0"),i("4d90"),i("333d")),C={components:{Pagination:M["a"]},filters:{dateFormat:function(t){var e=new Date(t),i=e.getFullYear(),a=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0");return"".concat(i,"/").concat(a,"/").concat(o)}},props:["multi","groupid","selecturl"],data:function(){return{title:"请购单",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M01B1/getOnlinePageList?groupid="+this.groupid;s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={goodsname:t,goodsuid:t,goodsspec:t,groupuid:t,groupname:t,refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},F=C,I=(i("7085"),Object(y["a"])(F,P,q,!1,null,"ee397bf0",null)),L=I.exports,E=i("2499"),T=i("e82a"),j=i("6465"),R=i("da92"),N={name:"Elitem",components:{SelGoods:B["a"],BuyPlan:L,SaleOrder:E["a"],QuickTick:T["a"],HistoryOrder:j["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{isShowHistory:!1,setColumsVisible:!1,historyData:[],currentRowKey:"",lst:[],multi:0,keynum:0,selgoodsvisible:!1,buyPlanFormVisible:!1,saleOrderFormVisible:!1,selecturl:"",saleorderindex:-1,deliverydate:"",deliverydatevisible:!1,dateType:"",tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:O["b"],customList:[],editmarkfiles:[],droupList:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate","maxqty"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{afterCellValueChange:function(){var e=Object(S["a"])(Object(D["a"])().mark((function e(i){var a,o,s,r,n;return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=i.row,o=i.column,i.changeValue,!t.editmarkfiles.includes(o.field)){e.next=14;break}return e.t0=Object,e.t1={},e.next=6,Object($["a"])("D03M02B1",a,o.field);case 6:for(r in e.t2=e.sent,s=e.t0.assign.call(e.t0,e.t1,e.t2),a)a[r]=s[r];t.setAttributeJson(a,a.rownum),t.countfiles.includes(o.field)?t.changeInput("",a,o.field):Object($["b"])()&&t.changeInput("",a,Object($["b"])()),n=t.customList.findIndex((function(t){return t.attrkey==o.field})),-1!=n&&t.setAttributeJson(a,a.rownum),t.$forceUpdate();case 14:case"end":return e.stop()}}),e)})));function i(t){return e.apply(this,arguments)}return i}()},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(){var e=Object(S["a"])(Object(D["a"])().mark((function e(i){var a,o,s,r,n,l,d;return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i.sourceSelectionData,a=i.targetSelectionData,o=0;case 2:if(!(o<a.length)){e.next=18;break}if(s=a[o],r=t.lst.findIndex((function(t){return t.rowKeys==s.rowKeys})),-1==r){e.next=15;break}return e.next=8,Object($["a"])("D03M02B1",t.lst[r],Object.keys(s)[1]);case 8:for(l in n=e.sent,t.lst[r])t.lst[r][l]=n[l];t.setAttributeJson(t.lst[r],r),t.countfiles.includes(Object.keys(s)[1])?t.changeInput("",t.lst[r],Object.keys(s)[1]):Object($["b"])()&&t.changeInput("",t.lst[r],Object($["b"])()),d=t.customList.findIndex((function(t){return t.attrkey==Object.keys(s)[1]})),-1!=d&&t.setAttributeJson(t.lst[r],r),t.$forceUpdate();case 15:o++,e.next=2;break;case 18:case"end":return e.stop()}}),e)})));function i(t){return e.apply(this,arguments)}return i}()},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},beforePaste:function(){if(2==t.formstate)return!1},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var s=JSON.parse(t[i].attributejson);for(a=0;a<s.length;a++)t[i][s[a].key]=s[a].value}}this.getdroupList(),this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(S["a"])(Object(D["a"])().mark((function e(){var i;return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=O["b"],t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn(O["b"].formcode,i,1).then((function(e){if(t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),0!=t.customList.length)for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=0;o<t.customList.length;o++){var s=t.customList[o];a[s.attrkey]||(a[s.attrkey]="")}t.editmarkfiles=[];for(i=0;i<t.tableForm.item.length;i++){s=t.tableForm.item[i];s.editmark&&t.editmarkfiles.push(s.itemcode)}t.editmarkfiles.push("machuid"),t.editmarkfiles.push("plandate"),t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,operationColumn:!!t.operationmark,renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex),r="",n=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));if(-1!=n){var l=e.customList[n].valuejson?e.customList[n].valuejson.split(","):[];return r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]])]),a("div",{directives:[{name:"show",value:l.length}]},[a("div",{style:"width:0;height:0"},[a("el-select",{class:"wpselect",style:"width:0;height:0;opacity: 0;",attrs:{size:"small",id:t.itemcode+o.rownum+O["b"].formcode},on:{change:function(){var i=Object(S["a"])(Object(D["a"])().mark((function i(a){return Object(D["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:e.selSpuVal(o,t.itemcode,a);case 1:case"end":return i.stop()}}),i)})));return function(t){return i.apply(this,arguments)}}()},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}},[l.map((function(t,e){return a("el-option",{key:e,attrs:{label:t,value:t},style:"max-width:200px"})}))])]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-arrow-down",style:"font-weight:bold;font-size:14px;",on:{click:function(){document.getElementById(t.itemcode+o.rownum+O["b"].formcode).click()}}})])]),r}if("price"===t.itemcode){var d="";return d=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("span",{class:o.disannulmark?"textlinethrough":"",style:"flex:1"},[o[t.itemcode]]),a("el-tooltip",{class:"item",attrs:{effect:"dark",content:"历史价格",placement:"top-start"}},[a("svg-icon",{attrs:{"icon-class":"historyline"},directives:[{name:"show",value:!e.formdata.assessor}],style:"cursor:pointer;",on:{click:function(){return e.getHistoyrOrder(o,o.goodsuid,e.formdata.groupname)}}})])]),d}if("goodsuid"==t.itemcode)return r=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}}),r;if("plandate"==t.itemcode)return r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[e.$options.filters.dateFormat(o[t.itemcode])])]),a("div",[a("div",{style:"opacity: 0;width:0;height:0"},[a("el-date-picker",{style:"width:0;height:0",attrs:{size:"small",id:t.itemcode+s+O["b"].formcode,value:new Date(o[t.itemcode]),type:"date",format:"MM-dd","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},on:{input:function(e){o[t.itemcode]=e}}})]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-date",on:{click:function(){document.getElementById(t.itemcode+s+O["b"].formcode).focus()}}})])]),r;if("machuid"==t.itemcode||"custpo"==t.itemcode)return r="其他采购"!=e.formdata.billtype?a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]):a("div",{class:"flex a-c",style:"width: 100%;justify-content: space-between;"},[a("span",{style:"flex:1"},[o[t.itemcode]]),a("el-popover",{attrs:{placement:"bottom-start",trigger:"click",title:"单据明细"}},[a("div",{style:"position: relative; min-height: 300px",directives:[{name:"show",value:s==e.saleorderindex}]},[a(E["a"],{attrs:{multi:0},style:"width: 800px; height: 220px",on:{singleSel:function(t){return o.machuid=t.refno,o.custpo=t.custorderid}}})]),a("div",{slot:"reference"},[a("i",{class:"writePacksn el-icon-circle-plus-outline",on:{click:function(){e.saleorderindex=s}}})])])]),r;if("status"==t.itemcode){r="";return 0!=o.finishqty&&o.finishqty<o.quantity?r=a("span",{class:"textborder-blue"},["收货"]):o.finishqty>=o.quantity&&0!=o.finishqty?r=a("span",{class:"textborder-green"},["完成"]):o.disannulmark>1?r=a("span",{class:"textborder-grey"},["撤销"]):o.closed&&(r=a("span",{class:"textborder-grey"},["中止"])),r}return r=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]),r}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["goodsuid","amount","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=this;return Object(S["a"])(Object(D["a"])().mark((function a(){var o,s,r,n,l,d,m,c,u;return Object(D["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:for(s in o=[],t[0])o.push(s);r=0;case 3:if(!(r<t.length)){a.next=24;break}n=t[r],l=0;case 6:if(!(l<o.length)){a.next=21;break}return d=o[l],i.lst[e+r][d]=n[d].replace(/^\s*|\s*$/g,""),a.next=11,Object($["a"])("D03M02B1",i.lst[e+r],Object.keys(n)[1]);case 11:for(c in m=a.sent,i.lst[e+r])i.lst[e+r][c]=m[c];i.setAttributeJson(i.lst[e+r],e+r),i.countfiles.includes(d)?i.changeInput("",i.lst[e+r],d):Object($["b"])()&&i.changeInput("",i.lst[e+r],Object($["b"])()),u=i.customList.findIndex((function(t){return t.attrkey==d})),-1!=u&&i.setAttributeJson(i.lst[e+r],e+r),i.$forceUpdate();case 18:l++,a.next=6;break;case 21:r++,a.next=3;break;case 24:case"end":return a.stop()}}),a)})))()},selSpuVal:function(t,e,i){var a=this;return Object(S["a"])(Object(D["a"])().mark((function o(){return Object(D["a"])().wrap((function(o){while(1)switch(o.prev=o.next){case 0:return t[e]=i,o.t0=a,o.next=4,Object($["a"])("D03M02B1",t,e);case 4:o.t1=o.sent,o.t2=t.rownum,o.t0.setAttributeJson.call(o.t0,o.t1,o.t2),Object($["b"])()&&a.changeInput("",t,Object($["b"])());case 8:case"end":return o.stop()}}),o)})))()},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],s={key:o.attrkey,value:t[o.attrkey]?String(t[o.attrkey]).replace(/\s*/g,""):""};""!=s.value&&i.push(s)}if(0==i.length)this.lst[e].attributejson="";else{this.lst[e].attributejson=JSON.stringify(i);for(a=0;a<i.length;a++)this.$set(this.lst[e],i[a].key,i[a].value)}this.$forceUpdate()},changeInput:function(t,e,i){"quantity"==i&&(e.maxqty=e.quantity),"maxqty"==i&&e.maxqty-e.quantity<0&&(this.$message.warning("最大允收数不能小于于数量"),e.maxqty=e.quantity),"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i),this.getSummary()},getAdd:function(t){"采购计划"==this.formdata.billtype?(this.buyPlanFormVisible=!0,this.selecturl="/D03M01B1/getOnlinePageList?appl=1"):"销售订单"==this.formdata.billtype?(this.saleOrderFormVisible=!0,this.selecturl="/D01M03B1/getOnlineWkPageList?appl=1"):this.selgoodsvisible=!0},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"closed",0),this.$set(e,"finishqty",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},selGoods:function(){var t=this.$refs.selGoods.$refs.selectgoods.selection;if(0!=t.length){this.selgoodsvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},c);a.goodsid=i.id,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.goodsmaterial=i.material,a.partid=i.partid,a.goodscustom1=i.custom1,a.goodscustom2=i.custom2,a.goodscustom3=i.custom3,a.goodscustom4=i.custom4,a.goodscustom5=i.custom5,a.goodscustom6=i.custom6,a.goodscustom7=i.custom7,a.goodscustom8=i.custom8,a.goodscustom9=i.custom9,a.goodscustom10=i.custom10,a.itemtaxrate=i.taxrate?i.taxrate:this.nowitemtaxrate,a.quantity=0,a.taxprice=i.inprice?i.inprice:0,a.attributejson=i.attributejson,0!=this.idx&&(a.pid=this.idx),this.changeInput("",a,"taxprice"),this.lst.push(a),this.setAttributeJson(a,this.lst.length-1)}}else this.$message.warning("请选择单据内容")},selDummy:function(t){var e=t;if(0!=e.length){this.$refs.itemGroupBtns.DummyVisible=!1;for(var i=0;i<e.length;i++){var a=e[i],o=Object.assign({},c);o.goodsid=a.id,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.goodscustom1=a.custom1,o.goodscustom2=a.custom2,o.goodscustom3=a.custom3,o.goodscustom4=a.custom4,o.goodscustom5=a.custom5,o.goodscustom6=a.custom6,o.goodscustom7=a.custom7,o.goodscustom8=a.custom8,o.goodscustom9=a.custom9,o.goodscustom10=a.custom10,o.price=a.inprice?a.inprice:0,o.itemtaxrate=a.taxrate?a.taxrate:0,o.quantity=1,o.virtualitem=1,0!=this.idx&&(o.pid=this.idx),this.changeInput("",o,"quantity"),this.lst.push(o)}}else this.$message.warning("请选择虚拟品内容")},buyPlanBtn:function(){this.buyPlanFormVisible=!1;for(var t=this.$refs.buyPlan.$refs.selectVal.selection,e=0;e<t.length;e++){var i=t[e],a=Object.assign({},c);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.citeitemid=i.id,a.citeuid=i.refno,a.customer=i.customer,a.custpo=i.custpo,a.itemtaxrate=i.itemtaxrate,a.maxqty=i.quantity,a.price=i.price,a.quantity=i.quantity,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxamount-i.amount,a.attributejson=i.attributejson,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}},saleOrderBtn:function(){this.saleOrderFormVisible=!1;for(var t=this.$refs.saleOrder.$refs.selectVal.selection,e=0;e<t.length;e++){var i=t[e],a=Object.assign({},c);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.citeitemid=i.id,a.citeuid=i.refno,a.machitemid=i.id,a.machuid=i.refno,a.machgroupid=i.groupid,a.mrpuid=i.mrpuid,a.mrpitemid=i.mrpitemid,a.customer=i.groupname,a.custpo=i.custorderid,a.groupname=i.groupname,a.groupuid=i.groupuid,a.abbreviate=i.abbreviate,a.itemtaxrate=i.itemtaxrate,a.maxqty=i.quantity,a.price=i.price,a.quantity=i.quantity,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxamount-i.amount,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}},submitTime:function(){var t=this;this.multipleSelection.forEach((function(e,i){t.lst[e.rownum][t.dateType]=t.deliverydate})),this.deliverydatevisible=!1,this.$forceUpdate(),this.deliverydate=""},getHistoyrOrder:function(t,e,i){var a=this;this.currentRowKey=t.rowKeys;var o={PageNum:1,PageSize:20,OrderType:1,SearchType:1,scenedata:[{field:"App_Workgroup.groupname",fieldtype:0,math:"like",value:i},{field:"Mat_Goods.goodsuid",fieldtype:0,math:"like",value:e}]};this.$request.post("/D03M02B1/getPageList",JSON.stringify(o)).then((function(t){200===t.data.code&&(a.isShowHistory=!0,a.historyData=t.data.data.list,a.$nextTick((function(){a.$refs.historyOrder.initEchart(a.historyData)})))}))},getHistoryPrice:function(t,e){var i=this;this.isShowHistory=!1,"含税"==e?this.lst.map((function(e){e.rowKeys===i.currentRowKey&&(e.taxprice=t,e.price=R["a"].round(t/1.13,4))})):this.lst.map((function(e){e.rowKeys===i.currentRowKey&&(e.price=t)}))},getdroupList:function(){if(0!=this.lst.length){this.droupList=[];var t,e=[],i=Object(_["a"])(this.lst);try{for(i.s();!(t=i.n()).done;){var a=t.value;if(-1==e.indexOf(a["goodsname"])){e.push(a["goodsname"]);var o={label:a.goodsname,show:!1};this.droupList.push(o)}}}catch(s){i.e(s)}finally{i.f()}}},changeDropdown:function(t){if(t){this.checkboxOption.selectedRowKeys=[],this.multipleSelection=[];for(var e=0;e<this.lst.length;e++)for(var i=this.lst[e],a=0;a<this.droupList.length;a++){var o=this.droupList[a];i.goodsname==o.label&&o.show&&(this.checkboxOption.selectedRowKeys.push(i.rowKeys),this.multipleSelection.push(i))}}},quickTickBtn:function(t){for(var e=0;e<this.lst.length;e++){var i=this.lst[e];if(i.goodsname==t.label)if(t.show)this.checkboxOption.selectedRowKeys.push(i.rowKeys),this.multipleSelection.push(i);else{var a=this.checkboxOption.selectedRowKeys.indexOf(i.rowKeys);this.checkboxOption.selectedRowKeys.splice(a,1),this.multipleSelection.splice(a,1)}}this.$forceUpdate()},quickTickSearch:function(t){if(t){var e=[];this.droupList.forEach((function(i){i.label.indexOf(t)>=0&&e.push(i)})),this.droupList=e}else this.getdroupList()}}},K=N,V=(i("3f1b"),Object(y["a"])(K,x,k,!1,null,"1e0fe4be",null)),z=V.exports,H=i("03b9"),A=i("dcb4"),G=i("ed33"),W=i("941f"),J=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},U=[],X=(i("3ca3"),i("ddb0"),i("4cf0")),Q=i("b893"),Y={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"941f"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:X["b"],customList:[],customData:[],columnHidden:[],footerData:[],rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={citecode:this.searchVal};this.queryParams.SearchPojo=e;var i="/D03M06B1PRE/getPageTh";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(Q["d"])()[0],EndDate:Object(Q["d"])()[1]}),s["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total)})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(S["a"])(Object(D["a"])().mark((function e(){return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(X["b"].formcode,X["b"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxamount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},Z=Y,tt=(i("664a"),Object(y["a"])(Z,J,U,!1,null,"c3a7dfd2",null)),et=tt.exports,it=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},at=[],ot=i("cdcd"),st={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"ed33"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:ot["a"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={orderno:this.searchVal};if(this.queryParams.SearchPojo=e,this.online)var i="/D03M03B1/getOnlinePageList";else i="/D03M03B1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(Q["d"])()[0],EndDate:Object(Q["d"])()[1]}),s["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++){var a=t.lst[i];a.compcost=a.quantity-a.buyqty;for(var o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(S["a"])(Object(D["a"])().mark((function e(){return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(ot["a"].formcode,ot["a"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=a("GoodsInfo",{attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}});return s}if("status"==t.itemcode){s="";return 0!=o.finishqty&&o.finishqty<o.quantity?s=a("span",{class:"textborder-blue"},["入库"]):o.finishqty>=o.quantity&&0!=o.finishqty?s=a("span",{class:"textborder-green"},["完成"]):o.disannulmark>1&&(s=a("span",{class:"textborder-grey"},["撤销"])),s}if("compcost"==t.itemcode){s="";return s=o.quantity-o.finishqty<0?a("span",{style:"color:#F56C6C"},[o.quantity-o.finishqty]):o.quantity-o.finishqty==0?a("span",{style:"color:#67C23A"},[o.quantity-o.finishqty]):o.quantity-o.finishqty==o.quantity?a("span",[o.quantity-o.finishqty]):a("span",{style:"color:#409eff"},[o.quantity-o.finishqty]),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity","compcost"])},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","finishqty","taxamount","compcost"];this.$countCellData(this,i,t,e)},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},rt=st,nt=(i("f550"),Object(y["a"])(rt,it,at,!1,null,"0faf5092",null)),lt=nt.exports,dt={name:"Formedit",components:{FormTemp:A["a"],EditHeader:v,EditItem:z,D03M06B1PRE:W["default"],D03M03B1:G["default"],D03M03B1List:lt,D03M06B1PREList:et},props:["idx","isDialog","initData","billcode","selectList"],data:function(){return{title:"采购订单",operateBar:u,processBar:h,commonurl:"/D03M02B1/printBill",formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,acceptdate:new Date,accepter:"",acceptercode:"",acceptremark:"",arrivaladd:"",assessdate:new Date,assessor:"",billamount:0,billdate:new Date,billplandate:new Date,billstatecode:"",billstatedate:new Date,billtaxamount:0,billtaxtotal:0,billtitle:"",billtype:"销售订单",disannulmark:0,groupid:"",groupname:"",item:[],linkman:"",linktel:"",operator:"",orderno:"",payment:"",prepayments:0,summary:"",taxrate:0,transport:"",webbill:0},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:H["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D03M02B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):H["a"],t.formtemplate.footer.type||(t.formtemplate.footer=H["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&s["a"].get("/D03M02B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.$refs.elitem.multipleSelection=[],t.$refs.elitem.checkboxOption.selectedRowKeys=[],t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id},autoClear:function(){this.formdata.groupname="",this.formdata.groupid=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.billamount=i.billamount,this.formdata.billtaxamount=i.billtaxamount,this.formdata.billtaxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(m,a,this.formdata),0==this.idx?n.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):n.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(e)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){console.log(t,"vallll"),this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},printButton:function(t){this.commonurl="/D03M02B1/printBillMergeGoods",this.$refs.PrintServer.printButton(0,1)},printMultiServer:function(){this.$refs.PrintMultiServer.printButton(0,1)},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t;var e=this.$refs.elitem.multipleSelection;"D03M03B1"===t&&e.length>0&&(this.formdata.item=e)},processBill:function(t){this.processVisible=!0,this.processTitle="D03M03B1"==t?"采购验收":"采购预付",this.processModel=t},billSwitch:function(t){var e=this.$store.getters.userinfo.configs;if("D01M03B1"==t){this.formdata.billtype="销售订单",this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】销售订单转入",this.formdata.billtaxamount=this.initData.billtaxamount,this.formdata.billamount=this.initData.billamount,this.formdata.billtaxtotal=this.initData.billtaxtotal,this.formdata.taxrate=this.initData.taxrate,this.formdata.transport=this.initData.logisticsmode,this.formdata.arrivaladd=this.initData.logisticsport,this.formdata.orderno=this.initData.refno,this.formdata.prepayments=0,this.formdata.assessor="",this.formdata.billdate=new Date,this.formdata.item=[];for(var i=0;i<this.initData.item.length;i++){var a=this.initData.item[i],o=Object.assign({},c);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.custpo=this.initData.custorderid,o.customer=this.initData.groupname,o.groupuid=this.initData.groupuid,o.groupname=this.initData.groupname,o.abbreviate=this.initData.abbreviate,o.citeitemid=a.id,o.citeuid=this.initData.refno,o.machitemid=a.id,o.machuid=this.initData.refno,o.machgroupid=this.initData.groupid,o.mrpuid=a.mrpuid,o.mrpitemid=a.mrpitemid,o.quantity=this.$fomatFloat(a.quantity-a.buyquantity,2),o.itemtaxrate=a.itemtaxrate,o.taxprice=0,o.taxtotal=0,o.maxqty=o.quantity,o.price=0,o.taxamount=0,o.amount=0,o.batchno=a.batchno,o.finishclosed=a.virtualitem?1:0,o.attributejson=a.attributejson,o.virtualitem=a.virtualitem,o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):this.formdata.item.push(o)}}else if("D03M01B2"==t){this.formdata.billtype="采购计划",this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】采购计划转入",this.formdata.orderno=this.initData.refno,this.formdata.arrivaladd=this.initData.arrivaladd,this.formdata.billamount=this.initData.billamount,this.formdata.billtaxtotal=this.initData.billtaxtotal,this.formdata.taxrate=this.initData.taxrate;for(i=0;i<this.initData.item.length;i++){a=this.initData.item[i],o=Object.assign({},c);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.custpo=a.custpo,o.citeitemid=a.id,o.citeuid=this.initData.refno,o.machitemid=a.machitemid,o.machuid=a.machuid,o.machgroupid=a.machgroupid,o.mrpuid=a.mrpuid,o.mrpitemid=a.mrpitemid,o.mainplanuid=a.mainplanuid,o.mainplanitemid=a.mainplanitemid,o.quantity=this.$fomatFloat(a.quantity-a.buyqty,2),o.itemtaxrate=a.itemtaxrate,o.taxprice=a.taxprice,o.taxtotal=a.taxtotal,o.maxqty=o.quantity,o.price=a.price,o.taxamount=a.buyqty?this.$fomatFloat(o.quantity*a.taxprice,2):a.taxamount,o.amount=a.buyqty?this.$fomatFloat(o.quantity*a.price,2):a.amount,o.batchno=a.batchno,o.finishclosed=a.virtualitem?1:0,o.attributejson=a.attributejson,o.virtualitem=a.virtualitem,o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):this.formdata.item.push(o)}}else if("D05M11B1"==t);else if("D05M01B1ML"==t){this.formdata.billtype="其他采购",this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】生产工单转入",this.formdata.orderno=this.initData.refno,this.formdata.item=[];var s=this.selectList.length>0?this.selectList:this.initData.mat;for(i=0;i<s.length;i++){a=s[i],o=Object.assign({},c);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.custpo=a.custpo,o.citeuid=this.initData.refno,o.quantity="物料"==a.goodsstate?this.$fomatFloat(a.bomqty-a.finishqty,2):this.$fomatFloat(a.stoplanqty-a.finishqty,2),o.maxqty=a.bomqty,o.batchno=a.batchno,o.attributejson=a.attributejson;var r=this.initData.item.findIndex((function(t){return t.rowcode==a.itemrowcode}));-1!=r&&(o.citeitemid=this.initData.item[r].id,o.machuid=this.initData.item[r].machuid,o.machitemid=this.initData.item[r].machitemid,o.machgroupid=this.initData.item[r].machgroupid,o.mainplanitemid=this.initData.item[r].mainplanitemid,o.mainplanuid=this.initData.item[r].mainplanuid,o.mrpuid=this.initData.item[r].mrpuid,o.mrpitemid=this.initData.item[r].mrpitemid),o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):o.quantity&&this.formdata.item.push(o)}}}}},mt=dt,ct=(i("7e4c"),Object(y["a"])(mt,a,o,!1,null,"15cbc436",null));e["default"]=ct.exports},a485:function(t,e,i){},af2e:function(t,e,i){"use strict";i("d56d")},b1a9:function(t,e,i){"use strict";i("3470")},b83a:function(t,e,i){"use strict";i("11f6")},bc99:function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"采购开票",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购开票",value:"采购开票"},{label:"委外开票",value:"委外开票"},{label:"外协开票",value:"外协开票"},{label:"其他应付",value:"其他应付"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"外协厂商",searchtype:"factory",type:"autocomplete",methods:"",param:"",require:!0,show:"this.formdata.billtype == '外协开票' || this.formdata.billtype == '委外开票'"},{col:5,code:"groupname",label:"供应厂商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0,show:"this.formdata.billtype == '采购开票' || this.formdata.billtype == '其他应付'"},{col:5,code:"invocode",label:"发票编码",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"invodate",label:"开票日期",type:"date",methods:"computerTime",param:""},{col:5,code:"aimdate",label:"付款计划",type:"date",methods:"",param:""},{col:3,code:"taxamount",label:"发票金额",type:"input",methods:"",param:"",show:"this.formdata.billtype == '其他应付'"},{col:3,code:"taxamount",label:"发票金额",type:"input",methods:"",param:"",show:"this.formdata.billtype != '其他应付'"},{col:3,code:"paid",label:"已付金额",type:"text",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},be8e:function(t,e,i){"use strict";i("1535")},c71a:function(t,e,i){"use strict";i("ebf1")},ca99:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.paid>0&&t.formdata.paid>=t.formdata.taxamount&&0==t.formdata.closed,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,beoverdue:t.formdata.paid<t.formdata.taxamount&&(new Date).getTime()>new Date(t.formdata.aimdate).getTime()&&0==t.formdata.closed,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,computerTime:t.computerTime}})],1)]},proxy:!0},{key:"Item",fn:function(){return["其他应付"!=t.formdata.billtype?i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1):t._e()]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M05B1Edit",commonurl:"/D03M05B1/printBill",weburl:"/D03M05B1/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M05B1Edit",examineurl:"/D03M05B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},[i("D03M06B1",{ref:"D03M06B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M05B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}})],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D03M06B1"==t.processModel?i("D03M06B1List",{ref:"D03M06B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=(i("b64b"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M05B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M05B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D03M05B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D03M05B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M05B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M05B1/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=r,l=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","taxrate","taxamount","taxtotal","amount","invodate","invocode","aimdate","itemtaxrate","summary","closed","disannulmark","fmdocmark","fmdoccode","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],d=["id","pid","finishuid","finishdate","finishtype","finishitemid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","partid","billqty","quantity","taxprice","taxamount","price","amount","taxtotal","rownum","remark","machuid","machitemid","machgroupid","custpo","customer","mrpuid","mrpitemid","orderuid","orderitemid","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10","custom11","custom12","custom13","custom14","custom15","custom16","custom17","custom18"],m=["finishuid","amount","billqty","customer","custpo","partid","goodscustom1","goodscustom10","goodscustom2","goodscustom3","goodscustom4","goodscustom5","goodscustom6","goodscustom7","goodscustom8","goodscustom9","goodsid","goodsmaterial","goodsname","goodsphoto1","goodsspec","goodsuid","goodsunit","price","quantity","remark","taxamount","taxprice","taxtotal"],c={params:l,paramsItem:d,copyItem:m},u={amount:0,billqty:0,custom1:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",customer:"",custpo:"",finishdate:new Date,finishitemid:"",finishtype:"",finishuid:"",goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",id:"",intQtyMark:0,machgroupid:"",machitemid:"",machuid:"",mrpitemid:"",mrpuid:"",partid:"",pid:"",price:0,quantity:0,remark:"",revision:0,rownum:0,taxamount:0,taxprice:0,taxtotal:0},h=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,divided:!0,label:"采购付款",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D03M06B1",children:[]}],f=[{show:1,divided:!1,label:"采购付款",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D03M06B1",children:[]}],p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[],t.formdata.taxamount=0,t.formdata.groupid="",t.formdata.groupname=""}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购开票",value:"采购开票"}}),i("el-option",{attrs:{label:"其他应付",value:"其他应付"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[i("el-form-item",{attrs:{label:"单据主题",prop:"billtitle"}},[i("el-input",{attrs:{placeholder:"请输入单据主题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)])],1),i("el-row",["外协开票"==t.formdata.billtype||"委外开票"==t.formdata.billtype?i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"外协厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B4/getPageList",type:"外协厂商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]):i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"供应厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("invocode")}}},[i("el-form-item",{attrs:{label:"发票编码"}},[i("el-input",{attrs:{placeholder:"请输入发票编码",clearable:"",size:"small"},model:{value:t.formdata.invocode,callback:function(e){t.$set(t.formdata,"invocode",e)},expression:"formdata.invocode"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("invodate")}}},[i("el-form-item",{attrs:{label:"开票日期",prop:"invodate"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期",size:"small"},on:{change:function(e){return t.$emit("computerTime")}},model:{value:t.formdata.invodate,callback:function(e){t.$set(t.formdata,"invodate",e)},expression:"formdata.invodate"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"付款计划"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.aimdate,callback:function(e){t.$set(t.formdata,"aimdate",e)},expression:"formdata.aimdate"}})],1)],1),i("el-col",{attrs:{span:9}},[i("div",{staticStyle:{display:"flex"}},[i("el-form-item",{attrs:{label:"发票金额"}},["其他应付"==t.formdata.billtype?i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入发票金额",size:"small"},model:{value:t.formdata.taxamount,callback:function(e){t.$set(t.formdata,"taxamount",e)},expression:"formdata.taxamount"}}):i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v(" ￥"+t._s(t.formdata.taxamount?t.formdata.taxamount:0))])],1),i("el-form-item",{attrs:{label:"已付金额"}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.paid?t.formdata.paid:0))])])],1)])],1)],1)},g=[],b={props:{formdata:{type:Object},title:{type:String}},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"往来单位为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},y=b,w=(i("b1a9"),i("2877")),v=Object(w["a"])(y,p,g,!1,null,"a883edd0",null),x=v.exports,k=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","copyrow","refresh"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"left",fn:function(){return[i("el-button",{attrs:{type:"primary",size:"mini",disabled:2==t.formstate},nativeOn:{click:function(e){return t.getSelKouKuan(1)}}},[i("i",{staticClass:"el-icon-remove-outline"}),t._v(" 扣款")])]},proxy:!0},{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.acceptFormVisible?i("el-dialog",{attrs:{title:"采购验收单","append-to-body":!0,visible:t.acceptFormVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.acceptFormVisible=e}}},[i("SelAccept",{ref:"selAccept",attrs:{multi:t.multi,selecturl:"/D03M03R1/getOnlineInvoPageList?groupid="+t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selAccept()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.acceptFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.KouKuanVisible?i("el-dialog",{attrs:{title:"扣款单","append-to-body":!0,visible:t.KouKuanVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.KouKuanVisible=e}}},[i("SelKouKuan",{ref:"selKouKuan",attrs:{multi:1,selecturl:"/D03M04B1/getOnlinePageList?groupid="+t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selKouKuan()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.KouKuanVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},_=[],D=i("c7eb"),S=i("1da1"),O=(i("c740"),i("caad"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),i("da86")),B=i("9a2c"),$=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),i("el-table-column",{attrs:{label:"金额",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.taxamount))])]}}])}),i("el-table-column",{attrs:{label:"采购订单号",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.orderuid))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},P=[],q=i("333d"),M={components:{Pagination:q["a"]},props:["multi","selecturl"],data:function(){return{title:"采购扣款",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M04B1/getPageList";s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},C=M,F=(i("c71a"),Object(w["a"])(C,$,P,!1,null,"3edf162c",null)),I=F.exports,L={name:"Elitem",components:{SelAccept:B["a"],SelKouKuan:I},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],multi:0,keynum:0,acceptFormVisible:!1,setColumsVisible:!1,KouKuanVisible:!1,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,index:0,tableForm:O["b"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate","billqty"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;t.editmarkfiles.includes(a.field)&&t.countfiles.includes(a.field)&&t.changeInput("",i,a.field)}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[s],Object.keys(o)[1])}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(S["a"])(Object(D["a"])().mark((function e(){var i;return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=O["b"],t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn(O["b"].formcode,i).then((function(e){t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,operationColumn:!!t.operationmark,renderBodyCell:function(e,i){var a=e.row;e.column,e.rowIndex;if("goodsuid"==t.itemcode){var o=i("GoodsInfo",{class:a.disannulmark?"textlinethrough":"",attrs:{scopeIndex:a.rownum,scopeVal:a[t.itemcode]}});return o}o=i("span",{class:a.disannulmark?"textlinethrough":""},[a[t.itemcode]]);return o}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1,this.$forceUpdate()},getSummary:function(){this.$getSummary(this,["goodsuid","amount","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.changeInput("",this.lst[e+o],n)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){if("itemtaxrate"==i)this.nowitemtaxrate=e.itemtaxrate;else if("quantity"==i&&e.quantity>e.billqty)return this.$message.warning("本次数量不能大于单据数量"),void(e.quantity=e.billqty);e=this.$countInput(e,i),this.getSummary()},getAdd:function(t){this.formdata.groupid?(this.acceptFormVisible=!0,this.multi=t):this.$message.warning("请先选择往来单位")},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"closed",0),this.$set(e,"finishqty",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},selAccept:function(){var t=this.$refs.selAccept.$refs.selectVal.selection;if(0!=t.length){this.acceptFormVisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.price=i.price,a.amount=i.amount,a.quantity=i.quantity,a.taxprice=i.taxprice,a.taxamount=i.taxamount,a.itemtaxrate=i.itemtaxrate,a.billqty=i.quantity,a.taxtotal=i.taxtotal,a.customer=i.customer,a.custpo=i.custpo,a.finishitemid=i.id,a.finishtype=i.billtype,a.finishuid=i.refno,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.orderuid=i.orderuid,a.orderitemid=i.orderitemid,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},getSelKouKuan:function(){this.formdata.groupid?this.KouKuanVisible=!0:this.$message.warning("请先选择供应商")},selKouKuan:function(){var t=this.$refs.selKouKuan.$refs.selectVal.selection;if(0!=t.length){this.KouKuanVisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=Number(0-i.amount),a.billqty=i.quantity,a.customer=i.customer,a.custpo=i.custpo,a.finishitemid=i.id,a.finishtype=i.billtype,a.finishuid=i.refno,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.price=Number(0-i.price),a.quantity=i.quantity,a.taxamount=Number(0-i.taxamount),a.taxprice=Number(0-i.taxprice),a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},E=L,T=(i("3949"),Object(w["a"])(E,k,_,!1,null,"4f9eee22",null)),j=T.exports,R=i("bc99"),N=i("dcb4"),K=i("d85f"),V=i("501c"),z={name:"Formedit",components:{FormTemp:N["a"],EditHeader:x,EditItem:j,D03M06B1:K["default"],D03M06B1List:V["a"]},props:["idx","isDialog","initData","billcode"],data:function(){return{title:"采购开票",operateBar:h,processBar:f,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,aimdate:new Date,amount:0,assessdate:new Date,assessor:"",billdate:new Date,billtitle:"",billtype:"采购开票",closed:0,disannulmark:0,groupid:"",groupname:"",invocode:"",invodate:new Date,item:[],paid:0,refno:"",statecode:"",statedate:new Date,summary:"",taxamount:0,taxrate:0,taxtotal:0},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",creditdquantity:0,creditduint:"day",formtemplate:R["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;s["a"].get("/SaFormCustom/getEntityByCode?key=D03M05B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):R["a"],t.formtemplate.footer.type||(t.formtemplate.footer=R["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&s["a"].get("/D03M05B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.creditdquantity=t.creditdquantity,this.creditduint=t.creditduint,this.computerTime()},autoClear:function(){this.formdata.groupname="",this.formdata.groupid=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if("其他应付"!=this.formdata.billtype){if(0==this.$refs.elitem.lst.length)return void this.$message.warning("单据内容不能为空");for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.amount=i.billamount,this.formdata.taxamount=i.billtaxamount,this.formdata.taxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(c,a,this.formdata)}else a=Object.assign({},this.formdata);0==this.idx?n.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):n.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(t)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},computerTime:function(){if(0!=this.creditdquantity){var t=this.formdata.invodate.getTime();"month"==this.creditduint?t+=2592e6*this.creditdquantity:t+=864e5*this.creditdquantity,this.formdata.aimdate=new Date(t)}},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t},processBill:function(t){this.processVisible=!0,this.processTitle="采购付款",this.processModel=t},billSwitch:function(t){if(console.log(this.initData,"123456"),"D03M03B1"==t){this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】采购验收转入",this.formdata.item=[];for(var e=0;e<this.initData.item.length;e++){var i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.billqty=i.quantity,a.price=i.price,a.amount=i.amount,a.quantity=i.quantity,a.taxprice=i.taxprice,a.taxamount=i.taxamount,a.itemtaxrate=i.itemtaxrate,a.taxtotal=i.taxtotal,a.finishuid=this.initData.refno,a.finishitemid=i.id,a.finishtype=this.initData.billtype,a.orderuid=i.orderuid,a.orderitemid=i.orderitemid,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.customer=i.customer,a.custpo=i.custpo,a.virtualitem=i.virtualitem,this.formdata.item.push(a)}}else"D08M04B1PN"==t&&(this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】支出计划转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.suppliers,this.formdata.groupuid=this.initData.groupuid,this.formdata.linkman=this.initData.linkman,this.formdata.telephone=this.initData.telephone,this.formdata.billtype="其他应付",this.formdata.invocode=this.initData.invocode,this.formdata.aimdate=this.initData.aimdate,this.formdata.taxamount=this.initData.taxamount,this.formdata.receipted=this.initData.finishamt,this.formdata.item=[])}}},H=z,A=(i("0377"),Object(w["a"])(H,a,o,!1,null,"c851054e",null));e["default"]=A.exports},cc06:function(t,e,i){"use strict";i("3218")},cdcd:function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r}));var a={formcode:"D03M03B1Th",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Finishing.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billtaxamount",itemname:"含税金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billtaxamount"},{itemcode:"billamount",itemname:"未税金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Finishing.billamount"},{itemcode:"billtaxtotal",itemname:"税额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billtaxtotal"},{itemcode:"arrivaladd",itemname:"收货地址",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Finishing.arrivaladd"},{itemcode:"transport",itemname:"物流方式",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Finishing.transport"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_Finishing.status"},{itemcode:"itemcount",itemname:"款数",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.assessor"}]},o={formcode:"D03M03B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Buy_Finishing.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"orderno",itemname:"订单编号",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_FinishingItem.orderno"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.taxamount"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.quantity"},{itemcode:"finishqty",itemname:"已入库",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"50",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.status"}]},s={formcode:"D03M03B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"60",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"70",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"60",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"finishqty",itemname:"已出入库数",minwidth:"80",displaymark:1,overflow:1},{itemcode:"orderno",itemname:"订单编号",minwidth:"100",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"150",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D03M03B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Buy_Finishing.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"orderno",itemname:"订单编号",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_FinishingItem.orderno"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.taxamount"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.quantity"},{itemcode:"finishqty",itemname:"已入库",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"50",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.status"}]}},d56d:function(t,e,i){},d8b2:function(t,e,i){"use strict";i("4d45")},d93a:function(t,e,i){"use strict";i("6e4b")},da86:function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r}));var a={formcode:"D03M05B1Th",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Bus_Invoice.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Invoice.billdate"},{itemcode:"groupuid",itemname:"供应商编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"invocode",itemname:"发票编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.invocode"},{itemcode:"aimdate",itemname:"付款计划",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Invoice.aimdate"},{itemcode:"taxrate",itemname:"税率%",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Invoice.taxrate"},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.taxamount"},{itemcode:"paid",itemname:"已付金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.paid"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.status"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.assessor"}]},o={formcode:"D03M05B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Bus_Invoice.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"finishuid",itemname:"收货单号",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_InvoiceItem.finishuid"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_InvoiceItem.quantity"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.taxamount"}]},s={formcode:"D03M05B1Item",item:[{itemcode:"finishuid",itemname:"收货单号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"billqty",itemname:"单据数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"本次数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"price",itemname:"未税单价",minwidth:"70",displaymark:1,overflow:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",displaymark:1,overflow:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"150",displaymark:1,overflow:1}]},r={formcode:"D03M05B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Bus_Invoice.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"finishuid",itemname:"收货单号",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_InvoiceItem.finishuid"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_InvoiceItem.quantity"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.taxamount"}]}},dea7:function(t,e,i){},e149:function(t,e,i){},ebf1:function(t,e,i){},ed33:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getSuppGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M03B1Edit",commonurl:"/D03M03B1/printBill",weburl:"/D03M03B1/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M03B1Edit",examineurl:"/D03M03B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},["D04M01B1"==t.processModel?i("D04M01B1",{ref:"D04M01B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M03B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e(),"D03M05B1"==t.processModel?i("D03M05B1",{ref:"D03M05B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M03B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e()],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D04M01B1"==t.processModel?i("D04M01B1List",{ref:"D04M01B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e(),"D03M05B1"==t.processModel?i("D03M05B1List",{ref:"D03M05B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=(i("b64b"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M03B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M03B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D03M03B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D03M03B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M03B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M03B1/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=r,l=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","operator","arrivaladd","transport","summary","billtaxamount","billtaxtotal","billamount","custom1","custom3","custom2","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],d=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","partid","itemcode","itemname","itemspec","itemunit","quantity","taxprice","taxamount","taxtotal","itemtaxrate","price","amount","remark","orderno","orderuid","orderitemid","closed","rownum","invoclosed","virtualitem","customer","custpo","location","batchno","machuid","machitemid","machgroupid","mainplanuid","mainplanitemid","mrpuid","mrpitemid","disannulmark","attributejson","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],m={params:l,paramsItem:d},c={amount:0,attributejson:"",batchno:"",closed:0,custom1:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",customer:"",custpo:"",deliqty:0,disannuldate:new Date,disannullister:"",disannullisterid:"",disannulmark:0,finishqty:0,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",id:"",inspectid:"",inspectuid:"",intQtyMark:0,invoclosed:0,invoqty:0,itemcode:"",itemname:"",itemspec:"",itemtaxrate:0,itemunit:"",location:"",machgroupid:"",machitemid:"",machuid:"",mainplanitemid:"",mainplanuid:"",mrpitemid:"",mrpuid:"",orderitemid:"",orderno:"",orderuid:"",partid:"",passedqty:0,pid:"",price:0,quantity:0,remark:"",revision:0,rownum:0,sourcetype:0,statecode:"",statedate:new Date,taxamount:0,taxprice:0,taxtotal:0,virtualitem:0},u=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,divided:!0,label:"出入库单",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D04M01B1",children:[]},{show:1,divided:!1,label:"采购开票",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D03M05B1",children:[]}],h=[{show:1,divided:!1,label:"出入库单",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D04M01B1",children:[]},{show:1,divided:!1,label:"采购开票",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D03M05B1",children:[]}],f=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[]}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购验收",value:"采购验收"}}),i("el-option",{attrs:{label:"采购退货",value:"采购退货"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据主题"}},[i("el-input",{attrs:{placeholder:"请输入单据主题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"供应厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("arrivaladd")}}},[i("el-form-item",{attrs:{label:"收货地址"}},[i("el-input",{attrs:{placeholder:"请输入收货地址",clearable:"",size:"small"},model:{value:t.formdata.arrivaladd,callback:function(e){t.$set(t.formdata,"arrivaladd",e)},expression:"formdata.arrivaladd"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("transport")}}},[i("el-form-item",{attrs:{label:"物流方式"}},[i("el-input",{attrs:{placeholder:"请输入物流方式",clearable:"",size:"small"},model:{value:t.formdata.transport,callback:function(e){t.$set(t.formdata,"transport",e)},expression:"formdata.transport"}})],1)],1)])],1)],1)},p=[],g={props:{formdata:{type:Object},title:{type:String}},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"供应商为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},b=g,y=(i("8955"),i("2877")),w=Object(y["a"])(b,f,p,!1,null,"8fa2c892",null),v=w.exports,x=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","dummy","moveup","movedown","delete","copyrow","refresh","billstate"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection,dummyurl:"/D91M01B1/getVirOnlinePageList"},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,selDummy:t.selDummy,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selbuyordervisible?i("el-dialog",{attrs:{title:"采购订单","append-to-body":!0,visible:t.selbuyordervisible,width:"1000px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selbuyordervisible=e}}},[i("SelBuyOrder",{ref:"selBuyOrder",attrs:{multi:t.multi,selecturl:t.selecturl,groupid:t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selBuyOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selbuyordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},k=[],_=i("c7eb"),D=i("1da1"),S=i("ade3"),O=(i("c740"),i("caad"),i("d81d"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),i("da92")),B=i("cdcd"),$=i("65e3"),P={name:"Elitem",components:{SelBuyOrder:$["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return Object(S["a"])(Object(S["a"])(Object(S["a"])({lst:[],multi:0,keynum:0,selbuyordervisible:!1,setColumsVisible:!1,selecturl:"",tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:B["b"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;if(t.editmarkfiles.includes(a.field)){t.countfiles.includes(a.field)&&t.changeInput("",i,a.field);var o=t.customList.findIndex((function(t){return t.attrkey==a.field}));-1!=o&&t.setAttributeJson(i,i.rownum)}}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}}},"footerData",[]),"cellAutofillOption",{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));if(-1!=s){t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[s],Object.keys(o)[1]);var r=t.customList.findIndex((function(t){return t.attrkey==Object.keys(o)[1]}));-1!=r&&t.setAttributeJson(t.lst[s],s)}}}}),"clipboardOption",{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}})},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var s=JSON.parse(t[i].attributejson);for(a=0;a<s.length;a++)t[i][s[a].key]=s[a].value}}this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(D["a"])(Object(_["a"])().mark((function e(){var i;return Object(_["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=B["b"],t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn(B["b"].formcode,i,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,operationColumn:!!t.operationmark,renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex),r="",n=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));if(-1!=n){var l=e.customList[n].valuejson?e.customList[n].valuejson.split(","):[];return r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]])]),a("div",{directives:[{name:"show",value:l.length}]},[a("div",{style:"width:0;height:0"},[a("el-select",{class:"wpselect",style:"width:0;height:0;opacity: 0;",attrs:{size:"small",id:t.itemcode+o.rownum+B["b"].formcode},on:{change:function(i){o[t.itemcode]=i,e.setAttributeJson(o,o.rownum)}},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}},[l.map((function(t,e){return a("el-option",{key:e,attrs:{label:t,value:t},style:"max-width:200px"})}))])]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-arrow-down",style:"font-weight:bold;font-size:14px;",on:{click:function(){document.getElementById(t.itemcode+o.rownum+B["b"].formcode).click()}}})])]),r}return"goodsuid"==t.itemcode?(r=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}}),r):"plandate"==t.itemcode?(r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[e.$options.filters.dateFormat(o[t.itemcode])])]),a("div",[a("div",{style:"opacity: 0;width:0;height:0"},[a("el-date-picker",{style:"width:0;height:0",attrs:{size:"small",id:t.itemcode+s+B["b"].formcode,value:new Date(o[t.itemcode]),type:"date",format:"MM-dd","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},on:{input:function(e){o[t.itemcode]=e}}})]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-date",on:{click:function(){document.getElementById(t.itemcode+s+B["b"].formcode).focus()}}})])]),r):(r=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]),r)}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["goodsuid","amount","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.changeInput("",this.lst[e+o],n);var l=this.customList.findIndex((function(t){return t.attrkey==n}));-1!=l&&this.setAttributeJson(this.lst[e+o],e+o)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],s={key:o.attrkey,value:t[o.attrkey]?t[o.attrkey].replace(/\s*/g,""):""};""!=s.value&&i.push(s)}0==i.length?this.lst[e].attributejson="":this.lst[e].attributejson=JSON.stringify(i),this.$forceUpdate()},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i),this.getSummary()},getAdd:function(t){this.formdata.groupid?("采购验收"==this.formdata.billtype?this.selecturl="/D03M02B1/getOnlineFinishPageList?groupid="+this.formdata.groupid+"&appl=1":this.selecturl="/D03M02B1/getRetrunFinishPageList?groupid="+this.formdata.groupid+"&appl=1",this.selbuyordervisible=!0,this.multi=t):this.$message.warning("请选择供应商")},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"closed",0),this.$set(e,"finishqty",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},selBuyOrder:function(){var t=this.$refs.selBuyOrder.$refs.selectVal.selection;if(0!=t.length){this.selbuyordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e];i.itemtaxrate&&(this.nowitemtaxrate=t[e].itemtaxrate);var a=Object.assign({},c);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.amount=i.amount,a.attributejson=i.attributejson,a.batchno=i.batchno,a.itemtaxrate=i.itemtaxrate,a.orderitemid=i.id,a.orderuid=i.refno,a.orderno=i.refno,a.price=i.price,a.quantity=O["a"].minus(i.quantity,i.finishqty),a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selDummy:function(t){var e=t;if(0!=e.length){this.$refs.itemGroupBtns.DummyVisible=!1;for(var i=0;i<e.length;i++){var a=e[i],o=Object.assign({},c);o.goodsid=a.id,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.goodscustom1=a.custom1,o.goodscustom2=a.custom2,o.goodscustom3=a.custom3,o.goodscustom4=a.custom4,o.goodscustom5=a.custom5,o.goodscustom6=a.custom6,o.goodscustom7=a.custom7,o.goodscustom8=a.custom8,o.goodscustom9=a.custom9,o.goodscustom10=a.custom10,o.price=a.inprice?a.inprice:0,o.itemtaxrate=a.taxrate?a.taxrate:0,o.quantity=1,o.virtualitem=1,0!=this.idx&&(o.pid=this.idx),this.changeInput("",o,"quantity"),this.lst.push(o)}}else this.$message.warning("请选择虚拟品内容")}}},q=P,M=(i("cc06"),Object(y["a"])(q,x,k,!1,null,"5fc99a69",null)),C=M.exports,F=i("1f11"),I=i("dcb4"),L=i("ca99"),E=i("13df"),T=i("27f6"),j=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},R=[],N=(i("3ca3"),i("ddb0"),i("da86")),K=i("b893"),V={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"ca99"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:N["a"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={finishuid:this.searchVal};if(this.queryParams.SearchPojo=e,this.online)var i="/D03M05B1/getOnlinePageList";else i="/D03M05B1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(K["d"])()[0],EndDate:Object(K["d"])()[1]}),s["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++){var a=t.lst[i];a.compcost=a.quantity-a.buyqty;for(var o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(D["a"])(Object(_["a"])().mark((function e(){return Object(_["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(N["a"].formcode,N["a"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=a("goodsInfo",{attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}});return s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","amount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},z=V,H=(i("d93a"),Object(y["a"])(z,j,R,!1,null,"0fc3ac8a",null)),A=H.exports,G={name:"Formedit",components:{FormTemp:I["a"],EditHeader:v,EditItem:C,D03M05B1:L["default"],D04M01B1:E["default"],D04M01B1List:T["a"],D03M05B1List:A},props:["idx","isDialog","initData","billcode","isprocessDialog"],data:function(){return{title:"采购验收",operateBar:u,processBar:h,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,arrivaladd:"",assessdate:new Date,assessor:"",billamount:0,billdate:new Date,billpaid:0,billstatecode:"",billstatedate:new Date,billtaxamount:0,billtaxtotal:0,billtitle:"",billtype:"采购验收",disannulmark:0,groupid:"",groupname:"",item:[],operator:"",refno:"",summary:"",transport:""},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:F["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D03M03B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):F["a"],t.formtemplate.footer.type||(t.formtemplate.footer=F["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx?s["a"].get("/D03M03B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.$refs.elitem.multipleSelection=[],t.$refs.elitem.checkboxOption.selectedRowKeys=[],t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")})):this.formdata.item=[]},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.item=[]},autoClear:function(){this.formdata.groupname="",this.formdata.groupid=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.billamount=i.billamount,this.formdata.billtaxamount=i.billtaxamount,this.formdata.billtaxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(m,a,this.formdata),0==this.idx?n.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.formdata=e.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):n.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.formdata=e.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(t)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){console.log(t,"vallll"),this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t;var e=this.$refs.elitem.multipleSelection;"D04M01B1"!==t&&"D03M05B1"!==t||e.length>0&&(this.formdata.item=e)},processBill:function(t){this.processVisible=!0,this.processTitle="D04M01B1"==t?"出入库单":"采购开票",this.processModel=t},billSwitch:function(t){console.log(this.initData);var e=this.$store.getters.userinfo.configs;if("D03M02B1"==t){this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】采购订单转入",this.formdata.abbreviate=this.initData.abbreviate,this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.arrivaladd=this.initData.arrivaladd,this.formdata.transport=this.initData.transport,this.formdata.amount=this.initData.amount,this.formdata.item=[];for(var i=0;i<this.initData.item.length;i++){var a=this.initData.item[i],o=Object.assign({},c);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.machgroupid=a.machgroupid,o.machitemid=a.machitemid,o.machuid=a.machuid,o.mrpitemid=a.mrpitemid,o.mrpuid=a.mrpuid,o.mainplanitemid=a.mainplanitemid,o.mainplanuid=a.mainplanuid,o.quantity=this.$fomatFloat(a.quantity-a.finishqty,2),o.price=a.price,o.amount=a.amount,o.taxprice=a.taxprice,o.taxamount=a.taxamount,o.taxtotal=a.taxtotal,o.itemtaxrate=a.itemtaxrate,o.customer=a.customer,o.custpo=a.custpo,o.orderitemid=a.id,o.orderno=this.initData.refno,o.orderuid=this.initData.refno,o.attributejson=a.attributejson,o.virtualitem=a.virtualitem,o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):(this.$refs.elitem.changeInput("",o,"quantity"),this.formdata.item.push(o))}}else if("D03M02B1List"==t){this.formdata.billtitle=this.initData[0].billtitle?this.initData[0].billtitle:"【"+this.initData[0].refno+"】采购订单转入",this.formdata.abbreviate=this.initData[0].abbreviate,this.formdata.groupid=this.initData[0].groupid,this.formdata.groupname=this.initData[0].groupname,this.formdata.groupuid=this.initData[0].groupuid,this.formdata.arrivaladd=this.initData[0].arrivaladd,this.formdata.transport=this.initData[0].transport,this.formdata.item=[];for(i=0;i<this.initData.length;i++){a=this.initData[i],o=Object.assign({},c);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.machgroupid=a.machgroupid,o.machitemid=a.machitemid,o.machuid=a.machuid,o.mrpitemid=a.mrpitemid,o.mrpuid=a.mrpuid,o.mainplanitemid=a.mainplanitemid,o.mainplanuid=a.mainplanuid,o.quantity=this.$fomatFloat(a.quantity-a.finishqty,2),o.price=a.price,o.amount=a.amount,o.taxprice=a.taxprice,o.taxamount=a.taxamount,o.taxtotal=a.taxtotal,o.itemtaxrate=a.itemtaxrate,o.customer=a.customer,o.custpo=a.custpo,o.orderitemid=a.id,o.orderno=a.refno,o.orderuid=a.refno,o.attributejson=a.attributejson,o.virtualitem=a.virtualitem,o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):(this.$refs.elitem.changeInput("",o,"quantity"),this.formdata.item.push(o))}}}}},W=G,J=(i("be8e"),Object(y["a"])(W,a,o,!1,null,"7255f33c",null));e["default"]=J.exports},f550:function(t,e,i){"use strict";i("367a")},fb056:function(t,e,i){"use strict";i("6ccf")}}]);