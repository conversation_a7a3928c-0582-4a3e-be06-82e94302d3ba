(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4c02cf38"],{3489:function(t,e,a){},"657b":function(t,e,a){"use strict";a("8cf5")},"714d":function(t,e,a){},"8cf5":function(t,e,a){},9443:function(t,e,a){"use strict";a("714d")},a422:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx,"get-type":t.getType}},{formcomp:t.formcomp,formclose:t.formclose,changeidx:t.changeidx,BindData:t.BindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.FormVisible,expression:"!FormVisible"}],ref:"index",staticClass:"index"},[a("listheader",{on:{btnadd:function(e){return t.showform(0,"create")},btnsearch:t.search,showAll:t.showAll,AdvancedSearch:t.AdvancedSearch,batcadd:t.batcadd}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"mytab tab-container",style:{height:t.MaxHeight}},[a("el-col",{attrs:{span:3}},[a("div",{staticClass:"projectTab tabItem"},[a("div",{staticClass:"tab-header"},[t._v(" 项目"),a("i",{staticClass:"el-icon-s-tools tools",on:{click:function(e){t.openproject=!t.openproject}}})]),a("ul",{staticClass:"tab-content"},t._l(t.projectData,(function(e,o){return a("li",{key:o,class:e.isActive?"isActive":"",on:{click:function(a){t.BindData("pageData",e.permid),t.changeLi("projectData",o)}}},[a("p",{attrs:{title:e.permname}},[t._v(t._s(e.permname))]),a("span",{directives:[{name:"show",rawName:"v-show",value:t.openproject,expression:"openproject"}]},[a("i",{staticClass:"el-icon-edit",on:{click:function(a){return t.showform(e.permid,"update")}}}),a("i",{staticClass:"el-icon-plus",on:{click:function(a){return t.showform(e.permid,"create")}}}),a("i",{staticClass:"el-icon-delete",on:{click:function(a){return t.deleteBtn(e.permid,"delete")}}})])])})),0)])]),a("el-col",{attrs:{span:3}},[a("div",{staticClass:"pageTab tabItem"},[a("div",{staticClass:"tab-header"},[t._v(" 页面 "),a("i",{staticClass:"el-icon-s-tools tools",on:{click:function(e){t.openpage=!t.openpage}}})]),a("ul",{staticClass:"tab-content"},t._l(t.pageData,(function(e,o){return a("li",{key:o,class:e.isActive?"isActive":"",on:{click:function(a){t.BindData("groupData",e.permid),t.changeLi("pageData",o)}}},[a("p",{attrs:{title:e.permname}},[t._v(t._s(e.permname))]),a("span",{directives:[{name:"show",rawName:"v-show",value:t.openpage,expression:"openpage"}]},[a("i",{staticClass:"el-icon-edit",on:{click:function(a){return t.showform(e.permid,"update")}}}),a("i",{staticClass:"el-icon-plus",on:{click:function(a){return t.showform(e.permid,"create")}}}),a("i",{staticClass:"el-icon-delete",on:{click:function(a){return t.deleteBtn(e.permid,"delete")}}})])])})),0)])]),a("el-col",{attrs:{span:3}},[a("div",{staticClass:"groupingTab tabItem"},[a("div",{staticClass:"tab-header"},[t._v(" 分组"),a("i",{staticClass:"el-icon-s-tools tools",on:{click:function(e){t.opengroup=!t.opengroup}}})]),a("ul",{staticClass:"tab-content"},t._l(t.groupData,(function(e,o){return a("li",{key:o,class:e.isActive?"isActive":"",on:{click:function(a){t.BindData("powerData",e.permid),t.changeLi("groupData",o)}}},[a("p",{attrs:{title:e.permname}},[t._v(t._s(e.permname))]),a("span",{directives:[{name:"show",rawName:"v-show",value:t.opengroup,expression:"opengroup"}]},[a("i",{staticClass:"el-icon-edit",on:{click:function(a){return t.showform(e.permid,"update")}}}),a("i",{staticClass:"el-icon-plus",on:{click:function(a){return t.showform(e.permid,"create")}}}),a("i",{staticClass:"el-icon-delete",on:{click:function(a){return t.deleteBtn(e.permid,"delete")}}})])])})),0)])]),a("el-col",{attrs:{span:15}},[a("div",{staticClass:"powerTab tabItem"},[t.refreshTable?a("el-table",{staticStyle:{overflow:"auto"},attrs:{data:t.powerData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight}},[a("el-table-column",{attrs:{label:"权限名称",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.permname))])]}}],null,!1,2416268043)}),a("el-table-column",{attrs:{label:"权限编码",align:"center","min-width":"100px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.permcode))])]}}],null,!1,1274863809)}),a("el-table-column",{attrs:{label:"权限类型",align:"center","min-width":"50px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return["0"==e.row.permtype?a("el-tag",{attrs:{type:"success"}},[t._v("项 目")]):"1"==e.row.permtype?a("el-tag",[t._v("页 面")]):"2"==e.row.permtype?a("el-tag",{attrs:{type:"warning"}},[t._v("分 组")]):a("el-tag",[t._v("权 限")])]}}],null,!1,1907008037)}),a("el-table-column",{attrs:{label:"状态",align:"center","min-width":"50px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.enabledmark?a("el-tag",[t._v("正常")]):a("el-tag",{attrs:{type:"warning"}},[t._v("停用")])]}}],null,!1,1057982045)}),a("el-table-column",{attrs:{label:"是否公开",align:"center","min-width":"50px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[0==e.row.ispublic?a("el-tag",[t._v("公开")]):a("el-tag",{attrs:{type:"info"}},[t._v("不公开")])]}}],null,!1,3149613024)}),a("el-table-column",{attrs:{label:"创建时间",align:"center","min-width":"80px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormat")(e.row.createdate)))])]}}],null,!1,4279550968)}),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"80px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-edit"},on:{click:function(a){return t.showform(e.row.permid,"update")}}},[t._v("修改")]),a("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-delete"},on:{click:function(a){return t.deleteBtn(e.row.permid,"delete")}}},[t._v("删除")])]}}],null,!1,3818400990)})],1):t._e()],1)])],1)])],1)],1)],1)])},i=[],r=(a("99af"),a("e9c4"),a("d3b7"),a("25f0"),a("4d90"),a("159b"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",size:"mini",plain:""},on:{click:t.btnadd}},[t._v(" 添加 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",size:"mini",plain:""},on:{click:t.batcadd}},[t._v(" 批量添加 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"info",icon:"el-icon-sort",size:"mini",plain:""},on:{click:t.showAll}},[t._v(" 展开/折叠 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"}},[t._v("列设置")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:t.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini"},on:{click:function(e){t.iShow=!t.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"权限编码"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入权限编码",size:"small"},model:{value:t.formdata.permcode,callback:function(e){t.$set(t.formdata,"permcode",e)},expression:"formdata.permcode"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"权限名称"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入权限名称",size:"small"},model:{value:t.formdata.permname,callback:function(e){t.$set(t.formdata,"permname",e)},expression:"formdata.permname"}})],1)],1),a("el-col",{attrs:{span:6}},[a("div")]),a("el-col",{attrs:{span:6}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"6px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.AdvancedSearch()}}},[t._v(" 搜索 ")])],1)],1),a("el-row")],1)],1)])],1)}),n=[],s={name:"Listheader",data:function(){return{strfilter:"",iShow:!1,formdata:{}}},methods:{AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},batcadd:function(){this.$emit("batcadd")},showAll:function(){this.$emit("showAll")},btnsearch:function(){console.log(this.strfilter),this.$emit("btnsearch",this.strfilter)}}},l=s,c=(a("de5d"),a("2877")),d=Object(c["a"])(l,r,n,!1,null,"2dbabc22",null),m=d.exports,p=a("333d"),f=a("b775"),u=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.formclose(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"on",rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"上级权限"}},[a("el-cascader",{staticStyle:{width:"100%"},attrs:{options:t.permData,props:t.defaultProps,clearable:"","change-on-select":"","show-all-levels":!1,size:"small"},on:{change:t.handleChange},model:{value:t.formdata.parentid,callback:function(e){t.$set(t.formdata,"parentid",e)},expression:"formdata.parentid"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("permcode")}}},[a("el-form-item",{attrs:{label:"权限编码",prop:"permcode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入权限编码",clearable:"",size:"small"},model:{value:t.formdata.permcode,callback:function(e){t.$set(t.formdata,"permcode",e)},expression:"formdata.permcode"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("permname")}}},[a("el-form-item",{attrs:{label:"权限名称",prop:"permname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入权限名称",clearable:"",size:"small"},model:{value:t.formdata.permname,callback:function(e){t.$set(t.formdata,"permname",e)},expression:"formdata.permname"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"显示排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,size:"small","controls-position":"right"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"权限类型"}},[a("el-radio",{attrs:{label:"0"},model:{value:t.formdata.permtype,callback:function(e){t.$set(t.formdata,"permtype",e)},expression:"formdata.permtype"}},[t._v("项目")]),a("el-radio",{attrs:{label:"1"},model:{value:t.formdata.permtype,callback:function(e){t.$set(t.formdata,"permtype",e)},expression:"formdata.permtype"}},[t._v("页面")]),a("el-radio",{attrs:{label:"2"},model:{value:t.formdata.permtype,callback:function(e){t.$set(t.formdata,"permtype",e)},expression:"formdata.permtype"}},[t._v("分组")]),a("el-radio",{attrs:{label:"3"},model:{value:t.formdata.permtype,callback:function(e){t.$set(t.formdata,"permtype",e)},expression:"formdata.permtype"}},[t._v("按键")])],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"是否公开"}},[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.ispublic,callback:function(e){t.$set(t.formdata,"ispublic",e)},expression:"formdata.ispublic"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"状态"}},[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.enabledmark,callback:function(e){t.$set(t.formdata,"enabledmark",e)},expression:"formdata.enabledmark"}})],1)],1)],1)],1),a("el-divider")],1),a("el-form",{attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},h=[],g=a("c7eb"),b=a("1da1"),w=(a("b64b"),a("5658")),v={name:"Formedit",components:{},props:["idx","getType"],data:function(){return{title:"权限编码",formdata:{parentid:"root",permcode:"",permname:"",permtype:"0",ispublic:0,enabledmark:1,rownum:0,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{permcode:[{required:!0,trigger:"blur",message:"权限编码为必填项"}],permname:[{required:!0,trigger:"blur",message:"权限名称为必填项"}]},formLabelWidth:"100px",formheight:"500px",functionOptions:[],permData:[],defaultProps:{children:"children",label:"permname",value:"permid"},queryParams:{PageNum:1,PageSize:1e3,OrderType:1,SearchType:0}}},computed:{formcontainHeight:function(){return window.innerHeight-50-33-20-20-50+"px"}},watch:{idx:function(t,e){console.log("new: %s, old: %s",t,e),this.BindData()}},created:function(){this.BindData()},methods:{BindData:function(){var t=this;return Object(b["a"])(Object(g["a"])().mark((function e(){return Object(g["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.listLoading=!0,e.next=3,f["a"].post("/SaPermCode/getPageList",JSON.stringify(t.queryParams)).then((function(e){console.log("=====00000000======",e),200==e.data.code&&(t.permData=t.changeFormat(e.data.data.list))}));case 3:if("update"!=t.getType){e.next=6;break}return e.next=6,f["a"].get("/SaPermCode/getEntity?key=".concat(t.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 数据错误，返回主表","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.formclose()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 6:case"end":return e.stop()}}),e)})))()},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;console.log("可保存"),e.formsave()}))},formsave:function(){var t=this;0==this.idx&&"create"==this.getType?(w["a"].add(this.formdata).then((function(e){200==e.code&&(t.$message({showClose:!0,message:"保存成功",type:"success"}),t.formclose())})).catch((function(e){t.$message({showClose:!0,message:"保存失败",type:"warning"})})),console.log("完成窗口")):0!=this.idx&&"create"==this.getType?(this.formdata.parentid=this.idx,console.log(),w["a"].add(this.formdata).then((function(e){console.log("新建保存====",e),200==e.code&&(t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("BindData"),t.formclose())})).catch((function(e){t.$message.warning("保存失败")})),console.log("完成窗口")):"update"==this.getType&&(w["a"].update(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("BindData"),t.formclose())})).catch((function(e){t.$message.warning("保存失败")})),console.log("完成窗口"))},formclose:function(){this.$emit("formclose"),console.log("关闭窗口")},rowdel:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),w["a"].delete(t).then((function(t){200==t.code&&e.$message({showClose:!0,message:"删除成功",type:"success"}),e.$emit("formcomp")})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},check:function(){console.log("check")},handleChange:function(t){t.length>0?this.formdata.parentid=t[t.length-1]:this.formdata.parentid="root"},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},changeFormat:function(t){var e=[];if(!Array.isArray(t))return e;t.forEach((function(t){delete t.children}));var a={};return t.forEach((function(t){a[t.permid]=t})),t.forEach((function(t){var o=a[t.parentid];o?(o.children||(o.children=[])).push(t):e.push(t)})),e}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),o=(e.getMonth()+1).toString().padStart(2,"0"),i=e.getDate().toString().padStart(2,"0"),r=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0"),s=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(o,"-").concat(i," ").concat(r,":").concat(n,":").concat(s)}}}},y=v,x=(a("657b"),Object(c["a"])(y,u,h,!1,null,"68ded390",null)),S=x.exports,_=a("d44f"),k={name:"SaPermCode",components:{Pagination:p["a"],listheader:m,formedit:S,batcadd:_["default"]},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),o=(e.getMonth()+1).toString().padStart(2,"0"),i=e.getDate().toString().padStart(2,"0"),r=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0"),s=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(o,"-").concat(i," ").concat(r,":").concat(n,":").concat(s)}},data:function(){return{lst:[],searchstr:"",total:0,FormVisible:!1,listLoading:!1,refreshTable:!1,isShowAll:!0,idx:0,getType:"create",queryParams:{PageNum:1,PageSize:1e3,OrderType:0,SearchType:0,OrderBy:"rownum"},projectData:[],openproject:!1,pageData:[],openpage:!1,groupData:[],opengroup:!1,powerData:[]}},computed:{MaxHeight:function(){return window.innerHeight-140+"px"},tableMaxHeight:function(){return window.innerHeight-145}},watch:{},created:function(){this.searchstr="",this.BindData("projectData","root"),this.showAll()},methods:{BindData:function(t,e){var a=this;this.listLoading=!0,console.log("paramVal",e);var o={parentid:e};this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,o):this.queryParams.SearchPojo=o,console.log(t),"projectData"==t?(this.groupData=[],this.powerData=[]):"groupData"==t&&(this.powerData=[]),f["a"].post("/SaPermCode/getPageList",JSON.stringify(this.queryParams)).then((function(e){if(console.log("=====00000000======",e),200==e.data.code){a[t]=e.data.data.list;for(var o=0;o<a[t].length;o++)a[t][o].isActive=!1}a.listLoading=!1})).catch((function(t){a.listLoading=!1}))},changeLi:function(t,e){for(var a=0;a<this[t].length;a++)this[t][a].isActive=!1;this[t][e].isActive=!0},deleteBtn:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),f["a"].get("/SaPermCode/delete?key=".concat(t)).then((function(t){console.log("删除："+t),200==t.data.code?(e.$message.success("删除成功"),e.BindData()):e.$message.warning(t.data.msg)})).catch((function(t){e.$message.warning("删除失败")}))})).catch((function(){}))},search:function(t){""!=t?this.queryParams.SearchPojo={permname:t,permcode:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.BindData()},AdvancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.BindData()},showform:function(t,e){this.idx=t,this.getType=e,this.FormVisible=!0},formclose:function(){this.FormVisible=!1,console.log("关闭编码窗口")},formcomp:function(){this.BindData(),this.FormVisible=!1,console.log("完成并刷新index")},handleNodeClick:function(t){console.log(t)},showAll:function(){this.isShowAll=!this.isShowAll,this.isShowAll?this.handleOpen():this.handleClose()},handleOpen:function(){var t=this;this.refreshTable=!1,this.isShowAll=!0,this.$nextTick((function(){t.refreshTable=!0}))},handleClose:function(){var t=this;this.refreshTable=!1,this.isShowAll=!1,this.$nextTick((function(){t.refreshTable=!0}))},batcadd:function(){console.log("批量添加"),this.$router.push({path:"/Sa/PermCode/batcadd"})},changeidx:function(t){this.idx=t},changeFormat:function(t,e,a){var o=[];if(!Array.isArray(t))return o;t.forEach((function(t){delete t.children}));var i={};return t.forEach((function(t){i[t.permid]=t})),t.forEach((function(t){var e=i[t.parentid];e?(e.children||(e.children=[])).push(t):o.push(t)})),o}}},C=k,$=(a("9443"),Object(c["a"])(C,o,i,!1,null,null,null));e["default"]=$.exports},de5d:function(t,e,a){"use strict";a("3489")}}]);