(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0dac9a"],{"6ca8":function(e,t,n){(function(t,n){e.exports=n()})(0,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={exports:{},id:r,loaded:!1};return e[r].call(i.exports,i,i.exports,n),i.loaded=!0,i.exports}return n.m=e,n.c=t,n.p="",n(0)}([function(e,t,n){n(7),n(8),e.exports=n(9)},function(e,t,n){(function(t){(function(n){var r="function"===typeof t&&t||function(e){setTimeout(e,1)};function i(e,t){return function(){e.apply(t,arguments)}}var o=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function a(e){if("object"!==typeof this)throw new TypeError("Promises must be constructed via new");if("function"!==typeof e)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],d(e,i(c,this),i(u,this))}function s(e){var t=this;null!==this._state?r((function(){var n=t._state?e.onFulfilled:e.onRejected;if(null!==n){var r;try{r=n(t._value)}catch(i){return void e.reject(i)}e.resolve(r)}else(t._state?e.resolve:e.reject)(t._value)})):this._deferreds.push(e)}function c(e){try{if(e===this)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"===typeof e||"function"===typeof e)){var t=e.then;if("function"===typeof t)return void d(i(t,e),i(c,this),i(u,this))}this._state=!0,this._value=e,l.call(this)}catch(n){u.call(this,n)}}function u(e){this._state=!1,this._value=e,l.call(this)}function l(){for(var e=0,t=this._deferreds.length;e<t;e++)s.call(this,this._deferreds[e]);this._deferreds=null}function f(e,t,n,r){this.onFulfilled="function"===typeof e?e:null,this.onRejected="function"===typeof t?t:null,this.resolve=n,this.reject=r}function d(e,t,n){var r=!1;try{e((function(e){r||(r=!0,t(e))}),(function(e){r||(r=!0,n(e))}))}catch(i){if(r)return;r=!0,n(i)}}a.prototype["catch"]=function(e){return this.then(null,e)},a.prototype.then=function(e,t){var n=this;return new a((function(r,i){s.call(n,new f(e,t,r,i))}))},a.all=function(){var e=Array.prototype.slice.call(1===arguments.length&&o(arguments[0])?arguments[0]:arguments);return new a((function(t,n){if(0===e.length)return t([]);var r=e.length;function i(o,a){try{if(a&&("object"===typeof a||"function"===typeof a)){var s=a.then;if("function"===typeof s)return void s.call(a,(function(e){i(o,e)}),n)}e[o]=a,0===--r&&t(e)}catch(c){n(c)}}for(var o=0;o<e.length;o++)i(o,e[o])}))},a.resolve=function(e){return e&&"object"===typeof e&&e.constructor===a?e:new a((function(t){t(e)}))},a.reject=function(e){return new a((function(t,n){n(e)}))},a.race=function(e){return new a((function(t,n){for(var r=0,i=e.length;r<i;r++)e[r].then(t,n)}))},a._setImmediateFn=function(e){r=e},a.prototype.always=function(e){var t=this.constructor;return this.then((function(n){return t.resolve(e()).then((function(){return n}))}),(function(n){return t.resolve(e()).then((function(){throw n}))}))},"undefined"!==typeof e&&e.exports?e.exports=a:n.Promise||(n.Promise=a)})(this)}).call(t,n(2).setImmediate)},function(e,t,n){(function(e){var r="undefined"!==typeof e&&e||"undefined"!==typeof self&&self||window,i=Function.prototype.apply;function o(e,t){this._id=e,this._clearFn=t}t.setTimeout=function(){return new o(i.call(setTimeout,r,arguments),clearTimeout)},t.setInterval=function(){return new o(i.call(setInterval,r,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},o.prototype.unref=o.prototype.ref=function(){},o.prototype.close=function(){this._clearFn.call(r,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout((function(){e._onTimeout&&e._onTimeout()}),t))},n(3),t.setImmediate="undefined"!==typeof self&&self.setImmediate||"undefined"!==typeof e&&e.setImmediate||this&&this.setImmediate,t.clearImmediate="undefined"!==typeof self&&self.clearImmediate||"undefined"!==typeof e&&e.clearImmediate||this&&this.clearImmediate}).call(t,function(){return this}())},function(e,t,n){(function(e,t){(function(e,n){"use strict";if(!e.setImmediate){var r,i=1,o={},a=!1,s=e.document,c=Object.getPrototypeOf&&Object.getPrototypeOf(e);c=c&&c.setTimeout?c:e,"[object process]"==={}.toString.call(e.process)?h():g()?p():e.MessageChannel?m():s&&"onreadystatechange"in s.createElement("script")?w():v(),c.setImmediate=u,c.clearImmediate=l}function u(e){"function"!==typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),n=0;n<t.length;n++)t[n]=arguments[n+1];var a={callback:e,args:t};return o[i]=a,r(i),i++}function l(e){delete o[e]}function f(e){var t=e.callback,r=e.args;switch(r.length){case 0:t();break;case 1:t(r[0]);break;case 2:t(r[0],r[1]);break;case 3:t(r[0],r[1],r[2]);break;default:t.apply(n,r);break}}function d(e){if(a)setTimeout(d,0,e);else{var t=o[e];if(t){a=!0;try{f(t)}finally{l(e),a=!1}}}}function h(){r=function(e){t.nextTick((function(){d(e)}))}}function g(){if(e.postMessage&&!e.importScripts){var t=!0,n=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=n,t}}function p(){var t="setImmediate$"+Math.random()+"$",n=function(n){n.source===e&&"string"===typeof n.data&&0===n.data.indexOf(t)&&d(+n.data.slice(t.length))};e.addEventListener?e.addEventListener("message",n,!1):e.attachEvent("onmessage",n),r=function(n){e.postMessage(t+n,"*")}}function m(){var e=new MessageChannel;e.port1.onmessage=function(e){var t=e.data;d(t)},r=function(t){e.port2.postMessage(t)}}function w(){var e=s.documentElement;r=function(t){var n=s.createElement("script");n.onreadystatechange=function(){d(t),n.onreadystatechange=null,e.removeChild(n),n=null},e.appendChild(n)}}function v(){r=function(e){setTimeout(d,0,e)}}})("undefined"===typeof self?"undefined"===typeof e?this:e:self)}).call(t,function(){return this}(),n(4))},function(e,t){var n,r,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(e){if(n===setTimeout)return setTimeout(e,0);if((n===o||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}function c(e){if(r===clearTimeout)return clearTimeout(e);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{return r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(function(){try{n="function"===typeof setTimeout?setTimeout:o}catch(e){n=o}try{r="function"===typeof clearTimeout?clearTimeout:a}catch(e){r=a}})();var u,l=[],f=!1,d=-1;function h(){f&&u&&(f=!1,u.length?l=u.concat(l):d=-1,l.length&&g())}function g(){if(!f){var e=s(h);f=!0;var t=l.length;while(t){u=l,l=[];while(++d<t)u&&u[d].run();d=-1,t=l.length}u=null,f=!1,c(e)}}function p(e,t){this.fun=e,this.array=t}function m(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];l.push(new p(e,t)),1!==l.length||f||s(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=m,i.addListener=m,i.once=m,i.off=m,i.removeListener=m,i.removeAllListeners=m,i.emit=m,i.prependListener=m,i.prependOnceListener=m,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,t){var n=function(){try{return new Blob,!0}catch(e){return!1}}()?window.Blob:function(e,t){var n=new(window.BlobBuilder||window.WebKitBlobBuilder||window.MSBlobBuilder||window.MozBlobBuilder);return e.forEach((function(e){n.append(e)})),n.getBlob(t?t.type:void 0)};function r(){var e=~navigator.userAgent.indexOf("Android")&&~navigator.vendor.indexOf("Google")&&!~navigator.userAgent.indexOf("Chrome");return e&&navigator.userAgent.match(/AppleWebKit\/(\d+)/).pop()<=534||/MQQBrowser/g.test(navigator.userAgent)}var i=function(){var e=0;function t(){var t=this,r=[],i=Array(21).join("-")+(+new Date*(1e16*Math.random())).toString(36),o=XMLHttpRequest.prototype.send;this.getParts=function(){return r.toString()},this.append=function(e,t,n){r.push("--"+i+'\r\nContent-Disposition: form-data; name="'+e+'"'),t instanceof Blob?(r.push('; filename="'+(n||"blob")+'"\r\nContent-Type: '+t.type+"\r\n\r\n"),r.push(t)):r.push("\r\n\r\n"+t),r.push("\r\n")},e++,XMLHttpRequest.prototype.send=function(a){var s,c,u=this;a===t?(r.push("--"+i+"--\r\n"),c=new n(r),s=new FileReader,s.onload=function(){o.call(u,s.result)},s.onerror=function(e){throw e},s.readAsArrayBuffer(c),this.setRequestHeader("Content-Type","multipart/form-data; boundary="+i),e--,0==e&&(XMLHttpRequest.prototype.send=o)):o.call(this,a)}}return t.prototype=Object.create(FormData.prototype),t}();e.exports={Blob:n,FormData:r()?i:FormData}},function(e,t,n){var r,i;(function(){var n=!1,o=function(e){return e instanceof o?e:this instanceof o?void(this.EXIFwrapped=e):new o(e)};"undefined"!==typeof e&&e.exports&&(t=e.exports=o),t.EXIF=o;var a=o.Tags={36864:"ExifVersion",40960:"FlashpixVersion",40961:"ColorSpace",40962:"PixelXDimension",40963:"PixelYDimension",37121:"ComponentsConfiguration",37122:"CompressedBitsPerPixel",37500:"MakerNote",37510:"UserComment",40964:"RelatedSoundFile",36867:"DateTimeOriginal",36868:"DateTimeDigitized",37520:"SubsecTime",37521:"SubsecTimeOriginal",37522:"SubsecTimeDigitized",33434:"ExposureTime",33437:"FNumber",34850:"ExposureProgram",34852:"SpectralSensitivity",34855:"ISOSpeedRatings",34856:"OECF",37377:"ShutterSpeedValue",37378:"ApertureValue",37379:"BrightnessValue",37380:"ExposureBias",37381:"MaxApertureValue",37382:"SubjectDistance",37383:"MeteringMode",37384:"LightSource",37385:"Flash",37396:"SubjectArea",37386:"FocalLength",41483:"FlashEnergy",41484:"SpatialFrequencyResponse",41486:"FocalPlaneXResolution",41487:"FocalPlaneYResolution",41488:"FocalPlaneResolutionUnit",41492:"SubjectLocation",41493:"ExposureIndex",41495:"SensingMethod",41728:"FileSource",41729:"SceneType",41730:"CFAPattern",41985:"CustomRendered",41986:"ExposureMode",41987:"WhiteBalance",41988:"DigitalZoomRation",41989:"FocalLengthIn35mmFilm",41990:"SceneCaptureType",41991:"GainControl",41992:"Contrast",41993:"Saturation",41994:"Sharpness",41995:"DeviceSettingDescription",41996:"SubjectDistanceRange",40965:"InteroperabilityIFDPointer",42016:"ImageUniqueID"},s=o.TiffTags={256:"ImageWidth",257:"ImageHeight",34665:"ExifIFDPointer",34853:"GPSInfoIFDPointer",40965:"InteroperabilityIFDPointer",258:"BitsPerSample",259:"Compression",262:"PhotometricInterpretation",274:"Orientation",277:"SamplesPerPixel",284:"PlanarConfiguration",530:"YCbCrSubSampling",531:"YCbCrPositioning",282:"XResolution",283:"YResolution",296:"ResolutionUnit",273:"StripOffsets",278:"RowsPerStrip",279:"StripByteCounts",513:"JPEGInterchangeFormat",514:"JPEGInterchangeFormatLength",301:"TransferFunction",318:"WhitePoint",319:"PrimaryChromaticities",529:"YCbCrCoefficients",532:"ReferenceBlackWhite",306:"DateTime",270:"ImageDescription",271:"Make",272:"Model",305:"Software",315:"Artist",33432:"Copyright"},c=o.GPSTags={0:"GPSVersionID",1:"GPSLatitudeRef",2:"GPSLatitude",3:"GPSLongitudeRef",4:"GPSLongitude",5:"GPSAltitudeRef",6:"GPSAltitude",7:"GPSTimeStamp",8:"GPSSatellites",9:"GPSStatus",10:"GPSMeasureMode",11:"GPSDOP",12:"GPSSpeedRef",13:"GPSSpeed",14:"GPSTrackRef",15:"GPSTrack",16:"GPSImgDirectionRef",17:"GPSImgDirection",18:"GPSMapDatum",19:"GPSDestLatitudeRef",20:"GPSDestLatitude",21:"GPSDestLongitudeRef",22:"GPSDestLongitude",23:"GPSDestBearingRef",24:"GPSDestBearing",25:"GPSDestDistanceRef",26:"GPSDestDistance",27:"GPSProcessingMethod",28:"GPSAreaInformation",29:"GPSDateStamp",30:"GPSDifferential"},u=o.StringValues={ExposureProgram:{0:"Not defined",1:"Manual",2:"Normal program",3:"Aperture priority",4:"Shutter priority",5:"Creative program",6:"Action program",7:"Portrait mode",8:"Landscape mode"},MeteringMode:{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"},LightSource:{0:"Unknown",1:"Daylight",2:"Fluorescent",3:"Tungsten (incandescent light)",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 - 5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"},Flash:{0:"Flash did not fire",1:"Flash fired",5:"Strobe return light not detected",7:"Strobe return light detected",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"},SensingMethod:{1:"Not defined",2:"One-chip color area sensor",3:"Two-chip color area sensor",4:"Three-chip color area sensor",5:"Color sequential area sensor",7:"Trilinear sensor",8:"Color sequential linear sensor"},SceneCaptureType:{0:"Standard",1:"Landscape",2:"Portrait",3:"Night scene"},SceneType:{1:"Directly photographed"},CustomRendered:{0:"Normal process",1:"Custom process"},WhiteBalance:{0:"Auto white balance",1:"Manual white balance"},GainControl:{0:"None",1:"Low gain up",2:"High gain up",3:"Low gain down",4:"High gain down"},Contrast:{0:"Normal",1:"Soft",2:"Hard"},Saturation:{0:"Normal",1:"Low saturation",2:"High saturation"},Sharpness:{0:"Normal",1:"Soft",2:"Hard"},SubjectDistanceRange:{0:"Unknown",1:"Macro",2:"Close view",3:"Distant view"},FileSource:{3:"DSC"},Components:{0:"",1:"Y",2:"Cb",3:"Cr",4:"R",5:"G",6:"B"}};function l(e){return!!e.exifdata}function f(e,t){t=t||e.match(/^data\:([^\;]+)\;base64,/im)[1]||"",e=e.replace(/^data\:([^\;]+)\;base64,/gim,"");for(var n=atob(e),r=n.length,i=new ArrayBuffer(r),o=new Uint8Array(i),a=0;a<r;a++)o[a]=n.charCodeAt(a);return i}function d(e,t){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="blob",n.onload=function(e){200!=this.status&&0!==this.status||t(this.response)},n.send()}function h(e,t){function r(n){var r=g(n),i=p(n);e.exifdata=r||{},e.iptcdata=i||{},t&&t.call(e)}if(e.src)if(/^data\:/i.test(e.src)){var i=f(e.src);r(i)}else if(/^blob\:/i.test(e.src)){var o=new FileReader;o.onload=function(e){r(e.target.result)},d(e.src,(function(e){o.readAsArrayBuffer(e)}))}else{var a=new XMLHttpRequest;a.onload=function(){200==this.status||0===this.status?r(a.response):t(new Error("Could not load image")),a=null},a.open("GET",e.src,!0),a.responseType="arraybuffer",a.send(null)}else if(window.FileReader&&(e instanceof window.Blob||e instanceof window.File)){o=new FileReader;o.onload=function(e){n&&console.log("Got file of length "+e.target.result.byteLength),r(e.target.result)},o.readAsArrayBuffer(e)}}function g(e){var t=new DataView(e);if(n&&console.log("Got file of length "+e.byteLength),255!=t.getUint8(0)||216!=t.getUint8(1))return n&&console.log("Not a valid JPEG"),!1;var r,i=2,o=e.byteLength;while(i<o){if(255!=t.getUint8(i))return n&&console.log("Not a valid marker at offset "+i+", found: "+t.getUint8(i)),!1;if(r=t.getUint8(i+1),n&&console.log(r),225==r)return n&&console.log("Found 0xFFE1 marker"),S(t,i+4,t.getUint16(i+2)-2);i+=2+t.getUint16(i+2)}}function p(e){var t=new DataView(e);if(n&&console.log("Got file of length "+e.byteLength),255!=t.getUint8(0)||216!=t.getUint8(1))return n&&console.log("Not a valid JPEG"),!1;var r=2,i=e.byteLength,o=function(e,t){return 56===e.getUint8(t)&&66===e.getUint8(t+1)&&73===e.getUint8(t+2)&&77===e.getUint8(t+3)&&4===e.getUint8(t+4)&&4===e.getUint8(t+5)};while(r<i){if(o(t,r)){var a=t.getUint8(r+7);a%2!==0&&(a+=1),0===a&&(a=4);var s=r+8+a,c=t.getUint16(r+6+a);return w(e,s,c)}r++}}var m={120:"caption",110:"credit",25:"keywords",55:"dateCreated",80:"byline",85:"bylineTitle",122:"captionWriter",105:"headline",116:"copyright",15:"category"};function w(e,t,n){var r,i,o,a,s=new DataView(e),c={},u=t;while(u<t+n)28===s.getUint8(u)&&2===s.getUint8(u+1)&&(a=s.getUint8(u+2),a in m&&(o=s.getInt16(u+3),o+5,i=m[a],r=b(s,u+5,o),c.hasOwnProperty(i)?c[i]instanceof Array?c[i].push(r):c[i]=[c[i],r]:c[i]=r)),u++;return c}function v(e,t,r,i,o){var a,s,c,u=e.getUint16(r,!o),l={};for(c=0;c<u;c++)a=r+12*c+2,s=i[e.getUint16(a,!o)],!s&&n&&console.log("Unknown tag: "+e.getUint16(a,!o)),l[s]=y(e,a,t,r,o);return l}function y(e,t,n,r,i){var o,a,s,c,u,l,f=e.getUint16(t+2,!i),d=e.getUint32(t+4,!i),h=e.getUint32(t+8,!i)+n;switch(f){case 1:case 7:if(1==d)return e.getUint8(t+8,!i);for(o=d>4?h:t+8,a=[],c=0;c<d;c++)a[c]=e.getUint8(o+c);return a;case 2:return o=d>4?h:t+8,b(e,o,d-1);case 3:if(1==d)return e.getUint16(t+8,!i);for(o=d>2?h:t+8,a=[],c=0;c<d;c++)a[c]=e.getUint16(o+2*c,!i);return a;case 4:if(1==d)return e.getUint32(t+8,!i);for(a=[],c=0;c<d;c++)a[c]=e.getUint32(h+4*c,!i);return a;case 5:if(1==d)return u=e.getUint32(h,!i),l=e.getUint32(h+4,!i),s=new Number(u/l),s.numerator=u,s.denominator=l,s;for(a=[],c=0;c<d;c++)u=e.getUint32(h+8*c,!i),l=e.getUint32(h+4+8*c,!i),a[c]=new Number(u/l),a[c].numerator=u,a[c].denominator=l;return a;case 9:if(1==d)return e.getInt32(t+8,!i);for(a=[],c=0;c<d;c++)a[c]=e.getInt32(h+4*c,!i);return a;case 10:if(1==d)return e.getInt32(h,!i)/e.getInt32(h+4,!i);for(a=[],c=0;c<d;c++)a[c]=e.getInt32(h+8*c,!i)/e.getInt32(h+4+8*c,!i);return a}}function b(e,t,n){var r,i="";for(r=t;r<t+n;r++)i+=String.fromCharCode(e.getUint8(r));return i}function S(e,t){if("Exif"!=b(e,t,4))return n&&console.log("Not valid EXIF data! "+b(e,t,4)),!1;var r,i,o,l,f,d=t+6;if(18761==e.getUint16(d))r=!1;else{if(19789!=e.getUint16(d))return n&&console.log("Not valid TIFF data! (no 0x4949 or 0x4D4D)"),!1;r=!0}if(42!=e.getUint16(d+2,!r))return n&&console.log("Not valid TIFF data! (no 0x002A)"),!1;var h=e.getUint32(d+4,!r);if(h<8)return n&&console.log("Not valid TIFF data! (First offset less than 8)",e.getUint32(d+4,!r)),!1;if(i=v(e,d,d+h,s,r),i.ExifIFDPointer)for(o in l=v(e,d,d+i.ExifIFDPointer,a,r),l){switch(o){case"LightSource":case"Flash":case"MeteringMode":case"ExposureProgram":case"SensingMethod":case"SceneCaptureType":case"SceneType":case"CustomRendered":case"WhiteBalance":case"GainControl":case"Contrast":case"Saturation":case"Sharpness":case"SubjectDistanceRange":case"FileSource":l[o]=u[o][l[o]];break;case"ExifVersion":case"FlashpixVersion":l[o]=String.fromCharCode(l[o][0],l[o][1],l[o][2],l[o][3]);break;case"ComponentsConfiguration":l[o]=u.Components[l[o][0]]+u.Components[l[o][1]]+u.Components[l[o][2]]+u.Components[l[o][3]];break}i[o]=l[o]}if(i.GPSInfoIFDPointer)for(o in f=v(e,d,d+i.GPSInfoIFDPointer,c,r),f){switch(o){case"GPSVersionID":f[o]=f[o][0]+"."+f[o][1]+"."+f[o][2]+"."+f[o][3];break}i[o]=f[o]}return i}o.getData=function(e,t){return!((e instanceof Image||e instanceof HTMLImageElement)&&!e.complete)&&(l(e)?t&&t.call(e):h(e,t),!0)},o.getTag=function(e,t){if(l(e))return e.exifdata[t]},o.getAllTags=function(e){if(!l(e))return{};var t,n=e.exifdata,r={};for(t in n)n.hasOwnProperty(t)&&(r[t]=n[t]);return r},o.pretty=function(e){if(!l(e))return"";var t,n=e.exifdata,r="";for(t in n)n.hasOwnProperty(t)&&("object"==typeof n[t]?n[t]instanceof Number?r+=t+" : "+n[t]+" ["+n[t].numerator+"/"+n[t].denominator+"]\r\n":r+=t+" : ["+n[t].length+" values]\r\n":r+=t+" : "+n[t]+"\r\n");return r},o.readFromBinaryFile=function(e){return g(e)},r=[],i=function(){return o}.apply(t,r),void 0===i||(e.exports=i)}).call(this)},function(e,t,n){var r,i;(function(){function n(e){var t=e.naturalWidth,n=e.naturalHeight;if(t*n>1048576){var r=document.createElement("canvas");r.width=r.height=1;var i=r.getContext("2d");return i.drawImage(e,1-t,0),0===i.getImageData(0,0,1,1).data[3]}return!1}function o(e,t,n){var r=document.createElement("canvas");r.width=1,r.height=n;var i=r.getContext("2d");i.drawImage(e,0,0);var o=i.getImageData(0,0,1,n).data,a=0,s=n,c=n;while(c>a){var u=o[4*(c-1)+3];0===u?s=c:a=c,c=s+a>>1}var l=c/n;return 0===l?1:l}function a(e,t,n){var r=document.createElement("canvas");return s(e,r,t,n),r.toDataURL("image/jpeg",t.quality||.8)}function s(e,t,r,i){var a=e.naturalWidth,s=e.naturalHeight,u=r.width,l=r.height,f=t.getContext("2d");f.save(),c(t,f,u,l,r.orientation);var d=n(e);d&&(a/=2,s/=2);var h=1024,g=document.createElement("canvas");g.width=g.height=h;var p=g.getContext("2d"),m=i?o(e,a,s):1,w=Math.ceil(h*u/a),v=Math.ceil(h*l/s/m),y=0,b=0;while(y<s){var S=0,I=0;while(S<a)p.clearRect(0,0,h,h),p.drawImage(e,-S,-y),f.drawImage(g,0,0,h,h,I,b,w,v),S+=h,I+=w;y+=h,b+=v}f.restore(),g=p=null}function c(e,t,n,r,i){switch(i){case 5:case 6:case 7:case 8:e.width=r,e.height=n;break;default:e.width=n,e.height=r}switch(i){case 2:t.translate(n,0),t.scale(-1,1);break;case 3:t.translate(n,r),t.rotate(Math.PI);break;case 4:t.translate(0,r),t.scale(1,-1);break;case 5:t.rotate(.5*Math.PI),t.scale(1,-1);break;case 6:t.rotate(.5*Math.PI),t.translate(0,-r);break;case 7:t.rotate(.5*Math.PI),t.translate(n,-r),t.scale(-1,1);break;case 8:t.rotate(-.5*Math.PI),t.translate(-n,0);break;default:break}}function u(e){if(window.Blob&&e instanceof Blob){var t=new Image,n=window.URL&&window.URL.createObjectURL?window.URL:window.webkitURL&&window.webkitURL.createObjectURL?window.webkitURL:null;if(!n)throw Error("No createObjectURL function found to create blob url");t.src=n.createObjectURL(e),this.blob=e,e=t}if(!e.naturalWidth&&!e.naturalHeight){var r=this;e.onload=function(){var e=r.imageLoadListeners;if(e){r.imageLoadListeners=null;for(var t=0,n=e.length;t<n;t++)e[t]()}},this.imageLoadListeners=[]}this.srcImage=e}u.prototype.render=function(e,t,n){if(this.imageLoadListeners){var r=this;this.imageLoadListeners.push((function(){r.render(e,t,n)}))}else{t=t||{};var i=this.srcImage,o=i.src,c=o.length,u=i.naturalWidth,l=i.naturalHeight,f=t.width,d=t.height,h=t.maxWidth,g=t.maxHeight,p=this.blob&&"image/jpeg"===this.blob.type||0===o.indexOf("data:image/jpeg")||o.indexOf(".jpg")===c-4||o.indexOf(".jpeg")===c-5;f&&!d?d=l*f/u<<0:d&&!f?f=u*d/l<<0:(f=u,d=l),h&&f>h&&(f=h,d=l*f/u<<0),g&&d>g&&(d=g,f=u*d/l<<0);var m={width:f,height:d};for(var w in t)m[w]=t[w];var v=e.tagName.toLowerCase();"img"===v?e.src=a(this.srcImage,m,p):"canvas"===v&&s(this.srcImage,e,m,p),"function"===typeof this.onrender&&this.onrender(e),n&&n()}},r=[],i=function(){return u}.apply(t,r),void 0===i||(e.exports=i)})()},function(e,t){function n(e){Math.round;var t,n,r,i,o,a=Math.floor,s=new Array(64),c=new Array(64),u=new Array(64),l=new Array(64),f=new Array(65535),d=new Array(65535),h=new Array(64),g=new Array(64),p=[],m=0,w=7,v=new Array(64),y=new Array(64),b=new Array(64),S=new Array(256),I=new Array(2048),P=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],F=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],T=[0,1,2,3,4,5,6,7,8,9,10,11],D=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],C=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],x=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],U=[0,1,2,3,4,5,6,7,8,9,10,11],L=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],A=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function R(e){for(var t=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],n=0;n<64;n++){var r=a((t[n]*e+50)/100);r<1?r=1:r>255&&(r=255),s[P[n]]=r}for(var i=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],o=0;o<64;o++){var f=a((i[o]*e+50)/100);f<1?f=1:f>255&&(f=255),c[P[o]]=f}for(var d=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],h=0,g=0;g<8;g++)for(var p=0;p<8;p++)u[h]=1/(s[P[h]]*d[g]*d[p]*8),l[h]=1/(c[P[h]]*d[g]*d[p]*8),h++}function M(e,t){for(var n=0,r=0,i=new Array,o=1;o<=16;o++){for(var a=1;a<=e[o];a++)i[t[r]]=[],i[t[r]][0]=n,i[t[r]][1]=o,r++,n++;n*=2}return i}function O(){t=M(F,T),n=M(x,U),r=M(D,C),i=M(L,A)}function k(){for(var e=1,t=2,n=1;n<=15;n++){for(var r=e;r<t;r++)d[32767+r]=n,f[32767+r]=[],f[32767+r][1]=n,f[32767+r][0]=r;for(var i=-(t-1);i<=-e;i++)d[32767+i]=n,f[32767+i]=[],f[32767+i][1]=n,f[32767+i][0]=t-1+i;e<<=1,t<<=1}}function E(){for(var e=0;e<256;e++)I[e]=19595*e,I[e+256>>0]=38470*e,I[e+512>>0]=7471*e+32768,I[e+768>>0]=-11059*e,I[e+1024>>0]=-21709*e,I[e+1280>>0]=32768*e+8421375,I[e+1536>>0]=-27439*e,I[e+1792>>0]=-5329*e}function G(e){var t=e[0],n=e[1]-1;while(n>=0)t&1<<n&&(m|=1<<w),n--,w--,w<0&&(255==m?(B(255),B(0)):B(m),w=7,m=0)}function B(e){p.push(S[e])}function _(e){B(e>>8&255),B(255&e)}function j(e,t){var n,r,i,o,a,s,c,u,l,f=0;const d=8,g=64;for(l=0;l<d;++l){n=e[f],r=e[f+1],i=e[f+2],o=e[f+3],a=e[f+4],s=e[f+5],c=e[f+6],u=e[f+7];var p=n+u,m=n-u,w=r+c,v=r-c,y=i+s,b=i-s,S=o+a,I=o-a,P=p+S,F=p-S,T=w+y,D=w-y;e[f]=P+T,e[f+4]=P-T;var C=.707106781*(D+F);e[f+2]=F+C,e[f+6]=F-C,P=I+b,T=b+v,D=v+m;var x=.382683433*(P-D),U=.5411961*P+x,L=1.306562965*D+x,A=.707106781*T,R=m+A,M=m-A;e[f+5]=M+U,e[f+3]=M-U,e[f+1]=R+L,e[f+7]=R-L,f+=8}for(f=0,l=0;l<d;++l){n=e[f],r=e[f+8],i=e[f+16],o=e[f+24],a=e[f+32],s=e[f+40],c=e[f+48],u=e[f+56];var O=n+u,k=n-u,E=r+c,G=r-c,B=i+s,_=i-s,j=o+a,N=o-a,W=O+j,H=O-j,z=E+B,q=E-B;e[f]=W+z,e[f+32]=W-z;var V=.707106781*(q+H);e[f+16]=H+V,e[f+48]=H-V,W=N+_,z=_+G,q=G+k;var X=.382683433*(W-q),Q=.5411961*W+X,J=1.306562965*q+X,Y=.707106781*z,K=k+Y,$=k-Y;e[f+40]=$+Q,e[f+24]=$-Q,e[f+8]=K+J,e[f+56]=K-J,f++}var Z;for(l=0;l<g;++l)Z=e[l]*t[l],h[l]=Z>0?Z+.5|0:Z-.5|0;return h}function N(){_(65504),_(16),B(74),B(70),B(73),B(70),B(0),B(1),B(1),B(0),_(1),_(1),B(0),B(0)}function W(e,t){_(65472),_(17),B(8),_(t),_(e),B(3),B(1),B(17),B(0),B(2),B(17),B(1),B(3),B(17),B(1)}function H(){_(65499),_(132),B(0);for(var e=0;e<64;e++)B(s[e]);B(1);for(var t=0;t<64;t++)B(c[t])}function z(){_(65476),_(418),B(0);for(var e=0;e<16;e++)B(F[e+1]);for(var t=0;t<=11;t++)B(T[t]);B(16);for(var n=0;n<16;n++)B(D[n+1]);for(var r=0;r<=161;r++)B(C[r]);B(1);for(var i=0;i<16;i++)B(x[i+1]);for(var o=0;o<=11;o++)B(U[o]);B(17);for(var a=0;a<16;a++)B(L[a+1]);for(var s=0;s<=161;s++)B(A[s])}function q(){_(65498),_(12),B(3),B(1),B(0),B(2),B(17),B(3),B(17),B(0),B(63),B(0)}function V(e,t,n,r,i){var o,a=i[0],s=i[240];const c=16,u=63,l=64;for(var h=j(e,t),p=0;p<l;++p)g[P[p]]=h[p];var m=g[0]-n;n=g[0],0==m?G(r[0]):(o=32767+m,G(r[d[o]]),G(f[o]));for(var w=63;w>0&&0==g[w];w--);if(0==w)return G(a),n;var v,y=1;while(y<=w){for(var b=y;0==g[y]&&y<=w;++y);var S=y-b;if(S>=c){v=S>>4;for(var I=1;I<=v;++I)G(s);S&=15}o=32767+g[y],G(i[(S<<4)+d[o]]),G(f[o]),y++}return w!=u&&G(a),n}function X(){for(var e=String.fromCharCode,t=0;t<256;t++)S[t]=e(t)}function Q(e){if(e<=0&&(e=1),e>100&&(e=100),o!=e){var t=0;t=e<50?Math.floor(5e3/e):Math.floor(200-2*e),R(t),o=e}}function J(){var t=(new Date).getTime();e||(e=50),X(),O(),k(),E(),Q(e);(new Date).getTime()}this.encode=function(e,o,a){var s=(new Date).getTime();o&&Q(o),p=new Array,m=0,w=7,_(65496),N(),H(),W(e.width,e.height),z(),q();var c=0,f=0,d=0;m=0,w=7,this.encode.displayName="_encode_";var h,g,S,P,F,T,D,C,x,U=e.data,L=e.width,A=e.height,R=4*L,M=0;while(M<A){h=0;while(h<R){for(F=R*M+h,T=F,D=-1,C=0,x=0;x<64;x++)C=x>>3,D=4*(7&x),T=F+C*R+D,M+C>=A&&(T-=R*(M+1+C-A)),h+D>=R&&(T-=h+D-R+4),g=U[T++],S=U[T++],P=U[T++],v[x]=(I[g]+I[S+256>>0]+I[P+512>>0]>>16)-128,y[x]=(I[g+768>>0]+I[S+1024>>0]+I[P+1280>>0]>>16)-128,b[x]=(I[g+1280>>0]+I[S+1536>>0]+I[P+1792>>0]>>16)-128;c=V(v,u,c,t,r),f=V(y,l,f,n,i),d=V(b,l,d,n,i),h+=32}M+=8}if(w>=0){var O=[];O[1]=w+1,O[0]=(1<<w+1)-1,G(O)}if(_(65497),a){for(var k=p.length,E=new Uint8Array(k),B=0;B<k;B++)E[B]=p[B].charCodeAt();p=[];(new Date).getTime();return E}var j="data:image/jpeg;base64,"+btoa(p.join(""));p=[];(new Date).getTime();return j},J()}e.exports=n},function(e,t,n){n.p=c("lrz")+"/",window.URL=window.URL||window.webkitURL;var r=n(1),i=n(5),o=n(6),a=function(e){var t=/OS (.*) like Mac OS X/g.exec(e),n=/Android (\d.*?);/g.exec(e)||/Android\/(\d.*?) /g.exec(e),r=t?+t.pop().replace(/-/g,"."):0;return{oldIOS:!!t&&r<8,newIOS:!!t&&r>=13,oldAndroid:!!n&&+n.pop().substr(0,3)<4.5,iOS:/\(i[^;]+;( U;)? CPU.+Mac OS X/.test(e),android:/Android/g.test(e),mQQBrowser:/MQQBrowser/g.test(e)}}(navigator.userAgent);function s(e,t){var n=this;if(!e)throw new Error("没有收到图片，可能的解决方案：https://github.com/think2011/localResizeIMG/issues/7");for(var r in t=t||{},n.defaults={width:null,height:null,fieldName:"file",ingnoreOrientation:!a.iOS||a.newIOS,quality:.7},n.file=e,t)t.hasOwnProperty(r)&&(n.defaults[r]=t[r]);return this.init()}function c(e){var t=null;return t=e?[].filter.call(document.scripts,(function(t){return-1!==t.src.indexOf(e)}))[0]:document.scripts[document.scripts.length-1],t?t.src.substr(0,t.src.lastIndexOf("/")):null}function u(e){var t;t=e.split(",")[0].indexOf("base64")>=0?atob(e.split(",")[1]):unescape(e.split(",")[1]);for(var n=e.split(",")[0].split(":")[1].split(";")[0],r=new Uint8Array(t.length),o=0;o<t.length;o++)r[o]=t.charCodeAt(o);return new i.Blob([r.buffer],{type:n})}s.prototype.init=function(){var e=this,t=e.file,n="string"===typeof t,o=/^data:/.test(t),a=new Image,s=document.createElement("canvas"),c=n?t:URL.createObjectURL(t);if(e.img=a,e.blob=c,e.canvas=s,e.fileName=n?o?"base64.jpg":t.split("/").pop():t.name,!document.createElement("canvas").getContext)throw new Error("浏览器不支持canvas");return new r((function(n,r){a.onerror=function(){var e=new Error("加载图片文件失败");throw r(e),e},a.onload=function(){e._getBase64().then((function(e){if(e.length<10){var t=new Error("生成base64失败");throw r(t),t}return e})).then((function(r){var o=null;for(var a in"object"===typeof e.file&&r.length>e.file.size?(o=new FormData,t=e.file):(o=new i.FormData,t=u(r)),o.append(e.defaults.fieldName,t,e.fileName.replace(/\..+/g,".jpg")),n({formData:o,fileLen:+t.size,base64:r,base64Len:r.length,origin:e.file,file:t}),e)e.hasOwnProperty(a)&&(e[a]=null);URL.revokeObjectURL(e.blob)}))},!o&&(a.crossOrigin="*"),a.src=c}))},s.prototype._getBase64=function(){var e=this,t=e.img,n=e.file,i=e.canvas;return new r((function(r){try{o.getData("object"===typeof n?n:t,(function(){e.orientation=e.defaults.ingnoreOrientation?0:o.getTag(this,"Orientation"),e.resize=e._getResize(),e.ctx=i.getContext("2d"),i.width=e.resize.width,i.height=e.resize.height,e.ctx.fillStyle="#fff",e.ctx.fillRect(0,0,i.width,i.height),a.oldIOS?e._createBase64ForOldIOS().then(r):e._createBase64().then(r)}))}catch(s){throw new Error(s)}}))},s.prototype._createBase64ForOldIOS=function(){var e=this,t=e.img,i=e.canvas,o=e.defaults,a=e.orientation;return new r((function(e){!function(){var r=[n(7)];(function(n){var r=new n(t);"5678".indexOf(a)>-1?r.render(i,{width:i.height,height:i.width,orientation:a}):r.render(i,{width:i.width,height:i.height,orientation:a}),e(i.toDataURL("image/jpeg",o.quality))}).apply(null,r)}()}))},s.prototype._createBase64=function(){var e=this,t=e.resize,i=e.img,o=e.canvas,s=e.ctx,c=e.defaults,u=e.orientation;switch(u){case 3:s.rotate(180*Math.PI/180),s.drawImage(i,-t.width,-t.height,t.width,t.height);break;case 6:s.rotate(90*Math.PI/180),s.drawImage(i,0,-t.width,t.height,t.width);break;case 8:s.rotate(270*Math.PI/180),s.drawImage(i,-t.height,0,t.height,t.width);break;case 2:s.translate(t.width,0),s.scale(-1,1),s.drawImage(i,0,0,t.width,t.height);break;case 4:s.translate(t.width,0),s.scale(-1,1),s.rotate(180*Math.PI/180),s.drawImage(i,-t.width,-t.height,t.width,t.height);break;case 5:s.translate(t.width,0),s.scale(-1,1),s.rotate(90*Math.PI/180),s.drawImage(i,0,-t.width,t.height,t.width);break;case 7:s.translate(t.width,0),s.scale(-1,1),s.rotate(270*Math.PI/180),s.drawImage(i,-t.height,0,t.height,t.width);break;default:s.drawImage(i,0,0,t.width,t.height)}return new r((function(e){a.oldAndroid||a.mQQBrowser||!navigator.userAgent?function(){var t=[n(8)];(function(t){var n=new t,r=s.getImageData(0,0,o.width,o.height);e(n.encode(r,100*c.quality))}).apply(null,t)}():e(o.toDataURL("image/jpeg",c.quality))}))},s.prototype._getResize=function(){var e=this,t=e.img,n=e.defaults,r=n.width,i=n.height,o=e.orientation,a={width:t.width,height:t.height};if("5678".indexOf(o)>-1&&(a.width=t.height,a.height=t.width),a.width<r||a.height<i)return a;var s=a.width/a.height;r&&i?s>=r/i?a.width>r&&(a.width=r,a.height=Math.ceil(r/s)):a.height>i&&(a.height=i,a.width=Math.ceil(i*s)):r?r<a.width&&(a.width=r,a.height=Math.ceil(r/s)):i&&i<a.height&&(a.width=Math.ceil(i*s),a.height=i);while(a.width>=3264||a.height>=2448)a.width*=.8,a.height*=.8;return a},window.lrz=function(e,t){return new s(e,t)},window.lrz.version="__packageJSON.version__",e.exports=window.lrz}])}))}}]);