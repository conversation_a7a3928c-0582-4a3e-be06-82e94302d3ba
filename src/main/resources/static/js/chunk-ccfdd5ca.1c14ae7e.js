(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ccfdd5ca"],{"0291":function(t,e,a){},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},1571:function(t,e,a){"use strict";a("6ed7")},1633:function(t,e,a){},"19cd":function(t,e,a){"use strict";a("47f0")},2799:function(t,e,a){},"47f0":function(t,e,a){},4867:function(t,e,a){"use strict";a("0291")},"4c0c":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selStore",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?a("el-table-column",{attrs:{fixed:"left",type:"selection",width:"55"}}):a("el-table-column",{attrs:{label:"",width:"40",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index,size:"small"},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" ")+" ")])]}}])}),a("el-table-column",{attrs:{label:"仓库编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storecode))])]}}])}),a("el-table-column",{attrs:{label:"仓库名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storename))])]}}])}),a("el-table-column",{attrs:{label:"仓库地址",align:"center","min-width":"150","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storeadd))])]}}])}),a("el-table-column",{attrs:{label:"仓管员",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.operator))])]}}])}),a("el-table-column",{attrs:{label:"电话",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storetel))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},o=[],n=(a("e9c4"),a("b775")),r=a("333d"),s={components:{Pagination:r["a"]},props:["multi","storeid"],data:function(){return{title:"选择仓库",listLoading:!0,lst:[],strfilter:"",radio:"",selrows:"",total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},watch:{},mounted:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.$emit("singleSel",t),this.selrows=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,this.storeid&&(this.queryParams.SearchPojo={storeid:this.storeid}),n["a"].post("/D04M21S1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={storename:t,storecode:t,operator:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},l=s,c=(a("6dd7"),a("2877")),d=Object(c["a"])(l,i,o,!1,null,"655e468e",null);e["a"]=d.exports},"5a30":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.FormVisible,expression:"!FormVisible"}],ref:"index",staticClass:"index"},[a("listheader",{directives:[{name:"show",rawName:"v-show",value:!t.isDialog,expression:"!isDialog"}],attrs:{tableForm:t.tableForm,total:t.total},on:{btnAdd:function(e){t.openVisible=!0},btnSearch:t.search,bindData:t.checkBindData,changeModelUrl:t.changeModelUrl,btnExport:t.btnExport,advancedSearch:t.advancedSearch,carryForward:function(e){t.carryForwardVisible=!0},deleteForm:t.deleteForm,btnHelp:t.btnHelp}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:5}},[a("tableTh",{ref:"tableTh",attrs:{searchVal:t.searchVal,isDialog:t.isDialog},on:{changeIdx:t.changeIdx,getTotal:t.getTotal,checkRowItem:t.checkRowItem}})],1),a("el-col",{attrs:{span:t.showhelp?15:19}},[a("checkoutTable",{ref:"checkoutTable",attrs:{row:t.row},on:{showForm:t.showForm,sendTableForm:t.sendTableForm}})],1),a("el-col",{attrs:{span:t.showhelp?4:0}},[a("HelpModel",{ref:"helpmodel",attrs:{code:"D04M05B1"}})],1)],1)],1)],1),t.openVisible?a("el-dialog",{attrs:{title:"开账",width:"400px","append-to-body":!0,"close-on-click-modal":!1,visible:t.openVisible},on:{"update:visible":function(e){t.openVisible=e}}},[a("div",[a("div",{staticClass:"block",staticStyle:{"margin-bottom":"10px"}},[a("span",{staticClass:"formdataTitle"},[t._v("年份:")]),a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"year",placeholder:"选择年份"},model:{value:t.formdata.year,callback:function(e){t.$set(t.formdata,"year",e)},expression:"formdata.year"}})],1),a("div",{staticClass:"block"},[a("span",{staticClass:"formdataTitle"},[t._v("月份:")]),a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"popper-class":"monthPickerStyle",type:"month",placeholder:"选择月份",format:"MM"},model:{value:t.formdata.month,callback:function(e){t.$set(t.formdata,"month",e)},expression:"formdata.month"}})],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitOpen()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.openVisible=!1}}},[t._v("取 消")])],1)]):t._e(),t.carryForwardVisible?a("el-dialog",{attrs:{title:"结转",width:"400px","append-to-body":!0,"close-on-click-modal":!1,visible:t.carryForwardVisible},on:{"update:visible":function(e){t.carryForwardVisible=e}}},[a("div",[a("div",{staticClass:"block",staticStyle:{"margin-bottom":"10px"}},[a("span",{staticClass:"formdataTitle"},[t._v("年份:")]),a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"year",placeholder:"选择年份"},model:{value:t.formdata.year,callback:function(e){t.$set(t.formdata,"year",e)},expression:"formdata.year"}})],1),a("div",{staticClass:"block"},[a("span",{staticClass:"formdataTitle"},[t._v("月份:")]),a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"popper-class":"monthPickerStyle",type:"month",placeholder:"选择月份",format:"MM"},model:{value:t.formdata.month,callback:function(e){t.$set(t.formdata,"month",e)},expression:"formdata.month"}})],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitCarryForward()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.carryForwardVisible=!1}}},[t._v("取 消")])],1)]):t._e()],1)},o=[],n=(a("ac1f"),a("841c"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[0==t.total?a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 开账 ")]):a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-position",plain:"",size:"mini"},on:{click:function(e){return t.$emit("carryForward")}}},[t._v(" 结转 ")]),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-edit-outline",plain:"",size:"mini"},on:{click:function(e){return t.$router.push("/D04/M06R1DETA")}}},[t._v(" 本期账单 ")]),0!=t.total?a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete",plain:"",size:"mini"},on:{click:function(e){return t.$emit("deleteForm")}}},[t._v(" 反结账 ")]):t._e()],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-help",title:"帮助"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"SaDgFormat"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)}),r=[],s={name:"Listheader",props:["tableForm","total"],components:{},data:function(){return{strfilter:"",iShow:!1,formdata:{},thorList:!0,setColumsVisible:!1,code:"D04M05B1List"}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(){this.iShow=!1,this.$emit("advancedSearch",this.formdata)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList)}}},l=s,c=(a("19cd"),a("2877")),d=Object(c["a"])(l,n,r,!1,null,"013a3df4",null),m=d.exports,u=a("b775"),f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight},on:{"selection-change":t.handleSelectionChange,"sort-change":t.changeSort,"row-click":t.checkRowItem}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"ID",width:"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["startdate"==e.itemcode||"enddate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))]):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,small:"","pager-count":5,page:t.queryParams.PageNum,limit:t.queryParams.PageSize,layout:"total,prev,pager,next"},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}}),t.checkOutVisible?a("el-dialog",{attrs:{title:"结账信息",width:"70vw",visible:t.checkOutVisible,"append-to-body":!0},on:{"update:visible":function(e){t.checkOutVisible=e}}},[a("checkoutTable",{ref:"checkoutTable",attrs:{row:t.row}})],1):t._e()],1)},h=[],p=(a("d81d"),a("e9c4"),a("333d"));const b={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);u["a"].post("/D04M05B1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);u["a"].post("/D04M05B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{u["a"].get("/D04M05B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var g=b,y=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{"min-height":"380px"},attrs:{"summary-method":t.getSummaries,"show-summary":"",data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",height:t.tableMaxHeight,"header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["startdate"==e.itemcode||"enddate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))]):"storecode"==e.itemcode?a("div",[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.$emit("showForm",i.row)}}},[t._v(t._s(i.row.storecode?i.row.storecode:"仓库编码"))])],1):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},w=[],v=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{disabled:!t.formdata.id,type:"primary",size:"small"},nativeOn:{click:function(e){return t.printButton()}}},[t._v("打 印")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.$emit("closeForm")}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("div",{staticClass:"flex j-end p-a",staticStyle:{top:"5px",right:"0"}},[a("div",{staticClass:"refNo flex j-end"},[a("div",{staticClass:"refNo-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),a("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini"},model:{value:t.formdata.billdate,callback:function(e){t.$set(t.formdata,"billdate",e)},expression:"formdata.billdate"}})],1),a("div",[a("span",[t._v("NO：")]),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini",readonly:""},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)]),a("div",{staticStyle:{display:"flex","justify-content":"flex-end"}},[a("div",{staticStyle:{"margin-right":"10px",width:"100px"}},[t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount?a("svg-icon",{staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"finish1"}}):t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount?a("svg-icon",{staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"revoke"}}):t._e()],1),a("div",{staticStyle:{"margin-right":"10px",width:"90px"}},[a("svg-icon",{directives:[{name:"show",rawName:"v-show",value:!!t.formdata.assessor,expression:"!!formdata.assessor"}],staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"approve"}})],1)])])]),a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:6}},[a("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[a("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[a("el-input",{attrs:{placeholder:"单据类型",readonly:"",size:"small"},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}})],1)],1)]),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"单据标题"}},[a("el-input",{attrs:{placeholder:"单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("div",{on:{click:function(e){return t.cleValidate("storeid")}}},[a("el-form-item",{attrs:{label:"仓库信息",prop:"storeid"}},[a("el-input",{attrs:{placeholder:"请输入仓库信息",size:"small",readonly:""},model:{value:t.formdata.storename,callback:function(e){t.$set(t.formdata,"storename",e)},expression:"formdata.storename"}})],1)],1)]),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"结转年份"}},[a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("el-input-number",{attrs:{"controls-position":"right",min:2020,size:"small"},model:{value:t.formdata.carryyear,callback:function(e){t.$set(t.formdata,"carryyear",e)},expression:"formdata.carryyear"}}),a("span",{staticStyle:{margin:"0 5px"}},[t._v(" 年 ")]),a("el-input-number",{attrs:{"controls-position":"right",min:1,max:12,size:"small"},model:{value:t.formdata.carrymonth,callback:function(e){t.$set(t.formdata,"carrymonth",e)},expression:"formdata.carrymonth"}}),a("span",{staticStyle:{margin:"0 5px"}},[t._v(" 月 ")])],1)])],1)],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"开始时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.startdate,callback:function(e){t.$set(t.formdata,"startdate",e)},expression:"formdata.startdate"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"结束时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.enddate,callback:function(e){t.$set(t.formdata,"enddate",e)},expression:"formdata.enddate"}})],1)],1)],1)],1),a("el-divider")],1),a("div",{staticClass:"form-body form f-1"},[a("elitem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx}})],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:18}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.summary,callback:function(e){t.$set(t.formdata,"summary",e)},expression:"formdata.summary"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"经办人","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"经办人",clearable:"",size:"small"},model:{value:t.formdata.operator,callback:function(e){t.$set(t.formdata,"operator",e)},expression:"formdata.operator"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"审核"}},[a("span",{staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.assessor))])])],1),a("el-col",{directives:[{name:"show",rawName:"v-show",value:t.formdata.assessor,expression:"formdata.assessor"}],attrs:{span:4}},[a("el-form-item",{attrs:{label:"审核日期"}},[a("span",{staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.assessdate)))])])],1)],1)],1)],1)]),a("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible},on:{"update:visible":function(e){t.ReportVisible=e}}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticStyle:{float:"left"},attrs:{type:"primary",size:"small"},on:{click:t.submitRemoteReport}},[t._v("云打印")]),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("打印")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),a("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[a("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})])],1)},x=[],k=a("c7eb"),S=a("1da1"),$=(a("b64b"),a("d3b7"),a("3ca3"),a("ddb0"),a("2b3d"),a("9861"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[a("el-row",[a("el-button-group",[a("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveUp()}}},[a("i",{staticClass:"el-icon-top"}),t._v(" 上 移")]),a("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveDown()}}},[a("i",{staticClass:"el-icon-bottom"}),t._v(" 下 移")])],1)],1),a("div",{staticStyle:{"margin-right":"10px",position:"relative"}},[a("el-button",{staticStyle:{"font-weight":"bold"},attrs:{size:"mini",icon:"el-icon-refresh-right"},nativeOn:{click:function(e){return t.bindData()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:function(e){return t.btnExport()}}})],1)],1),a("div",{staticClass:"table-container f-1 table-position"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",attrs:{"summary-method":t.getSummaries,"show-summary":"",data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",size:"small",height:t.tableHeight,"header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),t._l(t.tableForm.item,(function(e){return[!e.displaymark?t._e():a("el-table-column",{key:e.id,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(" "+t._s(a.row[e.itemcode])+" ")]}}],null,!0)})]}))],2)],1),a("pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize,pageSizes:[10,50,100,500]},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}}),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("SetColums",{ref:"setcolums",attrs:{code:"D04M06B1Item",tableForm:t.tableForm,baseparam:"SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)}),_=[],P=(a("7db0"),a("c740"),a("13d5"),a("a434"),a("a9e3"),a("5319"),a("159b"),a("da92")),D={formcode:"D04M05B1Th",item:[{itemcode:"rownum",itemname:"序号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"carryyear",itemname:"年份",minwidth:"80",displaymark:1,overflow:1},{itemcode:"carrymonth",itemname:"月份",minwidth:"80",displaymark:1,overflow:1}]},F={formcode:"D04M05B1List",item:[{itemcode:"storecode",itemname:"仓库编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"storename",itemname:"仓库",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billopenamount",itemname:"期初金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billinamount",itemname:"入账金额",minwidth:"80",displaymark:1},{itemcode:"billoutamount",itemname:"出账金额",minwidth:"100",displaymark:1,overflow:1},{itemcode:"billcloseamount",itemname:"期末金额",minwidth:"80",displaymark:1},{itemcode:"itemcount",itemname:"记录数",minwidth:"80",displaymark:1,overflow:1},{itemcode:"startdate",itemname:"开始时间",minwidth:"80",displaymark:1},{itemcode:"enddate",itemname:"结束时间",minwidth:"80",displaymark:1,overflow:1}]},C={formcode:"D04M05B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"100",displaymark:1,overflow:1},{itemcode:"openqty",itemname:"期初数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"openamount",itemname:"期初金额",minwidth:"100",displaymark:1,overflow:1},{itemcode:"inqty",itemname:"入账数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"inamount",itemname:"入账金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"outqty",itemname:"出账数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"outamount",itemname:"出账金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"closeqty",itemname:"期末数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"closeamount",itemname:"期末金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"120",displaymark:1,overflow:1}]},L=a("48da"),T={name:"Elitem",components:{Pagination:p["a"]},props:["formdata","idx"],data:function(){return{title:"货品信息",formLabelWidth:"100px",listLoading:!1,PwProcessFormVisible:!1,lst:[],multi:0,billamount:0,selected:!1,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:C,customList:[],setColumsVisible:!1,queryParams:{PageNum:1,PageSize:50,OrderType:1,SearchType:1},total:0}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){this.lst=[]},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},mounted:function(){this.catchHight()},methods:{bindData:function(t){var e=this;u["a"].post("/D04M06B1/getItemPageList?key="+this.formdata.id,JSON.stringify(this.queryParams)).then((function(t){if(console.log("getItemPageList",t),200==t.data.code){e.lst=t.data.data.list,e.total=t.data.data.total;for(var a=0;a<e.lst.length;a++)for(var i=e.lst[a],o=i.attributejson?JSON.parse(i.attributejson):[],n=0;n<o.length;n++)e.$set(e.lst[a],o[n].key,o[n].value)}else e.$message.warning("获取结转表失败")})).catch((function(t){e.$message.error(t||"请求错误")}))},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},getColumn:function(){var t=this;return Object(S["a"])(Object(k["a"])().mark((function e(){return Object(k["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u["a"].get("/D91M01S2/getListByShow").then((function(e){console.log(e),200==e.data.code?t.customList=e.data.data?e.data.data:[]:t.$message.warning(e.data.msg||"获取失败")})).catch((function(e){t.$message.error("请求错误")}));case 2:return e.next=4,u["a"].get("/SaDgFormat/getBillEntityByCode?code=D04M06B1Item").then((function(e){if(200==e.data.code){if(null==e.data.data){t.tableForm=C;for(var a=0;a<t.customList.length;a++){var i=t.customList[a],o=t.tableForm.item.findIndex((function(e){return e.itemcode==t.customList[a].attrkey}));if(-1==o){var n={itemcode:i.attrkey,itemname:i.attrname,minwidth:"80",displaymark:i.listshow,overflow:1};t.tableForm.item.push(n)}}return}t.tableForm=e.data.data;for(a=0;a<t.customList.length;a++){i=t.customList[a],o=t.tableForm.item.findIndex((function(e){return e.itemcode==t.customList[a].attrkey}));if(-1==o){n={itemcode:i.attrkey,itemname:i.attrname,minwidth:"80",displaymark:i.listshow,overflow:1};t.tableForm.item.push(n)}}}})).catch((function(e){t.$message.error("请求出错")}));case 4:case"end":return e.stop()}}),e)})))()},openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate(),this.$forceUpdate()},setAttributeJson:function(t,e){for(var a=[],i=0;i<this.customList.length;i++){var o=this.customList[i],n={key:o.attrkey,value:t[o.attrkey]?t[o.attrkey].replace(/\s*/g,""):""};""!=n.value&&a.push(n)}0==a.length?this.lst[e].attributejson="":this.lst[e].attributejson=JSON.stringify(a)},getSummaries:function(t){var e=this,a=t.columns,i=t.data,o=["openamount","inamount","outamount","closeamount"],n=[];return a.forEach((function(t,a){if(0!==a){var r=!1;o.length>0&&void 0!=o.find((function(e){return e==t.property}))&&(r=!0);var s=i.map((function(e){return Number(e[t.property])}));!s.every((function(t){return isNaN(t)}))&&r?n[a]=s.reduce((function(t,e){var a=Number(e);return isNaN(a)?Number(t):P["a"].plus(Number(t),Number(e))}),0):n[a]="","openamount"==t.property?e.formdata.billopenamount=n[a]:"inamount"==t.property?e.formdata.billinamount=n[a]:"outamount"==t.property?e.formdata.billoutamount=n[a]:"closeamount"==t.property&&(e.formdata.billcloseamount=n[a])}else n[a]="合计"})),n},handleSelectionChange:function(t){t.length>0?this.selected=!0:this.selected=!1;for(var e=0;e<this.lst.length;e++)this.lst[e].rownum=e;this.multipleSelection=t},btnExport:function(){var t=this;Promise.resolve().then(function(){for(var e=[],a=[],i=0;i<t.tableForm.item.length;i++){var o=t.tableForm.item[i];o.displaymark&&(e.push(o.itemname),a.push(o.itemcode))}var n=t.lst,r=t.formatJson(a,n);Object(L["a"])(e,r,"仓库结转明细")}.bind(null,a)).catch(a.oe)},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-64)}))},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}},V=T,M=(a("4867"),Object(c["a"])(V,$,_,!1,null,"6930f743",null)),O=M.exports,q=a("4c0c"),N={name:"Formedit",components:{elitem:O,selgroup:q["a"]},props:["idx"],data:function(){return{title:"仓库结转",formdata:{createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,billcloseamount:0,billdate:new Date,billinamount:0,billopenamount:0,billoutamount:0,billtitle:"",billtype:"仓库结转",carrymonth:(new Date).getMonth(),carryyear:(new Date).getFullYear(),enddate:"",itemcount:0,operator:"",printcount:0,refno:"",summary:"",startdate:"",storecode:"",storename:"",storeid:"",item:[]},formRules:{storeid:[{required:!0,trigger:"blur",message:"仓库信息为必填项"}]},multi:0,formLabelWidth:"100px",formheight:"500px",ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,selVisible4group:!1}},computed:{formcontainHeight:function(){return window.innerHeight-50-13-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},destroyed:function(){document.onkeydown=function(){},this.$setWs.wsClose()},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&u["a"].get("/D04M06B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.$nextTick((function(){t.$refs.elitem.bindData()}))):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error(e||"请求错误")}))},getAllBill:function(){var t=this;return Object(S["a"])(Object(k["a"])().mark((function e(){return Object(k["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$refs.formdata.validate((function(e){if(!e)return console.log("error submit!!"),!1;t.saveBill()}));case 1:case"end":return e.stop()}}),e)})))()},saveBill:function(){var t=this;return Object(S["a"])(Object(k["a"])().mark((function e(){return Object(k["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u["a"].post("/D04M06B1/createCarry",JSON.stringify(t.formdata)).then((function(e){200==e.data.code?(e.data.data.item=[],t.formdata=e.data.data,t.$emit("changeIdx",e.data.data.id),t.$message.success("保存成功"),t.$refs.elitem.bindData()):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}));case 2:case"end":return e.stop()}}),e)})))()},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.formdata.item=this.$refs.elitem.lst,0==this.idx?g.add(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.bindData()})).catch((function(e){t.$message.warning(e||"保存失败")})):g.update(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.bindData()})).catch((function(e){t.$message.warning(e||"保存失败")}))},closeForm:function(){this.$emit("closeForm")},approval:function(){var t=this;this.formdata.id?this.approvalRequest():this.$refs["formdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;0!=t.$refs.elitem.lst.length?(t.formdata.item=t.$refs.elitem.lst,g.add(t.formdata).then((function(e){t.formdata=e.data,t.$emit("changeIdx",t.formdata.id),t.approvalRequest()})).catch((function(e){t.$message.warning(e||"保存失败")}))):t.$message.warning("单据内容不能为空")}))},DeApproval:function(){var t=this;u["a"].get("/D04M06B1/approval?key="+this.formdata.id).then((function(e){200==e.data.code?(t.$message.success("反审核成功"),t.formdata=e.data.data):t.$message.warning(e.data.msg||"反审核失败")}))},approvalRequest:function(){var t=this;return Object(S["a"])(Object(k["a"])().mark((function e(){return Object(k["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u["a"].get("/D04M06B1/approval?key="+t.formdata.id).then((function(e){200==e.data.code?(t.$message.success("审核成功"),t.formdata=e.data.data):t.$message.warning(e.data.msg||"审核失败")}));case 2:case"end":return e.stop()}}),e)})))()},printButton:function(){var t=this;u["a"].get("/SaReports/getListByModuleCode?code=D04M06B1Edit").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0}))},submitReport:function(){var t=this;""!=this.reportModel?u["a"].get("/D04M06B1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.ReportVisible=!1;var a=[];a.push(e.data);var i=window.URL.createObjectURL(new Blob(a,{type:"application/pdf"}));t.pdfUrl=i,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},submitRemoteReport:function(){var t=this;""!=this.reportModel?u["a"].get("/D04M06B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel).then((function(e){200==e.data.code?null==e.data.data||""==e.data.data?t.$message.warning(e.data.msg||"打印失败"):"连接成功"!=t.$store.state.webSocketMsg.webSocketState?(t.$setWs.initWebSocket("ws://127.0.0.1:18800"),setTimeout((function(){t.remoteprinting(e.data.data)}),500)):setTimeout((function(){t.remoteprinting(e.data.data)}),100):t.$message.warning(e.data.msg||"打印失败")})):this.$message.warning("打印模板不能为空!")},fastKey:function(){var t=this;document.onkeydown=function(e){var a=e.keyCode;83==a&&e.ctrlKey?(e.preventDefault(),t.formdata.assessor?t.$message.warning("单据已审核，保存失败！"):t.submitForm("formdata")):27==a&&(e.preventDefault(),t.closeForm())}},remoteprinting:function(t){"连接成功"==this.$store.state.webSocketMsg.webSocketState?(this.$setWs.wsSend(t),this.ReportVisible=!1):this.$message.warning("连接打印服务器失败")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},handleFocus:function(){this.selVisible4group=!0},handleBlur:function(){this.selVisible4group=!1},selectGroup:function(t){this.formdata.storecode=t.storecode,this.formdata.storename=t.storename,this.formdata.storeid=t.id,this.selVisible4group=!1,this.$refs.formdata.clearValidate("storeid")}}},z=N,B=(a("d19c"),Object(c["a"])(z,v,x,!1,null,"3b5f02c6",null)),j=B.exports,R=a("b893"),I={components:{Pagination:p["a"],formedit:j},props:["row"],data:function(){return{title:"结账信息",listLoading:!1,lst:[],strfilter:"",total:0,idx:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},processVisible:!1,tableForm:F}},created:function(){},updated:function(){var t=this;this.$nextTick((function(){t.$refs.selectVal.doLayout()}))},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},methods:{getSummaries:function(t){return Object(R["f"])(t,["billopenamount","billinamount","billoutamount","billcloseamount"])},getColumn:function(){var t=this;u["a"].get("/SaDgFormat/getBillEntityByCode?code=D04M05B1List").then((function(e){if(200==e.data.code){if(null==e.data.data)return t.tableForm=F,void t.$emit("sendTableForm",t.tableForm);t.tableForm=e.data.data,t.$emit("sendTableForm",t.tableForm)}})).catch((function(e){t.$message.error(e||"请求出错")}))},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,u["a"].post("/D04M06B1/getPageThByMonth?year="+this.row.carryyear+"&month="+this.row.carrymonth,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error(e||"请求错误")}))},search:function(t){""!=t?this.queryParams.SearchPojo={groupuid:t,groupname:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},E=I,H=(a("1571"),Object(c["a"])(E,y,w,!1,null,"9fe1b730",null)),U=H.exports,J={components:{Pagination:p["a"],checkoutTable:U},data:function(){return{lst:[],searchstr:"",total:0,listLoading:!1,idx:0,row:{},queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:D,checkOutVisible:!1,selectList:[]}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},created:function(){this.searchstr=""},methods:{bindData:function(){var t=this;this.listLoading=!0,this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(R["d"])()[0],EndDate:Object(R["d"])()[1]}),u["a"].post("/D04M05B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total,t.$emit("getTotal",t.total)),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getColumn:function(){var t=this;u["a"].get("/SaDgFormat/getBillEntityByCode?code=D04M05B1Th").then((function(e){if(200==e.data.code){if(null==e.data.data)return t.tableForm=D,void t.$emit("sendTableForm",t.tableForm);t.tableForm=e.data.data,t.$emit("sendTableForm",t.tableForm)}})).catch((function(e){t.$message.error("请求出错")}))},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,groupname:t,billtitle:t,groupuid:t}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},openCheckout:function(t){this.checkOutVisible=!0,this.row=t},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(t,this.tableForm),this.bindData()},handleSelectionChange:function(t){console.log(t),this.selectList=t},deleteForm:function(){var t=this;if(1==this.selectList.length){var e=this.selectList[0];this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){g.delete(e.id).then((function(e){200==e.code&&(0==e.data?t.$message.warning("删除失败,"+e.msg+"中已使用"):(t.$message.success("删除成功"),t.bindData()))})).catch((function(e){t.$message.warning(e||"删除失败")}))})).catch((function(){}))}else this.$message.warning("请选择一行内容")},btnExport:function(){var t=this;Promise.resolve().then(function(){for(var e=[],a=[],i=0;i<t.tableForm.item.length;i++){var o=t.tableForm.item[i];o.displaymark&&o.displaymark&&(e.push(o.itemname),a.push(o.itemcode))}var n=t.lst,r=t.formatJson(a,n);Object(L["a"])(e,r,"仓库结账")}.bind(null,a)).catch(a.oe)},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))},checkRowItem:function(t){this.$emit("checkRowItem",t)}}},A=J,W=(a("8cbb"),Object(c["a"])(A,f,h,!1,null,"7063c33e",null)),G=W.exports,K={name:"D01M03B1",components:{listheader:m,tableTh:G,checkoutTable:U,formedit:j},props:["searchVal","isDialog"],data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",thorList:!0,tableForm:{},showhelp:!1,formdata:{month:new Date,year:new Date},openVisible:!1,carryForwardVisible:!1,row:{}}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){this.searchstr="",this.bindData()},methods:{checkRowItem:function(t){var e=this;this.row=t,this.$nextTick((function(){e.$refs.checkoutTable.bindData(),e.$refs.checkoutTable.getColumn()}))},bindData:function(){var t=this;console.log(this.formdata),this.thorList&&this.$nextTick((function(){t.$refs.tableTh.bindData()}))},checkBindData:function(){var t=this;this.row.id?this.$nextTick((function(){t.$refs.checkoutTable.bindData(),t.$refs.checkoutTable.getColumn()})):this.$refs.checkoutTable.lst=[]},sendTableForm:function(t){this.tableForm=t},getTotal:function(t){this.total=t},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},changeModelUrl:function(t){this.thorList=!!t,this.bindData()},btnExport:function(){var t=this;this.$nextTick((function(){t.$refs.tableTh.btnExport()}))},deleteForm:function(){var t=this;this.$nextTick((function(){t.$refs.tableTh.deleteForm()}))},search:function(t){this.$refs.tableTh.search(t)},advancedSearch:function(t){this.$refs.tableTh.advancedSearch(t)},submitOpen:function(){var t=this,e={month:new Date(this.formdata.month).getMonth()+1,year:new Date(this.formdata.year).getFullYear()};u["a"].get("/D04M05B1/open?month="+e.month+"&year="+e.year).then((function(e){200==e.data.code?(t.bindData(),t.openVisible=!1):t.$message.warning(e.data.msg||"开账失败")}))},submitCarryForward:function(){var t=this,e={month:new Date(this.formdata.month).getMonth()+1,year:new Date(this.formdata.year).getFullYear()};u["a"].get("/D04M05B1/batchCreate?month="+e.month+"&year="+e.year).then((function(e){200==e.data.code?(t.bindData(),t.carryForwardVisible=!1):t.$message.warning(e.data.msg||"结转失败")}))},closeForm:function(){this.FormVisible=!1},showForm:function(t){this.FormVisible=!0,this.idx=t.id},changeIdx:function(t){this.idx=t}}},Y=K,Q=(a("7830"),Object(c["a"])(Y,i,o,!1,null,"0be64f05",null));e["default"]=Q.exports},"5cc6":function(t,e,a){var i=a("74e8");i("Uint8",(function(t){return function(e,a,i){return t(this,e,a,i)}}))},"5f1f":function(t,e,a){},"61ca":function(t,e,a){},"6dd7":function(t,e,a){"use strict";a("5f1f")},"6ed7":function(t,e,a){},7830:function(t,e,a){"use strict";a("1633")},"841c":function(t,e,a){"use strict";var i=a("d784"),o=a("825a"),n=a("1d80"),r=a("129f"),s=a("14c3");i("search",1,(function(t,e,a){return[function(e){var a=n(this),i=void 0==e?void 0:e[t];return void 0!==i?i.call(e,a):new RegExp(e)[t](String(a))},function(t){var i=a(e,t,this);if(i.done)return i.value;var n=o(t),l=String(this),c=n.lastIndex;r(c,0)||(n.lastIndex=0);var d=s(n,l);return r(n.lastIndex,c)||(n.lastIndex=c),null===d?-1:d.index}]}))},"8cbb":function(t,e,a){"use strict";a("61ca")},bf19:function(t,e,a){"use strict";var i=a("23e7");i({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return URL.prototype.toString.call(this)}})},c19f:function(t,e,a){"use strict";var i=a("23e7"),o=a("da84"),n=a("621a"),r=a("2626"),s="ArrayBuffer",l=n[s],c=o[s];i({global:!0,forced:c!==l},{ArrayBuffer:l}),r(s)},d19c:function(t,e,a){"use strict";a("2799")}}]);