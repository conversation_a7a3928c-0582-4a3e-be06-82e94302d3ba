(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7876710e"],{"09bf":function(e,t,a){"use strict";a("f862")},"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"3ae5":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{ref:"index",staticClass:"index"},[a("ListHeader",{attrs:{tableForm:e.tableForm},on:{btnAdd:function(t){return e.showform(0)},btnsearch:e.search,bindData:e.bindData,advancedSearch:e.advancedSearch,btnHelp:e.btn<PERSON>elp}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{directives:[{name:"show",rawName:"v-show",value:e.treeVisble,expression:"treeVisble"}],attrs:{span:3}},[a("div",{staticStyle:{"font-size":"14px",color:"#606266"}},[a("div",{staticClass:"groupTitle"},[a("span",[e._v(e._s(e.treeTitle))]),a("i",{staticClass:"el-icon-s-tools",style:{color:e.treeEditable?"#1e80ff":""},on:{click:function(t){return e.treeEdit()}}})]),a("el-tree",{staticStyle:{"font-size":"14px",height:"80vh",overflow:"auto",width:"100%"},attrs:{data:e.groupData,"node-key":"id","default-expand-all":""},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.node,o=t.data;return a("span",{staticClass:"custom-tree-node"},[a("span",{on:{click:function(){return e.handleNodeClick(o)}}},[e._v(e._s(i.label)+" ")]),a("span",{directives:[{name:"show",rawName:"v-show",value:e.treeEditable,expression:"treeEditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==o.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-edit"},on:{click:function(){return e.editTreeNode(o)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:e.treeEditable,expression:"treeEditable"}]},[a("el-button",{staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-circle-plus-outline"},on:{click:function(){return e.addTreeChild(o)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:e.treeEditable,expression:"treeEditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==o.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-delete-solid"},on:{click:function(){return e.delTreeNode(o)}}})],1)])}}])})],1)]),a("el-col",{attrs:{span:e.treeVisble?e.showHelp?17:21:e.showHelp?20:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{overflow:"auto"},attrs:{data:e.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:e.tableMaxHeight},on:{"sort-change":e.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"40"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),e._l(e.tableForm.item,(function(t,i){return[!t.displaymark?e._e():a("el-table-column",{key:i,attrs:{prop:t.itemcode,columnKey:t.itemcode,label:t.itemname,align:t.aligntype?t.aligntype:"center","min-width":t.minwidth,"show-overflow-tooltip":!!t.overflow,sortable:!!t.sortable},scopedSlots:e._u([{key:"default",fn:function(i){return["modulecode"==t.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showform(i.row.id)}}},[e._v(" "+e._s(i.row[t.itemcode]?i.row[t.itemcode]:"模块编码"))]):"createdate"==t.itemcode?a("span",[e._v(e._s(e._f("dateFormat")(i.row[t.itemcode])))]):"fieldtype"==t.itemcode?a("div",[0==i.row[t.itemcode]?a("span",[e._v("文本")]):1==i.row[t.itemcode]?a("span",[e._v("数字")]):2==i.row[t.itemcode]?a("span",[e._v("日期")]):3==i.row[t.itemcode]?a("span",[e._v("布尔")]):a("span")]):"searchmark"==t.itemcode?a("div",[1==i.row[t.itemcode]?a("span",{staticClass:"textborder-blue"},[e._v("是")]):e._e()]):a("span",[e._v(e._s(i.row[t.itemcode]))])]}}],null,!0)})]}))],2),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.getList}})],1),a("el-col",{attrs:{span:e.showHelp?4:0}},[a("HelpModel",{ref:"helpmodel",attrs:{code:"SaScene"}})],1)],1)],1)],1),e.FormVisible?a("el-dialog",{attrs:{title:"场景字段","append-to-body":!0,visible:e.FormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.FormVisible=t}}},[a("FormEdit",e._g({ref:"formedit",attrs:{idx:e.idx}},{formcomp:e.formcomp,closeFrom:e.closeFrom,changeidx:e.changeidx,bindData:e.bindData}))],1):e._e(),e.gropuFormVisible?a("el-dialog",{attrs:{title:"场景字段分组","append-to-body":!0,visible:e.gropuFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.gropuFormVisible=t}}},[a("Group",{ref:"group",attrs:{idx:e.idx,pid:e.pid},on:{bindData:e.BindTreeData,closeDialog:function(t){e.gropuFormVisible=!1}}})],1):e._e()],1)},o=[],r=a("2909"),l=(a("99af"),a("d81d"),a("e9c4"),a("ac1f"),a("841c"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{class:e.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnsearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnsearch},slot:"append"},[e._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:e.btnAdd}},[e._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(t){return e.openDialog()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:e.bindData}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:e.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(t){e.iShow=!e.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:e.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"模块编码"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入模块编码",size:"small"},model:{value:e.formdata.modulecode,callback:function(t){e.$set(e.formdata,"modulecode",t)},expression:"formdata.modulecode"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"字段名称"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入字段名称",size:"small"},model:{value:e.formdata.fieldname,callback:function(t){e.$set(e.formdata,"fieldname",t)},expression:"formdata.fieldname"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"字段编码"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入字段编码",size:"small"},model:{value:e.formdata.lister,callback:function(t){e.$set(e.formdata,"lister",t)},expression:"formdata.lister"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.advancedSearch()}}},[e._v(" 搜索 ")])],1)],1)],1)],1)]),e.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:e.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.setColumsVisible=t}}},[a("Setcolums",{ref:"setcolums",attrs:{code:e.code,tableForm:e.tableForm},on:{bindData:e.bindData,closeDialog:function(t){e.setColumsVisible=!1}}})],1):e._e()],1)}),n=[],s=a("8daf"),d={name:"Listheader",components:{Setcolums:s["a"]},props:["tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"S05M88B1FieldList",setColumsVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(){this.iShow=!1,this.$emit("advancedSearch",this.formdata)},btnAdd:function(){this.$emit("btnAdd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},c=d,m=(a("c5d6"),a("2877")),u=Object(m["a"])(c,l,n,!1,null,"a64ee298",null),f=u.exports,p=a("333d"),h=a("b775"),b=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticStyle:{width:"100%"}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":e.formLabelWidth,rules:e.formRules}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("modulecode")}}},[a("el-form-item",{attrs:{label:"模块编码",prop:"modulecode"}},[a("el-input",{attrs:{placeholder:"请输入模块编码",clearable:""},model:{value:e.formdata.modulecode,callback:function(t){e.$set(e.formdata,"modulecode",t)},expression:"formdata.modulecode"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("gengroupid")}}},[a("el-form-item",{attrs:{label:"字段分组"}},[a("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{options:e.groupData,props:e.defaultProps,"show-all-levels":!1,placeholder:"请选择字段分组",size:"small"},on:{change:e.handleChange},model:{value:e.formdata.gengroupid,callback:function(t){e.$set(e.formdata,"gengroupid",t)},expression:"formdata.gengroupid"}})],1)],1)])],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("fieldname")}}},[a("el-form-item",{attrs:{label:"字段名称",prop:"fieldname"}},[a("el-input",{attrs:{placeholder:"字段名称",clearable:""},model:{value:e.formdata.fieldname,callback:function(t){e.$set(e.formdata,"fieldname",t)},expression:"formdata.fieldname"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("fieldcode")}}},[a("el-form-item",{attrs:{label:"字段编码",prop:"fieldcode"}},[a("el-input",{attrs:{placeholder:"字段编码",clearable:""},model:{value:e.formdata.fieldcode,callback:function(t){e.$set(e.formdata,"fieldcode",t)},expression:"formdata.fieldcode"}})],1)],1)])],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("fieldtype")}}},[a("el-form-item",{attrs:{label:"字段类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"字段类型"},model:{value:e.formdata.fieldtype,callback:function(t){e.$set(e.formdata,"fieldtype",t)},expression:"formdata.fieldtype"}},[a("el-option",{attrs:{label:"文本",value:0}}),a("el-option",{attrs:{label:"数字",value:1}}),a("el-option",{attrs:{label:"日期",value:2}}),a("el-option",{attrs:{label:"布尔",value:3}})],1)],1)],1)]),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticClass:"inputNumberContent",attrs:{controls:!0,type:"number",min:0,"controls-position":"right"},model:{value:e.formdata.rownum,callback:function(t){e.$set(e.formdata,"rownum",t)},expression:"formdata.rownum"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:""}},[a("el-checkbox",{attrs:{label:"搜索应用","true-label":1,"false-label":0,size:"mini"},model:{value:e.formdata.searchmark,callback:function(t){e.$set(e.formdata,"searchmark",t)},expression:"formdata.searchmark"}})],1)],1)],1)],1),a("el-divider")],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":e.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"60px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:""},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.createby))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.createdate)))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.lister))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.modifydate)))])])],1)],1)],1)],1)]),a("div",{staticClass:"button-container flex j-s a-c"},[a("div",[e.idx?a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{"margin-left":"40px"},attrs:{type:"danger"},on:{click:function(t){return e.rowdel(e.idx)}}},[e._v(" 删 除")]):e._e()],1),a("div",[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary"},on:{click:function(t){return e.submitForm("formdata")}}},[e._v(" 保 存")]),a("el-button",{nativeOn:{click:function(t){return e.closeFrom(t)}}},[e._v(" 关 闭")])],1)])])},g=[];a("b64b");const v={add(e){return new Promise((t,a)=>{var i=JSON.stringify(e);h["a"].post("/SaScene/createField",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var i=JSON.stringify(e);h["a"].post("/SaScene/updateField",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){return new Promise((t,a)=>{h["a"].get("/SaScene/deleteField?key="+e).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})}};var w=v,x=a("b0b8"),y={name:"Formedit",components:{},props:["idx"],data:function(){return{title:"场景字段",formdata:{modulecode:"",fieldname:"",fieldcode:"",fieldtype:0,rownum:0,remark:"",gengroupid:"",searchmark:1,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,enabledmark:1},formRules:{modulecode:[{required:!0,trigger:"blur",message:"模块编码不能为空"}],fieldcode:[{required:!0,trigger:"blur",message:"字段编码不能为空"}],fieldname:[{required:!0,trigger:"blur",message:"字段名称不能为空"}]},multi:0,selVisible:!1,formLabelWidth:"80px",groupData:[],defaultProps:{children:"children",label:"label",value:"id"}}},watch:{idx:function(e,t){this.bindData()}},created:function(){this.bindData(),this.BindTreeData()},methods:{bindData:function(){var e=this;this.listLoading=!0,0!=this.idx&&h["a"].get("/SaScene/getFieldEntity?key=".concat(this.idx)).then((function(t){200==t.data.code?e.formdata=t.data.data:e.$alert(t.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){e.closeFrom()}}),e.listLoading=!1})).catch((function(t){e.listLoading=!1,e.$message.error("请求错误")}))},BindTreeData:function(){var e=this;h["a"].get("/SaBillGroup/getListByModuleCode?Code=S05M88B1Field").then((function(t){if(200==t.data.code){var a=t.data.data.map((function(e){return{id:e.id,pid:e.parentid,label:e.groupname}})),i=Object(r["a"])(a);e.groupData=e.transData(i,"id","pid","children")}}))},submitForm:function(e){var t=this;this.$refs.formdata.validate((function(e){if(!e)return console.log("error submit!!"),!1;t.saveForm()}))},saveForm:function(){var e=this;0==this.idx?w.add(this.formdata).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.$emit("changeidx",t.data.id),e.$emit("bindData"),e.bindData(),e.$emit("formcomp"))})).catch((function(t){e.$message.warning(res.data.msg||"保存失败")})):w.update(this.formdata).then((function(t){200==t.code&&(e.$emit("bindData"),e.bindData(),e.$message.success("保存成功"),e.$emit("formcomp"))})).catch((function(t){e.$message.warning(res.data.msg||"保存失败")}))},closeFrom:function(){this.$emit("closeFrom")},rowdel:function(e){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),w.delete(e).then((function(e){200==e.code&&t.$message.success("删除成功"),t.$emit("formcomp")})).catch((function(){t.$message.warning(res.data.msg||"删除失败")}))})).catch((function(){}))},handleChange:function(e){console.log(e),e.length>0?this.formdata.gengroupid=e[e.length-1]:this.formdata.gengroupid="0"},writeCode:function(e){x.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.fieldcode=x.getFullChars(e)},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},cleValidate:function(e){this.$refs.formdata.clearValidate(e)},transData:function(e,t,a,i){for(var o=[],r={},l=t,n=a,s=i,d=0,c=0,m=e.length;d<m;d++)r[e[d][l]]=e[d];for(;c<m;c++){var u=e[c],f=r[u[n]];f?(!f[s]&&(f[s]=[]),f[s].push(u)):o.push(u)}return o}}},S=y,k=(a("09bf"),Object(m["a"])(S,b,g,!1,null,"0ae85e26",null)),_=k.exports,F={formcode:"SaSceneFieldList",item:[{itemcode:"modulecode",itemname:"模块编码",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_Sence.modulecode"},{itemcode:"fieldname",itemname:"字段名称",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_Sence.fieldname"},{itemcode:"fieldcode",itemname:"字段编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Sence.fieldcode"},{itemcode:"fieldtype",itemname:"字段类型",minwidth:"60",displaymark:1,overflow:1,datasheet:"Sa_Sence.fieldtype"},{itemcode:"searchmark",itemname:"是否搜索",minwidth:"60",displaymark:1,overflow:1,datasheet:"Sa_Sence.searchmark"},{itemcode:"remark",itemname:"备注",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_Sence.remark"},{itemcode:"createdate",itemname:"创建日期",minwidth:"70",displaymark:1,sortable:1,overflow:1,datasheet:"Sa_Sence.createdate"},{itemcode:"lister",itemname:"制表",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_Sence.lister"}]},C=a("0521"),D=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticStyle:{width:"100%"}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":e.formLabelWidth,rules:e.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"分组名称",prop:"groupname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入分组名称",size:"small"},on:{input:e.writeCode},model:{value:e.formdata.groupname,callback:function(t){e.$set(e.formdata,"groupname",t)},expression:"formdata.groupname"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("groupcode")}}},[a("el-form-item",{attrs:{label:"分组编码",prop:"groupcode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入分组编码",size:"small"},model:{value:e.formdata.groupcode,callback:function(t){e.$set(e.formdata,"groupcode",t)},expression:"formdata.groupcode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:e.formdata.rownum,callback:function(t){e.$set(e.formdata,"rownum",t)},expression:"formdata.rownum"}})],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":e.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.lister))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.modifydate)))])])],1)],1)],1)],1)]),a("div",{staticClass:"button-container flex j-end"},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(t){return e.submitForm("formdata")}}},[e._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.$emit("closeDialog")}}},[e._v(" 关 闭")])],1)])},$=[],P=(a("d3b7"),a("25f0"),a("4d90"),a("b0b8")),N={name:"Formedit",filters:{dateFormat:function(e){var t=new Date(e),a=t.getFullYear(),i=(t.getMonth()+1).toString().padStart(2,"0"),o=t.getDate().toString().padStart(2,"0"),r=t.getHours().toString().padStart(2,"0"),l=t.getMinutes().toString().padStart(2,"0"),n=t.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(r,":").concat(l,":").concat(n)}},props:["idx","pid"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,parentid:"",modulecode:"S05M88B1Field",groupcode:"",groupname:"",rownum:0,remark:"",enabledmark:1},formRules:{modulecode:[{required:!0,trigger:"blur",message:"功能编码为必填项"}],groupcode:[{required:!0,trigger:"blur",message:"分组编码为必填项"}],groupname:[{required:!0,trigger:"blur",message:"分组名称为必填项"}]},formLabelWidth:"100px",dialogVisible:!1,delDialogVisible:!1,option:""}},computed:{formcontainHeight:function(){return"300px"}},watch:{idx:function(e,t){console.log("new: %s, old: %s",e,t),this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var e=this;0!=this.idx?h["a"].get("/SaBillGroup/getEntity?key=".concat(this.idx)).then((function(t){console.log("==================",t),200==t.data.code&&(e.formdata=t.data.data)})):this.formdata.parentid=this.idx},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return console.log("error submit!!"),!1;console.log("可保存"),t.saveForm()}))},saveForm:function(){var e=this;this.pid&&(this.formdata.parentid=this.pid),0==this.idx?h["a"].post("/SaBillGroup/create",JSON.stringify(this.formdata)).then((function(t){e.$emit("bindData"),e.$emit("closeDialog")})):h["a"].post("/SaBillGroup/update",JSON.stringify(this.formdata)).then((function(t){e.$emit("bindData"),e.$emit("closeDialog")}))},closeFrom:function(){this.$emit("closeFrom"),console.log("关闭窗口")},check:function(){console.log("check")},cleValidate:function(e){this.$refs.formdata.clearValidate(e)},writeCode:function(e){P.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.groupcode=P.getFullChars(e)}}},V=N,z=(a("dd8d"),Object(m["a"])(V,D,$,!1,null,"d69b89e0",null)),q=z.exports,T={name:"SaSceneField",components:{Pagination:p["a"],ListHeader:f,FormEdit:_,HelpModel:C["a"],Group:q},data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:15,OrderType:1,SearchType:1},tableForm:F,showHelp:!1,pid:"root",treeTitle:"场景字段",gropuFormVisible:!1,treeVisble:!0,groupData:[],defaultProps:{children:"children",label:"label",value:"id"},treeEditable:!1,multipleSelection:[]}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){this.BindTreeData()},mounted:function(){this.bindData(),this.getcolumn()},methods:{bindData:function(){var e=this;this.listLoading=!0,h["a"].post("/SaScene/getFieldPageList",JSON.stringify(this.queryParams)).then((function(t){200==t.data.code&&(e.lst=t.data.data.list,e.total=t.data.data.total),e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},getcolumn:function(){var e=this;h["a"].get("/SaDgFormat/getBillEntityByCode?code=SaSceneFieldList").then((function(t){if(200==t.data.code){if(null==t.data.data)return void(e.tableForm=F);e.tableForm=t.data.data}})).catch((function(t){e.$message.error("请求出错")}))},changeSort:function(e){"descending"==e.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(e,this.tableForm),this.bindData()},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},getList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},search:function(e){""!=e?this.queryParams.SearchPojo={fieldcode:e,fieldname:e,modulecode:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(e){this.queryParams.SearchPojo=e,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showform:function(e){this.idx=e,this.FormVisible=!0},closeFrom:function(){this.bindData(),this.FormVisible=!1},formcomp:function(){this.bindData(),this.FormVisible=!1},changeidx:function(e){this.idx=e},BindTreeData:function(){var e=this;h["a"].get("/SaBillGroup/getListByModuleCode?Code=SaSceneField").then((function(t){if(200==t.data.code){var a=t.data.data.map((function(e){return{id:e.id,pid:e.parentid,label:e.groupname}})),i=[{id:"0",pid:"root",label:e.treeTitle}],o=[].concat(Object(r["a"])(a),i);e.groupData=e.transData(o,"id","pid","children")}}))},handleNodeClick:function(e){if(0==e.id){var t="";this.search(t)}else{var a=e.id;this.searchgroupid(a)}},searchgroupid:function(e){""!=e?this.queryParams.SearchPojo={gengroupid:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},treeEdit:function(){this.treeEditable=!this.treeEditable},editTreeNode:function(e){this.showGroupform(e.id)},addTreeChild:function(e){this.pid=e.id,this.showGroupform(0)},delTreeNode:function(e){var t=this;console.log(e),e.children?this.$message({message:"警告,该节点下存在子节点不能删除",type:"warning"}):this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){h["a"].get("/SaBillGroup/delete?key=".concat(e.id)).then((function(){console.log("执行关闭保存"),t.$message.success({message:"删除成功！"}),t.BindTreeData()})).catch((function(){t.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},showGroupform:function(e){this.idx=e,this.gropuFormVisible=!0},transData:function(e,t,a,i){for(var o=[],r={},l=t,n=a,s=i,d=0,c=0,m=e.length;d<m;d++)r[e[d][l]]=e[d];for(;c<m;c++){var u=e[c],f=r[u[n]];f?(!f[s]&&(f[s]=[]),f[s].push(u)):o.push(u)}return o}}},B=T,L=(a("8d01"),Object(m["a"])(B,i,o,!1,null,"337d4f72",null));t["default"]=L.exports},"841c":function(e,t,a){"use strict";var i=a("d784"),o=a("825a"),r=a("1d80"),l=a("129f"),n=a("14c3");i("search",1,(function(e,t,a){return[function(t){var a=r(this),i=void 0==t?void 0:t[e];return void 0!==i?i.call(t,a):new RegExp(t)[e](String(a))},function(e){var i=a(t,e,this);if(i.done)return i.value;var r=o(e),s=String(this),d=r.lastIndex;l(d,0)||(r.lastIndex=0);var c=n(r,s);return l(r.lastIndex,d)||(r.lastIndex=d),null===c?-1:c.index}]}))},"8ce1":function(e,t,a){},"8d01":function(e,t,a){"use strict";a("8ce1")},"9a3e":function(e,t,a){},b891:function(e,t,a){},c5d6:function(e,t,a){"use strict";a("9a3e")},dd8d:function(e,t,a){"use strict";a("b891")},f862:function(e,t,a){}}]);