(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-dcfb6afa"],{"22e3":function(t,e,i){"use strict";i("5cd2")},"5cd2":function(t,e,i){},f18a:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"border-x":"","border-y":"","border-around":!0,"column-width-resize-option":t.columnWidthResizeOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)},o=[],n=i("c7eb"),r=i("1da1"),l=(i("e9c4"),i("a9e3"),i("b64b"),i("d3b7"),i("c7cd"),i("159b"),i("b775")),s=i("f944"),c=i("b893"),u=i("f07e"),m={components:{MyPopover:u["a"]},props:["online","searchVal","isDialog","isHongchong"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:s["e"],customList:[],mypopoverTable:s["d"],mypopoverData:[],mypopoverIndex:-1,columnHidden:[],columnWidthResizeOption:{enable:!0,minWidth:50,sizeChange:function(e){var i=e.column,a=e.differWidth,o=e.columnWidth;t.columnResizeInfo.column=i,t.columnResizeInfo.differWidth=a,t.columnResizeInfo.columnWidth=o}},columnResizeInfo:{column:"",differWidth:"",columnWidth:""},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},footerData:[],customData:[],eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}},cellTotal:0,cellNum:0}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={orguid:this.searchVal};this.queryParams.SearchPojo=e;var i="/D01M08B1/getPageTh";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(c["d"])()[0],EndDate:Object(c["d"])()[1]}),l["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],n=0;n<o.length;n++)t.$set(t.lst[i],o[n].key,o[n].value)}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(r["a"])(Object(n["a"])().mark((function e(){return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(s["e"].formcode,s["e"]).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row,n=(i.column,i.rowIndex);if("billdate"==t.itemcode||"createdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("goodsuid"==t.itemcode){var r=a("GoodsInfo",{attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return r}if("itemcount"==t.itemcode){r="";return r=a("el-popover",{attrs:{placement:"left",trigger:"click",title:"单据明细"},ref:"'popover-' + scope.$index"},[a("div",{style:"position: relative; min-height: 100px",directives:[{name:"show",value:n+e.rowScroll==e.mypopoverIndex}]},[a(u["a"],{ref:"mypopover",attrs:{tableForm:e.mypopoverTable,lst:e.mypopoverData}})]),a("span",{slot:"reference",class:"textunderline",on:{click:function(){return e.getBillList(o,n+e.rowScroll)}}},[o[t.itemcode]])]),r}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,i=["quantity"];this.$countCellData(this,i,t,e)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},getBillList:function(t,e){var i=this;this.mypopoverIndex=e,l["a"].get("/D04M01R1/getBillEntity?key=".concat(t.id)).then((function(t){200==t.data.code?i.mypopoverData=t.data.data.item:i.mypopoverData=[]}))}}},d=m,h=(i("22e3"),i("2877")),f=Object(h["a"])(d,a,o,!1,null,"3e8a0430",null);e["default"]=f.exports}}]);