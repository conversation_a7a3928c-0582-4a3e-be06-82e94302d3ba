(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-34b0b56f","chunk-65a471dd","chunk-196c9b92"],{"0041":function(t,e,i){},"0276":function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"收款单",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",required:!0,options:[{label:"销售收款",value:"销售收款"},{label:"单据收款",value:"单据收款"},{label:"其他收款",value:"其他收款"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:"",required:!0}]},{rowitem:[{col:5,code:"groupname",label:"客户",searchtype:"customer",type:"autocomplete",methods:"",param:"",show:!0,required:!0},{col:5,code:"billamount",label:"收款金额：",type:"text",methods:"",param:""}]}]},cash:{type:0,content:[{itemcode:"cashaccname",itemname:"出纳账户",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"amount",itemname:"金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"业务员",billcode:"sale.salesman",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},list:{type:0,content:[]},item:{type:0,content:[]}}},"0362":function(t,e,i){},"04b7":function(t,e,i){"use strict";i("89cb")},"04f2":function(t,e,i){},"063e":function(t,e,i){"use strict";i("0e26")},"06c72":function(t,e,i){},"09eb":function(t,e,i){"use strict";i("e510")},"0d4c":function(t,e,i){},"0e26":function(t,e,i){},"11f6":function(t,e,i){},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},1941:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D01M06B2Edit",commonurl:"/D01M06B2/printBill",weburl:"/D01M06B2/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D01M06B2Edit",examineurl:"/D01M06B2/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},["D04M01B2"==t.processModel?i("D04M01B2",{ref:"D04M01B2",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D01M06B2"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e()],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D04M01B2"==t.processModel?i("D04M01B2List",{ref:"D04M01B2List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=(i("b64b"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D01M06B2/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D01M06B2/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D01M06B2/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D01M06B2/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D01M06B2/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D01M06B2/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=r,l=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[]}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"其他发货",value:"其他发货"}}),i("el-option",{attrs:{label:"其他退货",value:"其他退货"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"客户",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B1/getOnlinePageList",type:"客户"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"联系人"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入联系人",clearable:"",size:"small"},model:{value:t.formdata.linkman,callback:function(e){t.$set(t.formdata,"linkman",e)},expression:"formdata.linkman"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"联系电话"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入联系电话",clearable:"",size:"small"},model:{value:t.formdata.telephone,callback:function(e){t.$set(t.formdata,"telephone",e)},expression:"formdata.telephone"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"送货地址",prop:"deliadd"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入送货地址",clearable:"",size:"small"},model:{value:t.formdata.deliadd,callback:function(e){t.$set(t.formdata,"deliadd",e)},expression:"formdata.deliadd"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"运输方式"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入运输方式",clearable:"",size:"small"},model:{value:t.formdata.transport,callback:function(e){t.$set(t.formdata,"transport",e)},expression:"formdata.transport"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"业务员"}},[i("el-popover",{ref:"dictionaryRef",staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.salesmanRef.bindData()}}},[i("SelDict",{ref:"salesmanRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"sale.salesman"},on:{singleSel:function(e){t.formdata.salesman=e.dictvalue,t.$refs["dictionaryRef"].doClose(),t.cleValidate("salesman")},closedic:function(e){return t.$refs["dictionaryRef"].doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"业务员",clearable:"",size:"small"},model:{value:t.formdata.salesman,callback:function(e){t.$set(t.formdata,"salesman",e)},expression:"formdata.salesman"}})],1)],1)],1)],1)],1)],1)},d=[],m={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"客户为必填项"}],billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},c=m,u=(i("2179"),i("2877")),f=Object(u["a"])(c,l,d,!1,null,"8b974f0e",null),h=f.exports,p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,showcontent:["add","dummy","moveup","movedown","delete","copyrow","refresh","billstate"],lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn,selDummy:t.selDummy},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"border-x":"","border-y":"","border-around":!0,"checkbox-option":t.checkboxOption,"column-width-resize-option":t.columnWidthResizeOption,editOption:t.editOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selgoodsvisible?i("el-dialog",{attrs:{title:"货品信息","append-to-body":!0,visible:t.selgoodsvisible,width:"1200px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selgoodsvisible=e}}},[i("SelGoods",{ref:"selGoods",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selGoods()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selgoodsvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},b=[],g=(i("c740"),i("caad"),i("d81d"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),i("da92"),{amount:0,attributejson:"",batchno:"",bfitemid:0,bussclosed:0,bussqty:0,citeitemid:"",citeuid:"",costgroupjson:"",costitemjson:"",custom1:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",custpo:"",disannuldate:new Date,disannullister:"",disannulmark:0,finishclosed:0,finishqty:0,freeqty:0,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",invoclosed:0,invoqty:0,itemcode:"",itemname:"",itemspec:"",itemtaxrate:0,itemunit:"",location:"",machdate:new Date,machitemid:"",machtype:"",machuid:"",partid:"",pickqty:0,pid:"",price:0,quantity:0,rebate:0,remark:"",returnclosed:0,returnmatqty:0,returnqty:0,rownum:0,salescost:0,sourcetype:0,statecode:"",statedate:new Date,stdamount:0,stdprice:0,taxamount:0,taxprice:0,taxtotal:0,virtualitem:0}),y=g,w=i("735b"),v=i("9bda"),x={name:"Elitem",components:{SelGoods:v["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{listLoading:!1,selgoodsvisible:!1,lst:[],setColumsVisible:!1,keynum:0,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:w["a"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate"],costgroupList:[],columsData:[],rowStyleOption:{clickHighlight:!1,hoverHighlight:!1,stripe:!1},columnHidden:[],checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},columnWidthResizeOption:{enable:!0,minWidth:10,sizeChange:function(e){var i=e.column,a=e.differWidth,o=e.columnWidth;t.columnResizeInfo.column=i,t.columnResizeInfo.differWidth=a,t.columnResizeInfo.columnWidth=o}},columnResizeInfo:{column:"",differWidth:"",columnWidth:""},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;if(t.editmarkfiles.includes(a.field)){t.countfiles.includes(a.field)&&t.changeInput("",i,a.field);var o=t.customList.findIndex((function(t){return t.attrkey==a.field}));-1!=o&&t.setAttributeJson(i,i.rownum)}}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},footerData:[],cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));if(-1!=s){t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[s],Object.keys(o)[1]);var r=t.customList.findIndex((function(t){return t.attrkey==Object.keys(o)[1]}));-1!=r&&t.setAttributeJson(t.lst[s],s)}}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;e.selectionRangeIndexes,e.selectionRangeKeys;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}},copyText:""}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){var i=this;if(0!=this.lstitem.length){this.nowitemtaxrate=this.lstitem[0].itemtaxrate;for(var a=0;a<this.lstitem.length;a++)if(""!=this.lstitem[a].attributejson&&null!=this.lstitem[a].attributejson)for(var o=JSON.parse(this.lstitem[a].attributejson),s=0;s<o.length;s++)i.$set(i.lstitem[a],o[s].key,o[s].value)}this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var s=JSON.parse(t[i].attributejson);for(a=0;a<s.length;a++)t[i][s[a].key]=s[a].value}}this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=w["a"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(w["a"].formcode,e,1).then((function(e){t.customList=e.customList,t.costgroupList=e.costgroupList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex,e.customList.findIndex((function(e){return t.itemcode==e.attrkey})));if(-1!=s){var r=e.customList[s].valuejson?e.customList[s].valuejson.split(","):[],n=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]])]),a("div",{directives:[{name:"show",value:r.length}]},[a("div",{style:"width:0;height:0"},[a("el-select",{class:"wpselect",style:"width:0;height:0;opacity: 0;",attrs:{size:"small",id:t.itemcode+o.rownum+w["a"].formcode},on:{change:function(i){o[t.itemcode]=i,e.setAttributeJson(o,o.rownum)}},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}},[r.map((function(t,e){return a("el-option",{key:e,attrs:{label:t,value:t},style:"max-width:200px"})}))])]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-arrow-down",style:"font-weight:bold;font-size:14px;",on:{click:function(){document.getElementById(t.itemcode+o.rownum+w["a"].formcode).click()}}})])]);return n}if("goodsuid"==t.itemcode){n=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeVal:o[t.itemcode]}});return n}if("status"==t.itemcode){n="";return 0!=o.finishqty&&o.finishqty<o.quantity?n=a("span",{class:"textborder-blue"},[e.formdata.billtype.includes("退货")?"入库":"出库"]):o.finishqty==o.quantity&&0!=o.finishqty?n=a("span",{class:"textborder-green"},["完成"]):1==o.disannulmark?n=a("span",{class:"textborder-grey"},["撤销"]):o.finishclosed&&(n=a("span",{class:"textborder-grey"},["中止"])),n}n=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]);return n}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["goodsuid","amount","quantity","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.changeInput("",this.lst[e+o],n);var l=this.customList.findIndex((function(t){return t.attrkey==n}));-1!=l&&this.setAttributeJson(this.lst[e+o],e+o)}},selDummy:function(t){var e=t;if(0!=e.length){this.$refs.itemGroupBtns.DummyVisible=!1;for(var i=0;i<e.length;i++){var a=e[i],o=Object.assign({},y);o.goodsid=a.id,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.goodscustom1=a.custom1,o.goodscustom2=a.custom2,o.goodscustom3=a.custom3,o.goodscustom4=a.custom4,o.goodscustom5=a.custom5,o.goodscustom6=a.custom6,o.goodscustom7=a.custom7,o.goodscustom8=a.custom8,o.goodscustom9=a.custom9,o.goodscustom10=a.custom10,o.price=a.outprice,o.virtualitem=1,0!=this.idx&&(o.pid=this.idx);for(var s=0;s<this.customList.length;s++){a=this.customList[s];o[a.attrkey]=""}for(s=0;s<this.costgroupList.length;s++){a=this.costgroupList[s];o[a.attrkey]=""}this.lst.push(o)}}else this.$message.warning("请选择虚拟品内容")},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],s={key:o.attrkey,value:t[o.attrkey]?t[o.attrkey].replace(/\s*/g,""):""};""!=s.value&&i.push(s)}0==i.length?this.lst[e].attributejson="":this.lst[e].attributejson=JSON.stringify(i)},numFormat:function(t){t+="";var e=t.lastIndexOf(".")?t.lastIndexOf("."):0,i=t.length,a=t.substring(e,i);return a>0?t:t.substring(0,e)},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i)},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"finishqty",0),this.$set(e,"closed",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.selgoodsvisible=!0,this.multi=t},selGoods:function(){var t=this.$refs.selGoods.$refs.selectgoods.selection;if(0!=t.length){this.selgoodsvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=i.taxprice?i.taxprice:0,o=i.price?i.price:0,s=i.itemtaxrate?i.itemtaxrate:this.nowitemtaxrate,r=0,n=0,l=0,d=Object.assign({},y);if(d.goodsid=i.id,d.goodsuid=i.goodsuid,d.goodsname=i.goodsname,d.goodsunit=i.goodsunit,d.goodsspec=i.goodsspec,d.goodsuid=i.goodsuid,d.partid=i.partid,d.itemname=i.itemname,d.itemcode=i.itemcode,d.itemspec=i.itemspec,d.itemunit=i.itemunit,d.goodscustom1=i.custom1,d.goodscustom2=i.custom2,d.goodscustom3=i.custom3,d.goodscustom4=i.custom4,d.goodscustom5=i.custom5,d.goodscustom6=i.custom6,d.goodscustom7=i.custom7,d.goodscustom8=i.custom8,d.goodscustom9=i.custom9,d.goodscustom10=i.custom10,d.custpo=i.custorderid,d.machtype=i.billtype,d.citeitemid=i.id,d.citeuid=i.refno,d.machuid=i.refno,d.machitemid=i.id,d.machdate=i.billdate,d.amount=l,d.itemtaxrate=s,d.price=o,d.quantity=r,d.taxamount=n||0,d.taxprice=a,d.taxtotal=d.taxamount-d.amount,d.virtualitem=i.virtualitem?i.virtualitem:0,d.attributejson=i.attributejson,d.costitemjson=i.costitemjson,d.costgroupjson=i.costgroupjson,0!=this.idx&&(d.pid=this.idx),""==d.attributejson||null==d.attributejson);else for(var m=JSON.parse(d.attributejson),c=0;c<m.length;c++)d[m[c].key]=m[c].value;this.lst.push(d),this.setAttributeJson(d,this.lst.length-1)}}else this.$message.warning("请选择货品内容")}}},k=x,D=(i("585f"),Object(u["a"])(k,p,b,!1,null,"7c35feb8",null)),S=D.exports,$=i("f68b"),_=i("dcb4"),B=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","telephone","linkman","deliadd","taxrate","transport","salesman","salesmanid","operator","operatorid","summary","billtaxamount","billtaxtotal","billamount","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],O=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","itemcode","itemname","itemspec","itemunit","quantity","taxprice","taxamount","price","amount","itemtaxrate","taxtotal","stdprice","stdamount","rebate","freeqty","rownum","remark","citeuid","citeitemid","custpo","machtype","salescost","virtualitem","location","batchno","machuid","machitemid","disannulmark","bfitemid","attributejson","machdate","costitemjson","costgroupjson","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10","custom11","custom12","custom13","custom14","custom15","custom16","custom17","custom18"],C={params:B,paramsItem:O},P=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"特权审核",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"approvalByPassAdmin",param:"",children:[]},{show:1,divided:!1,ieval:1,label:'this.formdata.billtype == "订单退货" ? "客退入库" : "发货出库"',icon:"",disabled:"this.formstate!=2",methods:"operateBill",param:"D04M01B2",children:[]}],F=[{show:1,ieval:1,divided:!1,label:'this.formdata.billtype == "订单退货" ? "客退入库" : "发货出库"',icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D04M01B2",label:"出入库单"},children:[]}],M=i("13df"),I=i("27f6"),q={name:"Formedit",components:{FormTemp:_["a"],EditHeader:h,EditItem:S,D04M01B2:M["default"],D04M01B2List:I["a"]},props:["idx","isprocessDialog"],data:function(){return{title:"其他发货",operateBar:P,processBar:F,formdata:{assessdate:"",assessor:"",billamount:0,billdate:new Date,billreceived:0,billstatecode:"",billstatedate:"",billtaxamount:0,billtaxtotal:0,billtitle:"",billtype:"其他发货",deliadd:"",disannulmark:0,groupid:"",groupname:"",linkman:"",operator:"",refno:"",salesman:"",summary:"",taxrate:0,telephone:"",tenantid:"",transport:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:$["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData()},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D01M06B2").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):$["a"],t.formtemplate.footer.type||(t.formtemplate.footer=$["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&s["a"].get("/D01M06B2/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.$refs.elitem.multipleSelection=[],t.$refs.elitem.checkboxOption.selectedRowKeys=[],t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.salesman=t.seller,this.formdata.abbreviate=t.abbreviate,this.formdata.grouplevel=t.grouplevel,this.formdata.linkman=t.linkman,this.formdata.telephone=t.telephone,this.formdata.deliadd=t.deliveradd},autoClear:function(){this.formdata.groupname="",this.formdata.groupid="",this.formdata.salesman="",this.formdata.abbreviate="",this.formdata.grouplevel="",this.formdata.linkman="",this.formdata.telephone="",this.formdata.deliadd=""},changeIdx:function(t){this.dialogIdx=t},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){try{this.$refs.elitem.$refs.multipleTable.stopEditingCell()}catch(o){}for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0"),void this.$refs.elitem.saveRow(this.$refs.elitem.lst[e]);this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.billamount=i.billamount,this.formdata.billtaxamount=i.billtaxamount,this.formdata.billtaxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(C,a,this.formdata),0==this.idx?n.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.submitting=0,t.$message.warning(e||"保存失败")})):n.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.submitting=0,t.$message.warning(e||"保存失败")}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(t)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t,console.log(this.processModel,"ss");var e=this.$refs.elitem.multipleSelection;"D04M01B2"===t&&e.length>0&&(this.formdata.item=e)},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},changeBillType:function(){formdata.item=[]},approvalByPassAdmin:function(){var t=this;s["a"].get("/D01M06B2/approvalByPassAdmin?key="+this.formdata.id).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败"))}))}}},T=q,L=(i("8576"),Object(u["a"])(T,a,o,!1,null,"c39b1784",null));e["a"]=L.exports},"1d21":function(t,e,i){},"1d9a":function(t,e,i){"use strict";i("58a1")},"1d9e":function(t,e,i){"use strict";i("a5ab")},"1e58":function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r}));var a={formcode:"D01M06B1Th",item:[{itemcode:"refno",itemname:"发货单号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Bus_Deliery.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtitle"},{itemcode:"billdate",itemname:"发货日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deliery.billdate"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"linkman",itemname:"联系人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.linkman"},{itemcode:"telephone",itemname:"联系电话",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.telephone"},{itemcode:"billtaxamount",itemname:"含税金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtaxamount"},{itemcode:"transport",itemname:"运输方式",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.transport"},{itemcode:"salesman",itemname:"业务员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.salesman"},{itemcode:"summary",itemname:"摘要",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.summary"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.status"},{itemcode:"itemcount",itemname:"款数",sortable:1,minwidth:"60",displaymark:1,overflow:1,datasheet:"Bus_Deliery.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Bus_Deliery.assessor"}]},o={formcode:"D01M06B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:1,sortable:1,aligntype:"center",datasheet:"Bus_Deliery.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deliery.billdate"},{itemcode:"groupname",itemname:"客户",minwidth:"80",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_DelieryItem.quantity"},{itemcode:"finishqty",itemname:"已出库",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remainder",itemname:"结余数",minwidth:"100",displaymark:0,overflow:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_DelieryItem.taxprice"},{itemcode:"taxamount",itemname:"含税金额",sortable:1,minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_DelieryItem.taxamount"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1},{itemcode:"custom1",itemname:"发货备注",minwidth:"100",displaymark:0,overflow:1}]},s={formcode:"D01M06B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"citeuid",itemname:"销售订单",minwidth:"100",displaymark:1,overflow:1},{itemcode:"machtype",itemname:"订单类型",minwidth:"100",displaymark:1,overflow:1},{itemcode:"finishqty",itemname:"已出入库",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"custom1",itemname:"发货备注",minwidth:"100",displaymark:0,overflow:1,editmark:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:0,overflow:1}]},r={formcode:"D01M06B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:1,sortable:1,aligntype:"center",datasheet:"Bus_Deliery.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deliery.billdate"},{itemcode:"groupname",itemname:"客户",minwidth:"80",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_DelieryItem.quantity"},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_DelieryItem.taxprice"},{itemcode:"taxamount",itemname:"含税金额",sortable:1,minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_DelieryItem.taxamount"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1}]}},"1ec9":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D01M09B1Edit",commonurl:"/D01M09B1/printBill",weburl:"/D01M09B1/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D01M09B1Edit",examineurl:"/D01M09B1/sendapprovel"}})],1)},o=[],s=(i("b64b"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D01M09B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D01M09B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D01M09B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D01M09B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D01M09B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D01M09B1/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=r,l=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small",clearable:""},on:{clear:function(e){t.formdata.item=[]}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"销售扣款",value:"销售扣款"}}),i("el-option",{attrs:{label:"其他扣款",value:"其他扣款"}})],1)],1)],1),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{attrs:{placeholder:"请输入单据",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"客户",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B1/getOnlinePageList",type:"客户"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{staticStyle:{display:"flex"}},[i("el-form-item",{attrs:{label:"总金额:"}},[i("span",{staticStyle:{"font-size":"18px",color:"#606266"}},[t._v(" ￥"+t._s(t.formdata.billtaxamount?t.formdata.billtaxamount:0))])])],1)])],1)],1)},d=[],m={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{billtitle:[{required:!0,trigger:"blur",message:"单据标题为必填项"}],groupname:[{required:!0,trigger:"blur",message:"客户为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},c=m,u=(i("9a71"),i("2877")),f=Object(u["a"])(c,l,d,!1,null,"b2c0c72c",null),h=f.exports,p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","dummy","moveup","movedown","delete","copyrow","refresh","billstate"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection,dummyurl:"/D91M01B1/getVirOnlinePageList"},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,selDummy:t.selDummy,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"border-x":"","border-y":"","border-around":!0,"checkbox-option":t.checkboxOption,"column-width-resize-option":t.columnWidthResizeOption,editOption:t.editOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selgoodsvisible?i("el-dialog",{attrs:{title:"货品信息","append-to-body":!0,visible:t.selgoodsvisible,width:"1200px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selgoodsvisible=e}}},[i("SelGoods",{ref:"selGoods",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selGoods()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selgoodsvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.ordervisible?i("el-dialog",{attrs:{title:"销售订单","append-to-body":!0,visible:t.ordervisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.ordervisible=e}}},[i("SelOrder",{ref:"selOrder",attrs:{multi:1,selecturl:"/D01M03B1/getOnlinePageList?groupid="+t.formdata.groupid+"&appl=1"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.ordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},b=[],g=(i("c740"),i("caad"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),{amount:0,citeitemid:"",citeuid:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",custpo:"",disannuldate:new Date,disannullister:"",disannullisterid:"",disannulmark:0,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",id:"",invoclosed:0,invoqty:0,itemtaxrate:0,machitemid:"",machuid:"",partid:"",pid:"",price:0,quantity:0,remark:"",revision:0,rownum:0,taxamount:0,taxprice:0,taxtotal:0}),y=i("fe08"),w=i("9bda"),v=i("2499"),x={name:"Elitem",components:{SelGoods:w["a"],SelOrder:v["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{listLoading:!1,selgoodsvisible:!1,setColumsVisible:!1,lst:[],keynum:0,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:y["a"],customList:[],editmarkfiles:[],stockInfo:{},ordervisible:!1,orderindex:0,columsData:[],rowStyleOption:{clickHighlight:!1,hoverHighlight:!1,stripe:!1},columnHidden:[],checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},columnWidthResizeOption:{enable:!0,minWidth:10,sizeChange:function(e){var i=e.column,a=e.differWidth,o=e.columnWidth;t.columnResizeInfo.column=i,t.columnResizeInfo.differWidth=a,t.columnResizeInfo.columnWidth=o}},columnResizeInfo:{column:"",differWidth:"",columnWidth:""},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;t.editmarkfiles.includes(a.field)&&t.changeInput("",i,a.field)}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},footerData:[],cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.changeInput("",t.lst[s],Object.keys(o)[1])}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!0,afterCopy:function(e){var i=e.data;e.selectionRangeIndexes,e.selectionRangeKeys;t.copyText=i[0][0]},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=e.selectionRangeKeys,s=a.startRowIndex,r=o.startColKey;t.lst[s][r]=i[0][r].replace(/^\s*|\s*$/g,""),t.changeInput("",t.lst[s],r)},afterCut:function(e){var i=e.data;e.selectionRangeIndexes,e.selectionRangeKeys;t.copyText=i[0][0]},afterDelete:function(t){t.data;var e=t.selectionRangeIndexes;t.selectionRangeKeys,e.startRowIndex}},copyText:""}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){var i=this;if(0!=this.lstitem.length){this.nowitemtaxrate=this.lstitem[0].itemtaxrate;for(var a=0;a<this.lstitem.length;a++)if(""!=this.lstitem[a].attributejson&&null!=this.lstitem[a].attributejson)for(var o=JSON.parse(this.lstitem[a].attributejson),s=0;s<o.length;s++)i.$set(i.lstitem[a],o[s].key,o[s].value)}this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=y["a"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(y["a"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(e,i){var a=e.row;e.column,e.rowIndex;if("goodsuid"==t.itemcode){var o=i("GoodsInfo",{class:a.disannulmark?"textlinethrough":"",attrs:{scopeVal:a[t.itemcode]}});return o}o=i("span",{class:a.disannulmark?"textlinethrough":""},[a[t.itemcode]]);return o}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["goodsuid","amount","quantity","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i),this.getSummary()},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"finishqty",0),this.$set(e,"closed",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(){if("销售扣款"==this.formdata.billtype){if(""==this.formdata.groupid)return void this.$message.warning("请选择客户");this.ordervisible=!0}else this.selgoodsvisible=!0},selGoods:function(){var t=this.$refs.selGoods.$refs.selectgoods.selection;if(0!=t.length){this.selgoodsvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},g);a.goodsid=i.id,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择货品内容")},selOrder:function(){var t=this.$refs.selOrder.$refs.selectVal.selection;if(0!=t.length){this.ordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},g);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.goodsuid=i.goodsuid,a.partid=i.partid,a.machitemid=i.id,a.machuid=i.refno,a.machdate=i.billdate,a.itemtaxrate=i.itemtaxrate,a.quantity=i.quantity,a.price=i.price,a.amount=i.amount,a.taxprice=i.taxprice,a.taxamount=i.taxamount,a.taxtotal=i.taxtotal,a.machuid=i.refno,a.machitemid=i.id,a.custpo=i.custorderid,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selDummy:function(t){var e=t;if(0!=e.length){this.$refs.itemGroupBtns.DummyVisible=!1;for(var i=0;i<e.length;i++){var a=e[i],o=Object.assign({},g);o.goodsid=a.id,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.price=a.outprice,o.taxprice=a.outprice,o.virtualitem=1,0!=this.idx&&(o.pid=this.idx),this.lst.push(o)}}else this.$message.warning("请选择虚拟品内容")}}},k=x,D=(i("5caf"),Object(u["a"])(k,p,b,!1,null,"f88a961a",null)),S=D.exports,$=i("a9bb"),_=i("dcb4"),B=["id","refno","billtype","billdate","billtitle","groupid","groupuid","groupname","abbreviate","grouplevel","operator","taxrate","billtaxamount","billamount","billtaxtotal","summary","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],O=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","quantity","taxprice","taxamount","taxtotal","itemtaxrate","price","amount","remark","citeuid","citeitemid","machuid","machitemid","custpo","rownum","disannulmark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],C={params:B,paramsItem:O},P=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]}],F=[],M={name:"Formedit",components:{FormTemp:_["a"],EditHeader:h,EditItem:S},props:["idx","isprocessDialog"],data:function(){return{title:"销售扣款",operateBar:P,processBar:F,formdata:{refno:"",billtype:"销售扣款",billtitle:"",billdate:new Date,groupid:"",groupname:"",custorderid:"",logisticsmode:"",advance:"",taxrate:"",billtaxamount:"",billtaxtotal:"",billamount:"",billstatecode:"",billstatedate:"",billplandate:"",groupcode:"",disannulmark:0,summary:"",assessor:"",assessdate:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},formRules:{billtitle:[{required:!0,trigger:"blur",message:"单据标题为必填项"}]},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:$["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp(),this.bindData()},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D01M09B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):$["a"],t.formtemplate.footer.type||(t.formtemplate.footer=$["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&s["a"].get("/D01M09B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(res.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.salesman=t.seller,this.formdata.abbreviate=t.abbreviate,this.formdata.grouplevel=t.grouplevel,this.formdata.linkman=t.linkman,this.formdata.telephone=t.telephone,this.formdata.deliadd=t.deliveradd},autoClear:function(){this.formdata.groupname="",this.formdata.groupid="",this.formdata.salesman="",this.formdata.abbreviate="",this.formdata.grouplevel="",this.formdata.linkman="",this.formdata.telephone="",this.formdata.deliadd=""},changeIdx:function(t){this.dialogIdx=t},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0"),void this.$refs.elitem.saveRow(this.$refs.elitem.lst[e]);this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.billamount=i.billamount,this.formdata.billtaxamount=i.billtaxamount,this.formdata.billtaxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(C,a,this.formdata),0==this.idx?n.add(a).then((function(e){t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0})):n.update(a).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(e)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t,console.log(this.processModel,"ss")},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},changeBillType:function(){formdata.item=[]}}},I=M,q=(i("09eb"),Object(u["a"])(I,a,o,!1,null,"7466b45e",null));e["a"]=q.exports},2179:function(t,e,i){"use strict";i("ebf8")},"233f":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{},[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",{staticStyle:{"margin-bottom":"10px"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,height:350,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"1px 0px 1px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"55"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"账户名称",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.accountname)+" ")]}}])}),i("el-table-column",{attrs:{label:"账户类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.accounttype))])]}}])}),i("el-table-column",{attrs:{label:"当前金额",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.currentamt))])]}}])}),i("el-table-column",{attrs:{label:"备注",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.remark))])]}}])}),i("el-table-column",{attrs:{label:"创建时间",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormat")(e.row.createdate)))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},o=[],s=(i("e9c4"),i("b775")),r=i("333d"),n={components:{Pagination:r["a"]},props:["multi"],data:function(){return{title:"出纳账户",listLoading:!0,lst:[],strfilter:"",radio:"",selrows:"",total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},watch:{},created:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,s["a"].post("/D07M21B2/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={accountname:t,accounttype:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},l=n,d=i("2877"),m=Object(d["a"])(l,a,o,!1,null,"0e79971c",null);e["a"]=m.exports},2399:function(t,e,i){"use strict";i("a5e0")},2499:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px",width:"100%"}},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1)]),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"450px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"客户订单号",align:"center",width:"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.custorderid))])]}}])}),i("el-table-column",{attrs:{label:"客户",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),t._l(t.customList,(function(t,e){return[i("el-table-column",{key:e,attrs:{"header-align":"center",align:"center",prop:t.attrkey,label:t.attrname}})]}))],2)],1),i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],s=i("c7eb"),r=i("1da1"),n=(i("c740"),i("e9c4"),i("b64b"),i("b775")),l=i("8daf"),d=i("40d9"),m={props:["multi","groupid","selecturl"],components:{Setcolums:l["a"]},data:function(){return{listLoading:!0,lst:[],strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},customList:[],setColumsVisible:!1,tableForm:d["d"]}},created:function(){this.bindData(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,n["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code?t.customList=e.data.data?e.data.data:[]:t.$message.warning(e.data.msg||"获取失败")})).catch((function(e){t.$message.error("请求错误")}));case 2:return e.next=4,n["a"].get("/SaDgFormat/getBillEntityByCode?code=D01M03B1Select").then((function(e){if(200==e.data.code){if(null==e.data.data){t.tableForm=d["d"];for(var i=0;i<t.customList.length;i++){var a=t.customList[i],o=t.tableForm.item.findIndex((function(e){return e.itemcode==t.customList[i].attrkey}));if(-1==o){var s={itemcode:a.attrkey,itemname:a.attrname,minwidth:"80",displaymark:a.listshow,overflow:1};t.tableForm.item.push(s)}}return}t.tableForm=e.data.data;for(i=0;i<t.customList.length;i++){a=t.customList[i],o=t.tableForm.item.findIndex((function(e){return e.itemcode==t.customList[i].attrkey}));if(-1==o){s={itemcode:a.attrkey,itemname:a.attrname,minwidth:"80",displaymark:a.listshow,overflow:1};t.tableForm.item.push(s)}}}})).catch((function(e){t.$message.error("请求出错")}));case 4:case"end":return e.stop()}}),e)})))()},getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){var i;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.listLoading=!0,t.listLoading=!0,i=t.selecturl?t.selecturl:"/D01M03B1/getPageList",e.next=5,n["a"].post(i,JSON.stringify(t.queryParams)).then((function(e){if(200==e.data.code){console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 5:return e.next=7,n["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code||t.$message.warning(e.data.msg||"获取spu失败")})).catch((function(e){t.$message.error("请求错误")}));case 7:case"end":return e.stop()}}),e)})))()},search:function(t){var e=t.split(",");0!=e.length?1==e.length?(this.queryParams.SearchPojo={goodsuid:e[0],goodsname:e[0],goodsunit:e[0],groupid:e[0],goodsspec:e[0],partid:e[0],refno:e[0],custorderid:e[0],groupname:e[0],attributestr:e[0]},this.$delete(this.queryParams,"scenedata")):e.length>1&&(this.queryParams.scenedata=[{field:"Mat_Goods.goodsuid",fieldtype:0,math:"like",value:"".concat(e[0])},{field:"Mat_Goods.goodsname",fieldtype:0,math:"like",value:"".concat(e[1])}],3==e.length&&this.queryParams.scenedata.push({field:"Mat_Goods.partid",fieldtype:0,math:"like",value:"".concat(e[2])}),this.$delete(this.queryParams,"SearchPojo")):(this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"scenedata")),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},c=m,u=(i("fb056"),i("2877")),f=Object(u["a"])(c,a,o,!1,null,"56660287",null);e["a"]=f.exports},"251e":function(t,e,i){},2645:function(t,e,i){"use strict";i("bf63")},"27f6":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},o=[],s=i("c7eb"),r=i("1da1"),n=(i("caad"),i("e9c4"),i("a9e3"),i("b64b"),i("d3b7"),i("2532"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),i("b775")),l=i("5e63"),d=i("b893"),m={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"13df"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:l["b"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={citeuid:this.searchVal};this.queryParams.SearchPojo=e;var i="/D04M01R1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(d["d"])()[0],EndDate:Object(d["d"])()[1]}),n["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(l["b"].formcode,l["b"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=a("GoodsInfo",{attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return s}if("billtype"==t.itemcode){s=a("span",[o[t.itemcode]]);return o[t.itemcode].includes("红冲")&&(s=a("span",{style:"color:#f44336"},[o[t.itemcode]])),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},c=m,u=(i("b83a"),i("2877")),f=Object(u["a"])(c,a,o,!1,null,"34f0715a",null);e["a"]=f.exports},"28f6":function(t,e,i){"use strict";i("c6bc")},"2a69":function(t,e,i){"use strict";i("8d63")},"2ee7":function(t,e,i){},"30e1":function(t,e,i){},3559:function(t,e,i){},"358b":function(t,e,i){"use strict";i("8377")},"3f80":function(t,e,i){"use strict";i("251e")},"40d9":function(t,e,i){"use strict";i.d(e,"f",(function(){return a})),i.d(e,"e",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"b",(function(){return r})),i.d(e,"a",(function(){return n})),i.d(e,"d",(function(){return l}));var a={formcode:"D01M03B1Th",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"150",defwidth:"",displaymark:1,fixed:1,sortable:1,overflow:1,aligntype:"center",datasheet:"Bus_Machining.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billdate"},{itemcode:"custorderid",itemname:"客户订单号",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.custorderid"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"abbreviate",itemname:"简称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.abbreviate"},{itemcode:"groupadd",itemname:"客户地址",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupadd"},{itemcode:"billtaxamount",itemname:"总金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtaxamount"},{itemcode:"advaamount",itemname:"预收款",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.advaamount"},{itemcode:"balance",itemname:"结余",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billcostbudgetamt",itemname:"成本预算",minwidth:"80",displaymark:0,overflow:1},{itemcode:"billplandate",itemname:"计划时间",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billplandate"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.summary"},{itemcode:"salesman",itemname:"业务员",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.salesman"},{itemcode:"billwkwpname",itemname:"最新工序",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.billwkwpname"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Machining.status"},{itemcode:"itemcount",itemname:"款数",minwidth:"60",displaymark:1,overflow:1,datasheet:"Bus_Machining.itemcount"},{itemcode:"printcount",itemname:"打印次数",minwidth:"100",displaymark:0,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Machining.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Bus_Machining.assessor"},{itemcode:"amtstatus",itemname:"收款状态",minwidth:"100",displaymark:1,overflow:1}]},o={formcode:"D01M03B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"150",defwidth:"",displaymark:1,overflow:1,fixed:1,sortable:1,aligntype:"center",datasheet:"Bus_Machining.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billdate"},{itemcode:"custorderid",itemname:"客户订单号",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.custorderid"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"matcode",itemname:"物料编码",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.matcode"},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.quantity"},{itemcode:"taxprice",itemname:"单价",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxprice"},{itemcode:"taxamount",itemname:"金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxamount"},{itemcode:"finishqty",itemname:"完成数",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.finishqty"},{itemcode:"remainder",itemname:"结余数",minwidth:"100",displaymark:0,overflow:1},{itemcode:"buyquantity",itemname:"采购数",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.buyquantity"},{itemcode:"wkwpname",itemname:"工序",minwidth:"100",displaymark:1,overflow:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"matstatus",itemname:"物料状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemplandate",itemname:"评审交期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.itemplandate"},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.remark"}]},s={formcode:"D01M03B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"120",defwidth:"",displaymark:1,fixed:1,sortable:0,overflow:1,aligntype:"center",operationmark:1},{itemcode:"goodsname",itemname:"货品名称",minwidth:"120",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"200",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"120",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"partid",itemname:"外部编码",minwidth:"120",displaymark:1,overflow:1,operationmark:1},{itemcode:"quantity",itemname:"数量",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"itemorgdate",itemname:"原始交期",minwidth:"120",displaymark:1,overflow:1},{itemcode:"itemplandate",itemname:"评审交期",minwidth:"120",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"stoqty",itemname:"库存发货",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"maxqty",itemname:"最大发货数",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"wkqty",itemname:"生产需求",minwidth:"120",displaymark:1,overflow:1},{itemcode:"wkquantity",itemname:"生产数",minwidth:"120",displaymark:1,overflow:1},{itemcode:"buyquantity",itemname:"采购数",minwidth:"100",displaymark:1,overflow:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:0,overflow:1},{itemcode:"finishqty",itemname:"发货数",minwidth:"120",displaymark:1,overflow:1},{itemcode:"outquantity",itemname:"出库数",minwidth:"120",displaymark:1,overflow:1}]},r={formcode:"D01M03B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"150",defwidth:"",displaymark:1,overflow:1,fixed:1,sortable:1,aligntype:"center",datasheet:"Bus_Machining.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billdate"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"matcode",itemname:"物料编码",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.matcode"},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.quantity"},{itemcode:"taxprice",itemname:"单价",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxprice"},{itemcode:"taxamount",itemname:"金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxamount"},{itemcode:"wkwpname",itemname:"工序",minwidth:"100",displaymark:1,overflow:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"matstatus",itemname:"物料状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemplandate",itemname:"评审交期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.itemplandate"},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.remark"}]},n={formcode:"D03M02B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"130",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",atasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,atasheet:"Buy_Order.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Buy_Order.billtitle"},{itemcode:"billdate",itemname:"单据日期",sortable:1,minwidth:"100",displaymark:1,overflow:1,atasheet:"Buy_Order.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"plandate",itemname:"计划完成",minwidth:"100",sortable:1,displaymark:1,overflow:1,atasheet:"Buy_OrderItem.billdate"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"规格",minwidth:"120",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.quantity"},{itemcode:"finishqty",itemname:"已收",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.status"}]},l={formcode:"D01M03B1Select",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"130",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",atasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"custorderid",itemname:"客户订单号",minwidth:"100",displaymark:1,overflow:1},{itemcode:"groupname",itemname:"客户",minwidth:"120",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,overflow:1,operationmark:1},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"120",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1}]}},4630:function(t,e,i){"use strict";i("80b3")},"49b1":function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return s})),i.d(e,"b",(function(){return r}));var a={formcode:"D01M08B1DEPList",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Bus_Deposit.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deposit.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Bus_Deposit.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deposit.billdate"},{itemcode:"abbreviate",itemname:"简称",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.abbreviate"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Deposit.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deposit.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deposit.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Deposit.summary"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deposit.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Bus_Deposit.assessor"},{itemcode:"outamount",itemname:"转出金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Deposit.outamount"}]},o={formcode:"D01M08B1DEPItem",item:[{itemcode:"machbillcode",itemname:"订单单号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"billtaxamount",itemname:"单据金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},s={formcode:"D01M08B1DEPCash",item:[{itemcode:"cashaccname",itemname:"出纳账户",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"amount",itemname:"金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D01M08B1DEPCite",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Bus_Deposit.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deposit.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Bus_Deposit.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deposit.billdate"},{itemcode:"abbreviate",itemname:"简称",minwidth:"80",displaymark:1,overflow:1,datasheet:"App_Workgroup.abbreviate"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Deposit.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deposit.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deposit.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Deposit.summary"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deposit.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Bus_Deposit.assessor"},{itemcode:"outamount",itemname:"转出金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Deposit.outamount"}]}},"4aa6":function(t,e,i){},"52f2":function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"销售开票",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"销售开票",value:"销售开票"},{label:"其他应收",value:"其他应收"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""},{col:5,code:"invocode",label:"发票编码",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"客户",searchtype:"customer",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"linkman",label:"联系人",type:"input",methods:"",param:""},{col:5,code:"telephone",label:"联系电话",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"invodate",label:"开票日期",type:"date",methods:"computerTime",param:""},{col:5,code:"aimdate",label:"收款计划",type:"date",methods:"",param:""},{col:3,code:"taxamount",label:"发票金额",type:"input",methods:"",param:"",show:"this.formdata.billtype== '其他应收'"},{col:3,code:"taxamount",label:"发票金额",type:"text",methods:"",param:"",show:"this.formdata.billtype== '销售开票'"},{col:2,code:"receipted",label:"已收金额",type:"text",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"salesman",label:"业务员",billcode:"sale.salesman",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},5827:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{ref:"index",staticClass:"index"},[i("listheader",{directives:[{name:"show",rawName:"v-show",value:!t.isDialog,expression:"!isDialog"}],attrs:{tableForm:t.tableForm,total:t.total},on:{btnAdd:function(e){t.openVisible=!0},btnSearch:t.search,bindData:t.bindData,changeModelUrl:t.changeModelUrl,btnExport:t.btnExport,advancedSearch:t.advancedSearch,carryForward:function(e){t.carryForwardVisible=!0},deleteForm:t.deleteForm,btnHelp:t.btnHelp,bindColumn:function(e){return t.$refs.tableTh.getColumn()}}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{attrs:{span:t.showhelp?20:24}},[i("tableTh",{ref:"tableTh",attrs:{searchVal:t.searchVal,isDialog:t.isDialog},on:{changeIdx:t.changeIdx,getTotal:t.getTotal,sendTableForm:t.sendTableForm}})],1),i("el-col",{attrs:{span:t.showhelp?4:0}},[i("HelpModel",{ref:"helpmodel",attrs:{code:"D01M014B1"}})],1)],1)],1)],1),t.openVisible?i("el-dialog",{attrs:{title:"开账",width:"400px","append-to-body":!0,"close-on-click-modal":!1,visible:t.openVisible},on:{"update:visible":function(e){t.openVisible=e}}},[i("div",[i("div",{staticClass:"block",staticStyle:{"margin-bottom":"10px"}},[i("span",{staticClass:"formdataTitle"},[t._v("年份:")]),i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"year",placeholder:"选择年份"},model:{value:t.formdata.year,callback:function(e){t.$set(t.formdata,"year",e)},expression:"formdata.year"}})],1),i("div",{staticClass:"block"},[i("span",{staticClass:"formdataTitle"},[t._v("月份:")]),i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"month",placeholder:"选择月份",format:"MM"},model:{value:t.formdata.month,callback:function(e){t.$set(t.formdata,"month",e)},expression:"formdata.month"}})],1)]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitOpen()}}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.openVisible=!1}}},[t._v("取 消")])],1)]):t._e(),t.carryForwardVisible?i("el-dialog",{attrs:{title:"结转",width:"400px","append-to-body":!0,"close-on-click-modal":!1,visible:t.carryForwardVisible},on:{"update:visible":function(e){t.carryForwardVisible=e}}},[i("div",[i("div",{staticClass:"block",staticStyle:{"margin-bottom":"10px"}},[i("span",{staticClass:"formdataTitle"},[t._v("年份:")]),i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"year",placeholder:"选择年份"},model:{value:t.formdata.year,callback:function(e){t.$set(t.formdata,"year",e)},expression:"formdata.year"}})],1),i("div",{staticClass:"block"},[i("span",{staticClass:"formdataTitle"},[t._v("月份:")]),i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"month",placeholder:"选择月份",format:"MM"},model:{value:t.formdata.month,callback:function(e){t.$set(t.formdata,"month",e)},expression:"formdata.month"}})],1)]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small",disabled:t.isdisabled},on:{click:function(e){return t.submitCarryForward()}}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.carryForwardVisible=!1}}},[t._v("取 消")])],1)]):t._e()],1)},o=[],s=(i("ac1f"),i("841c"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[i("div",[0==t.total?i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 开账 ")]):i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-position",plain:"",size:"mini"},on:{click:function(e){return t.$emit("carryForward")}}},[t._v(" 结转 ")]),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-edit-outline",plain:"",size:"mini"},on:{click:function(e){return t.$router.push("/D01/M12R1DETA")}}},[t._v(" 本期账单 ")]),0!=t.total?i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete",plain:"",size:"mini"},on:{click:function(e){return t.$emit("deleteForm")}}},[t._v(" 反结账 ")]):t._e()],1),i("div",{staticClass:"iShowBtn"},[i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.code?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-help",title:"帮助"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.bindData,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.code,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),r=[],n={name:"Listheader",props:["tableForm","total"],components:{},data:function(){return{strfilter:"",iShow:!1,formdata:{},thorList:!0,setColumsVisible:!1,code:"D01M14B1List",searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(t){this.iShow=!1,this.$emit("advancedSearch",t),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList)}}},l=n,d=(i("682f"),i("2877")),m=Object(d["a"])(l,s,r,!1,null,"3bd3afbe",null),c=m.exports,u=i("b775"),f=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableTh",attrs:{data:t.lst,"element-loading-text":"Loading","summary-method":t.getSummaries,"show-summary":"",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight},on:{"selection-change":t.handleSelectionChange,"sort-change":t.changeSort,"row-click":t.openCheckout}},[i("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),i("el-table-column",{attrs:{align:"center",label:"ID",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,a){return[!e.displaymark?t._e():i("el-table-column",{key:a,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(a){return["startdate"==e.itemcode||"enddate"==e.itemcode?i("span",[t._v(t._s(t._f("dateFormat")(a.row[e.itemcode])))]):i("span",[t._v(t._s(a.row[e.itemcode]))])]}}],null,!0)})]}))],2),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}}),i("div",{staticStyle:{"margin-right":"40px"}},[i("scene",{ref:"scene",attrs:{code:"D01M14B1Th"},on:{bindData:t.bindData}})],1)],1),i("el-divider"),i("div",{staticStyle:{height:"450px"}},[i("checkoutTable",{ref:"checkoutTable",attrs:{row:t.row}})],1)],1)},h=[],p=(i("d81d"),i("e9c4"),i("b64b"),i("333d"));const b={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);u["a"].post("/D01M14B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);u["a"].post("/D01M14B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){return new Promise((e,i)=>{u["a"].get("/D01M14B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})}};var g=b,y=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticStyle:{height:"100%"}},[i("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[i("div",{staticClass:"filter-container",staticStyle:{margin:"0 10px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")]),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-printer",plain:"",size:"mini"},on:{click:function(e){return t.$refs.PrintServer.printButton(1)}}},[t._v(" 打印 ")])],1),i("div",{staticStyle:{"margin-right":"10px",position:"relative"}},[i("el-button",{staticStyle:{"font-weight":"bold"},attrs:{size:"mini",icon:"el-icon-refresh-right"},nativeOn:{click:function(e){return t.bindData()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}})],1)]),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",height:t.tableHeight,"header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[i("el-table-column",{attrs:{align:"center",label:"ID",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,a){return[!e.displaymark?t._e():i("el-table-column",{key:a,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(a){return["groupuid"==e.itemcode?i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showForm(a.row)}}},[t._v(t._s(a.row.groupuid?a.row.groupuid:"客户编码"))]):"startdate"==e.itemcode||"enddate"==e.itemcode?i("span",[t._v(t._s(t._f("dateFormat")(a.row[e.itemcode])))]):i("span",[t._v(t._s(a.row[e.itemcode]))])]}}],null,!0)})]}))],2)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}}),t.processVisible?i("el-dialog",{attrs:{title:"客户结账明细",width:"80vw","append-to-body":!0,visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},[i("formedit",{ref:"formedit",attrs:{idx:t.idx},on:{closeDialog:function(e){t.processVisible=!1}}})],1):t._e(),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.lst,printcode:"D01M14B1Edit",commonurl:"/D01M14B1/printPageList",weburl:"/D01M14B1/printWebPageList",queryParams:t.queryParams}})],1)},w=[],v=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("el-button",{attrs:{disabled:!t.formdata.id,type:"primary",size:"small"},nativeOn:{click:function(e){return t.printButton()}}},[t._v("打 印")]),i("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.$emit("closeDialog")}}},[t._v(" 关 闭")])],1)]),i("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[i("el-form",{ref:"formdata",staticClass:"custInfo",staticStyle:{"min-height":"115px"},attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"on",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据类型",readonly:"",size:"small"},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupid")}}},[i("el-form-item",{attrs:{label:"来往客户",prop:"groupid"}},[i("autocomplete",{ref:"autocomplete",attrs:{formdata:t.formdata},on:{setRow:t.setGroupRow}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("enddate")}}},[i("el-form-item",{attrs:{label:"结束时间",prop:"enddate"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.enddate,callback:function(e){t.$set(t.formdata,"enddate",e)},expression:"formdata.enddate"}})],1)],1)])],1)],1),i("el-divider")],1),i("div",{staticClass:"form-body form f-1"},[i("elitem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx},on:{bindData:t.bindData}})],1),i("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[i("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[i("el-col",{attrs:{span:18}},[i("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[i("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.summary,callback:function(e){t.$set(t.formdata,"summary",e)},expression:"formdata.summary"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"经办人"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入经办人",clearable:"",size:"small"},model:{value:t.formdata.operator,callback:function(e){t.$set(t.formdata,"operator",e)},expression:"formdata.operator"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创建人"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创建日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"制表"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"修改日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),i("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible},on:{"update:visible":function(e){t.ReportVisible=e}}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return i("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),i("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[i("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})])],1)},x=[],k=(i("d3b7"),i("3ca3"),i("ddb0"),i("2b3d"),i("9861"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[i("el-row"),i("div",{staticStyle:{"margin-right":"10px",position:"relative"}},[i("el-button",{staticStyle:{"font-weight":"bold"},attrs:{size:"mini",icon:"el-icon-refresh-right"},nativeOn:{click:function(e){return t.$emit("bindData")}}}),i("el-button",{staticStyle:{float:"right"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}})],1)],1),i("div",{staticClass:"table-container f-1 table-position"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",staticStyle:{overflow:"auto"},attrs:{"summary-method":t.getSummaries,"show-summary":"",data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",size:"small",height:t.tableHeight,"header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange,"row-click":t.saveRow}},[i("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),t._l(t.tableForm.item,(function(e){return[!e.displaymark?t._e():i("el-table-column",{key:e.id,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(a){return["billdate"==e.itemcode?i("div",[i("span",[t._v(t._s(t._f("dateFormat")(a.row[e.itemcode])))])]):"billuid"==e.itemcode?i("div",[null==a.row.modulecode||""==a.row.modulecode?i("span",[t._v(t._s(a.row[e.itemcode]))]):i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showInfo(a.row)}}},[t._v(t._s(a.row.billuid?a.row.billuid:"单据编号"))])],1):i("span",[i("span",[t._v(t._s(a.row[e.itemcode]))])])]}}],null,!0)})]}))],2)],1),t.PwProcessFormVisible?i("el-dialog",{attrs:{title:"订单信息","append-to-body":!0,visible:t.PwProcessFormVisible,width:"1200px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.PwProcessFormVisible=e}}},[i("selPwProcess",{ref:"selPwProcess",attrs:{multi:t.multi,groupid:t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selPwProcess()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.PwProcessFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("Setcolums",{ref:"setcolums",attrs:{code:"D01M12B1Item",tableForm:t.tableForm},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),t.processVisible?i("el-dialog",{attrs:{width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,"append-to-body":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D01M08"==t.processRow.modulecode?i("D01M08B1",{ref:"D01M08B1",attrs:{idx:t.processRow.billid,isprocessDialog:!0},on:{closeDialog:function(e){t.processVisible=!1}}}):t._e(),"D01M08DEP"==t.processRow.modulecode?i("D01M08B1DEP",{ref:"D01M08B1DEP",attrs:{idx:t.processRow.billid,isprocessDialog:!0},on:{closeDialog:function(e){t.processVisible=!1}}}):t._e(),"D01M09"==t.processRow.modulecode?i("D01M09B1",{ref:"D01M09B1",attrs:{idx:t.processRow.billid,isprocessDialog:!0},on:{closeDialog:function(e){t.processVisible=!1}}}):t._e(),"D01M06"==t.processRow.modulecode&&"发出商品"==t.processRow.billtype?i("D01M06B1",{ref:"D01M06B1",attrs:{idx:t.processRow.billid,isprocessDialog:!0},on:{closeDialog:function(e){t.processVisible=!1}}}):t._e(),"D01M06"==t.processRow.modulecode&&"其他发货"==t.processRow.billtype?i("D01M06B2",{ref:"D01M06B2",attrs:{idx:t.processRow.billid,isprocessDialog:!0},on:{closeDialog:function(e){t.processVisible=!1}}}):t._e()],1):t._e()],1)}),D=[],S=(i("7db0"),i("13d5"),i("a434"),i("a9e3"),i("b680"),i("159b"),i("2499")),$=i("da92"),_=i("8daf"),B={formcode:"D01M14B1List",item:[{itemcode:"rownum",itemname:"序号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"carryyear",itemname:"年份",minwidth:"80",displaymark:1,overflow:1},{itemcode:"carrymonth",itemname:"月份",minwidth:"80",displaymark:1,overflow:1},{itemcode:"startdate",itemname:"开始时间",minwidth:"80",displaymark:1},{itemcode:"enddate",itemname:"结束时间",minwidth:"100",displaymark:1,overflow:1},{itemcode:"billopenamount",itemname:"期初金额",minwidth:"80",displaymark:1},{itemcode:"billinamount",itemname:"入账金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billoutamount",itemname:"出账金额",minwidth:"80",displaymark:1},{itemcode:"billcloseamount",itemname:"期末金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1}]},O={formcode:"D01M14B1Item",item:[{itemcode:"groupuid",itemname:"客户编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,overflow:1},{itemcode:"billopenamount",itemname:"期初金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billinamount",itemname:"入账金额",minwidth:"100",displaymark:1,overflow:1},{itemcode:"billoutamount",itemname:"出账金额",minwidth:"100",displaymark:1,overflow:1},{itemcode:"billcloseamount",itemname:"期末金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"seller",itemname:"业务员",minwidth:"80",displaymark:1,overflow:1},{itemcode:"startdate",itemname:"开始时间",minwidth:"80",displaymark:1,overflow:1},{itemcode:"enddate",itemname:"结束时间",minwidth:"80",displaymark:1,overflow:1}]},C={formcode:"D01M13B1InitItem",item:[{itemcode:"billuid",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,overflow:1},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billtitle",itemname:"单据标题",minwidth:"100",displaymark:1,overflow:1},{itemcode:"openamount",itemname:"期初金额",minwidth:"100",displaymark:1,overflow:1},{itemcode:"inamount",itemname:"入账金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"outamount",itemname:"出账金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"closeamount",itemname:"期末金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"120",displaymark:1,overflow:1}]},P=i("910f"),F=i("aa59"),M=i("1ec9"),I=i("5ad6"),q=i("1941"),T={name:"Elitem",components:{selPwProcess:S["a"],Setcolums:_["a"],D01M08B1:P["default"],D01M08B1DEP:F["default"],D01M09B1:M["a"],D01M06B1:I["default"],D01M06B2:q["a"]},props:["formdata","lstitem","idx"],data:function(){return{title:"订单发货",formLabelWidth:"100px",listLoading:!1,PwProcessFormVisible:!1,lst:[],multi:0,selected:!1,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:C,setColumsVisible:!1,processVisible:!1,processRow:{}}},watch:{lstitem:function(t,e){this.lst=this.lstitem,0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate)},"formdata.assessor":function(t,e){this.formdata.assessor?this.isEditOk=!1:this.isEditOk=!0},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i}},created:function(){this.lst=[],this.getColumn()},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},mounted:function(){this.catchHight()},methods:{getColumn:function(){var t=this;u["a"].get("/SaDgFormat/getBillEntityByCode?code=D01M12B1Item").then((function(e){if(200==e.data.code){if(null==e.data.data)return void(t.tableForm=C);t.tableForm=e.data.data}})).catch((function(e){t.$message.error("请求出错")}))},openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate(),this.$forceUpdate()},getSummaries:function(t){var e=this,i=t.columns,a=t.data,o=["openamount","inamount","outamount","closeamount"],s=[];return i.forEach((function(t,i){if(0!==i){var r=!1;o.length>0&&void 0!=o.find((function(e){return e==t.property}))&&(r=!0);var n=a.map((function(e){return Number(e[t.property])}));!n.every((function(t){return isNaN(t)}))&&r?s[i]=n.reduce((function(t,i){var a=Number(i);return isNaN(a)?e.numFormat(Number(t).toFixed(4)):e.numFormat($["a"].plus(Number(t),Number(i)).toFixed(4))}),0):s[i]="","openamount"==t.property?e.formdata.billopenamount=s[i]:"inamount"==t.property?e.formdata.billinamount=s[i]:"outamount"==t.property?e.formdata.billoutamount=s[i]:"closeamount"==t.property&&(e.formdata.billcloseamount=s[i])}else s[i]="合计"})),s},getselPwProcess:function(t){var e=this;if(this.formdata.groupid&&this.formdata.enddate){var i={groupid:this.formdata.groupid,enddate:this.formdata.enddate};u["a"].post("/D01M12B1/pullItemList",JSON.stringify(i)).then((function(t){200==t.data.code?e.lst=t.data.data:e.$message.warning(t.data.msg||"导入失败")})).catch((function(t){e.$message.error("请求错误")}))}else this.$message.warning("请选择往来客户和时间")},selPwProcess:function(){this.PwProcessFormVisible=!1},numFormat:function(t){t+="";var e=t.lastIndexOf(".")?t.lastIndexOf("."):0,i=t.length,a=t.substring(e,i);return a>0?t:t.substring(0,e)},changeInput:function(t,e,i){},handleSelectionChange:function(t){t.length>0?this.selected=!0:this.selected=!1;for(var e=0;e<this.lst.length;e++)this.lst[e].rownum=e;this.multipleSelection=t},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},deleteRows:function(t,e){var i=this,a=this.multipleSelection;console.log("选中数据",a),a&&a.forEach((function(t,e){i.lst.forEach((function(e,a){t.goodsid===e.goodsid&&t.goodsname===e.goodsname&&i.lst.splice(a,1)}))})),this.$refs.multipleTable.clearSelection(),this.selected=!1},showInfo:function(t){this.processRow=t,this.processVisible=!0},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},saveRow:function(t){for(var e=0;e<this.lst.length;e++)this.lst[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0)),this.$forceUpdate()},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}},L=T,R=(i("04b7"),Object(d["a"])(L,k,D,!1,null,"75ddadbc",null)),N=R.exports,j=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("el-autocomplete",{staticStyle:{width:"100%"},attrs:{size:"small","fetch-suggestions":t.querySearchAsync,"trigger-on-focus":!0,placeholder:"请选择"+(t.type?t.type:"客户"),"value-key":"name",clearable:"",disabled:!!t.formdata.assessor},on:{select:t.handleSelect,focus:function(t){return t.currentTarget.select()},clear:function(e){return t.blurForBug()}},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.item;return[i("div",[i("span",{staticClass:"groupnameSty"},[t._v(" "+t._s(a.name))]),i("span",{staticClass:"selectSpan",staticStyle:{float:"right","text-align":"right",color:"#8492a6","font-size":"13px"}},[t._v(t._s(a.groupuid))])])]}}]),model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname","string"===typeof e?e.trim():e)},expression:"formdata.groupname"}})],1)},V=[],z=(i("c740"),{components:{},props:["formdata","groupid","baseurl","type"],data:function(){return{title:"客户信息",lst:[],queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1}}},mounted:function(){var t=this;this.$nextTick((function(){t.$forceUpdate()}))},methods:{querySearchAsync:function(t,e){var i=this;""!=t?this.queryParams.SearchPojo={groupuid:t,groupname:t,abbreviate:t}:this.$delete(this.queryParams,"SearchPojo");var a=[],o="/D01M01B1/getOnlinePageList";this.baseurl&&(o=this.baseurl),u["a"].post(o,JSON.stringify(this.queryParams)).then((function(t){if(console.log(t),200==t.data.code){i.lst=t.data.data.list;for(var o=0;o<t.data.data.list.length;o++){var s=t.data.data.list[o],r={value:s.id,name:s.groupname,abbreviate:s.abbreviate,groupuid:s.groupuid};a.push(r)}}e(a)})).catch((function(t){e([])}))},handleSelect:function(t){var e=this.lst.findIndex((function(e){return t.value==e.id}));-1!=e&&this.$emit("setRow",this.lst[e])},blurForBug:function(){document.activeElement.blur()}}}),E=z,H=(i("737c"),Object(d["a"])(E,j,V,!1,null,"31f7622a",null)),K=H.exports,A={name:"Formedit",components:{elitem:N,autocomplete:K},props:["idx"],data:function(){return{title:"销售账单",formdata:{billcloseamount:0,billdate:new Date,billinamount:0,billopenamount:0,billoutamount:0,billtitle:"",billtype:"销售账单",carrymonth:(new Date).getMonth()+1,carryyear:(new Date).getFullYear(),groupid:"",groupname:"",operator:"",refno:"",summary:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},formRules:{groupid:[{required:!0,trigger:"blur",message:"客户为必填项"}],enddate:[{required:!0,trigger:"blur",message:"时间为必填项"}],billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}]},multi:0,selVisible:!1,formLabelWidth:"100px",ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,dialogIdx:0}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;0!=this.idx&&(this.listLoading=!0,u["a"].get("/D01M12B1/getBillEntity?key=".concat(this.idx)).then((function(e){console.log("============",e),200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")})))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.linkman=t.linkman,this.formdata.telephone=t.telephone,this.formdata.groupid=t.id,this.formdata.deliadd=t.deliveradd,this.$refs.formdata.clearValidate("groupid")},printButton:function(){var t=this;u["a"].get("/SaReports/getListByModuleCode?code=D01M12B1Edit").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0}))},submitReport:function(){var t=this;""!=this.reportModel?u["a"].get("/D01M12B1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.ReportVisible=!1;var i=[];i.push(e.data);var a=window.URL.createObjectURL(new Blob(i,{type:"application/pdf"}));t.pdfUrl=a,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},handleBlur:function(){this.selVisible=!1},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},G=A,W=(i("f098"),Object(d["a"])(G,v,x,!1,null,"4d97ee04",null)),J=W.exports,U=i("48da"),Y=i("b893"),X={components:{Pagination:p["a"],formedit:J},props:["row"],data:function(){return{title:"结账信息",listLoading:!1,lst:[],strfilter:"",total:0,idx:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},processVisible:!1,tableHeight:300,tableForm:O}},updated:function(){var t=this;this.$nextTick((function(){t.$refs.selectVal.doLayout()}))},mounted:function(){this.catchHight()},methods:{catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-52)}))},getSummaries:function(t){return Object(Y["f"])(t,["billopenamount","billinamount","billoutamount","billcloseamount"])},btnExport:function(){var t=this;Promise.resolve().then(function(){for(var e=[],i=[],a=0;a<t.tableForm.item.length;a++){var o=t.tableForm.item[a];o.displaymark&&o.displaymark&&(e.push(o.itemname),i.push(o.itemcode))}var s=t.lst,r=t.formatJson(i,s);Object(U["a"])(e,r,"客户总账明细")}.bind(null,i)).catch(i.oe)},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,u["a"].post("/D01M12B1/getPageThByMonth?year="+this.row.carryyear+"&month="+this.row.carrymonth,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={groupuid:t,groupname:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},showForm:function(t){this.processVisible=!0,this.idx=t.id}}},Q=X,Z=(i("6c92"),Object(d["a"])(Q,y,w,!1,null,"24220f46",null)),tt=Z.exports,et=i("4363"),it={components:{Pagination:p["a"],checkoutTable:tt,scene:et["a"]},data:function(){return{lst:[],searchstr:"",total:0,listLoading:!1,idx:0,row:{},queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},tableForm:B,checkOutVisible:!1,selectList:[]}},computed:{tableMaxHeight:function(){return window.innerHeight-160-490+"px"}},created:function(){this.searchstr=""},updated:function(){var t=this;this.$nextTick((function(){t.$refs.tableTh.doLayout()}))},methods:{bindData:function(){var t=this;this.listLoading=!0,this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(Y["d"])()[0],EndDate:Object(Y["d"])()[1]}),this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),u["a"].post("/D01M14B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total,t.$emit("getTotal",t.total)),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getColumn:function(){var t=this;u["a"].get("/SaDgFormat/getBillEntityByCode?code=D01M14B1List").then((function(e){if(200==e.data.code){if(null==e.data.data)return t.tableForm=B,void t.$emit("sendTableForm",t.tableForm);t.tableForm=e.data.data,t.$emit("sendTableForm",t.tableForm)}})).catch((function(e){t.$message.error("请求出错")}))},getSummaries:function(t){return Object(Y["f"])(t,["billinamount","billoutamount"])},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,groupname:t,billtitle:t,groupuid:t}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.queryParams.scenedata=t,""==t[0].field?this.queryParams.SearchType=1:this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},openCheckout:function(t){var e=this;this.row=t,setTimeout((function(){e.$refs.checkoutTable.bindData()}),100)},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(t,this.tableForm),this.bindData()},handleSelectionChange:function(t){console.log(t),this.selectList=t},deleteForm:function(){var t=this;if(1==this.selectList.length){var e=this.selectList[0];this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){g.delete(e.id).then((function(e){200==e.code&&(0==e.data?t.$message.warning("删除失败,"+e.msg+"中已使用"):(t.$message.success("删除成功"),t.bindData()))})).catch((function(e){t.$message.warning(e||"删除失败")}))})).catch((function(){}))}else this.$message.warning("请选择一行内容")},btnExport:function(){var t=this;Promise.resolve().then(function(){for(var e=[],i=[],a=0;a<t.tableForm.item.length;a++){var o=t.tableForm.item[a];o.displaymark&&o.displaymark&&(e.push(o.itemname),i.push(o.itemcode))}var s=t.lst,r=t.formatJson(i,s);Object(U["a"])(e,r,"销售账单")}.bind(null,i)).catch(i.oe)},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))}}},at=it,ot=(i("2645"),Object(d["a"])(at,f,h,!1,null,"0a0b156d",null)),st=ot.exports,rt={name:"D01M03B1",components:{listheader:c,tableTh:st},props:["searchVal","isDialog"],data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",thorList:!0,tableForm:{},formdata:{month:new Date,year:new Date},openVisible:!1,carryForwardVisible:!1,isdisabled:!1,showhelp:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},mounted:function(){this.bindData(),this.$refs.tableTh.getColumn()},methods:{bindData:function(){var t=this;this.isDialog&&(this.thorList=!1),this.thorList&&this.$nextTick((function(){t.$refs.tableTh.bindData()}))},sendTableForm:function(t){this.tableForm=t},getTotal:function(t){this.total=t},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},changeModelUrl:function(t){this.thorList=!!t,this.bindData()},btnExport:function(){var t=this;this.$nextTick((function(){t.$refs.tableTh.btnExport()}))},deleteForm:function(){var t=this;this.$nextTick((function(){t.$refs.tableTh.deleteForm()}))},search:function(t){this.$refs.tableTh.search(t)},advancedSearch:function(t){this.$refs.tableTh.advancedSearch(t)},submitOpen:function(){var t=this,e={month:new Date(this.formdata.month).getMonth()+1,year:new Date(this.formdata.year).getFullYear()};u["a"].get("/D01M14B1/open?month="+e.month+"&year="+e.year).then((function(e){200==e.data.code?(t.bindData(),t.openVisible=!1):t.$message.warning(e.data.msg||"开账失败")}))},submitCarryForward:function(){var t=this,e={month:new Date(this.formdata.month).getMonth()+1,year:new Date(this.formdata.year).getFullYear()};this.isdisabled=!0,u["a"].get("/D01M14B1/batchCreate?month="+e.month+"&year="+e.year).then((function(e){200==e.data.code?(t.bindData(),t.carryForwardVisible=!1,t.isdisabled=!1):t.$message.warning(e.data.msg||"结转失败")}))},closeForm:function(){this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeIdx:function(t){this.idx=t}}},nt=rt,lt=(i("1d9a"),Object(d["a"])(nt,a,o,!1,null,"a7aa12ce",null));e["default"]=lt.exports},"585f":function(t,e,i){"use strict";i("c4d7")},"58a1":function(t,e,i){},"5a67":function(t,e,i){"use strict";i("5fe0")},"5ad6":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D01M06B1Edit",commonurl:"/D01M06B1/printBill",weburl:"/D01M06B1/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D01M06B1Edit",examineurl:"/D01M06B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},["D01M05B1"==t.processModel?i("D01M05B1",{ref:"D01M05B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D01M06B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e(),"D04M01B2"==t.processModel?i("D04M01B2",{ref:"D04M01B2",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D01M06B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e(),"D01M08B1"==t.processModel?i("D01M08B1",{ref:"D01M08B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D01M06B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e()],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D04M01B2"==t.processModel?i("D04M01B2List",{ref:"D04M01B2List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e(),"D01M05B1"==t.processModel?i("D01M05B1List",{ref:"D01M05B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e(),"D01M08B1"==t.processModel?i("D01M08B1List",{ref:"D01M08B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e(),i("el-dialog",{staticClass:"warnDialog",attrs:{title:"提示",visible:t.stockwarningvisible,width:"50vw","close-on-press-escape":!1,"close-on-click-modal":!1,"append-to-body":!0},on:{"update:visible":function(e){t.stockwarningvisible=e}}},[i("div",[i("div",{staticStyle:{display:"flex","align-items":"center",margin:"10px"}},[i("i",{staticClass:"el-icon-warning",staticStyle:{"font-size":"24px",color:"#e6a23c"}}),i("span",{staticStyle:{"margin-left":"10px","font-size":"15px"}},[t._v(" 货品可用数量不足，是否继续审核？")])]),i("el-table",{staticStyle:{width:"100%"},attrs:{data:t.stockwarndata,"header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px"},"cell-style":{padding:"4px 0px"},border:"","highlight-current-row":""}},[i("el-table-column",{attrs:{align:"center",label:"ID",width:"50px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),i("el-table-column",{attrs:{prop:"goodsuid",label:"编码",align:"center"}}),i("el-table-column",{attrs:{prop:"goodsname",label:"名称",align:"center"}}),i("el-table-column",{attrs:{prop:"goodsspec",label:"规格",align:"center"}}),i("el-table-column",{attrs:{prop:"quantity",label:"账面库存",align:"center"}}),i("el-table-column",{attrs:{prop:"stoqty",label:"可用数量",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",{staticStyle:{color:"red"}},[t._v(t._s(e.row.stoqty))])]}}])}),i("el-table-column",{attrs:{prop:"buyremqty",label:"采购待入",align:"center"}}),i("el-table-column",{attrs:{prop:"busremqty",label:"订单待用",align:"center"}}),i("el-table-column",{attrs:{prop:"requremqty",label:"领料待出",align:"center"}})],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.stockwarningvisible=!1}}},[t._v("取 消")]),i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.approvalRequest(t.formdata.id)}}},[t._v("确 定")])],1)])],1)},o=[],s=i("c7eb"),r=i("1da1"),n=(i("b64b"),i("b775"));const l={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);n["a"].post("/D01M06B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);n["a"].post("/D01M06B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){n["a"].get("/D01M06B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,n["a"].get("/D01M06B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D01M06B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D01M06B1/closed?type="+(3==t?1:0);n["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var d=l,m=i("da92"),c=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[]}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"发出商品",value:"发出商品"}}),i("el-option",{attrs:{label:"订单退货",value:"订单退货"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"客户",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B1/getOnlinePageList",type:"客户"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"联系人"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入联系人",clearable:"",size:"small"},model:{value:t.formdata.linkman,callback:function(e){t.$set(t.formdata,"linkman",e)},expression:"formdata.linkman"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"联系电话"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入联系电话",clearable:"",size:"small"},model:{value:t.formdata.telephone,callback:function(e){t.$set(t.formdata,"telephone",e)},expression:"formdata.telephone"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"送货地址",prop:"deliadd"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入送货地址",clearable:"",size:"small"},model:{value:t.formdata.deliadd,callback:function(e){t.$set(t.formdata,"deliadd",e)},expression:"formdata.deliadd"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"运输方式"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入运输方式",clearable:"",size:"small"},model:{value:t.formdata.transport,callback:function(e){t.$set(t.formdata,"transport",e)},expression:"formdata.transport"}})],1)],1),i("el-col",{attrs:{span:5}},[i("div",{ref:"colRefs",on:{click:function(e){return t.cleValidate("salesman")}}},[i("el-form-item",{attrs:{label:"业务员",prop:"salesman"}},[i("el-popover",{ref:"dictionaryRef",staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.salesmanRef.bindData()}}},[i("SelDict",{ref:"salesmanRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"sale.salesman",baseparam:"/SaDict"},on:{singleSel:function(e){t.formdata.salesman=e.dictvalue,t.$refs["dictionaryRef"].doClose(),t.cleValidate("salesman")},closedic:function(e){return t.$refs["dictionaryRef"].doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"业务员",clearable:"",size:"small"},model:{value:t.formdata.salesman,callback:function(e){t.$set(t.formdata,"salesman",e)},expression:"formdata.salesman"}})],1)],1)],1)],1)])],1)],1)},u=[],f={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"客户为必填项"}],billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},h=f,p=(i("eb28"),i("2877")),b=Object(p["a"])(h,c,u,!1,null,"268d3f79",null),g=b.exports,y=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection,showcontent:["add","moveup","movedown","delete","copyrow","refresh","billstate"]},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"订单信息","append-to-body":!0,visible:t.selordervisible,width:"1200px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1,selecturl:t.selecturl,groupid:t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.SelOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},w=[],v=(i("c740"),i("caad"),i("d81d"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),{amount:0,attributejson:"",batchno:"",bfitemid:0,bussclosed:0,bussqty:0,citeitemid:"",citeuid:"",costgroupjson:"",costitemjson:"",custom1:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",custpo:"",disannuldate:new Date,disannullister:"",disannulmark:0,finishclosed:0,finishqty:0,freeqty:0,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",invoclosed:0,invoqty:0,itemcode:"",itemname:"",itemspec:"",itemtaxrate:0,itemunit:"",location:"",machdate:new Date,machitemid:"",machtype:"",machuid:"",partid:"",pickqty:0,pid:"",price:0,quantity:0,rebate:0,remark:"",returnclosed:0,returnmatqty:0,returnqty:0,rownum:0,salescost:0,sourcetype:0,statecode:"",statedate:new Date,stdamount:0,stdprice:0,taxamount:0,taxprice:0,taxtotal:0,virtualitem:0}),x=i("1e58"),k=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px",width:"100%"}},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("el-button",{attrs:{plain:"",size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})],1),i("div",[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"fixed-footer":!0}})],1),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("Setcolums",{ref:"setcolums",attrs:{code:"D01M03B1Select",tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},D=[],S=(i("a434"),i("8daf")),$=i("40d9"),_={props:["multi","groupid","selecturl"],components:{Setcolums:S["a"]},data:function(){var t=this;return{listLoading:!0,lst:[],strfilter:"",keynum:0,total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},selectList:[],customList:[],customData:[],setColumsVisible:!1,tableForm:$["d"],columnHidden:[],virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){var i=e.row,a=(e.isSelected,e.selectedRowKeys);if(t.checkboxOption.selectedRowKeys=a,a.includes(i.id))t.selectList.push(i);else{var o=t.selectList.findIndex((function(t){return t.id==i.id}));-1!=o&&t.selectList.splice(o,1)}},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.selectList=t.lst,t.checkboxOption.selectedRowKeys=a):(t.selectList=[],t.checkboxOption.selectedRowKeys=[])}}}},created:function(){this.bindData(),this.getColumn()},computed:{tableMaxHeight:function(){var t=window.innerHeight-500;return console.log(t,"tableMaxHeight"),t<600&&(t=500),"500px"},tableMinWidth:function(){var t="calc(100vw - 60vw)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},methods:{getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){var i;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=$["d"],t.$getColumn(t.tableForm.formcode,i,1,0,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 2:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex,"");return"createdate"==t.itemcode||"modifydate"==t.itemcode?e.$options.filters.dateFormats(o[t.itemcode]):"refno"==t.itemcode?(s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showDialog(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]),s):o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.customData=i,this.keynum+=1},getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){var i;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.listLoading=!0,t.listLoading=!0,i=t.selecturl?t.selecturl:"/D01M03B1/getPageList",e.next=5,n["a"].post(i,JSON.stringify(t.queryParams)).then((function(e){if(200==e.data.code){console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 5:return e.next=7,n["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code||t.$message.warning(e.data.msg||"获取spu失败")})).catch((function(e){t.$message.error("请求错误")}));case 7:case"end":return e.stop()}}),e)})))()},search:function(t){var e=t.split(",");0!=e.length?1==e.length?(this.queryParams.SearchPojo={goodsuid:e[0],goodsname:e[0],goodsunit:e[0],groupid:e[0],goodsspec:e[0],partid:e[0],refno:e[0],custorderid:e[0],groupname:e[0],attributestr:e[0]},this.$delete(this.queryParams,"scenedata")):e.length>1&&(this.queryParams.scenedata=[{field:"Mat_Goods.goodsuid",fieldtype:0,math:"like",value:"".concat(e[0])},{field:"Mat_Goods.goodsname",fieldtype:0,math:"like",value:"".concat(e[1])}],3==e.length&&this.queryParams.scenedata.push({field:"Mat_Goods.partid",fieldtype:0,math:"like",value:"".concat(e[2])}),this.$delete(this.queryParams,"SearchPojo")):(this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"scenedata")),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index}}},B=_,O=(i("358b"),Object(p["a"])(B,k,D,!1,null,"3fadc678",null)),C=O.exports,P={name:"Elitem",components:{SelOrder:C},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{selordervisible:!1,lst:[],keynum:0,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],setColumsVisible:!1,isEditOk:!0,selecturl:"",tableForm:x["b"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(){var e=Object(r["a"])(Object(s["a"])().mark((function e(i){var a,o,r;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=i.row,o=i.column,t.editmarkfiles.includes(o.field)&&(t.countfiles.includes(o.field)&&t.changeInput("",a,o.field),r=t.customList.findIndex((function(t){return t.attrkey==o.field})),-1!=r&&t.setAttributeJson(a,a.rownum),t.$forceUpdate());case 2:case"end":return e.stop()}}),e)})));function i(t){return e.apply(this,arguments)}return i}()},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(){var e=Object(r["a"])(Object(s["a"])().mark((function e(i){var a,o,r,n,l;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(i.sourceSelectionData,a=i.targetSelectionData,o=0;o<a.length;o++)r=a[o],n=t.lst.findIndex((function(t){return t.rowKeys==r.rowKeys})),-1!=n&&(t.countfiles.includes(Object.keys(r)[1])&&t.changeInput("",t.lst[n],Object.keys(r)[1]),l=t.customList.findIndex((function(t){return t.attrkey==Object.keys(r)[1]})),-1!=l&&t.setAttributeJson(t.lst[n],n),t.$forceUpdate());case 2:case"end":return e.stop()}}),e)})));function i(t){return e.apply(this,arguments)}return i}()},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;e.selectionRangeIndexes,e.selectionRangeKeys;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var s=JSON.parse(t[i].attributejson);for(a=0;a<s.length;a++)t[i][s[a].key]=s[a].value}}this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=x["b"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(x["b"].formcode,e,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(i,a){var o=i.row,n=(i.column,i.rowIndex,e.customList.findIndex((function(e){return t.itemcode==e.attrkey})));if(-1!=n){var l=e.customList[n].valuejson?e.customList[n].valuejson.split(","):[],d=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]])]),a("div",{directives:[{name:"show",value:l.length}]},[a("div",{style:"width:0;height:0"},[a("el-select",{class:"wpselect",style:"width:0;height:0;opacity: 0;",attrs:{size:"small",id:t.itemcode+o.rownum+x["b"].formcode},on:{change:function(){var i=Object(r["a"])(Object(s["a"])().mark((function i(a){return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:e.selSpuVal(o,t.itemcode,a);case 1:case"end":return i.stop()}}),i)})));return function(t){return i.apply(this,arguments)}}()},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}},[l.map((function(t,e){return a("el-option",{key:e,attrs:{label:t,value:t},style:"max-width:200px"})}))])]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-arrow-down",style:"font-weight:bold;font-size:14px;",on:{click:function(){document.getElementById(t.itemcode+o.rownum+x["b"].formcode).click()}}})])]);return d}if("goodsuid"==t.itemcode){d=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return d}if("status"==t.itemcode){d="";return 0!=o.finishqty&&o.finishqty<o.quantity?d=a("span",{class:"textborder-blue"},[e.formdata.billtype.includes("退货")?"入库":"出库"]):o.finishqty==o.quantity&&0!=o.finishqty?d=a("span",{class:"textborder-green"},["完成"]):1==o.disannulmark?d=a("span",{class:"textborder-grey"},["撤销"]):o.finishclosed&&(d=a("span",{class:"textborder-grey"},["中止"])),d}d=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]);return d}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["goodsuid","amount","quantity","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=this;return Object(r["a"])(Object(s["a"])().mark((function a(){var o,r,n,l,d,m,c;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:for(r in o=[],t[0])o.push(r);for(n=0;n<t.length;n++)for(l=t[n],d=0;d<o.length;d++)m=o[d],i.lst[e+n][m]=l[m].replace(/^\s*|\s*$/g,""),i.countfiles.includes(m)&&i.changeInput("",i.lst[e+n],m),c=i.customList.findIndex((function(t){return t.attrkey==m})),-1!=c&&i.setAttributeJson(i.lst[e+n],e+n),i.$forceUpdate();case 3:case"end":return a.stop()}}),a)})))()},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],s={key:o.attrkey,value:t[o.attrkey]?String(t[o.attrkey]).replace(/\s*/g,""):""};""!=s.value&&i.push(s)}if(0==i.length)this.lst[e].attributejson="";else{this.lst[e].attributejson=JSON.stringify(i);for(a=0;a<i.length;a++)this.$set(this.lst[e],i[a].key,i[a].value)}this.$forceUpdate()},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i),console.log("rwpo"),this.getSummary()},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"finishqty",0),this.$set(e,"closed",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},selSpuVal:function(t,e,i){return Object(r["a"])(Object(s["a"])().mark((function a(){return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:t[e]=i;case 1:case"end":return a.stop()}}),a)})))()},getAdd:function(t){this.formdata.groupid?("发出商品"==this.formdata.billtype?this.selecturl="/D01M03B1/getOnlineDeliPageList?groupid="+this.formdata.groupid+"&appl=1":this.selecturl="/D01M03B1/getRetrunDeliPageList?groupid="+this.formdata.groupid+"&appl=1",this.selordervisible=!0,this.multi=t):this.$message.warning("请选择客户")},SelOrder:function(){var t=this.$refs.SelOrder.selectList;if(console.log(t,"datalst"),0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=i.taxprice?i.taxprice:0,o=i.price?i.price:0,s=i.itemtaxrate?i.itemtaxrate:this.nowitemtaxrate;if("发出商品"==this.formdata.billtype){var r=m["a"].minus(i.quantity,i.finishqty);if(i.finishqty)n=m["a"].minus(i.amount,m["a"].times(i.finishqty,o)),l=m["a"].minus(i.taxamount,m["a"].times(i.finishqty,a));else var n=m["a"].times(r,o),l=m["a"].times(r,a)}else{r=m["a"].minus(t[e].quantity,t[e].outquantity);if(t[e].outquantity)n=m["a"].minus(t[e].amount,m["a"].times(t[e].outquantity,o)),l=m["a"].minus(t[e].taxamount,m["a"].times(t[e].outquantity,a));else var n=m["a"].times(r,o),l=m["a"].times(r,a)}var d=Object.assign({},v);d.goodsid=i.goodsid,d.goodsuid=i.goodsuid,d.goodsname=i.goodsname,d.goodsunit=i.goodsunit,d.goodsspec=i.goodsspec,d.goodsuid=i.goodsuid,d.partid=i.partid,d.itemname=i.itemname,d.itemcode=i.itemcode,d.itemspec=i.itemspec,d.itemunit=i.itemunit,d.goodscustom1=i.custom1,d.goodscustom2=i.custom2,d.goodscustom3=i.custom3,d.goodscustom4=i.custom4,d.goodscustom5=i.custom5,d.goodscustom6=i.custom6,d.goodscustom7=i.custom7,d.goodscustom8=i.custom8,d.goodscustom9=i.custom9,d.goodscustom10=i.custom10,d.custpo=i.custorderid,d.machtype=i.billtype,d.citeitemid=i.id,d.citeuid=i.refno,d.machuid=i.refno,d.machitemid=i.id,d.machdate=i.billdate,d.amount=n,d.itemtaxrate=s,d.price=o,d.quantity=r,d.taxamount=l||0,d.taxprice=a,d.taxtotal=d.taxamount-d.amount,d.virtualitem=i.virtualitem?i.virtualitem:0,d.attributejson=i.attributejson,d.costitemjson=i.costitemjson,d.costgroupjson=i.costgroupjson,0!=this.idx&&(d.pid=this.idx),this.lst.push(d)}}else this.$message.warning("请选择单据内容")}}},F=P,M=(i("8389"),Object(p["a"])(F,y,w,!1,null,"6e7d53cb",null)),I=M.exports,q=i("ea7b"),T=i("dcb4"),L=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","telephone","linkman","deliadd","taxrate","transport","salesman","salesmanid","operator","operatorid","summary","billtaxamount","billtaxtotal","billamount","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],R=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","itemcode","itemname","itemspec","itemunit","quantity","taxprice","taxamount","price","amount","itemtaxrate","taxtotal","stdprice","stdamount","rebate","freeqty","rownum","remark","citeuid","citeitemid","custpo","machtype","salescost","virtualitem","location","batchno","machuid","machitemid","disannulmark","bfitemid","attributejson","machdate","costitemjson","costgroupjson","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10","custom11","custom12","custom13","custom14","custom15","custom16","custom17","custom18"],N={params:L,paramsItem:R},j=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,divided:!1,label:"特权审核",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"approvalByPassAdmin",param:"",children:[]},{show:1,divided:!1,ieval:1,label:'this.formdata.billtype == "订单退货" ? "客退入库" : "发货出库"',icon:"",disabled:"this.formstate!=2",methods:"operateBill",param:"D04M01B2",children:[]},{show:1,divided:!1,label:"销售开票",icon:"",disabled:"this.formstate!=2",methods:"operateBill",param:"D01M05B1",children:[]},{show:1,divided:!1,label:"收款单",icon:"",disabled:"this.formstate!=2",methods:"operateBill",param:"D01M08B1",children:[]}],V=[{show:1,ieval:1,divided:!1,label:'this.formdata.billtype == "订单退货" ? "客退入库" : "发货出库"',icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D04M01B2",label:""},children:[]},{show:1,divided:!1,label:"销售开票",icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D01M05B1",label:"销售开票"},children:[]},{show:1,divided:!1,label:"收款单",icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D01M08B1",label:"收款单"},children:[]}],z=i("910f"),E=i("e845"),H=i("13df"),K=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},A=[],G=(i("3ca3"),i("ddb0"),i("6956")),W=i("b893"),J={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"e845"))}},props:["online","searchVal","isDialog","billcode"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:G["a"],customList:[],selectList:[],progressData:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectList=i?t.lst:[]}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},created:function(){},mounted:function(){this.bindData(),this.getColumn()},methods:{bindData:function(){var t=this;if(this.isDialog){var e={deliuid:this.searchVal};this.queryParams.SearchType=0,this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e}this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(W["d"])()[0],EndDate:Object(W["d"])()[1]});var i="/D01M05B1/getPageList";n["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total)})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(t.tableForm.formcode,G["a"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=a("GoodsInfo",{attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxamount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","quantity","taxamount"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},U=J,Y=(i("9b16"),Object(p["a"])(U,K,A,!1,null,"3dfdb774",null)),X=Y.exports,Q=i("5afc"),Z=i("27f6"),tt={name:"Formedit",components:{FormTemp:T["a"],EditHeader:g,EditItem:I,D01M05B1:E["default"],D01M08B1:z["default"],D04M01B2:H["default"],D04M01B2List:Z["a"],D01M05B1List:X,D01M08B1List:Q["a"]},props:["idx","isDialog","initData","billcode","isprocessDialog"],data:function(){return{title:"订单发货",operateBar:j,processBar:V,formdata:{assessdate:"",assessor:"",billamount:0,billdate:new Date,billreceived:0,billstatecode:"",billstatedate:"",billtaxamount:0,billtaxtotal:0,billtitle:"",billtype:"发出商品",deliadd:"",disannulmark:0,groupid:"",groupname:"",linkman:"",operator:"",refno:"",salesman:"",summary:"",taxrate:0,telephone:"",tenantid:"",transport:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},D01M05B1Visible:!1,operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:q["a"],formstate:0,submitting:0,stockwarndata:[],stockwarningvisible:!1}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D01M06B1").then((function(e){200==e.data.code&&(null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):q["a"],t.formtemplate.footer.type||(t.formtemplate.footer=q["a"].footer)),console.log(t.formtemplate," this.formtemplate"))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&n["a"].get("/D01M06B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.$refs.elitem.multipleSelection=[],t.$refs.elitem.checkboxOption.selectedRowKeys=[],t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.salesman=t.seller,this.formdata.abbreviate=t.abbreviate,this.formdata.grouplevel=t.grouplevel,this.formdata.linkman=t.linkman,this.formdata.telephone=t.telephone,this.formdata.deliadd=t.deliveradd},autoClear:function(t){this.formdata.groupname="",this.formdata.groupid="",this.formdata.salesman="",this.formdata.abbreviate="",this.formdata.grouplevel="",this.formdata.linkman="",this.formdata.telephone="",this.formdata.deliadd=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){try{this.$refs.elitem.$refs.multipleTable.stopEditingCell()}catch(o){}for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.billamount=i.billamount,this.formdata.billtaxamount=i.billtaxamount,this.formdata.billtaxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(N,a,this.formdata),0==this.idx?d.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.submitting=0,t.$message.warning(e||"保存失败")})):d.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){d.delete(t)})).catch((function(){}))},approval:function(){d.approval(this)},approvalRequest:function(t){var e=this;return Object(r["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:d.approval(e);case 1:case"end":return t.stop()}}),t)})))()},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?d.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){d.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){console.log(t,"vallll"),this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t;var e=this.$refs.elitem.multipleSelection;"D04M01B2"===t&&e.length>0&&(this.formdata.item=e)},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},changeBillType:function(){formdata.item=[]},approvalByPassAdmin:function(){var t=this;n["a"].get("/D01M06B1/approvalByPassAdmin?key="+this.formdata.id).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData")):t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败"))}))},billSwitch:function(t){var e=this.$store.getters.userinfo.configs;if("D01M03B1"==t){this.formdata.billtype="发出商品",this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】销售订单转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billtaxamount=this.initData.billtaxamount,this.formdata.billamount=this.initData.billamount,this.formdata.billtaxtotal=this.initData.billtaxtotal,this.formdata.taxrate=this.initData.taxrate,this.formdata.transport=this.initData.logisticsmode,this.formdata.deliadd=this.initData.logisticsport,this.formdata.operator=this.initData.operator,this.formdata.salesman=this.initData.salesman,this.formdata.assessor="",this.formdata.billdate=new Date,this.formdata.item=[];for(var i=0;i<this.initData.item.length;i++){var a=this.initData.item[i],o=Object.assign({},v);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.goodsuid=a.goodsuid,o.partid=a.partid,o.itemname=a.itemname,o.itemcode=a.itemcode,o.itemspec=a.itemspec,o.itemunit=a.itemunit,o.goodscustom1=a.custom1,o.goodscustom2=a.custom2,o.goodscustom3=a.custom3,o.goodscustom4=a.custom4,o.goodscustom5=a.custom5,o.goodscustom6=a.custom6,o.goodscustom7=a.custom7,o.goodscustom8=a.custom8,o.goodscustom9=a.custom9,o.goodscustom10=a.custom10,o.custpo=this.initData.custorderid,o.machtype=this.initData.billtype,o.citeitemid=a.id,o.citeuid=this.initData.refno,o.machuid=this.initData.refno,o.machitemid=a.id,o.machdate=this.initData.billdate,o.machgroupid=this.initData.groupid,o.mrpuid=a.mrpuid,o.mrpitemid=a.mrpitemid,o.finishclosed=a.virtualitem?1:0,o.itemtaxrate=a.itemtaxrate,o.price=a.price,o.quantity=m["a"].minus(a.quantity,a.finishqty),o.amount=a.finishqty?m["a"].minus(a.amount,m["a"].times(a.finishqty,a.price)):a.amount,o.taxamount=a.finishqty?m["a"].minus(a.taxamount,m["a"].times(a.finishqty,a.taxprice)):a.taxamount,o.taxprice=a.taxprice,o.taxtotal=a.taxtotal,o.rebate=a.rebate,o.remark=a.remark,o.virtualitem=a.virtualitem?a.virtualitem:0,o.attributejson=a.attributejson,o.costitemjson=a.costitemjson,o.costgroupjson=a.costgroupjson,o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):this.formdata.item.push(o)}}else if("D01M03B1List"==t){console.log(this.initData),this.formdata.groupid=this.initData[0].groupid,this.formdata.groupid&&(this.formdata.groupname=this.initData[0].groupname,this.formdata.groupuid=this.initData[0].groupuid),this.formdata.transport=this.initData[0].logisticsmode,this.formdata.deliadd=this.initData[0].logisticsport,this.formdata.operator=this.initData[0].operator,this.formdata.salesman=this.initData[0].salesman,this.formdata.assessor="",this.formdata.billdate=new Date,this.formdata.item=[];for(i=0;i<this.initData.length;i++){a=this.initData[i],o=Object.assign({},v);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.goodsuid=a.goodsuid,o.partid=a.partid,o.itemname=a.itemname,o.itemcode=a.itemcode,o.itemspec=a.itemspec,o.itemunit=a.itemunit,o.goodscustom1=a.custom1,o.goodscustom2=a.custom2,o.goodscustom3=a.custom3,o.goodscustom4=a.custom4,o.goodscustom5=a.custom5,o.goodscustom6=a.custom6,o.goodscustom7=a.custom7,o.goodscustom8=a.custom8,o.goodscustom9=a.custom9,o.goodscustom10=a.custom10,o.custpo=a.custorderid,o.machtype=a.billtype,o.citeitemid=a.id,o.citeuid=a.refno,o.machuid=a.refno,o.machitemid=a.id,o.machdate=a.billdate,o.machgroupid=a.groupid,o.mrpuid=a.mrpuid,o.mrpitemid=a.mrpitemid,o.finishclosed=a.virtualitem?1:0,o.itemtaxrate=a.itemtaxrate,o.price=a.price,o.amount=a.finishqty?m["a"].minus(a.amount,m["a"].times(a.finishqty,a.price)):a.amount,o.itemtaxrate=a.itemtaxrate,o.price=a.price,o.quantity=m["a"].minus(a.quantity,a.finishqty),o.taxamount=a.finishqty?m["a"].minus(a.taxamount,m["a"].times(a.finishqty,a.taxprice)):a.taxamount,o.taxprice=a.taxprice,o.taxtotal=o.taxamount-o.amount,o.rebate=a.rebate,o.remark=a.remark,o.virtualitem=a.virtualitem?a.virtualitem:0,o.attributejson=a.attributejson,o.costitemjson=a.costitemjson,o.costgroupjson=a.costgroupjson,o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):this.formdata.item.push(o)}}}}},et=tt,it=(i("3f80"),Object(p["a"])(et,a,o,!1,null,"6aa20eae",null));e["default"]=it.exports},"5afc":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"border-x":"","border-y":"","border-around":!0,"column-width-resize-option":t.columnWidthResizeOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},o=[],s=i("c7eb"),r=i("1da1"),n=(i("caad"),i("e9c4"),i("a9e3"),i("d3b7"),i("2532"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),i("b775")),l=i("f944"),d=i("b893"),m={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"910f"))}},props:["searchVal","isDialog","billcode"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:l["c"],customList:[],progressData:[],columnHidden:[],columnWidthResizeOption:{enable:!0,minWidth:50,sizeChange:function(e){var i=e.column,a=e.differWidth,o=e.columnWidth;t.columnResizeInfo.column=i,t.columnResizeInfo.differWidth=a,t.columnResizeInfo.columnWidth=o}},columnResizeInfo:{column:"",differWidth:"",columnWidth:""},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},footerData:[],customData:[],eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}},cellTotal:0,cellNum:0}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},created:function(){},mounted:function(){this.bindData(),this.getColumn()},methods:{bindData:function(){var t=this;if(this.isDialog){var e={citecode:this.searchVal};this.queryParams.SearchType=0,this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e}var i="/D01M08B1/getPageTh";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(d["d"])()[0],EndDate:Object(d["d"])()[1]}),n["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total)})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(t.tableForm.formcode,l["c"]).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("billtype"==t.itemcode){s=a("span",[o[t.itemcode]]);return o[t.itemcode].includes("红冲")&&(s=a("span",{style:"color:#f44336"},[o[t.itemcode]])),s}if("assessor"==t.itemcode){s="";return s=o.oaflowmark&&!o.assessor?a("span",{style:"color:#ff9800"},["审核中"]):a("span",[o.assessor]),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxprice","taxamount"];this.$countCellData(this,i,t,e)},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},c=m,u=(i("2399"),i("2877")),f=Object(u["a"])(c,a,o,!1,null,"520e7e04",null);e["a"]=f.exports},"5caf":function(t,e,i){"use strict";i("a426")},"5cc6":function(t,e,i){var a=i("74e8");a("Uint8",(function(t){return function(e,i,a){return t(this,e,i,a)}}))},"5fe0":function(t,e,i){},"682f":function(t,e,i){"use strict";i("f1fd")},"68bd":function(t,e,i){},6956:function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r}));var a={formcode:"D01M05B1Th",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Bus_Invoice.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.billdate"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"invocode",itemname:"发票编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.invocode"},{itemcode:"aimdate",itemname:"收款计划",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.aimdate"},{itemcode:"taxrate",itemname:"税率%",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.taxrate"},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.taxamount"},{itemcode:"receipted",itemname:"已收金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.receipted"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,datasheet:"Bus_Invoice.status"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.assessor"}]},o={formcode:"D01M05B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Bus_Invoice.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.billdate"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"deliuid",itemname:"发货单号",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.deliuid"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.quantity"},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.itemtaxrate"},{itemcode:"taxamount",itemname:"金额",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.taxamount"}]},s={formcode:"D01M05B1Item",item:[{itemcode:"deliuid",itemname:"发货单号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billqty",itemname:"单据数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"本次数量",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"machuid",itemname:"销售单",minwidth:"100",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D01M05B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Bus_Invoice.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.billdate"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"deliuid",itemname:"发货单号",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.deliuid"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.quantity"},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.itemtaxrate"},{itemcode:"taxamount",itemname:"金额",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.taxamount"}]}},"6bae":function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"预收款",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"销售预收",value:"销售预收"},{label:"其他预收",value:"其他预收"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"客户",searchtype:"customer",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"billamount",label:"预收金额",type:"text",methods:"",param:""}]}]},cash:{type:0,content:[{itemcode:"cashaccname",itemname:"出纳账户",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"amount",itemname:"金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"业务员",billcode:"sale.salesman",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},list:{type:0,content:[]},item:{type:0,content:[]}}},"6c92":function(t,e,i){"use strict";i("0041")},"6ccf":function(t,e,i){},"703e":function(t,e,i){},"735b":function(t,e,i){"use strict";i.d(e,"c",(function(){return a})),i.d(e,"b",(function(){return o})),i.d(e,"a",(function(){return s}));var a={formcode:"D01M06B2Th",item:[{itemcode:"refno",itemname:"发货单号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Bus_Deliery.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtitle"},{itemcode:"billdate",itemname:"发货日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deliery.billdate"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"linkman",itemname:"联系人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.linkman"},{itemcode:"telephone",itemname:"联系电话",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.telephone"},{itemcode:"billtaxamount",itemname:"含税金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtaxamount"},{itemcode:"transport",itemname:"运输方式",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.transport"},{itemcode:"salesman",itemname:"业务员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.salesman"},{itemcode:"summary",itemname:"摘要",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.summary"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.status"},{itemcode:"itemcount",itemname:"款数",sortable:1,minwidth:"60",displaymark:1,overflow:1,datasheet:"Bus_Deliery.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.assessor"}]},o={formcode:"D01M06B2List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Bus_Deliery.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deliery.billdate"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_DelieryItem.quantity"},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_DelieryItem.taxprice"},{itemcode:"taxamount",itemname:"含税金额",sortable:1,minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_DelieryItem.taxamount"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1}]},s={formcode:"D01M06B2Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:0,overflow:1},{itemcode:"finishqty",itemname:"已出入库",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]}},"736e":function(t,e,i){},"737c":function(t,e,i){"use strict";i("e7c1")},"78a4":function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"销售订单",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",billcode:"sale.machtype",type:"dictionary",methods:"",param:"",required:!0},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""},{col:5,code:"custorderid",label:"客户订单号",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"客户",searchtype:"customer",type:"autocomplete",methods:"",param:"",required:!0},{col:5,code:"billplandate",label:"计划时间",type:"date",methods:"",param:""}]},{rowitem:[{col:5,code:"logisticsmode",label:"交货方式",type:"input",methods:"",param:""},{col:5,code:"logisticsport",label:"交货地址",type:"input",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"salesman",label:"业务员",billcode:"sale.salesman",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},7996:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据时间",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormat")(e.row.billdate)))])]}}])}),i("el-table-column",{attrs:{label:"客户",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"单据金额",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtaxamount))])]}}])}),i("el-table-column",{attrs:{label:"业务员",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.salesman))])]}}])}),i("el-table-column",{attrs:{label:"款数",align:"center",width:"60px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-popover",{attrs:{placement:"left",trigger:"click",title:"单据明细"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:e.$index==t.mypopoverIndex,expression:"scope.$index == mypopoverIndex"}],staticStyle:{position:"relative","min-height":"100px"}},[i("mypopover",{ref:"mypopover",attrs:{tableForm:t.mypopoverTable,lst:t.mypopoverData}})],1),i("span",{staticClass:"textunderline",attrs:{slot:"reference"},on:{click:function(i){return i.stopPropagation(),t.getBillList(e.row,e.$index)}},slot:"reference"},[t._v(" "+t._s(e.row.itemcount)+" ")])])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],s=(i("e9c4"),i("b64b"),i("b775")),r=i("333d"),n=i("40d9"),l=i("f07e"),d={components:{Pagination:r["a"],mypopover:l["a"]},props:["multi","selecturl"],data:function(){return{title:"销售订单",listLoading:!0,lst:[],strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},mypopoverTable:n["c"],mypopoverData:[],mypopoverIndex:-1}},created:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D01M03B1/getPageTh";s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},getBillList:function(t,e){var i=this;this.mypopoverIndex=e,s["a"].get("/D01M03B1/getBillEntity?key=".concat(t.id)).then((function(t){if(200==t.data.code){i.mypopoverData=t.data.data.item;for(var e=0;e<i.mypopoverData.length;e++){var a=i.mypopoverData[e];if(""==a.attributejson||null==a.attributejson);else for(var o=JSON.parse(a.attributejson),s=0;s<o.length;s++)a[o[s].key]=o[s].value}}else i.mypopoverData=[]}))}}},m=d,c=(i("1d9e"),i("2877")),u=Object(c["a"])(m,a,o,!1,null,"194684bb",null);e["a"]=u.exports},"7da1":function(t,e,i){},"7dfe":function(t,e,i){"use strict";i("bf1d")},"80b3":function(t,e,i){},8377:function(t,e,i){},8389:function(t,e,i){"use strict";i("06c72")},"841c":function(t,e,i){"use strict";var a=i("d784"),o=i("825a"),s=i("1d80"),r=i("129f"),n=i("14c3");a("search",1,(function(t,e,i){return[function(e){var i=s(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,i):new RegExp(e)[t](String(i))},function(t){var a=i(e,t,this);if(a.done)return a.value;var s=o(t),l=String(this),d=s.lastIndex;r(d,0)||(s.lastIndex=0);var m=n(s,l);return r(s.lastIndex,d)||(s.lastIndex=d),null===m?-1:m.index}]}))},8576:function(t,e,i){"use strict";i("2ee7")},8661:function(t,e,i){},"89cb":function(t,e,i){},"8d63":function(t,e,i){},"8dce":function(t,e,i){"use strict";i("b5cb")},"910f":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{writedate:!1,formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,changeBillType:t.changeBillType}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px",height:"100%"}},[i("EditCash",{ref:"cashitem",style:{width:"99%",height:"其他收款"!=t.formdata.billtype?"50%":"100%"},attrs:{lstitem:t.formdata.cash,formdata:t.formdata,formtemplateItem:t.formtemplate.cash,idx:t.idx,formstate:t.formstate,itemAmount:t.itemAmount},on:{bindData:t.bindData,computerCashAmount:t.computerCashAmount}}),"其他收款"!=t.formdata.billtype?i("EditItem",{ref:"elitem",staticStyle:{width:"99%",height:"48%","margin-top":"10px"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData,computerItemAmount:t.computerItemAmount}}):t._e()],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D01M08B1Edit",commonurl:"/D01M08B1/printBill",weburl:"/D01M08B1/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D01M08B1Edit",examineurl:"/D01M08B1/sendapprovel"}}),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D01M08B1"==t.processModel?i("D01M08B1List",{ref:"D01M08B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0,isHongchong:!0}}):t._e()],1):t._e()],1)},o=[],s=i("2909"),r=(i("e9c4"),i("a9e3"),i("b64b"),i("d3b7"),i("ac1f"),i("6062"),i("3ca3"),i("5319"),i("159b"),i("ddb0"),i("b775"));const n={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D01M08B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D01M08B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){r["a"].get("/D01M08B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,r["a"].get("/D01M08B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D01M08B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D01M08B1/closed?type="+(3==t?1:0);r["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var l=n,d=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){return t.$emit("changeBillType")}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"销售收款",value:"销售收款"}}),i("el-option",{attrs:{label:"单据收款",value:"单据收款"}}),i("el-option",{attrs:{label:"其他收款",value:"其他收款"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"客户",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B1/getOnlinePageList",type:"客户"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"收款金额："}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.billamount))])])],1)],1)],1)},m=[],c={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupname:[{required:!0,trigger:"blur",message:"客户为必填项"}],operator:[{required:!0,trigger:"blur",message:"经办人为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},u=c,f=(i("4630"),i("2877")),h=Object(f["a"])(u,d,m,!1,null,"6bee69f1",null),p=h.exports,b=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"销售开票","append-to-body":!0,visible:t.selordervisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"selOrder",attrs:{multi:1,selecturl:"/D01M05B1/getOnlinePageTh?groupid="+t.formdata.groupid+"&appl=1"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.deliveryvisible?i("el-dialog",{attrs:{title:"发货单","append-to-body":!0,visible:t.deliveryvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.deliveryvisible=e}}},[i("SelDelivery",{ref:"selDelivery",attrs:{multi:1,selecturl:"/D01M06R1/getOnlineRecePageTh?groupid="+t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selDelivery()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.deliveryvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},g=[],y=(i("c740"),i("caad"),i("2532"),i("c7cd"),{amount:0,custom1:"",custom10:"",custom2:"",custom4:"",custom5:"",custom3:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",invoamount:0,invobillcode:"",invocode:"",invoid:"",pid:"",remark:"",rownum:0}),w={amount:0,cashaccid:"",cashaccname:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",pid:"",remark:"",rownum:0},v=i("f944"),x=i("fb0b"),k=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"发货单号",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据主题",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtitle))])]}}])}),i("el-table-column",{attrs:{label:"发货日期",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormat")(e.row.billdate)))])]}}])}),i("el-table-column",{attrs:{label:"客户",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"含税金额",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtaxamount))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},D=[],S=i("333d"),$={components:{Pagination:S["a"]},props:["multi","groupid","selecturl"],data:function(){return{title:"发货信息",listLoading:!0,lst:[],strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.bindData()},methods:{getCurrentRow:function(t){console.log(t),this.$forceUpdate(),this.selrows=t,this.$emit("singleSel")},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D01M06B1/getPageTh";r["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,groupname:t,billtype:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},_=$,B=(i("94ef"),Object(f["a"])(_,k,D,!1,null,"b75c7e7e",null)),O=B.exports,C={name:"Elitem",components:{SelOrder:x["a"],SelDelivery:O},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],selordervisible:!1,deliveryvisible:!1,setColumsVisible:!1,keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:v["d"],customList:[],editmarkfiles:[],countfiles:["invoamount","amount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=v["d"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(v["d"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(e,i){var a=e.row;e.column,e.rowIndex;if("goodsuid"==t.itemcode){var o=i("GoodsInfo",{class:a.disannulmark?"textlinethrough":"",attrs:{scopeVal:a[t.itemcode]}});return o}o=i("span",{class:a.disannulmark?"textlinethrough":""},[a[t.itemcode]]);return o}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["machbillcode","invoamount","amount"]);var t=this.footerData[0].amount;this.$emit("computerItemAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.formdata.groupid?"销售收款"==this.formdata.billtype?this.selordervisible=!0:this.deliveryvisible=!0:this.$message.warning("请选择客户")},selOrder:function(){var t=this.$refs.selOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},y);a.amount=this.$fomatFloat(i.taxamount-i.receipted,2),a.invoamount=i.taxamount,a.invobillcode=i.refno,a.invocode=i.invocode,a.invoid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selDelivery:function(){var t=this.$refs.selDelivery.$refs.selectVal.selection;if(0!=t.length){this.deliveryvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},y);a.amount=this.$fomatFloat(i.billtaxamount-i.billreceived,2),a.invoamount=i.billtaxamount,a.invobillcode=i.refno,a.invoid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},P=C,F=(i("063e"),Object(f["a"])(P,b,g,!1,null,"0e15a670",null)),M=F.exports,I=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"出纳账户","append-to-body":!0,visible:t.selordervisible,width:"1000px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.SelOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},q=[],T=i("233f"),L={name:"Elitem",components:{SelOrder:T["a"]},props:["formdata","lstitem","idx","itemAmount","formstate","formtemplateItem"],data:function(){var t=this;return{selordervisible:!1,setColumsVisible:!1,lst:[],keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:v["b"],customList:[],editmarkfiles:[],countfiles:["amount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=v["b"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(v["b"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(e,i){var a=e.row;e.column,e.rowIndex;if("goodsuid"==t.itemcode){var o=i("GoodsInfo",{class:a.disannulmark?"textlinethrough":"",attrs:{scopeVal:a[t.itemcode]}});return o}o=i("span",{class:a.disannulmark?"textlinethrough":""},[a[t.itemcode]]);return o}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["cashaccname","amount"]);var t=this.footerData[0].amount;this.$emit("computerCashAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.selordervisible=!0},SelOrder:function(){var t=this.$refs.SelOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},w);a.amount=this.itemAmount,a.cashaccid=i.id,a.cashaccname=i.accountname,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},R=L,N=(i("2a69"),Object(f["a"])(R,I,q,!1,null,"61143e4c",null)),j=N.exports,V=i("0276"),z=i("dcb4"),E=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","billamount","operator","citecode","returnuid","orguid","summary","fmdoccode","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],H=["id","pid","invoid","invobillcode","invocode","invoamount","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],K=["id","pid","cashaccid","cashaccname","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],A={params:E,paramsItem:H,paramsCash:K},G=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1? true : !!this.formdata.orguid ? true : !!this.formdata.returnuid",methods:"deleteForm",param:"",children:[]},{show:1,divided:!1,label:"红 冲",icon:"el-icon-edit-outline",disabled:"!this.formdata.id? true : !!this.formdata.orguid ? true : !!this.formdata.returnuid",methods:"hongChong",param:"",children:[]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]}],W=[{show:1,divided:!1,label:"红冲记录",icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D01M08B1",label:"红冲记录"},children:[]}],J={name:"Formedit",components:{FormTemp:z["a"],EditHeader:p,EditItem:M,EditCash:j,D01M08B1List:function(){return Promise.all([i.e("chunk-commons"),i.e("chunk-dcfb6afa")]).then(i.bind(null,"f18a"))}},props:["idx","isDialog","initData","billcode","isprocessDialog"],data:function(){return{title:"收款单",operateBar:G,processBar:W,formdata:{abbreviate:"",billamount:0,assessdate:new Date,assessor:"",billdate:new Date,billtitle:"",billtype:"销售收款",citecode:"",createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,createdate:"",groupid:"",groupname:"",groupuid:"",item:[],cash:[],lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,operator:"",orguid:"",outamount:0,refno:"",returnuid:"",revision:0,summary:""},itemAmount:0,cashAmount:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:V["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp(),this.bindData()},mounted:function(){this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D01M08B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):V["a"],t.formtemplate.cash=V["a"].cash,t.formtemplate.footer.type||(t.formtemplate.footer=V["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&r["a"].get("/D01M08B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1,"其他收款"==t.formdata.billtype&&t.$refs.cashitem.catchHight()):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},computerItemAmount:function(t){this.itemAmount=t},computerCashAmount:function(t){this.cashAmount=t,this.formdata.billamount=Number(this.cashAmount)},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if("其他收款"!=this.formdata.billtype){if(this.formdata.billamount!=this.itemAmount||this.formdata.billamount!=this.cashAmount)return void this.$message.warning("收款金额 与 出纳金额和发票金额不一致");this.formdata.item=this.$refs.elitem.lst,this.formdata.citecode="";for(var e="",i=0;i<this.formdata.item.length;i++){var a=this.formdata.item[i];e+=a.invobillcode+","}var o=/,$/gi;e=e.replace(o,"");var r=e.split(","),n=Object(s["a"])(new Set(r));for(i=0;i<n.length;i++)this.formdata.citecode+=n[i]+",";this.formdata.citecode=this.formdata.citecode.replace(o,"")}this.submitting=1,this.formdata.cash=this.$refs.cashitem.lst;var d={item:[],cash:[]};d=this.$getParam(A,d,this.formdata),0==this.idx?l.add(d).then((function(e){t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0})):l.update(d).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0}))},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.delete(e)})).catch((function(){}))},approval:function(){l.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?l.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.item=[],this.formdata.cash=[]},autoClear:function(t){this.formdata.groupname="",this.formdata.groupid=""},changeBillType:function(){this.formdata.cash=[],this.formdata.item=[],this.formdata.taxamount=0,this.$refs.cashitem.catchHight()},hongChong:function(){var t=this;console.log("红冲"),this.$confirm("红冲后不可退回，是否确认将该单据转为红冲单？","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((function(){var e={item:[],cash:[]};e=t.$getParam(A,e,t.formdata),e.billtype="收款红冲",e.billtitle="【"+t.formdata.refno+"】收款单转入",e.orguid=t.formdata.refno,e.billamount=0-t.formdata.billamount,e.outamount=0,e.billdate=new Date,t.$delete(e,"refno"),t.$delete(e,"id"),t.$delete(e,"operator"),e.cash.forEach((function(e,i){e.amount=0-t.formdata.cash[i].amount,t.$delete(e,"id"),t.$delete(e,"pid")})),e.item.forEach((function(e,i){e.amount=0-t.formdata.item[i].amount,t.$delete(e,"id"),t.$delete(e,"pid")})),r["a"].post("/D01M08B1/create",JSON.stringify(e)).then((function(e){console.log(e),200==e.data.code?(t.$message.success(e.data.msg||"红冲成功"),t.$emit("changeIdx",e.data.data.id),t.$emit("bindData"),t.bindData()):t.$message.warning(e.data.msg||"红冲失败")})).catch((function(e){t.$message.warning(e||"红冲失败")}))})).catch((function(){}))},billSwitch:function(t){if("D01M05B1"==t){this.formdata.billtype="销售收款",this.formdata.billtitle="【"+this.initData.refno+"】销售开票单转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billamount=this.initData.billtaxamount,this.formdata.citecode=this.initData.refno,this.formdata.item=[];var e={invoamount:this.initData.taxamount,amount:this.$fomatFloat(this.initData.taxamount-this.initData.receipted,2),invoid:this.initData.id,invobillcode:this.initData.refno,invocode:this.initData.invocode,virtualitem:this.initData.virtualitem};this.formdata.item.push(e)}else if("D01M06B1"==t){this.formdata.billtype="单据收款",this.formdata.billtitle="【"+this.initData.refno+"】订单发货转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.citecode=this.initData.refno,this.formdata.billamount=this.initData.billtaxamount,this.formdata.item=[];e={invoamount:this.initData.billtaxamount,amount:this.$fomatFloat(Number(this.initData.billtaxamount)-Number(this.initData.billreceived),2),invoid:this.initData.id,invobillcode:this.initData.refno,invocode:"",virtualitem:this.initData.virtualitem};this.formdata.item.push(e)}}}},U=J,Y=(i("c026"),Object(f["a"])(U,a,o,!1,null,"09af63ca",null));e["default"]=Y.exports},"94ef":function(t,e,i){"use strict";i("aff2")},"99fe":function(t,e,i){"use strict";i("7da1")},"9a71":function(t,e,i){"use strict";i("ab2b")},"9b16":function(t,e,i){"use strict";i("68bd")},a3db:function(t,e,i){"use strict";i("04f2")},a426:function(t,e,i){},a5ab:function(t,e,i){},a5e0:function(t,e,i){},a9bb:function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"销售扣款",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"销售扣款",value:"销售扣款"},{label:"其他扣款",value:"其他扣款"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"客户",searchtype:"customer",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"billtaxamount",label:"总金额",type:"text",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"salesman",label:"业务员",billcode:"sale.salesman",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},aa59:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{writedate:!1,formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,changeBillType:t.changeBillType}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px",height:"100%"}},[i("EditCash",{ref:"cashitem",style:{width:"99%",height:"其他预收"!=t.formdata.billtype?"50%":"100%"},attrs:{lstitem:t.formdata.cash,formdata:t.formdata,formtemplateItem:t.formtemplate.cash,idx:t.idx,formstate:t.formstate,itemAmount:t.itemAmount},on:{bindData:t.bindData,computerCashAmount:t.computerCashAmount}}),"其他预收"!=t.formdata.billtype?i("EditItem",{ref:"elitem",staticStyle:{width:"99%",height:"48%","margin-top":"10px"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData,computerItemAmount:t.computerItemAmount}}):t._e()],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D01M08B1DEPEdit",commonurl:"/D01M08B1DEP/printBill",weburl:"/D01M08B1DEP/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D01M08B1DEPEdit",examineurl:"/D01M08B1DEP/sendapprovel"}}),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D01M03B1"==t.processModel?i("D01M03B1List",{ref:"D01M03B1List",attrs:{searchVal:t.formdata.citecode,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=i("2909"),r=(i("a9e3"),i("b64b"),i("d3b7"),i("ac1f"),i("6062"),i("3ca3"),i("5319"),i("ddb0"),i("b775"));const n={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D01M08B1DEP/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D01M08B1DEP/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){r["a"].get("/D01M08B1DEP/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,r["a"].get("/D01M08B1DEP/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D01M08B1DEP/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D01M08B1DEP/closed?type="+(3==t?1:0);r["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var l,d=n,m=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){return t.$emit("changeBillType")}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"销售预收",value:"销售预收"}}),i("el-option",{attrs:{label:"其他预收",value:"其他预收"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"客户",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B1/getOnlinePageList",type:"客户"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"预收金额"}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.billamount))])])],1)],1)],1)},c=[],u={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupname:[{required:!0,trigger:"blur",message:"客户为必填项"}],operator:[{required:!0,trigger:"blur",message:"经办人为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},f=u,h=(i("b9c6"),i("2877")),p=Object(h["a"])(f,m,c,!1,null,"3c40d3c4",null),b=p.exports,g=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","copyrow","refresh"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"订单信息","append-to-body":!0,visible:t.selordervisible,width:"1200px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1,selecturl:"/D01M03B1/getOnlineAdvaPageTh?groupid="+t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.SelOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},y=[],w=(i("c740"),i("caad"),i("e9c4"),i("2532"),i("c7cd"),i("159b"),i("ade3")),v=(l={amount:0},Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(l,"amount",0),"billtaxamount",0),"custom1",""),"custom10",""),"custom2",""),"custom3",""),"custom4",""),"custom5",""),"custom6",""),"custom7",""),Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(Object(w["a"])(l,"custom8",""),"custom9",""),"delibillcode",""),"delibillid",""),"id",""),"machbillcode",""),"machbillid",""),"pid",""),"remark",""),"rownum",0)),x={amount:0,cashaccid:"",cashaccname:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",pid:"",remark:"",rownum:0},k=i("49b1"),D=i("7996"),S={name:"Elitem",components:{SelOrder:D["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],selordervisible:!1,setColumsVisible:!1,keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:k["c"],customList:[],editmarkfiles:[],countfiles:["amount","billtaxamount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=k["c"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(k["c"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(e,i){var a=e.row;e.column,e.rowIndex;if("goodsuid"==t.itemcode){var o=i("GoodsInfo",{class:a.disannulmark?"textlinethrough":"",attrs:{scopeVal:a[t.itemcode]}});return o}o=i("span",{class:a.disannulmark?"textlinethrough":""},[a[t.itemcode]]);return o}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["machbillcode","amount","billtaxamount"]);var t=this.footerData[0].amount;this.$emit("computerItemAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.formdata.groupid?this.selordervisible=!0:this.$message.warning("请选择客户")},SelOrder:function(){var t=this.$refs.SelOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},v);a.amount=i.billtaxamount-i.advaamount,a.billtaxamount=i.billtaxamount,a.machbillcode=i.refno,a.machbillid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},$=S,_=(i("28f6"),Object(h["a"])($,g,y,!1,null,"3d6ab2b1",null)),B=_.exports,O=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"出纳账户","append-to-body":!0,visible:t.selordervisible,width:"1000px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.SelOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},C=[],P=i("233f"),F={name:"Elitem",components:{SelOrder:P["a"]},props:["formdata","lstitem","idx","itemAmount","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],selordervisible:!1,setColumsVisible:!1,keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:k["a"],customList:[],editmarkfiles:[],countfiles:["amount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=k["a"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(k["a"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(e,i){var a=e.row;e.column,e.rowIndex;if("goodsuid"==t.itemcode){var o=i("GoodsInfo",{class:a.disannulmark?"textlinethrough":"",attrs:{scopeVal:a[t.itemcode]}});return o}o=i("span",{class:a.disannulmark?"textlinethrough":""},[a[t.itemcode]]);return o}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["cashaccname","amount"]);var t=this.footerData[0].amount;this.$emit("computerCashAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.selordervisible=!0},SelOrder:function(){var t=this.$refs.SelOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},x);a.amount=this.itemAmount,a.cashaccid=i.id,a.cashaccname=i.accountname,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},M=F,I=(i("b3dd"),Object(h["a"])(M,O,C,!1,null,"29c7bd7b",null)),q=I.exports,T=i("6bae"),L=i("dcb4"),R=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","billamount","operator","citecode","outamount","returnuid","orguid","summary","fmdoccode","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],N=["id","pid","machbillid","machbillcode","delibillid","delibillcode","billtaxamount","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],j=["id","pid","cashaccid","cashaccname","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],V={params:R,paramsItem:N,paramsCash:j},z=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:0,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]}],E=[],H=i("aaa1"),K={name:"Formedit",components:{FormTemp:L["a"],EditHeader:b,EditItem:B,EditCash:q,D01M03B1List:H["default"]},props:["idx","isDialog","initData","billcode","isprocessDialog"],data:function(){return{title:"预收款",operateBar:z,processBar:E,formdata:{abbreviate:"",billamount:0,assessdate:new Date,assessor:"",billdate:new Date,billtitle:"",billtype:"销售预收",citecode:"",createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,createdate:"",groupid:"",groupname:"",groupuid:"",item:[],cash:[],lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,operator:"",orguid:"",outamount:0,refno:"",returnuid:"",revision:0,summary:""},formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupname:[{required:!0,trigger:"blur",message:"客户为必填项"}],operator:[{required:!0,trigger:"blur",message:"经办人为必填项"}]},itemAmount:0,cashAmount:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:T["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp(),this.bindData()},mounted:function(){this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D01M08B1DEP").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):T["a"],t.formtemplate.cash=T["a"].cash,t.formtemplate.footer.type||(t.formtemplate.footer=T["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&r["a"].get("/D01M08B1DEP/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1,"其他预收"==t.formdata.billtype&&t.$refs.cashitem.catchHight()):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},computerItemAmount:function(t){this.itemAmount=t},computerCashAmount:function(t){this.cashAmount=t,this.formdata.billamount=Number(this.cashAmount)},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if("其他预收"!=this.formdata.billtype){if(console.log(this.formdata,this.itemAmount),this.cashAmount,this.formdata.billamount!=this.itemAmount||this.formdata.billamount!=this.cashAmount)return void this.$message.warning("预收金额 与 出纳金额和单据金额不一致");this.formdata.item=this.$refs.elitem.lst,this.formdata.citecode="";for(var e="",i=0;i<this.formdata.item.length;i++){var a=this.formdata.item[i];e+=a.machbillcode+","}var o=/,$/gi;e=e.replace(o,"");var r=e.split(","),n=Object(s["a"])(new Set(r));for(i=0;i<n.length;i++)this.formdata.citecode+=n[i]+",";this.formdata.citecode=this.formdata.citecode.replace(o,"")}this.submitting=1,this.formdata.cash=this.$refs.cashitem.lst;var l={item:[],cash:[]};l=this.$getParam(V,l,this.formdata),0==this.idx?d.add(l).then((function(e){t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败")})):d.update(l).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){d.delete(e)})).catch((function(){}))},approval:function(){d.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?d.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){d.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t,console.log(this.processModel,"ss")},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.item=[],this.formdata.cash=[]},autoClear:function(t){this.formdata.groupname="",this.formdata.groupid=""},changeBillType:function(){this.formdata.item=[],this.formdata.cash=[],this.formdata.taxamount=0,this.$refs.cashitem.catchHight()},billSwitch:function(t){if(console.log(this.initData),"D01M03B1"==t){this.formdata.billtype="销售预收",this.formdata.billtitle="【"+this.initData.refno+"】销售订单转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billamount=this.initData.billtaxamount,this.formdata.citecode=this.initData.refno,this.formdata.item=[];var e={billtaxamount:this.initData.billtaxamount,amount:this.initData.billtaxamount,machbillid:this.initData.id,machbillcode:this.initData.refno,virtualitem:this.initData.virtualitem};this.formdata.item.push(e)}}}},A=K,G=(i("a3db"),Object(h["a"])(A,a,o,!1,null,"65205326",null));e["default"]=G.exports},aaa1:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[i("ListHeader",{attrs:{tableForm:t.tableForm,passkey:t.passkey},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,changeModelUrl:t.changeModelUrl,btnExport:t.btnExport,pagePrint:t.pagePrint,changeBalance:t.changeBalance,btnPrint:function(e){return t.$refs.tableTh.btnPrint()},setDelivery:function(e){return t.$refs.tableList.setDelivery()},bindColumn:function(e){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()}}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{attrs:{span:24}},[i("TableTh",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],ref:"tableTh",attrs:{online:t.online,progressData:t.progressData,formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}}),i("TableList",{directives:[{name:"show",rawName:"v-show",value:!t.thorList,expression:"!thorList"}],ref:"tableList",attrs:{online:t.online,progressData:t.progressData,formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}})],1)],1)],1)],1)])},o=[],s=(i("e9c4"),i("b64b"),i("d3b7"),i("ac1f"),i("3ca3"),i("841c"),i("ddb0"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{class:"filter-container flex j-s a-c"},[i("div",{staticStyle:{display:"flex"}},[i("div",{staticClass:"flex infoForm a-c"},[i("span",{staticClass:"infoForm-Title"},[t._v("时间范围")]),i("el-date-picker",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"unlink-panels":"",size:"mini","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini",disabled:t.statusType||!t.isregister},on:{click:t.btnAdd}},[t._v(" 添加 ")]),i("el-button",{attrs:{size:"mini",icon:"el-icon-printer",title:"打印列表"},on:{click:function(e){return t.$emit("pagePrint")}}},[t._v("打印")]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:"el-icon-printer",plain:"",size:"mini",title:"打印单据"},on:{click:function(e){return t.$emit("btnPrint")}}},[t._v(" 单据 ")]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:!t.thorList,expression:"!thorList"}],staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-s-finance",plain:"",size:"mini"},on:{click:function(e){return t.$emit("setDelivery")}}},[t._v(" 订单发货 ")])],1),i("div",{staticClass:"iShowBtn"},[i("div",{staticStyle:{display:"inline-block"}},[i("el-switch",{staticStyle:{"margin-right":"10px"},attrs:{"inactive-color":"#13ce66"},on:{change:t.changeModelUrl},model:{value:t.thorList,callback:function(e){t.thorList=e},expression:"thorList"}}),i("span",{staticStyle:{"margin-right":"10px","font-size":"14px"}},[t._v(t._s(t.thorList?"单据":"明细"))])],1),i("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"结存",size:"mini",border:""},on:{change:t.changeBalance},model:{value:t.balance,callback:function(e){t.balance=e},expression:"balance"}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"SaDgFormat"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),r=[],n=i("b893"),l={name:"Listheader",props:["tableForm","passkey"],data:function(){return{strfilter:"",formdata:{},dateRange:Object(n["d"])(),pickerOptions:Object(n["h"])(),thorList:!0,balance:!1,setColumsVisible:!1,searchVisible:!1,statusType:!1,isregister:0}},mounted:function(){this.intervalTime(this.passkey),this.isregister=localStorage.getItem("getInfo")?JSON.parse(localStorage.getItem("getInfo")).isregister:0},methods:{advancedSearch:function(t){var e={dateRange:this.dateRange,formdata:t};this.$emit("advancedSearch",e),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){var t={dateRange:this.dateRange,strfilter:this.strfilter};this.$emit("btnSearch",t)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList),this.$emit("bindColumn")},changeBalance:function(t){var e=0;e=t?1:0,this.$emit("changeBalance",e)},intervalTime:function(t){if(t){var e=t.ex,i=Date.parse(new Date)/1e3,a=i,o=e/1e3,s=1e3*(o-a),r=Math.floor(s/864e5),n=s%864e5,l=(Math.floor(n/36e5),n%36e5),d=(Math.floor(l/6e4),l%6e4);Math.round(d/1e3);this.statusType=r<0}else this.statusType=!0}}},d=l,m=(i("8dce"),i("2877")),c=Object(m["a"])(d,s,r,!1,null,"07395c25",null),u=c.exports,f=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableTh",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize,pageSizes:[10,20,100,500]},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px",display:"flex","align-items":"center"}},[i("div",{staticStyle:{"margin-right":"10px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1),i("div",[i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.showItemList,expression:"showItemList"}],attrs:{size:"mini",icon:"el-icon-refresh-right"},on:{click:function(e){return t.refreshItem()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-operation"},on:{click:function(e){t.showItemList=!t.showItemList,t.getColumnItem()}}})],1)])]),t.showItemList?i("div",[i("MyPopover",{ref:"mypopover",staticClass:"mypopovers",attrs:{tableForm:t.mypopoverTable,lst:t.mypopoverData,progressData:t.progressData}})],1):t._e(),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.selectList,printcode:"D01M03B1Edit",commonurl:"/D01M03B1/printBatchBill",weburl:"/D01M03B1/printBatchWebBill"}}),i("PrintServer",{ref:"PrintPageServer",attrs:{formdata:t.lst,queryParams:t.queryParams,printcode:"D01M03B1Th",commonurl:"/D01M03B1/printPageTh",weburl:"/D01M03B1/printWebPageTh"}})],1)},h=[],p=(i("99af"),i("c740"),i("a9e3"),i("c7cd"),i("159b"),i("f07e")),b=i("b775"),g=i("40d9"),y=i("da92"),w={components:{MyPopover:p["a"]},props:["online","progressData","formtemplate"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:g["f"],mypopoverTable:g["c"],mypopoverData:[],mypopoverIndex:0,selectList:[],showItemList:!1,totalfields:["refno","billtaxamount","advaamount","balance"],exportitle:"销售订单",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectList=i?t.lst:[]}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyRowEvents:function(e){var i=e.row,a=e.rowIndex;return{click:function(e){t.showItemList&&(t.mypopoverIndex=a,t.getBillList(i,a),t.keyDownReview(),t.keyDown())}}},bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160-(this.showItemList?250:0);return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},beforeDestroy:function(){this.keyDownReview()},methods:{bindData:function(){var t=this;if(this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),this.online)var e="/D01M03B1/getOnlinePageTh";else e="/D01M03B1/getPageTh";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(n["d"])()[0],EndDate:Object(n["d"])()[1]}),b["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total,t.mypopoverData=[];for(var i=0;i<t.lst.length;i++){var a=t.lst[i];a.balance=y["a"].minus(a["billtaxamount"],a["advaamount"])}}else t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this,e=g["f"];this.formtemplate.th.type&&(e.item=this.formtemplate.th.content),this.$getColumn(this.tableForm.formcode,e).then((function(e){t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("billplandate"==t.itemcode){var s="#000";o.finishcount+o.disannulcount<o.itemcount&&(new Date(o[t.itemcode]).getTime()-(new Date).getTime()<2592e5&&new Date(o[t.itemcode]).getTime()-(new Date).getTime()>0?s="#ff9800":new Date(o[t.itemcode]).getTime()-(new Date).getTime()<0&&(s="#fd5353"));var r=a("span",{style:"color:"+s},[e.$options.filters.dateFormat(o[t.itemcode])]);return r}if("refno"==t.itemcode){r=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return r}if("assessor"==t.itemcode){r="";return r=o.oaflowmark&&!o.assessor?a("span",{style:"color:#ff9800"},["审核中"]):a("span",[o.assessor]),r}if("billwkwpname"==t.itemcode){r="";return r=o.wkfinishcount>0?o.wkfinishcount+o.disannulcount==o.wkitemcount?a("span",{class:"textborder-green"},["生产完工"]):a("span",{class:"textborder-blue"},["生产入库"]):a("span",{directives:[{name:"show",value:o[t.itemcode]}],class:"textborder",style:"background:"+e.getBackGroundColor(o)+";color:"+e.getColor(o)},[o[t.itemcode]]),r}if("balance"==t.itemcode)return a("span",[y["a"].minus(o.billtaxamount,o.advaamount)]);if("groupuid"==t.itemcode){r=a("GroupInfo",{attrs:{scopeVal:o[t.itemcode],searchUrl:"/D01M01B1/getGeneral?key="+o.groupid}});return r}if("status"==t.itemcode){r="";return o.finishcount>0&&o.finishcount+o.disannulcount<o.itemcount?r=a("span",{class:"textborder-blue"},["".concat(o.finishcount,"/").concat(o.itemcount)]):o.finishcount>0&&o.finishcount+o.disannulcount==o.itemcount?r=a("span",{class:"textborder-green"},["完成"]):o.disannulcount>0&&o.disannulcount==o.itemcount&&(r=a("span",{class:"textborder-grey"},["撤销"])),r}if("amtstatus"==t.itemcode){r="";return r=o.firstamt+o.lastamt>0&&o.firstamt+o.lastamt<o.billtaxamount?a("span",{class:"textborder-blue"},["收款"]):o.firstamt+o.lastamt>=o.billtaxamount&&o.billtaxamount>0?a("span",{class:"textborder-green"},["收结"]):a("span",{directives:[{name:"show",value:o[t.itemcode]}],class:"textborder",style:"background:"+e.getBackGroundColor(o)+";color:"+e.getColor(o)},[o[t.itemcode]]),r}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.customData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,this.totalfields)},countCellData:function(){var t=this.$refs.tableTh.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableTh.getRangeCellSelection().selectionRangeIndexes,i=["billtaxamount","advaamount","balance"];this.$countCellData(this,i,t,e)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t,1)},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){for(var t=0;t<this.lst.length;t++){var e=this.lst[t];e.finishcount>0&&e.finishcount+e.disannulcount<e.itemcount?e.status="发货":e.finishcount>0&&e.finishcount+e.disannulcount==e.itemcount?e.status="完成":e.disannulcount>0&&e.disannulcount==e.itemcount&&(e.status="撤销"),e.wkfinishcount>0&&(row.wkfinishcount+row.disannulcount==row.wkitemcount?e.billwkwpname="生产完工":e.billwkwpname="生产入库"),e.balance=y["a"].minus(e.billtaxamount,e.advaamount)}this.$btnExport(this.lst,this.tableForm,this.exportitle)},pagePrint:function(){this.$refs.PrintPageServer.printButton(1,1)},btnPrint:function(){this.$refs.PrintServer.printButton(2,1)},refreshItem:function(){var t=this.lst[this.mypopoverIndex];t?this.getBillList(t,this.mypopoverIndex):this.mypopoverData=[]},keyDownReview:function(){document.onkeydown=function(t){var e=t||window.event;e.returnValue=!0}},keyDown:function(){var t=this;document.onkeydown=function(e){if(40===e.keyCode){var i=t.$refs.tableTh,a=i.cellSelectionData.currentCell.rowIndex;t.getBillList(t.lst[a],a)}else if(38===e.keyCode){i=t.$refs.tableTh,a=i.cellSelectionData.currentCell.rowIndex;t.getBillList(t.lst[a],a)}}},getBillList:function(t,e){var i=this;this.mypopoverIndex=e,b["a"].get("/D01M03B1/getItemSpuList?key=".concat(t.id)).then((function(t){200==t.data.code?i.mypopoverData=t.data.data:i.mypopoverData=[]}))},getColumnItem:function(){var t=this;this.$getColumn(g["c"].formcode,g["c"],1).then((function(e){t.mypopoverTable=e.colList}))},getBackGroundColor:function(t){var e=this.progressData.findIndex((function(e){return e.id==t.billwkwpid})),i="#FFF";return-1!=e&&(i=this.progressData[e].backcolorargb?this.progressData[e].backcolorargb:"#FFF"),i},getColor:function(t){var e=this.progressData.findIndex((function(e){return e.id==t.billwkwpid})),i="#000";return-1!=e&&(i=this.progressData[e].forecolorargb?this.progressData[e].forecolorargb:"#000"),i}},destroyed:function(){this.$setWs.wsClose()}},v=w,x=(i("c0a7"),Object(m["a"])(v,f,h,!1,null,"445340d4",null)),k=x.exports,D=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize,pageSizes:[10,20,100,500]},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.printData,queryParams:t.queryParams,printcode:"D01M03B1List",weburl:"/D01M03B1/printFreeWebPageList"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},[i("D01M06B1",{ref:"D01M06B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.selectList,billcode:"D01M03B1List"},on:{closeDialog:function(e){t.operationVisible=!1,t.selectList=[],t.checkboxOption.selectedRowKeys=[],t.bindData()}}})],1):t._e()],1)},S=[],$=i("c7eb"),_=i("1da1"),B=(i("caad"),i("a434"),i("2532"),i("5ad6")),O={components:{MyPopover:p["a"],D01M06B1:B["default"]},props:["online","progressData","formtemplate"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:g["e"],customList:[],partGroup:[],costgroupList:[],buyListData:[],buyListIndex:-1,buyListTable:g["a"],operationVisible:!1,dialogIdx:0,customData:[],columnHidden:[],footerData:[],totalfields:["refno","taxamount","quantity","finishqty","remainder"],cellTotal:0,cellNum:0,rowScroll:0,printData:[],virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},selectList:[],checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){var i=e.row,a=(e.isSelected,e.selectedRowKeys);if(t.checkboxOption.selectedRowKeys=a,a.includes(i.id))t.selectList.push(i);else{var o=t.selectList.findIndex((function(t){return t.id==i.id}));-1!=o&&t.selectList.splice(o,1)}},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.selectList=t.lst,t.checkboxOption.selectedRowKeys=a):(t.selectList=[],t.checkboxOption.selectedRowKeys=[])}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},watch:{lst:function(t,e){0!=this.lst.length&&(this.getColumn(),this.getSummary())}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},methods:{bindData:function(){var t=this;if(this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),this.online)var e="/D01M03B1/getOnlinePageList";else e="/D01M03B1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(n["d"])()[0],EndDate:Object(n["d"])()[1]}),b["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++){var a=t.lst[i],o=a.quantity-a.finishqty<0?0:a.quantity-a.finishqty;a.remainder=o;for(var s=a.attributejson?JSON.parse(a.attributejson):[],r=0;r<s.length;r++)t.$set(t.lst[i],s[r].key,s[r].value);var n=a.costgroupjson?JSON.parse(a.costgroupjson):[];for(r=0;r<n.length;r++)t.$set(t.lst[i],n[r].key,n[r].value)}}else t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(_["a"])(Object($["a"])().mark((function e(){var i;return Object($["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=g["e"],t.formtemplate.list.type&&(i.item=t.formtemplate.list.content),t.$getColumn(t.tableForm.formcode,i,1,0,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex),r="";if("billdate"==t.itemcode||"itemplandate"==t.itemcode||"billplandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("goodsuid"==t.itemcode||"matcode"==t.itemcode)return r=a("GoodsInfo",{attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}}),r;if("status"==t.itemcode)return 0!=o.finishqty&&o.finishqty<o.quantity?r=a("span",{class:"textborder-blue"},["发货"]):o.finishqty==o.quantity&&0!=o.finishqty?r=a("span",{class:"textborder-green"},["完成"]):1==o.disannulmark?r=a("span",{class:"textborder-grey"},["撤销"]):1==o.closed&&(r=a("span",{class:"textborder-grey"},["中止"])),r;if("refno"==t.itemcode)return r=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]),r;if("wkwpname"==t.itemcode)return o[t.itemcode]&&(r=a("span",{class:"textborder",style:"background:"+e.getBackGroundColor(o)+";color:"+e.getColor(o)},[o[t.itemcode]])),r;if("matstatus"==t.itemcode)return o.matcode&&(r=a("div",[a("el-tag",{directives:[{name:"show",value:o.matused}],attrs:{effect:"dark",size:"small"}},["已领"]),a("el-tag",{directives:[{name:"show",value:!o.matused}],attrs:{effect:"plain",size:"small"}},["未领"])])),r;if("buyquantity"==t.itemcode){r="";return r=a("el-popover",{attrs:{placement:"left",trigger:"click",title:"采购明细"},ref:"'popover-' + scope.$index"},[a("div",{style:"position: relative; min-height: 100px",directives:[{name:"show",value:s==e.buyListIndex}]},[a(p["a"],{ref:"mypopover",attrs:{tableForm:e.buyListTable,lst:e.buyListData}})]),a("span",{slot:"reference",class:"textunderline",on:{click:function(){return e.getBuyList(o,s)}}},[o[t.itemcode]])]),r}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,this.totalfields)},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxprice","taxamount"];this.$countCellData(this,i,t,e)},setDelivery:function(){var t=this;if(0!=this.selectList.length){var e=this.selectList.every((function(e){return e.groupid==t.selectList[0].groupid}));if(e){var i=this.selectList.every((function(t){return""==t.assessor}));if(i)this.$message.warning("单据未审核");else{var a=this.selectList.every((function(t){return t.finishqty>=t.quantity}));a?this.$message.warning("单据已完成"):this.operationVisible=!0}}else this.$message.warning("请选择相同客户")}else this.$message.warning("请选择货品内容")},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t,1)},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){for(var t=0;t<this.lst.length;t++){var e=this.lst[t];0!=e.finishqty&&e.finishqty<e.quantity?e.status="发货":0!=e.finishcount&&e.finishqty==e.quantity?e.status="完成":1==e.disannulmark?e.status="撤销":e.closed&&(e.status="中止"),e.matused&&(e.matstatus="已领")}this.$btnExport(this.lst,this.tableForm,"销售明细表")},pagePrint:function(){var t=this;this.selectList.length>0?this.printData=this.selectList:this.printData=this.lst,this.$nextTick((function(){t.$refs.PrintServer.printButton(5,1)}))},getBuyList:function(t,e){var i=this,a={PageNum:1,PageSize:20,OrderType:1,SearchType:1,SearchPojo:{machuid:t.refno}};this.buyListIndex=e,b["a"].post("/D03M02B1/getPageList",JSON.stringify(a)).then((function(t){200==t.data.code?i.buyListData=t.data.data.list:i.buyListData=[]}))},getBackGroundColor:function(t){var e=this.progressData.findIndex((function(e){return e.id==t.wkwpid})),i="#FFF";return-1!=e&&(i=this.progressData[e].backcolorargb?this.progressData[e].backcolorargb:"#FFF"),i},getColor:function(t){var e=this.progressData.findIndex((function(e){return e.id==t.wkwpid})),i="#000";return-1!=e&&(i=this.progressData[e].forecolorargb?this.progressData[e].forecolorargb:"#000"),i}}},C=O,P=(i("e41b"),Object(m["a"])(C,D,S,!1,null,"21aaba7a",null)),F=P.exports,M=i("78a4"),I={name:"D01M03B1",components:{ListHeader:u,TableTh:k,TableList:F,FormEdit:function(){return Promise.all([i.e("chunk-elementUI"),i.e("chunk-commons"),i.e("chunk-0f374694"),i.e("chunk-eb579050"),i.e("chunk-27979340")]).then(i.bind(null,"fc38"))}},props:["billcode"],data:function(){return{total:0,formvisible:!1,idx:0,thorList:!0,online:0,tableForm:{},showhelp:!1,progressData:[],formtemplate:M["a"],passkey:{}}},created:function(){this.passkey=JSON.parse(localStorage.getItem("getInfo")).registrkey?JSON.parse(JSON.parse(localStorage.getItem("getInfo")).registrkey):{}},mounted:function(){this.bindTemp(),this.bindData()},methods:{bindTemp:function(){var t=this;this.formtemplate=M["a"],this.$request.get("/SaFormCustom/getEntityByCode?key=D01M03B1").then((function(e){200==e.data.code?(null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):M["a"]),t.$nextTick((function(){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()}))):t.$alert(e.data.msg||"获取页面信息失败")})).catch((function(e){t.$message.error("请求错误")}))},bindData:function(){var t=this;this.thorList?(this.$refs.tableTh.keyDownReview(),this.$nextTick((function(){t.$refs.tableTh.bindData()}))):this.$nextTick((function(){t.$refs.tableList.bindData()}))},getprogressData:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:0,SearchType:1,OrderBy:"rownum"};b["a"].post("/D05M21S1/getPageTh",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.progressData=e.data.data.list)}))},search:function(t){this.thorList?this.$refs.tableTh.search(t):this.$refs.tableList.search(t)},advancedSearch:function(t){this.thorList?this.$refs.tableTh.advancedSearch(t):this.$refs.tableList.advancedSearch(t)},changeModelUrl:function(t){this.thorList=!!t,this.bindData()},changeBalance:function(t){this.online=t,this.bindData()},pagePrint:function(){this.thorList?this.$refs.tableTh.pagePrint():this.$refs.tableList.pagePrint()},btnExport:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.tableTh.btnExport()})):this.$nextTick((function(){t.$refs.tableList.btnExport()}))},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1,this.bindData()},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},sendTableForm:function(t){this.tableForm=t}}},q=I,T=(i("5a67"),Object(m["a"])(q,a,o,!1,null,"f8deaabe",null));e["default"]=T.exports},ab2b:function(t,e,i){},aff2:function(t,e,i){},b3dd:function(t,e,i){"use strict";i("0362")},b5cb:function(t,e,i){},b67b:function(t,e,i){"use strict";i("30e1")},b83a:function(t,e,i){"use strict";i("11f6")},b9c6:function(t,e,i){"use strict";i("703e")},bf19:function(t,e,i){"use strict";var a=i("23e7");a({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return URL.prototype.toString.call(this)}})},bf1d:function(t,e,i){},bf63:function(t,e,i){},c026:function(t,e,i){"use strict";i("f31c")},c0a7:function(t,e,i){"use strict";i("736e")},c19f:function(t,e,i){"use strict";var a=i("23e7"),o=i("da84"),s=i("621a"),r=i("2626"),n="ArrayBuffer",l=s[n],d=o[n];a({global:!0,forced:d!==l},{ArrayBuffer:l}),r(n)},c4d7:function(t,e,i){},c6bc:function(t,e,i){},cf0e:function(t,e,i){"use strict";i("0d4c")},cff6:function(t,e,i){"use strict";i("4aa6")},e41b:function(t,e,i){"use strict";i("3559")},e510:function(t,e,i){},e7c1:function(t,e,i){},e845:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.receipted>0&&t.formdata.receipted>=t.formdata.taxamount&&0==t.formdata.closed,revoke:!!t.formdata.disannulmark,yizhongzhi:1==t.formdata.closed,beoverdue:t.formdata.receipted<t.formdata.taxamount&&(new Date).getTime()>new Date(t.formdata.aimdate).getTime()&&0==t.formdata.closed,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,computerTime:t.computerTime}})],1)]},proxy:!0},{key:"Item",fn:function(){return["销售开票"==t.formdata.billtype?i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1):t._e()]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D01M05B1Edit",commonurl:"/D01M05B1/printBill",weburl:"/D01M05B1/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D01M05B1Edit",examineurl:"/D01M05B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},[i("D01M08B1",{ref:"D01M08B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D01M05B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1}}})],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"close-on-click-modal":!1,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D01M08B1"==t.processModel?i("D01M08B1List",{ref:"D01M08B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=(i("caad"),i("b64b"),i("2532"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D01M05B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D01M05B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D01M05B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D01M05B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D01M05B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D01M05B1/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=r,l=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[],t.formdata.taxamount=0}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"销售开票",value:"销售开票"}}),i("el-option",{attrs:{label:"其他应收",value:"其他应收"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"发票编码"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入发票编码",clearable:"",size:"small"},on:{focus:function(t){return t.currentTarget.select()}},model:{value:t.formdata.invocode,callback:function(e){t.$set(t.formdata,"invocode",e)},expression:"formdata.invocode"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"客户",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B1/getOnlinePageList",type:"客户"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"联系人"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入联系人",clearable:"",size:"small"},model:{value:t.formdata.linkman,callback:function(e){t.$set(t.formdata,"linkman",e)},expression:"formdata.linkman"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"联系电话"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入联系电话",clearable:"",size:"small"},model:{value:t.formdata.telephone,callback:function(e){t.$set(t.formdata,"telephone",e)},expression:"formdata.telephone"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"开票日期"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},on:{change:function(e){return t.$emit("computerTime")}},model:{value:t.formdata.invodate,callback:function(e){t.$set(t.formdata,"invodate",e)},expression:"formdata.invodate"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"收款计划"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.aimdate,callback:function(e){t.$set(t.formdata,"aimdate",e)},expression:"formdata.aimdate"}})],1)],1),i("el-col",{attrs:{span:9}},[i("div",{staticStyle:{display:"flex"}},[i("el-form-item",{attrs:{label:"发票金额"}},["销售开票"!=t.formdata.billtype?i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入发票金额",size:"small"},model:{value:t.formdata.taxamount,callback:function(e){t.$set(t.formdata,"taxamount",e)},expression:"formdata.taxamount"}}):i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v(" ￥"+t._s(t.formdata.taxamount?t.formdata.taxamount:0))])],1),i("el-form-item",{attrs:{label:"已收金额"}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.receipted?t.formdata.receipted:0))])])],1)])],1)],1)},d=[],m={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{billtitle:[{required:!0,trigger:"blur",message:"单据标题为必填项"}],groupname:[{required:!0,trigger:"blur",message:"客户为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},c=m,u=(i("b67b"),i("2877")),f=Object(u["a"])(c,l,d,!1,null,"bd9a0c02",null),h=f.exports,p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["moveup","movedown","delete","copyrow","refresh","billstate"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd},scopedSlots:t._u([{key:"left",fn:function(){return[i("el-button",{attrs:{disabled:2==t.formstate,type:"primary",size:"mini",icon:"el-icon-circle-plus-outline"},nativeOn:{click:function(e){return t.getAdd()}}},[t._v("发货")]),i("el-button",{attrs:{disabled:2==t.formstate,type:"primary",size:"mini",icon:"el-icon-remove-outline"},nativeOn:{click:function(e){return t.getselKouKuan()}}},[t._v("扣款")])]},proxy:!0},{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"border-x":"","border-y":"","border-around":!0,"checkbox-option":t.checkboxOption,"column-width-resize-option":t.columnWidthResizeOption,editOption:t.editOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"发货单信息","append-to-body":!0,visible:t.selordervisible,width:"1200px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1,selecturl:"/D01M06R1/getOnlineInvoPageList?groupid="+this.formdata.groupid+"&appl=1",groupid:t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.SelOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.KouKuanVisible?i("el-dialog",{attrs:{title:"扣款单","append-to-body":!0,visible:t.KouKuanVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.KouKuanVisible=e}}},[i("SelKouKuan",{ref:"selKouKuan",attrs:{multi:1,selecturl:"/D01M09B1/getOnlinePageList?groupid="+t.formdata.groupid+"&appl=1"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selKouKuan()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.KouKuanVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},b=[],g=(i("c740"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("5319"),i("c7cd"),i("159b"),{amount:0,billqty:0,custom1:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",custpo:"",delidate:new Date,deliitemid:"",delitype:"",deliuid:"",goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",id:"",itemtaxrate:0,machitemid:"",machuid:"",partid:"",pid:"",price:0,quantity:0,remark:"",rownum:0,taxamount:0,taxprice:0,taxtotal:0}),y=i("6956"),w=i("4fcf"),v=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),i("el-table-column",{attrs:{label:"金额",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.taxamount))])]}}])}),i("el-table-column",{attrs:{label:"订单号",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.machuid))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},x=[],k=i("333d"),D={components:{Pagination:k["a"]},props:["multi","selecturl"],data:function(){return{title:"销售扣款",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D01M09B1/getPageList";s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},S=D,$=(i("cf0e"),Object(u["a"])(S,v,x,!1,null,"c6f399ee",null)),_=$.exports,B={name:"Elitem",components:{SelOrder:w["a"],SelKouKuan:_},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{listLoading:!1,selordervisible:!1,KouKuanVisible:!1,lst:[],keynum:0,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],setColumsVisible:!1,isEditOk:!0,selecturl:"",tableForm:y["b"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate"],columsData:[],rowStyleOption:{clickHighlight:!1,hoverHighlight:!1,stripe:!1},columnHidden:[],checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},columnWidthResizeOption:{enable:!0,minWidth:10,sizeChange:function(e){var i=e.column,a=e.differWidth,o=e.columnWidth;t.columnResizeInfo.column=i,t.columnResizeInfo.differWidth=a,t.columnResizeInfo.columnWidth=o}},columnResizeInfo:{column:"",differWidth:"",columnWidth:""},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;t.editmarkfiles.includes(a.field)&&t.countfiles.includes(a.field)&&t.changeInput("",i,a.field)}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},footerData:[],cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[s],Object.keys(o)[1])}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}},copyText:""}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=y["b"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(y["b"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(e,i){var a=e.row;e.column,e.rowIndex;if("goodsuid"==t.itemcode){var o=i("GoodsInfo",{class:a.disannulmark?"textlinethrough":"",attrs:{scopeVal:a[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return o}o=i("span",{class:a.disannulmark?"textlinethrough":""},[a[t.itemcode]]);return o}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["goodsuid","amount","quantity","taxamount"]),this.formdata.amount=this.footerData[0].amount,this.formdata.taxamount=this.footerData[0].taxamount,this.formdata.taxtotal=this.formdata.taxamount-this.formdata.amount},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.changeInput("",this.lst[e+o],n)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i)},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"finishqty",0),this.$set(e,"closed",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.formdata.groupid?this.selordervisible=!0:this.$message.warning("请选择客户")},SelOrder:function(){var t=this.$refs.SelOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},g);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.goodsuid=i.goodsuid,a.partid=i.partid,a.goodscustom1=i.custom1,a.goodscustom2=i.custom2,a.goodscustom3=i.custom3,a.goodscustom4=i.custom4,a.goodscustom5=i.custom5,a.goodscustom6=i.custom6,a.goodscustom7=i.custom7,a.goodscustom8=i.custom8,a.goodscustom9=i.custom9,a.goodscustom10=i.custom10,a.amount=i.amount,a.billqty=i.quantity,a.custpo=i.custpo,a.deliitemid=i.id,a.delitype=i.billtype,a.deliuid=i.refno,a.taxrate=i.itemtaxrate,a.machitemid=i.citeitemid,a.machuid=i.citeuid,a.price=i.price,a.quantity=i.quantity,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.itemtaxrate=i.itemtaxrate,a.taxtotal=i.taxtotal,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},getselKouKuan:function(){this.formdata.groupid?this.KouKuanVisible=!0:this.$message.warning("请先选择客户")},selKouKuan:function(){var t=this.$refs.selKouKuan.$refs.selectVal.selection;if(0!=t.length){this.KouKuanVisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},g);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.goodsuid=i.goodsuid,a.partid=i.partid,a.itemtaxrate=i.itemtaxrate,a.amount=Number(0-i.amount),a.price=Number(0-i.price),a.taxamount=Number(0-i.taxamount),a.taxprice=Number(0-i.taxprice),a.taxtotal=i.taxtotal,a.quantity=i.quantity,a.billqty=i.quantity,a.custpo=i.custpo,a.delidate=i.billdate,a.deliitemid=i.id,a.delitype=i.billtype,a.deliuid=i.refno,a.machuid=i.machuid,a.machitemid=i.machitemid,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},O=B,C=(i("cff6"),Object(u["a"])(O,p,b,!1,null,"3b14b5c0",null)),P=C.exports,F=i("52f2"),M=i("dcb4"),I=["id","refno","billtype","billtitle","billdate","groupid","taxamount","amount","taxtotal","taxrate","invocode","invodate","aimdate","summary","closed","disannulmark","fmdocmark","fmdoccode","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],q=["id","pid","deliuid","delidate","delitype","deliitemid","goodsid","billqty","quantity","taxprice","taxamount","price","amount","itemtaxrate","taxtotal","rownum","remark","machuid","machitemid","custpo","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10","custom11","custom12","custom13","custom14","custom15","custom16","custom17","custom18"],T={params:I,paramsItem:q},L=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,divided:!0,label:"销售收款",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D01M08B1",children:[]}],R=[{show:1,divided:!1,label:"销售收款",icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D01M08B1",label:"销售收款"},children:[]}],N=i("910f"),j=i("5afc"),V={name:"Formedit",components:{FormTemp:M["a"],EditHeader:h,EditItem:P,D01M08B1:N["default"],D01M08B1List:j["a"]},props:["idx","isDialog","initData","billcode"],data:function(){return{title:"销售开票",operateBar:L,processBar:R,formdata:{aimdate:"",amount:0,assessdate:new Date,assessor:"",billdate:new Date,billtitle:"",billtype:"销售开票",closed:0,disannulmark:0,groupid:"",groupname:"",linkman:"",telephone:"",invocode:"",invodate:new Date,receipted:0,statecode:"",statedate:"",summary:"",taxamount:0,taxrate:0,taxtotal:0,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:F["a"],formstate:0,submitting:0,creditdquantity:0,creditduint:"day"}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp(),this.bindData()},mounted:function(){this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D01M05B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):F["a"],t.formtemplate.footer.type||(t.formtemplate.footer=F["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&s["a"].get("/D01M05B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.linkman=t.linkman,this.formdata.telephone=t.telephone,this.formdata.groupid=t.id,this.formdata.deliadd=t.deliveradd,this.creditdquantity=t.creditdquantity,this.creditduint=t.creditduint,this.computerTime()},autoClear:function(t){this.formdata.groupname="",this.formdata.linkman="",this.formdata.telephone="",this.formdata.groupid="",this.formdata.deliadd="",this.creditdquantity="",this.creditduint=""},submitForm:function(t){var e,i=this;e=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,e.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;i.saveForm()}))},saveForm:function(){var t=this;if("销售开票"==this.formdata.billtype){if(0==this.$refs.elitem.lst.length)return void this.$message.warning("单据内容不能为空");for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].billqty)return this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0"),void this.$refs.elitem.saveRow(this.$refs.elitem.lst[e]);this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.amount=i.billamount,this.formdata.taxamount=i.billtaxamount,this.formdata.taxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(T,a,this.formdata)}else a=Object.assign({},this.formdata);0==this.idx?n.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败")})):n.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败")})),setTimeout((function(){t.submitting=0}),500)},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(e)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},computerTime:function(){if(0!=this.creditdquantity){var t=this.formdata.invodate.getTime();"month"==this.creditduint?t+=2592e6*this.creditdquantity:t+=864e5*this.creditdquantity,this.formdata.aimdate=new Date(t)}},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},changeBillType:function(){formdata.item=[]},billSwitch:function(t){if(console.log(this.initData),"D01M06B1"==t){this.formdata.billtitle="【"+this.initData.refno+"】发货单转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.linkman=this.initData.linkman,this.formdata.telephone=this.initData.telephone,this.formdata.item=[];for(var e=0;e<this.initData.item.length;e++){var i=this.initData.item[e],a=Object.assign({},g);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.goodsuid=i.goodsuid,a.partid=i.partid,a.amount=i.amount,a.billqty=i.quantity,a.itemtaxrate=i.itemtaxrate,a.price=i.price,a.quantity=i.quantity,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,a.custpo=i.custpo,a.delidate=i.billdate,a.deliitemid=i.id,a.deliuid=this.initData.refno,a.delitype=this.initData.billtype,a.machitemid=i.machitemid,a.machuid=i.machuid,a.virtualitem=i.virtualitem,a.delitype.includes("退货")&&(a.billqty=-a.billqty,a.quantity=-a.quantity,a.taxamount=-a.taxamount,a.amount=-a.amount),this.formdata.item.push(a)}}}}},z=V,E=(i("7dfe"),Object(u["a"])(z,a,o,!1,null,"a7820ce8",null));e["default"]=E.exports},ea7b:function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"订单发货",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"发出商品",value:"发出商品"},{label:"订单退货",value:"订单退货"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"客户",searchtype:"customer",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"linkman",label:"联系人",type:"input",methods:"",param:""},{col:5,code:"telephone",label:"联系电话",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"deliadd",label:"送货地址",type:"input",methods:"",param:""},{col:5,code:"transport",label:"运输方式",type:"input",methods:"",param:""},{col:5,code:"salesman",label:"业务员",billcode:"sale.salesman",type:"dictionary",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:23,code:"summary",label:"摘要",type:"input",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},eb28:function(t,e,i){"use strict";i("1d21")},ebf8:function(t,e,i){},f098:function(t,e,i){"use strict";i("8661")},f1fd:function(t,e,i){},f31c:function(t,e,i){},f68b:function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"其他发货",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"其他发货",value:"其他发货"},{label:"其他退货",value:"其他退货"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"客户",searchtype:"customer",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"linkman",label:"联系人",type:"input",methods:"",param:""},{col:5,code:"telephone",label:"联系电话",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"deliadd",label:"送货地址",type:"input",methods:"",param:""},{col:5,code:"transport",label:"运输方式",type:"input",methods:"",param:""},{col:5,code:"salesman",label:"业务员",billcode:"sale.salesman",type:"dictionary",methods:"",param:""}]}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"salesman",label:"业务员",billcode:"sale.salesman",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},list:{type:0,content:[]},th:{type:0,content:[]},item:{type:0,content:[]}}},f944:function(t,e,i){"use strict";i.d(e,"e",(function(){return a})),i.d(e,"d",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r})),i.d(e,"c",(function(){return n}));var a={formcode:"D01M08B1List",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Bus_Receipt.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Receipt.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Bus_Receipt.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Receipt.billdate"},{itemcode:"abbreviate",itemname:"简称",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.abbreviate"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Receipt.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Receipt.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Receipt.invobillcode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Receipt.summary"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Receipt.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Bus_Receipt.assessor"}]},o={formcode:"D01M08BItem",item:[{itemcode:"invobillcode",itemname:"发票单据号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"invocode",itemname:"发票编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"invoamount",itemname:"发票金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},s={formcode:"D01M08B1Cash",item:[{itemcode:"cashaccname",itemname:"出纳账户",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"amount",itemname:"金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D01M08B1Th",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,overflow:1},{itemcode:"groupname",itemname:"客户名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"billamount",itemname:"金额",minwidth:"100",displaymark:1,overflow:1},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1}]},n={formcode:"D01M08B1Cite",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Bus_Receipt.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Receipt.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Bus_Receipt.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Receipt.billdate"},{itemcode:"abbreviate",itemname:"简称",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.abbreviate"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Receipt.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Receipt.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Receipt.invobillcode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Receipt.summary"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Receipt.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Bus_Receipt.assessor"}]}},fb056:function(t,e,i){"use strict";i("6ccf")},fb0b:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtitle))])]}}])}),i("el-table-column",{attrs:{label:"发票编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.invocode))])]}}])}),i("el-table-column",{attrs:{label:"客户",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"含税金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.taxamount)+" ")]}}])}),i("el-table-column",{attrs:{label:"已付金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.receipted))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],s=(i("99af"),i("e9c4"),i("d3b7"),i("25f0"),i("4d90"),i("b775")),r=i("333d"),n={components:{Pagination:r["a"]},filters:{dateFormat:function(t){var e=new Date(t),i=e.getFullYear(),a=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0");return"".concat(i,"/").concat(a,"/").concat(o)}},props:["multi","selecturl"],data:function(){return{title:"销售开票",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){console.log(t),this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D01M05B1/getPageTh";s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},l=n,d=(i("99fe"),i("2877")),m=Object(d["a"])(l,a,o,!1,null,"2eb1492a",null);e["a"]=m.exports},fe08:function(t,e,i){"use strict";i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r}));var a=i("ade3"),o={formcode:"D01M09B1Th",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Bus_Deduction.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deduction.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deduction.billdate"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deduction.billtitle"},Object(a["a"])(Object(a["a"])(Object(a["a"])({itemcode:"groupuid",itemname:"客户编码",minwidth:"100",displaymark:1,sortable:1},"sortable",1),"overflow",1),"datasheet","App_Workgroup.groupuid"),{itemcode:"groupname",itemname:"客户名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"operator",itemname:"经办人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deduction.operator"},{itemcode:"billtaxamount",itemname:"含税金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deduction.billtaxamount"},{itemcode:"billamount",itemname:"未税金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Deduction.billamount"},{itemcode:"billtaxtotal",itemname:"税额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deduction.billtaxtotal"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deduction.status"},{itemcode:"itemcount",itemname:"款数",minwidth:"60",displaymark:1,overflow:1,datasheet:"Bus_Deduction.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deduction.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deduction.assessor"}]},s={formcode:"D01M09B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Bus_Deduction.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deduction.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deduction.billdate"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deduction.billtitle"},{itemcode:"groupname",itemname:"客户名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"quantity",itemname:"数量",sortable:1,minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_DeductionItem.quantity"},{itemcode:"price",itemname:"单价",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_DeductionItem.price"},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_DeductionItem.taxamount"},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_DeductionItem.itemtaxrate"}]},r={formcode:"D01M09B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"80",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"70",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"70",displaymark:1,overflow:1,editmark:1},{itemcode:"machuid",itemname:"销售订单号",minwidth:"100",displaymark:1,overflow:1},{itemcode:"invoqty",itemname:"发票数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]}}}]);