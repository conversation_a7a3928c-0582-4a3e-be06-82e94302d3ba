(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-115371b9"],{"0b6c":function(t,e,i){},"0ba2":function(t,e,i){"use strict";i("8844")},1156:function(t,e,i){},"13df":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("div",{staticClass:"flex"},[i("el-button",{attrs:{type:"primary",size:"small",disabled:1==t.formstate||1==t.submitting},nativeOn:{click:function(e){return t.submitForm(e)}}},[t._v(" 保 存")]),i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["print","operate","process"],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1)]),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getAllGroupName:t.setGroupRow,getGroupName:t.setGroupRow,getSuppGroupName:t.setGroupRow,getFactGroupName:t.setGroupRow,getWorkGroupName:t.setGroupRow,getStoreName:t.getStoreName,autoClear:t.autoClear,autoStoreClear:t.autoStoreClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,getStoreName:t.getStoreName,autoStoreClear:t.autoStoreClear,changeBillType:t.changeBillType}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D04M01R1Edit",commonurl:"/D04M01R1/printBill",weburl:"/D04M01R1/printWebBill"}}),i("PrintServer",{ref:"PrintMultiServer",attrs:{formdata:t.formdata,printcode:"D04M01R1EditMulti",commonurl:"/D04M01R1/printWebBillMulti",weburl:"/D04M01R1/printWebBillMulti"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}}):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D04M01M1"==t.processModel?i("D04M01M1List",{ref:"D04M01M1List",attrs:{searchVal:t.formdata.refno,isDialog:!0,isHongchong:!0}}):t._e()],1):t._e()],1)},o=[],s=(i("b64b"),i("d3b7"),i("3ca3"),i("ddb0"),i("b775"));const n={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D04M01R1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D04M01R1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D04M01R1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})}};var r=n,l=["id","refno","billdate","typecode","billtype","billtitle","direction","groupid","storeid","storecode","storename","operator","summary","plusinfo","billtaxamount","billtaxtotal","billamount","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],d=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","quantity","price","amount","taxprice","taxamount","itemtaxrate","taxtotal","remark","citeuid","citeitemid","rownum","location","batchno","packsn","expidate","customer","custpo","machuid","machitemid","machgroupid","mainplanuid","mainplanitemid","orderuid","orderitemid","workuid","workitemid","subcuid","subcitemid","custuid","cuistitemid","inveid","skuid","attributejson","wkqtyid","labelcodes","labelqty","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],c={params:l,paramsItem:d},u={amount:0,attributejson:"",batchno:"",citeitemid:"",citeuid:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",customer:"",custpo:"",expidate:new Date,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",id:"",inveid:"",itemtaxrate:0,labelcodes:"",labelqty:0,location:"",machgroupid:"",machitemid:"",machuid:"",mainplanitemid:"",mainplanuid:"",mrpitemid:"",mrpuid:"",packsn:"",partid:"",pid:"",price:0,quantity:0,remark:"",rownum:0,skuid:"",sourcetype:0,statecode:"",statedate:new Date,taxamount:0,taxprice:0,taxtotal:0,wkqtyid:"",workuid:""},m=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"!this.formdata.returnuid",methods:"deleteForm",param:"",children:[]},{show:1,divided:!1,label:"红 冲",icon:"el-icon-edit-outline",disabled:"!this.formdata.id ? true : !!this.formdata.orguid ? true : !!this.formdata.returnuid",methods:"hongChong",param:"",children:[]}],p=[{show:1,divided:!1,label:"红冲记录",icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D04M01M1",label:"红冲记录"},children:[]},{show:1,divided:!1,label:"重打标签",icon:"el-icon-edit-outline",disabled:"this.formstate==0",methods:"getScan",param:"",children:[]}],h=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){return t.$emit("changeBillType",e)}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("div",[i("el-option",{attrs:{label:"收货入库",value:"收货入库"}}),i("el-option",{attrs:{label:"购退出库",value:"购退出库"}}),i("el-option",{attrs:{label:"发货出库",value:"发货出库"}}),i("el-option",{attrs:{label:"客退入库",value:"客退入库"}}),i("el-option",{attrs:{label:"领料出库",value:"领料出库"}}),i("el-option",{attrs:{label:"退料入库",value:"退料入库"}}),i("el-option",{attrs:{label:"其他入库",value:"其他入库"}}),i("el-option",{attrs:{label:"其他出库",value:"其他出库"}}),i("el-option",{attrs:{label:"报废出库",value:"报废出库"}}),i("el-option",{attrs:{label:"盘盈入库",value:"盘盈入库"}}),i("el-option",{attrs:{label:"盘亏出库",value:"盘亏出库"}})],1)])],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("direction")}}},[i("el-form-item",{attrs:{label:"操作方向",prop:"direction"}},[i("el-input",{attrs:{readonly:!0,size:"small"},model:{value:t.formdata.direction,callback:function(e){t.$set(t.formdata,"direction",e)},expression:"formdata.direction"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据主题"}},[i("el-input",{attrs:{size:"small",placeholder:"请输入单据主题"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",["发货出库"==t.formdata.billtype||"客退入库"==t.formdata.billtype?i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupid")}}},[i("el-form-item",{attrs:{label:"客户",prop:"groupid"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B1/getOnlinePageList",type:"客户"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupid")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]):"收货入库"==t.formdata.billtype||"购退出库"==t.formdata.billtype?i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupid")}}},[i("el-form-item",{attrs:{label:"供应商",prop:"groupid"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupid")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]):"领料出库"==t.formdata.billtype||"退料入库"==t.formdata.billtype?i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupid")}}},[i("el-form-item",{attrs:{label:"车间",prop:"groupid"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B3/getPageList",type:"车间"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupid")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]):"其他入库"==t.formdata.billtype||"其他出库"==t.formdata.billtype?i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupid")}}},[i("el-form-item",{attrs:{label:"往来单位",prop:"groupid"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01R1/getOnlinePageList"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupid")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]):t._e(),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("storeid")}}},[i("el-form-item",{attrs:{label:"仓库名称",prop:"storeid"}},[i("StoreAutoComplete",{attrs:{value:t.formdata.storename,baseurl:"/D04M21S1/getPageList"},on:{setRow:function(e){t.$emit("getStoreName",e),t.cleValidate("storeid")},autoClear:function(e){return t.$emit("autoStoreClear")}}})],1)],1)]),t.formdata.orguid?i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"原始单号"}},[i("el-input",{attrs:{size:"small",placeholder:"请输入原始单号"},model:{value:t.formdata.orguid,callback:function(e){t.$set(t.formdata,"orguid",e)},expression:"formdata.orguid"}})],1)],1):t._e(),t.formdata.returnuid?i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"红冲单号"}},[i("el-input",{attrs:{size:"small",placeholder:"请输入红冲单号"},model:{value:t.formdata.returnuid,callback:function(e){t.$set(t.formdata,"returnuid",e)},expression:"formdata.returnuid"}})],1)],1):t._e()],1)],1)},f=[],g={props:{formdata:{type:Object},title:{type:String}},data:function(){return{formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupid:[{required:!0,trigger:"blur",message:"往来单位为必填项"}],storeid:[{required:!0,trigger:"blur",message:"仓库名称为必填项"}],direction:[{required:!0,trigger:"blur",message:"操作方向为必填项"}]}}},mounted:function(){},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},b=g,y=(i("94da"),i("2877")),w=Object(y["a"])(b,h,f,!1,null,"a57865e4",null),v=w.exports,x=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","copyrow","refresh","billstate"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("div",{staticStyle:{display:"inline-block"}},[i("el-button",{attrs:{size:"mini"},nativeOn:{click:function(e){return t.getScan()}}},[i("svg-icon",{attrs:{"svg-icon":"","icon-class":"D05M05B1"}}),t._v(" 标签")],1)],1),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.seldeliveryvisible?i("el-dialog",{attrs:{title:"客退入库","append-to-body":!0,visible:t.seldeliveryvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.seldeliveryvisible=e}}},[i("SelDelivery",{ref:"selDelivery",attrs:{multi:1,selecturl:"/D01M06B1/getOnlinePageList?groupid="+t.formdata.groupid+"&appl=1"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selDelivery()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.seldeliveryvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.OrderVisible?i("el-dialog",{attrs:{title:"发货单","append-to-body":!0,visible:t.OrderVisible,width:"80vw","close-on-press-escape":!1,"close-on-click-modal":!1,top:"5vh"},on:{"update:visible":function(e){t.OrderVisible=e}}},[i("SelOrder",{ref:"selOrder",attrs:{multi:0,selecturl:"//D01M06R1/getOnlineOutPageList?groupid="+this.formdata.groupid+"&appl=1",storeid:t.formdata.storeid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.OrderVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.BuyOrderVisible?i("el-dialog",{attrs:{title:"收货入库","append-to-body":!0,visible:t.BuyOrderVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.BuyOrderVisible=e}}},[i("SelBuyOrder",{ref:"selBuyOrder",attrs:{multi:t.multi,selecturl:"/D03M03R1/getOnlineInPageList?groupid="+this.formdata.groupid+"&appl=1",groupid:t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selBuyOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.BuyOrderVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.BuyOrderOutVisible?i("el-dialog",{attrs:{title:"购退出库","append-to-body":!0,visible:t.BuyOrderOutVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1,top:"5vh"},on:{"update:visible":function(e){t.BuyOrderOutVisible=e}}},[i("SelBuyOrderOut",{ref:"selBuyOrderOut",attrs:{multi:t.multi,selecturl:"/D03M03R1/getOnlineOutPageList?groupid="+t.formdata.groupid+"&appl=1",groupid:t.formdata.groupid,storeid:t.formdata.storeid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selBuyOrderOut()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.BuyOrderOutVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.pickingVisible?i("el-dialog",{attrs:{title:"领料出库","append-to-body":!0,visible:t.pickingVisible,width:"70vw","close-on-press-escape":!1,"close-on-click-modal":!1,top:"5vh"},on:{"update:visible":function(e){t.pickingVisible=e}}},[i("SelPicking",{ref:"selPicking",attrs:{multi:0,groupid:t.formdata.groupid,storeid:t.formdata.storeid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selPicking()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.pickingVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.materialVisible?i("el-dialog",{attrs:{title:"退料入库","append-to-body":!0,visible:t.materialVisible,width:"70vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.materialVisible=e}}},[i("SelMaterial",{ref:"selMaterial",attrs:{multi:t.multi,groupid:t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selMaterial()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.materialVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.goodsVisible?i("el-dialog",{attrs:{title:"其他入库","append-to-body":!0,visible:t.goodsVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.goodsVisible=e}}},[i("SelGoods",{ref:"selGoods",attrs:{multi:t.multi}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selGoods()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.goodsVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.stockVisible?i("el-dialog",{attrs:{title:"其他出库","append-to-body":!0,visible:t.stockVisible,width:"80vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.stockVisible=e}}},[i("SelStock",{ref:"selStock",attrs:{multi:t.multi,storeid:t.formdata.storeid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selStock()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.stockVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.labelvisible?i("el-dialog",{attrs:{width:"50vw",title:"条码管理","append-to-body":!0,visible:t.labelvisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.labelvisible=e}}},[i("LabelItem",{ref:"labelItem",attrs:{formdataInfo:t.formdata,selectList:t.multipleSelection},on:{closeDialog:function(e){t.labelvisible=!1}}})],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},k=[],_=i("c7eb"),S=i("1da1"),P=i("ade3"),$=(i("c740"),i("caad"),i("d81d"),i("e9c4"),i("a9e3"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),i("5e63")),q=i("9bda"),D=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex,height:"220px"},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"发货单号",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据主题",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtitle))])]}}])}),i("el-table-column",{attrs:{label:"发货日期",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormat")(e.row.billdate)))])]}}])}),i("el-table-column",{attrs:{label:"客户",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.itemname))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),t._l(t.customList,(function(t,e){return[i("el-table-column",{key:e,attrs:{"header-align":"center",align:"center",prop:t.attrkey,label:t.attrname}})]}))],2)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}}),i("el-divider",{attrs:{"content-position":"left"}},[t._v("库存信息")]),i("div",{staticClass:"filter-container",staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px",height:"100%"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.searchItem(t.strItemfilter)}},model:{value:t.strItemfilter,callback:function(e){t.strItemfilter=e},expression:"strItemfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.searchItem(t.strItemfilter)}}},[t._v(" 查询 ")])],1),i("div",{staticClass:"istotal"},[t._v(" 合计："),i("span",{ref:"isTotal",style:{"margin-right":"10px",color:t.isOut?"#F00":"#606266"}},[t._v(t._s(t.isTotal))]),t._v(" 差值："),i("span",{ref:"isTotal",style:{color:0!=t.difference?"#F00":"#606266"}},[t._v(t._s(t.difference))])])]),i("div",{staticStyle:{padding:"10px 0px"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectItemVal",staticStyle:{overflow:"auto"},attrs:{data:t.lstItem,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",height:"220px","header-cell-style":t.headerStyle,"cell-style":{padding:"4px 0px 4px 0px"}},on:{"row-click":t.saveRow,"selection-change":t.handleSelect}},[i("el-table-column",{attrs:{type:"selection",width:"50",align:"center",fixed:"left"}}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"仓库",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.storename))])]}}])}),i("el-table-column",{attrs:{label:"库位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.location))])]}}])}),i("el-table-column",{attrs:{label:"库存",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),i("el-table-column",{attrs:{label:"实际出库",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isEdit?i("el-input",{attrs:{size:"small",placeholder:"实际出库"},on:{input:function(i){return t.changeInput(e.row)},focus:function(t){return t.currentTarget.select()}},model:{value:e.row.realqty,callback:function(i){t.$set(e.row,"realqty",i)},expression:"scope.row.realqty"}}):i("span",[t._v(t._s(e.row.realqty))])]}}])}),i("el-table-column",{attrs:{label:"批号",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.batchno))])]}}])}),t._l(t.customList,(function(t,e){return[i("el-table-column",{key:e,attrs:{"header-align":"center","min-width":"50",align:"center",prop:t.attrkey,label:t.attrname}})]}))],2),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.storeTotal>0,expression:"storeTotal > 0"}],attrs:{total:t.storeTotal,page:t.storeParams.PageNum,limit:t.storeParams.PageSize},on:{"update:page":function(e){return t.$set(t.storeParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.storeParams,"PageSize",e)},pagination:t.GetStoreList}})],1)],1)},O=[],I=i("333d"),C={components:{Pagination:I["a"]},props:["multi","storeid","selecturl"],data:function(){return{title:"发货信息",listLoading:!0,lst:[],strfilter:"",strItemfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:5,OrderType:1,SearchType:1},storeTotal:0,storeParams:{PageNum:1,PageSize:5,OrderType:1,SearchType:1},lstItem:[],customList:[],isEditOk:!0,isTotal:0,difference:0,isOut:!1,selectList:[]}},created:function(){this.bindData(),this.getColumn()},methods:{getColumn:function(){var t=this;s["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code?t.customList=e.data.data?e.data.data:[]:t.$message.warning(e.data.msg||"获取失败")})).catch((function(e){t.$message.error("请求错误")}))},getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.getStoreInfo(t.goodsid)},getStoreInfo:function(t){var e=this;this.storeParams.SearchPojo={storeid:this.storeid},s["a"].post("/D04M04B1/geOnlinePageListByGoods?key="+t,JSON.stringify(this.storeParams)).then((function(t){if(200==t.data.code){e.lstItem=t.data.data.list,e.storeTotal=t.data.data.total,e.difference=e.$fomatFloat(e.selrows.quantity-e.selrows.finishqty,2);for(var i=0;i<e.lstItem.length;i++){var a=e.lstItem[i];e.$set(e.lstItem[i],"finishqty",0),e.$set(a,"realqty",a.quantity);for(var o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)e.$set(e.lstItem[i],o[s].key,o[s].value)}}e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},GetStoreList:function(t){this.storeParams.PageNum=t.page,this.storeParams.PageSize=t.limit,this.getStoreInfo(this.selrows.goodsid)},handleSelect:function(t){this.isTotal=0;for(var e=0;e<t.length;e++)this.isTotal+=Number(t[e].realqty);var i=this.$fomatFloat(Number(this.selrows.quantity)-Number(this.selrows.finishqty),2);this.difference=this.$fomatFloat(i-Number(this.isTotal),2),i<this.isTotal?this.isOut=!0:this.isOut=!1,this.selectList=t,this.$forceUpdate()},changeInput:function(t){if(console.log(t),Number(t.realqty)-Number(t.quantity)>0)return t.realqty=t.quantity,void this.$message.warning("实领数量不能大于库存数量");if(0!=this.$refs.selectItemVal.selection.length){this.isTotal=0;for(var e=this.$refs.selectItemVal.selection,i=0;i<e.length;i++)this.isTotal+=Number(e[i].realqty);var a=this.$fomatFloat(Number(this.selrows.quantity)-Number(this.selrows.finishqty),2);this.difference=this.$fomatFloat(a-Number(this.isTotal),2),a<this.isTotal?this.isOut=!0:this.isOut=!1}},saveRow:function(t){for(var e=0;e<this.lstItem.length;e++)this.lstItem[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0))},searchItem:function(t){""!=t?this.storeParams.SearchPojo={groupuid:t,groupname:t}:this.$delete(this.storeParams,"SearchPojo"),this.storeParams.SearchType=1,this.storeParams.PageNum=1,this.getCurrentRow(this.selrows)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D01M06B1/getPageList";s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,groupname:t,billtype:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},headerStyle:function(t){t.row;var e=t.column,i=(t.rowIndex,t.columnIndex,"background: #F3F4F7; color:#555;padding: 3px 0px 3px 0px;");return"实际出库"==e.label?"background:#eff18c;color:#555;padding: 3px 0px 3px 0px":i}}},L=C,j=(i("bfb0"),Object(y["a"])(L,D,O,!1,null,"7dc0b381",null)),M=j.exports,N=i("4fcf"),T=i("9a2c"),B=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto","min-height":"352 px",width:"100%"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"220px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),i("el-table-column",{attrs:{label:"金额",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.taxamount))])]}}])}),i("el-table-column",{attrs:{label:"订单编号",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.orderno))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}}),i("el-divider",{attrs:{"content-position":"left"}},[t._v("库存信息")]),i("div",{staticClass:"filter-container",staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px",height:"100%"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.searchItem(t.strItemfilter)}},model:{value:t.strItemfilter,callback:function(e){t.strItemfilter=e},expression:"strItemfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.searchItem(t.strItemfilter)}}},[t._v(" 查询 ")])],1),i("div",{staticClass:"istotal"},[t._v(" 合计："),i("span",{ref:"isTotal",style:{color:t.isOut?"#F00":"#606266"}},[t._v(t._s(t.isTotal))])])]),i("div",{staticStyle:{padding:"10px 0px"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectItemVal",staticStyle:{overflow:"auto"},attrs:{data:t.lstItem,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",height:"220px","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}},on:{"row-click":t.saveRow,"selection-change":t.handleSelect}},[i("el-table-column",{attrs:{type:"selection",width:"50",align:"center",fixed:"left"}}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"仓库",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.storename))])]}}])}),i("el-table-column",{attrs:{label:"库位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.location))])]}}])}),i("el-table-column",{attrs:{label:"库存",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),i("el-table-column",{attrs:{label:"批号",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.batchno))])]}}])}),t._l(t.customList,(function(t,e){return[i("el-table-column",{key:e,attrs:{"header-align":"center",align:"center",prop:t.attrkey,label:t.attrname}})]}))],2),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.storeTotal>0,expression:"storeTotal > 0"}],attrs:{total:t.storeTotal,page:t.storeParams.PageNum,limit:t.storeParams.PageSize},on:{"update:page":function(e){return t.$set(t.storeParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.storeParams,"PageSize",e)},pagination:t.GetStoreList}})],1)],1)},V=[],R={components:{Pagination:I["a"]},props:["multi","groupid","selecturl","storeid"],data:function(){return{title:"采购订单",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},strItemfilter:"",storeTotal:0,storeParams:{PageNum:1,PageSize:5,OrderType:1,SearchType:1},lstItem:[],customList:[],isEditOk:!0,isTotal:0,isOut:!1,selectList:[]}},created:function(){this.bindData(),this.getColumn()},methods:{getColumn:function(){var t=this;s["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code?t.customList=e.data.data?e.data.data:[]:t.$message.warning(e.data.msg||"获取失败")})).catch((function(e){t.$message.error("请求错误")}))},getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.getStoreInfo(t.goodsid)},getStoreInfo:function(t){var e=this;this.storeParams.SearchPojo={storeid:this.storeid},s["a"].post("/D04M04B1/geOnlinePageListByGoods?key="+t,JSON.stringify(this.storeParams)).then((function(t){if(200==t.data.code){e.lstItem=t.data.data.list,e.storeTotal=t.data.data.total;for(var i=0;i<e.lstItem.length;i++){var a=e.lstItem[i];e.$set(e.lstItem[i],"finishqty",0);for(var o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)e.$set(e.lstItem[i],o[s].key,o[s].value)}}e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},GetStoreList:function(t){this.storeParams.PageNum=t.page,this.storeParams.PageSize=t.limit,this.getStoreInfo(this.selrows.goodsid)},handleSelect:function(t){this.isTotal=0;for(var e=0;e<t.length;e++)this.isTotal+=Number(t[e].quantity);this.selrows.quantity<this.isTotal?this.isOut=!0:this.isOut=!1,this.selectList=t,this.$forceUpdate()},changeInput:function(t){if(console.log(t),t.quantity-t.quantity>0&&(t.quantity=0,this.$message.warning("实领数量不能大于库存数量")),0!=this.$refs.selectItemVal.selection.length){this.isTotal=0;for(var e=this.$refs.selectItemVal.selection,i=0;i<e.length;i++)this.isTotal+=Number(e[i].quantity);this.selrows.quantity<this.isTotal?this.isOut=!0:this.isOut=!1}},saveRow:function(t){for(var e=0;e<this.lstItem.length;e++)this.lstItem[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0))},searchItem:function(t){""!=t?this.storeParams.SearchPojo={groupuid:t,groupname:t}:this.$delete(this.storeParams,"SearchPojo"),this.storeParams.SearchType=1,this.storeParams.PageNum=1,this.getCurrentRow(this.selrows)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M03B1/getPageList";s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={goodsname:t,goodsuid:t,goodsspec:t,groupuid:t,groupname:t,refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},F=R,z=(i("89b7"),Object(y["a"])(F,B,V,!1,null,"f8b27562",null)),A=z.exports,E=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"220px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据日期",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormat")(e.row.billdate)))])]}}])}),i("el-table-column",{attrs:{label:"车间",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编号",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"货品规格",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"货品单位",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"外部编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.partid))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),i("el-table-column",{attrs:{label:"完成数",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.finishqty))])]}}])}),i("el-table-column",{attrs:{label:"加工单号",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.workuid))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),i("el-divider",{attrs:{"content-position":"left"}},[t._v("库存信息")]),i("div",{staticClass:"filter-container",staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px",height:"100%"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.searchItem(t.strItemfilter)}},model:{value:t.strItemfilter,callback:function(e){t.strItemfilter=e},expression:"strItemfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.searchItem(t.strItemfilter)}}},[t._v(" 查询 ")])],1),i("div",{staticClass:"istotal"},[t._v(" 合计："),i("span",{ref:"isTotal",style:{"margin-right":"10px",color:t.isOut?"#F00":"#606266"}},[t._v(t._s(t.isTotal))]),t._v(" 差值："),i("span",{ref:"isTotal",style:{color:0!=t.difference?"#F00":"#606266"}},[t._v(t._s(t.difference))])])]),i("div",{staticStyle:{padding:"10px 0px"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectItemVal",staticStyle:{overflow:"auto"},attrs:{data:t.lstItem,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",height:"220px","header-cell-style":t.headerStyle,"cell-style":{padding:"4px 0px 4px 0px"}},on:{"row-click":t.saveRow,"selection-change":t.handleSelect}},[i("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"仓库",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.storename))])]}}])}),i("el-table-column",{attrs:{label:"库位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.location))])]}}])}),i("el-table-column",{attrs:{label:"库存",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),i("el-table-column",{attrs:{label:"实际出库",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isEdit?i("el-input",{attrs:{size:"small",placeholder:"实际出库"},on:{input:function(i){return t.changeInput(e.row)},focus:function(t){return t.currentTarget.select()}},model:{value:e.row.realqty,callback:function(i){t.$set(e.row,"realqty",i)},expression:"scope.row.realqty"}}):i("span",[t._v(t._s(e.row.realqty))])]}}])}),i("el-table-column",{attrs:{label:"批号",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.batchno))])]}}])}),t._l(t.customList,(function(t,e){return[i("el-table-column",{key:e,attrs:{"min-width":"50","header-align":"center",align:"center",prop:t.attrkey,label:t.attrname}})]}))],2),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.storeTotal>0,expression:"storeTotal > 0"}],attrs:{total:t.storeTotal,page:t.storeParams.PageNum,limit:t.storeParams.PageSize},on:{"update:page":function(e){return t.$set(t.storeParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.storeParams,"PageSize",e)},pagination:t.GetStoreList}})],1)],1)},G=[],J={components:{Pagination:I["a"]},props:["multi","groupid","storeid","selecturl"],data:function(){return{title:"生产领料",listLoading:!0,lst:[],strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},strItemfilter:"",storeTotal:0,storeParams:{PageNum:1,PageSize:5,OrderType:1,SearchType:1},lstItem:[],customList:[],isEditOk:!0,isTotal:0,difference:0,isOut:!1,selectList:[]}},created:function(){this.bindData(),this.getColumn()},methods:{getColumn:function(){var t=this;s["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code?t.customList=e.data.data?e.data.data:[]:t.$message.warning(e.data.msg||"获取失败")})).catch((function(e){t.$message.error("请求错误")}))},getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t),this.getStoreInfo(t.goodsid)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,s["a"].post("/D04M08R1/getOnlinePageList?groupid="+this.groupid,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo=Object(P["a"])({refno:t,billtitle:t,groupname:t,goodsname:t,goodsuid:t,partid:t,groupuid:t},"groupname",t):this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},getStoreInfo:function(t){var e=this;this.storeParams.SearchPojo={storeid:this.storeid},s["a"].post("/D04M04B1/geOnlinePageListByGoods?key="+t,JSON.stringify(this.storeParams)).then((function(t){if(200==t.data.code){e.lstItem=t.data.data.list,e.storeTotal=t.data.data.total,e.difference=e.$fomatFloat(e.selrows.quantity-e.selrows.finishqty,2);for(var i=0;i<e.lstItem.length;i++){var a=e.lstItem[i];e.$set(a,"finishqty",0),e.$set(a,"realqty",a.quantity);for(var o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)e.$set(e.lstItem[i],o[s].key,o[s].value)}}e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},GetStoreList:function(t){this.storeParams.PageNum=t.page,this.storeParams.PageSize=t.limit,this.getStoreInfo(this.selrows.goodsid)},handleSelect:function(t){this.isTotal=0;for(var e=0;e<t.length;e++)this.isTotal+=Number(t[e].realqty);var i=this.$fomatFloat(Number(this.selrows.quantity)-Number(this.selrows.finishqty),2);this.difference=this.$fomatFloat(i-Number(this.isTotal),2),i<this.isTotal?this.isOut=!0:this.isOut=!1,this.selectList=t,this.$forceUpdate()},changeInput:function(t){if(Number(t.realqty)-Number(t.quantity)>0)return t.realqty=t.quantity,void this.$message.warning("实际出库不能大于库存");if(0!=this.$refs.selectItemVal.selection.length){this.isTotal=0;for(var e=this.$refs.selectItemVal.selection,i=0;i<e.length;i++)this.isTotal+=Number(e[i].realqty);var a=this.$fomatFloat(Number(this.selrows.quantity)-Number(this.selrows.finishqty),2);this.difference=this.$fomatFloat(a-Number(this.isTotal),2),this.selrows.quantity<this.isTotal?this.isOut=!0:this.isOut=!1}},saveRow:function(t){for(var e=0;e<this.lstItem.length;e++)this.lstItem[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0))},searchItem:function(t){""!=t?this.storeParams.SearchPojo={groupuid:t,groupname:t}:this.$delete(this.storeParams,"SearchPojo"),this.storeParams.SearchType=1,this.storeParams.PageNum=1,this.getCurrentRow(this.selrows)},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},headerStyle:function(t){t.row;var e=t.column,i=(t.rowIndex,t.columnIndex,"background: #F3F4F7; color:#555;padding: 3px 0px 3px 0px;");return"实际出库"==e.label?"background:#eff18c;color:#555;padding: 3px 0px 3px 0px":i}}},U=J,W=(i("2bb55"),Object(y["a"])(U,E,G,!1,null,"48c9aaed",null)),H=W.exports,K=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"351px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return t.showForm(e.row.pid)}}},[t._v(" "+t._s(e.row.refno||"单据编码")+" ")])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据日期",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormat")(e.row.billdate)))])]}}])}),i("el-table-column",{attrs:{label:"车间",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编号",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"货品规格",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"货品单位",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),i("el-table-column",{attrs:{label:"完成数",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.finishqty))])]}}])}),i("el-table-column",{attrs:{label:"加工单号",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.workuid))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},Y=[],X={components:{Pagination:I["a"]},props:["multi","groupid"],data:function(){return{title:"生产领料",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,s["a"].post("/D04M08R1/getOnlinePageList?groupid="+this.groupid,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo=Object(P["a"])({refno:t,billtitle:t,groupname:t,goodsname:t,goodsuid:t,partid:t,groupuid:t},"groupname",t):this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},Q=X,Z=(i("272e"),Object(y["a"])(Q,K,Y,!1,null,"6c9844ec",null)),tt=Z.exports,et=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"372px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(" "+t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"外部编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.partid))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),i("el-table-column",{attrs:{label:"SN",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.packsn))])]}}])}),i("el-table-column",{attrs:{label:"仓库名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.storename))])]}}])}),i("el-table-column",{attrs:{label:"库位编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.location))])]}}])}),i("el-table-column",{attrs:{label:"批号",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.batchno))])]}}])}),t._l(t.customList,(function(t,e){return[i("el-table-column",{key:e,attrs:{"header-align":"center",align:"center",prop:t.attrkey,label:t.attrname}})]}))],2)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},it=[],at={components:{Pagination:I["a"]},props:["multi","storeid"],data:function(){return{title:"库存信息",listLoading:!0,lst:[],strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},customList:[]}},created:function(){},mounted:function(){this.bindData()},methods:{getCurrentRow:function(t){console.log(t),this.$forceUpdate(),this.selrows=t,this.$emit("singleSel")},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;return Object(S["a"])(Object(_["a"])().mark((function e(){return Object(_["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.queryParams.SearchType=1,e.next=3,s["a"].post("/D04M04B1/getOnlinePageList?storeid="+t.storeid,JSON.stringify(t.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total?e.data.data.total:0;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 3:return e.next=5,s["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code?t.customList=e.data.data?e.data.data:[]:t.$message.warning(e.data.msg||"获取spu失败")})).catch((function(e){t.$message.error("请求错误")}));case 5:case"end":return e.stop()}}),e)})))()},search:function(t){""!=t?this.queryParams.SearchPojo={packsn:t,goodsname:t,goodsspec:t,goodsuid:t,location:t,batchno:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},ot=at,st=(i("88b7"),Object(y["a"])(ot,et,it,!1,null,"67188e80",null)),nt=st.exports,rt=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"flex-end","margin-top":"-25px"}},[i("el-form",{ref:"formdata",staticStyle:{"max-height":"400px",flex:"1"},attrs:{model:t.formdata,"label-width":"80px"}},[i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:6}},[i("el-form-item",{staticStyle:{"margin-bottom":"4px"},attrs:{label:"货品编码:"}},[t._v(" "+t._s(t.selectList[0].goodsuid)+" ")])],1),i("el-col",{attrs:{span:6}},[i("el-form-item",{staticStyle:{"margin-bottom":"4px"},attrs:{label:"货品名称:"}},[t._v(" "+t._s(t.selectList[0].goodsname)+" ")])],1),i("el-col",{attrs:{span:6}},[i("el-form-item",{staticStyle:{"margin-bottom":"4px"},attrs:{label:"货品规格:"}},[t._v(" "+t._s(t.selectList[0].goodsspec)+" ")])],1),i("el-col",{attrs:{span:6}},[i("el-form-item",{staticStyle:{"margin-bottom":"4px"},attrs:{label:"订单数:"}},[t._v(" "+t._s(t.selectList[0].quantity)+" ")])],1)],1),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:8}},[i("el-form-item",{staticStyle:{"margin-bottom":"6px"},attrs:{label:"本次生成数","label-width":"100px"}},[i("el-input-number",{staticStyle:{width:"80%"},attrs:{size:"small",min:0,"controls-position":"right"},on:{focus:function(t){return t.currentTarget.select()}},model:{value:t.formdata.labelnum,callback:function(e){t.$set(t.formdata,"labelnum",e)},expression:"formdata.labelnum"}})],1)],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{staticStyle:{"margin-bottom":"6px"},attrs:{label:"每张数量"}},[i("el-input-number",{staticStyle:{width:"80%"},attrs:{size:"small",min:0,"controls-position":"right"},on:{focus:function(t){return t.currentTarget.select()}},model:{value:t.formdata.quantity,callback:function(e){t.$set(t.formdata,"quantity",e)},expression:"formdata.quantity"}})],1)],1),i("el-col",{attrs:{span:7}},[i("el-form-item",{staticStyle:{"margin-bottom":"6px"},attrs:{label:"标签分组"}},[i("el-select",{attrs:{placeholder:"请选择",size:"small"},model:{value:t.formdata.glgroupid,callback:function(e){t.$set(t.formdata,"glgroupid",e)},expression:"formdata.glgroupid"}},t._l(t.groupdata,(function(t){return i("el-option",{key:t.id,attrs:{label:t.groupname,value:t.id}})})),1)],1)],1)],1)],1),i("div",{staticStyle:{"margin-bottom":"10px"}},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.btnsave}},[t._v(" 生成")]),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.printButton()}}},[t._v("打印")])],1)],1),i("ve-table",{ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":"60vh","scroll-width":400,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,"column-width-resize-option":t.columnWidthResizeOption,"checkbox-option":t.checkboxOption,editOption:t.editOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,cellSelectionOption:{enable:!0}}}),i("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible},on:{"update:visible":function(e){t.ReportVisible=e}}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return i("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button-group",{staticStyle:{float:"left"}},[i("el-button",{attrs:{type:"print"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="print",t.submitRemoteReport(0)}}},[t._v("云打印")]),i("el-button",{attrs:{type:"preview"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="preview",t.submitRemoteReport(1)}}},[t._v("云预览")])],1),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),i("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[i("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})])],1)},lt=[],dt=(i("2b3d"),i("9861"),i("da92"),{props:["selectList","formdataInfo"],data:function(){var t=this;return{formitem:[],formdata:{labelnum:0,quantity:0,glgroupid:""},totalPrice:0,costgroupjson:"",ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,printType:"print",groupdata:[],multipleSelection:[],lst:[],customData:[],columnWidthResizeOption:{enable:!0,minWidth:50,sizeChange:function(e){var i=e.column,a=e.differWidth,o=e.columnWidth;t.columnResizeInfo.column=i,t.columnResizeInfo.differWidth=a,t.columnResizeInfo.columnWidth=o}},columnResizeInfo:{column:"",differWidth:"",columnWidth:""},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;"price"!=a.field&&"quantity"!=a.field&&"amount"!=a.field||t.countInput(i,a.field),t.getSummary(),t.changeInput()}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.id==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.multipleSelection=i?JSON.parse(JSON.stringify(t.lst)):[]}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},footerData:[]}},watch:{},mounted:function(){this.getgroup(),this.getColumn($["a"])},methods:{btnsave:function(){var t=this;if(0!=this.formdata.quantity)if(this.formdata.labelnum>this.selectList[0].quantity)this.$message.warning("本次生成数不能大于订单数");else if(Number(this.formdata.labelnum)/Number(this.formdata.quantity)>50)this.$message.warning("标签最多不能超过20张");else if(""!=this.formdata.glgroupid){var e=Object.assign({},this.selectList[0]);e.quantity=this.formdata.quantity,e.totalqty=this.formdata.labelnum,e.glgroupid=this.formdata.glgroupid,e.labeltitle=this.formdataInfo.billtitle,e.itemcode=this.selectList[0].goodsuid,e.itemname=this.selectList[0].goodsname,e.itemspec=this.selectList[0].goodsspec,e.itemunit=this.selectList[0].goodsunit,e.accesstype=this.formdataInfo.billtype,e.acceitemid=e.id,s["a"].post("/D04M17B1/createLabel",JSON.stringify(e)).then((function(e){200==e.data.code?t.lst=e.data.data:t.$message.warning(e.data.msg||"获取条码失败")}))}else this.$message.warning("请选择标签分组");else this.$message.warning("每张数量不能为0")},bindData:function(t){var e=this;if(this.formdata.labelnum=this.selectList[0].labelqty?this.$fomatFloat(Number(this.selectList[0].quantity)-Number(this.selectList[0].labelqty),2):this.selectList[0].quantity,t){var i={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};s["a"].post("/D04M17B1/getPageListByCodes?key="+this.selectList[0].id,JSON.stringify(i)).then((function(t){200==t.data.code?e.lst=t.data.data.list:e.$message.warning(t.data.msg||"获取条码失败")}))}},getColumn:function(t){var e=this,i=(this.$createElement,[]);t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:t.minwidth,displaymark:t.displaymark,fixed:!1,ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",edit:!!t.editmark,renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("createdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);var s=a("span",[o[t.itemcode]]);return s}};i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.customData=i,this.$forceUpdate()},getgroup:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};s["a"].post("/D04M17B1GRPC/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.groupdata=e.data.data.list)}))},printButton:function(){var t=this;0!=this.multipleSelection.length?s["a"].get("/SaReports/getListByModuleCode?code=D04M17B1List").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0})):this.$message.warning("请选择打印内容!")},submitReport:function(){var t=this;""!=this.reportModel?s["a"].get("/D04M17B1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.formdata.printcount+=1,t.ReportVisible=!1;var i=[];i.push(e.data);var a=window.URL.createObjectURL(new Blob(i,{type:"application/pdf"}));t.pdfUrl=a,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},submitRemoteReport:function(t){var e=this;if(""!=this.reportModel){var i="/D04M17B1/printWebList?ptid="+this.reportModel+"&redis=1";"preview"==this.printType&&(i="/D04M17B1/printWebList?ptid="+this.reportModel+"&cmd=1&redis=1"),s["a"].post(i,JSON.stringify(this.multipleSelection)).then((function(i){200==i.data.code?null==i.data.data||""==i.data.data?e.$message.warning(i.data.msg||"打印失败"):0===t||1===t?"连接成功"!=e.$store.state.webSocketMsg.webSocketState?(e.$setWs.initWebSocket("ws://".concat(e.wsUrl,":18800")),setTimeout((function(){e.remoteprinting(i.data.data)}),500)):setTimeout((function(){e.remoteprinting(i.data.data)}),100):2===t&&e.remoteReport(i.data.data):e.$message.warning(i.data.msg||"打印失败")}))}else this.$message.warning("打印模板不能为空!")},remoteprinting:function(t){"连接成功"==this.$store.state.webSocketMsg.webSocketState?(this.formdata.printcount+=1,this.$setWs.wsSend(t),this.ReportVisible=!1):this.$message.warning("连接打印服务器失败")},remoteReport:function(t){var e=this;this.$request.post("/SaReports/getGrfReport",t,{responseType:"blob"}).then((function(t){if(200===t.status){var i=[];i.push(t.data);var a=window.URL.createObjectURL(new Blob(i,{type:"application/pdf"}));e.pdfUrl=a,e.isViewPdf20=!0}}))}},destroyed:function(){document.onkeydown=function(){},this.$setWs.wsClose()}}),ct=dt,ut=(i("b201"),Object(y["a"])(ct,rt,lt,!1,null,"3654ff7a",null)),mt=ut.exports,pt={name:"Elitem",components:{SelGoods:q["a"],SelDelivery:N["a"],SelOrder:M,SelBuyOrder:T["a"],SelBuyOrderOut:A,SelPicking:H,SelMaterial:tt,SelStock:nt,LabelItem:mt},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return Object(P["a"])(Object(P["a"])(Object(P["a"])({lst:[],multi:0,keynum:0,OrderVisible:!1,BuyOrderOutVisible:!1,BuyOrderVisible:!1,pickingVisible:!1,materialVisible:!1,productVisible:!1,weizhiVisible:!1,goodsVisible:!1,stockVisible:!1,seldeliveryvisible:!1,labelvisible:!1,setColumsVisible:!1,saleorderVisible:!1,saleorderIndex:-1,selecturl:"",tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,index:0,tableForm:$["c"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;if(t.editmarkfiles.includes(a.field)){t.countfiles.includes(a.field)&&t.changeInput("",i,a.field);var o=t.customList.findIndex((function(t){return t.attrkey==a.field}));-1!=o&&t.setAttributeJson(i,i.rownum)}}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}}},"footerData",[]),"cellAutofillOption",{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData,a=Object.keys(i[0])[1];if(!t.editmarkfiles.includes(a))return!1},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));if(-1!=s){t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[s],Object.keys(o)[1]);var n=t.customList.findIndex((function(t){return t.attrkey==Object.keys(o)[1]}));-1!=n&&t.setAttributeJson(t.lst[s],s)}}}}),"clipboardOption",{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}})},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){0==this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var s=JSON.parse(t[i].attributejson);for(a=0;a<s.length;a++)t[i][s[a].key]=s[a].value}}this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(S["a"])(Object(_["a"])().mark((function e(){var i;return Object(_["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=$["c"],t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn($["c"].formcode,i,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,operationColumn:!!t.operationmark,renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex),n="",r=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));if(-1!=r){var l=e.customList[r].valuejson?e.customList[r].valuejson.split(","):[];return n=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]])]),a("div",{directives:[{name:"show",value:l.length}]},[a("div",{style:"width:0;height:0"},[a("el-select",{class:"wpselect",style:"width:0;height:0;opacity: 0;",attrs:{size:"small",id:t.itemcode+o.rownum+$["c"].formcode},on:{change:function(i){o[t.itemcode]=i,e.setAttributeJson(o,o.rownum)}},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}},[l.map((function(t,e){return a("el-option",{key:e,attrs:{label:t,value:t},style:"max-width:200px"})}))])]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-arrow-down",style:"font-weight:bold;font-size:14px;",on:{click:function(){document.getElementById(t.itemcode+o.rownum+$["c"].formcode).click()}}})])]),n}return"goodsuid"==t.itemcode?(n=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}}),n):"machuid"==t.itemcode?(n="其他入库"==e.formdata.billtype?a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("span",{class:o.disannulmark?"textlinethrough":"",style:"flex:1"},[o[t.itemcode]]),a("i",{directives:[{name:"show",value:!!e.formdata.id}],class:"writePacksn el-icon-circle-plus-outline",on:{click:function(){e.saleorderVisible=!0,e.saleorderIndex=s}}})]):a("span",[" ",o[t.itemcode]]),n):(n=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]),n)}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1,this.$forceUpdate()},getSummary:function(){this.$getSummary(this,["goodsuid","amount","taxamount","quantity"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],n=0;n<i.length;n++){var r=i[n];this.lst[e+o][r]=s[r].replace(/^\s*|\s*$/g,""),this.countfiles.includes(r)&&this.changeInput("",this.lst[e+o],r);var l=this.customList.findIndex((function(t){return t.attrkey==r}));-1!=l&&this.setAttributeJson(this.lst[e+o],e+o)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],s={key:o.attrkey,value:t[o.attrkey]?t[o.attrkey].replace(/\s*/g,""):""};""!=s.value&&i.push(s)}0==i.length?this.lst[e].attributejson="":this.lst[e].attributejson=JSON.stringify(i),this.$forceUpdate()},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i),this.getSummary()},getAdd:function(t){if("发货出库"!=this.formdata.billtype&&"客退入库"!=this.formdata.billtype&&"收货入库"!=this.formdata.billtype&&"购退出库"!=this.formdata.billtype&&"领料出库"!=this.formdata.billtype&&"退料入库"!=this.formdata.billtype||this.formdata.groupid){if(this.multi=t,"发货出库"==this.formdata.billtype)this.OrderVisible=!0;else if("客退入库"==this.formdata.billtype)this.seldeliveryvisible=!0;else if("收货入库"==this.formdata.billtype)this.BuyOrderVisible=!0;else if("购退出库"==this.formdata.billtype)this.BuyOrderOutVisible=!0;else if("领料出库"==this.formdata.billtype)this.pickingVisible=!0;else if("退料入库"==this.formdata.billtype)this.materialVisible=!0;else if("其他入库"==this.formdata.billtype||"盘盈入库"==this.formdata.billtype)this.goodsVisible=!0;else if("其他出库"==this.formdata.billtype||"报废出库"==this.formdata.billtype||"盘亏出库"==this.formdata.billtype){if(!this.formdata.storeid)return void this.$message.warning("请选择仓库");this.stockVisible=!0}}else this.$message.warning("请选择往来单位")},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getScan:function(t){var e=this;if(1!=this.multipleSelection.length)return this.$message.warning("请选择一条单据内容");this.labelvisible=!0,setTimeout((function(){e.$refs.labelItem.bindData(t)}),100)},selStock:function(){var t=this.$refs.selStock.$refs.selectVal.selection;if(0!=t.length){this.stockVisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.batchno=i.batchno,a.citeitemid=i.id,a.citeuid=i.refno,a.customer=i.customer,a.custpo=i.custpo,a.location=i.location,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.quantity=i.quantity,a.packsn=i.packsn,a.attributejson=i.attributejson,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selGoods:function(){var t=this.$refs.selGoods.$refs.selectgoods.selection;if(0!=t.length){this.goodsVisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=i.outprice?i.outprice:0,o=Object.assign({},u);o.goodsid=i.id,o.goodsuid=i.goodsuid,o.goodsname=i.goodsname,o.goodsunit=i.goodsunit,o.goodsspec=i.goodsspec,o.price=a,o.taxprice=a,0!=this.idx&&(o.pid=this.idx),this.lst.push(o)}}else this.$message.warning("请选择单据内容")},selMaterial:function(){var t=this.$refs.selMaterial.$refs.selectVal.selection;if(0!=t.length){this.materialVisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.batchno=i.batchno,a.citeitemid=i.id,a.citeuid=i.refno,a.customer=i.customer,a.custpo=i.custpo,a.location=i.location,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.packsn=i.packsn,a.attributejson=i.attributejson,"领料单"!=i.billtype&&"生产领料"!=i.billtype||(a.quantity=i.finishqty),0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selPicking:function(){var t=this.$refs.selPicking.selrows,e=this.$refs.selPicking.$refs.selectItemVal.selection,i=this.$refs.selPicking.isTotal;if(0!=e.length){this.pickingVisible=!1;var a=this.$fomatFloat(t.quantity-t.finishqty,2);1!=e.length&&i>a&&this.$confirm("库存数量("+i+") > 单据数量("+a+"), 需要手动调整","提示",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!1,type:"warning"});for(var o=0;o<e.length;o++){var s=e[o],n=Object.assign({},u);n.goodsid=s.goodsid,n.goodsuid=s.goodsuid,n.goodsname=s.goodsname,n.goodsunit=s.goodsunit,n.goodsspec=s.goodsspec,n.partid=s.partid,n.batchno=s.batchno,n.citeitemid=t.id,n.citeuid=t.refno,n.custpo=t.custpo,n.customer=t.customer,n.location=s.location,n.machgroupid=t.machgroupid,n.machitemid=t.machitemid,n.machuid=t.machuid,n.mainplanitemid=t.mainplanitemid,n.mainplanuid=t.mainplanuid,n.mrpitemid=t.mrpitemid,n.mrpuid=t.mrpuid,n.quantity=i,n.packsn=t.packsn,n.attributejson=t.attributejson,0!=this.idx&&(n.pid=this.idx),this.lst.push(n)}}else this.$message.warning("请选择单据内容")},selBuyOrder:function(){var t=this.$refs.selBuyOrder.$refs.selectVal.selection;if(0!=t.length){this.BuyOrderVisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.price=i.price,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.citeuid=i.refno,a.citeitemid=i.id,a.custpo=i.custpo,a.customer=i.customer,a.location=i.location,a.batchno=i.batchno,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.packsn=i.packsn,a.attributejson=i.attributejson,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selBuyOrderOut:function(){var t=this.$refs.selBuyOrderOut.selrows,e=this.$refs.selBuyOrderOut.$refs.selectItemVal.selection,i=this.$refs.selBuyOrderOut.isTotal;if(0!=e.length){this.BuyOrderOutVisible=!1;var a=this.$fomatFloat(t.quantity-t.finishqty,2);1!=e.length&&i>a&&this.$confirm("库存数量("+i+") > 单据数量("+a+"), 需要手动调整","提示",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!1,type:"warning"});for(var o=0;o<e.length;o++){var s=e[o],n=Object.assign({},u);n.goodsid=s.goodsid,n.goodsuid=s.goodsuid,n.goodsname=s.goodsname,n.goodsunit=s.goodsunit,n.goodsspec=s.goodsspec,n.partid=s.partid,n.amount=t.amount,n.price=t.price,n.taxamount=t.taxamount,n.taxprice=t.taxprice,n.taxtotal=t.taxtotal,n.itemtaxrate=t.itemtaxrate,n.quantity=t.finishqty,n.citeuid=t.refno,n.citeitemid=t.id,n.custpo=t.custpo,n.customer=t.customer,n.location=s.location,n.batchno=s.batchno,n.machgroupid=t.machgroupid,n.machitemid=t.machitemid,n.machuid=t.machuid,n.mainplanitemid=t.mainplanitemid,n.mainplanuid=t.mainplanuid,n.mrpitemid=t.mrpitemid,n.mrpuid=t.mrpuid,n.packsn=s.packsn,n.attributejson=t.attributejson,0!=this.idx&&(n.pid=this.idx),this.lst.push(n)}}else this.$message.warning("请选择单据内容")},selOrder:function(){var t=this.$refs.selOrder.selrows,e=this.$refs.selOrder.$refs.selectItemVal.selection;if(0!=e.length){this.OrderVisible=!1;var i=this.$refs.selOrder.isTotal,a=this.$fomatFloat(t.quantity-t.finishqty,2);i>a&&this.$confirm("库存数量("+i+") > 单据数量("+a+"), 需要手动调整","提示",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!1,type:"warning"});for(var o=0;o<e.length;o++){var s=e[o],n=Object.assign({},u);n.goodsid=s.goodsid,n.goodsuid=s.goodsuid,n.goodsname=s.goodsname,n.goodsunit=s.goodsunit,n.goodsspec=s.goodsspec,n.partid=s.partid,n.amount=t.amount,n.price=t.price,n.taxamount=t.taxamount,n.taxprice=t.taxprice,n.taxtotal=t.taxtotal,n.itemtaxrate=t.itemtaxrate,n.quantity=i,n.citeuid=t.refno,n.citeitemid=t.id,n.custpo=t.custpo,n.customer=t.customer,n.location=s.location,n.batchno=s.batchno,n.machgroupid=t.machgroupid,n.machitemid=t.machitemid,n.machuid=t.machuid,n.mainplanitemid=t.mainplanitemid,n.mainplanuid=t.mainplanuid,n.mrpitemid=t.mrpitemid,n.mrpuid=t.mrpuid,n.packsn=s.packsn,n.attributejson=t.attributejson,n.inveid=s.id,0!=this.idx&&(n.pid=this.idx),this.lst.push(n)}}else this.$message.warning("请选择单据内容")},selDelivery:function(){var t=this.$refs.selDelivery.$refs.selectVal.selection;if(0!=t.length){this.seldeliveryvisible=!1;for(var e=0;e<t.length;e++){var i=Object.assign({},u);i.goodsid=Item.goodsid,i.goodsuid=Item.goodsuid,i.goodsname=Item.goodsname,i.goodsunit=Item.goodsunit,i.goodsspec=Item.goodsspec,i.partid=Item.partid,i.amount=Item.amount,i.price=Item.price,i.taxamount=Item.taxamount,i.taxprice=Item.taxprice,i.taxtotal=Item.taxtotal,i.itemtaxrate=Item.itemtaxrate,i.quantity=this.$fomatFloat(Item.quantity-Item.finishqty,2),i.citeuid=Item.refno,i.citeitemid=Item.id,i.custpo=Item.custpo,i.customer=Item.customer,i.location=Item.location,i.batchno=Item.batchno,i.machgroupid=Item.machgroupid,i.machitemid=Item.machitemid,i.machuid=Item.machuid,i.mainplanitemid=Item.mainplanitemid,i.mainplanuid=Item.mainplanuid,i.mrpitemid=Item.mrpitemid,i.mrpuid=Item.mrpuid,i.packsn=Item.packsn,i.attributejson=Item.attributejson,0!=this.idx&&(i.pid=this.idx),this.lst.push(i)}}else this.$message.warning("请选择单据内容")}}},ht=pt,ft=(i("3d3a"),Object(y["a"])(ht,x,k,!1,null,"63a6180e",null)),gt=ft.exports,bt=i("4155"),yt=i("dcb4"),wt={name:"FormEdit",components:{FormTemp:yt["a"],EditHeader:v,EditItem:gt,D04M01M1List:function(){return Promise.all([i.e("chunk-commons"),i.e("chunk-054ab98a")]).then(i.bind(null,"fa11"))}},props:["idx","isDialog","initData","billcode"],data:function(){return{title:"出入库单",operateBar:m,processBar:p,formdata:{abbreviate:"",billdate:new Date,billtitle:"",billtype:"发货出库",direction:"出库单",groupid:"",groupname:"",groupuid:"",item:[],operator:"",orguid:"",plusinfo:"",refno:"",returnuid:"",storecode:"",storeid:"",storename:"",summary:"",tenantid:"",typecode:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:bt["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D04M01M1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):bt["a"],t.formtemplate.footer.type||(t.formtemplate.footer=bt["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&s["a"].get("/D04M01R1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error(e||"请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.salesman=t.seller,this.formdata.abbreviate=t.abbreviate,this.formdata.grouplevel=t.grouplevel},autoClear:function(){this.formdata.groupname="",this.formdata.groupid="",this.formdata.salesman="",this.formdata.abbreviate="",this.formdata.grouplevel=""},getStoreName:function(t){this.formdata.storecode=t.storecode,this.formdata.storename=t.storename,this.formdata.storeid=t.id},autoStoreClear:function(){this.formdata.storecode="",this.formdata.storename="",this.formdata.storeid=""},printMultiServer:function(){this.$refs.PrintMultiServer.printButton(0,1)},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i={item:[]};i=this.$getParam(c,i,this.formdata),0==this.idx?r.add(i).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):r.update(i).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){r.delete(t)})).catch((function(){}))},approval:function(){r.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?r.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){r.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},getScan:function(){this.$refs.elitem.getScan(!0)},changeBillType:function(t){console.log(t,"changeBillType"),this.formdata.item=[],this.formdata.groupid="",this.formdata.groupname="",this.formdata.groupuid="",this.formdata.direction="购退出库"==t||"发货出库"==t||"领料出库"==t||"其他出库"==t||"报废出库"==t||"盘亏出库"==t?"出库单":"入库单"},hongChong:function(){var t=this;this.$confirm("红冲后不可退回，是否确认将该单据转为红冲单？","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((function(){s["a"].get("/D04M01R1/createRed?key="+t.idx).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"红冲成功"),t.$emit("changeIdx",e.data.data.id),t.$emit("bindData"),t.bindData()):t.$message.warning(e.data.msg||"红冲失败")})).catch((function(e){t.$message.warning(e||"红冲失败")}))})).catch((function(){}))},billSwitch:function(t){if(console.log(this.initData,t),"D01M06B3"==t){this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billtitle="【"+this.initData.refno+"】返工补发单转入",this.formdata.billtype="发货出库",this.formdata.direction="出库单",this.formdata.item=[];for(var e=0;e<this.initData.item.length;e++){var i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.price=i.price,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.custpo=i.custpo,a.citeuid=this.initData.refno,a.citeitemid=i.id,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&this.formdata.item.push(a)}}else if("D01M06B2"==t){this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,"其他退货"==this.initData.billtype?(this.formdata.billtype="客退入库",this.formdata.direction="入库单",this.formdata.billtitle="【"+this.initData.refno+"】其他退货单转入"):(this.formdata.billtype="发货出库",this.formdata.direction="出库单",this.formdata.billtitle="【"+this.initData.refno+"】其他发货单转入"),this.formdata.item=[];for(e=0;e<this.initData.item.length;e++){i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.price=i.price,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.custpo=i.custpo,a.citeuid=this.initData.refno,a.citeitemid=i.id,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&this.formdata.item.push(a)}}else if("D01M06B1"==t){this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,"订单退货"==this.initData.billtype?(this.formdata.billtype="客退入库",this.formdata.direction="入库单",this.formdata.billtitle="【"+this.initData.refno+"】退货单转入"):(this.formdata.billtype="发货出库",this.formdata.direction="出库单",this.formdata.billtitle="【"+this.initData.refno+"】发货单转入"),this.formdata.item=[];for(e=0;e<this.initData.item.length;e++){i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.price=i.price,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.custpo=i.custpo,a.citeuid=this.initData.refno,a.citeitemid=i.id,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&this.formdata.item.push(a)}}else if("D03M03B1"==t){"采购验收"==this.initData.billtype?(this.formdata.billtype="收货入库",this.formdata.direction="入库单"):(this.formdata.billtype="购退出库",this.formdata.direction="出库单"),this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billtitle="【"+this.initData.refno+"】采购单转入",this.formdata.item=[];for(e=0;e<this.initData.item.length;e++){i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.price=i.price,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.custpo=i.custpo,a.citeuid=this.initData.refno,a.citeitemid=i.id,a.orderuid=i.orderno,a.orderitemid=i.orderitemid,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&this.formdata.item.push(a)}}else if("D03M03B2"==t){"其他收货"==this.initData.billtype?(this.formdata.billtype="收货入库",this.formdata.direction="入库单"):(this.formdata.billtype="购退出库",this.formdata.direction="出库单"),this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billtitle="【"+this.initData.refno+"】其他收货转入",this.formdata.item=[];for(e=0;e<this.initData.item.length;e++){i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.price=i.price,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.custpo=i.custpo,a.citeuid=this.initData.refno,a.citeitemid=i.id,a.orderuid=i.orderno,a.orderitemid=i.orderitemid,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&this.formdata.item.push(a)}}else if("D04M08B1"==t){"生产领料"==this.initData.billtype||"领料单"==this.initData.billtype||"外协领料"==this.initData.billtype?(this.formdata.billtype="领料出库",this.formdata.direction="出库单"):(this.formdata.billtype="退料入库",this.formdata.direction="入库单"),this.formdata.billtitle="【"+this.initData.refno+"】"+this.initData.billtype+"转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.item=[];for(e=0;e<this.initData.item.length;e++){i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.custpo=i.custpo,a.citeuid=this.initData.refno,a.citeitemid=i.id,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&this.formdata.item.push(a)}}else if("D01M06B3"==t){this.formdata.billtype="退料入库",this.formdata.direction="入库单",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billtitle="【"+this.initData.refno+"】退货返工单转入",this.formdata.item=[];for(e=0;e<this.initData.item.length;e++){i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.price=i.price,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.custpo=i.custpo,a.citeuid=this.initData.refno,a.citeitemid=i.id,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&this.formdata.item.push(a)}}else if("D05M01B1"==t){this.formdata.billtype="生产入库",this.formdata.direction="入库单",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billtitle="【"+this.initData.refno+"】生产加工单转入",this.formdata.item=[];for(e=0;e<this.initData.item.length;e++){i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.price=i.price,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.custpo=i.custpo,a.citeuid=this.initData.refno,a.citeitemid=i.id,a.workuid=this.initData.refno,a.workitemid=i.id,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&this.formdata.item.push(a)}}else if("D05M02B1"==t){this.formdata.billtype="加工入库",this.formdata.direction="入库单",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billtitle="【"+this.initData.refno+"】委制工单转入",this.formdata.item=[];for(e=0;e<this.initData.item.length;e++){i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.price=i.price,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,a.quantity=this.$fomatFloat(i.subqty-i.finishqty,2),a.custpo=i.custpo,a.citeuid=this.initData.refno,a.citeitemid=i.id,a.subcuid=this.initData.refno,a.subcitemid=i.id,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&(this.$refs.elitem.changeInput("",a,"quantity"),this.formdata.item.push(a))}}else if("D05M02B2"==t){console.log(t,"code"),this.formdata.billtype="加工入库",this.formdata.direction="入库单",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billtitle="【"+this.initData.refno+"】委制工单转入",this.formdata.item=[];for(e=0;e<this.initData.item.length;e++){i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.price=i.price,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.custpo=i.custpo,a.citeuid=this.initData.refno,a.citeitemid=i.id,a.subcuid=this.initData.refno,a.subcitemid=i.id,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&(this.$refs.elitem.changeInput("",a,"quantity"),this.formdata.item.push(a))}}else if("SYSM04B6"==t){this.formdata.billtype="其他入库",this.formdata.direction="入库单",this.formdata.billtitle="库存导入",this.formdata.item=[];for(e=0;e<this.initData.item.length;e++){i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.goodsmaterial=i.goodsmaterial,a.partid=i.partid,a.price=i.price,a.quantity=i.quantity,a.packsn=i.packsn?i.packsn:"",a.attributejson=i.attributejson?i.attributejson:"",a.batchno=i.batchno?i.batchno:"",a.location=i.location?i.location:"",1!=i.virtualitem&&(this.$refs.elitem.changeInput("",a,"quantity"),this.formdata.item.push(a))}}else if("DD03M03B1List"==t){"采购验收"==this.initData[0].billtype?(this.formdata.billtype="收货入库",this.formdata.direction="入库单"):(this.formdata.billtype="购退出库",this.formdata.direction="出库单"),this.formdata.groupid=this.initData[0].groupid,this.formdata.groupid&&(this.formdata.groupname=this.initData[0].groupname,this.formdata.groupuid=this.initData[0].groupuid),this.formdata.billtitle="【"+this.initData[0].refno+"】采购验收单转入",this.formdata.item=[];for(e=0;e<this.initData.length;e++){i=this.initData[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.price=i.price,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.custpo=i.custpo,a.citeuid=i.refno,a.citeitemid=i.id,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&this.formdata.item.push(a)}}else if("D04M08B1List"==t){"生产领料"!=this.initData[0].billtype&&"领料单"!=this.initData[0].billtype||(this.formdata.billtype="领料出库",this.formdata.direction="出库单"),this.formdata.billtitle="【"+this.initData[0].refno+"】"+this.initData[0].billtype+"转入",this.formdata.groupid=this.initData[0].groupid,this.formdata.groupid&&(this.formdata.groupname=this.initData[0].groupname,this.formdata.groupuid=this.initData[0].groupuid),this.formdata.item=[];for(e=0;e<this.initData.length;e++){i=this.initData[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.custpo=i.custpo,a.citeuid=i.refno,a.citeitemid=i.id,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&this.formdata.item.push(a)}}else if("D05M03B1SC"==t){this.formdata.billtype="厂制入库",this.formdata.direction="入库单",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billtitle="【"+this.initData.refno+"】生产验收单转入",this.formdata.item=[];for(e=0;e<this.initData.item.length;e++){i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.goodsmaterial=i.goodsmaterial,a.partid=i.partid,a.itemtaxrate=i.itemtaxrate,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.price=i.price,a.amount=this.$fomatFloat(a.quantity*a.price,2),a.taxprice=i.taxprice,a.taxamount=this.$fomatFloat(a.quantity*a.taxprice,2),a.taxtotal=this.$fomatFloat(a.taxamount-a.amount,2),a.custpo=i.custpo,a.citeuid=this.initData.refno,a.citeitemid=i.id,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&this.formdata.item.push(a)}}else if("D05M03B1"==t){this.formdata.billtype="委外入库",this.formdata.direction="入库单",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billtitle="【"+this.initData.refno+"】生产验收单转入",this.formdata.item=[];for(e=0;e<this.initData.item.length;e++){i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.goodsmaterial=i.goodsmaterial,a.partid=i.partid,a.itemtaxrate=i.itemtaxrate,a.quantity=this.$fomatFloat(i.quantity-i.finishqty,2),a.price=i.price,a.amount=this.$fomatFloat(a.quantity*a.price,2),a.taxprice=i.taxprice,a.taxamount=this.$fomatFloat(a.quantity*a.taxprice,2),a.taxtotal=this.$fomatFloat(a.taxamount-a.amount,2),a.custpo=i.custpo,a.citeuid=this.initData.refno,a.citeitemid=i.id,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mainplanitemid=i.mainplanitemid,a.mainplanuid=i.mainplanuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.attributejson=i.attributejson,a.location=i.location,a.batchno=i.batchno,1!=i.virtualitem&&this.formdata.item.push(a)}}}}},vt=wt,xt=(i("36d5"),Object(y["a"])(vt,a,o,!1,null,"23bae785",null));e["default"]=xt.exports},"1dd5":function(t,e,i){},"272e":function(t,e,i){"use strict";i("1dd5")},"2bb55":function(t,e,i){"use strict";i("42b3")},"2cb7":function(t,e,i){},"36d5":function(t,e,i){"use strict";i("4f49")},"3d3a":function(t,e,i){"use strict";i("a6a8")},"3e59":function(t,e,i){"use strict";i("2cb7")},4155:function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"出入库单",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"收货入库",value:"收货入库"},{label:"购退出库",value:"购退出库"},{label:"发货出库",value:"发货出库"},{label:"客退入库",value:"客退入库"},{label:"领料出库",value:"领料出库"},{label:"退料入库",value:"退料入库"},{label:"生产入库",value:"生产入库"},{label:"加工入库",value:"加工入库"},{label:"其他入库",value:"其他入库"},{label:"其他出库",value:"其他出库"},{label:"报废出库",value:"报废出库"},{label:"盘盈入库",value:"盘盈入库"},{label:"盘亏出库",value:"盘亏出库"}],required:!0},{col:5,code:"direction",label:"操作方向",type:"input",methods:"",param:"",readonly:!0},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"客户",searchtype:"customer",type:"autocomplete",methods:"",param:"",required:!0,show:"this.formdata.billtype == '发货出库' || this.formdata.billtype == '客退入库'"},{col:5,code:"groupname",label:"供应商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",required:!0,show:"this.formdata.billtype == '收货入库' || this.formdata.billtype == '购退出库'"},{col:5,code:"groupname",label:"车间",searchtype:"workshop",type:"autocomplete",methods:"",param:"",required:!0,show:"this.formdata.billtype == '领料出库' || this.formdata.billtype == '退料入库'|| this.formdata.billtype == '生产入库'"},{col:5,code:"groupname",label:"外协厂商",searchtype:"factory",type:"autocomplete",methods:"",param:"",required:!0,show:"this.formdata.billtype == '加工入库' "},{col:5,code:"groupname",label:"往来单位",searchtype:"group",type:"autocomplete",methods:"",param:"",required:!0,show:"this.formdata.billtype == '其他入库'||this.formdata.billtype == '其他出库' "},{col:5,code:"storename",label:"仓库名称",searchtype:"store",type:"autocomplete",methods:"",param:"",required:!0},{col:5,code:"orguid",label:"原始单号",type:"input",methods:"",param:"",show:"!!this.formdata.orguid"},{col:5,code:"returnuid",label:"红冲单号",type:"input",methods:"",param:"",show:"!!this.formdata.returnuid"}]}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",type:"input",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},"42b3":function(t,e,i){},4893:function(t,e,i){},"4f49":function(t,e,i){},"4fcf":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"发货单号",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"发货日期",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormat")(e.row.billdate)))])]}}])}),i("el-table-column",{attrs:{label:"客户",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.itemname))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),i("el-table-column",{attrs:{label:"含税金额",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.taxamount))])]}}])})],1)],1),i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},o=[],s=(i("e9c4"),i("b775")),n={props:["multi","groupid","selecturl"],data:function(){return{title:"发货信息",listLoading:!0,lst:[],strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel")},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D01M06B1/getPageList";s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={groupuid:t,groupname:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},r=n,l=(i("0ba2"),i("2877")),d=Object(l["a"])(r,a,o,!1,null,"0fad5116",null);e["a"]=d.exports},"5c73":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{},[i("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?i("ul",{},t._l(t.lst,(function(e,a){return i("li",{key:a,class:t.radio==a?"active":"",on:{click:function(i){t.getCurrentRow(e),t.radio=a}}},[i("span",[t._v(t._s(e.dictvalue))])])})),0):i("div",{staticClass:"noData"},[t._v("暂无数据")])]),i("div",{staticClass:"foots"},[i("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),i("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?i("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[i("div",{staticClass:"dialog-body customDialog"},[i("div",{staticClass:"left"},t._l(t.lst,(function(e,a){return i("p",{key:a,class:t.ActiveIndex==a?"isActive":"",on:{click:function(e){t.ActiveIndex=a}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),i("div",{staticClass:"right"},[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),i("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},o=[],s=(i("a434"),i("e9c4"),i("b775")),n=i("333d"),r=i("b0b8"),l={components:{Pagination:n["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],s["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var i=0;i<t.formdata.item.length;i++)t.lst.push(t.formdata.item[i])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.addItem(i)})).catch((function(t){console.log(t)}))},addItem:function(t){r.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:r.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.lst[t.ActiveIndex].dictvalue=i,r.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=r.getFullChars(i)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,s["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},d=l,c=(i("af2b"),i("2877")),u=Object(c["a"])(d,a,o,!1,null,"d2ba3d7a",null);e["a"]=u.exports},"5e63":function(t,e,i){"use strict";i.d(e,"e",(function(){return a})),i.d(e,"d",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return n})),i.d(e,"b",(function(){return r}));var a={formcode:"D04M01R1Th",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Mat_Access.refno"},{itemcode:"direction",itemname:"方向",minwidth:"50",displaymark:1,overflow:1,datasheet:"Mat_Access.direction"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Access.billtype"},{itemcode:"billtitle",itemname:"单据主题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Access.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Access.billdate"},{itemcode:"storename",itemname:"仓库名称",minwidth:"70",displaymark:1,overflow:1,datasheet:"Mat_Access.storename"},{itemcode:"groupname",itemname:"往来单位",minwidth:"70",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"operator",itemname:"经办人",minwidth:"70",displaymark:1,overflow:1,datasheet:"Mat_Access.operator"},{itemcode:"itemcount",itemname:"款数",minwidth:"60",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Access.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Access.lister"},{itemcode:"createdate",itemname:"新建日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Access.createdate"}]},o={formcode:"D04M01R1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",datasheet:"Mat_Access.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Access.billtype"},{itemcode:"billtitle",itemname:"单据主题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Access.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Access.billdate"},{itemcode:"groupname",itemname:"往来单位",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"batchno",itemname:"批号",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_AccessItem.batchno"},{itemcode:"location",itemname:"库位",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_AccessItem.location"},{itemcode:"quantity",itemname:"数量",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_AccessItem.quantity"},{itemcode:"remark",itemname:"备注",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_AccessItem.remark"}]},s={formcode:"D04M01R1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"60",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:0,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:0,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",displaymark:0,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:0,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:0,overflow:1,editmark:1},{itemcode:"batchno",itemname:"批号",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"location",itemname:"库位",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"machuid",itemname:"销售单",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"120",displaymark:1,overflow:1,editmark:1}]},n={formcode:"labelItem",item:[{itemcode:"labelcode",itemname:"预生成条码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1},{itemcode:"createdate",itemname:"生成日期",minwidth:"80",displaymark:1,overflow:1}]},r={formcode:"D04M01R1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",datasheet:"Mat_Access.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Access.billtype"},{itemcode:"billtitle",itemname:"单据主题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Access.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Access.billdate"},{itemcode:"groupname",itemname:"往来单位",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"batchno",itemname:"批号",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_AccessItem.batchno"},{itemcode:"location",itemname:"库位",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_AccessItem.location"},{itemcode:"quantity",itemname:"数量",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_AccessItem.quantity"},{itemcode:"remark",itemname:"备注",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_AccessItem.remark"}]}},"7de4":function(t,e,i){},"87cd":function(t,e,i){},8844:function(t,e,i){},8885:function(t,e,i){},"88b7":function(t,e,i){"use strict";i("87cd")},"89b7":function(t,e,i){"use strict";i("1156")},"94da":function(t,e,i){"use strict";i("c611")},"9a2c":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto","min-height":"352 px",width:"100%"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),i("el-table-column",{attrs:{label:"金额",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.taxamount))])]}}])}),i("el-table-column",{attrs:{label:"订单编号",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.orderno))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],s=(i("e9c4"),i("b775")),n=i("333d"),r={components:{Pagination:n["a"]},props:["multi","groupid","selecturl"],data:function(){return{title:"采购订单",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M03B1/getPageList";s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={goodsname:t,goodsuid:t,goodsspec:t,groupuid:t,groupname:t,refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},l=r,d=(i("3e59"),i("2877")),c=Object(d["a"])(l,a,o,!1,null,"3efad6b9",null);e["a"]=c.exports},"9bda":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")]),i("div",{staticStyle:{float:"right"}},[i("el-radio-group",{attrs:{size:"small"},on:{change:function(e){return t.getStatus()}},model:{value:t.goodsType,callback:function(e){t.goodsType=e},expression:"goodsType"}},[i("el-radio-button",{attrs:{label:"物料"}}),i("el-radio-button",{attrs:{label:"半成品"}}),i("el-radio-button",{attrs:{label:"成品"}})],1)],1)],1),i("div",{staticStyle:{"margin-bottom":"10px"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectgoods",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",height:"380px",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"货品规格",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"货品状态",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsstate))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"外部编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.partid))])]}}])}),i("el-table-column",{attrs:{label:"价格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.outprice))])]}}])}),i("el-table-column",{attrs:{label:"当前库存",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.ivquantity))])]}}])})],1)],1),i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],s=(i("e9c4"),i("b775")),n=i("333d"),r={components:{Pagination:n["a"]},props:["multi","groupid","goodsstate"],data:function(){return{title:"货品信息",listLoading:!1,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},goodsType:"成品",goodsVal:"p"}},created:function(){this.searchstr="",this.multi&&this.bindData()},methods:{getCurrentRow:function(t){console.log(t),this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.groupid){var e={groupid:this.groupid};this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e}if(this.goodsstate){e={goodsstate:this.goodsstate};this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e}s["a"].post("/D91M01B1/getOnlinePageList?state="+this.goodsVal,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={goodsuid:t,goodsname:t,goodsunit:t,groupid:t,goodsspec:t,partid:t,goodsstate:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},getStatus:function(){this.strfilter="",this.$delete(this.queryParams,"SearchPojo"),"成品"==this.goodsType?this.goodsVal="p":"半成品"==this.goodsType?this.goodsVal="s":"物料"==this.goodsType&&(this.goodsVal="m"),this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},l=r,d=(i("ee58"),i("2877")),c=Object(d["a"])(l,a,o,!1,null,"b16f8594",null);e["a"]=c.exports},a6a8:function(t,e,i){},af2b:function(t,e,i){"use strict";i("7de4")},b201:function(t,e,i){"use strict";i("4893")},bfb0:function(t,e,i){"use strict";i("0b6c")},c611:function(t,e,i){},ee58:function(t,e,i){"use strict";i("8885")}}]);