(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e6efd80a"],{"08dc":function(e,t,i){"use strict";i("fac2")},"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"6f4b":function(e,t,i){},7231:function(e,t,i){},8126:function(e,t,i){},"841c":function(e,t,i){"use strict";var a=i("d784"),r=i("825a"),s=i("1d80"),n=i("129f"),o=i("14c3");a("search",1,(function(e,t,i){return[function(t){var i=s(this),a=void 0==t?void 0:t[e];return void 0!==a?a.call(t,i):new RegExp(t)[e](String(i))},function(e){var a=i(t,e,this);if(a.done)return a.value;var s=r(e),l=String(this),c=s.lastIndex;n(c,0)||(s.lastIndex=0);var d=o(s,l);return n(s.lastIndex,c)||(s.lastIndex=c),null===d?-1:d.index}]}))},"900b":function(e,t,i){"use strict";i("7231")},a69e:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[e.formvisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("FormEdit",e._g({ref:"formedit",attrs:{idx:e.idx}},{compForm:e.compForm,closeForm:e.closeForm,changeIdx:e.changeIdx,bindData:e.bindData}))],1):e._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!e.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[i("ListHeader",{attrs:{tableForm:e.tableForm,treeVisble:e.treeVisble},on:{btnAdd:function(t){return e.showForm(0)},btnSearch:e.search,bindData:e.bindData,advancedSearch:e.advancedSearch,btnLabelPrint:function(t){return e.$refs.tableList.btnPrint()},btnHelp:e.btnHelp,changeModelUrl:e.changeModelUrl,btnExport:e.btnExport,showTree:function(t){e.treeVisble=!e.treeVisble},pagePrint:e.pagePrint,btnPrint:function(t){return e.$refs.tableTh.btnPrint()},bindColumn:function(t){e.thorList?e.$refs.tableTh.getColumn():e.$refs.tableList.getColumn()}}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{directives:[{name:"show",rawName:"v-show",value:e.treeVisble,expression:"treeVisble"}],attrs:{span:4}},[i("div",{staticClass:"progress",style:{height:e.tableMaxHeight}},[i("div",{staticClass:"progress-header"},[i("span",[e._v("类型")]),i("i",{staticClass:"el-icon-s-tools setIcon",attrs:{title:"出入库性质设置"},on:{click:function(t){return e.openInOutStore()}}})]),i("div",{staticClass:"progress-content"},[i("el-radio-group",{attrs:{size:"mini"},on:{change:e.changeCheck},model:{value:e.checkboxGroup,callback:function(t){e.checkboxGroup=t},expression:"checkboxGroup"}},[i("el-radio-button",{attrs:{label:"全选"}}),i("el-radio-button",{attrs:{label:"反选"}}),i("el-radio-button",{attrs:{label:"出库"}}),i("el-radio-button",{attrs:{label:"入库"}})],1),i("el-table",{staticStyle:{width:"100%","margin-top":"15px"},attrs:{data:e.billTypeData,"header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px"},"cell-style":{padding:"4px 0px"},border:"","highlight-current-row":""}},[i("el-table-column",{attrs:{align:"center",label:"",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.row.ischeck,callback:function(i){e.$set(t.row,"ischeck",i)},expression:"scope.row.ischeck"}})]}}])}),i("el-table-column",{attrs:{prop:"label",label:"类型",align:"center"}})],1)],1)])]),i("el-col",{attrs:{span:e.treeVisble?e.showhelp?16:20:e.showhelp?20:24}},[i("TableTh",{directives:[{name:"show",rawName:"v-show",value:e.thorList,expression:"thorList"}],ref:"tableTh",attrs:{billTypeData:e.billTypeData,isHongchong:e.isHongchong,formtemplate:e.formtemplate},on:{changeIdx:e.changeIdx,showForm:e.showForm,sendTableForm:e.sendTableForm}}),i("TableList",{directives:[{name:"show",rawName:"v-show",value:!e.thorList,expression:"!thorList"}],ref:"tableList",attrs:{billTypeData:e.billTypeData,formtemplate:e.formtemplate},on:{showForm:e.showForm,changeIdx:e.changeIdx,sendTableForm:e.sendTableForm}})],1)],1)],1)],1),e.inoutstorevisible?i("el-dialog",{attrs:{title:"出入库性质",visible:e.inoutstorevisible,width:"580px","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.inoutstorevisible=t}}},[i("TransferTemp",{ref:"TransferTemp",attrs:{titles:["待选","已选"],data:e.storeTypeList,rightVal:e.rightVal},on:{savetranser:e.savetranser}})],1):e._e()],1)},r=[],s=(i("e9c4"),i("b64b"),i("d3b7"),i("ac1f"),i("841c"),i("159b"),function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticClass:"filter-container flex j-s a-c"},[i("div",{staticStyle:{display:"flex"}},[i("div",{staticClass:"showgroup",on:{click:function(t){return e.$emit("showTree")}}},[i("i",{class:[e.treeVisble?"el-icon-s-fold":"el-icon-s-unfold"],staticStyle:{"font-size":"20px",top:"4px"}})]),i("div",{staticClass:"flex infoForm a-c"},[i("span",{staticClass:"infoForm-Title"},[e._v("时间范围")]),i("el-date-picker",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"unlink-panels":"",size:"mini","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnSearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnSearch},slot:"append"},[e._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:e.btnAdd}},[e._v(" 添加 ")]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.thorList,expression:"!thorList"}],staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-printer",plain:"",size:"mini"},on:{click:function(t){return e.$emit("btnLabelPrint")}}},[e._v(" 标签 ")]),i("el-button",{attrs:{size:"mini",icon:"el-icon-printer",title:"打印列表"},on:{click:function(t){return e.$emit("pagePrint")}}},[e._v("打印")]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:e.thorList,expression:"thorList"}],staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:"el-icon-printer",plain:"",size:"mini",title:"打印单据"},on:{click:function(t){return e.$emit("btnPrint")}}},[e._v(" 单据 ")])],1),i("div",{staticClass:"iShowBtn"},[i("div",{staticStyle:{display:"inline-block"}},[i("el-switch",{staticStyle:{"margin-right":"10px"},attrs:{"inactive-color":"#13ce66"},on:{change:e.changeModelUrl},model:{value:e.thorList,callback:function(t){e.thorList=t},expression:"thorList"}}),i("span",{staticStyle:{"margin-right":"10px","font-size":"14px"}},[e._v(e._s(e.thorList?"单据":"明细"))])],1),i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:e.$store.state.advancedSearch.modulecode==e.tableForm.formcode?"primary":"default"},on:{click:function(t){return e.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right"},on:{click:e.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:e.btnExport}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(t){e.setColumsVisible=!0}}})],1)]),e.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:e.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.setColumsVisible=t}}},[i("SetColums",{ref:"setcolums",attrs:{code:e.tableForm.formcode,tableForm:e.tableForm,baseparam:"/SaDgFormat"},on:{bindData:function(t){return e.$emit("bindColumn")},closeDialog:function(t){e.setColumsVisible=!1}}})],1):e._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:e.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.searchVisible=t}}},[i("SearchForm",{ref:"searchForm",attrs:{code:e.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:e.advancedSearch,closedDialog:function(t){e.searchVisible=!1},bindData:e.bindData}})],1)],1)}),n=[],o=i("b893"),l={name:"Listheader",props:["tableForm","treeVisble"],data:function(){return{strfilter:"",formdata:{},dateRange:Object(o["d"])(),pickerOptions:Object(o["h"])(),thorList:!0,setColumsVisible:!1,searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(e){var t={dateRange:this.dateRange,formdata:e};this.$emit("advancedSearch",t),this.searchVisible=!1},openSearchForm:function(){var e=this;this.searchVisible=!0,setTimeout((function(){e.$refs.searchForm.getInit()}),100)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){var e={dateRange:this.dateRange,strfilter:this.strfilter};this.$emit("btnSearch",e)},bindData:function(){this.$emit("bindData")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList),this.$emit("bindColumn")},btnExport:function(){this.$emit("btnExport")}}},c=l,d=(i("e84e"),i("2877")),h=Object(d["a"])(c,s,n,!1,null,"50421688",null),u=h.exports,m=i("13df"),p=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("ve-table",{key:e.keynum,ref:"tableTh",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":e.tableMaxHeight,"scroll-width":e.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:e.customData,"table-data":e.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:e.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":e.virtualScrollOption,"checkbox-option":e.checkboxOption,"footer-data":e.footerData,"fixed-footer":!0,"sort-option":e.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.getList}}),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:"D04M01R1Th"},on:{bindData:e.bindData}})],1)],1),i("PrintServer",{ref:"PrintServer",attrs:{formdata:e.selectList,printcode:"D04M01R1Edit",commonurl:"/D04M01R1/printBatchBill",weburl:"/D04M01R1/printBatchWebBill"}}),i("PrintServer",{ref:"PrintPageServer",attrs:{formdata:e.lst,queryParams:e.queryParams,printcode:"D04M01R1Th",commonurl:"/D04M01R1/printPageTh",weburl:"/D04M01R1/printWebPageTh"}})],1)},f=[],b=(i("caad"),i("a9e3"),i("2532"),i("c7cd"),i("b775")),g=i("f07e"),v=i("5e63"),y={components:{MyPopover:g["a"]},props:["searchVal","isDialog","billTypeData","formtemplate"],data:function(){var e=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:v["e"],customList:[],selectList:[],mypopoverTable:v["c"],mypopoverData:[],mypopoverIndex:-1,customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(t){var i=t.startRowIndex;e.rowScroll=i}},checkboxOption:{selectedRowChange:function(t){t.row,t.isSelected;var i=t.selectedRowKeys;if(e.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)e.selectList.push({id:i[a]})},selectedAllChange:function(t){var i=t.isSelected;t.selectedRowKeys;e.selectList=i?e.lst:[]}},sortOption:{sortChange:function(t){e.changeSort(t)}}}},computed:{tableMaxHeight:function(){var e=window.innerHeight-160;return e<600&&(e=600),e+"px"},tableMinWidth:function(){var e="calc(100vw - 64px)";if(0!=this.tableForm.item.length){e=0;for(var t=0;t<this.tableForm.item.length;t++){var i=this.tableForm.item[t];i.displaymark&&(e+=Number(i.minwidth))}}return e}},methods:{bindData:function(){var e=this;if(this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(o["d"])()[0],EndDate:Object(o["d"])()[1]}),this.isDialog){var t={orguid:this.searchVal};this.queryParams.SearchType=0,this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,t):this.queryParams.SearchPojo=t,this.$delete(this.queryParams,"DateRange")}if(this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),0!=this.billTypeData.length){for(var i="",a=0;a<this.billTypeData.length;a++){var r=this.billTypeData[a];r.ischeck&&(i+=r.value)}var s="/D04M01R1/getPageTh?type="+i}else s="/D04M01R1/getPageTh";b["a"].post(s,JSON.stringify(this.queryParams)).then((function(t){200==t.data.code?(e.lst=t.data.data.list,e.total=t.data.data.total):e.$message.warning(t.data.msg||"获取内容失败")})).catch((function(t){e.$message.warning(t||"请求错误")}))},getColumn:function(){var e=this,t=v["e"];this.formtemplate.th.type&&(t.item=this.formtemplate.th.content),this.$getColumn(this.tableForm.formcode,t).then((function(t){e.customList=t.customList,e.tableForm=Object.assign({},t.colList),e.initTable(e.tableForm),e.$emit("sendTableForm",e.tableForm)}))},initTable:function(e){var t=this,i=(this.$createElement,[]);this.columnHidden=[],e["item"].forEach((function(e,a){var r={field:e.itemcode,key:e.itemcode,title:e.itemname,width:isNaN(e.minwidth)?e.minwidth:Number(e.minwidth),displaymark:e.displaymark,fixed:!!e.fixed&&(1==e.fixed?"left":"right"),ellipsis:!!e.overflow&&{showTitle:!0},align:e.aligntype?e.aligntype:"center",sortBy:!!e.sortable&&"",renderBodyCell:function(i,a){var r=i.row,s=(i.column,i.rowIndex),n="";return"billdate"==e.itemcode||"createdate"==e.itemcode||"modifydate"==e.itemcode?t.$options.filters.dateFormat(r[e.itemcode]):"refno"==e.itemcode?(n=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return t.showForm(r.id)}}},[r[e.itemcode]?r[e.itemcode]:"单据编码"]),n):"itemcount"==e.itemcode?(n=a("el-popover",{attrs:{placement:"left",trigger:"click",title:"单据明细"}},[a("div",{style:"position: relative; min-height: 100px",directives:[{name:"show",value:s+t.rowScroll==t.mypopoverIndex}]},[a(g["a"],{ref:"mypopover",attrs:{tableForm:t.mypopoverTable,lst:t.mypopoverData}})]),a("span",{slot:"reference",class:"textunderline",on:{click:function(){return t.getBillList(r,s+t.rowScroll)}}},[r[e.itemcode]])]),n):"billtype"==e.itemcode?(n=a("span",[r[e.itemcode]]),r[e.itemcode].includes("红冲")&&(n=a("span",{style:"color:#f44336"},[r[e.itemcode]])),n):r[e.itemcode]}};e.displaymark||t.columnHidden.push(e.itemcode),i.push(r)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(e,i){e.row,e.column;var a=e.rowIndex;return a+t.rowScroll+1}}),this.customData=i,this.keynum+=1},pagePrint:function(){this.$refs.PrintPageServer.printButton(1,1)},btnPrint:function(){this.$refs.PrintServer.printButton(2,1)},getList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},search:function(e){this.$search(this,e,1)},advancedSearch:function(e){this.$advancedSearch(this,e,1)},changeSort:function(e){for(var t in e)if(""!=e[t]){var i={prop:t};"desc"==e[t]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(e){this.$emit("showForm",e)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,"出入库单")},getBillList:function(e,t){var i=this;this.mypopoverIndex=t,b["a"].get("/D04M01R1/getBillEntity?key=".concat(e.id)).then((function(e){if(200==e.data.code){i.mypopoverData=e.data.data.item;for(var t=0;t<i.mypopoverData.length;t++)i.mypopoverData[t]}else i.mypopoverData=[]}))}},filters:{filterStr:function(e){var t=e;return"出库单"==t?t="出库":"入库单"==t&&(t="入库"),t}}},x=y,w=(i("08dc"),Object(d["a"])(x,p,f,!1,null,"0f18c3dc",null)),k=w.exports,S=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("ve-table",{key:e.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":e.tableMaxHeight,"scroll-width":e.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:e.customData,"table-data":e.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:e.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":e.virtualScrollOption,"checkbox-option":e.checkboxOption,"footer-data":e.footerData,"fixed-footer":!0,"event-custom-option":e.eventCustomOption,"sort-option":e.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.getList}}),0!=e.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[e._v("计数="+e._s(e.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[e._v("求和="+e._s(e.cellTotal))])]):e._e()],1),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:"D04M01R1List"},on:{bindData:e.bindData}})],1)]),i("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:e.ReportVisible,"close-on-click-modal":!1},on:{"update:visible":function(t){e.ReportVisible=t}}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:e.reportModel,callback:function(t){e.reportModel=t},expression:"reportModel"}},e._l(e.ReportData,(function(e){return i("el-option",{key:e.id,attrs:{label:e.rptname,value:e.id}})})),1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.submitReport}},[e._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(t){e.ReportVisible=!1}}},[e._v("取 消")])],1)],1),i("el-dialog",{staticClass:"pdfDialog",attrs:{title:"标签预览",width:"80%",visible:e.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(t){e.isViewPdf20=t}}},[i("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:e.pdfUrl,frameborder:"0"}})]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:e.lst,queryParams:e.queryParams,printcode:"D04M01R1List",commonurl:"/D04M01R1/printPageList",weburl:"/D04M01R1/printWebPageList"}})],1)},D=[],P=i("c7eb"),$=i("1da1"),T=(i("3ca3"),i("ddb0"),i("2b3d"),i("9861"),{components:{},props:["searchVal","isDialog","billTypeData","formtemplate"],data:function(){var e=this;return{lst:[],total:0,idx:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,selectList:[],tableForm:v["d"],customList:[],keynum:0,customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(t){var i=t.startRowIndex;e.rowScroll=i}},checkboxOption:{selectedRowChange:function(t){t.row,t.isSelected;var i=t.selectedRowKeys;if(e.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)e.selectList.push(i[a])},selectedAllChange:function(t){var i=t.isSelected;t.selectedRowKeys;e.selectList=i?e.lst:[]}},eventCustomOption:{bodyCellEvents:function(t){t.row,t.column,t.rowIndx;return{mouseup:function(t){e.countCellData()}}}},sortOption:{sortChange:function(t){e.changeSort(t)}}}},computed:{tableMaxHeight:function(){var e=window.innerHeight-160;return e<600&&(e=600),e+"px"},tableMinWidth:function(){var e="calc(100vw - 64px)";if(0!=this.tableForm.item.length){e=0;for(var t=0;t<this.tableForm.item.length;t++){var i=this.tableForm.item[t];i.displaymark&&(e+=Number(i.minwidth))}}return e}},watch:{lst:function(e,t){0!=this.lst.length&&this.getSummary()}},methods:{bindData:function(){var e=this;if(this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(o["d"])()[0],EndDate:Object(o["d"])()[1]}),this.isDialog){var t={citeuid:this.searchVal};this.queryParams.SearchType=0,this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,t):this.queryParams.SearchPojo=t,this.$delete(this.queryParams,"DateRange")}if(0!=this.billTypeData.length){for(var i="",a=0;a<this.billTypeData.length;a++){var r=this.billTypeData[a];r.ischeck&&(i+=r.value)}var s="/D04M01R1/getPageList?type="+i}else s="/D04M01R1/getPageList";this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),b["a"].post(s,JSON.stringify(this.queryParams)).then((function(t){if(200==t.data.code){e.lst=t.data.data.list,e.total=t.data.data.total;for(var i=0;i<e.lst.length;i++)for(var a=e.lst[i],r=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<r.length;s++)e.$set(e.lst[i],r[s].key,r[s].value)}else e.$message.warning(t.data.msg||"获取内容失败")})).catch((function(t){e.$message.warning(t||"请求错误")}))},getColumn:function(){var e=this;return Object($["a"])(Object(P["a"])().mark((function t(){var i;return Object(P["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:i=v["d"],e.formtemplate.list.type&&(i.item=e.formtemplate.list.content),e.$getColumn(e.tableForm.formcode,v["d"],1).then((function(t){e.customList=t.customList,e.tableForm=Object.assign({},t.colList),e.initTable(e.tableForm),e.$emit("sendTableForm",e.tableForm)}));case 3:case"end":return t.stop()}}),t)})))()},initTable:function(e){var t=this,i=(this.$createElement,[]);this.columnHidden=[],e["item"].forEach((function(e,a){var r={field:e.itemcode,key:e.itemcode,title:e.itemname,width:isNaN(e.minwidth)?e.minwidth:Number(e.minwidth),displaymark:e.displaymark,fixed:!!e.fixed&&(1==e.fixed?"left":"right"),ellipsis:!!e.overflow&&{showTitle:!0},align:e.aligntype?e.aligntype:"center",sortBy:!!e.sortable&&"",renderBodyCell:function(i,a){var r=i.row;i.column,i.rowIndex;if("billdate"==e.itemcode||"plandate"==e.itemcode)return t.$options.filters.dateFormat(r[e.itemcode]);if("refno"==e.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return t.showForm(r.pid)}}},[r[e.itemcode]?r[e.itemcode]:"单据编码"]);return s}if("goodsuid"==e.itemcode){s=a("GoodsInfo",{attrs:{scopeIndex:r.rownum,scopeVal:r[e.itemcode]}});return s}if("billtype"==e.itemcode){s=a("span",[r[e.itemcode]]);return r[e.itemcode].includes("红冲")&&(s=a("span",{style:"color:#f44336"},[r[e.itemcode]])),s}return r[e.itemcode]}};e.displaymark||t.columnHidden.push(e.itemcode),i.push(r)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(e,i){e.row,e.column;var a=e.rowIndex;return a+t.rowScroll+1}}),this.customData=i,this.keynum+=1},pagePrint:function(){this.$refs.PrintServer.printButton(1)},countCellData:function(){var e=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,t=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,i=["quantity","compcost","finishqty"];this.$countCellData(this,i,e,t)},getSummary:function(){this.$getSummary(this,["refno","quantity"])},getList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},search:function(e){this.$search(this,e,1)},advancedSearch:function(e){this.$advancedSearch(this,e,1)},btnPrint:function(){var e=this;0!=this.selectList.length?b["a"].get("/Sareports/getListByModuleCode?code=D04M01R1Label").then((function(t){e.ReportData=t.data.data,0!=e.ReportData.length&&(e.reportModel=e.ReportData[0].id),e.ReportVisible=!0})):this.$message.warning("标签内容不能为空!")},submitReport:function(){var e=this;""!=this.reportModel?b["a"].post("/D04M01R1/printList?ptid="+this.reportModel+"&qrcodeurl="+window.location.host,JSON.stringify(this.selectList),{responseType:"blob"}).then((function(t){e.selectList=[],e.ReportVisible=!1;var i=[];i.push(t.data);var a=window.URL.createObjectURL(new Blob(i,{type:"application/pdf"}));e.pdfUrl=a,e.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},changeSort:function(e){for(var t in e)if(""!=e[t]){var i={prop:t};"desc"==e[t]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(e){this.$emit("showForm",e)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,"出入库明细")}}}),L=T,F=(i("900b"),Object(d["a"])(L,S,D,!1,null,"0784638d",null)),C=F.exports,R=i("baaf"),O=i("4155"),q={name:"D04M01M1",components:{ListHeader:u,FormEdit:m["default"],TableTh:k,TableList:C,TransferTemp:R["a"]},props:["searchVal","isDialog","isHongchong"],data:function(){return{title:"发货出库",formvisible:!1,listLoading:!1,idx:0,thorList:!0,tableForm:{},showhelp:!1,treeVisble:!0,inoutstorevisible:!1,storeTypeList:[],rightVal:[],storeTypeData:{},billTypeData:[{label:"收货入库",direction:"入库",ischeck:1,value:"a"},{label:"购退出库",direction:"出库",ischeck:1,value:"b"},{label:"发货出库",direction:"出库",ischeck:1,value:"c"},{label:"客退入库",direction:"入库",ischeck:1,value:"d"},{label:"领料出库",direction:"出库",ischeck:1,value:"e"},{label:"退料入库",direction:"入库",ischeck:1,value:"f"},{label:"其他入库",direction:"入库",ischeck:1,value:"i"},{label:"其他出库",direction:"出库",ischeck:1,value:"l"},{label:"报废出库",direction:"出库",ischeck:1,value:"j"},{label:"盘盈入库",direction:"入库",ischeck:1,value:"k"},{label:"盘亏出库",direction:"出库",ischeck:1,value:"m"}],checkboxGroup:"全选",formtemplate:O["a"]}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},created:function(){},mounted:function(){this.bindTemp(),this.bindData()},methods:{bindTemp:function(){var e=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D04M01M1").then((function(t){200==t.data.code?(null!=t.data.data&&(e.formtemplate=t.data.data.frmcontent?JSON.parse(t.data.data.frmcontent):O["a"]),e.$nextTick((function(){e.thorList?e.$refs.tableTh.getColumn():e.$refs.tableList.getColumn()}))):e.$alert(t.data.msg||"获取页面信息失败")})).catch((function(t){e.$message.error("请求错误")}))},bindData:function(){var e=this;this.thorList?this.$nextTick((function(){e.$refs.tableTh.bindData()})):this.$nextTick((function(){e.$refs.tableList.bindData()}))},pagePrint:function(){this.thorList?this.$refs.tableTh.pagePrint():this.$refs.tableList.pagePrint()},sendTableForm:function(e){this.tableForm=e},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},changeCheck:function(e){this.billTypeData.forEach((function(t){"全选"==e?t.ischeck=1:"出库"==e?"出库"==t.direction?t.ischeck=1:t.ischeck=0:"入库"==e?"入库"==t.direction?t.ischeck=1:t.ischeck=0:"反选"==e&&(t.ischeck=!t.ischeck)})),this.$forceUpdate(),this.bindData()},changeModelUrl:function(e){this.thorList=!!e,this.bindData()},btnExport:function(){var e=this;this.thorList?this.$nextTick((function(){e.$refs.tableTh.btnExport()})):this.$nextTick((function(){e.$refs.tableList.btnExport()}))},search:function(e){this.thorList?this.$refs.tableTh.search(e):this.$refs.tableList.search(e)},advancedSearch:function(e){this.thorList?this.$refs.tableTh.advancedSearch(e):this.$refs.tableList.advancedSearch(e)},showForm:function(e){this.idx=e,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(e){this.idx=e},openInOutStore:function(){var e=this;this.storeTypeList.length||this.getStoreTypeList(),this.$nextTick((function(){e.inoutstorevisible=!0}))},getStoreTypeList:function(){this.storeTypeList=[{key:1,label:"收货入库",direction:"入库",ischeck:1,value:"a"},{key:2,label:"购退出库",direction:"出库",ischeck:1,value:"b"},{key:3,label:"发货出库",direction:"出库",ischeck:1,value:"c"},{key:4,label:"客退入库",direction:"入库",ischeck:1,value:"d"},{key:5,label:"领料出库",direction:"出库",ischeck:1,value:"e"},{key:6,label:"退料入库",direction:"入库",ischeck:1,value:"f"},{key:9,label:"其他入库",direction:"入库",ischeck:1,value:"i"},{key:10,label:"其他出库",direction:"出库",ischeck:1,value:"l"},{key:11,label:"报废出库",direction:"出库",ischeck:1,value:"j"},{key:12,label:"盘盈入库",direction:"入库",ischeck:1,value:"k"},{key:13,label:"盘亏出库",direction:"出库",ischeck:1,value:"m"}]},savetranser:function(e){var t=this;console.log("savetranser",e);for(var i=[],a=0;a<e.length;a++){var r=e[a];i.push(r)}var s={typejson:JSON.stringify(i)},n=this.$request.post("/D04M21S3/create",JSON.stringify(s));this.storeTypeData.id&&(s.id=this.storeTypeData.id,n=this.$request.post("/D04M21S3/update",JSON.stringify(s))),n.then((function(e){200==e.data.code&&(t.inoutstorevisible=!1,t.$message.success("出入库性质设置成功"),t.initBillType())}))}}},M=q,N=(i("c306"),Object(d["a"])(M,a,r,!1,null,"98e28860",null));t["default"]=N.exports},c306:function(e,t,i){"use strict";i("6f4b")},e84e:function(e,t,i){"use strict";i("8126")},fac2:function(e,t,i){}}]);