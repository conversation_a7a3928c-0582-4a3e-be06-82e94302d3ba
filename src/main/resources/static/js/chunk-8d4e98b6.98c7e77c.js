(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8d4e98b6"],{"20ab":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):a("div",{ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,btnExport:t.btnExport}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading","summary-method":t.getSummaries,"show-summary":"",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["accountname"==e.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showForm(i.row.id)}}},[t._v(t._s(i.row[e.itemcode]))]):"endinuid"==e.itemcode||"endindate"==e.itemcode||"endoutuid"==e.itemcode||"endoutdate"==e.itemcode||"createdate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormats")(i.row[e.itemcode])))]):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),a("div",{staticStyle:{"margin-right":"40px"}},[a("scene",{ref:"scene",attrs:{code:"D07M21B2List"},on:{bindData:t.bindData}})],1)],1)],1)],1)],1)],1)])},n=[],r=a("ade3"),o=(a("7db0"),a("d81d"),a("13d5"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("159b"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",{staticStyle:{display:"flex"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.code?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}})],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("SetColums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.bindData,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),a("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[a("SearchForm",{ref:"searchForm",attrs:{code:t.code,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),s=[],l={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},setColumsVisible:!1,code:"D07M21B2List",searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(t){this.iShow=!1,this.$emit("advancedSearch",t),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnPrint:function(){this.$emit("btnPrint")},btnExport:function(){this.$emit("btnExport")},modelExport:function(){this.$emit("modelExport")},btnImport:function(){this.$emit("btnImport")},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},c=l,d=(a("23a4"),a("2877")),m=Object(d["a"])(c,o,s,!1,null,"6351e64c",null),u=m.exports,f=a("333d"),h=a("b775"),p=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.formdata.id},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-divider",{attrs:{"content-position":"left"}},[t._v("基础信息")]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("accounttype")}}},[a("el-form-item",{attrs:{label:"账户类型",prop:"accounttype"}},[a("el-input",{attrs:{placeholder:"请输入账户类型",clearable:"",size:"small"},model:{value:t.formdata.accounttype,callback:function(e){t.$set(t.formdata,"accounttype",e)},expression:"formdata.accounttype"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("accountname")}}},[a("el-form-item",{attrs:{label:"账户名称",prop:"accountname"}},[a("el-input",{attrs:{placeholder:"请输入账户名称",clearable:"",size:"small"},model:{value:t.formdata.accountname,callback:function(e){t.$set(t.formdata,"accountname",e)},expression:"formdata.accountname"}})],1)],1)])],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[t._v("详细信息")]),a("el-form",{staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"最小限额"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:"small"},model:{value:t.formdata.minlimit,callback:function(e){t.$set(t.formdata,"minlimit",e)},expression:"formdata.minlimit"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"最大限额"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:"small"},model:{value:t.formdata.maxlimit,callback:function(e){t.$set(t.formdata,"maxlimit",e)},expression:"formdata.maxlimit"}})],1)],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[t._v("其他信息")])],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},b=[],g=a("c7eb"),v=a("1da1");a("99af"),a("25f0"),a("4d90");const w={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/D07M21B2/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/D07M21B2/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{h["a"].get("/D07M21B2/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var y=w,x=(a("6ca8"),{name:"Formedit",components:{},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),r=e.getHours().toString().padStart(2,"0"),o=e.getMinutes().toString().padStart(2,"0"),s=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n," ").concat(r,":").concat(o,":").concat(s)}}},props:["idx"],data:function(){return Object(r["a"])(Object(r["a"])(Object(r["a"])(Object(r["a"])({title:"出纳账户",formdata:{maxlimit:0,minlimit:0,moneyid:"RMB",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{accountname:[{required:!0,trigger:"blur",message:"账户名称为必填项"}],accounttype:[{required:!0,trigger:"blur",message:"账户类型为必填项"}],moneyid:[{required:!0,trigger:"blur",message:"币种为必填项"}],currentamt:[{required:!0,trigger:"blur",message:"当前金额为必填项"}]},multi:0,selVisible:!1,formLabelWidth:"100px",formheight:"500px",finshDialogVisible:!1},"formLabelWidth","100px"),"enActive",!1),"visable",!1),"disabled",!1)},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;return Object(v["a"])(Object(g["a"])().mark((function e(){return Object(g["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.listLoading=!0,0==t.idx){e.next=4;break}return e.next=4,h["a"].get("/D07M21B2/getEntity?key=".concat(t.idx)).then((function(e){console.log("===========",e),200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")}));case 4:case"end":return e.stop()}}),e)})))()},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;0==this.idx?y.add(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.bindData())})).catch((function(e){t.$message.warning("保存失败")})):y.update(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData())})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),y.delete(t).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}}),S=x,k=(a("fd10"),Object(d["a"])(S,p,b,!1,null,"782c2747",null)),_=k.exports,F=a("da92"),$=a("48da"),D={formcode:"D07M21B2List",item:[{itemcode:"accountname",itemname:"账户名称",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"accounttype",itemname:"账户类型",minwidth:"80",displaymark:1,overflow:1},{itemcode:"minlimit",itemname:"最小限额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"maxlimit",itemname:"最大限额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"currentamt",itemname:"当前金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1},{itemcode:"endinuid",itemname:"末次入账",minwidth:"80",displaymark:1,overflow:1},{itemcode:"endindate",itemname:"入账时间",minwidth:"80",displaymark:1,overflow:1},{itemcode:"endoutuid",itemname:"末次出账",minwidth:"80",displaymark:1,overflow:1},{itemcode:"endoutdate",itemname:"出账时间",minwidth:"80",displaymark:1,overflow:1},{itemcode:"createdate",itemname:"创建时间",minwidth:"80",displaymark:1,overflow:1}]},P=a("b893"),C=a("4363"),N=a("0521"),O=Object(r["a"])(Object(r["a"])({name:"",components:{Pagination:f["a"],listheader:u,formedit:_,helpmodel:N["a"],scene:C["a"]},data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},showhelp:!1,tableForm:D}},computed:{tableMaxHeight:function(){return window.innerHeight-160}},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},watch:{},created:function(){},mounted:function(){this.bindData(),this.getColumn()}},"updated",(function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))})),"methods",Object(r["a"])({bindData:function(){var t=this;this.listLoading=!0,this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),h["a"].post("/D07M21B2/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;h["a"].get("/SaDgFormat/getBillEntityByCode?code=D07M21B2List").then((function(e){if(200==e.data.code){if(null==e.data.data)return void(t.tableForm=D);t.tableForm=e.data.data}})).catch((function(e){t.$message.error("请求出错")}))},getSummaries:function(t){return Object(P["f"])(t,["currentamt"])},btnExport:function(){var t=this;Promise.resolve().then(function(){for(var e=[],a=[],i=0;i<t.tableForm.item.length;i++){var n=t.tableForm.item[i];n.displaymark&&n.displaymark&&(e.push(n.itemname),a.push(n.itemcode))}var r=t.lst,o=t.formatJson(a,r);Object($["a"])(e,o,"出纳账户")}.bind(null,a)).catch(a.oe)},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={accounttype:t,accountname:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.queryParams.scenedata=t,""==t[0].field?this.queryParams.SearchType=1:this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showForm:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeIdx:function(t){this.idx=t}},"getSummaries",(function(t){var e=t.columns,a=t.data,i=["currentamt"],n=[];return e.forEach((function(t,e){if(1!==e){var r=!1;i.length>0&&void 0!=i.find((function(e){return e==t.property}))&&(r=!0);var o=a.map((function(e){return Number(e[t.property])}));!o.every((function(t){return isNaN(t)}))&&r?n[e]=o.reduce((function(t,e){var a=Number(e);return isNaN(a)?Number(t):F["a"].plus(Number(t),Number(e))}),0):n[e]=""}else n[e]="合计"})),n}))),L=O,V=(a("b8c1"),Object(d["a"])(L,i,n,!1,null,"2214abc0",null));e["default"]=V.exports},"23a4":function(t,e,a){"use strict";a("f4b1")},"33b6":function(t,e,a){},"5cc6":function(t,e,a){var i=a("74e8");i("Uint8",(function(t){return function(e,a,i){return t(this,e,a,i)}}))},"9dcd":function(t,e,a){},b8c1:function(t,e,a){"use strict";a("9dcd")},bf19:function(t,e,a){"use strict";var i=a("23e7");i({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return URL.prototype.toString.call(this)}})},c19f:function(t,e,a){"use strict";var i=a("23e7"),n=a("da84"),r=a("621a"),o=a("2626"),s="ArrayBuffer",l=r[s],c=n[s];i({global:!0,forced:c!==l},{ArrayBuffer:l}),o(s)},f4b1:function(t,e,a){},fd10:function(t,e,a){"use strict";a("33b6")}}]);