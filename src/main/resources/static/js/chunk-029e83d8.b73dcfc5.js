(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-029e83d8"],{"38eca":function(e,t,a){"use strict";a("5513")},5513:function(e,t,a){},5658:function(e,t,a){"use strict";var r=a("b775");const l={add(e){return new Promise((t,a)=>{var l=JSON.stringify(e);r["a"].post("/SaPermCode/create",l).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var l=JSON.stringify(e);r["a"].post("/SaPermCode/update",l).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){return new Promise((t,a)=>{r["a"].get("/SaPermCode/delete?key="+e).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})}};t["a"]=l},"797f":function(e,t,a){},d44f:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(t){return e.submitForm("formdata")}}},[e._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.formclose(t)}}},[e._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:e.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":e.formLabelWidth,"auto-complete":"on",rules:e.formRules}},["create"!=e.getType?a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"上级权限",prop:"parentid"}},[a("el-popover",{attrs:{placement:"bottom-start",trigger:"click"},model:{value:e.selVisible,callback:function(t){e.selVisible=t},expression:"selVisible"}},[a("selPower",{ref:"selPower",staticStyle:{width:"700px",height:"500px"},attrs:{multi:0},on:{singleSel:e.selPower}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择上级权限",clearable:"",size:"small",readonly:""},on:{change:e.handleBlur},model:{value:e.formdata.parentname,callback:function(t){e.$set(e.formdata,"parentname",t)},expression:"formdata.parentname"}},[a("i",{staticClass:"el-icon-circle-plus-outline",staticStyle:{"font-size":"15px",color:"rgb(64, 158, 255)"},attrs:{slot:"suffix"},slot:"suffix"})])],1)],1)],1)],1)],1):e._e(),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"是否公开"}},[a("el-radio",{attrs:{label:0},model:{value:e.formdata.ispublic,callback:function(t){e.$set(e.formdata,"ispublic",t)},expression:"formdata.ispublic"}},[e._v("正常")]),a("el-radio",{attrs:{label:1},model:{value:e.formdata.ispublic,callback:function(t){e.$set(e.formdata,"ispublic",t)},expression:"formdata.ispublic"}},[e._v("停用")])],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"状态"}},[a("el-radio",{attrs:{label:1},model:{value:e.formdata.enabledmark,callback:function(t){e.$set(e.formdata,"enabledmark",t)},expression:"formdata.enabledmark"}},[e._v("正常")]),a("el-radio",{attrs:{label:0},model:{value:e.formdata.enabledmark,callback:function(t){e.$set(e.formdata,"enabledmark",t)},expression:"formdata.enabledmark"}},[e._v("停用")])],1)],1)],1),a("el-divider"),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("permcode")}}},[a("el-form-item",{attrs:{label:"权限编码",prop:"permcode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入权限编码",clearable:"",size:"small"},model:{value:e.formdata.permcode,callback:function(t){e.$set(e.formdata,"permcode",t)},expression:"formdata.permcode"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("permname")}}},[a("el-form-item",{attrs:{label:"权限名称",prop:"permname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入权限名称",clearable:"",size:"small"},model:{value:e.formdata.permname,callback:function(t){e.$set(e.formdata,"permname",t)},expression:"formdata.permname"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"全选"}},[a("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("All")])],1)],1)],1),a("el-checkbox-group",{on:{change:e.handleCheckedPowerChange},model:{value:e.checkedPower,callback:function(t){e.checkedPower=t},expression:"checkedPower"}},[a("el-row",[a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"列表"}},[a("el-checkbox",{attrs:{label:"List",size:"mini"}})],1)],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"审核"}},[a("el-checkbox",{attrs:{label:"Approval",size:"mini"}})],1)],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"打印"}},[a("el-checkbox",{attrs:{label:"Print",size:"mini"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"添加"}},[a("el-checkbox",{attrs:{label:"Add",size:"mini"}})],1)],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"编辑"}},[a("el-checkbox",{attrs:{label:"Edit",size:"mini"}})],1)],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"删除"}},[a("el-checkbox",{attrs:{label:"Delete",size:"mini"}})],1)],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"注销"}},[a("el-checkbox",{attrs:{label:"Disannul",size:"mini"}})],1)],1)],1)],1)],1),a("el-divider")],1),a("el-form",{attrs:{"label-width":e.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.modifydate)))])])],1)],1)],1)],1)])])},l=[],o=a("c7eb"),i=a("1da1"),n=(a("99af"),a("e9c4"),a("b64b"),a("d3b7"),a("25f0"),a("3ca3"),a("4d90"),a("ddb0"),a("b775")),s=a("5658"),c=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"small"},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(t){return e.search(e.strfilter)}}},[e._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:e.lst,"element-loading-text":"Loading",border:"",fit:"",height:"400px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-key":"permid","default-expand-all":!1,"tree-props":{children:"children",hasChildren:"hasChildren"}},on:{select:e.rowSelect,"select-all":e.selectAll}},[1==e.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"30",type:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-radio",{attrs:{label:t.$index},nativeOn:{change:function(a){return e.getCurrentRow(t.row)}},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[e._v(e._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"序号",align:"center",width:"60px"}}),a("el-table-column",{attrs:{label:"权限编码",align:"center","min-width":"100px","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.permcode))])]}}])}),a("el-table-column",{attrs:{label:"权限名称",align:"center","min-width":"100px","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.permname))])]}}])}),a("el-table-column",{attrs:{label:"状态",align:"center","min-width":"50px","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.enabledmark?a("el-tag",[e._v("正常")]):a("el-tag",{attrs:{type:"warning"}},[e._v("停用")])]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createdate","min-width":"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("dateFormat")(t.row.createdate)))])]}}])})],1)],1)])},d=[],m=(a("159b"),a("333d")),f={components:{Pagination:m["a"]},props:["multi"],data:function(){return{title:"权限信息",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:1e4,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.BindData()},methods:{getCurrentRow:function(e){this.$forceUpdate(),this.selrows=e,this.$emit("singleSel",e)},GetList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.BindData()},BindData:function(){var e=this;this.listLoading=!0,n["a"].post("/SaPermCode/getPageList",JSON.stringify(this.queryParams)).then((function(t){200==t.data.code&&(e.lst=e.changeFormat(t.data.data.list)),e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},rowSelect:function(e,t){var a=[];a.push(t),this.forEachExample(a,!t.isChecked)},selectAll:function(e){0!=e.length?this.forEachExample(this.$refs.selectVal.data,!this.$refs.selectVal.data[0].isChecked):this.$message.warning("暂无数据")},forEachExample:function(e,t){var a=this,r=this;this.$nextTick((function(){e.forEach((function(e,l){t?(e.isChecked=!0,r.$refs.selectVal.toggleRowSelection(e,!0),e.children&&0!=e.children.length&&a.forEachExample(e.children,!0)):(e.isChecked=!1,r.$refs.selectVal.toggleRowSelection(e,!1),e.children&&0!=e.children.length&&a.forEachExample(e.children,!1))}))}))},selsChange:function(e,t){console.log(e)},search:function(e){""!=e?this.queryParams.SearchPojo={navname:e,mvcurl:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.BindData()},changeFormat:function(e){var t=[];if(!Array.isArray(e))return t;e.forEach((function(e){delete e.children}));var a={};return e.forEach((function(e){a[e.permid]=e})),e.forEach((function(e){var r=a[e.parentid];r?(r.children||(r.children=[])).push(e):t.push(e)})),t}},filters:{dateFormat:function(e){if(e){var t=new Date(e),a=t.getFullYear(),r=(t.getMonth()+1).toString().padStart(2,"0"),l=t.getDate().toString().padStart(2,"0"),o=t.getHours().toString().padStart(2,"0"),i=t.getMinutes().toString().padStart(2,"0"),n=t.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(r,"-").concat(l," ").concat(o,":").concat(i,":").concat(n)}}}},p=f,u=(a("38eca"),a("2877")),h=Object(u["a"])(p,c,d,!1,null,"a1a975a4",null),g=h.exports,b={name:"Formedit",components:{selPower:g},props:["idx","title","getType"],data:function(){return{formdata:{permcode:"",permname:"",ispublic:0,enabledmark:1,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{permcode:[{required:!0,trigger:"blur",message:"权限编码为必填项"}],permname:[{required:!0,trigger:"blur",message:"权限名称为必填项"}]},formLabelWidth:"100px",formheight:"500px",powerData:[],checkAll:!1,isIndeterminate:!1,checkedPower:[],Powers:["List","Add","Edit","Delete","Approval","Print","Report","Disannul"],queryParams:{PageNum:1,PageSize:1e3,OrderType:1,SearchType:0,SearchPojo:{parentid:"root"}},multi:0,selVisible:!1,permData:[],defaultProps:{children:"children",label:"permname",value:"permid"}}},computed:{formcontainHeight:function(){return window.innerHeight-50-33-20-20-50+"px"}},watch:{idx:function(e,t){console.log("new: %s, old: %s",e,t),this.BindData()}},created:function(){this.BindData()},methods:{BindData:function(){var e=this;console.log("绑定数据"),n["a"].post("/SaPermCode/getPageList",JSON.stringify(this.queryParams)).then((function(t){console.log(t),200==t.data.code&&(e.powerData=t.data.data.list),e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return console.log("error submit!!"),!1;console.log("可保存"),t.formsave()}))},formsave:function(){var e=this;return Object(i["a"])(Object(o["a"])().mark((function t(){var a,r,l,i,n,c;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:for(a=e,r=[],l=e.formdata.permname,i=e.formdata.permcode,n=0;n<e.checkedPower.length;n++)c=new Promise((function(t,a){switch(e.formdata.permcode=i+"."+e.checkedPower[n],e.checkedPower[n]){case"List":e.formdata.permname=l+".列表";break;case"Add":e.formdata.permname=l+".添加";break;case"Edit":e.formdata.permname=l+".编辑";break;case"Delete":e.formdata.permname=l+".删除";break;case"Disannul":e.formdata.permname=l+".注销";break;case"Approval":e.formdata.permname=l+".审核";break;case"Print":e.formdata.permname=l+".打印";break;default:break}s["a"].add(e.formdata).then((function(e){console.log(e),200==e.code?t("保存成功"):a("保存失败")})).catch((function(e){a("保存失败")}))})),r.push(c);return t.next=7,Promise.all(r).then((function(t){console.log(e.formdata),a.$message.success("保存成功"),e.formclose()})).catch((function(e){console.log(e),a.$message.warning("保存失败")}));case 7:case"end":return t.stop()}}),t)})))()},formclose:function(){this.$store.dispatch("tagsView/delView",this.$route),this.$router.go(-1),console.log("关闭窗口")},check:function(){console.log("check")},selPower:function(e){var t=this.$refs.selPower.selrows;console.log(t),this.formdata.parentid=t.permid,this.formdata.parentname=t.permname,this.selVisible=!1,this.$refs.formdata.clearValidate("parentid")},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},cleValidate:function(e){this.$refs.formdata.clearValidate(e)},handleCheckAllChange:function(e){console.log(e),this.checkedPower=e?this.Powers:[],console.log(this.checkedPower),this.isIndeterminate=!1},handleCheckedPowerChange:function(e){console.log(e);var t=e.length;this.checkAll=9===t,this.isIndeterminate=t>0&&t<this.Powers.length}},filters:{dateFormat:function(e){if(e){var t=new Date(e),a=t.getFullYear(),r=(t.getMonth()+1).toString().padStart(2,"0"),l=t.getDate().toString().padStart(2,"0"),o=t.getHours().toString().padStart(2,"0"),i=t.getMinutes().toString().padStart(2,"0"),n=t.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(r,"-").concat(l," ").concat(o,":").concat(i,":").concat(n)}}}},w=b,v=(a("d657"),Object(u["a"])(w,r,l,!1,null,"0af9363f",null));t["default"]=v.exports},d657:function(e,t,a){"use strict";a("797f")}}]);