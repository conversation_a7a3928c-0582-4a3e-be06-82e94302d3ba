(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-73bac150"],{"0e0ff":function(t,e,i){},"11f6":function(t,e,i){},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},2499:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px",width:"100%"}},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1)]),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"450px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"客户订单号",align:"center",width:"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.custorderid))])]}}])}),i("el-table-column",{attrs:{label:"客户",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),t._l(t.customList,(function(t,e){return[i("el-table-column",{key:e,attrs:{"header-align":"center",align:"center",prop:t.attrkey,label:t.attrname}})]}))],2)],1),i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],n=i("c7eb"),r=i("1da1"),s=(i("c740"),i("e9c4"),i("b64b"),i("b775")),d=i("8daf"),l=i("40d9"),m={props:["multi","groupid","selecturl"],components:{Setcolums:d["a"]},data:function(){return{listLoading:!0,lst:[],strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},customList:[],setColumsVisible:!1,tableForm:l["d"]}},created:function(){this.bindData(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(r["a"])(Object(n["a"])().mark((function e(){return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,s["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code?t.customList=e.data.data?e.data.data:[]:t.$message.warning(e.data.msg||"获取失败")})).catch((function(e){t.$message.error("请求错误")}));case 2:return e.next=4,s["a"].get("/SaDgFormat/getBillEntityByCode?code=D01M03B1Select").then((function(e){if(200==e.data.code){if(null==e.data.data){t.tableForm=l["d"];for(var i=0;i<t.customList.length;i++){var a=t.customList[i],o=t.tableForm.item.findIndex((function(e){return e.itemcode==t.customList[i].attrkey}));if(-1==o){var n={itemcode:a.attrkey,itemname:a.attrname,minwidth:"80",displaymark:a.listshow,overflow:1};t.tableForm.item.push(n)}}return}t.tableForm=e.data.data;for(i=0;i<t.customList.length;i++){a=t.customList[i],o=t.tableForm.item.findIndex((function(e){return e.itemcode==t.customList[i].attrkey}));if(-1==o){n={itemcode:a.attrkey,itemname:a.attrname,minwidth:"80",displaymark:a.listshow,overflow:1};t.tableForm.item.push(n)}}}})).catch((function(e){t.$message.error("请求出错")}));case 4:case"end":return e.stop()}}),e)})))()},getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;return Object(r["a"])(Object(n["a"])().mark((function e(){var i;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.listLoading=!0,t.listLoading=!0,i=t.selecturl?t.selecturl:"/D01M03B1/getPageList",e.next=5,s["a"].post(i,JSON.stringify(t.queryParams)).then((function(e){if(200==e.data.code){console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],n=0;n<o.length;n++)t.$set(t.lst[i],o[n].key,o[n].value)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 5:return e.next=7,s["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code||t.$message.warning(e.data.msg||"获取spu失败")})).catch((function(e){t.$message.error("请求错误")}));case 7:case"end":return e.stop()}}),e)})))()},search:function(t){var e=t.split(",");0!=e.length?1==e.length?(this.queryParams.SearchPojo={goodsuid:e[0],goodsname:e[0],goodsunit:e[0],groupid:e[0],goodsspec:e[0],partid:e[0],refno:e[0],custorderid:e[0],groupname:e[0],attributestr:e[0]},this.$delete(this.queryParams,"scenedata")):e.length>1&&(this.queryParams.scenedata=[{field:"Mat_Goods.goodsuid",fieldtype:0,math:"like",value:"".concat(e[0])},{field:"Mat_Goods.goodsname",fieldtype:0,math:"like",value:"".concat(e[1])}],3==e.length&&this.queryParams.scenedata.push({field:"Mat_Goods.partid",fieldtype:0,math:"like",value:"".concat(e[2])}),this.$delete(this.queryParams,"SearchPojo")):(this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"scenedata")),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},c=m,u=(i("fb056"),i("2877")),h=Object(u["a"])(c,a,o,!1,null,"56660287",null);e["a"]=h.exports},"27f6":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},o=[],n=i("c7eb"),r=i("1da1"),s=(i("caad"),i("e9c4"),i("a9e3"),i("b64b"),i("d3b7"),i("2532"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),i("b775")),d=i("5e63"),l=i("b893"),m={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"13df"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:d["b"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={citeuid:this.searchVal};this.queryParams.SearchPojo=e;var i="/D04M01R1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(l["d"])()[0],EndDate:Object(l["d"])()[1]}),s["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],n=0;n<o.length;n++)t.$set(t.lst[i],o[n].key,o[n].value)}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(r["a"])(Object(n["a"])().mark((function e(){return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(d["b"].formcode,d["b"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var n=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return n}if("goodsuid"==t.itemcode){n=a("GoodsInfo",{attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return n}if("billtype"==t.itemcode){n=a("span",[o[t.itemcode]]);return o[t.itemcode].includes("红冲")&&(n=a("span",{style:"color:#f44336"},[o[t.itemcode]])),n}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},c=m,u=(i("b83a"),i("2877")),h=Object(u["a"])(c,a,o,!1,null,"34f0715a",null);e["a"]=h.exports},"40d9":function(t,e,i){"use strict";i.d(e,"f",(function(){return a})),i.d(e,"e",(function(){return o})),i.d(e,"c",(function(){return n})),i.d(e,"b",(function(){return r})),i.d(e,"a",(function(){return s})),i.d(e,"d",(function(){return d}));var a={formcode:"D01M03B1Th",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"150",defwidth:"",displaymark:1,fixed:1,sortable:1,overflow:1,aligntype:"center",datasheet:"Bus_Machining.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billdate"},{itemcode:"custorderid",itemname:"客户订单号",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.custorderid"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"abbreviate",itemname:"简称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.abbreviate"},{itemcode:"groupadd",itemname:"客户地址",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupadd"},{itemcode:"billtaxamount",itemname:"总金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtaxamount"},{itemcode:"advaamount",itemname:"预收款",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.advaamount"},{itemcode:"balance",itemname:"结余",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billcostbudgetamt",itemname:"成本预算",minwidth:"80",displaymark:0,overflow:1},{itemcode:"billplandate",itemname:"计划时间",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billplandate"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.summary"},{itemcode:"salesman",itemname:"业务员",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.salesman"},{itemcode:"billwkwpname",itemname:"最新工序",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.billwkwpname"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Machining.status"},{itemcode:"itemcount",itemname:"款数",minwidth:"60",displaymark:1,overflow:1,datasheet:"Bus_Machining.itemcount"},{itemcode:"printcount",itemname:"打印次数",minwidth:"100",displaymark:0,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Machining.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Bus_Machining.assessor"},{itemcode:"amtstatus",itemname:"收款状态",minwidth:"100",displaymark:1,overflow:1}]},o={formcode:"D01M03B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"150",defwidth:"",displaymark:1,overflow:1,fixed:1,sortable:1,aligntype:"center",datasheet:"Bus_Machining.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billdate"},{itemcode:"custorderid",itemname:"客户订单号",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Machining.custorderid"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"matcode",itemname:"物料编码",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.matcode"},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.quantity"},{itemcode:"taxprice",itemname:"单价",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxprice"},{itemcode:"taxamount",itemname:"金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxamount"},{itemcode:"finishqty",itemname:"完成数",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.finishqty"},{itemcode:"remainder",itemname:"结余数",minwidth:"100",displaymark:0,overflow:1},{itemcode:"buyquantity",itemname:"采购数",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.buyquantity"},{itemcode:"wkwpname",itemname:"工序",minwidth:"100",displaymark:1,overflow:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"matstatus",itemname:"物料状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemplandate",itemname:"评审交期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.itemplandate"},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.remark"}]},n={formcode:"D01M03B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"120",defwidth:"",displaymark:1,fixed:1,sortable:0,overflow:1,aligntype:"center",operationmark:1},{itemcode:"goodsname",itemname:"货品名称",minwidth:"120",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"200",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"120",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"partid",itemname:"外部编码",minwidth:"120",displaymark:1,overflow:1,operationmark:1},{itemcode:"quantity",itemname:"数量",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"itemorgdate",itemname:"原始交期",minwidth:"120",displaymark:1,overflow:1},{itemcode:"itemplandate",itemname:"评审交期",minwidth:"120",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"stoqty",itemname:"库存发货",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"maxqty",itemname:"最大发货数",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"wkqty",itemname:"生产需求",minwidth:"120",displaymark:1,overflow:1},{itemcode:"wkquantity",itemname:"生产数",minwidth:"120",displaymark:1,overflow:1},{itemcode:"buyquantity",itemname:"采购数",minwidth:"100",displaymark:1,overflow:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:0,overflow:1},{itemcode:"finishqty",itemname:"发货数",minwidth:"120",displaymark:1,overflow:1},{itemcode:"outquantity",itemname:"出库数",minwidth:"120",displaymark:1,overflow:1}]},r={formcode:"D01M03B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"150",defwidth:"",displaymark:1,overflow:1,fixed:1,sortable:1,aligntype:"center",datasheet:"Bus_Machining.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billdate"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"matcode",itemname:"物料编码",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.matcode"},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.quantity"},{itemcode:"taxprice",itemname:"单价",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxprice"},{itemcode:"taxamount",itemname:"金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.taxamount"},{itemcode:"wkwpname",itemname:"工序",minwidth:"100",displaymark:1,overflow:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"matstatus",itemname:"物料状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemplandate",itemname:"评审交期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_MachiningItem.itemplandate"},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_MachiningItem.remark"}]},s={formcode:"D03M02B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"130",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",atasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,atasheet:"Buy_Order.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Buy_Order.billtitle"},{itemcode:"billdate",itemname:"单据日期",sortable:1,minwidth:"100",displaymark:1,overflow:1,atasheet:"Buy_Order.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"plandate",itemname:"计划完成",minwidth:"100",sortable:1,displaymark:1,overflow:1,atasheet:"Buy_OrderItem.billdate"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"规格",minwidth:"120",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.quantity"},{itemcode:"finishqty",itemname:"已收",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.status"}]},d={formcode:"D01M03B1Select",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"130",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",atasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Machining.billtype"},{itemcode:"custorderid",itemname:"客户订单号",minwidth:"100",displaymark:1,overflow:1},{itemcode:"groupname",itemname:"客户",minwidth:"120",displaymark:1,fixed:1,overflow:1,operationmark:1},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,overflow:1,operationmark:1},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"120",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1}]}},"4a10":function(t,e,i){"use strict";i("53de")},"53de":function(t,e,i){},5413:function(t,e,i){},"5d993":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[i("ListHeader",{attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,changeModelUrl:t.changeModelUrl,btnExport:t.btnExport,btnHelp:t.btnHelp,changeBalance:t.changeBalance,pagePrint:t.pagePrint,btnPrint:function(e){return t.$refs.tableTh.btnPrint()},setDelivery:function(e){return t.$refs.tableList.setDelivery()},bindColumn:function(e){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()}}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{attrs:{span:t.showhelp?20:24}},[i("TableTh",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],ref:"tableTh",attrs:{online:t.online,formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}}),i("TableList",{directives:[{name:"show",rawName:"v-show",value:!t.thorList,expression:"!thorList"}],ref:"tableList",attrs:{online:t.online,formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}})],1),i("el-col",{attrs:{span:t.showhelp?4:0}},[i("HelpModel",{ref:"helpmodel",attrs:{code:"D04M08B1"}})],1)],1)],1)],1)])},o=[],n=(i("b64b"),i("ac1f"),i("841c"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container flex j-s a-c"},[i("div",{staticStyle:{display:"flex"}},[i("div",{staticClass:"flex infoForm a-c"},[i("span",{staticClass:"infoForm-Title"},[t._v("时间范围")]),i("el-date-picker",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"unlink-panels":"",size:"mini","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")]),i("el-button",{attrs:{size:"mini",icon:"el-icon-printer",title:"打印列表"},on:{click:function(e){return t.$emit("pagePrint")}}},[t._v("打印")]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:"el-icon-printer",plain:"",size:"mini",title:"打印单据"},on:{click:function(e){return t.$emit("btnPrint")}}},[t._v(" 单据 ")]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:!t.thorList,expression:"!thorList"}],staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-coin",plain:"",size:"mini"},on:{click:function(e){return t.$emit("setDelivery")}}},[t._v(" 出库 ")])],1),i("div",{staticClass:"iShowBtn"},[i("div",{staticStyle:{display:"inline-block"}},[i("el-switch",{staticStyle:{"margin-right":"10px"},attrs:{"inactive-color":"#13ce66"},on:{change:t.changeModelUrl},model:{value:t.thorList,callback:function(e){t.thorList=e},expression:"thorList"}}),i("span",{staticStyle:{"margin-right":"10px","font-size":"14px"}},[t._v(t._s(t.thorList?"单据":"明细"))])],1),i("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"结存",size:"mini",border:""},on:{change:t.changeBalance},model:{value:t.balance,callback:function(e){t.balance=e},expression:"balance"}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}}),i("el-button",{attrs:{size:"mini",title:"帮助",icon:"el-icon-s-help"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),r=[],s=i("b893"),d={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",formdata:{},dateRange:Object(s["d"])(),pickerOptions:Object(s["h"])(),thorList:!0,balance:!1,setColumsVisible:!1,searchVisible:!1}},methods:{btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){var t={dateRange:this.dateRange,strfilter:this.strfilter};this.$emit("btnSearch",t)},advancedSearch:function(t){var e={dateRange:this.dateRange,formdata:t};this.$emit("advancedSearch",e),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList),this.$emit("bindColumn")},changeBalance:function(t){var e=0;e=t?1:0,this.$emit("changeBalance",e)}}},l=d,m=(i("a5d3"),i("2877")),c=Object(m["a"])(l,n,r,!1,null,"dd9d1cca",null),u=c.exports,h=i("7f963"),p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableTh",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px",display:"flex","align-items":"center"}},[i("div",{staticStyle:{"margin-right":"10px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)])]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.selectList,printcode:"D04M08B1Edit",commonurl:"/D04M08B1/printBatchBill",weburl:"/D04M08B1/printBatchWebBill"}}),i("PrintServer",{ref:"PrintPageServer",attrs:{formdata:t.lst,queryParams:t.queryParams,printcode:"D04M08B1Th",commonurl:"/D04M08B1/printPageTh",weburl:"/D04M08B1/printWebPageTh"}})],1)},f=[],g=(i("e9c4"),i("a9e3"),i("d3b7"),i("c7cd"),i("159b"),i("f07e")),b=i("b775"),y=i("c03f"),w={components:{MyPopover:g["a"]},props:["online","formtemplate"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:y["d"],mypopoverTable:y["b"],mypopoverData:[],mypopoverIndex:0,exportitle:"领料单",selectList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectList=i?t.lst:[]}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},methods:{bindData:function(){var t=this;if(this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),this.online)var e="/D04M081/getOnlinePageTh";else e="/D04M08B1/getPageTh";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(s["d"])()[0],EndDate:Object(s["d"])()[1]}),b["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this,e=y["d"];this.formtemplate.th.type&&(e.item=this.formtemplate.th.content),this.$getColumn(this.tableForm.formcode,e).then((function(e){t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row,n=(i.column,i.rowIndex);if("billdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("status"==t.itemcode){var r="";return o.finishcount>0&&o.finishcount+o.disannulcount<o.itemcount?r=a("span",{class:"textborder-blue"},["领料"]):o.finishcount+o.disannulcount==o.itemcount&&o.finishcount>0?r=a("span",{class:"textborder-green"},["完成"]):o.disannulcount>0&&o.disannulmark==o.itemcount&&(r=a("span",{class:"textborder-grey"},["撤销"])),r}if("refno"==t.itemcode){r=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return r}if("assessor"==t.itemcode){r="";return r=o.oaflowmark&&!o.assessor?a("span",{style:"color:#ff9800"},["审核中"]):a("span",[o.assessor]),r}if("itemcount"==t.itemcode){r="";return r=a("el-popover",{attrs:{placement:"left",trigger:"click",title:"单据明细"},ref:"'popover-' + scope.$index"},[a("div",{style:"position: relative; min-height: 100px",directives:[{name:"show",value:n==e.mypopoverIndex}]},[a(g["a"],{ref:"mypopover",attrs:{tableForm:e.mypopoverTable,lst:e.mypopoverData}})]),a("span",{slot:"reference",class:"textunderline",on:{click:function(){return e.getBillList(o,n)}}},[o[t.itemcode]])]),r}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:40,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},pagePrint:function(){this.$refs.PrintPageServer.printButton(1,1)},btnPrint:function(){this.$refs.PrintServer.printButton(2,1)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t,1)},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)},getBillList:function(t,e){var i=this;this.mypopoverIndex=e,b["a"].get("/D04M08B1/getBillEntity?key=".concat(t.id)).then((function(t){200==t.data.code?i.mypopoverData=t.data.data.item:i.mypopoverData=[]}))}}},v=w,k=(i("f052"),Object(m["a"])(v,p,f,!1,null,"2572688e",null)),x=k.exports,D=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize,pageSizes:[10,20,100,500]},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.lst,queryParams:t.queryParams,printcode:"D04M08B1List",commonurl:"/D04M08B1/printPageList",weburl:"/D04M08B1/printWebPageList"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},[i("D04M01B3",{ref:"D04M01B3",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.selectList,billcode:"D04M08B1List"},on:{closeDialog:function(e){t.operationVisible=!1,t.bindData()}}})],1):t._e()],1)},S=[],$=(i("c740"),i("caad"),i("a434"),i("2532"),i("13df")),M={components:{D04M01B3:$["default"]},props:["online","formtemplate"],data:function(){var t=this;return{lst:[],total:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:y["c"],customList:[],selectList:[],totalfields:["refno","quantity"],exportitle:"领料明细",dialogIdx:0,operationVisible:!1,customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},checkboxOption:{selectedRowChange:function(e){var i=e.row,a=(e.isSelected,e.selectedRowKeys);if(a.includes(i.id))t.selectList.push(i);else{var o=t.selectList.findIndex((function(t){return t.id==i.id}));-1!=o&&t.selectList.splice(o,1)}},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectList=i?t.lst:[]}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},methods:{bindData:function(){var t=this;if(this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(s["d"])()[0],EndDate:Object(s["d"])()[1]}),this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),this.online)var e="/D04M08B1/getOnlinePageList";else e="/D04M08B1/getPageList";b["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this,e=y["c"];this.formtemplate.list.type&&(e.item=this.formtemplate.list.content),this.$getColumn(this.tableForm.formcode,e).then((function(e){t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var n=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return n}if("goodsuid"==t.itemcode){n=a("GoodsInfo",{attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return n}if("status"==t.itemcode){n="";return 0!=o.finishqty&&o.finishqty<o.quantity?n=a("span",{class:"textborder-blue"},["领料"]):o.finishqty>=o.quantity&&0!=o.finishqty?n=a("span",{class:"textborder-green"},["完成"]):1==o.disannulmark&&(n=a("span",{class:"textborder-grey"},["撤销"])),n}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},pagePrint:function(){this.$refs.PrintServer.printButton(1)},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxamount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t,1)},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)},setDelivery:function(){var t=this;if(0!=this.selectList.length){var e=this.selectList.every((function(t){return t.finishqty===t.quantity}));if(e)this.$message.warning("单据已完成");else{var i=this.selectList.every((function(e){return console.log(e.groupuid),e.groupuid==t.selectList[0].groupuid}));i?this.operationVisible=!0:this.$message.warning("请选择相同车间")}}else this.$message.warning("请选择货品内容")}}},q=M,_=(i("4a10"),Object(m["a"])(q,D,S,!1,null,"034940e2",null)),B=_.exports,O=i("bb0f"),P={name:"D04M08B1",components:{ListHeader:u,FormEdit:h["default"],TableTh:x,TableList:B},data:function(){return{idx:0,online:0,formvisible:!1,thorList:!0,tableForm:{},showhelp:!1,formtemplate:O["a"]}},mounted:function(){this.bindTemp(),this.bindData()},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D04M08B1").then((function(e){200==e.data.code?(null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):O["a"]),t.$nextTick((function(){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()}))):t.$alert(e.data.msg||"获取页面信息失败")})).catch((function(e){t.$message.error("请求错误")}))},bindData:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.tableTh.bindData()})):this.$nextTick((function(){t.$refs.tableList.bindData()}))},pagePrint:function(){this.thorList?this.$refs.tableTh.pagePrint():this.$refs.tableList.pagePrint()},search:function(t){this.thorList?this.$refs.tableTh.search(t):this.$refs.tableList.search(t)},advancedSearch:function(t){this.thorList?this.$refs.tableTh.advancedSearch(t):this.$refs.tableList.advancedSearch(t)},changeModelUrl:function(t){this.thorList=!!t,this.bindData()},changeBalance:function(t){this.online=t,this.bindData()},btnExport:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.tableTh.btnExport()})):this.$nextTick((function(){t.$refs.tableList.btnExport()}))},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},sendTableForm:function(t){this.tableForm=t}}},F=P,C=(i("cecb"),Object(m["a"])(F,a,o,!1,null,"4e6aad4a",null));e["default"]=C.exports},"6ccf":function(t,e,i){},"7f963":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getWorkGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D04M08B1Edit",commonurl:"/D04M08B1/printBill",weburl:"/D04M08B1/printWebBill"}}),i("PrintServer",{ref:"PrintMultiServer",attrs:{formdata:t.formdata,printcode:"D04M08B1EditMulti",commonurl:"/D04M08B1/printWebBillMulti",weburl:"/D04M08B1/printWebBillMulti"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D04M08B1Edit",examineurl:"/D04M08B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},["D04M01B3"==t.processModel?i("D04M01B3",{ref:"D04M01B3",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D04M08B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e()],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D04M01B3"==t.processModel?i("D04M01B3List",{ref:"D04M01B3List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],n=(i("b64b"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);n["a"].post("/D04M08B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);n["a"].post("/D04M08B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){n["a"].get("/D04M08B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,n["a"].get("/D04M08B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D04M08B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D04M08B1/closed?type="+(3==t?1:0);n["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var s=r,d=["id","refno","billtype","billdate","billtitle","groupid","branthname","groupuid","groupname","abbreviate","grouplevel","operator","summary","modulecode","citeuid","citeid","projectid","projcode","projname","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],l=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","quantity","plandate","remark","rownum","pickqty","finishcost","citeuid","citeitemid","customer","custpo","backqty","backcost","closed","stdqty","storeid","storename","location","batchno","machuid","machitemid","machitemgoodid","machgroupid","mainplanuid","mainplanitemid","mainplanitemgoodid","mrpuid","mrpitemid","attributejson","mrpitemgoodid","wkbilltype","workuid","workitemid","workitemgoodid","workitemmatid","parentgoodsid","disannulmark","sourcetype","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],m={params:d,paramsItem:l},c={backcost:0,backqty:0,batchno:"",citeitemid:"",citeuid:"",closed:0,custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",customer:"",custpo:"",disannulmark:0,finishcost:0,finishqty:0,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",id:"",location:"",machgroupid:"",machitemgoodid:"",machitemid:"",machuid:"",mainplanitemgoodid:"",mainplanitemid:"",mainplanuid:"",mrpitemgoodid:"",mrpitemid:"",mrpuid:"",parentgoodsid:"",partid:"",pickqty:0,pid:"",plandate:new Date,quantity:0,remark:"",rownum:0,sourcetype:0,statecode:"",statedate:new Date,stdqty:0,storeid:"",wkbilltype:1,workitemgoodid:"",workitemid:"",workitemmatid:"",workuid:""},u=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,label:"一页两联",icon:"el-icon-printer",disabled:"this.formstate==0",methods:"printMultiServer",children:[]},{show:1,divided:!0,ieval:1,label:'this.formdata.billtype == "生产领料"|| this.formdata.billtype == "领料单" || this.formdata.billtype == "外协领料"? "领料出库" : "退料入库"',icon:"",disabled:"this.formstate!=2",methods:"operateBill",param:"D04M01B3",children:[]}],h=[{show:1,ieval:1,divided:!1,label:'this.formdata.billtype == "生产领料"|| this.formdata.billtype == "领料单" || this.formdata.billtype == "外协领料"? "领料出库" : "退料入库"',icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D04M01B3",label:""},children:[]}],p=i("c7eb"),f=i("1da1");i("c740"),i("b680"),i("ac1f"),i("5319");function g(t,e,i){return b.apply(this,arguments)}function b(){return b=Object(f["a"])(Object(p["a"])().mark((function t(e,i,a){return Object(p["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,n["a"].get("/D91M01B1/getEntity?key="+i.goodsid).then((function(t){if(console.log("zhugoods",t),200==t.data.code){var e=t.data.data;1==e.matqtyunit?(i.goodsunit=e.weightunit,i.quantity=a.$fomatFloat(i.quantity*e.weightqty,4)):2==e.matqtyunit?(i.goodsunit=e.areaunit,i.quantity=a.$fomatFloat(i.quantity*e.areaqty,4)):3==e.matqtyunit?(i.goodsunit=e.volumeunit,i.quantity=a.$fomatFloat(i.quantity*e.volumeqty,4)):(i.goodsunit=e.goodsunit,i.quantity=a.$fomatFloat(i.quantity,4))}else a.$alert(t.data.msg||"查询物料为空","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){a.closeForm()}})}));case 2:return t.next=4,n["a"].get("/D91M01B1/getMapEntityByGoodsUid?key="+e).then((function(t){if(console.log("goods",t),200==t.data.code&&t.data.data.id){var o=t.data.data;i.goodsid=o.id,i.goodsname=o.goodsname,i.goodsspec=o.goodsspec,i.goodsuid=o.goodsuid,i.goodsphoto1=o.goodsphoto1,i.partid=o.partid,a.formdata.item.push(i)}else a.$alert(t.data.msg||"查询"+e+"物料为空","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){a.closeForm()}})}));case 4:case"end":return t.stop()}}),t)}))),b.apply(this,arguments)}function y(t,e,i,a){var o=t.$store.getters.userinfo.configs;if("D01M03B1"==e){t.formdata.billtype=i||"领料单",t.formdata.billtitle="【"+t.initData.refno+"】销售订单转入",t.formdata.item=[];for(var n=0;n<t.initData.item.length;n++){var r=t.initData.item[n],s=Object.assign({},a);s.citeitemid=r.id,s.citeuid=t.initData.refno,s.customer=t.initData.groupid,s.goodsid=r.goodsid,s.goodsname=r.goodsname,s.goodsphoto1=r.goodsphoto1,s.goodsspec=r.goodsspec,s.goodsuid=r.goodsuid,s.goodsunit=r.goodsunit,s.partid=r.partid,s.machgroupid=t.initData.groupid,s.machitemid=r.id,s.machuid=t.initData.refno,s.machdate=t.initData.billdate,s.mainplanitemid=r.mainplanitemid,s.mainplanuid=r.mainplanuid,s.mrpitemid=r.mrpitemid,s.mrpuid=r.mrpuid,s.location=r.location,s.batchno=r.batchno,s.quantity=t.$fomatFloat(r.quantity-r.outquantity,2),s.attributejson=r.attributejson,s.virtualitem=r.virtualitem,s.quantity<=0?(s.quantity=0,o&&o["system.bill.execfinishow"]&&JSON.parse(o["system.bill.execfinishow"])&&t.formdata.item.push(s)):t.formdata.item.push(s)}}else if("D01M03B1Mat"==e){t.formdata.billtype=i||"领料单",t.formdata.billtitle="【"+t.initData.refno+"】销售订单转入",t.formdata.item=[];for(n=0;n<t.initData.item.length;n++){r=t.initData.item[n],s=Object.assign({},a);s.citeitemid=r.id,s.citeuid=t.initData.refno,s.customer=t.initData.groupid,s.goodsid=r.goodsid,s.goodsname=r.goodsname,s.goodsphoto1=r.goodsphoto1,s.goodsspec=r.goodsspec,s.goodsuid=r.goodsuid,s.goodsunit=r.goodsunit,s.partid=r.partid,s.location=r.location,s.batchno=r.batchno,s.machgroupid=t.initData.groupid,s.machitemid=r.id,s.machuid=t.initData.refno,s.machdate=t.initData.billdate,s.mainplanitemid=r.mainplanitemid,s.mainplanuid=r.mainplanuid,s.mrpitemid=r.mrpitemid,s.mrpuid=r.mrpuid,s.quantity=t.$fomatFloat(r.quantity-r.outquantity,2),s.attributejson=r.attributejson,s.virtualitem=r.virtualitem,s.quantity<=0?(s.quantity=0,o&&o["system.bill.execfinishow"]&&JSON.parse(o["system.bill.execfinishow"])&&g(r.matcode,s,t)):g(r.matcode,s,t)}}else if("D05M01B4"==e||"D05M01B1"==e){t.formdata.billtype=i||"领料单",t.formdata.billtitle="【"+t.initData.refno+"】生产工单转入",t.formdata.groupid=t.initData.groupid,t.formdata.groupname=t.initData.groupname,t.formdata.groupuid=t.initData.groupuid,t.formdata.item=[];for(n=0;n<t.initData.item.length;n++){r=t.initData.item[n],s=Object.assign({},a);s.citeitemid=r.id,s.citeuid=t.initData.refno,s.customer=r.customer,s.goodsid=r.goodsid,s.goodsname=r.goodsname,s.goodsphoto1=r.goodsphoto1,s.goodsspec=r.goodsspec,s.goodsuid=r.goodsuid,s.goodsunit=r.goodsunit,s.partid=r.partid,s.location=r.location,s.batchno=r.batchno,s.machgroupid=r.machgroupid,s.machitemid=r.machitemid,s.machuid=r.machuid,s.machdate=r.machdate,s.mainplanitemid=r.mainplanitemid,s.mainplanuid=r.mainplanuid,s.mrpitemid=r.mrpitemid,s.mrpuid=r.mrpuid,s.workitemid=r.id,s.workuid=t.initData.refno,s.quantity=r.wkpcsqty.toFixed(2).replace(/\.00$/,""),s.attributejson=r.attributejson,s.virtualitem=r.virtualitem,s.quantity<=0?(s.quantity=0,o&&o["system.bill.execfinishow"]&&JSON.parse(o["system.bill.execfinishow"])&&t.formdata.item.push(s)):t.formdata.item.push(s)}}else if("D05M02B1"==e){t.formdata.billtype=i||"领料单",t.formdata.billtitle="【"+t.initData.refno+"】委外加工单转入",t.formdata.groupid=t.initData.groupid,t.formdata.groupname=t.initData.groupname,t.formdata.groupuid=t.initData.groupuid,t.formdata.item=[];for(n=0;n<t.initData.item.length;n++){r=t.initData.item[n],s=Object.assign({},a);s.citeitemid=r.id,s.citeuid=t.initData.refno,s.customer=r.groupid,s.goodsid=r.goodsid,s.goodsname=r.goodsname,s.goodsphoto1=r.goodsphoto1,s.goodsspec=r.goodsspec,s.goodsuid=r.goodsuid,s.goodsunit=r.goodsunit,s.partid=r.partid,s.machgroupid=r.machgroupid,s.machitemid=r.machitemid,s.machuid=r.machuid,s.machdate=r.machdate,s.mainplanitemid=r.mainplanitemid,s.mainplanuid=r.mainplanuid,s.mrpitemid=r.mrpitemid,s.mrpuid=r.mrpuid,s.workitemid=r.workitemid,s.workuid=r.workuid,s.quantity=r.subqty.toFixed(2).replace(/\.00$/,""),s.attributejson=r.attributejson,s.virtualitem=r.virtualitem,s.quantity<=0?(s.quantity=0,o&&o["system.bill.execfinishow"]&&JSON.parse(o["system.bill.execfinishow"])&&t.formdata.item.push(s)):t.formdata.item.push(s)}}else if("D05M02B2"===e){t.formdata.billtype=i||"领料单",t.formdata.billtitle="【"+t.initData.refno+"】其他委外加工转入",t.formdata.groupid=t.initData.groupid,t.formdata.groupname=t.initData.groupname,t.formdata.groupuid=t.initData.groupuid,t.formdata.item=[];for(n=0;n<t.initData.mat.length;n++){r=t.initData.mat[n],s=Object.assign({},a);s.citeitemid=r.id,s.citeuid=t.initData.refno,s.customer=r.groupid,s.goodsid=r.goodsid,s.goodsname=r.goodsname,s.goodsphoto1=r.goodsphoto1,s.goodsspec=r.goodsspec,s.goodsuid=r.goodsuid,s.goodsunit=r.goodsunit,s.partid=r.partid,s.machgroupid=r.machgroupid,s.machitemid=r.machitemid,s.machuid=r.machuid,s.machdate=r.machdate,s.mainplanitemid=r.mainplanitemid,s.mainplanuid=r.mainplanuid,s.mrpitemid=r.mrpitemid,s.mrpuid=r.mrpuid,s.workitemid=r.workitemid,s.workuid=r.workuid,s.quantity=r.quantity.toFixed(2).replace(/\.00$/,""),s.attributejson=r.attributejson,s.virtualitem=r.virtualitem,s.quantity<=0?(s.quantity=0,o&&o["system.bill.execfinishow"]&&JSON.parse(o["system.bill.execfinishow"])&&t.formdata.item.push(s)):t.formdata.item.push(s)}}else if("D05M01B1SL"==e||"D05M01B1ML"==e){console.log(t.selectList,"selectListselectListselectList"),console.log(t.initData,"initData"),t.formdata.billtype=i||"生产领料",t.formdata.billtitle="【"+t.initData.refno+"】生产工单转入",t.formdata.groupid=t.initData.groupid,t.formdata.groupname=t.initData.groupname,t.formdata.groupuid=t.initData.groupuid,t.formdata.item=[];var d=t.selectList.length>0?t.selectList:t.initData.mat;for(n=0;n<d.length;n++){r=d[n],s=Object.assign({},a);s.goodsid=r.goodsid,s.goodsname=r.goodsname,s.goodsphoto1=r.goodsphoto1,s.goodsspec=r.goodsspec,s.goodsuid=r.goodsuid,s.goodsunit=r.goodsunit,s.goodsmaterial=r.goodsmaterial,s.partid=r.partid,s.citeitemid=r.id,s.citeuid=t.initData.refno,s.customer=r.customer,s.custpo=r.custpo,s.machgroupid=r.machgroupid,s.machitemid=r.machitemid,s.machuid=r.machuid,s.mainplanitemid=r.mainplanitemid,s.mainplanuid=r.mainplanuid,s.mrpitemid=r.mrpitemid,s.mrpuid=r.mrpuid,s.plandate=t.initData.plandate?t.initData.plandate:new Date,s.quantity=i?r.finishqty:"物料"==r.goodsstate?t.$fomatFloat(r.bomqty-r.finishqty,2):t.$fomatFloat(r.stoplanqty-r.finishqty,2),s.workitemmatid=r.id,s.workuid=t.initData.refno,s.workitemgoodid=r.goodsid,s.attributejson=r.attributejson,s.virtualitem=r.virtualitem;var l=t.initData.item.findIndex((function(t){return t.rowcode==r.itemrowcode}));-1!=l&&(s.machuid=t.initData.item[l].machuid,s.machitemid=t.initData.item[l].machitemid,s.machgroupid=t.initData.item[l].machgroupid,s.mainplanitemid=t.initData.item[l].mainplanitemid,s.mainplanuid=t.initData.item[l].mainplanuid,s.mrpitemid=t.initData.item[l].mrpitemid,s.mrpuid=t.initData.item[l].mrpuid),s.quantity&&t.formdata.item.push(s)}}else if("D05M02B1ML"==e){t.formdata.billtype=i||"领料单",t.formdata.billtitle="【"+t.initData.refno+"】委外加工单转入",t.formdata.item=[];for(d=t.initData.mat,n=0;n<d.length;n++){r=d[n],s=Object.assign({},a);s.goodsid=r.goodsid,s.goodsname=r.goodsname,s.goodsphoto1=r.goodsphoto1,s.goodsspec=r.goodsspec,s.goodsuid=r.goodsuid,s.goodsunit=r.goodsunit,s.goodsmaterial=r.goodsmaterial,s.partid=r.partid,s.citeitemid=r.id,s.citeuid=t.initData.refno,s.customer=r.customer,s.custpo=r.custpo,s.machgroupid=r.machgroupid,s.machitemid=r.machitemid,s.machuid=r.machuid,s.machdate=r.machdate,s.mainplanitemid=r.mainplanitemid,s.mainplanuid=r.mainplanuid,s.mrpitemid=r.mrpitemid,s.mrpuid=r.mrpuid,s.quantity=i?r.finishqty:"物料"==r.goodsstate?t.$fomatFloat(r.bomqty-r.finishqty,2):t.$fomatFloat(r.stoplanqty-r.finishqty,2),s.workitemid=r.workitemid,s.workitemmatid=r.workitemmatid,s.workuid=r.workuid,s.workitemgoodid=r.workitemgoodid,s.attributejson=r.attributejson,s.virtualitem=r.virtualitem,s.wkbilltype=2;l=t.initData.item.findIndex((function(t){return t.rowcode==r.itemrowcode}));-1!=l&&(s.machuid=t.initData.item[l].machuid,s.machitemid=t.initData.item[l].machitemid,s.machgroupid=t.initData.item[l].machgroupid,s.mainplanitemid=t.initData.item[l].mainplanitemid,s.mainplanuid=t.initData.item[l].mainplanuid,s.mrpitemid=t.initData.item[l].mrpitemid,s.mrpuid=t.initData.item[l].mrpuid),t.formdata.item.push(s)}}else if("D05M03B1"==e){console.log(t.selectList,"selectListselectListselectList"),console.log(t.initData,"initData"),t.formdata.billtype=i||"生产领料",t.formdata.billtitle="【"+t.initData.refno+"】生产验收单转入",t.formdata.groupid=t.initData.groupid,t.formdata.groupname=t.initData.groupname,t.formdata.groupuid=t.initData.groupuid,t.formdata.item=[];for(d=t.selectList.length>0?t.selectList:t.initData.mat,n=0;n<d.length;n++){r=d[n],s=Object.assign({},a);s.goodsid=r.goodsid,s.goodsname=r.goodsname,s.goodsphoto1=r.goodsphoto1,s.goodsspec=r.goodsspec,s.goodsuid=r.goodsuid,s.goodsunit=r.goodsunit,s.goodsmaterial=r.goodsmaterial,s.partid=r.partid,s.sourcetype=2,s.citeitemid=r.id,s.citeuid=t.initData.refno,s.customer=r.customer,s.custpo=r.custpo,s.machgroupid=r.machgroupid,s.machitemid=r.machitemid,s.machuid=r.machuid,s.mainplanitemid=r.mainplanitemid,s.mainplanuid=r.mainplanuid,s.mrpitemid=r.mrpitemid,s.mrpuid=r.mrpuid,s.quantity=r.quantity,s.workitemmatid=r.workitemmatid,s.workitemid=r.workitemid;l=t.initData.item.findIndex((function(t){return t.id==r.itemid}));-1!=l&&(s.machuid=t.initData.item[l].machuid,s.machitemid=t.initData.item[l].machitemid,s.machgroupid=t.initData.item[l].machgroupid,s.mainplanitemid=t.initData.item[l].mainplanitemid,s.mainplanuid=t.initData.item[l].mainplanuid,s.mrpitemid=t.initData.item[l].mrpitemid,s.mrpuid=t.initData.item[l].mrpuid,s.workuid=t.initData.item[l].workuid),s.quantity&&t.formdata.item.push(s)}}}var w=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[]}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"领料单",value:"领料单"}}),i("el-option",{attrs:{label:"退料单",value:"退料单"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[i("el-form-item",{attrs:{label:"单据主题"}},[i("el-input",{attrs:{placeholder:"请输入单据主题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupid")}}},[i("el-form-item",{attrs:{label:"车间",prop:"groupid"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B3/getPageList",type:"生产车间"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupid")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)])],1)],1)},v=[],k={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupid:[{required:!0,trigger:"blur",message:"部门为必填项"}],storeid:[{required:!0,trigger:"blur",message:"仓库名称为必填项"}],direction:[{required:!0,trigger:"blur",message:"操作方向为必填项"}]},selVisible:!1}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},x=k,D=(i("8d15"),i("2877")),S=Object(D["a"])(x,w,v,!1,null,"72dd3a33",null),$=S.exports,M=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","copyrow","refresh","billstate"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selgoodsvisible?i("el-dialog",{attrs:{title:"货品信息","append-to-body":!0,visible:t.selgoodsvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selgoodsvisible=e}}},[i("SelGoods",{ref:"selGoods",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selGoods()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selgoodsvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.machuidVisible?i("el-dialog",{attrs:{title:"销售订单","append-to-body":!0,visible:t.machuidVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.machuidVisible=e}}},[i("SaleOrder",{ref:"SaleOrder",attrs:{multi:0}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selMachid()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.machuidVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.inventoryVisible?i("el-dialog",{attrs:{title:"BOM清单","append-to-body":!0,visible:t.inventoryVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.inventoryVisible=e}}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")]),i("el-table",{staticStyle:{width:"100%","max-height":"450px",overflow:"auto"},attrs:{data:t.inventoryData,border:"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"highlight-current-row":""},on:{"selection-change":t.handleBom,"row-click":t.saveinventory}},[i("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),i("el-table-column",{attrs:{prop:"goodsType",label:"类别","min-width":"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return["主料"==e.row.goodsType?i("span",{staticStyle:{color:"#67c23a"}},[t._v(t._s(e.row.goodsType))]):i("span",{staticStyle:{color:"#409eff"}},[t._v(t._s(e.row.goodsType))])]}}],null,!1,3971086252)}),i("el-table-column",{attrs:{prop:"goodsuid",label:"货品编码","min-width":"100",align:"center"}}),i("el-table-column",{attrs:{prop:"goodsname",label:"货品名称","min-width":"100",align:"center","show-overflow-tooltip":""}}),i("el-table-column",{attrs:{prop:"goodsspec",label:"货品规格","min-width":"100",align:"center","show-overflow-tooltip":""}}),i("el-table-column",{attrs:{prop:"partid",label:"外部编码","min-width":"100",align:"center","show-overflow-tooltip":""}}),i("el-table-column",{attrs:{prop:"quantity",label:"领用数量","min-width":"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-input-number",{directives:[{name:"show",rawName:"v-show",value:e.row.isEdit,expression:"scope.row.isEdit"}],staticStyle:{width:"100%"},attrs:{"controls-position":"right",step:1,min:0,size:"small",controls:!1},model:{value:e.row.quantity,callback:function(i){t.$set(e.row,"quantity",i)},expression:"scope.row.quantity"}}),i("span",{directives:[{name:"show",rawName:"v-show",value:!e.row.isEdit,expression:"!scope.row.isEdit"}]},[t._v(t._s(e.row.quantity))])]}}],null,!1,2527021124)})],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selinventory()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.inventoryVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},q=[],_=(i("caad"),i("a434"),i("e9c4"),i("a9e3"),i("d3b7"),i("2532"),i("c7cd"),i("159b"),i("c03f")),B=i("2499"),O=i("9bda"),P={name:"Elitem",components:{SelGoods:O["a"],SaleOrder:B["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],multi:0,keynum:0,selgoodsvisible:!1,setColumsVisible:!1,machuidVisible:!1,machuidIndex:0,replaceList:[],replaceVisible:!1,replaceIndex:0,radio:"",CurrentRow:{},total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:0},inventoryVisible:!1,getallList:[],inventoryData:[],selrowsList:[],strfilter:"",tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,index:0,tableForm:_["b"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(){var e=Object(f["a"])(Object(p["a"])().mark((function e(i){var a,o,n;return Object(p["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=i.row,o=i.column,t.editmarkfiles.includes(o.field)&&(t.countfiles.includes(o.field)&&t.changeInput("",a,o.field),n=t.customList.findIndex((function(t){return t.attrkey==o.field})),-1!=n&&t.setAttributeJson(a,a.rownum),t.$forceUpdate());case 2:case"end":return e.stop()}}),e)})));function i(t){return e.apply(this,arguments)}return i}()},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(){var e=Object(f["a"])(Object(p["a"])().mark((function e(i){var a,o,n,r,s;return Object(p["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(i.sourceSelectionData,a=i.targetSelectionData,o=0;o<a.length;o++)n=a[o],r=t.lst.findIndex((function(t){return t.rowKeys==n.rowKeys})),-1!=r&&(t.countfiles.includes(Object.keys(n)[1])&&t.changeInput("",t.lst[r],Object.keys(n)[1]),s=t.customList.findIndex((function(t){return t.attrkey==Object.keys(n)[1]})),-1!=s&&t.setAttributeJson(t.lst[r],r),t.$forceUpdate());case 2:case"end":return e.stop()}}),e)})));function i(t){return e.apply(this,arguments)}return i}()},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),t[i].parentgoodsid?t[i].goodsType="替代料":t[i].goodsType="主料",0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var n=JSON.parse(t[i].attributejson);for(a=0;a<n.length;a++)t[i][n[a].key]=n[a].value}}this.$forceUpdate(),this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(f["a"])(Object(p["a"])().mark((function e(){var i;return Object(p["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=_["b"],t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn(_["b"].formcode,i,1,0,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,operationColumn:!!t.operationmark,renderBodyCell:function(i,a){var o=i.row,n=(i.column,i.rowIndex);if("billdate"==t.itemcode||"plandate"==t.itemcode){var r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[e.$options.filters.dateFormat(o[t.itemcode])])]),a("div",[a("div",{style:"opacity: 0;width:0;height:0"},[a("el-date-picker",{style:"width:0;height:0",attrs:{size:"small",id:t.itemcode+n+_["b"].formcode,value:new Date(o[t.itemcode]),type:"date",format:"MM-dd","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},on:{input:function(e){o[t.itemcode]=e}}})]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-date",on:{click:function(){document.getElementById(t.itemcode+n+_["b"].formcode).focus()}}})])]);return r}if("goodsuid"==t.itemcode){r=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeVal:o[t.itemcode]}});return r}if("goodsType"==t.itemcode){r="";return r="主料"==o.goodsType?a("span",{style:"color: #67c23a"},[" ",o.goodsType]):a("span",{style:"color: #409eff"},[o.goodsType]),r}if("machuid"==t.itemcode){r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("span",{class:o.disannulmark?"textlinethrough":"",style:"flex:1"},[o[t.itemcode]]),a("i",{class:"writePacksn el-icon-circle-plus-outline",on:{click:function(){return e.openMachuid(o,o.rownum)}}})]);return r}r=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]);return r}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1,this.$forceUpdate()},getSummary:function(){this.$getSummary(this,["goodsuid","quantity"])},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var n=t[o],r=0;r<i.length;r++){var s=i[r];this.lst[e+o][s]=n[s].replace(/^\s*|\s*$/g,""),this.countfiles.includes(s)&&this.changeInput("",this.lst[e+o],s)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),this.getSummary()},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.$delete(e,"finishqty"),this.$delete(e,"statecode"),this.$delete(e,"statedate"),this.lst.push(e)}},getAdd:function(t){this.selgoodsvisible=!0},openMachuid:function(t,e){this.machuidVisible=!0,this.machuidIndex=e},selMachid:function(){var t=this.$refs.SaleOrder.selrows;this.lst[this.machuidIndex].machuid=t.refno,this.lst[this.machuidIndex].machgroupid=t.groupid,this.lst[this.machuidIndex].machitemid=t.id,this.machuidVisible=!1},selGoods:function(){var t=this.$refs.selGoods.$refs.selectgoods.selection;if(0!=t.length){this.selgoodsvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},c);a.goodsid=i.id,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.itemcode=i.goodsuid,a.itemname=i.goodsname,a.itemspec=i.goodsspec,a.itemunit=i.goodsunit,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selPwProcess:function(){var t=this;return Object(f["a"])(Object(p["a"])().mark((function e(){var i;return Object(p["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=t.$refs.selPwProcess.selrows,i){e.next=4;break}return t.$message.warning("选择内容不能为空"),e.abrupt("return");case 4:return t.formdata.billtitle="【"+i.refno+"】加工单转入",e.next=7,n["a"].get("/D05M01B1/getMatList?key="+i.pid).then((function(e){if(200==e.data.code){if(t.inventoryVisible=!0,!e.data.data)return void t.$message.warning("该货品暂无物料");t.inventoryData=[],e.data.data.forEach((function(e,a){var o={goodsid:e.goodsid,goodsuid:e.goodsuid,goodsname:e.goodsname,goodsunit:e.goodsunit,goodsspec:e.goodsspec,flowcode:e.flowcode,partid:e.partid,quantity:e.quantity,plandate:i.plandate,finishqty:0,citeuid:i.refno,citeitemid:i.id,machuid:i.machuid,machitemid:i.machitemid,machgroupid:i.machgroupid,mainplanitemid:i.mainplanitemid,mainplanuid:i.mainplanuid,workitemgoodid:i.goodsid,workuid:i.refno,workitemid:i.id,mrpuid:i.mrpuid,mrpitemid:i.mrpitemid,wkbilltype:1,workitemmatid:e.id};0!=t.idx&&(o.pid=t.idx),e.parentgoodsid?o.goodsType="替代料":o.goodsType="主料",t.getallList.push(o),t.inventoryData.push(o)}))}else t.$message.warning(e.data.code+"，物料查询失败")}));case 7:case"end":return e.stop()}}),e)})))()},search:function(t){if(""!=t){this.inventoryData=[];for(var e=0;e<this.getallList.length;e++){var i=this.getallList[e];t==i.flowcode&&this.inventoryData.push(i)}}else this.inventoryData=this.getallList},selReplace:function(){var t=this.lst[this.replaceIndex];if(Number(t.quantity)<Number(this.CurrentRow.quantity))this.$message.warning("替代料不能大于主料");else{t.quantity=Number(t.quantity)-Number(this.CurrentRow.quantity);var e={goodsid:this.CurrentRow.goodsid,goodsuid:this.CurrentRow.goodsuid,goodsname:this.CurrentRow.goodsname,goodsunit:this.CurrentRow.goodsunit,goodsspec:this.CurrentRow.goodsspec,partid:this.CurrentRow.partid,quantity:this.CurrentRow.quantity,goodsType:"替代料",plandate:t.plandate,finishqty:0,citeuid:t.citeuid,citeitemid:t.citeitemid,workitemgoodid:t.workitemgoodid,workuid:t.workuid,workitemid:t.workitemid};0!=this.idx&&(e.pid=this.idx),this.lst.splice(this.replaceIndex+1,0,e),this.replaceVisible=!1}},getCurrentRow:function(t){this.CurrentRow=t},handleBom:function(t){this.selrowsList=t},saveinventory:function(t){for(var e=0;e<this.inventoryData.length;e++)this.inventoryData[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0))},BomRowClassName:function(t){var e=t.row,i=t.rowIndex;e.index=i},savereplace:function(t){console.log(t,"savereplace"),this.radio=t.index;for(var e=0;e<this.replaceList.length;e++)this.replaceList[e].isEdit=!1;t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0)},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],n={key:o.attrkey,value:t[o.attrkey]?String(t[o.attrkey]).replace(/\s*/g,""):""};""!=n.value&&i.push(n)}if(0==i.length)this.lst[e].attributejson="";else{this.lst[e].attributejson=JSON.stringify(i);for(a=0;a<i.length;a++)this.$set(this.lst[e],i[a].key,i[a].value)}this.$forceUpdate()}}},F=P,C=(i("8c16"),Object(D["a"])(F,M,q,!1,null,"b75ac3d0",null)),L=C.exports,I=i("bb0f"),T=i("dcb4"),j=i("13df"),R=i("27f6"),N={name:"Formedit",components:{FormTemp:T["a"],EditHeader:$,EditItem:L,D04M01B3:j["default"],D04M01B3List:R["a"]},props:["idx","isDialog","initData","billcode","billType","selectList"],data:function(){return{title:"领料单据",operateBar:u,processBar:h,formdata:{billdate:new Date,billtitle:"",billtype:"领料单",branthname:"",disannulmark:0,groupid:"",groupname:"",groupuid:"",projectid:"",projcode:"",projname:"",item:[],modulecode:"",operator:"",refno:"",summary:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:I["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode,this.billType)},methods:{bindTemp:function(){var t=this;n["a"].get("/SaFormCustom/getEntityByCode?key=D04M08B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):I["a"],t.formtemplate.footer.type||(t.formtemplate.footer=I["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&n["a"].get("/D04M08B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id},autoClear:function(){this.formdata.groupname="",this.formdata.groupid=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i={item:[]};i=this.$getParam(m,i,this.formdata),0==this.idx?s.add(i).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):s.update(i).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){console.log("isDialog",this.isDialog),this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){s.delete(e)})).catch((function(){}))},approval:function(){s.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?s.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){s.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},changeBillType:function(){formdata.item=[]},printMultiServer:function(){this.$refs.PrintMultiServer.printButton(0,1)},billSwitch:function(t,e){y(this,t,e,c)}}},E=N,z=(i("bfc3"),Object(D["a"])(E,a,o,!1,null,"b00f9962",null));e["default"]=z.exports},"841c":function(t,e,i){"use strict";var a=i("d784"),o=i("825a"),n=i("1d80"),r=i("129f"),s=i("14c3");a("search",1,(function(t,e,i){return[function(e){var i=n(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,i):new RegExp(e)[t](String(i))},function(t){var a=i(e,t,this);if(a.done)return a.value;var n=o(t),d=String(this),l=n.lastIndex;r(l,0)||(n.lastIndex=0);var m=s(n,d);return r(n.lastIndex,l)||(n.lastIndex=l),null===m?-1:m.index}]}))},"8c16":function(t,e,i){"use strict";i("9c55")},"8d15":function(t,e,i){"use strict";i("c403")},"92a7":function(t,e,i){},"9c55":function(t,e,i){},a5d3:function(t,e,i){"use strict";i("0e0ff")},a7b6:function(t,e,i){},b83a:function(t,e,i){"use strict";i("11f6")},bb0f:function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"领料单",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"领料单",value:"领料单"},{label:"退料单",value:"退料单"},{label:"生产领料",value:"生产领料"},{label:"生产退料",value:"生产退料"}],required:!0},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"车间",searchtype:"workshop",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"projectid",label:"项目",type:"input",methods:"",param:""}]}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"领用人员",type:"input",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},bfc3:function(t,e,i){"use strict";i("a7b6")},c03f:function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return n})),i.d(e,"a",(function(){return r}));var a={formcode:"D04M08B1Th",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Mat_Requisition.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.billtype"},{itemcode:"billtitle",itemname:"单据主题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Requisition.billdate"},{itemcode:"groupname",itemname:"车间",minwidth:"70",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"operator",itemname:"领用人员",minwidth:"70",displaymark:1,overflow:1,datasheet:"Mat_Requisition.operator"},{itemcode:"summary",itemname:"摘要",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.summary"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemcount",itemname:"款数",sortable:1,minwidth:"60",displaymark:1,overflow:1,datasheet:"Mat_Requisition.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.assessor"}]},o={formcode:"D04M08B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",datasheet:"Mat_Requisition.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.billtype"},{itemcode:"billtitle",itemname:"单据主题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Requisition.billdate"},{itemcode:"groupname",itemname:"车间",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_RequisitionItem.quantity"},{itemcode:"workuid",itemname:"加工单号",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_RequisitionItem.workuid"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1}]},n={formcode:"D04M08B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"60",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"plandate",itemname:"计划完成",minwidth:"180",displaymark:1,overflow:1},{itemcode:"finishqty",itemname:"完成数",minwidth:"80",displaymark:1,overflow:1},{itemcode:"citeuid",itemname:"应用单号",minwidth:"100",displaymark:1,overflow:1},{itemcode:"machuid",itemname:"销售订单",minwidth:"125",displaymark:1,overflow:1,editmark:1},{itemcode:"goodsType",itemname:"物料类别",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"120",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D04M08B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",datasheet:"Mat_Requisition.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.billtype"},{itemcode:"billtitle",itemname:"单据主题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Requisition.billdate"},{itemcode:"groupname",itemname:"车间",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"goodsmaterial",itemname:"材质",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.material"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_RequisitionItem.quantity"},{itemcode:"workuid",itemname:"加工单号",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_RequisitionItem.workuid"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1}]}},c403:function(t,e,i){},cecb:function(t,e,i){"use strict";i("5413")},f052:function(t,e,i){"use strict";i("92a7")},fb056:function(t,e,i){"use strict";i("6ccf")}}]);