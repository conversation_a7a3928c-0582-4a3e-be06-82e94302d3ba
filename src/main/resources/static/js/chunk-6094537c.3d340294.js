(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6094537c","chunk-eb579050"],{"015e":function(t,e,i){"use strict";i("0e1a")},"0377":function(t,e,i){"use strict";i("6947")},"0d78":function(t,e,i){"use strict";i("4f7f")},"0e1a":function(t,e,i){},"11f6":function(t,e,i){},"12f9":function(t,e,i){},1535:function(t,e,i){},"1f11":function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"采购验收",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购验收",value:"采购验收"},{label:"采购退货",value:"采购退货"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"供应商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"arrivaladd",label:"收货地址",type:"input",methods:"",param:""},{col:5,code:"transport",label:"物流方式",type:"input",methods:"",param:""}]}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},"233b":function(t,e,i){},"233f":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{},[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",{staticStyle:{"margin-bottom":"10px"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,height:350,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"1px 0px 1px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"55"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"账户名称",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.accountname)+" ")]}}])}),i("el-table-column",{attrs:{label:"账户类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.accounttype))])]}}])}),i("el-table-column",{attrs:{label:"当前金额",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.currentamt))])]}}])}),i("el-table-column",{attrs:{label:"备注",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.remark))])]}}])}),i("el-table-column",{attrs:{label:"创建时间",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormat")(e.row.createdate)))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},o=[],s=(i("e9c4"),i("b775")),r=i("333d"),n={components:{Pagination:r["a"]},props:["multi"],data:function(){return{title:"出纳账户",listLoading:!0,lst:[],strfilter:"",radio:"",selrows:"",total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},watch:{},created:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,s["a"].post("/D07M21B2/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={accountname:t,accounttype:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},l=n,d=i("2877"),m=Object(d["a"])(l,a,o,!1,null,"0e79971c",null);e["a"]=m.exports},"27f6":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},o=[],s=i("c7eb"),r=i("1da1"),n=(i("caad"),i("e9c4"),i("a9e3"),i("b64b"),i("d3b7"),i("2532"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),i("b775")),l=i("5e63"),d=i("b893"),m={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"13df"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:l["b"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={citeuid:this.searchVal};this.queryParams.SearchPojo=e;var i="/D04M01R1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(d["d"])()[0],EndDate:Object(d["d"])()[1]}),n["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(l["b"].formcode,l["b"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=a("GoodsInfo",{attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return s}if("billtype"==t.itemcode){s=a("span",[o[t.itemcode]]);return o[t.itemcode].includes("红冲")&&(s=a("span",{style:"color:#f44336"},[o[t.itemcode]])),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},c=m,u=(i("b83a"),i("2877")),f=Object(u["a"])(c,a,o,!1,null,"34f0715a",null);e["a"]=f.exports},3218:function(t,e,i){},3470:function(t,e,i){},3949:function(t,e,i){"use strict";i("dea7")},"3a59":function(t,e,i){},"3d5d":function(t,e,i){"use strict";i.d(e,"e",(function(){return a})),i.d(e,"b",(function(){return o})),i.d(e,"d",(function(){return s})),i.d(e,"a",(function(){return r})),i.d(e,"c",(function(){return n}));var a={formcode:"D03M06B1List",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Voucher.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Voucher.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Voucher.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.summary"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.assessor"}]},o={formcode:"D03M06B1Cash",item:[{itemcode:"cashaccname",itemname:"出纳账户",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"amount",itemname:"金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},s={formcode:"D03M06B1Item",item:[{itemcode:"invobillcode",itemname:"发票单据号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"invocode",itemname:"发票编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"invoamount",itemname:"发票金额",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"金额",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D03M06B1List",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Voucher.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.summary"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.assessor"}]},n={formcode:"D03M06B1Cite",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Voucher.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Voucher.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Voucher.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.summary"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.assessor"}]}},"3e09":function(t,e,i){},"3fbc":function(t,e,i){"use strict";i("a485")},"458a":function(t,e,i){"use strict";i("3a59")},"4c0c":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selStore",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{fixed:"left",type:"selection",width:"55"}}):i("el-table-column",{attrs:{label:"",width:"40",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index,size:"small"},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" ")+" ")])]}}])}),i("el-table-column",{attrs:{label:"仓库编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.storecode))])]}}])}),i("el-table-column",{attrs:{label:"仓库名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.storename))])]}}])}),i("el-table-column",{attrs:{label:"仓库地址",align:"center","min-width":"150","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.storeadd))])]}}])}),i("el-table-column",{attrs:{label:"仓管员",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.operator))])]}}])}),i("el-table-column",{attrs:{label:"电话",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.storetel))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},o=[],s=(i("e9c4"),i("b775")),r=i("333d"),n={components:{Pagination:r["a"]},props:["multi","storeid"],data:function(){return{title:"选择仓库",listLoading:!0,lst:[],strfilter:"",radio:"",selrows:"",total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},watch:{},mounted:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.$emit("singleSel",t),this.selrows=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,this.storeid&&(this.queryParams.SearchPojo={storeid:this.storeid}),s["a"].post("/D04M21S1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={storename:t,storecode:t,operator:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},l=n,d=(i("6dd7"),i("2877")),m=Object(d["a"])(l,a,o,!1,null,"655e468e",null);e["a"]=m.exports},"4cf0":function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"a",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"b",(function(){return r}));var a={formcode:"D03M06B1PREList",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Prepayments.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.summary"},{itemcode:"outamount",itemname:"转出金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.outamount"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.status"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.assessor"}]},o={formcode:"D03M06B1PRECash",item:[{itemcode:"cashaccname",itemname:"出纳账户",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"amount",itemname:"金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},s={formcode:"D03M06B1PREItem",item:[{itemcode:"orderbillcode",itemname:"订单单号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"billtaxamount",itemname:"单据金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"金额",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D03M06B1PRECite",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Prepayments.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.summary"},{itemcode:"outamount",itemname:"转出金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.outamount"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.status"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.assessor"}]}},"4d45":function(t,e,i){},"4f5e":function(t,e,i){},"4f7f":function(t,e,i){},"501c":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},o=[],s=i("c7eb"),r=i("1da1"),n=(i("caad"),i("e9c4"),i("a9e3"),i("d3b7"),i("2532"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),i("b775")),l=i("3d5d"),d=i("b893"),m={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"d85f"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:l["c"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={citecode:this.searchVal};this.queryParams.SearchPojo=e;var i="/D03M06B1/getPageTh";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(d["d"])()[0],EndDate:Object(d["d"])()[1]}),n["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total)})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(l["c"].formcode,l["c"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("billtype"==t.itemcode){s=a("span",[o[t.itemcode]]);return o[t.itemcode].includes("红冲")&&(s=a("span",{style:"color:#f44336"},[o[t.itemcode]])),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxamount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){console.log(t,"222"),this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},c=m,u=(i("5880"),i("2877")),f=Object(u["a"])(c,a,o,!1,null,"7c16eaee",null);e["a"]=f.exports},5880:function(t,e,i){"use strict";i("4f5e")},"5b00":function(t,e,i){"use strict";i("66cd")},"5cc6":function(t,e,i){var a=i("74e8");a("Uint8",(function(t){return function(e,i,a){return t(this,e,i,a)}}))},"5f1f":function(t,e,i){},"65e3":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],s=(i("e9c4"),i("b775")),r=i("333d"),n={components:{Pagination:r["a"]},props:["multi","groupid","selecturl"],data:function(){return{title:"采购订单",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M02B1/getOnlinePageList?groupid="+this.groupid;s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={goodsname:t,goodsuid:t,goodsspec:t,groupuid:t,groupname:t,refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},l=n,d=(i("8da8"),i("2877")),m=Object(d["a"])(l,a,o,!1,null,"ecf1cb7c",null);e["a"]=m.exports},"66cd":function(t,e,i){},6947:function(t,e,i){},"6c68":function(t,e,i){"use strict";i("12f9")},"6dd7":function(t,e,i){"use strict";i("5f1f")},"6e4b":function(t,e,i){},8559:function(t,e,i){"use strict";i("8bcd")},"85e2":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getSuppGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M04B1Edit",commonurl:"/D03M04B1/printBill",weburl:"/D03M04B1/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M04B1Edit",examineurl:"/D03M04B1/sendapprovel"}})],1)},o=[],s=(i("b64b"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M04B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M04B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D03M04B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D03M04B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M04B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M04B1/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=r,l=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","operator","taxrate","billtaxamount","billamount","billtaxtotal","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10","deptid"],d=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","partid","itemcode","itemname","itemspec","itemunit","quantity","taxprice","taxamount","taxtotal","itemtaxrate","price","amount","remark","citeuid","citeitemid","orderuid","orderitemid","custpo","rownum","invoclosed","disannulmark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],m={params:l,paramsItem:d},c={amount:0,citeitemid:"",citeuid:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",custpo:"",disannuldate:new Date,disannullister:"",disannullisterid:"",disannulmark:0,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",id:"",intQtyMark:0,invoclosed:0,invoqty:0,itemcode:"",itemname:"",itemspec:"",itemtaxrate:0,itemunit:"",orderitemid:"",orderuid:"",partid:"",pid:"",price:0,quantity:0,remark:"",revision:0,rownum:0,taxamount:0,taxprice:0,taxtotal:0},u=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]}],f=[],h=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[]}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购扣款",value:"采购扣款"}}),i("el-option",{attrs:{label:"其他扣款",value:"其他扣款"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[i("el-form-item",{attrs:{label:"单据主题"}},[i("el-input",{attrs:{placeholder:"请输入单据主题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"供应商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{staticStyle:{display:"flex"}},[i("el-form-item",{attrs:{label:"总金额:"}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.billtaxamount?t.formdata.billtaxamount:0))])])],1)])],1)],1)},p=[],g={props:{formdata:{type:Object},title:{type:String}},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"供应商为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},b=g,y=(i("0d78"),i("2877")),v=Object(y["a"])(b,h,p,!1,null,"94db9a60",null),w=v.exports,x=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","dummy","moveup","movedown","delete","copyrow","refresh","billstate"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection,dummyurl:"/D91M01B1/getVirOnlinePageList"},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,selDummy:t.selDummy,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selgoodsvisible?i("el-dialog",{attrs:{title:"货品信息","append-to-body":!0,visible:t.selgoodsvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selgoodsvisible=e}}},[i("SelGoods",{ref:"selGoods",attrs:{multi:t.multi}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selGoods()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selgoodsvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.OrderVisible?i("el-dialog",{attrs:{title:"采购订单","append-to-body":!0,visible:t.OrderVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.OrderVisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1,selecturl:"/D03M02B1/getOnlinePageList?groupid="+t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.OrderVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},k=[],S=i("c7eb"),D=i("1da1"),$=(i("c740"),i("caad"),i("d81d"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),i("af70")),_=i("9bda"),B=i("65e3"),O={name:"Elitem",components:{SelGoods:_["a"],SelOrder:B["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],multi:0,keynum:0,selgoodsvisible:!1,setColumsVisible:!1,OrderVisible:!1,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,index:0,tableForm:$["a"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;if(t.editmarkfiles.includes(a.field)){t.countfiles.includes(a.field)&&t.changeInput("",i,a.field);var o=t.customList.findIndex((function(t){return t.attrkey==a.field}));-1!=o&&t.setAttributeJson(i,i.rownum)}}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));if(-1!=s){t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[s],Object.keys(o)[1]);var r=t.customList.findIndex((function(t){return t.attrkey==Object.keys(o)[1]}));-1!=r&&t.setAttributeJson(t.lst[s],s)}}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var s=JSON.parse(t[i].attributejson);for(a=0;a<s.length;a++)t[i][s[a].key]=s[a].value}}this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(D["a"])(Object(S["a"])().mark((function e(){var i;return Object(S["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=$["a"],t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn($["a"].formcode,i).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,operationColumn:!!t.operationmark,renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex,""),r=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));if(-1!=r){var n=e.customList[r].valuejson?e.customList[r].valuejson.split(","):[];return s=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]])]),a("div",{directives:[{name:"show",value:n.length}]},[a("div",{style:"width:0;height:0"},[a("el-select",{class:"wpselect",style:"width:0;height:0;opacity: 0;",attrs:{size:"small",id:t.itemcode+o.rownum+$["a"].formcode},on:{change:function(i){o[t.itemcode]=i,e.setAttributeJson(o,o.rownum)}},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}},[n.map((function(t,e){return a("el-option",{key:e,attrs:{label:t,value:t},style:"max-width:200px"})}))])]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-arrow-down",style:"font-weight:bold;font-size:14px;",on:{click:function(){document.getElementById(t.itemcode+o.rownum+$["a"].formcode).click()}}})])]),s}return"goodsuid"==t.itemcode?(s=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}}),s):(s=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]),s)}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1,this.$forceUpdate()},getSummary:function(){this.$getSummary(this,["goodsuid","amount","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.changeInput("",this.lst[e+o],n);var l=this.customList.findIndex((function(t){return t.attrkey==n}));-1!=l&&this.setAttributeJson(this.lst[e+o],e+o)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],s={key:o.attrkey,value:t[o.attrkey]?t[o.attrkey].replace(/\s*/g,""):""};""!=s.value&&i.push(s)}0==i.length?this.lst[e].attributejson="":this.lst[e].attributejson=JSON.stringify(i),this.$forceUpdate()},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i),this.getSummary()},getAdd:function(t){if("采购扣款"==this.formdata.billtype){if(""==this.formdata.groupid)return void this.$message.warning("请选择供应商");this.OrderVisible=!0}else this.selgoodsvisible=!0;this.multi=t},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"closed",0),this.$delete(e,"disannullisterid"),this.$delete(e,"disannuldate"),this.$delete(e,"invoqty"),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},selGoods:function(){var t=this.$refs.selGoods.$refs.selectgoods.selection;if(0!=t.length){this.selgoodsvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},c);a.goodsid=i.id,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.itemcode=i.goodsuid,a.itemname=i.goodsname,a.itemspec=i.goodsspec,a.itemunit=i.goodsunit,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selDummy:function(t){var e=t;if(0!=e.length){this.$refs.itemGroupBtns.DummyVisible=!1;for(var i=0;i<e.length;i++){var a=e[i],o=Object.assign({},c);o.goodsid=a.id,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.itemcode=a.goodsuid,o.itemname=a.goodsname,o.itemspec=a.goodsspec,o.itemunit=a.goodsunit,o.goodscustom1=a.custom1,o.goodscustom2=a.custom2,o.goodscustom3=a.custom3,o.goodscustom4=a.custom4,o.goodscustom5=a.custom5,o.goodscustom6=a.custom6,o.goodscustom7=a.custom7,o.goodscustom8=a.custom8,o.goodscustom9=a.custom9,o.goodscustom10=a.custom10,o.price=a.outprice,o.virtualitem=1,0!=this.idx&&(o.pid=this.idx),this.lst.push(o)}}else this.$message.warning("请选择虚拟品内容")},selOrder:function(){var t=this.$refs.SelOrder.$refs.selectVal.selection;if(0!=t.length){for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},c);a.amount=i.amount,a.citeitemid=i.id,a.citeuid=i.refno,a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.itemcode=i.goodsuid,a.itemname=i.goodsname,a.itemunit=i.goodsunit,a.itemspec=i.goodsspec,a.itemtaxrate=i.itemtaxrate,a.orderitemid=i.id,a.orderuid=i.refno,a.price=i.price,a.quantity=i.quantity,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}this.OrderVisible=!1}else this.$message.warning("请选择单据内容")}}},C=O,F=(i("8559"),Object(y["a"])(C,x,k,!1,null,"af86947c",null)),P=F.exports,I=i("8ddf"),M=i("dcb4"),q={name:"Formedit",components:{FormTemp:M["a"],EditHeader:w,EditItem:P},props:["idx","isprocessDialog"],data:function(){return{title:"采购扣款",operateBar:u,processBar:f,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,assessdate:new Date,assessor:"",billamount:0,billdate:new Date,billtaxamount:0,billtaxtotal:0,billtitle:"",billtype:"采购扣款",groupid:"",groupname:"",item:[],operator:"",refno:"",summary:"",taxrate:0,tenantid:""},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:I["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData()},methods:{bindTemp:function(){var t=this;s["a"].get("/SaFormCustom/getEntityByCode?key=D03M04B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):I["a"],t.formtemplate.footer.type||(t.formtemplate.footer=I["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&s["a"].get("/D03M04B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id},autoClear:function(){this.formdata.groupname="",this.formdata.groupid=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.billamount=i.billamount,this.formdata.billtaxamount=i.billtaxamount,this.formdata.billtaxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(m,a,this.formdata),0==this.idx?n.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):n.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(e)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()}}},T=q,R=(i("e866"),Object(y["a"])(T,a,o,!1,null,"bda97c0e",null));e["a"]=R.exports},8679:function(t,e,i){},8955:function(t,e,i){"use strict";i("8679")},"8bcd":function(t,e,i){},"8da8":function(t,e,i){"use strict";i("3e09")},"8ddf":function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"采购扣款",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购扣款",value:"采购扣款"},{label:"其他扣款",value:"其他扣款"}],required:!0},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"供应商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"billtaxamount",label:"总金额:",type:"text",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},"9145c":function(t,e,i){},"941f":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{writedate:!1,formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getSuppGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,changeBillType:t.changeBillType}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px",height:"100%"}},[i("EditCash",{ref:"cashitem",style:{width:"99%",height:"其他预付"!=t.formdata.billtype?"50%":"100%"},attrs:{lstitem:t.formdata.cash,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate,itemAmount:t.itemAmount},on:{bindData:t.bindData,computerCashAmount:t.computerCashAmount}}),"其他预付"!=t.formdata.billtype?i("EditItem",{ref:"elitem",staticStyle:{width:"99%",height:"48%","margin-top":"10px"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData,computerItemAmount:t.computerItemAmount}}):t._e()],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M06B1PREEdit",commonurl:"/D03M06B1PRE/printBill",weburl:"/D03M06B1PRE/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M06B1PREEdit",examineurl:"/D03M06B1PRE/sendapprovel"}})],1)},o=[],s=i("2909"),r=(i("a9e3"),i("b64b"),i("d3b7"),i("ac1f"),i("6062"),i("3ca3"),i("5319"),i("ddb0"),i("b775"));const n={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D03M06B1PRE/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D03M06B1PRE/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){r["a"].get("/D03M06B1PRE/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,r["a"].get("/D03M06B1PRE/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M06B1PRE/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M06B1PRE/closed?type="+(3==t?1:0);r["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var l=n,d=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){return t.$emit("changeBillType")}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购预付",value:"采购预付"}}),i("el-option",{attrs:{label:"其他预付",value:"其他预付"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},["外协预付"==t.formdata.billtype||"委外预付"==t.formdata.billtype?i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"外协厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B4/getPageList",type:"外协厂商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1):i("div",[i("el-form-item",{attrs:{label:"供应厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"预付金额"}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.billamount))])])],1)],1)],1)},m=[],c={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupname:[{required:!0,trigger:"blur",message:"往来单位为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},u=c,f=(i("d8b2"),i("2877")),h=Object(f["a"])(u,d,m,!1,null,"8c70604e",null),p=h.exports,g=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh","column"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn}}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},editOption:t.editOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.deliveryvisible?i("el-dialog",{attrs:{title:"采购订单","append-to-body":!0,visible:t.deliveryvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.deliveryvisible=e}}},[i("SelDelivery",{ref:"selDelivery",attrs:{multi:1,selecturl:"/D03M02B1/getOnlinePageTh?groupid="+t.formdata.groupid+"&appl=1"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selDelivery()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.deliveryvisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},b=[],y=(i("c740"),i("caad"),i("e9c4"),i("2532"),i("c7cd"),i("159b"),{amount:0,cashaccid:"",cashaccname:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",pid:"",remark:"",rownum:0}),v={amount:0,cashaccid:"",cashaccname:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",pid:"",remark:"",rownum:0},w=i("4cf0"),x=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto","min-height":"352 px",width:"100%"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据日期",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormats")(e.row.billdate)))])]}}])}),i("el-table-column",{attrs:{label:"供应商编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupuid))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"含税金额",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtaxamount))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},k=[],S=i("333d"),D={components:{Pagination:S["a"]},props:["multi","groupid","selecturl"],data:function(){return{title:"采购订单",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M02B1/getPageTh";r["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtype:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},$=D,_=(i("5b00"),Object(f["a"])($,x,k,!1,null,"892e62f4",null)),B=_.exports,O={name:"Elitem",components:{SelDelivery:B},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{listLoading:!1,deliveryvisible:!1,lst:[],keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:w["c"],customList:[],editmarkfiles:[],countfiles:["billtaxamount","amount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=w["c"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(w["c"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=[];this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["index","amount"]);var t=this.footerData[0].amount;this.$emit("computerItemAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.formdata.groupid?this.deliveryvisible=!0:this.$message.warning("请选择往来单位")},selWaXie:function(){var t=this.$refs.selWaXie.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},y);a.invoamount=0,a.invobillcode=i.refno,a.invocode="",a.invoid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selDelivery:function(){var t=this.$refs.selDelivery.$refs.selectVal.selection;if(0!=t.length){this.deliveryvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},y);a.amount=i.billtaxamount,a.billtaxamount=i.billtaxamount,a.orderbillcode=i.refno,a.orderbillid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},C=O,F=(i("015e"),Object(f["a"])(C,g,b,!1,null,"6a248a27",null)),P=F.exports,I=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh","column"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn}}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"出纳账户","append-to-body":!0,visible:t.selordervisible,width:"1000px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.SelOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},M=[],q=i("233f"),T={name:"Elitem",components:{SelOrder:q["a"]},props:["formdata","lstitem","idx","itemAmount","formstate","formtemplateItem"],data:function(){var t=this;return{listLoading:!1,selordervisible:!1,lst:[],keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:w["a"],customList:[],editmarkfiles:[],countfiles:["amount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=w["a"];this.$getColumn(w["a"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=[];this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["cashaccname","amount"]);var t=this.footerData[0].amount;this.$emit("computerCashAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}this.multipleSelection=[],this.checkboxOption.selectedRowKeys=[]},getAdd:function(t){this.selordervisible=!0},SelOrder:function(){var t=this.$refs.SelOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},v);a.amount=this.itemAmount,a.cashaccid=i.id,a.cashaccname=i.accountname,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},R=T,N=(i("3fbc"),Object(f["a"])(R,I,M,!1,null,"da1038a0",null)),V=N.exports,E={header:{type:0,title:"付款单",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购预付",value:"采购预付"},{label:"委外预付",value:"委外预付"},{label:"外协预付",value:"外协预付"},{label:"其他预付",value:"其他预付"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"供应厂商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"billamount",label:"预付金额",type:"text",methods:"",param:""}]}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},list:{type:0,content:[]},item:{type:0,content:[]}},L=i("dcb4"),j=["id","refno","billtype","billtitle","billdate","groupid","groupid","groupuid","groupname","abbreviate","grouplevel","billamount","operator","citecode","outamount","returnuid","orguid","summary","fmdocmark","fmdoccode","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],z=["id","pid","orderbillid","orderbillcode","finishbillid","finishbillcode","billtaxamount","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],H=["id","pid","cashaccid","cashaccname","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],K={params:j,paramsItem:z,paramsCash:H},A=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]}],G=[],J={name:"Formedit",components:{FormTemp:L["a"],EditHeader:p,EditItem:P,EditCash:V},props:["idx","isDialog","initData","billcode","isprocessDialog"],data:function(){return{title:"预付款",operateBar:A,processBar:G,formdata:{abbreviate:"",billamount:0,assessdate:new Date,assessor:"",billdate:new Date,billtitle:"",billtype:"采购预付",citecode:"",createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,createdate:"",groupid:"",groupname:"",groupuid:"",item:[],cash:[],lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,operator:"",orguid:"",outamount:0,refno:"",returnuid:"",revision:0,summary:""},itemAmount:0,cashAmount:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:E,formstate:0,submitting:0}},computed:{formcontainHeight:function(){return window.innerHeight-50-13-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp(),this.bindData()},mounted:function(){this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;r["a"].get("/SaFormCustom/getEntityByCode?key=D03M06B1PRE").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):E,t.formtemplate.footer.type||(t.formtemplate.footer=E.footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&r["a"].get("/D03M06B1PRE/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1,"其他收款"==t.formdata.billtype&&t.$refs.cashitem.catchHight()):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},computerItemAmount:function(t){this.itemAmount=t},computerCashAmount:function(t){this.cashAmount=t,this.formdata.billamount=Number(this.cashAmount)},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if("其他预付"!=this.formdata.billtype){if(this.formdata.billamount!=this.itemAmount||this.formdata.billamount!=this.cashAmount)return void this.$message.warning("预付金额 与 出纳金额和单据金额不一致");this.formdata.item=this.$refs.elitem.lst,this.formdata.citecode="";for(var e="",i=0;i<this.formdata.item.length;i++){var a=this.formdata.item[i];e+=a.orderbillcode+","}var o=/,$/gi;e=e.replace(o,"");var r=e.split(","),n=Object(s["a"])(new Set(r));for(i=0;i<n.length;i++)this.formdata.citecode+=n[i]+",";this.formdata.citecode=this.formdata.citecode.replace(o,"")}this.submitting=1,this.formdata.cash=this.$refs.cashitem.lst;var d={item:[],cash:[]};d=this.$getParam(K,d,this.formdata),0==this.idx?l.add(d).then((function(e){t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0})):l.update(d).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0}))},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.delete(e)})).catch((function(){}))},approval:function(){l.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?l.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t,console.log(this.processModel,"ss")},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.item=[]},autoClear:function(t){this.formdata.groupname="",this.formdata.groupid=""},changeBillType:function(){this.formdata.item=[],this.formdata.taxamount=0,"其他预付"==this.formdata.billtype&&this.$refs.cashitem.catchHight()},billSwitch:function(t){if(console.log(this.initData),"D03M02B1"==t){this.formdata.billtype="采购预付",this.formdata.billtitle="【"+this.initData.refno+"】采购订单转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billamount=this.initData.billamount,this.formdata.citecode=this.initData.refno,this.formdata.item=[];var e={billtaxamount:this.initData.billtaxamount,amount:this.initData.billtaxamount,finishbillid:"",finishbillcode:"",orderbillcode:this.initData.refno,orderbillid:this.initData.id,remark:"",rownum:0};this.formdata.item.push(e)}}}},W=J,U=(i("af2e"),Object(f["a"])(W,a,o,!1,null,"44f6e4d4",null));e["default"]=U.exports},a485:function(t,e,i){},adfb:function(t,e,i){"use strict";i("233b")},aeac:function(t,e,i){"use strict";i("be85")},aedf:function(t,e,i){"use strict";i("cb97")},af2e:function(t,e,i){"use strict";i("d56d")},af70:function(t,e,i){"use strict";i.d(e,"c",(function(){return a})),i.d(e,"b",(function(){return o})),i.d(e,"a",(function(){return s}));var a={formcode:"D03M04B1Th",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Bus_Deduction.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deduction.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deduction.billdate"},{itemcode:"groupname",itemname:"供应商",sortable:1,minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billtaxamount",itemname:"含税金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Deduction.billtaxamount"},{itemcode:"billamount",itemname:"未税金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deduction.billamount"},{itemcode:"billtaxtotal",itemname:"税额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deduction.billtaxtotal"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,datasheet:"Bus_Deduction.status"},{itemcode:"itemcount",itemname:"款数",minwidth:"60",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deduction.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deduction.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deduction.assessor"}]},o={formcode:"D03M04B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Bus_Deduction.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deduction.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deduction.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_DeductionItem.quantity"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_DeductionItem.taxamount"},{itemcode:"orderuid",itemname:"采购订单号",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_DeductionItem.orderuid"}]},s={formcode:"D03M04B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"60",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"70",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"60",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"orderuid",itemname:"采购订单号",minwidth:"80",displaymark:1,overflow:1},{itemcode:"invoqty",itemname:"发票数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]}},b1a9:function(t,e,i){"use strict";i("3470")},b686:function(t,e,i){"use strict";i("fae8")},b83a:function(t,e,i){"use strict";i("11f6")},bc99:function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"采购开票",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购开票",value:"采购开票"},{label:"委外开票",value:"委外开票"},{label:"外协开票",value:"外协开票"},{label:"其他应付",value:"其他应付"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"外协厂商",searchtype:"factory",type:"autocomplete",methods:"",param:"",require:!0,show:"this.formdata.billtype == '外协开票' || this.formdata.billtype == '委外开票'"},{col:5,code:"groupname",label:"供应厂商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0,show:"this.formdata.billtype == '采购开票' || this.formdata.billtype == '其他应付'"},{col:5,code:"invocode",label:"发票编码",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"invodate",label:"开票日期",type:"date",methods:"computerTime",param:""},{col:5,code:"aimdate",label:"付款计划",type:"date",methods:"",param:""},{col:3,code:"taxamount",label:"发票金额",type:"input",methods:"",param:"",show:"this.formdata.billtype == '其他应付'"},{col:3,code:"taxamount",label:"发票金额",type:"input",methods:"",param:"",show:"this.formdata.billtype != '其他应付'"},{col:3,code:"paid",label:"已付金额",type:"text",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},bca8:function(t,e,i){},be85:function(t,e,i){},be8e:function(t,e,i){"use strict";i("1535")},bf19:function(t,e,i){"use strict";var a=i("23e7");a({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return URL.prototype.toString.call(this)}})},bf47:function(t,e,i){},c113:function(t,e,i){},c19f:function(t,e,i){"use strict";var a=i("23e7"),o=i("da84"),s=i("621a"),r=i("2626"),n="ArrayBuffer",l=s[n],d=o[n];a({global:!0,forced:d!==l},{ArrayBuffer:l}),r(n)},c5e2:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{directives:[{name:"show",rawName:"v-show",value:!t.FormVisible,expression:"!FormVisible"}],ref:"index",staticClass:"index"},[i("listheader",{directives:[{name:"show",rawName:"v-show",value:!t.isDialog,expression:"!isDialog"}],ref:"listheader",attrs:{tableForm:t.tableForm},on:{bindData:t.bindData,btnExport:t.btnExport,btnHelp:t.btnHelp,customTimeData:t.customTimeData}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{attrs:{span:t.showhelp?20:24}},[i("tableTh",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],ref:"tableTh",attrs:{searchVal:t.searchVal,isDialog:t.isDialog},on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}})],1),i("el-col",{attrs:{span:t.showhelp?4:0}},[i("helpmodel",{ref:"helpmodel",attrs:{code:"D01M012R1"}})],1)],1)],1)],1)])},o=[],s=(i("b0c0"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[i("div",[i("div",{staticClass:"infoForm"},[i("div",{staticClass:"infoForm-Title",staticStyle:{width:"40px"}},[t._v("仓库")]),i("el-select",{attrs:{placeholder:"请选择仓库",size:"small"},on:{change:t.changeStore},model:{value:t.groupInfo.storename,callback:function(e){t.$set(t.groupInfo,"storename",e)},expression:"groupInfo.storename"}},t._l(t.storeData,(function(t){return i("el-option",{key:t.id,attrs:{label:t.storename,value:t.id}})})),1),i("div",{staticStyle:{margin:"0 15px"}},[i("el-button-group",[i("el-button",{attrs:{type:"上一期"==t.urlType?"primary":"default",size:"small"},on:{click:function(e){t.urlType="上一期",t.getStatus()}}},[t._v("上一期")]),i("el-button",{attrs:{type:"本期"==t.urlType?"primary":"default",size:"small"},on:{click:function(e){t.urlType="本期",t.getStatus()}}},[t._v("本 期")])],1)],1)],1)]),i("div",{staticClass:"iShowBtn"},[i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:t.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(e){t.iShow=!t.iShow}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-help",title:"帮助"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)]),i("el-collapse-transition",[i("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[i("el-form",{ref:"formdata",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[i("el-row",{attrs:{gutter:20}},[i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"货品编码"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入货品编码",size:"small"},model:{value:t.formdata.goodsuid,callback:function(e){t.$set(t.formdata,"goodsuid",e)},expression:"formdata.goodsuid"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"货品名称"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入货品名称",size:"small"},model:{value:t.formdata.goodsname,callback:function(e){t.$set(t.formdata,"goodsname",e)},expression:"formdata.goodsname"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"货品规格"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入货品规格",size:"small"},model:{value:t.formdata.goodsspec,callback:function(e){t.$set(t.formdata,"goodsspec",e)},expression:"formdata.goodsspec"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单位"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入单位",size:"small"},model:{value:t.formdata.goodsunit,callback:function(e){t.$set(t.formdata,"goodsunit",e)},expression:"formdata.goodsunit"}})],1)],1),i("el-col",{attrs:{span:4}},[i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"6px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.advancedSearch()}}},[t._v(" 搜索 ")])],1)],1)],1)],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"SaDgFormat"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"选择日期","append-to-body":!0,width:"500px",visible:t.customTimeVisible},on:{"update:visible":function(e){t.customTimeVisible=e}}},[i("el-form",{ref:"customTimeForm",attrs:{"label-width":"80px"}},[i("el-form-item",{attrs:{label:"时间范围"}},[i("el-date-picker",{staticStyle:{width:"100%","margin-right":"10px"},attrs:{"unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitTime}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.customTimeVisible=!1}}},[t._v("取 消")])],1)],1)],1)}),r=[],n=(i("e9c4"),i("d3b7"),i("159b"),i("b775")),l=i("4c0c"),d=i("b893"),m={name:"Listheader",props:["searchVal","isDialog","tableForm"],components:{selectCust:l["a"]},data:function(){return{strfilter:"",iShow:!1,formdata:{},thorList:!0,setColumsVisible:!1,code:"D03M08R1DETAItem",selVisible:!1,groupInfo:{storecode:"",storeid:"",storename:""},urlType:"本期",dateRange:Object(d["d"])(),pickerOptions:Object(d["h"])(),customTimeVisible:!1,storeData:[]}},created:function(){this.getStoreData()},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(){this.iShow=!1,this.$emit("advancedSearch",this.formdata)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},bindData:function(){this.getStatus()},btnExport:function(){this.$emit("btnExport")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList)},getStoreData:function(){var t=this,e={PageNum:1,PageSize:100,OrderType:1,SearchType:1};n["a"].post("/D04M21S1/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code?t.storeData=e.data.data.list:t.$message.warning(e.data.msg||"获取仓库信息失败")}))},changeStore:function(t){var e=this;this.storeData.forEach((function(i,a){i.id==t&&(e.groupInfo.storecode=i.storecode,e.groupInfo.storename=i.storename,e.groupInfo.storeid=i.id)}))},getStatus:function(){""!=this.groupInfo.storeid?"本期"==this.urlType?this.$emit("bindData","/D04M06B1/getBillEntityByNew?key="+this.groupInfo.storeid):"上一期"==this.urlType?this.$emit("bindData","/D04M06B1/getMaxBillEntityByStore?key="+this.groupInfo.storeid):this.customTimeVisible=!0:this.$message.warning("请选择仓库")},submitTime:function(){var t={url:"/D04M06B1/getBuyAccountList?groupid="+this.groupInfo.storeid,DateRange:{StartDate:this.dateRange[0],EndDate:this.dateRange[1]}};this.$emit("customTimeData",t),this.customTimeVisible=!1}}},c=m,u=(i("adfb"),i("2877")),f=Object(u["a"])(c,s,r,!1,null,"35a9472e",null),h=f.exports,p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableTh",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading","summary-method":t.getSummaries,"show-summary":"",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight},on:{"sort-change":t.changeSort}},[i("el-table-column",{attrs:{align:"center",label:"ID",width:"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,a){return[!e.displaymark?t._e():i("el-table-column",{key:a,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(a){return["billuid"==e.itemcode?i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showInfo(a.row)}}},[t._v(t._s(a.row.billuid?a.row.billuid:"单据编号"))]):"billdate"==e.itemcode||"enddate"==e.itemcode?i("span",[t._v(t._s(t._f("dateFormat")(a.row[e.itemcode])))]):i("span",[t._v(t._s(a.row[e.itemcode]))])]}}],null,!0)})]}))],2),t.processVisible?i("el-dialog",{attrs:{width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,"append-to-body":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D03M03"==t.processRow.modulecode?i("D03M03B1",{ref:"D03M03B1",attrs:{idx:t.processRow.billid,isprocessDialog:!0},on:{closeDialog:function(e){t.processVisible=!1}}}):t._e(),"D03M04"==t.processRow.modulecode?i("D03M04B1",{ref:"D03M04B1",attrs:{idx:t.processRow.billid,isprocessDialog:!0},on:{closeDialog:function(e){t.processVisible=!1}}}):t._e(),"D03M06PRE"==t.processRow.modulecode?i("D03M06B1PRE",{ref:"D03M06B1PRE",attrs:{idx:t.processRow.billid,isprocessDialog:!0},on:{closeDialog:function(e){t.processVisible=!1}}}):t._e(),"D03M06"==t.processRow.modulecode?i("D03M06B1",{ref:"D03M06B1",attrs:{idx:t.processRow.billid,isprocessDialog:!0},on:{closeDialog:function(e){t.processVisible=!1}}}):t._e()],1):t._e()],1)},g=[],b=(i("99af"),i("d81d"),i("25f0"),i("4d90"),i("333d")),y=i("48da"),v={formcode:"D04M06R1DETAItem",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"100",displaymark:1,overflow:1},{itemcode:"openqty",itemname:"期初数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"openamount",itemname:"期初金额",minwidth:"100",displaymark:1,overflow:1},{itemcode:"inqty",itemname:"入账数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"inamount",itemname:"入账金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"outqty",itemname:"出账数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"outamount",itemname:"出账金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"closeqty",itemname:"期末数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"closeamount",itemname:"期末金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"120",displaymark:1,overflow:1}]},w=i("ed33"),x=i("85e2"),k=i("941f"),S=i("d85f"),D={components:{Pagination:b["a"],D03M03B1:w["default"],D03M04B1:x["a"],D03M06B1:S["default"],D03M06B1PRE:k["default"]},data:function(){return{lst:[],searchstr:"",total:0,listLoading:!1,idx:0,queryParams:{PageNum:1,PageSize:1e3,OrderType:1,SearchType:1},tableForm:v,processVisible:!1,processRow:{}}},computed:{tableMaxHeight:function(){return window.innerHeight-140+"px"}},created:function(){this.searchstr=""},updated:function(){var t=this;this.$nextTick((function(){t.$refs.tableTh.doLayout()}))},methods:{customTimeData:function(t){var e=this;this.lst=[],this.queryParams.DateRange=t.DateRange,n["a"].post(t.url,JSON.stringify(this.queryParams)).then((function(t){200==t.data.code?e.lst=t.data.data:e.$message.warning(t.data.msg||"查询失败")}))},bindData:function(t){var e=this;this.listLoading=!0,this.lst=[],n["a"].get(t).then((function(t){200==t.data.code?(e.lst=t.data.data.item,e.$message.success(t.data.msg)):e.$message.warning(t.data.msg||"查询失败"),e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},getColumn:function(){var t=this;n["a"].get("/SaDgFormat/getBillEntityByCode?code=D04M06R1DETAItem").then((function(e){if(200==e.data.code){if(null==e.data.data)return t.tableForm=v,void t.$emit("sendTableForm",t.tableForm);t.tableForm=e.data.data,t.$emit("sendTableForm",t.tableForm)}})).catch((function(e){t.$message.error("请求出错")}))},getSummaries:function(t){return Object(d["f"])(t,["openamount","inamount","outamount","closeamount"])},showInfo:function(t){this.processRow=t,this.processVisible=!0},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,groupname:t,billtitle:t,groupuid:t}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(t,this.tableForm),this.bindData()},showForm:function(t){this.$emit("showForm",t)},schedule:function(t,e){return t=parseFloat(t),e=parseFloat(e),isNaN(t)||isNaN(e)||e<=0?0:Math.round(t/e*1e4)/100},prformat:function(t){return"".concat(t)},btnExport:function(){var t=this;console.log("btnExports"),Promise.resolve().then(function(){for(var e=[],i=[],a=0;a<t.tableForm.item.length;a++){var o=t.tableForm.item[a];o.displaymark&&o.displaymark&&(e.push(o.itemname),i.push(o.itemcode))}var s=t.lst,r=t.formatJson(i,s);Object(y["a"])(e,r,"应收明细")}.bind(null,i)).catch(i.oe)},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))}},filters:{dateFormat:function(t){if(t){var e=new Date(t),i=e.getFullYear(),a=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0");return"".concat(i,"/").concat(a,"/").concat(o)}},dateFormats:function(t){if(t){var e=new Date(t),i=e.getFullYear(),a=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),n=e.getSeconds().toString().padStart(2,"0");return"".concat(i,"-").concat(a,"-").concat(o," ").concat(s,":").concat(r,":").concat(n)}}}},$=D,_=(i("458a"),Object(u["a"])($,p,g,!1,null,"77ae4885",null)),B=_.exports,O=i("0521"),C={name:"D03M08R1DETA",components:{listheader:h,tableTh:B,helpmodel:O["a"]},props:["searchVal","isDialog"],data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",thorList:!0,tableForm:{},showhelp:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{$route:function(t,e){"D04M06R1DETA"==t.name&&t.params.id&&this.$refs.listheader.SelCust(t.params)}},created:function(){},mounted:function(){"D04M06R1DETA"==this.$route.name&&this.$route.params.id&&this.$refs.listheader.SelCust(this.$route.params)},methods:{bindData:function(t){var e=this;this.$nextTick((function(){e.$refs.tableTh.bindData(t),e.$refs.tableTh.getColumn()}))},customTimeData:function(t){var e=this;this.$nextTick((function(){e.$refs.tableTh.customTimeData(t),e.$refs.tableTh.getColumn()}))},sendTableForm:function(t){this.tableForm=t},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},btnExport:function(){var t=this;this.$nextTick((function(){t.$refs.tableTh.btnExport()}))},advancedSearch:function(t){this.$refs.tableTh.advancedSearch(t)},showForm:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeIdx:function(t){this.idx=t}}},F=C,P=(i("aedf"),Object(u["a"])(F,a,o,!1,null,"63205c7e",null));e["default"]=P.exports},c71a:function(t,e,i){"use strict";i("ebf1")},ca99:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.paid>0&&t.formdata.paid>=t.formdata.taxamount&&0==t.formdata.closed,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,beoverdue:t.formdata.paid<t.formdata.taxamount&&(new Date).getTime()>new Date(t.formdata.aimdate).getTime()&&0==t.formdata.closed,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,computerTime:t.computerTime}})],1)]},proxy:!0},{key:"Item",fn:function(){return["其他应付"!=t.formdata.billtype?i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1):t._e()]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M05B1Edit",commonurl:"/D03M05B1/printBill",weburl:"/D03M05B1/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M05B1Edit",examineurl:"/D03M05B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},[i("D03M06B1",{ref:"D03M06B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M05B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}})],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D03M06B1"==t.processModel?i("D03M06B1List",{ref:"D03M06B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=(i("b64b"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M05B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M05B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D03M05B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D03M05B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M05B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M05B1/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=r,l=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","taxrate","taxamount","taxtotal","amount","invodate","invocode","aimdate","itemtaxrate","summary","closed","disannulmark","fmdocmark","fmdoccode","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],d=["id","pid","finishuid","finishdate","finishtype","finishitemid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","partid","billqty","quantity","taxprice","taxamount","price","amount","taxtotal","rownum","remark","machuid","machitemid","machgroupid","custpo","customer","mrpuid","mrpitemid","orderuid","orderitemid","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10","custom11","custom12","custom13","custom14","custom15","custom16","custom17","custom18"],m=["finishuid","amount","billqty","customer","custpo","partid","goodscustom1","goodscustom10","goodscustom2","goodscustom3","goodscustom4","goodscustom5","goodscustom6","goodscustom7","goodscustom8","goodscustom9","goodsid","goodsmaterial","goodsname","goodsphoto1","goodsspec","goodsuid","goodsunit","price","quantity","remark","taxamount","taxprice","taxtotal"],c={params:l,paramsItem:d,copyItem:m},u={amount:0,billqty:0,custom1:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",customer:"",custpo:"",finishdate:new Date,finishitemid:"",finishtype:"",finishuid:"",goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",id:"",intQtyMark:0,machgroupid:"",machitemid:"",machuid:"",mrpitemid:"",mrpuid:"",partid:"",pid:"",price:0,quantity:0,remark:"",revision:0,rownum:0,taxamount:0,taxprice:0,taxtotal:0},f=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,divided:!0,label:"采购付款",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D03M06B1",children:[]}],h=[{show:1,divided:!1,label:"采购付款",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D03M06B1",children:[]}],p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[],t.formdata.taxamount=0,t.formdata.groupid="",t.formdata.groupname=""}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购开票",value:"采购开票"}}),i("el-option",{attrs:{label:"其他应付",value:"其他应付"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[i("el-form-item",{attrs:{label:"单据主题",prop:"billtitle"}},[i("el-input",{attrs:{placeholder:"请输入单据主题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)])],1),i("el-row",["外协开票"==t.formdata.billtype||"委外开票"==t.formdata.billtype?i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"外协厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B4/getPageList",type:"外协厂商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]):i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"供应厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("invocode")}}},[i("el-form-item",{attrs:{label:"发票编码"}},[i("el-input",{attrs:{placeholder:"请输入发票编码",clearable:"",size:"small"},model:{value:t.formdata.invocode,callback:function(e){t.$set(t.formdata,"invocode",e)},expression:"formdata.invocode"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("invodate")}}},[i("el-form-item",{attrs:{label:"开票日期",prop:"invodate"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期",size:"small"},on:{change:function(e){return t.$emit("computerTime")}},model:{value:t.formdata.invodate,callback:function(e){t.$set(t.formdata,"invodate",e)},expression:"formdata.invodate"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"付款计划"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.aimdate,callback:function(e){t.$set(t.formdata,"aimdate",e)},expression:"formdata.aimdate"}})],1)],1),i("el-col",{attrs:{span:9}},[i("div",{staticStyle:{display:"flex"}},[i("el-form-item",{attrs:{label:"发票金额"}},["其他应付"==t.formdata.billtype?i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入发票金额",size:"small"},model:{value:t.formdata.taxamount,callback:function(e){t.$set(t.formdata,"taxamount",e)},expression:"formdata.taxamount"}}):i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v(" ￥"+t._s(t.formdata.taxamount?t.formdata.taxamount:0))])],1),i("el-form-item",{attrs:{label:"已付金额"}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.paid?t.formdata.paid:0))])])],1)])],1)],1)},g=[],b={props:{formdata:{type:Object},title:{type:String}},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"往来单位为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},y=b,v=(i("b1a9"),i("2877")),w=Object(v["a"])(y,p,g,!1,null,"a883edd0",null),x=w.exports,k=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","copyrow","refresh"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"left",fn:function(){return[i("el-button",{attrs:{type:"primary",size:"mini",disabled:2==t.formstate},nativeOn:{click:function(e){return t.getSelKouKuan(1)}}},[i("i",{staticClass:"el-icon-remove-outline"}),t._v(" 扣款")])]},proxy:!0},{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.acceptFormVisible?i("el-dialog",{attrs:{title:"采购验收单","append-to-body":!0,visible:t.acceptFormVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.acceptFormVisible=e}}},[i("SelAccept",{ref:"selAccept",attrs:{multi:t.multi,selecturl:"/D03M03R1/getOnlineInvoPageList?groupid="+t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selAccept()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.acceptFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.KouKuanVisible?i("el-dialog",{attrs:{title:"扣款单","append-to-body":!0,visible:t.KouKuanVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.KouKuanVisible=e}}},[i("SelKouKuan",{ref:"selKouKuan",attrs:{multi:1,selecturl:"/D03M04B1/getOnlinePageList?groupid="+t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selKouKuan()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.KouKuanVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},S=[],D=i("c7eb"),$=i("1da1"),_=(i("c740"),i("caad"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),i("da86")),B=i("9a2c"),O=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),i("el-table-column",{attrs:{label:"金额",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.taxamount))])]}}])}),i("el-table-column",{attrs:{label:"采购订单号",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.orderuid))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},C=[],F=i("333d"),P={components:{Pagination:F["a"]},props:["multi","selecturl"],data:function(){return{title:"采购扣款",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M04B1/getPageList";s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},I=P,M=(i("c71a"),Object(v["a"])(I,O,C,!1,null,"3edf162c",null)),q=M.exports,T={name:"Elitem",components:{SelAccept:B["a"],SelKouKuan:q},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],multi:0,keynum:0,acceptFormVisible:!1,setColumsVisible:!1,KouKuanVisible:!1,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,index:0,tableForm:_["b"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate","billqty"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;t.editmarkfiles.includes(a.field)&&t.countfiles.includes(a.field)&&t.changeInput("",i,a.field)}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[s],Object.keys(o)[1])}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object($["a"])(Object(D["a"])().mark((function e(){var i;return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=_["b"],t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn(_["b"].formcode,i).then((function(e){t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,operationColumn:!!t.operationmark,renderBodyCell:function(e,i){var a=e.row;e.column,e.rowIndex;if("goodsuid"==t.itemcode){var o=i("GoodsInfo",{class:a.disannulmark?"textlinethrough":"",attrs:{scopeIndex:a.rownum,scopeVal:a[t.itemcode]}});return o}o=i("span",{class:a.disannulmark?"textlinethrough":""},[a[t.itemcode]]);return o}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1,this.$forceUpdate()},getSummary:function(){this.$getSummary(this,["goodsuid","amount","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.changeInput("",this.lst[e+o],n)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){if("itemtaxrate"==i)this.nowitemtaxrate=e.itemtaxrate;else if("quantity"==i&&e.quantity>e.billqty)return this.$message.warning("本次数量不能大于单据数量"),void(e.quantity=e.billqty);e=this.$countInput(e,i),this.getSummary()},getAdd:function(t){this.formdata.groupid?(this.acceptFormVisible=!0,this.multi=t):this.$message.warning("请先选择往来单位")},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"closed",0),this.$set(e,"finishqty",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},selAccept:function(){var t=this.$refs.selAccept.$refs.selectVal.selection;if(0!=t.length){this.acceptFormVisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.price=i.price,a.amount=i.amount,a.quantity=i.quantity,a.taxprice=i.taxprice,a.taxamount=i.taxamount,a.itemtaxrate=i.itemtaxrate,a.billqty=i.quantity,a.taxtotal=i.taxtotal,a.customer=i.customer,a.custpo=i.custpo,a.finishitemid=i.id,a.finishtype=i.billtype,a.finishuid=i.refno,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.orderuid=i.orderuid,a.orderitemid=i.orderitemid,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},getSelKouKuan:function(){this.formdata.groupid?this.KouKuanVisible=!0:this.$message.warning("请先选择供应商")},selKouKuan:function(){var t=this.$refs.selKouKuan.$refs.selectVal.selection;if(0!=t.length){this.KouKuanVisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=Number(0-i.amount),a.billqty=i.quantity,a.customer=i.customer,a.custpo=i.custpo,a.finishitemid=i.id,a.finishtype=i.billtype,a.finishuid=i.refno,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.price=Number(0-i.price),a.quantity=i.quantity,a.taxamount=Number(0-i.taxamount),a.taxprice=Number(0-i.taxprice),a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},R=T,N=(i("3949"),Object(v["a"])(R,k,S,!1,null,"4f9eee22",null)),V=N.exports,E=i("bc99"),L=i("dcb4"),j=i("d85f"),z=i("501c"),H={name:"Formedit",components:{FormTemp:L["a"],EditHeader:x,EditItem:V,D03M06B1:j["default"],D03M06B1List:z["a"]},props:["idx","isDialog","initData","billcode"],data:function(){return{title:"采购开票",operateBar:f,processBar:h,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,aimdate:new Date,amount:0,assessdate:new Date,assessor:"",billdate:new Date,billtitle:"",billtype:"采购开票",closed:0,disannulmark:0,groupid:"",groupname:"",invocode:"",invodate:new Date,item:[],paid:0,refno:"",statecode:"",statedate:new Date,summary:"",taxamount:0,taxrate:0,taxtotal:0},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",creditdquantity:0,creditduint:"day",formtemplate:E["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;s["a"].get("/SaFormCustom/getEntityByCode?key=D03M05B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):E["a"],t.formtemplate.footer.type||(t.formtemplate.footer=E["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&s["a"].get("/D03M05B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.creditdquantity=t.creditdquantity,this.creditduint=t.creditduint,this.computerTime()},autoClear:function(){this.formdata.groupname="",this.formdata.groupid=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if("其他应付"!=this.formdata.billtype){if(0==this.$refs.elitem.lst.length)return void this.$message.warning("单据内容不能为空");for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.amount=i.billamount,this.formdata.taxamount=i.billtaxamount,this.formdata.taxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(c,a,this.formdata)}else a=Object.assign({},this.formdata);0==this.idx?n.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):n.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(t)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},computerTime:function(){if(0!=this.creditdquantity){var t=this.formdata.invodate.getTime();"month"==this.creditduint?t+=2592e6*this.creditdquantity:t+=864e5*this.creditdquantity,this.formdata.aimdate=new Date(t)}},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t},processBill:function(t){this.processVisible=!0,this.processTitle="采购付款",this.processModel=t},billSwitch:function(t){if(console.log(this.initData,"123456"),"D03M03B1"==t){this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】采购验收转入",this.formdata.item=[];for(var e=0;e<this.initData.item.length;e++){var i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.billqty=i.quantity,a.price=i.price,a.amount=i.amount,a.quantity=i.quantity,a.taxprice=i.taxprice,a.taxamount=i.taxamount,a.itemtaxrate=i.itemtaxrate,a.taxtotal=i.taxtotal,a.finishuid=this.initData.refno,a.finishitemid=i.id,a.finishtype=this.initData.billtype,a.orderuid=i.orderuid,a.orderitemid=i.orderitemid,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.customer=i.customer,a.custpo=i.custpo,a.virtualitem=i.virtualitem,this.formdata.item.push(a)}}else"D08M04B1PN"==t&&(this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】支出计划转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.suppliers,this.formdata.groupuid=this.initData.groupuid,this.formdata.linkman=this.initData.linkman,this.formdata.telephone=this.initData.telephone,this.formdata.billtype="其他应付",this.formdata.invocode=this.initData.invocode,this.formdata.aimdate=this.initData.aimdate,this.formdata.taxamount=this.initData.taxamount,this.formdata.receipted=this.initData.finishamt,this.formdata.item=[])}}},K=H,A=(i("0377"),Object(v["a"])(K,a,o,!1,null,"c851054e",null));e["default"]=A.exports},cb97:function(t,e,i){},cc06:function(t,e,i){"use strict";i("3218")},cdcd:function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r}));var a={formcode:"D03M03B1Th",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Finishing.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billtaxamount",itemname:"含税金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billtaxamount"},{itemcode:"billamount",itemname:"未税金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Finishing.billamount"},{itemcode:"billtaxtotal",itemname:"税额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billtaxtotal"},{itemcode:"arrivaladd",itemname:"收货地址",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Finishing.arrivaladd"},{itemcode:"transport",itemname:"物流方式",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Finishing.transport"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_Finishing.status"},{itemcode:"itemcount",itemname:"款数",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.assessor"}]},o={formcode:"D03M03B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Buy_Finishing.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"orderno",itemname:"订单编号",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_FinishingItem.orderno"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.taxamount"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.quantity"},{itemcode:"finishqty",itemname:"已入库",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"50",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.status"}]},s={formcode:"D03M03B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"60",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"70",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"60",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"finishqty",itemname:"已出入库数",minwidth:"80",displaymark:1,overflow:1},{itemcode:"orderno",itemname:"订单编号",minwidth:"100",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"150",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D03M03B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Buy_Finishing.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"orderno",itemname:"订单编号",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_FinishingItem.orderno"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.taxamount"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.quantity"},{itemcode:"finishqty",itemname:"已入库",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"50",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.status"}]}},ce51:function(t,e,i){"use strict";i("bf47")},d56d:function(t,e,i){},d85f:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{writedate:!1,formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getSuppGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,changeBillType:t.changeBillType}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px",height:"100%"}},[i("EditCash",{ref:"cashitem",style:{width:"99%",height:"其他付款"!=t.formdata.billtype?"50%":"100%"},attrs:{lstitem:t.formdata.cash,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate,itemAmount:t.itemAmount},on:{bindData:t.bindData,computerCashAmount:t.computerCashAmount}}),"其他付款"!=t.formdata.billtype?i("EditItem",{ref:"elitem",staticStyle:{width:"99%",height:"48%","margin-top":"10px"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData,computerItemAmount:t.computerItemAmount}}):t._e()],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M06B1Edit",commonurl:"/D03M06B1/printBill",weburl:"/D03M06B1/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M06B1Edit",examineurl:"/D03M06B1/sendapprovel"}}),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D03M06B1"==t.processModel?i("D03M06B1List",{ref:"D03M06B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0,isHongchong:!0}}):t._e()],1):t._e()],1)},o=[],s=i("2909"),r=(i("e9c4"),i("a9e3"),i("b64b"),i("d3b7"),i("ac1f"),i("6062"),i("3ca3"),i("5319"),i("159b"),i("ddb0"),i("b775"));const n={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D03M06B1/create",a).then(t=>{console.log(t),200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D03M06B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){r["a"].get("/D03M06B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,r["a"].get("/D03M06B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M06B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M06B1/closed?type="+(3==t?1:0);r["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var l=n,d=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){return t.$emit("changeBillType")}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购付款",value:"采购付款"}}),i("el-option",{attrs:{label:"单据付款",value:"单据付款"}}),i("el-option",{attrs:{label:"其他付款",value:"其他付款"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},["外协付款"==t.formdata.billtype?i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"外协厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B4/getPageList",type:"外协厂商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1):i("div",[i("el-form-item",{attrs:{label:"供应厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"付款金额："}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.billamount))])])],1)],1)],1)},m=[],c={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupname:[{required:!0,trigger:"blur",message:"客户为必填项"}],operator:[{required:!0,trigger:"blur",message:"经办人为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},u=c,f=(i("aeac"),i("2877")),h=Object(f["a"])(u,d,m,!1,null,"0ccea106",null),p=h.exports,g=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},editOption:t.editOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"采购发票","append-to-body":!0,visible:t.selordervisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"selOrder",attrs:{multi:1,selecturl:"/D03M05B1/getOnlinePageTh?groupid="+t.formdata.groupid+"&appl=1"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.deliveryvisible?i("el-dialog",{attrs:{title:"收货单","append-to-body":!0,visible:t.deliveryvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.deliveryvisible=e}}},[i("SelDelivery",{ref:"selDelivery",attrs:{multi:1,selecturl:"/D03M03R1/getOnlinePaidPageTh?groupid="+t.formdata.groupid+"&appl=1"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selDelivery()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.deliveryvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},b=[],y=i("ade3"),v=(i("c740"),i("caad"),i("2532"),i("c7cd"),{amount:0,custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",invoamount:0,invobillcode:"",invocode:"",invoid:"",pid:"",remark:"",rownum:0}),w={amount:0,cashaccid:"",cashaccname:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",pid:"",remark:"",rownum:0},x=i("3d5d"),k=i("dd56"),S=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto","min-height":"352 px",width:"100%"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据日期",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormats")(e.row.billdate)))])]}}])}),i("el-table-column",{attrs:{label:"供应商编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupuid))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"含税金额",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtaxamount))])]}}])}),i("el-table-column",{attrs:{label:"已付金额",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billpaid))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},D=[],$=i("333d"),_={components:{Pagination:$["a"]},props:["multi","groupid","selecturl"],data:function(){return{title:"采购订单",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M03B1/getPageList";r["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtype:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},B=_,O=(i("e79b"),Object(f["a"])(B,S,D,!1,null,"c89161e8",null)),C=O.exports,F={name:"Elitem",components:{SelOrder:k["a"],SelDelivery:C},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return Object(y["a"])(Object(y["a"])(Object(y["a"])(Object(y["a"])(Object(y["a"])(Object(y["a"])(Object(y["a"])({listLoading:!1,selordervisible:!1,deliveryvisible:!1,setColumsVisible:!1,lst:[],keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:x["d"],customList:[],editmarkfiles:[],countfiles:["amount","invoamount"],columsData:[],columnHidden:[],footerData:[],copyText:""},"columnHidden",[]),"checkboxOption",{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}}),"editOption",{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}}),"rowScroll",0),"virtualScrollOption",{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}}),"cellAutofillOption",{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}}),"clipboardOption",{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}})},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=x["d"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(x["d"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=[];this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["index","amount"]);var t=this.footerData[0].amount;this.$emit("computerItemAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.formdata.groupid?"采购付款"==this.formdata.billtype?this.selordervisible=!0:this.deliveryvisible=!0:this.$message.warning("请选择往来单位")},selOrder:function(){var t=this.$refs.selOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},v);a.amount=this.$fomatFloat(i.taxamount-i.paid,2),a.invoamount=i.taxamount,a.invobillcode=i.refno,a.invocode=i.invocode,a.invoid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selWaXie:function(){var t=this.$refs.selWaXie.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},v);a.invoamount=0,a.invobillcode=i.refno,a.invocode="",a.invoid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selDelivery:function(){var t=this.$refs.selDelivery.$refs.selectVal.selection;if(0!=t.length){this.deliveryvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},v);"采购退货"==i.billtype||"其他退货"==i.billtype?a.amount=this.$fomatFloat(0-i.billtaxamount-i.billpaid,2):a.amount=this.$fomatFloat(i.billtaxamount-i.billpaid,2),a.invoamount=i.billtaxamount,a.invobillcode=i.refno,a.invoid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},P=F,I=(i("eb4d"),Object(f["a"])(P,g,b,!1,null,"0a1b9eaa",null)),M=I.exports,q=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},editOption:t.editOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"出纳账户","append-to-body":!0,visible:t.selordervisible,width:"1000px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.SelOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},T=[],R=i("233f"),N={name:"Elitem",components:{SelOrder:R["a"]},props:["formdata","lstitem","idx","itemAmount","formstate","formtemplateItem"],data:function(){var t=this;return{listLoading:!1,selordervisible:!1,setColumsVisible:!1,lst:[],keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:x["b"],customList:[],editmarkfiles:[],countfiles:["amount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(column.field)&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=x["b"];this.$getColumn(x["b"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=[];this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["cashaccname","amount"]);var t=this.footerData[0].amount;this.$emit("computerCashAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.selordervisible=!0},SelOrder:function(){var t=this.$refs.SelOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},w);a.amount=this.itemAmount,a.cashaccid=i.id,a.cashaccname=i.accountname,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},V=N,E=(i("6c68"),Object(f["a"])(V,q,T,!1,null,"7a7021c1",null)),L=E.exports,j={header:{type:0,title:"付款单",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购付款",value:"采购付款"},{label:"单据付款",value:"单据付款"},{label:"外协付款",value:"外协付款"},{label:"其他付款",value:"其他付款"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"供应厂商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"billamount",label:"付款金额：",type:"text",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},list:{type:0,content:[]},item:{type:0,content:[]}},z=i("dcb4"),H=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","billamount","operator","citecode","returnuid","orguid","summary","fmdocmark","fmdoccode","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],K=["id","pid","invoid","invobillcode","invocode","invoamount","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],A=["id","pid","cashaccid","cashaccname","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],G={params:H,paramsItem:K,paramsCash:A},J=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,divided:!0,label:"红 冲",icon:"el-icon-edit-outline",disabled:"this.formstate==0?true:(!!this.formdata.orguid? true : !!this.formdata.returnuid)",methods:"hongChong",param:"",children:[]}],W=[{show:1,divided:!1,label:"红冲记录",icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D01M08B1",label:"红冲记录"},children:[]}],U={name:"Formedit",components:{FormTemp:z["a"],EditHeader:p,EditItem:M,EditCash:L,D03M06B1List:function(){return Promise.all([i.e("chunk-commons"),i.e("chunk-0f9ed869")]).then(i.bind(null,"9b2e"))}},props:["idx","isDialog","initData","billcode","isprocessDialog"],data:function(){return{title:"付款单",operateBar:J,processBar:W,formdata:{abbreviate:"",billamount:0,assessdate:new Date,assessor:"",billdate:new Date,billtitle:"",billtype:"采购付款",citecode:"",createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,createdate:"",groupid:"",groupname:"",groupuid:"",item:[],cash:[],lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,operator:"",orguid:"",outamount:0,refno:"",returnuid:"",revision:0,summary:""},itemAmount:0,cashAmount:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:j,formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;r["a"].get("/SaFormCustom/getEntityByCode?key=D03M06B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):j,t.formtemplate.footer.type||(t.formtemplate.footer=j.footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&r["a"].get("/D03M06B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1,"其他收款"==t.formdata.billtype&&t.$refs.cashitem.catchHight()):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},computerItemAmount:function(t){this.itemAmount=t},computerCashAmount:function(t){this.cashAmount=t,this.formdata.billamount=Number(this.cashAmount)},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if("其他付款"!=this.formdata.billtype){if(this.formdata.billamount!=this.itemAmount||this.formdata.billamount!=this.cashAmount)return void this.$message.warning("付款金额 与 出纳金额和发票的金额不一致");this.formdata.item=this.$refs.elitem.lst,this.formdata.citecode="";for(var e="",i=0;i<this.formdata.item.length;i++){var a=this.formdata.item[i];e+=a.invobillcode+","}var o=/,$/gi;e=e.replace(o,"");var r=e.split(","),n=Object(s["a"])(new Set(r));for(i=0;i<n.length;i++)this.formdata.citecode+=n[i]+",";this.formdata.citecode=this.formdata.citecode.replace(o,"")}this.submitting=1,this.formdata.cash=this.$refs.cashitem.lst;var d={item:[],cash:[]};d=this.$getParam(G,d,this.formdata),0==this.idx?l.add(d).then((function(e){t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0})):l.update(d).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0}))},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.delete(e)})).catch((function(){}))},approval:function(){l.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?l.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t,console.log(this.processModel,"ss")},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.item=[],this.formdata.cash=[]},autoClear:function(t){this.formdata.groupname="",this.formdata.groupid=""},changeBillType:function(){this.formdata.cash=[],this.formdata.item=[],this.formdata.taxamount=0,this.$refs.cashitem.catchHight()},hongChong:function(){var t=this;console.log("红冲"),this.$confirm("红冲后不可退回，是否确认将该单据转为红冲单？","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((function(){var e=Object.assign({},t.formdata);e.billtype="付款红冲",e.billtitle="【"+t.formdata.refno+"】付款单转入",e.orguid=t.formdata.refno,e.billamount=0-t.formdata.billamount,e.outamount=0,e.cash.forEach((function(e,i){e.amount=0-t.formdata.cash[i].amount})),e.item.forEach((function(e,i){e.amount=0-t.formdata.item[i].amount})),e.billdate=new Date,t.$delete(e,"refno"),t.$delete(e,"assessor"),t.$delete(e,"assessorid"),t.$delete(e,"assessdate"),t.$delete(e,"lister"),t.$delete(e,"listerid"),t.$delete(e,"createby"),t.$delete(e,"createbyid"),t.$delete(e,"createdate"),t.$delete(e,"modifydate"),t.$delete(e,"operator"),r["a"].post("/D03M06B1/create",JSON.stringify(e)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"红冲成功"),t.$emit("changeIdx",e.data.data.id),t.$emit("bindData"),t.bindData()):t.$message.warning(e.data.msg||"红冲失败")})).catch((function(e){t.$message.warning(e||"红冲失败")}))})).catch((function(){}))},billSwitch:function(t){if("D03M05B1"==t){this.formdata.billtype="采购付款",this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】采购开票转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billamount=this.initData.billamount,this.formdata.citecode=this.initData.refno,this.formdata.item=[];var e={invoid:this.initData.id,invobillcode:this.initData.refno,invocode:this.initData.invocode,invoamount:this.initData.taxamount,amount:this.$fomatFloat(this.initData.taxamount-this.initData.paid,2)};this.formdata.item.push(e)}}}},X=U,Y=(i("b686"),Object(f["a"])(X,a,o,!1,null,"72ef1b58",null));e["default"]=Y.exports},d8b2:function(t,e,i){"use strict";i("4d45")},d93a:function(t,e,i){"use strict";i("6e4b")},da86:function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r}));var a={formcode:"D03M05B1Th",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Bus_Invoice.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Invoice.billdate"},{itemcode:"groupuid",itemname:"供应商编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"invocode",itemname:"发票编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.invocode"},{itemcode:"aimdate",itemname:"付款计划",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Invoice.aimdate"},{itemcode:"taxrate",itemname:"税率%",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Invoice.taxrate"},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.taxamount"},{itemcode:"paid",itemname:"已付金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.paid"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.status"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.assessor"}]},o={formcode:"D03M05B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Bus_Invoice.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"finishuid",itemname:"收货单号",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_InvoiceItem.finishuid"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_InvoiceItem.quantity"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.taxamount"}]},s={formcode:"D03M05B1Item",item:[{itemcode:"finishuid",itemname:"收货单号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"billqty",itemname:"单据数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"本次数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"price",itemname:"未税单价",minwidth:"70",displaymark:1,overflow:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",displaymark:1,overflow:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"150",displaymark:1,overflow:1}]},r={formcode:"D03M05B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Bus_Invoice.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"finishuid",itemname:"收货单号",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_InvoiceItem.finishuid"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_InvoiceItem.quantity"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.taxamount"}]}},dd56:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtitle))])]}}])}),i("el-table-column",{attrs:{label:"发票编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.invocode))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"税率%",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.taxrate))])]}}])}),i("el-table-column",{attrs:{label:"含税金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.taxamount)+" ")]}}])}),i("el-table-column",{attrs:{label:"已付金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.paid)+" ")]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],s=(i("99af"),i("e9c4"),i("d3b7"),i("25f0"),i("4d90"),i("b775")),r=i("333d"),n={components:{Pagination:r["a"]},filters:{dateFormat:function(t){var e=new Date(t),i=e.getFullYear(),a=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0");return"".concat(i,"/").concat(a,"/").concat(o)}},props:["multi","selecturl"],data:function(){return{title:"采购开票",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){console.log(t),this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M05B1/getPageTh";s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},l=n,d=(i("ce51"),i("2877")),m=Object(d["a"])(l,a,o,!1,null,"c300cc12",null);e["a"]=m.exports},dea7:function(t,e,i){},e79b:function(t,e,i){"use strict";i("bca8")},e866:function(t,e,i){"use strict";i("9145c")},eb4d:function(t,e,i){"use strict";i("c113")},ebf1:function(t,e,i){},ed33:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getSuppGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M03B1Edit",commonurl:"/D03M03B1/printBill",weburl:"/D03M03B1/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M03B1Edit",examineurl:"/D03M03B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},["D04M01B1"==t.processModel?i("D04M01B1",{ref:"D04M01B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M03B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e(),"D03M05B1"==t.processModel?i("D03M05B1",{ref:"D03M05B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M03B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e()],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D04M01B1"==t.processModel?i("D04M01B1List",{ref:"D04M01B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e(),"D03M05B1"==t.processModel?i("D03M05B1List",{ref:"D03M05B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=(i("b64b"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M03B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M03B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D03M03B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D03M03B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M03B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M03B1/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=r,l=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","operator","arrivaladd","transport","summary","billtaxamount","billtaxtotal","billamount","custom1","custom3","custom2","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],d=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","partid","itemcode","itemname","itemspec","itemunit","quantity","taxprice","taxamount","taxtotal","itemtaxrate","price","amount","remark","orderno","orderuid","orderitemid","closed","rownum","invoclosed","virtualitem","customer","custpo","location","batchno","machuid","machitemid","machgroupid","mainplanuid","mainplanitemid","mrpuid","mrpitemid","disannulmark","attributejson","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],m={params:l,paramsItem:d},c={amount:0,attributejson:"",batchno:"",closed:0,custom1:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",customer:"",custpo:"",deliqty:0,disannuldate:new Date,disannullister:"",disannullisterid:"",disannulmark:0,finishqty:0,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",id:"",inspectid:"",inspectuid:"",intQtyMark:0,invoclosed:0,invoqty:0,itemcode:"",itemname:"",itemspec:"",itemtaxrate:0,itemunit:"",location:"",machgroupid:"",machitemid:"",machuid:"",mainplanitemid:"",mainplanuid:"",mrpitemid:"",mrpuid:"",orderitemid:"",orderno:"",orderuid:"",partid:"",passedqty:0,pid:"",price:0,quantity:0,remark:"",revision:0,rownum:0,sourcetype:0,statecode:"",statedate:new Date,taxamount:0,taxprice:0,taxtotal:0,virtualitem:0},u=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,divided:!0,label:"出入库单",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D04M01B1",children:[]},{show:1,divided:!1,label:"采购开票",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D03M05B1",children:[]}],f=[{show:1,divided:!1,label:"出入库单",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D04M01B1",children:[]},{show:1,divided:!1,label:"采购开票",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D03M05B1",children:[]}],h=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[]}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购验收",value:"采购验收"}}),i("el-option",{attrs:{label:"采购退货",value:"采购退货"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据主题"}},[i("el-input",{attrs:{placeholder:"请输入单据主题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"供应厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("arrivaladd")}}},[i("el-form-item",{attrs:{label:"收货地址"}},[i("el-input",{attrs:{placeholder:"请输入收货地址",clearable:"",size:"small"},model:{value:t.formdata.arrivaladd,callback:function(e){t.$set(t.formdata,"arrivaladd",e)},expression:"formdata.arrivaladd"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("transport")}}},[i("el-form-item",{attrs:{label:"物流方式"}},[i("el-input",{attrs:{placeholder:"请输入物流方式",clearable:"",size:"small"},model:{value:t.formdata.transport,callback:function(e){t.$set(t.formdata,"transport",e)},expression:"formdata.transport"}})],1)],1)])],1)],1)},p=[],g={props:{formdata:{type:Object},title:{type:String}},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"供应商为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},b=g,y=(i("8955"),i("2877")),v=Object(y["a"])(b,h,p,!1,null,"8fa2c892",null),w=v.exports,x=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","dummy","moveup","movedown","delete","copyrow","refresh","billstate"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection,dummyurl:"/D91M01B1/getVirOnlinePageList"},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,selDummy:t.selDummy,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selbuyordervisible?i("el-dialog",{attrs:{title:"采购订单","append-to-body":!0,visible:t.selbuyordervisible,width:"1000px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selbuyordervisible=e}}},[i("SelBuyOrder",{ref:"selBuyOrder",attrs:{multi:t.multi,selecturl:t.selecturl,groupid:t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selBuyOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selbuyordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},k=[],S=i("c7eb"),D=i("1da1"),$=i("ade3"),_=(i("c740"),i("caad"),i("d81d"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),i("da92")),B=i("cdcd"),O=i("65e3"),C={name:"Elitem",components:{SelBuyOrder:O["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return Object($["a"])(Object($["a"])(Object($["a"])({lst:[],multi:0,keynum:0,selbuyordervisible:!1,setColumsVisible:!1,selecturl:"",tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:B["b"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;if(t.editmarkfiles.includes(a.field)){t.countfiles.includes(a.field)&&t.changeInput("",i,a.field);var o=t.customList.findIndex((function(t){return t.attrkey==a.field}));-1!=o&&t.setAttributeJson(i,i.rownum)}}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}}},"footerData",[]),"cellAutofillOption",{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));if(-1!=s){t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[s],Object.keys(o)[1]);var r=t.customList.findIndex((function(t){return t.attrkey==Object.keys(o)[1]}));-1!=r&&t.setAttributeJson(t.lst[s],s)}}}}),"clipboardOption",{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}})},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var s=JSON.parse(t[i].attributejson);for(a=0;a<s.length;a++)t[i][s[a].key]=s[a].value}}this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(D["a"])(Object(S["a"])().mark((function e(){var i;return Object(S["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=B["b"],t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn(B["b"].formcode,i,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,operationColumn:!!t.operationmark,renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex),r="",n=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));if(-1!=n){var l=e.customList[n].valuejson?e.customList[n].valuejson.split(","):[];return r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]])]),a("div",{directives:[{name:"show",value:l.length}]},[a("div",{style:"width:0;height:0"},[a("el-select",{class:"wpselect",style:"width:0;height:0;opacity: 0;",attrs:{size:"small",id:t.itemcode+o.rownum+B["b"].formcode},on:{change:function(i){o[t.itemcode]=i,e.setAttributeJson(o,o.rownum)}},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}},[l.map((function(t,e){return a("el-option",{key:e,attrs:{label:t,value:t},style:"max-width:200px"})}))])]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-arrow-down",style:"font-weight:bold;font-size:14px;",on:{click:function(){document.getElementById(t.itemcode+o.rownum+B["b"].formcode).click()}}})])]),r}return"goodsuid"==t.itemcode?(r=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}}),r):"plandate"==t.itemcode?(r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[e.$options.filters.dateFormat(o[t.itemcode])])]),a("div",[a("div",{style:"opacity: 0;width:0;height:0"},[a("el-date-picker",{style:"width:0;height:0",attrs:{size:"small",id:t.itemcode+s+B["b"].formcode,value:new Date(o[t.itemcode]),type:"date",format:"MM-dd","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},on:{input:function(e){o[t.itemcode]=e}}})]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-date",on:{click:function(){document.getElementById(t.itemcode+s+B["b"].formcode).focus()}}})])]),r):(r=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]),r)}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["goodsuid","amount","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.changeInput("",this.lst[e+o],n);var l=this.customList.findIndex((function(t){return t.attrkey==n}));-1!=l&&this.setAttributeJson(this.lst[e+o],e+o)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],s={key:o.attrkey,value:t[o.attrkey]?t[o.attrkey].replace(/\s*/g,""):""};""!=s.value&&i.push(s)}0==i.length?this.lst[e].attributejson="":this.lst[e].attributejson=JSON.stringify(i),this.$forceUpdate()},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i),this.getSummary()},getAdd:function(t){this.formdata.groupid?("采购验收"==this.formdata.billtype?this.selecturl="/D03M02B1/getOnlineFinishPageList?groupid="+this.formdata.groupid+"&appl=1":this.selecturl="/D03M02B1/getRetrunFinishPageList?groupid="+this.formdata.groupid+"&appl=1",this.selbuyordervisible=!0,this.multi=t):this.$message.warning("请选择供应商")},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"closed",0),this.$set(e,"finishqty",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},selBuyOrder:function(){var t=this.$refs.selBuyOrder.$refs.selectVal.selection;if(0!=t.length){this.selbuyordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e];i.itemtaxrate&&(this.nowitemtaxrate=t[e].itemtaxrate);var a=Object.assign({},c);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.amount=i.amount,a.attributejson=i.attributejson,a.batchno=i.batchno,a.itemtaxrate=i.itemtaxrate,a.orderitemid=i.id,a.orderuid=i.refno,a.orderno=i.refno,a.price=i.price,a.quantity=_["a"].minus(i.quantity,i.finishqty),a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selDummy:function(t){var e=t;if(0!=e.length){this.$refs.itemGroupBtns.DummyVisible=!1;for(var i=0;i<e.length;i++){var a=e[i],o=Object.assign({},c);o.goodsid=a.id,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.goodscustom1=a.custom1,o.goodscustom2=a.custom2,o.goodscustom3=a.custom3,o.goodscustom4=a.custom4,o.goodscustom5=a.custom5,o.goodscustom6=a.custom6,o.goodscustom7=a.custom7,o.goodscustom8=a.custom8,o.goodscustom9=a.custom9,o.goodscustom10=a.custom10,o.price=a.inprice?a.inprice:0,o.itemtaxrate=a.taxrate?a.taxrate:0,o.quantity=1,o.virtualitem=1,0!=this.idx&&(o.pid=this.idx),this.changeInput("",o,"quantity"),this.lst.push(o)}}else this.$message.warning("请选择虚拟品内容")}}},F=C,P=(i("cc06"),Object(y["a"])(F,x,k,!1,null,"5fc99a69",null)),I=P.exports,M=i("1f11"),q=i("dcb4"),T=i("ca99"),R=i("13df"),N=i("27f6"),V=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},E=[],L=(i("3ca3"),i("ddb0"),i("da86")),j=i("b893"),z={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"ca99"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:L["a"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={finishuid:this.searchVal};if(this.queryParams.SearchPojo=e,this.online)var i="/D03M05B1/getOnlinePageList";else i="/D03M05B1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(j["d"])()[0],EndDate:Object(j["d"])()[1]}),s["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++){var a=t.lst[i];a.compcost=a.quantity-a.buyqty;for(var o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(D["a"])(Object(S["a"])().mark((function e(){return Object(S["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(L["a"].formcode,L["a"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=a("goodsInfo",{attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}});return s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","amount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},H=z,K=(i("d93a"),Object(y["a"])(H,V,E,!1,null,"0fc3ac8a",null)),A=K.exports,G={name:"Formedit",components:{FormTemp:q["a"],EditHeader:w,EditItem:I,D03M05B1:T["default"],D04M01B1:R["default"],D04M01B1List:N["a"],D03M05B1List:A},props:["idx","isDialog","initData","billcode","isprocessDialog"],data:function(){return{title:"采购验收",operateBar:u,processBar:f,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,arrivaladd:"",assessdate:new Date,assessor:"",billamount:0,billdate:new Date,billpaid:0,billstatecode:"",billstatedate:new Date,billtaxamount:0,billtaxtotal:0,billtitle:"",billtype:"采购验收",disannulmark:0,groupid:"",groupname:"",item:[],operator:"",refno:"",summary:"",transport:""},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:M["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D03M03B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):M["a"],t.formtemplate.footer.type||(t.formtemplate.footer=M["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx?s["a"].get("/D03M03B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.$refs.elitem.multipleSelection=[],t.$refs.elitem.checkboxOption.selectedRowKeys=[],t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")})):this.formdata.item=[]},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.item=[]},autoClear:function(){this.formdata.groupname="",this.formdata.groupid=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.billamount=i.billamount,this.formdata.billtaxamount=i.billtaxamount,this.formdata.billtaxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(m,a,this.formdata),0==this.idx?n.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.formdata=e.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):n.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.formdata=e.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(t)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){console.log(t,"vallll"),this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t;var e=this.$refs.elitem.multipleSelection;"D04M01B1"!==t&&"D03M05B1"!==t||e.length>0&&(this.formdata.item=e)},processBill:function(t){this.processVisible=!0,this.processTitle="D04M01B1"==t?"出入库单":"采购开票",this.processModel=t},billSwitch:function(t){console.log(this.initData);var e=this.$store.getters.userinfo.configs;if("D03M02B1"==t){this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】采购订单转入",this.formdata.abbreviate=this.initData.abbreviate,this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.arrivaladd=this.initData.arrivaladd,this.formdata.transport=this.initData.transport,this.formdata.amount=this.initData.amount,this.formdata.item=[];for(var i=0;i<this.initData.item.length;i++){var a=this.initData.item[i],o=Object.assign({},c);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.machgroupid=a.machgroupid,o.machitemid=a.machitemid,o.machuid=a.machuid,o.mrpitemid=a.mrpitemid,o.mrpuid=a.mrpuid,o.mainplanitemid=a.mainplanitemid,o.mainplanuid=a.mainplanuid,o.quantity=this.$fomatFloat(a.quantity-a.finishqty,2),o.price=a.price,o.amount=a.amount,o.taxprice=a.taxprice,o.taxamount=a.taxamount,o.taxtotal=a.taxtotal,o.itemtaxrate=a.itemtaxrate,o.customer=a.customer,o.custpo=a.custpo,o.orderitemid=a.id,o.orderno=this.initData.refno,o.orderuid=this.initData.refno,o.attributejson=a.attributejson,o.virtualitem=a.virtualitem,o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):(this.$refs.elitem.changeInput("",o,"quantity"),this.formdata.item.push(o))}}else if("D03M02B1List"==t){this.formdata.billtitle=this.initData[0].billtitle?this.initData[0].billtitle:"【"+this.initData[0].refno+"】采购订单转入",this.formdata.abbreviate=this.initData[0].abbreviate,this.formdata.groupid=this.initData[0].groupid,this.formdata.groupname=this.initData[0].groupname,this.formdata.groupuid=this.initData[0].groupuid,this.formdata.arrivaladd=this.initData[0].arrivaladd,this.formdata.transport=this.initData[0].transport,this.formdata.item=[];for(i=0;i<this.initData.length;i++){a=this.initData[i],o=Object.assign({},c);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.machgroupid=a.machgroupid,o.machitemid=a.machitemid,o.machuid=a.machuid,o.mrpitemid=a.mrpitemid,o.mrpuid=a.mrpuid,o.mainplanitemid=a.mainplanitemid,o.mainplanuid=a.mainplanuid,o.quantity=this.$fomatFloat(a.quantity-a.finishqty,2),o.price=a.price,o.amount=a.amount,o.taxprice=a.taxprice,o.taxamount=a.taxamount,o.taxtotal=a.taxtotal,o.itemtaxrate=a.itemtaxrate,o.customer=a.customer,o.custpo=a.custpo,o.orderitemid=a.id,o.orderno=a.refno,o.orderuid=a.refno,o.attributejson=a.attributejson,o.virtualitem=a.virtualitem,o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):(this.$refs.elitem.changeInput("",o,"quantity"),this.formdata.item.push(o))}}}}},J=G,W=(i("be8e"),Object(y["a"])(J,a,o,!1,null,"7255f33c",null));e["default"]=W.exports},fae8:function(t,e,i){}}]);