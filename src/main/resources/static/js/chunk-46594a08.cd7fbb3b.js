(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-46594a08"],{"037a":function(t,e,a){"use strict";a("420b")},"06cb":function(t,e,a){"use strict";a("1896")},1896:function(t,e,a){},"1b00":function(t,e,a){"use strict";a("de2f")},"420b":function(t,e,a){},4973:function(t,e,a){"use strict";a("f748")},"5c73":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{},[a("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?a("ul",{},t._l(t.lst,(function(e,i){return a("li",{key:i,class:t.radio==i?"active":"",on:{click:function(a){t.getCurrentRow(e),t.radio=i}}},[a("span",[t._v(t._s(e.dictvalue))])])})),0):a("div",{staticClass:"noData"},[t._v("暂无数据")])]),a("div",{staticClass:"foots"},[a("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),a("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?a("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"left"},t._l(t.lst,(function(e,i){return a("p",{key:i,class:t.ActiveIndex==i?"isActive":"",on:{click:function(e){t.ActiveIndex=i}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),a("div",{staticClass:"right"},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},o=[],r=(a("a434"),a("e9c4"),a("b775")),s=a("333d"),n=a("b0b8"),l={components:{Pagination:s["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],r["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var a=0;a<t.formdata.item.length;a++)t.lst.push(t.formdata.item[a])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.addItem(a)})).catch((function(t){console.log(t)}))},addItem:function(t){n.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:n.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.lst[t.ActiveIndex].dictvalue=a,n.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=n.getFullChars(a)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,r["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=l,d=(a("af2b"),a("2877")),m=Object(d["a"])(c,i,o,!1,null,"d2ba3d7a",null);e["a"]=m.exports},"676d":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.formvisible?a("div",{staticClass:"formedit"},[a("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[a("ListHeader",{attrs:{tableForm:t.tableForm},on:{bindData:t.bindData,btnAdd:function(e){return t.showForm(0)},btnSearch:function(e){return t.$refs.tableList.search(e)},advancedSearch:function(e){return t.$refs.tableList.advancedSearch(e)},btnExport:function(e){return t.$refs.tableList.btnExport()},bindColumn:function(e){return t.$refs.tableList.getColumn()}}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("TableList",{ref:"tableList",on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}})],1)],1)],1)],1)])},o=[],r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setcolumsvisible=!0}}})],1)]),t.setcolumsvisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setcolumsvisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setcolumsvisible=e}}},[a("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setcolumsvisible=!1}}})],1):t._e(),a("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchvisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchvisible=e}}},[a("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchvisible=!1},bindData:t.bindData}})],1)],1)},s=[],n={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",setcolumsvisible:!1,searchvisible:!1}},methods:{btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},advancedSearch:function(t){this.$emit("advancedSearch",t),this.searchvisible=!1},openSearchForm:function(){var t=this;this.searchvisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnExport:function(){this.$emit("btnExport")},bindData:function(){this.$emit("bindData")}}},l=n,c=(a("87ec"),a("2877")),d=Object(c["a"])(l,r,s,!1,null,"6fb1dcb4",null),m=d.exports,u=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["save","operate"],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,closeForm:t.closeForm,clickMethods:t.clickMethods}})],1)]),a("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate,selectform:t.selectform},on:{clickMethods:t.clickMethods},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[a("div",{staticClass:"form form-head p-r"},[a("EditHeader",{ref:"formHeader",attrs:{title:t.formtemplate.header.title?t.formtemplate.header.title:t.title,formdata:t.formdata},on:{writeCode:t.writeCode}})],1)]},proxy:!0}],null,!0)})],1)]),a("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D01M01B3Edit",commonurl:"/D01M01B3/printBill",weburl:"/D01M01B3/printWebBill"}})],1)},f=[],h=(a("e9c4"),a("b64b"),a("b775"));const p={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/D91M01S2/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/D91M01S2/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){h["a"].get("/D91M01S2/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})}};var b=p,v=["id","attrgroupid","attrkey","attrname","valuetype","defvalue","valuejson","listshow","enabledmark","skumark","remark","rownum","numericmark","requiredmark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],g={params:v},y=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]}],w=[],x=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("attrname")}}},[a("el-form-item",{attrs:{label:"属性名称",prop:"attrname"}},[a("el-input",{attrs:{placeholder:"属性名称",clearable:"",size:"small"},on:{input:function(e){return t.$emit("writeCode",t.formdata.attrname)}},model:{value:t.formdata.attrname,callback:function(e){t.$set(t.formdata,"attrname",e)},expression:"formdata.attrname"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("attrkey")}}},[a("el-form-item",{attrs:{label:"属性Key",prop:"attrkey"}},[a("el-input",{attrs:{placeholder:"属性Key",clearable:"",size:"small"},model:{value:t.formdata.attrkey,callback:function(e){t.$set(t.formdata,"attrkey",e)},expression:"formdata.attrkey"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"数值类型"}},[a("el-input",{attrs:{placeholder:"数值类型",size:"small"},model:{value:t.formdata.valuetype,callback:function(e){t.$set(t.formdata,"valuetype",e)},expression:"formdata.valuetype"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"默认值"}},[a("el-input",{attrs:{placeholder:"默认值",size:"small"},model:{value:t.formdata.defvalue,callback:function(e){t.$set(t.formdata,"defvalue",e)},expression:"formdata.defvalue"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:""}},[a("div",{staticStyle:{display:"flex"}},[a("el-checkbox",{attrs:{label:"列表显示","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.listshow,callback:function(e){t.$set(t.formdata,"listshow",e)},expression:"formdata.listshow"}}),a("el-checkbox",{attrs:{label:"SKU","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.skumark,callback:function(e){t.$set(t.formdata,"skumark",e)},expression:"formdata.skumark"}}),a("el-checkbox",{attrs:{label:"数字","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.numericmark,callback:function(e){t.$set(t.formdata,"numericmark",e)},expression:"formdata.numericmark"}}),a("el-checkbox",{attrs:{label:"必填","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.requiredmark,callback:function(e){t.$set(t.formdata,"requiredmark",e)},expression:"formdata.requiredmark"}})],1)])],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{staticStyle:{"margin-top":"10px"},attrs:{label:"可择值","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入可择值",clearable:"",size:"small",type:"textarea",rows:3},model:{value:t.formdata.valuejson,callback:function(e){t.$set(t.formdata,"valuejson",e)},expression:"formdata.valuejson"}})],1),a("div",{staticStyle:{color:"#fa7753","font-size":"12px",margin:"-10px 0 6px 100px"}},[t._v(' 注1：输入可择值时，注意要用英文 "," 号分隔！ '),a("br"),t._v(" 注2: SKU会影响库存数量，谨慎勾选 ")])],1)],1)],1)},k=[],S={props:{formdata:{type:Object},title:{type:String}},data:function(){return{formRules:{attrkey:[{required:!0,trigger:"blur",message:"属性key为必填项"}],attrname:[{required:!0,trigger:"blur",message:"属性名称为必填项"}]}}},created:function(){},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},$=S,C=(a("06cb"),Object(c["a"])($,x,k,!1,null,"268eea82",null)),F=C.exports,D={header:{type:0,title:"SPU属性表",content:[{type:"form",rowitem:[{col:5,code:"attrname",label:"属性名称",type:"input",methods:"writeCode",param:"this.formdata.attrname",required:!0},{col:5,code:"attrkey",label:"属性Key",type:"input",methods:"",param:"",required:!0}]},{rowitem:[{col:5,code:"valuetype",label:"数值类型",type:"input",methods:"",param:"",required:!0},{col:5,code:"defvalue",label:"默认值",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"rownum",label:"排序",type:"number",methods:"",param:""},{col:5,code:"listshow",label:"列表显示",type:"checkbox",methods:"",param:""},{col:2,code:"skumark",label:"SKU",type:"checkbox",methods:"",param:""},{col:2,code:"numericmark",label:"数字",type:"checkbox",methods:"",param:""},{col:2,code:"requiredmark",label:"必填",type:"checkbox",methods:"",param:""}]},{rowitem:[{col:12,code:"valuejson",label:"可择值",type:"textarea",methods:"",param:""}]}]},item:{type:0,content:[]},footer:{type:0,content:[{type:"divider",center:"left",label:""},{rowitem:[{col:22,code:"summary",label:"摘要",type:"input",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"}]}]}},P=a("dcb4"),_=a("b0b8"),I={name:"Formedit",components:{EditHeader:F,FormTemp:P["a"]},props:["idx"],data:function(){return{title:"SPU属性表",operateBar:y,processBar:w,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,attrgroupid:"",attrkey:"",attrname:"",defvalue:"",enabledmark:1,listshow:1,remark:"",skumark:0,revision:0,rownum:0,valuejson:"",valuetype:"",numericmark:"",requiredmark:""},formstate:0,submitting:0,formtemplate:D,selectform:[]}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp(),this.bindData()},mounted:function(){},methods:{bindTemp:function(){this.formtemplate=D},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&h["a"].get("/D91M01S2/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.id?1:0):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},bindTreeData:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};this.$request.post("/D01M02S1/getGroupPageList",JSON.stringify(e)).then((function(e){if(200==e.data.code){t.selectform=[];var a=e.data.data.list;if(a.length){for(var i={code:"partgroupid",data:[]},o=0;o<a.length;o++){var r={label:a[o].grpname,value:a[o].id};i.data.push(r)}t.selectform.push(i)}console.log(t.selectform,"ss")}}))},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.submitting=1;var e={};e=this.$getParam(g,e,this.formdata),0==this.idx?b.add(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.id?1:0)})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0})):b.update(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.id?1:0)})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0}))},clickMethods:function(t){this[t.meth](t.param)},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){b.delete(e)})).catch((function(){}))},writeCode:function(t){_.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.attrkey="spu"+_.getFullChars(t)}}},B=I,O=(a("037a"),Object(c["a"])(B,u,f,!1,null,"4545f5ea",null)),z=O.exports,T=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("div",{staticClass:"flex a-c"},[a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1),a("div",{staticStyle:{"margin-right":"40px"}},[a("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)])],1)},M=[],q=a("c7eb"),L=a("1da1"),E=(a("a9e3"),a("d3b7"),a("c7cd"),a("159b"),{formcode:"D01M02S1List",item:[{itemcode:"attrkey",itemname:"属性Key",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",datasheet:"Bus_CostPart.attrkey"},{itemcode:"attrname",itemname:"属性名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_CostPart.attrname"},{itemcode:"valuetype",itemname:"数值类型",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_CostPart.valuetype"},{itemcode:"valuejson",itemname:"默认值",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_CostPart.valuejson"},{itemcode:"baseprice",itemname:"可择值",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_CostPart.baseprice"},{itemcode:"listshow",itemname:"列表显示",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_CostPart.listshow"},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_CostPart.remark"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_CostPart.lister"}]}),A={components:{},props:["online"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:E,customList:[],selectList:[],totalfields:["refno"],exportitle:"spu",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var a=e.selectedRowKeys;if(t.selectList=[],t.checkboxOption.selectedRowKeys=a,0!=a.length)for(var i=0;i<a.length;i++)t.selectList.push({id:a[i]})},selectedAllChange:function(e){var a=e.isSelected,i=e.selectedRowKeys;if(a){if(t.checkboxOption.selectedRowKeys=i,0!=i.length)for(var o=0;o<i.length;o++)t.selectList.push({id:i[o]})}else t.selectList=[],t.checkboxOption.selectedRowKeys=[]}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var a=e.startRowIndex;t.rowScroll=a}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var a=this.tableForm.item[e];a.displaymark&&(t+=Number(a.minwidth))}}return t}},methods:{bindData:function(){var t=this;this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1));var e="/D91M01S2/getPageList";h["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(L["a"])(Object(q["a"])().mark((function e(){return Object(q["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(t.tableForm.formcode,E).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,a=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,i){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(a,i){var o=a.row;a.column,a.rowIndex;if("createdate"==t.itemcode||"modifydate"==t.itemcode)return e.$options.filters.dateFormats(o[t.itemcode]);if("attrkey"==t.itemcode){var r=i("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"属性Key"]);return r}if("listshow"==t.itemcode){r=null;return r=1==o.listshow?i("el-tag",{attrs:{size:"small"}},["显示"]):i("el-tag",{attrs:{size:"small",type:"warning"}},["隐藏"]),r}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),a.push(o)})),a.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,a){t.row,t.column;var i=t.rowIndex;return i+e.rowScroll+1}}),this.customData=a,this.keynum+=1},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,a=["baseprice","rebate","price","pricemin","pricemax"];this.$countCellData(this,a,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t)},advancedSearch:function(t){this.$advancedSearch(this,t)},changeSort:function(t){for(var e in t)if(""!=t[e]){var a={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(a,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)},groupsearch:function(t){""!=t?this.queryParams.SearchPojo={partgroupid:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},j=A,N=(a("1b00"),Object(c["a"])(j,T,M,!1,null,"4b035516",null)),H=N.exports,K={name:"D91M01S2",components:{TableList:H,ListHeader:m,FormEdit:z},data:function(){return{idx:0,formvisible:!1,tableForm:{},showhelp:!1}},watch:{},mounted:function(){this.bindData(),this.$refs.tableList.getColumn()},methods:{bindData:function(){this.$refs.tableList.bindData()},sendTableForm:function(t){this.tableForm=t},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t}}},R=K,V=(a("4973"),Object(c["a"])(R,i,o,!1,null,"81e99920",null));e["default"]=V.exports},"744c":function(t,e,a){},"7de4":function(t,e,a){},"87ec":function(t,e,a){"use strict";a("744c")},af2b:function(t,e,a){"use strict";a("7de4")},de2f:function(t,e,a){},f748:function(t,e,a){}}]);