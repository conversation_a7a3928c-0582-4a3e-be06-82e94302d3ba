(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-539c3462"],{"0239":function(t,e,a){},"030e":function(t,e,a){"use strict";a("7bb8")},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"1e8d":function(t,e,a){},"2b8e":function(t,e,a){},"3a54":function(t,e,a){},"5c44":function(t,e,a){},"5c73":function(t,e,a){"use strict";var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{},[a("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?a("ul",{},t._l(t.lst,(function(e,o){return a("li",{key:o,class:t.radio==o?"active":"",on:{click:function(a){t.getCurrentRow(e),t.radio=o}}},[a("span",[t._v(t._s(e.dictvalue))])])})),0):a("div",{staticClass:"noData"},[t._v("暂无数据")])]),a("div",{staticClass:"foots"},[a("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),a("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?a("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"left"},t._l(t.lst,(function(e,o){return a("p",{key:o,class:t.ActiveIndex==o?"isActive":"",on:{click:function(e){t.ActiveIndex=o}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),a("div",{staticClass:"right"},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},i=[],s=(a("a434"),a("e9c4"),a("b775")),r=a("333d"),n=a("b0b8"),l={components:{Pagination:r["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],s["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var a=0;a<t.formdata.item.length;a++)t.lst.push(t.formdata.item[a])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.addItem(a)})).catch((function(t){console.log(t)}))},addItem:function(t){n.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:n.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.lst[t.ActiveIndex].dictvalue=a,n.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=n.getFullChars(a)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,s["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=l,d=(a("af2b"),a("2877")),u=Object(d["a"])(c,o,i,!1,null,"d2ba3d7a",null);e["a"]=u.exports},"5df0":function(t,e,a){"use strict";a("5c44")},6456:function(t,e,a){"use strict";a("686a")},"686a":function(t,e,a){},"773b":function(t,e,a){"use strict";a("0239")},"7bb8":function(t,e,a){},"7de4":function(t,e,a){},"841c":function(t,e,a){"use strict";var o=a("d784"),i=a("825a"),s=a("1d80"),r=a("129f"),n=a("14c3");o("search",1,(function(t,e,a){return[function(e){var a=s(this),o=void 0==e?void 0:e[t];return void 0!==o?o.call(e,a):new RegExp(e)[t](String(a))},function(t){var o=a(e,t,this);if(o.done)return o.value;var s=i(t),l=String(this),c=s.lastIndex;r(c,0)||(s.lastIndex=0);var d=n(s,l);return r(s.lastIndex,c)||(s.lastIndex=c),null===d?-1:d.index}]}))},8885:function(t,e,a){},"9bda":function(t,e,a){"use strict";var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")]),a("div",{staticStyle:{float:"right"}},[a("el-radio-group",{attrs:{size:"small"},on:{change:function(e){return t.getStatus()}},model:{value:t.goodsType,callback:function(e){t.goodsType=e},expression:"goodsType"}},[a("el-radio-button",{attrs:{label:"物料"}}),a("el-radio-button",{attrs:{label:"半成品"}}),a("el-radio-button",{attrs:{label:"成品"}})],1)],1)],1),a("div",{staticStyle:{"margin-bottom":"10px"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectgoods",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",height:"380px",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsuid))])]}}])}),a("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsname))])]}}])}),a("el-table-column",{attrs:{label:"货品规格",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsspec))])]}}])}),a("el-table-column",{attrs:{label:"货品状态",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsstate))])]}}])}),a("el-table-column",{attrs:{label:"单位",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsunit))])]}}])}),a("el-table-column",{attrs:{label:"外部编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.partid))])]}}])}),a("el-table-column",{attrs:{label:"价格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.outprice))])]}}])}),a("el-table-column",{attrs:{label:"当前库存",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.ivquantity))])]}}])})],1)],1),a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},i=[],s=(a("e9c4"),a("b775")),r=a("333d"),n={components:{Pagination:r["a"]},props:["multi","groupid","goodsstate"],data:function(){return{title:"货品信息",listLoading:!1,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},goodsType:"成品",goodsVal:"p"}},created:function(){this.searchstr="",this.multi&&this.bindData()},methods:{getCurrentRow:function(t){console.log(t),this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.groupid){var e={groupid:this.groupid};this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e}if(this.goodsstate){e={goodsstate:this.goodsstate};this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e}s["a"].post("/D91M01B1/getOnlinePageList?state="+this.goodsVal,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={goodsuid:t,goodsname:t,goodsunit:t,groupid:t,goodsspec:t,partid:t,goodsstate:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},getStatus:function(){this.strfilter="",this.$delete(this.queryParams,"SearchPojo"),"成品"==this.goodsType?this.goodsVal="p":"半成品"==this.goodsType?this.goodsVal="s":"物料"==this.goodsType&&(this.goodsVal="m"),this.bindData()},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},l=n,c=(a("ee58"),a("2877")),d=Object(c["a"])(l,o,i,!1,null,"b16f8594",null);e["a"]=d.exports},a535:function(t,e,a){"use strict";a("2b8e")},a55b:function(t,e,a){},aad1:function(t,e,a){t.exports=a.p+"static/img/noFace.855a718f.jpg"},af2b:function(t,e,a){"use strict";a("7de4")},b82e:function(t,e,a){"use strict";a("3a54")},cc2f:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.formvisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[a("ListHeader",{attrs:{showTree:t.treevisble,tableForm:t.tableForm,passkey:t.passkey},on:{btnAdd:function(e){return t.showForm(0)},btnshowGroup:function(e){t.treevisble=!t.treevisble},bindData:t.bindData,btnImport:t.btnImport,Export:t.Export,pagePrint:function(e){return t.$refs.tableList.pagePrint()},btnPrint:function(e){return t.$refs.tableList.btnPrint()},btnSearch:function(e){return t.$refs.tableList.search(e)},advancedSearch:function(e){return t.$refs.tableList.advancedSearch(e)},allDelete:function(e){return t.$refs.tableList.allDelete()},btnExport:function(e){return t.$refs.tableList.btnExport()},bindColumn:function(e){return t.$refs.tableList.getColumn()}}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{directives:[{name:"show",rawName:"v-show",value:t.treevisble,expression:"treevisble"}],attrs:{span:4}},[a("div",{staticStyle:{"font-size":"14px",color:"#606266"}},[a("div",{staticClass:"groupTitle"},[a("span",[t._v("分组")]),a("i",{staticClass:"el-icon-s-tools",style:{color:t.treeeditable?"#1e80ff":""},on:{click:t.treeEdit}})]),a("el-tree",{staticStyle:{"font-size":"14px",height:"80vh",overflow:"auto",width:"100%"},attrs:{data:t.groupData,"node-key":"id","default-expand-all":""},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.node,i=e.data;return a("span",{staticClass:"custom-tree-node"},[a("span",{on:{click:function(){return t.handleNodeClick(i)}}},[t._v(t._s(o.label)+" "),i.prefix?a("a",{attrs:{href:"javascript:;"}},[t._v("["+t._s(i.prefix)+"]")]):t._e()]),a("span",{directives:[{name:"show",rawName:"v-show",value:t.treeeditable,expression:"treeeditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==i.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-edit"},on:{click:function(){return t.editTreeNode(i)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:t.treeeditable,expression:"treeeditable"}]},[a("el-button",{staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-circle-plus-outline"},on:{click:function(){return t.addTreeChild(i)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:t.treeeditable,expression:"treeeditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==i.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-delete-solid"},on:{click:function(){return t.delTreeNode(i)}}})],1)])}}])})],1)]),a("el-col",{attrs:{span:20}},[a("TableList",{ref:"tableList",attrs:{formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}})],1)],1)],1)],1),t.gropuformvisible?a("el-dialog",{attrs:{title:"货品分类","append-to-body":!0,visible:t.gropuformvisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.gropuformvisible=e}}},[a("FormAdd",t._g({ref:"formadd",attrs:{idx:t.idx,pid:t.pid}},{compForm:t.compForm,closeForm:t.closeForm,bindData:t.bindTreeData}))],1):t._e(),t.exportvisible?a("el-dialog",{attrs:{title:"货品导入",visible:t.exportvisible,width:"60%",top:"4vh"},on:{"update:visible":function(e){t.exportvisible=e}}},[a("Export",{ref:"exportFile",on:{closeDialog:function(e){t.exportvisible=!1},bindData:t.bindData}})],1):t._e(),t.exportInfoVisble?a("el-dialog",{attrs:{title:"批量导出",visible:t.exportInfoVisble,width:"400px"},on:{"update:visible":function(e){t.exportInfoVisble=e}}},[a("div",{staticClass:"export-content"},[a("div",{staticStyle:{"margin-bottom":"10px",display:"flex","align-items":"center","justify-content":"space-around"}},[a("span",[t._v("页码")]),a("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"small",min:1},model:{value:t.exportInfo.PageNum,callback:function(e){t.$set(t.exportInfo,"PageNum",e)},expression:"exportInfo.PageNum"}})],1),a("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-around"}},[a("span",[t._v("数量")]),a("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"small",min:1,step:100},model:{value:t.exportInfo.PageSize,callback:function(e){t.$set(t.exportInfo,"PageSize",e)},expression:"exportInfo.PageSize"}})],1)]),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.submitExport}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.exportInfoVisble=!1}}},[t._v("取 消")])],1)]):t._e()],1)},i=[],s=a("2909"),r=(a("99af"),a("d81d"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("3ca3"),a("841c"),a("ddb0"),a("2b3d"),a("9861"),a("b775")),n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["save","print","operate","process"],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1)]),a("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate,selectform:t.selectform},on:{clickMethods:t.clickMethods},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[a("div",{staticClass:"form form-head p-r"},[a("EditHeader",{ref:"formHeader",attrs:{title:t.formtemplate.header.title?t.formtemplate.header.title:t.title,formdata:t.formdata,storelist:t.storelist,storeOptions:t.storeOptions,groupnameOptions:t.groupnameOptions,customDataList:t.customDataList},on:{handleChangeGroupid:t.handleChangeGroupid,getpingyin:t.getpingyin,setGroupRow:t.setGroupRow,changeStore:t.changeStore,setStoreList:function(e){t.storelist=e},selStoreList:t.selStoreList,openImgInfo:function(e){t.ImgInfoVisible=!0}}})],1)]},proxy:!0}],null,!0)})],1)]),a("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D91M01B1Edit",commonurl:"/D91M01B/printBill",weburl:"/D91M01B/printWebBill"}}),t.SupplierVisible?a("el-dialog",{attrs:{title:"供应商报价","append-to-body":!0,visible:t.SupplierVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.SupplierVisible=e}}},[a("Supplier",{ref:"supplier",attrs:{idx:t.idx,multi:1}})],1):t._e(),t.historyGoodsVisible?a("el-dialog",{attrs:{title:"历史货品",visible:t.historyGoodsVisible,width:"860px",top:"5vh","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.historyGoodsVisible=e}}},[a("SelGoods",{ref:"selGoods",attrs:{multi:0}}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitselGoods}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.historyGoodsVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},l=[];a("4de4"),a("c740"),a("a434"),a("5319"),a("159b");const c={add(t){return console.log(t),new Promise((e,a)=>{var o=JSON.stringify(t);r["a"].post("/D91M01B1/create",o).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var o=JSON.stringify(t);r["a"].post("/D91M01B1/update",o).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){r["a"].get("/D91M01B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},checkGoodsUid(t){return new Promise((e,a)=>{var o=JSON.stringify(t);r["a"].post("/D91M01B1/getEntityBynsp",o).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},importEntity(t){return new Promise((e,a)=>{var o=JSON.stringify(t);r["a"].post("/D91M01B1/importEntity",o).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var d=c,u=["id","goodsuid","goodsname","goodsspec","goodsunit","goodsstate","goodspinyin","versionnum","material","surface","barcode","safestock","inprice","outprice","groupid","groupname","fileguid","drawing","taxrate","storeid","storelistname","storelistguid","ivquantity","ageprice","uidgroupguid","uidgroupcode","uidgroupname","uidgroupnum","partid","pid","puid","enabledmark","goodsphoto1","goodsphoto2","remark","batchmg","batchonly","skumark","packsnmark","virtualitem","bomid","quickcode","brandname","buyremqty","wkwsremqty","wkscremqty","busremqty","mrpremqty","requremqty","alertsqty","intqtymark","weightqty","weightunit","lengthqty","lengthunit","areaqty","areaunit","volumeqty","volumeunit","packqty","packunit","matqtyunit","overflowqty","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],m={params:u},p=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[o("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),o("el-divider",{attrs:{"content-position":"left"}},[t._v("编码规则")]),o("el-row",[o("el-col",{attrs:{span:4}},[o("div",{on:{click:function(e){return t.cleValidate("uidgroupguid")}}},[o("el-form-item",{attrs:{label:"分组名称",prop:"uidgroupguid"}},[o("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{options:t.groupnameOptions,props:t.defaultProps,"show-all-levels":!1,placeholder:"请选择分组名称",size:"small"},on:{change:function(e){return t.$emit("handleChangeGroupid",e)}},model:{value:t.formdata.uidgroupguid,callback:function(e){t.$set(t.formdata,"uidgroupguid",e)},expression:"formdata.uidgroupguid"}})],1)],1)]),o("el-col",{attrs:{span:4}},[o("div",{on:{click:function(e){return t.cleValidate("goodsuid")}}},[o("el-form-item",{attrs:{label:"货品编码",prop:"goodsuid"}},[o("el-input",{attrs:{placeholder:"请输入货品编码",clearable:"",size:"small"},model:{value:t.formdata.goodsuid,callback:function(e){t.$set(t.formdata,"goodsuid",e)},expression:"formdata.goodsuid"}})],1)],1)]),o("el-col",{attrs:{span:4}},[o("div",{on:{click:function(e){return t.cleValidate("taxrate")}}},[o("el-form-item",{attrs:{label:"默认税率",prop:"taxrate"}},[o("el-input",{attrs:{placeholder:"请输入默认税率",size:"small"},model:{value:t.formdata.taxrate,callback:function(e){t.$set(t.formdata,"taxrate",e)},expression:"formdata.taxrate"}})],1)],1)])],1),o("el-divider",{attrs:{"content-position":"left"}},[t._v("货品信息")]),o("el-row",[o("el-col",{attrs:{span:6}},[o("div",{ref:"colRefs",on:{click:function(e){return t.cleValidate("goodsname")}}},[o("el-form-item",{attrs:{label:"货品名称",prop:"goodsname"}},[o("el-popover",{ref:"goodsnamedict",staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click",width:t.eleWidth},on:{show:function(e){return t.$refs.goodsnameRef.bindData()}}},[o("SelDict",{ref:"goodsnameRef",attrs:{multi:0,billcode:"goods.goodsname",baseparam:"/SaDict"},on:{singleSel:function(e){t.formdata.goodsname=e.dictvalue,t.$refs["goodsnamedict"].doClose(),t.cleValidate("goodsname")},closedic:function(e){return t.$refs["goodsnamedict"].doClose()}}}),o("div",{attrs:{slot:"reference"},slot:"reference"},[o("el-input",{attrs:{placeholder:"请选择货品规格",clearable:"",size:"small"},model:{value:t.formdata.goodsname,callback:function(e){t.$set(t.formdata,"goodsname",e)},expression:"formdata.goodsname"}})],1)],1)],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("goodsspec")}}},[o("el-form-item",{attrs:{label:"规格描述",prop:"goodsspec"}},[o("el-popover",{ref:"goodsspecdict",staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click",width:t.eleWidth},on:{show:function(e){return t.$refs.goodsspecRef.bindData()}}},[o("SelDict",{ref:"goodsspecRef",attrs:{multi:0,billcode:"goods.goodsspec",baseparam:"/SaDict"},on:{singleSel:function(e){t.formdata.goodsspec=e.dictvalue,t.$refs["goodsspecdict"].doClose(),t.cleValidate("goodsspec")},closedic:function(e){return t.$refs["goodsspecdict"].doClose()}}}),o("div",{attrs:{slot:"reference"},slot:"reference"},[o("el-input",{attrs:{placeholder:"请选择货品规格",clearable:"",size:"small"},model:{value:t.formdata.goodsspec,callback:function(e){t.$set(t.formdata,"goodsspec",e)},expression:"formdata.goodsspec"}})],1)],1)],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("goodsunit")}}},[o("el-form-item",{attrs:{label:"货品单位",prop:"goodsunit"}},[o("el-popover",{ref:"goodsunitdict",staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click",width:t.eleWidth},on:{show:function(e){return t.$refs.goodsunitRef.bindData()}}},[o("SelDict",{ref:"goodsunitRef",attrs:{multi:0,billcode:"goods.goodsunit",baseparam:"/SaDict"},on:{singleSel:function(e){t.formdata.goodsunit=e.dictvalue,t.$refs["goodsunitdict"].doClose(),t.cleValidate("goodsunit")},closedic:function(e){return t.$refs["goodsunitdict"].doClose()}}}),o("div",{attrs:{slot:"reference"},slot:"reference"},[o("el-input",{attrs:{placeholder:"请选择货品单位",clearable:"",size:"small"},model:{value:t.formdata.goodsunit,callback:function(e){t.$set(t.formdata,"goodsunit",e)},expression:"formdata.goodsunit"}})],1)],1)],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("goodsstate")}}},[o("el-form-item",{attrs:{label:"货品状态",prop:"goodsstate"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择状态",size:"small",clearable:""},model:{value:t.formdata.goodsstate,callback:function(e){t.$set(t.formdata,"goodsstate",e)},expression:"formdata.goodsstate"}},[o("el-option",{attrs:{label:"物料",value:"物料"}}),o("el-option",{attrs:{label:"成品",value:"成品"}}),o("el-option",{attrs:{label:"半成品",value:"半成品"}})],1)],1)],1)])],1),o("el-row",[o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("surface")}}},[o("el-form-item",{attrs:{label:"表面处理",prop:"surface"}},[o("el-popover",{ref:"surfacedict",staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click",width:t.eleWidth},on:{show:function(e){return t.$refs.surfaceRef.bindData()}}},[o("SelDict",{ref:"surfaceRef",attrs:{multi:0,baseparam:"/SaDict",billcode:"goods.surface"},on:{singleSel:function(e){t.formdata.surface=e.dictvalue,t.$refs["surfacedict"].doClose(),t.cleValidate("surface")},closedic:function(e){return t.$refs["surfacedict"].doClose()}}}),o("div",{attrs:{slot:"reference"},slot:"reference"},[o("el-input",{attrs:{placeholder:"请选择表面处理",clearable:"",size:"small"},model:{value:t.formdata.surface,callback:function(e){t.$set(t.formdata,"surface",e)},expression:"formdata.surface"}})],1)],1)],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("material")}}},[o("el-form-item",{attrs:{label:"货品材质",prop:"material"}},[o("el-popover",{ref:"materialdict",staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click",width:t.eleWidth},on:{show:function(e){return t.$refs.materialRef.bindData()}}},[o("SelDict",{ref:"materialRef",attrs:{multi:0,baseparam:"/SaDict",billcode:"goods.material"},on:{singleSel:function(e){t.formdata.material=e.dictvalue,t.$refs["materialdict"].doClose(),t.cleValidate("material")},closedic:function(e){return t.$refs["materialdict"].doClose()}}}),o("div",{attrs:{slot:"reference"},slot:"reference"},[o("el-input",{attrs:{placeholder:"请选择货品材质",clearable:"",size:"small"},model:{value:t.formdata.material,callback:function(e){t.$set(t.formdata,"material",e)},expression:"formdata.material"}})],1)],1)],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("barcode")}}},[o("el-form-item",{attrs:{label:"条形码",prop:"barcode"}},[o("el-input",{attrs:{placeholder:"条形码",clearable:"",size:"small"},model:{value:t.formdata.barcode,callback:function(e){t.$set(t.formdata,"barcode",e)},expression:"formdata.barcode"}})],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("goodspinyin")}}},[o("el-form-item",{attrs:{label:"货品拼音",prop:"goodspinyin"}},[o("el-input",{attrs:{placeholder:"请输入货品拼音",clearable:"",size:"small"},model:{value:t.formdata.goodspinyin,callback:function(e){t.$set(t.formdata,"goodspinyin",e)},expression:"formdata.goodspinyin"}})],1)],1)])],1),o("el-row",[o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[o("el-form-item",{attrs:{label:"相关厂商",prop:"groupname"}},[o("GroupAutoComplete",{attrs:{baseurl:"/D01M01B2/getOnlinePageList",type:"供应商",value:t.formdata.groupname},on:{setRow:function(e){t.$emit("setGroupRow",e),t.cleValidate("groupname")}}})],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("inprice")}}},[o("el-form-item",{attrs:{label:"建议进价",prop:"inprice"}},[o("el-input",{attrs:{placeholder:"建议进价",clearable:"",size:"small"},model:{value:t.formdata.inprice,callback:function(e){t.$set(t.formdata,"inprice",e)},expression:"formdata.inprice"}})],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("outprice")}}},[o("el-form-item",{attrs:{label:"建议售价",prop:"outprice"}},[o("el-input",{attrs:{placeholder:"建议售价",clearable:"",size:"small"},model:{value:t.formdata.outprice,callback:function(e){t.$set(t.formdata,"outprice",e)},expression:"formdata.outprice"}})],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("partid")}}},[o("el-form-item",{attrs:{label:"外部编码",prop:"partid"}},[o("el-input",{attrs:{placeholder:"请输入外部编码",clearable:"",size:"small"},model:{value:t.formdata.partid,callback:function(e){t.$set(t.formdata,"partid",e)},expression:"formdata.partid"}})],1)],1)])],1),o("el-divider",{attrs:{"content-position":"left"}},[t._v("库存属性")]),o("el-row",[o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("storeid")}}},[o("el-form-item",{attrs:{label:"默认仓库",prop:"storeid"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择仓库",size:"small"},on:{change:function(e){return t.$emit("changeStore",e)}},model:{value:t.formdata.storeid,callback:function(e){t.$set(t.formdata,"storeid",e)},expression:"formdata.storeid"}},t._l(t.storeOptions,(function(t){return o("el-option",{key:t.id,attrs:{label:t.storename,value:t.id}})})),1)],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("storelistname")}}},[o("el-form-item",{attrs:{label:"授权仓库",prop:"storelistname"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",size:"small"},on:{change:function(e){return t.$emit("selStoreList",e)}},model:{value:t.copystorelist,callback:function(e){t.copystorelist=e},expression:"copystorelist"}},t._l(t.storeOptions,(function(t){return o("el-option",{key:t.id,attrs:{value:t.id,label:t.storename}})})),1)],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("safestock")}}},[o("el-form-item",{attrs:{label:"安全库存",prop:"safestock"}},[o("el-input",{attrs:{placeholder:"安全库存",clearable:"",size:"small"},model:{value:t.formdata.safestock,callback:function(e){t.$set(t.formdata,"safestock",e)},expression:"formdata.safestock"}})],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{staticStyle:{"line-height":"40px","margin-left":"20px"}},[o("el-checkbox",{attrs:{label:"批次存储","true-label":1,"false-label":0},model:{value:t.formdata.batchmg,callback:function(e){t.$set(t.formdata,"batchmg",e)},expression:"formdata.batchmg"}}),o("el-checkbox",{attrs:{label:"批次独立","true-label":1,"false-label":0},model:{value:t.formdata.batchonly,callback:function(e){t.$set(t.formdata,"batchonly",e)},expression:"formdata.batchonly"}}),o("el-checkbox",{attrs:{label:"SN包装","true-label":1,"false-label":0},model:{value:t.formdata.packsnmark,callback:function(e){t.$set(t.formdata,"packsnmark",e)},expression:"formdata.packsnmark"}}),o("el-checkbox",{attrs:{label:"SKU","true-label":1,"false-label":0},model:{value:t.formdata.skumark,callback:function(e){t.$set(t.formdata,"skumark",e)},expression:"formdata.skumark"}})],1)])],1),o("el-row",[o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("quickcode")}}},[o("el-form-item",{attrs:{label:"快码字段",prop:"quickcode"}},[o("el-input",{attrs:{placeholder:"请输入快码字段",clearable:"",size:"small"},model:{value:t.formdata.quickcode,callback:function(e){t.$set(t.formdata,"quickcode",e)},expression:"formdata.quickcode"}})],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("brandname")}}},[o("el-form-item",{attrs:{label:"品牌",prop:"brandname"}},[o("el-popover",{ref:"brandnamedict",staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click",width:t.eleWidth},on:{show:function(e){return t.$refs.brandnameRef.bindData()}}},[o("SelDict",{ref:"brandnameRef",attrs:{multi:0,baseparam:"/SaDict",billcode:"goods.brandname"},on:{singleSel:function(e){t.formdata.brandname=e.dictvalue,t.$refs["brandnamedict"].doClose(),t.cleValidate("material")},closedic:function(e){return t.$refs["brandnamedict"].doClose()}}}),o("div",{attrs:{slot:"reference"},slot:"reference"},[o("el-input",{attrs:{placeholder:"请选择品牌",clearable:"",size:"small"},model:{value:t.formdata.brandname,callback:function(e){t.$set(t.formdata,"brandname",e)},expression:"formdata.brandname"}})],1)],1)],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",{on:{click:function(e){return t.cleValidate("alertsqty")}}},[o("el-form-item",{attrs:{label:"预警数量",prop:"alertsqty"}},[o("el-input",{attrs:{placeholder:"预警数量",clearable:"",size:"small"},model:{value:t.formdata.alertsqty,callback:function(e){t.$set(t.formdata,"alertsqty",e)},expression:"formdata.alertsqty"}})],1)],1)]),o("el-col",{attrs:{span:6}},[o("div",[o("el-form-item",{attrs:{label:"","label-width":"22px"}},[o("el-checkbox",{attrs:{label:"状态","true-label":1,"false-label":0},model:{value:t.formdata.enabledmark,callback:function(e){t.$set(t.formdata,"enabledmark",e)},expression:"formdata.enabledmark"}})],1)],1)])],1),o("el-divider",{attrs:{"content-position":"left"}},[o("span",{staticStyle:{cursor:"pointer"},on:{click:function(e){t.ishowAdd=!t.ishowAdd}}},[t._v("附加信息 "),o("i",{class:t.ishowAdd?"el-icon-arrow-down":"el-icon-arrow-up"})])]),o("el-col",{attrs:{span:18}},[o("el-row",{directives:[{name:"show",rawName:"v-show",value:t.ishowAdd,expression:"ishowAdd"}]},[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"物料单位",prop:"matqtyunit"}},[o("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",size:"small"},model:{value:t.formdata.matqtyunit,callback:function(e){t.$set(t.formdata,"matqtyunit",e)},expression:"formdata.matqtyunit"}},[o("el-option",{attrs:{label:"主单位",value:0}}),o("el-option",{attrs:{label:"重量",value:1}}),o("el-option",{attrs:{label:"面积",value:2}}),o("el-option",{attrs:{label:"体积",value:3}})],1)],1)])],1)],1),o("el-row",{directives:[{name:"show",rawName:"v-show",value:t.ishowAdd,expression:"ishowAdd"}]},[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"单件重量",prop:"weightqty"}},[o("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[o("el-input",{attrs:{placeholder:"单件重量",clearable:"",size:"small"},model:{value:t.formdata.weightqty,callback:function(e){t.$set(t.formdata,"weightqty",e)},expression:"formdata.weightqty"}}),o("el-input",{staticStyle:{width:"140px","margin-left":"4px"},attrs:{placeholder:"重量单位",size:"small"},model:{value:t.formdata.weightunit,callback:function(e){t.$set(t.formdata,"weightunit",e)},expression:"formdata.weightunit"}})],1)])],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"单件面积",prop:"areaqty"}},[o("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[o("el-input",{attrs:{placeholder:"单件面积",clearable:"",size:"small"},model:{value:t.formdata.areaqty,callback:function(e){t.$set(t.formdata,"areaqty",e)},expression:"formdata.areaqty"}}),o("el-input",{staticStyle:{width:"140px","margin-left":"4px"},attrs:{placeholder:"面积单位",size:"small"},model:{value:t.formdata.areaunit,callback:function(e){t.$set(t.formdata,"areaunit",e)},expression:"formdata.areaunit"}})],1)])],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"单件体积",prop:"volumeqty"}},[o("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[o("el-input",{attrs:{placeholder:"单件体积",clearable:"",size:"small"},model:{value:t.formdata.volumeqty,callback:function(e){t.$set(t.formdata,"volumeqty",e)},expression:"formdata.volumeqty"}}),o("el-input",{staticStyle:{width:"140px","margin-left":"4px"},attrs:{placeholder:"体积单位",size:"small"},model:{value:t.formdata.volumeunit,callback:function(e){t.$set(t.formdata,"volumeunit",e)},expression:"formdata.volumeunit"}})],1)])],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"包装数量",prop:"packqty"}},[o("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[o("el-input",{attrs:{placeholder:"包装数量",clearable:"",size:"small"},model:{value:t.formdata.packqty,callback:function(e){t.$set(t.formdata,"packqty",e)},expression:"formdata.packqty"}}),o("el-input",{staticStyle:{width:"140px","margin-left":"4px"},attrs:{placeholder:"包装单位",size:"small"},model:{value:t.formdata.packunit,callback:function(e){t.$set(t.formdata,"packunit",e)},expression:"formdata.packunit"}})],1)])],1)],1),o("el-row",{directives:[{name:"show",rawName:"v-show",value:t.ishowAdd,expression:"ishowAdd"}]},[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:t.VbindCustom("custom1","自定义1"),prop:"custom1"}},[o("el-input",{attrs:{placeholder:"请输入内容",clearable:"",size:"small"},model:{value:t.formdata.custom1,callback:function(e){t.$set(t.formdata,"custom1",e)},expression:"formdata.custom1"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:t.VbindCustom("custom2","自定义2"),prop:"custom2"}},[o("el-input",{attrs:{placeholder:"请输入内容",clearable:"",size:"small"},model:{value:t.formdata.custom2,callback:function(e){t.$set(t.formdata,"custom2",e)},expression:"formdata.custom2"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:t.VbindCustom("custom3","自定义3"),prop:"custom3"}},[o("el-input",{attrs:{placeholder:"请输入内容",clearable:"",size:"small"},model:{value:t.formdata.custom3,callback:function(e){t.$set(t.formdata,"custom3",e)},expression:"formdata.custom3"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:t.VbindCustom("custom4","自定义4"),prop:"custom4"}},[o("el-input",{attrs:{placeholder:"请输入内容",clearable:"",size:"small"},model:{value:t.formdata.custom4,callback:function(e){t.$set(t.formdata,"custom4",e)},expression:"formdata.custom4"}})],1)],1)],1),o("el-row",{directives:[{name:"show",rawName:"v-show",value:t.ishowAdd,expression:"ishowAdd"}]},[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:t.VbindCustom("custom5","自定义5"),prop:"custom5"}},[o("el-input",{attrs:{placeholder:"请输入内容",clearable:"",size:"small"},model:{value:t.formdata.custom5,callback:function(e){t.$set(t.formdata,"custom5",e)},expression:"formdata.custom5"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:t.VbindCustom("custom6","自定义6"),prop:"custom6"}},[o("el-input",{attrs:{placeholder:"请输入内容",clearable:"",size:"small"},model:{value:t.formdata.custom6,callback:function(e){t.$set(t.formdata,"custom6",e)},expression:"formdata.custom6"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:t.VbindCustom("custom7","自定义7"),prop:"custom7"}},[o("el-input",{attrs:{placeholder:"请输入内容",clearable:"",size:"small"},model:{value:t.formdata.custom7,callback:function(e){t.$set(t.formdata,"custom7",e)},expression:"formdata.custom7"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:t.VbindCustom("custom8","自定义8"),prop:"custom8"}},[o("el-input",{attrs:{placeholder:"请输入内容",clearable:"",size:"small"},model:{value:t.formdata.custom8,callback:function(e){t.$set(t.formdata,"custom8",e)},expression:"formdata.custom8"}})],1)],1)],1),o("el-row",{directives:[{name:"show",rawName:"v-show",value:t.ishowAdd,expression:"ishowAdd"}]},[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:t.VbindCustom("custom9","自定义9"),prop:"custom9"}},[o("el-input",{attrs:{placeholder:"请输入内容",clearable:"",size:"small"},model:{value:t.formdata.custom9,callback:function(e){t.$set(t.formdata,"custom9",e)},expression:"formdata.custom9"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:t.VbindCustom("custom10","自定义10"),prop:"custom10"}},[o("el-input",{attrs:{placeholder:"请输入内容",clearable:"",size:"small"},model:{value:t.formdata.custom10,callback:function(e){t.$set(t.formdata,"custom10",e)},expression:"formdata.custom10"}})],1)],1)],1)],1),o("el-col",{directives:[{name:"show",rawName:"v-show",value:t.ishowAdd,expression:"ishowAdd"}],attrs:{span:6}},[o("el-form-item",{attrs:{label:"附图",prop:"goodsphoto1"}},[t.formdata.id?o("div",{staticClass:"cardphotoImg"},[o("img",{attrs:{src:t.formdata.goodsphoto1?t.formdata.goodsphoto1:a("aad1"),alt:""},on:{click:function(e){return t.$emit("openImgInfo")}}}),t.formdata.goodsphoto1?o("div",{staticClass:"imgClose",on:{click:function(e){t.formdata.goodsphoto1=""}}},[o("i",{staticClass:"el-icon-close"})]):t._e()]):o("div",{staticStyle:{color:"#666"}},[t._v("请先保存货品后，再添加图片!")])])],1)],1)},f=[],h=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{},[a("div",{staticStyle:{"border-bottom":"1px solid #ccc"},on:{click:t.goBack}},[a("div",{staticClass:"goBack"},[t.lastValue?a("div",[a("span",{staticClass:"iconfont el-icon-arrow-left"})]):t._e(),a("div",{staticStyle:{"margin-left":"10px","text-align":"left"}},[t._v(" "+t._s(t.lastValue?t.lastValue.tgvalue:"暂无上级")+" ")])])]),a("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?a("ul",{},t._l(t.lst,(function(e,o){return a("li",{key:o,class:t.radio==o?"active":"",on:{click:function(a){t.getCurrentRow(e),t.bindData("children",e.id),t.radio=o}}},[a("span",[t._v(t._s(e.tgvalue))])])})),0):a("div",{staticClass:"noData"},[t._v("暂无数据")])]),a("div",{staticClass:"foots"},[a("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0}}},[t._v("编辑")]),a("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),a("el-dialog",{staticStyle:{"z-index":"10"},attrs:{width:"450px",title:t.editTitle,"append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"20vh"},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"left"},t._l(t.lst,(function(e,o){return a("p",{key:o,class:e.isActive?"isActive":"",on:{click:function(e){return t.changeLi("lst",o)}}},[t._v(" "+t._s(e.tgvalue)+" ")])})),0),a("div",{staticClass:"right",staticStyle:{width:"100px"}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)])]),a("el-dialog",{staticStyle:{"z-index":"9999"},attrs:{title:"新增",visible:t.dialogVisible,width:"30%","append-to-body":"",top:"20vh"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"130px","auto-complete":"off",rules:t.rules}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"上级名称"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"上级名称",clearable:"",size:"small",disabled:!t.selrows.tgvalue,readonly:""},model:{value:t.selrows.tgvalue,callback:function(e){t.$set(t.selrows,"tgvalue",e)},expression:"selrows.tgvalue"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.clearValidt("tgvalue")}}},[a("el-form-item",{attrs:{label:"名称",prop:"tgvalue"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"名称",clearable:"",size:"small"},model:{value:t.formdata.tgvalue,callback:function(e){t.$set(t.formdata,"tgvalue",e)},expression:"formdata.tgvalue"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"前缀"}},[a("el-select",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请选择",size:"small",disabled:!t.selrows.tgvalue},on:{change:t.changeTgcode},model:{value:t.selTgValue,callback:function(e){t.selTgValue=e},expression:"selTgValue"}},t._l(t.options,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small"},on:{click:t.closeDialog}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitForm}},[t._v("确 定")])],1)],1)],1)},g=[],b=(a("b0b8"),{components:{},props:["multi","billcode","selectValue"],data:function(){return{title:"数据字典",lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:2e3,OrderType:0,SearchType:1,OrderBy:"rownum"},selectIdList:[],newVal:[],idx:"",dialogVisible:!1,formdata:{parentid:"",tgvalue:"",tgcode:"",tglevel:0,tgbillcode:this.billcode,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},rules:{tgvalue:[{required:!0,message:"请输入货品名称",trigger:"blur"}]},options:[{value:"/",label:"/"},{value:"-",label:"-"},{value:"_",label:"_"}],selTgValue:"",selectId:"",changeLiItem:{},isEdit:!1}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a},selectValue:function(t,e){this.newVal=t}},computed:{lastValue:function(){return this.newVal.length>0?this.newVal[this.newVal.length-1]:""},editTitle:function(){if(this.selectValue.length>0){for(var t="",e=0;e<this.selectValue.length;e++)t+=" ".concat(this.selectValue[e].tgcode," ").concat(this.selectValue[e].tgvalue);return t+" 分类"}return"新增一级分类"}},methods:{submitForm:function(){var t=this;this.$refs.formdata.validate((function(e){if(!e)return console.log("error submit!!"),!1;t.isEdit?t.submitUpdate():t.formsave()}))},formsave:function(){var t=this;console.log(this.formdata,"添加");var e=this;0==this.idx?r["a"].post("/system/SYSM07B1TG/create",e.formdata).then((function(e){200==e.data.code&&(t.$message.success("保存成功"),t.dialogVisible=!1,t.formdata.parentid="",t.formdata.tgvalue="",t.formdata.tgcode="",t.selTgValue="",t.formdata.id="",t.bindData("projectData",""))})).catch((function(e){t.$message.warning("保存失败")})):0!=this.idx&&(e.formdata.parentid=this.idx,r["a"].post("/system/SYSM07B1TG/create",e.formdata).then((function(e){200==e.data.code&&(t.$message.success("保存成功"),t.dialogVisible=!1,t.formdata.parentid="",t.formdata.tgvalue="",t.formdata.tgcode="",t.formdata.id="",t.selTgValue="",t.bindData("create",t.idx))})).catch((function(e){t.$message.warning("保存失败")})))},changeTgcode:function(t){this.formdata.tgcode=t},closeDialog:function(){this.formdata.parentid="",this.formdata.tgvalue="",this.formdata.tgcode="",this.formdata.id="",this.selTgValue="",this.dialogVisible=!1,this.isEdit=!1,this.$refs.formdata.resetFields()},getCurrentRow:function(t){this.selrows=t||{},this.idx=t.id,this.$forceUpdate(),this.$emit("singleSel",t,this.billcode)},bindData:function(t,e){var a=this;this.lst=[];var o=[{field:"CiTextGenerator.parentid",fieldtype:4,math:"like",value:e}];if(e){this.queryParams.scenedata=o;var i=this.selectIdList.findIndex((function(t){return t===e}));i<0&&this.selectIdList.push(e)}else this.$delete(this.queryParams,"scenedata"),this.queryParams.scenedata=[{field:"CiTextGenerator.tgbillcode",fieldtype:4,math:"like",value:this.billcode}];r["a"].post("/system/SYSM07B1TG/getPageList",JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){a.lst="projectData"===t?e.data.data.list.filter((function(t){return""===t.parentid})):e.data.data.list;for(var o=0;o<a.lst.length;o++)a.lst[o].isActive=!1}a.ActiveIndex=-1})).catch((function(t){a.$message.warning("获取字典数据失败")}))},goBack:function(){if(this.selectIdList.pop(),this.$emit("deleteItemVal",this.billcode),0===this.selectIdList.length)return this.bindData("projectData",this.selectIdList[this.selectIdList.length-1]),void this.$message.warning("已经是第一级");this.bindData("gooback",this.selectIdList[this.selectIdList.length-1])},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},changeLi:function(t,e){this.ActiveIndex=e;for(var a=0;a<this[t].length;a++)this[t][a].isActive=!1;this[t][e].isActive=!0,this.changeLiItem=this[t][e],this.$forceUpdate()},addInput:function(){console.log(this.formdata,"formdata"),this.dialogVisible=!0,this.formdata.tgvalue="",this.formdata.tgcode="",this.formdata.parentid=this.selrows.id,this.selTgValue="",this.formdata.id=""},editInput:function(){console.log(this.formdata,"123"),this.dialogVisible=!0,this.isEdit=!0,this.formdata.tgvalue=this.changeLiItem.tgvalue,this.selTgValue=this.changeLiItem.tgcode},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){r["a"].get("/system/SYSM07B1TG/delete?key=".concat(t.changeLiItem.id)).then((function(e){200==e.data.code?(t.$message.success("删除成功"),t.bindData(t.changeLiItem.parentid?"delItem":"projectData",t.selrows.id)):t.$message.warning("删除失败"|e.data.msg)}))}))},getMoveUp:function(){var t=this;if(0!=this.ActiveIndex){var e=this.lst[this.ActiveIndex],a=this.lst[this.ActiveIndex].rownum;this.lst.splice(a,1),this.lst.splice(a-1,0,e),this.ActiveIndex-=1;for(var o=0;o<this.lst.length;o++)this.lst[o].rownum=o;var i=this.lst.map((function(t){return t.id}));this.$request.post("/system/SYSM07B1TG/orderByRowNum",i).then((function(e){200===e.data.code||t.$message.warning("排序失败")}))}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){var t=this;if(this.ActiveIndex!=this.lst.length-1){var e=this.lst[this.ActiveIndex],a=this.lst[this.ActiveIndex].rownum;this.lst.splice(a,1),this.lst.splice(a+1,0,e),this.ActiveIndex+=1;for(var o=0;o<this.lst.length;o++)this.lst[o].rownum=o;var i=this.lst.map((function(t){return t.id}));this.$request.post("/system/SYSM07B1TG/orderByRowNum",i).then((function(e){200===e.data.code||t.$message.warning("排序失败")}))}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.id=this.changeLiItem.id,this.formdata.tgcode=this.selTgValue,this.formdata.parentid=this.changeLiItem.parentid,r["a"].post("/system/SYSM07B1TG/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code&&(t.$message.success("保存成功"),t.dialogVisible=!1,t.formdata.tgvalue="",t.formdata.tgcode="",t.formdata.id="",t.selrows.tgvalue?t.bindData("updataItem",t.changeLiItem.parentid):t.bindData("projectData",""))})).catch((function(e){t.$message.warning("保存失败")})),this.isEdit=!1},clearValidt:function(t){this.$refs.formdata.clearValidate(t)}}}),v=b,y=(a("773b"),a("2877")),w=Object(y["a"])(v,h,g,!1,null,"1cc4d750",null),x=w.exports,S={props:{formdata:{type:Object},title:{type:String},storeOptions:{type:Array},groupnameOptions:{type:Array},storelist:{type:Array},customDataList:{type:Array}},components:{TextGenerator:x},data:function(){return{formRules:{goodsname:[{required:!0,trigger:"blur",message:"货品名称为必填项"}],goodsunit:[{required:!0,trigger:"blur",message:"货品单位为必填项"}],goodsstate:[{required:!0,trigger:"blur",message:"货品状态为必填项"}],safestock:[{required:!0,trigger:"blur",message:"安全库存为必填项"}],inprice:[{required:!0,trigger:"blur",message:"建议进价为必填项"}],outprice:[{required:!0,trigger:"blur",message:"建议售价为必填项"}],storeid:[{required:!0,trigger:"blur",message:"默认仓库为必填项"}],ivquantity:[{required:!0,trigger:"blur",message:"库存数量为必填项"}],ageprice:[{required:!0,trigger:"blur",message:"库存单价为必填项"}],uidgroupguid:[{required:!0,trigger:"blur",message:"分组名称为必填项"}],goodsuid:[{required:!0,trigger:"blur",message:"货品编码为必填项"}]},ishowAdd:!1,defaultProps:{children:"children",label:"groupname",value:"id"},copystorelist:[],eleWidth:"",selectValue:[]}},mounted:function(){var t=this;window.addEventListener("resize",(function(){t.eleWidth=t.SelDict()})),this.eleWidth=this.SelDict()},watch:{storelist:function(t,e){this.copystorelist=this.storelist},copystorelist:function(t,e){this.$emit("setStoreList",this.copystorelist)}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)},VbindCustom:function(t,e){if(0==this.customDataList.length)return e;var a=this.customDataList.findIndex((function(e){return e.columncode==t}));if(-1!=a){var o=this.customDataList[a];return null==o.itemlabel||""==o.itemlabel?o.itemname:o.itemlabel}return e},SelDict:function(){var t=this.$refs.colRefs;return"".concat(t.offsetWidth-100)},deleteItem:function(t){var e=t.split(".")[1];this.selectValue.pop();for(var a="",o=0;o<this.selectValue.length;o++)a+="".concat(this.selectValue[o].tgcode).concat(this.selectValue[o].tgvalue);this.formdata[e]=a},getSelectItem:function(t,e){var a=e.split(".")[1],o=this.selectValue.findIndex((function(e){return e.tgvalue===t.tgvalue}));o<0&&this.selectValue.push(t);for(var i="",s=0;s<this.selectValue.length;s++)i+="".concat(this.selectValue[s].tgcode).concat(this.selectValue[s].tgvalue);this.formdata[a]=i}}},k=S,D=(a("ce62"),Object(y["a"])(k,p,f,!1,null,"7b57e4bf",null)),$=D.exports,_=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"引入货品",icon:"el-icon-plus",disabled:"this.formstate!==0",methods:"openHistoryGoods",param:"",children:[]}],P=[{show:1,divided:!1,label:"供应商报价",icon:"",disabled:"this.formstate==0",methods:"selectSupplier",param:"",children:[]}],C={header:{type:0,title:"",content:[{type:"divider",center:"left",label:"编码规则"},{type:"form",rowitem:[{col:5,code:"partgroupid",label:"分组名称",type:"select",methods:"",param:"",required:!0}]},{rowitem:[{col:5,code:"itemname",label:"名称",type:"input",methods:"",param:"",required:!0},{col:5,code:"itemtype",label:"类型",type:"input",methods:"",param:""},{col:5,code:"enabledmark",label:"有效",type:"checkbox",methods:"",param:""}]},{rowitem:[{col:5,code:"itemspec",label:"规格",type:"input",methods:"",param:""},{col:5,code:"itemunit",label:"单位",type:"input",methods:"",param:""},{col:2,code:"allowedit",label:"允许编辑",type:"checkbox",methods:"",param:""},{col:2,code:"allowdelete",label:"允许删除",type:"checkbox",methods:"",param:""}]},{type:"divider",center:"left",label:"价格设置"},{rowitem:[{col:5,code:"baseprice",label:"基础价",type:"input",methods:"countPrice",param:""},{col:5,code:"rebate",label:"折扣",type:"number",methods:"countPrice",param:""},{col:5,code:"rebatesec",label:"二级折扣",type:"number",methods:"countPrice",param:""}]},{rowitem:[{col:5,code:"pricemin",label:"最小单价",type:"input",methods:"",param:""},{col:5,code:"pricemax",label:"最大单价",type:"input",methods:"",param:""},{col:5,code:"price",label:"单价",type:"text",methods:"",param:""}]}]},footer:{type:0,content:[{type:"divider",center:"left",label:""},{rowitem:[{col:22,code:"summary",label:"摘要",type:"input",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}},I=a("dcb4"),V=a("b0b8"),q=a.n(V),O=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:"352px"},on:{"sort-change":t.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.$index+1))])]}}])}),a("el-table-column",{attrs:{label:"单据编号",align:"center","min-width":"100","show-overflow-tooltip":"",prop:"refno"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.refno?e.row.refno:"单据编号"))])]}}])}),a("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.billtype))])]}}])}),a("el-table-column",{attrs:{label:"单据日期",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormats")(e.row.billdate)))])]}}])}),a("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.groupname)+" ")]}}])}),a("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsname))])]}}])}),a("el-table-column",{attrs:{label:"单位",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsunit))])]}}])}),a("el-table-column",{attrs:{label:"数量",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.quantity))])]}}])}),a("el-table-column",{attrs:{label:"单价",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.price))])]}}])}),a("el-table-column",{attrs:{label:"金额",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.taxamount))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},T=[],z=a("ade3"),L=a("333d"),B={components:{Pagination:L["a"]},props:["idx"],data:function(){return{lst:[],searchstr:"",total:0,listLoading:!1,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:0}}},created:function(){},mounted:function(){},methods:{bindData:function(){var t=this;console.log(this.idx,"idx"),this.listLoading=!0,this.queryParams.SearchPojo={goodsid:this.idx},r["a"].post("/D03M07B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo=Object(z["a"])({refno:t,billtitle:t,groupname:t,goodsname:t,goodsuid:t,partid:t,groupuid:t},"groupname",t):this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(t,this.tableForm),this.bindData()}}},F=B,N=Object(y["a"])(F,O,T,!1,null,null,null),M=N.exports,j=a("9bda"),E={name:"Formedit",components:{EditHeader:$,FormTemp:I["a"],Supplier:M,SelGoods:j["a"]},props:["idx"],data:function(){return{title:"货品信息",operateBar:_,processBar:P,formdata:{ageprice:0,alertsqty:0,barcode:"",batchmg:0,batchonly:0,createdate:new Date,modifydate:new Date,deletelister:"",enabledmark:1,packsnmark:0,goodsname:"",goodspinyin:"",goodsspec:"",goodsstate:"",goodsuid:"",goodsunit:"",groupid:"",groupname:"",inprice:0,ivquantity:0,material:"",outprice:0,partid:"",pid:"",puid:"",remark:"",safestock:0,taxrate:13,storeid:"",storelistguid:"",storelistname:"",surface:"",uidgroupcode:"",uidgroupguid:"",uidgroupname:"",uidgroupnum:0,versionnum:"",brandname:"",virtualitem:0,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},formstate:0,submitting:0,formtemplate:C,selectform:[],SupplierVisible:!1,historyGoodsVisible:!1,ishowAdd:!1,ImgInfoVisible:!1,customDataList:[],storelist:[],goodsunitRefsVisible:!1,surfaceRefsVisible:!1,materialRefsVisible:!1,brandnameRefsVisible:!1,storeOptions:[],groupnameOptions:[]}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp(),this.bindDataByStoreList(),this.bindDataByUidGroupName()},mounted:function(){this.bindData()},methods:{bindTemp:function(){this.formtemplate=C},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&r["a"].get("/D91M01B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formdata.storelistname&&(t.storelist=t.formdata.storelistguid.split(",")),t.formstate=t.formdata.id?1:0):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.submitting=1;var e={};e=this.$getParam(m,e,this.formdata),0==this.idx?d.add(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.id?1:0)})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):d.update(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.id?1:0)})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))},clickMethods:function(t){this[t.meth](t.param)},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){d.delete(e)})).catch((function(){}))},selectSupplier:function(){this.SupplierVisible=!0},openHistoryGoods:function(){var t=this;this.historyGoodsVisible=!0,this.$nextTick((function(){t.$refs.selGoods.bindData()}))},submitselGoods:function(){this.formdata=this.$refs.selGoods.selrows,this.formdata.uidgroupguid="",this.formdata.uidgroupname="",this.formdata.uidgroupcode="",this.formdata.uidgroupnum="",this.formdata.goodsuid="",this.historyGoodsVisible=!1},bindDataByUidGroupName:function(){var t=this,e={PageNum:1,PageSize:1e4,OrderType:0,SearchType:1,OrderBy:"rownum"};r["a"].post("/D91M01S1/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.groupnameOptions=t.changeFormat(e.data.data.list))})).catch((function(t){}))},handleChangeGroupid:function(t){var e=t[t.length-1];this.formdata.uidgroupguid=t[t.length-1],this.changeByGroup(e)},changeByGroup:function(t){var e=this;r["a"].get("/D91M01B1/getNewUidByGroup?key="+t).then((function(t){e.formdata.goodsuid=t.data.data.goodsuid,e.formdata.uidgroupcode=t.data.data.uidgroupcode,e.formdata.uidgroupnum=t.data.data.uidgroupnum,e.formdata.uidgroupname=t.data.data.uidgroupname}))},getListByCode:function(){var t=this;r["a"].get("/system/SYSM07B6/getListByCode?key=d91m01").then((function(e){200==e.data.code?0==e.data.data.length?t.customDataList=[]:t.customDataList=e.data.data:t.$message.warning(e.data.msg||"查询失败")}))},getpingyin:function(t){q.a.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.goodspinyin=q.a.getFullChars(t)},bindDataByStoreList:function(){var t=this,e={PageNum:1,PageSize:500,OrderType:1,SearchType:1};r["a"].post("/D04M21S1/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.storeOptions=e.data.data.list)})).catch((function(t){}))},changeStore:function(t){var e=this;this.storeOptions.forEach((function(a){if(a.id==t){e.formdata.storename=a.storename,e.formdata.storeid=a.id;var o=e.storelist.findIndex((function(e){return e==t}));-1==o&&(e.storelist.push(t),e.selStoreList(e.storelist))}}))},selStoreList:function(t){var e=this,a=t.findIndex((function(t){return t==e.formdata.storeid}));if(-1==a)return this.$message.warning("授权仓库须含有默认仓库"),this.storelist=this.storelist.filter((function(t){return t!==e.formdata.storeid})),void this.storelist.splice(a,0,this.formdata.storeid);var o="",i="";t.forEach((function(t,e,a){e==a.length-1?o+=t:o=o+t+","}));for(var s=0;s<this.storeOptions.length;s++)for(var r=0;r<t.length;r++)t[r]==this.storeOptions[s].id&&(i+=this.storeOptions[s].storename+",");var n=/,$/gi;i=i.replace(n,""),this.formdata.storelistguid=o,this.formdata.storelistname=i},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.selVisible=!1},changeIdx:function(t){this.idx=t},changeFormat:function(t){var e=[];if(!Array.isArray(t))return e;t.forEach((function(t){delete t.children}));var a={};return t.forEach((function(t){a[t.id]=t})),t.forEach((function(t){var o=a[t.parentid];o?(o.children||(o.children=[])).push(t):e.push(t)})),e}}},G=E,A=(a("5df0"),Object(y["a"])(G,n,l,!1,null,"c4d5625c",null)),R=A.exports,J=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("parentid")}}},[a("el-form-item",{attrs:{label:"父级",prop:"parentid"}},[a("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{options:t.groupData,props:t.defaultProps,"show-all-levels":!1,placeholder:"请选择父级名称",size:"small",clearable:""},on:{change:t.handleChangeGroupid},model:{value:t.groupPid,callback:function(e){t.groupPid=e},expression:"groupPid"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"分组名称",prop:"groupname"}},[a("el-input",{attrs:{placeholder:"请输入分组名称",clearable:"",size:"small"},on:{input:t.writeCode},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupcode")}}},[a("el-form-item",{attrs:{label:"分组编码"}},[a("el-input",{attrs:{placeholder:"请输分组编码",size:"small"},model:{value:t.formdata.groupcode,callback:function(e){t.$set(t.formdata,"groupcode",e)},expression:"formdata.groupcode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("prefix")}}},[a("el-form-item",{attrs:{label:"前缀",prop:"prefix"}},[a("el-input",{attrs:{placeholder:"请输入前缀",clearable:"",size:"small"},model:{value:t.formdata.prefix,callback:function(e){t.$set(t.formdata,"prefix",e)},expression:"formdata.prefix"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("suffix")}}},[a("el-form-item",{attrs:{label:"后缀",prop:"suffix"}},[a("el-input",{attrs:{placeholder:"请输后缀",clearable:"",size:"small"},model:{value:t.formdata.suffix,callback:function(e){t.$set(t.formdata,"suffix",e)},expression:"formdata.suffix"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("sncode")}}},[a("el-form-item",{attrs:{label:"序号位",prop:"sncode"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"","allow-create":"",clearable:"",placeholder:"请输入前缀",size:"small"},model:{value:t.formdata.sncode,callback:function(e){t.$set(t.formdata,"sncode",e)},expression:"formdata.sncode"}},[a("el-option",{attrs:{label:"[00]",value:"[00]"}}),a("el-option",{attrs:{label:"[000]",value:"[000]"}}),a("el-option",{attrs:{label:"[0000]",value:"[0000]"}}),a("el-option",{attrs:{label:"[00000]",value:"[00000]"}}),a("el-option",{attrs:{label:"[000000]",value:"[000000]"}}),a("el-option",{attrs:{label:"[0000000]",value:"[0000000]"}})],1)],1)],1)]),a("el-col",{attrs:{span:6}},[a("div",[a("el-form-item",{attrs:{prop:"allowitem"}},[a("el-checkbox",{attrs:{label:"允许货品建立","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.allowitem,callback:function(e){t.$set(t.formdata,"allowitem",e)},expression:"formdata.allowitem"}})],1)],1)])],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("rownum")}}},[a("el-form-item",{attrs:{label:"排序",prop:"rownum"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"SVG微标"}},[a("div",{staticClass:"flex j-start",staticStyle:{"align-items":"flex-start"}},[a("el-button",{staticStyle:{margin:"4px 10px 0 0"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.$refs.upload.click()}}},[t._v("SVG上传")]),a("div",{staticClass:"groupsvg"},[a("div",{staticClass:"close",on:{click:function(e){t.formdata.groupsvg=""}}},[t._v("X")]),a("div",{staticClass:"c-svg",staticStyle:{"text-align":"center"},domProps:{innerHTML:t._s(t.formdata.groupsvg)}})])],1)])],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("div",{staticClass:"button-container flex j-end",staticStyle:{"margin-top":"26px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[a("input",{ref:"upload",attrs:{type:"file"},on:{change:t.getFile}})])])},U=[];a("00b4"),a("25f0"),a("4d90");const H={add(t){return new Promise((e,a)=>{var o=JSON.stringify(t);r["a"].post("/D91M01S1/create",o).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var o=JSON.stringify(t);r["a"].post("/D91M01S1/update",o).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{r["a"].get("/D91M01S1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var W=H,Y=a("b0b8"),K={name:"Formedit",filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),o=(e.getMonth()+1).toString().padStart(2,"0"),i=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),n=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(o,"-").concat(i," ").concat(s,":").concat(r,":").concat(n)}},props:["idx","groupnum"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,parentid:"",grouptype:"",groupcode:"",groupname:"",rownum:0,grouplevel:0,allowitem:1,statecode:"",childcount:0,remark:"",groupsvg:""},formRules:{grouptype:[{required:!0,trigger:"blur",message:"分组类型不能为空"}],groupcode:[{required:!0,trigger:"blur",message:"分组编码不能为空"}],groupname:[{required:!0,trigger:"blur",message:"分组名称不能为空"}]},formLabelWidth:"100px",dialogVisible:!1,delDialogVisible:!1,option:"",defaultProps:{children:"children",label:"label",value:"id",checkStrictly:!0},groupData:[],groupPid:""}},computed:{formcontainHeight:function(){return"300px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.formdata.parentid=this.$attrs.pid?this.$attrs.pid:0,this.groupPid=this.$attrs.pid?this.$attrs.pid:0,this.bindData()},mounted:function(){this.Bindgroupname()},methods:{Bindgroupname:function(){var t=this;this.listLoading=!0;var e={PageNum:1,PageSize:1e4,OrderType:0,SearchType:1,OrderBy:"rownum"};r["a"].post("/D91M01S1/getPageList",JSON.stringify(e)).then((function(e){if(200==e.data.code){t.test=e.data.data.list;var a=e.data.data.list.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname,prefix:t.prefix,groupsvg:t.groupsvg,rownum:t.rownum}})),o=[{id:"0",pid:-1,label:"货品分组",prefix:!1,groupsvg:"",rownum:0}],i=[].concat(Object(s["a"])(a),o);t.groupData=t.transData(i,"id","pid","children")}t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},transData:function(t,e,a,o){for(var i=[],s={},r=e,n=a,l=o,c=0,d=0,u=t.length;c<u;c++)s[t[c][r]]=t[c];for(;d<u;d++){var m=t[d],p=s[m[n]];p?(!p[l]&&(p[l]=[]),p[l].push(m)):i.push(m)}return i},handleChangeGroupid:function(t){this.$set(this.formdata,"parentid",t[t.length-1]),this.$forceUpdate()},bindData:function(){var t=this;this.listLoading=!0,0!=this.idx?r["a"].get("/D91M01S1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code&&(t.formdata=e.data.data,t.groupPid=t.formdata.parentid),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")})):this.formdata.rownum=this.groupnum},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0==this.idx)W.add(this.formdata).then((function(){t.$message.success("保存成功"),t.$emit("closeForm"),t.$emit("bindData"),t.bindData()})).catch((function(e){t.$message.warning(e||"保存失败")}));else{if(this.formdata.id==this.formdata.parentid)return void this.$message.warning("保存失败：父级不能是自己，请重新选择");W.update(this.formdata).then((function(){t.$message.success("保存成功"),t.$emit("closeForm"),t.$emit("bindData"),t.bindData()})).catch((function(e){t.$message.warning(e||"保存失败")}))}},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),W.delete(t).then((function(){e.$message.success({message:"删除成功！"}),e.$emit("compForm")})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},check:function(){console.log("check")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},writeCode:function(t){Y.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.groupcode=Y.getFullChars(t)},getFile:function(){var t=this,e=this.$refs.upload,a=e.files[0],o=new Blob([a],{type:"text/plain;charset=utf-8"}),i=new FileReader;i.onload=function(e){t.formdata.groupsvg=e.target.result},i.readAsText(o),this.$forceUpdate()}}},Q=K,X=(a("b82e"),Object(y["a"])(Q,J,U,!1,null,"b753d8be",null)),Z=X.exports,tt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:"filter-container flex j-s"},[a("div",[a("div",{staticClass:"p-r",staticStyle:{display:"inline-block",height:"28px","margin-right":"5px",cursor:"pointer",color:"#383838"},on:{click:function(e){return t.$emit("btnshowGroup")}}},[a("i",{staticClass:"p-r",class:[t.showTree?"el-icon-folder-opened":"el-icon-folder"],staticStyle:{"font-size":"20px",top:"4px",color:"#5e5c5c"}})]),a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini",disabled:t.statusType||!t.isregister},on:{click:function(e){return t.$emit("btnAdd")}}},[t._v(" 添加 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-upload2",plain:"",size:"mini"},on:{click:t.btnImport}},[t._v(" 导入 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete",plain:"",size:"mini"},on:{click:t.allDelete}},[t._v(" 删除 ")]),a("el-button",{attrs:{size:"mini",icon:"el-icon-printer",title:"打印列表"},on:{click:function(e){return t.$emit("pagePrint")}}},[t._v("打印")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:"el-icon-printer",plain:"",size:"mini",title:"打印单据"},on:{click:function(e){return t.$emit("btnPrint")}}},[t._v(" 单据 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgformat"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),a("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[a("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)},et=[],at={name:"Listheader",props:["showTree","tableForm","passkey"],data:function(){return{strfilter:"",formdata:{},setColumsVisible:!1,code:"D91M01B1List",searchVisible:!1,statusType:!1,isregister:0}},mounted:function(){this.intervalTime(this.passkey),this.isregister=localStorage.getItem("getInfo")?JSON.parse(localStorage.getItem("getInfo")).isregister:0},methods:{advancedSearch:function(t){this.$emit("advancedSearch",t),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},btnImport:function(){this.$emit("btnImport")},modelExport:function(){this.$emit("modelExport")},bindData:function(){this.$emit("bindData")},allDelete:function(){this.$emit("allDelete")},btnExport:function(){this.$emit("btnExport")},Export:function(){this.$emit("Export")},intervalTime:function(t){if(t){var e=t.ex,a=Date.parse(new Date)/1e3,o=a,i=e/1e3,s=1e3*(i-o),r=Math.floor(s/864e5),n=s%864e5,l=(Math.floor(n/36e5),n%36e5),c=(Math.floor(l/6e4),l%6e4);Math.round(c/1e3);this.statusType=r<0}else this.statusType=!0}}},ot=at,it=(a("a535"),Object(y["a"])(ot,tt,et,!1,null,"1b591c2e",null)),st=it.exports,rt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",[a("div",[a("el-form",{ref:"mainData",staticClass:"custInfo",attrs:{model:t.mainData,"label-width":t.formLabelWidth,"auto-complete":"off"}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"货品分组"}},[a("el-cascader",{attrs:{options:t.menuData,props:t.defaultProps,clearable:"","show-all-levels":!1,size:"small",disabled:!!t.goodsData.length},on:{change:t.handleCascader},model:{value:t.mainData.matGroup,callback:function(e){t.$set(t.mainData,"matGroup",e)},expression:"mainData.matGroup"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"默认仓库"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.goodsData.length,placeholder:"请选择仓库",size:"small"},model:{value:t.mainData.matStore,callback:function(e){t.$set(t.mainData,"matStore",e)},expression:"mainData.matStore"}},t._l(t.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value},on:{focus:t.setMinWidthEmpty}})})),1)],1)],1)],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}}),a("div",[a("el-row",[a("el-button-group",[a("el-button",{attrs:{type:"primary",icon:"el-icon-folder-add",size:"mini"},on:{click:t.showImport}},[t._v(" 文件导入 ")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-brush",size:"mini",disabled:0==t.goodsData.length},on:{click:t.checkGoodsUid}},[t._v(" 识别编码 ")])],1)],1),a("el-table",{ref:"multipleTable",staticClass:"tb-edit tableBox",staticStyle:{overflow:"auto"},attrs:{data:t.goodsData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"0px 0px"},"row-style":{height:"40px"},height:"40vh"}},[a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsuid))])]}}])}),a("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsname))])]}}])}),a("el-table-column",{attrs:{label:"货品规格",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsspec))])]}}])}),a("el-table-column",{attrs:{label:"货品状态",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsstate))])]}}])}),a("el-table-column",{attrs:{label:"单位",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsunit))])]}}])}),a("el-table-column",{attrs:{label:"安全库存",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.safestock))])]}}])}),a("el-table-column",{attrs:{label:"当前库存",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.ivquantity))])]}}])}),a("el-table-column",{attrs:{label:"材质",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.material))])]}}])}),a("el-table-column",{attrs:{label:"条形码",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.barcode))])]}}])})],1)],1),a("div",{staticStyle:{margin:"15px 0 0 0",height:"45px"}},[a("el-button",{staticStyle:{float:"right","margin-right":"20px"},attrs:{type:"primary"},on:{click:t.submitGoods}},[t._v("确 定")]),a("el-button",{staticStyle:{float:"right","margin-right":"20px"},on:{click:t.closeDialog}},[t._v("关 闭")])],1)],1),a("el-dialog",{attrs:{title:"货品信息",width:"400px",visible:t.importVisble,"append-to-body":""},on:{"update:visible":function(e){t.importVisble=e}}},[a("div",[a("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{drag:!0,action:"",multiple:!1,"on-change":t.handleChange,"on-remove":t.handleRemove,"on-preview":t.handlePreview,"on-success":t.handleSuccess,limit:t.limitUpload,"auto-upload":!1,accept:".xlsx,.xls,.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[t._v("上传文件")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[a("p",[t._v(" 只能上传xlsx / xls文件"),a("el-button",{attrs:{type:"text"},nativeOn:{click:function(e){return t.modelExport(e)}}},[t._v("下载模板")])],1)])])],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.importf}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.importVisble=!1}}},[t._v("取 消")])],1)])],1)},nt=[],lt=a("c7eb"),ct=a("1da1"),dt=(a("b0c0"),a("820e"),{data:function(){return{limitUpload:1,fileTemp:{},staffVisible:!1,goodsData:[],formdata:{ageprice:0,barcode:"",batchmg:0,batchonly:0,createdate:new Date,modifydate:new Date,deletelister:"",enabledmark:1,goodsname:"",goodspinyin:"",goodsspec:"",goodsstate:"",goodsuid:"",goodsunit:"",groupid:"",inprice:0,ivquantity:0,material:"",outprice:0,partid:"",pid:"",puid:"",remark:"",safestock:0,storeid:"",storelistguid:"",storelistname:"",surface:"",uidgroupcode:"",uidgroupguid:"",uidgroupname:"",uidgroupnum:0,versionnum:"",virtualitem:0,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},formLabelWidth:"100px",menuData:[],defaultProps:{children:"children",label:"label",value:"id"},mainData:{matGroup:"",matStore:""},options:[{value:"临时id",label:"仓库"}],importVisble:!1,failcount:0,allowUpload:!1}},created:function(){this.bindData(),this.BindStoreData()},methods:{bindData:function(){var t=this;return Object(ct["a"])(Object(lt["a"])().mark((function e(){var a;return Object(lt["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a={PageNum:1,PageSize:500,OrderType:1,SearchType:1},t.listLoading=!0,e.next=4,r["a"].post("/D91M01S1/getPageList",JSON.stringify(a)).then((function(e){if(200==e.data.code){t.test=e.data.data.list,console.log("实际读取的list",t.test);var a=e.data.data.list.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname}})),o=Object(s["a"])(a);t.menuData=t.transData(o,"id","pid","children"),console.log("this.menuData",t.menuData)}}));case 4:case"end":return e.stop()}}),e)})))()},BindStoreData:function(){var t=this,e={PageNum:1,PageSize:500,OrderType:1,SearchType:1};r["a"].post("/D04M21S1/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(console.log(e.data.data.list),t.options=e.data.data.list.map((function(t){return{value:t.id,label:t.storename}})))})).catch((function(t){}))},handleChange:function(t,e){this.fileTemp=t.raw;var a=t.raw.name,o=a.substring(a.lastIndexOf(".")+1);this.fileTemp?"xlsx"==o||"xls"==o||this.$message({type:"warning",message:"文件格式错误，请删除后重新上传！"}):this.$message({type:"warning",message:"请上传文件！"})},importf:function(t){var e=this,a=this,o=(this.$refs.inputer,this.fileTemp),i=new FormData;i.append("file",o),r["a"].post("/D91M01B1/importExecl",i,{headers:{"Content-Type":"multipart/form-data"}}).then((function(t){if(console.log("文件导入",t),200==t.data.code){e.$message.success("文件导入成功"),e.importVisble=!1;var o=t.data.data.map((function(t){return t.storeid=a.mainData.matStore,t.uidgroupguid=a.mainData.matGroup[a.mainData.matGroup.length-1],t}));e.goodsData=o}else e.$message.warning("文件导入失败，请重试")}))},handlePreview:function(t){console.log(t)},handleSuccess:function(t,e){console.log("handleSuccess",t)},handleRemove:function(t,e){console.log("handleRemove",t)},setMinWidthEmpty:function(t){var e=document.getElementsByClassName("el-select-dropdown__empty");e.length>0&&(e[0].style["min-width"]=t.srcElement.clientWidth+2+"px")},transData:function(t,e,a,o){for(var i=[],s={},r=e,n=a,l=o,c=0,d=0,u=t.length;c<u;c++)s[t[c][r]]=t[c];for(;d<u;d++){var m=t[d],p=s[m[n]];p?(!p[l]&&(p[l]=[]),p[l].push(m)):i.push(m)}return i},changeFormat:function(t){var e=[];if(!Array.isArray(t))return e;var a={};return t.forEach((function(t){a[t.id]=t})),t.forEach((function(t){var o=a[t.navpid];o?(o.children||(o.children=[])).push(t):e.push(t)})),e},handleCascader:function(t){console.log(t)},showImport:function(){this.importVisble=!0},modelExport:function(){r["a"].get("/D91M01B1/exportModel",{responseType:"blob"}).then((function(t){console.log(t);var e=document.createElement("a"),a=new Blob([t.data],{type:"application/vnd.ms-excel"});e.style.display="none",e.href=URL.createObjectURL(a),e.download="货品信息模板.xls",document.body.appendChild(e),e.click()})).catch((function(t){console.log(t)}))},checkGoodsUid:function(){var t=this;return Object(ct["a"])(Object(lt["a"])().mark((function e(){var a,o,i,s,r;return Object(lt["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(a=t,o=0,i=[],s=0;s<a.goodsData.length;s++)r=new Promise((function(e,i){var r={goodsname:a.goodsData[s].goodsname,goodsspec:a.goodsData[s].goodsspec,partid:a.goodsData[s].partid?a.goodsData[s].partid:""},n=s;d.checkGoodsUid(r).then((function(a){console.log("res.data",!!a.data),a.data?(t.goodsData[n].goodsid=a.data.id,t.goodsData[n].goodsuid=a.data.goodsuid,t.goodsData[n].isNew=!1,e(a.data)):(o++,t.goodsData[n].isNew=!0,i("新货品不在货品表内"))})).catch((function(t){console.log(t),i("查询失败")}))})),i.push(r);return e.next=6,Promise.allSettled(i).then((function(t){console.log(t)})).catch((function(t){})).finally((function(){console.log(o),a.$message("识别完毕,共".concat(a.goodsData.length,"条,需导入新货品").concat(o,"条")),t.allowUpload=!0}));case 6:case"end":return e.stop()}}),e)})))()},submitGoods:function(){var t=this;return Object(ct["a"])(Object(lt["a"])().mark((function e(){var a,o,i,s,r;return Object(lt["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.allowUpload){e.next=2;break}return e.abrupt("return",t.$message("请先在选择仓库和货品分组，导入货品再保存"));case 2:console.log("执行逐个保存"),a=t,o=0,[],i=0,s=Object(lt["a"])().mark((function t(){var e;return Object(lt["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a.goodsData[r].isNew){t.next=32;break}return a.formdata.goodsname=a.goodsData[r].goodsname,a.formdata.goodsuid=a.goodsData[r].goodsuid,a.formdata.goodsspec=a.goodsData[r].goodsspec,a.formdata.goodsunit=a.goodsData[r].goodsunit,a.formdata.goodsstate=a.goodsData[r].goodsstate,a.formdata.storeid=a.goodsData[r].storeid,a.formdata.uidgroupguid=a.goodsData[r].uidgroupguid,a.formdata.partid=a.goodsData[r].partid,a.formdata.custom1=a.goodsData[r].custom1,a.formdata.custom2=a.goodsData[r].custom2,a.formdata.custom3=a.goodsData[r].custom3,a.formdata.custom4=a.goodsData[r].custom4,a.formdata.custom5=a.goodsData[r].custom5,a.formdata.custom6=a.goodsData[r].custom6,a.formdata.custom7=a.goodsData[r].custom7,a.formdata.custom8=a.goodsData[r].custom8,a.formdata.custom9=a.goodsData[r].custom9,a.formdata.custom10=a.goodsData[r].custom10,a.formdata.brandname=a.goodsData[r].brandname,a.formdata.ageprice=a.goodsData[r].ageprice?a.goodsData[r].ageprice:0,a.formdata.barcode=a.goodsData[r].barcode?a.goodsData[r].barcode:"",a.formdata.batchmg=a.goodsData[r].batchmg?a.goodsData[r].batchmg:0,a.formdata.batchonly=a.goodsData[r].batchonly?a.goodsData[r].batchonly:0,a.formdata.enabledmark=a.goodsData[r].enabledmark?a.goodsData[r].enabledmark:1,a.formdata.inprice=a.goodsData[r].inprice?a.goodsData[r].inprice:0,a.formdata.ivquantity=a.goodsData[r].ivquantity?a.goodsData[r].ivquantity:0,a.formdata.material=a.goodsData[r].material?a.goodsData[r].material:"",a.formdata.outprice=a.goodsData[r].outprice?a.goodsData[r].outprice:0,e=r,t.next=32,d.importEntity(a.formdata).then((function(t){console.log("保存结果",t),console.log("完成",i),200==t.code?a.goodsData.splice(e,1):console.log("保存失败")})).catch((function(t){o++,console.log(o,"保存失败")}));case 32:case"end":return t.stop()}}),t)})),r=a.goodsData.length-1;case 9:if(!(r>=0)){e.next=14;break}return e.delegateYield(s(),"t0",11);case 11:r--,e.next=9;break;case 14:0==a.goodsData.length&&(a.$emit("closeDialog"),a.$emit("bindData"));case 15:case"end":return e.stop()}}),e)})))()},closeDialog:function(){this.$emit("closeDialog")}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),o=(e.getMonth()+1).toString().padStart(2,"0"),i=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),n=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(o,"-").concat(i," ").concat(s,":").concat(r,":").concat(n)}}}}),ut=dt,mt=(a("030e"),Object(y["a"])(ut,rt,nt,!1,null,"f834f466",null)),pt=mt.exports,ft=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("div",{staticClass:"flex a-c"},[a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1),a("div",{staticStyle:{"margin-right":"40px"}},[a("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)]),a("PrintServer",{ref:"PrintServer",attrs:{formdata:t.selectList,printcode:"D91M01B1Edit",commonurl:"/D91M01B1/printBatchBill",weburl:"/D91M01B1/printBatchWebBill"}}),a("PrintServer",{ref:"PrintPageServer",attrs:{formdata:t.lst,queryParams:t.queryParams,printcode:"D91M01B1Th",commonurl:"/D91M01B1/printPageList",weburl:"/D91M01B1/printWebPageList"}})],1)},ht=[],gt=a("b85c"),bt=(a("caad"),a("a9e3"),a("2532"),a("c7cd"),{formcode:"D91M01B1List",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"100",displaymark:1,overflow:1,sortable:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsstate",itemname:"货品状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsstate"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"safestock",itemname:"安全库存",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Mat_Goods.safestock"},{itemcode:"ivquantity",itemname:"当前库存",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Mat_Goods.ivquantity"},{itemcode:"barcode",itemname:"条形码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.barcode"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1},{itemcode:"packsn",itemname:"货品SN",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.packsn"},{itemcode:"modifydate",itemname:"修改日期",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Mat_Goods.modifydate"},{itemcode:"operate",itemname:"操作",minwidth:"150",defwidth:"150px",displaymark:1,overflow:1}]}),vt={components:{},props:["online","formtemplate"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:bt,customList:[],selectList:[],goodsInfo:{},totalfields:["refno"],exportitle:"货品信息",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){var a=e.row,o=(e.isSelected,e.selectedRowKeys);if(t.checkboxOption.selectedRowKeys=o,o.includes(a.id))t.selectList.push(a);else{var i=t.selectList.findIndex((function(t){return t.id==a.id}));-1!=i&&t.selectList.splice(i,1)}},selectedAllChange:function(e){var a=e.isSelected,o=e.selectedRowKeys;if(a){if(t.checkboxOption.selectedRowKeys=o,0!=o.length)for(var i=0;i<o.length;i++)t.selectList.push({id:o[i]})}else t.selectList=[],t.checkboxOption.selectedRowKeys=[]}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var a=e.startRowIndex;t.rowScroll=a}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var a=this.tableForm.item[e];a.displaymark&&(t+=Number(a.minwidth))}}return t}},methods:{bindData:function(){var t=this;this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1));var e="/D91M01B1/getPageList";r["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(ct["a"])(Object(lt["a"])().mark((function e(){var a;return Object(lt["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=bt,t.formtemplate.list.type&&(a.item=t.formtemplate.list.content),t.$getColumn(t.tableForm.formcode,a).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,a=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,o){var i={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(a,o){var i=a.row,s=(a.column,a.rowIndex),r=null;return"createdate"==t.itemcode||"modifydate"==t.itemcode?e.$options.filters.dateFormats(i[t.itemcode]):"goodsuid"==t.itemcode?(r=o("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(i.id)}}},[i[t.itemcode]?i[t.itemcode]:"货品编码"]),r):"ivquantity"==t.itemcode?(r=o("div",[o("el-popover",{attrs:{placement:"left",trigger:"click",title:"货品信息"}},[o("div",{directives:[{name:"show",value:!!e.goodsInfo}]},[o("div",{class:"ellipsis"},[o("i",{class:"el-icon-discount",style:"color: #909399"}),"货品编码：",e.goodsInfo.goodsuid]),o("div",{class:"ellipsis"},[o("i",{class:"el-icon-coin",style:"color: #909399"}),"货品名称：",e.goodsInfo.goodsname]),o("div",[o("i",{class:"el-icon-s-home",style:"color: #409eff"}),"仓库名称：",e.goodsInfo.storename]),o("div",[o("i",{class:"el-icon-s-order",style:"color: #409eff"}),"账面库存：",e.goodsInfo.quantity]),o("div",[o("i",{class:"el-icon-s-data",style:"color: #6dc76a"}),"可用数量：",e.goodsInfo.stoqty]),o("div",[o("i",{class:"el-icon-circle-plus",style:"color: #6dc76a"}),"采购待入：",e.goodsInfo.buyremqty]),o("div",[o("i",{class:"el-icon-remove",style:"color: #F56C6C"}),"订单待用：",e.goodsInfo.busremqty]),o("div",[o("i",{class:"el-icon-remove",style:"color: #F56C6C"}),"领料待出：",e.goodsInfo.requremqty])]),o("div",{directives:[{name:"show",value:!e.goodsInfo}],class:"noData",style:"font-size: 18px"},["暂无内容"]),o("span",{slot:"reference",class:"textunderline",on:{click:function(){return e.showData(i,s)}},style:i.ivquantity<i.safestock?"color:#f02423;font-weight:bold":""},[i[t.itemcode]])])]),r):i[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),a.push(i)})),a.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),a.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,a){t.row,t.column;var o=t.rowIndex;return o+e.rowScroll+1}}),this.customData=a,this.keynum+=1},pagePrint:function(){this.$refs.PrintPageServer.printButton(1,1)},btnPrint:function(){this.$refs.PrintServer.printButton(2,1)},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,a=["baseprice","rebate","price","pricemin","pricemax"];this.$countCellData(this,a,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={goodsname:t,goodsspec:t,uidgroupguid:t,goodsuid:t,partid:t}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.queryParams.scenedata=t,""==t[0].field?this.queryParams.SearchType=1:this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var a={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(a,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)},showData:function(t){var e=this,a={PageNum:1,PageSize:10,OrderType:1,SearchType:1};a.SearchPojo={goodsuid:t.goodsuid},r["a"].post("/D04M04B1/getMachMatQtyPageListByGoods?key="+t.id,JSON.stringify(a)).then((function(t){e.goodsInfo=t.data.data.list[0]}))},groupsearch:function(t){""!=t?this.queryParams.SearchPojo={partgroupid:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},allDelete:function(){var t=this;0!=this.selectList.length?this.$confirm("此操作将永久删除记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){})):this.$message.warning("请先选择货品")},deleteRows:function(t,e){var a=this;return Object(ct["a"])(Object(lt["a"])().mark((function t(){var e,o,i,s,n;return Object(lt["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a,e=a.selectList,!e){t.next=22;break}o=[],i=Object(gt["a"])(e),t.prev=5,n=Object(lt["a"])().mark((function t(){var e,a;return Object(lt["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=s.value,a=new Promise((function(t,a){r["a"].get("/D91M01B1/delete?key=".concat(e.id)).then((function(o){200==o.data.code?0==o.data.data?a("删除失败,"+e.goodsname+"在系统中已使用"):t("删除成功"):a("删除失败")})).catch((function(t){a("删除失败")}))})),o.push(a);case 3:case"end":return t.stop()}}),t)})),i.s();case 8:if((s=i.n()).done){t.next=12;break}return t.delegateYield(n(),"t0",10);case 10:t.next=8;break;case 12:t.next=17;break;case 14:t.prev=14,t.t1=t["catch"](5),i.e(t.t1);case 17:return t.prev=17,i.f(),t.finish(17);case 20:return t.next=22,Promise.all(o).then((function(t){a.$message.success("删除成功")})).catch((function(t){a.$message.warning(t)})).finally((function(){a.selectList=[],a.checkboxOption.selectedRowKeys=[],a.bindData()}));case 22:case"end":return t.stop()}}),t,null,[[5,14,17,20]])})))()}}},yt=vt,wt=(a("cf28"),Object(y["a"])(yt,ft,ht,!1,null,"090e9ef8",null)),xt=wt.exports,St={name:"D91M01B1",components:{ListHeader:st,FormEdit:R,FormAdd:Z,Export:pt,TableList:xt},data:function(){return{lst:[],formvisible:!1,idx:0,total:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},treetitle:"货品分组",gropuformvisible:!1,treevisble:!0,groupData:[],treeeditable:!1,pid:0,exportvisible:!1,selectList:[],exportInfoVisble:!1,exportInfo:{PageNum:1,PageSize:100},showhelp:!1,goodsInfo:{},tableForm:{},formtemplate:C,passkey:{}}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},created:function(){this.passkey=JSON.parse(localStorage.getItem("getInfo")).registrkey?JSON.parse(JSON.parse(localStorage.getItem("getInfo")).registrkey):{},this.bindTreeData()},mounted:function(){this.bindTemp(),this.bindData()},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D91M01B1").then((function(e){200==e.data.code?(null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):C),t.$nextTick((function(){t.$refs.tableList.getColumn()}))):t.$alert(e.data.msg||"获取页面信息失败")})).catch((function(e){t.$message.error("请求错误")}))},bindData:function(){this.$refs.tableList.bindData()},bindTreeData:function(){var t=this,e={PageNum:1,PageSize:1e4,OrderType:0,SearchType:1,OrderBy:"rownum"};r["a"].post("/D91M01S1/getPageList",JSON.stringify(e)).then((function(e){if(200==e.data.code){var a=e.data.data.list.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname,prefix:t.prefix}})),o=[{id:"0",pid:-1,label:t.treetitle,prefix:!1}],i=[].concat(Object(s["a"])(a),o);t.groupData=t.transData(i,"id","pid","children")}})).catch((function(t){console.log(t)}))},sendTableForm:function(t){this.tableForm=t},Export:function(){this.exportInfoVisble=!0},submitExport:function(){var t=this,e={PageNum:this.exportInfo.PageNum,PageSize:this.exportInfo.PageSize,OrderType:1,SearchType:1};r["a"].post("/D91M01B1/exportList",JSON.stringify(e),{responseType:"blob"}).then((function(e){t.$message.success("导出成功");var a=document.createElement("a"),o=new Blob([e.data],{type:"application/vnd.ms-excel"});a.style.display="none",a.href=URL.createObjectURL(o),a.download="货品信息",document.body.appendChild(a),a.click(),t.exportInfoVisble=!1}))},showForm:function(t){this.idx=t,this.formvisible=!0},showGroupform:function(t){this.idx=t,this.gropuformvisible=!0},closeForm:function(){this.formvisible=!1,this.gropuformvisible=!1},compForm:function(){this.bindTreeData(),this.bindData(),this.formvisible=!1,this.gropuformvisible=!1},transData:function(t,e,a,o){for(var i=[],s={},r=e,n=a,l=o,c=0,d=0,u=t.length;c<u;c++)s[t[c][r]]=t[c];for(;d<u;d++){var m=t[d],p=s[m[n]];p?(!p[l]&&(p[l]=[]),p[l].push(m)):i.push(m)}return i},handleNodeClick:function(t){if(0==t.id)this.$refs.tableList.search("");else{var e=t.id;this.$refs.tableList.search(e)}},treeEdit:function(){console.log(10),this.treeeditable=!this.treeeditable},editTreeNode:function(t){this.pid=t.pid,this.showGroupform(t.id)},addTreeChild:function(t){this.pid=t.id,this.showGroupform(0)},delTreeNode:function(t){var e=this;t.children?this.$message({message:"警告,该节点下存在子节点不能删除",type:"warning"}):this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),W.delete(t.id).then((function(){e.$message.success("删除成功！"),e.bindTreeData()})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},changeIdx:function(t){this.idx=t},btnImport:function(){this.exportvisible=!0},submitExPort:function(){console.log(this.$refs.exportFile),this.$refs.exportFile.importf()},modelExport:function(){r["a"].get("/D91M01B1/exportModel",{responseType:"blob"}).then((function(t){console.log(t);var e=document.createElement("a"),a=new Blob([t.data],{type:"application/vnd.ms-excel"});e.style.display="none",e.href=URL.createObjectURL(a),e.download="货品信息模板.xls",document.body.appendChild(e),e.click()})).catch((function(t){console.log(t)}))}}},kt=St,Dt=(a("6456"),Object(y["a"])(kt,o,i,!1,null,"1e6c214a",null));e["default"]=Dt.exports},ce62:function(t,e,a){"use strict";a("a55b")},cf28:function(t,e,a){"use strict";a("1e8d")},ee58:function(t,e,a){"use strict";a("8885")}}]);