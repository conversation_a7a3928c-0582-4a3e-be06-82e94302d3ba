(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-073430db"],{"0733":function(t,e,i){"use strict";i("549c")},"09c6":function(t,e,i){"use strict";i("b75e")},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"1bb0":function(t,e,i){"use strict";i("8e86")},2265:function(t,e,i){"use strict";i("4e39")},"233f":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{},[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",{staticStyle:{"margin-bottom":"10px"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,height:350,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"1px 0px 1px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"55"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"账户名称",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.accountname)+" ")]}}])}),i("el-table-column",{attrs:{label:"账户类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.accounttype))])]}}])}),i("el-table-column",{attrs:{label:"当前金额",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.currentamt))])]}}])}),i("el-table-column",{attrs:{label:"备注",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.remark))])]}}])}),i("el-table-column",{attrs:{label:"创建时间",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormat")(e.row.createdate)))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},o=[],n=(i("e9c4"),i("b775")),r=i("333d"),s={components:{Pagination:r["a"]},props:["multi"],data:function(){return{title:"出纳账户",listLoading:!0,lst:[],strfilter:"",radio:"",selrows:"",total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},watch:{},created:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,n["a"].post("/D07M21B2/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={accountname:t,accounttype:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},l=s,d=i("2877"),c=Object(d["a"])(l,a,o,!1,null,"0e79971c",null);e["a"]=c.exports},"31ae":function(t,e,i){},"4e39":function(t,e,i){},"4f5e":function(t,e,i){},"501c":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},o=[],n=i("c7eb"),r=i("1da1"),s=(i("caad"),i("e9c4"),i("a9e3"),i("d3b7"),i("2532"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),i("b775")),l=i("3d5d"),d=i("b893"),c={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"d85f"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:l["c"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={citecode:this.searchVal};this.queryParams.SearchPojo=e;var i="/D03M06B1/getPageTh";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(d["d"])()[0],EndDate:Object(d["d"])()[1]}),s["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total)})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(r["a"])(Object(n["a"])().mark((function e(){return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(l["c"].formcode,l["c"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var n=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return n}if("billtype"==t.itemcode){n=a("span",[o[t.itemcode]]);return o[t.itemcode].includes("红冲")&&(n=a("span",{style:"color:#f44336"},[o[t.itemcode]])),n}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxamount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){console.log(t,"222"),this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},m=c,u=(i("5880"),i("2877")),h=Object(u["a"])(m,a,o,!1,null,"7c16eaee",null);e["a"]=h.exports},"549c":function(t,e,i){},5713:function(t,e,i){"use strict";i("31ae")},5880:function(t,e,i){"use strict";i("4f5e")},"5c1e":function(t,e,i){},"5c73":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{},[i("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?i("ul",{},t._l(t.lst,(function(e,a){return i("li",{key:a,class:t.radio==a?"active":"",on:{click:function(i){t.getCurrentRow(e),t.radio=a}}},[i("span",[t._v(t._s(e.dictvalue))])])})),0):i("div",{staticClass:"noData"},[t._v("暂无数据")])]),i("div",{staticClass:"foots"},[i("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),i("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?i("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[i("div",{staticClass:"dialog-body customDialog"},[i("div",{staticClass:"left"},t._l(t.lst,(function(e,a){return i("p",{key:a,class:t.ActiveIndex==a?"isActive":"",on:{click:function(e){t.ActiveIndex=a}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),i("div",{staticClass:"right"},[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),i("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},o=[],n=(i("a434"),i("e9c4"),i("b775")),r=i("333d"),s=i("b0b8"),l={components:{Pagination:r["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],n["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var i=0;i<t.formdata.item.length;i++)t.lst.push(t.formdata.item[i])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.addItem(i)})).catch((function(t){console.log(t)}))},addItem:function(t){s.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:s.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.lst[t.ActiveIndex].dictvalue=i,s.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=s.getFullChars(i)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,n["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},d=l,c=(i("af2b"),i("2877")),m=Object(c["a"])(d,a,o,!1,null,"d2ba3d7a",null);e["a"]=m.exports},"7de4":function(t,e,i){},"841c":function(t,e,i){"use strict";var a=i("d784"),o=i("825a"),n=i("1d80"),r=i("129f"),s=i("14c3");a("search",1,(function(t,e,i){return[function(e){var i=n(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,i):new RegExp(e)[t](String(i))},function(t){var a=i(e,t,this);if(a.done)return a.value;var n=o(t),l=String(this),d=n.lastIndex;r(d,0)||(n.lastIndex=0);var c=s(n,l);return r(n.lastIndex,d)||(n.lastIndex=d),null===c?-1:c.index}]}))},"8e86":function(t,e,i){},"93dc":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[i("ListHeader",{directives:[{name:"show",rawName:"v-show",value:!t.isDialog,expression:"!isDialog"}],attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,changeModelUrl:t.changeModelUrl,btnExport:t.btnExport,changeBalance:t.changeBalance,pagePrint:t.pagePrint,btnPrint:function(e){return t.$refs.tableTh.btnPrint()},bindColumn:function(e){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()}}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{attrs:{span:t.showhelp?20:24}},[i("TableTh",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],ref:"tableTh",attrs:{online:t.online,searchVal:t.searchVal,isDialog:t.isDialog,formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}}),i("TableList",{directives:[{name:"show",rawName:"v-show",value:!t.thorList,expression:"!thorList"}],ref:"tableList",attrs:{online:t.online,searchVal:t.searchVal,isDialog:t.isDialog,formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}})],1)],1)],1)],1)])},o=[],n=(i("b64b"),i("ac1f"),i("841c"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container flex a-c j-s"},[i("div",{staticStyle:{display:"flex"}},[i("div",{staticClass:"flex infoForm a-c"},[i("span",{staticClass:"infoForm-Title"},[t._v("时间范围")]),i("el-date-picker",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"unlink-panels":"",size:"mini","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")]),i("el-button",{attrs:{size:"mini",icon:"el-icon-printer",title:"打印列表"},on:{click:function(e){return t.$emit("pagePrint")}}},[t._v("打印")]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:"el-icon-printer",plain:"",size:"mini",title:"打印单据"},on:{click:function(e){return t.$emit("btnPrint")}}},[t._v(" 单据 ")])],1),i("div",{staticClass:"iShowBtn"},[i("div",{staticStyle:{display:"inline-block"}},[i("el-switch",{staticStyle:{"margin-right":"10px"},attrs:{"inactive-color":"#13ce66"},on:{change:t.changeModelUrl},model:{value:t.thorList,callback:function(e){t.thorList=e},expression:"thorList"}}),i("span",{staticStyle:{"margin-right":"10px","font-size":"14px"}},[t._v(t._s(t.thorList?"单据":"明细"))])],1),i("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"结存",size:"mini",border:""},on:{change:t.changeBalance},model:{value:t.balance,callback:function(e){t.balance=e},expression:"balance"}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),r=[],s=i("b893"),l={name:"ListHeader",props:["tableForm"],data:function(){return{strfilter:"",formdata:{},dateRange:Object(s["d"])(),pickerOptions:Object(s["h"])(),thorList:!0,balance:!1,setColumsVisible:!1,searchVisible:!1}},methods:{advancedSearch:function(t){var e={dateRange:this.dateRange,formdata:t};this.$emit("advancedSearch",e),this.searchVisible=!1},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){var t={dateRange:this.dateRange,strfilter:this.strfilter};this.$emit("btnSearch",t)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList),this.$emit("bindColumn")},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},changeBalance:function(t){var e=0;e=t?1:0,this.$emit("changeBalance",e)}}},d=l,c=(i("e341"),i("2877")),m=Object(c["a"])(d,n,r,!1,null,"f1fffe60",null),u=m.exports,h=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.receipted>0&&t.formdata.receipted>=t.formdata.taxamount&&0==t.formdata.closed,revoke:!!t.formdata.disannulmark,yizhongzhi:1==t.formdata.closed,beoverdue:t.formdata.receipted<t.formdata.taxamount&&(new Date).getTime()>new Date(t.formdata.aimdate).getTime()&&0==t.formdata.closed,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,computerTime:t.computerTime,getProjectName:t.getProjectName,autoClearProject:t.autoClearProject}})],1)]},proxy:!0},{key:"Item",fn:function(){return["付款申请"==t.formdata.billtype?i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1):t._e()]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D01M05B1Edit",commonurl:"/D01M05B1/printBill",weburl:"/D01M05B1/printWebBill",modelurl:"/SaReports/getListByModuleCode"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D01M05B1Edit",examineurl:"/D01M05B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},[i("D03M06B1",{ref:"D03M06B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D07M14B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1}}})],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"close-on-click-modal":!1,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D07M14B1"==t.processModel?i("D03M06B1List",{ref:"D03M06B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},f=[],p=(i("caad"),i("a9e3"),i("d3b7"),i("2532"),i("159b"),i("b775"));const b={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);p["a"].post("/D07M14B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);p["a"].post("/D07M14B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){p["a"].get("/D07M14B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,p["a"].get("/D07M14B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D07M14B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D07M14B1/closed?type="+(3==t?1:0);p["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var g=b,v=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[],t.formdata.taxamount=0}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"付款申请",value:"付款申请"}}),i("el-option",{attrs:{label:"其他付款申请",value:"其他付款申请"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"付款方式"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入付款方式",clearable:"",size:"small"},model:{value:t.formdata.payment,callback:function(e){t.$set(t.formdata,"payment",e)},expression:"formdata.payment"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("suppliers")}}},[i("el-form-item",{attrs:{label:"往来单位",prop:"suppliers"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.suppliers,baseurl:"/D01M01R1/getOnlinePageList",type:"往来单位"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("suppliers")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"经办人"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入经办人",clearable:"",size:"small"},model:{value:t.formdata.operator,callback:function(e){t.$set(t.formdata,"operator",e)},expression:"formdata.operator"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"申请部门"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入申请部门",clearable:"",size:"small"},model:{value:t.formdata.applicantdept,callback:function(e){t.$set(t.formdata,"applicantdept",e)},expression:"formdata.applicantdept"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("projname")}}},[i("el-form-item",{attrs:{label:"项目名称",prop:"projname"}},[i("ProjectAutoComplete",{attrs:{type:"项目名称",value:t.formdata.projname,params:{name:"projname",other:"projcode"}},on:{setRow:function(e){t.$emit("getProjectName",e),t.cleValidate("projname")},autoClear:function(e){return t.$emit("autoClearProject")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"项目编码"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入项目编码",clearable:"",size:"small"},model:{value:t.formdata.projcode,callback:function(e){t.$set(t.formdata,"projcode",e)},expression:"formdata.projcode"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"付款计划"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.aimdate,callback:function(e){t.$set(t.formdata,"aimdate",e)},expression:"formdata.aimdate"}})],1)],1),i("el-col",{attrs:{span:9}},[i("div",{staticStyle:{display:"flex"}},[i("el-form-item",{attrs:{label:"付款金额"}},["付款申请"!=t.formdata.billtype?i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入付款金额",size:"small"},model:{value:t.formdata.amount,callback:function(e){t.$set(t.formdata,"amount",e)},expression:"formdata.amount"}}):i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v(" ￥"+t._s(t.formdata.amount?t.formdata.amount:0))])],1),i("el-form-item",{attrs:{label:"已付金额"}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.receipted?t.formdata.receipted:0))])])],1)])],1)],1)},y=[],w={props:{formdata:{type:Object},title:{type:String}},data:function(){return{formRules:{billtitle:[{required:!0,trigger:"blur",message:"单据标题为必填项"}],suppliers:[{required:!0,trigger:"blur",message:"往来单位为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},x=w,k=(i("f01c"),Object(c["a"])(x,v,y,!1,null,"294bccc6",null)),S=k.exports,D=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["moveup","movedown","delete","copyrow","refresh","billstate"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd},scopedSlots:t._u([{key:"left",fn:function(){return[i("el-button",{attrs:{disabled:2==t.formstate,type:"primary",size:"mini",icon:"el-icon-circle-plus-outline"},nativeOn:{click:function(e){return t.getAdd()}}},[t._v("发票")])]},proxy:!0},{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"border-x":"","border-y":"","border-around":!0,"checkbox-option":t.checkboxOption,"column-width-resize-option":t.columnWidthResizeOption,editOption:t.editOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"发票信息","append-to-body":!0,visible:t.selordervisible,width:"1200px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1,groupid:t.formdata.groupid,selecturl:"/D03M05B1/getOnlinePageTh"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.SelOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},$=[],P=(i("c740"),i("e9c4"),i("5319"),i("c7cd"),{custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",pid:"",remark:"",rownum:0,invoid:"",invobillcode:"",invoamount:0,amount:0,invocode:""}),C={formcode:"D07M14B1Th",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Fm_PayRequest.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Fm_PayRequest.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Fm_PayRequest.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Fm_PayRequest.billdate"},{itemcode:"projname",itemname:"项目名称",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Cms_Project.projname"},{itemcode:"projcode",itemname:"项目编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Cms_Project.projcode"},{itemcode:"invocode",itemname:"发票编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Fm_PayRequest.invocode"},{itemcode:"payment",itemname:"付款方式",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Fm_PayRequest.payment"},{itemcode:"aimdate",itemname:"计划日期",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Fm_PayRequest.aimdate"},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Fm_PayRequest.taxamount"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,datasheet:"Fm_PayRequest.status"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Fm_PayRequest.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Fm_PayRequest.assessor"}]},_={formcode:"D07M14B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Fm_PayRequest.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Fm_PayRequest.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Fm_PayRequest.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Fm_PayRequest.billdate"},{itemcode:"projcode",itemname:"项目编码",minwidth:"100",displaymark:1,overflow:1,datasheet:"Cms_Project.projcode"},{itemcode:"projname",itemname:"项目名称",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Cms_Project.projname"},{itemcode:"applicantdept",itemname:"申请部门",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Fm_PayRequest.applicantdept"},{itemcode:"belong",itemname:"费用归属",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Fm_PayRequest.belong"},{itemcode:"taxamount",itemname:"金额",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Fm_PayRequestItem.taxamount"}]},F={formcode:"D07M14B1Item",item:[{itemcode:"invobillcode",itemname:"发票单据号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"invocode",itemname:"发票编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"invoamount",itemname:"发票金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"amount",itemname:"申请金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},B=i("dd56"),O={name:"Elitem",components:{SelOrder:B["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{listLoading:!1,selordervisible:!1,KouKuanVisible:!1,lst:[],keynum:0,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],setColumsVisible:!1,isEditOk:!0,selecturl:"",tableForm:F,customList:[],editmarkfiles:[],countfiles:["amount","invoamount"],columsData:[],rowStyleOption:{clickHighlight:!1,hoverHighlight:!1,stripe:!1},columnHidden:[],checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},columnWidthResizeOption:{enable:!0,minWidth:10,sizeChange:function(e){var i=e.column,a=e.differWidth,o=e.columnWidth;t.columnResizeInfo.column=i,t.columnResizeInfo.differWidth=a,t.columnResizeInfo.columnWidth=o}},columnResizeInfo:{column:"",differWidth:"",columnWidth:""},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;t.editmarkfiles.includes(a.field)&&t.countfiles.includes(a.field)&&t.changeInput("",i,a.field)}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},footerData:[],cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],n=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=n&&t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[n],Object.keys(o)[1])}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}},copyText:""}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=F;this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(F.formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(e,i){var a=e.row;e.column,e.rowIndex;if("goodsuid"==t.itemcode)return o;var o=i("span",{class:a.disannulmark?"textlinethrough":""},[a[t.itemcode]]);return o}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["rownum","invoamount","amount"]),this.formdata.amount=this.footerData[0].amount,this.formdata.invoamount=this.footerData[0].invoamount},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var n=t[o],r=0;r<i.length;r++){var s=i[r];this.lst[e+o][s]=n[s].replace(/^\s*|\s*$/g,""),this.countfiles.includes(s)&&this.changeInput("",this.lst[e+o],s)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i)},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"pid"),this.$set(e,"closed",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.formdata.groupid?this.selordervisible=!0:this.$message.warning("请选择往来单位")},SelOrder:function(){var t=this.$refs.SelOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},P);a.invoid=i.id,a.invobillcode=i.refno,a.invocode=i.invocode,a.invoamount=i.taxamount,a.amount=i.taxamount,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},T=O,I=(i("2265"),Object(c["a"])(T,D,$,!1,null,"21f7e186",null)),L=I.exports,R={header:{type:0,title:"销售开票",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"付款申请",value:"付款申请"},{label:"其他付款申请",value:"其他付款申请"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""},{col:5,code:"invocode",label:"发票编码",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"往来单位",searchtype:"customer",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"linkman",label:"联系人",type:"input",methods:"",param:""},{col:5,code:"telephone",label:"联系电话",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"invodate",label:"开票日期",type:"date",methods:"computerTime",param:""},{col:5,code:"aimdate",label:"收款计划",type:"date",methods:"",param:""},{col:3,code:"taxamount",label:"发票金额",type:"input",methods:"",param:"",show:"this.formdata.billtype== '其他应收'"},{col:3,code:"taxamount",label:"发票金额",type:"text",methods:"",param:"",show:"this.formdata.billtype== '销售开票'"},{col:2,code:"receipted",label:"已收金额",type:"text",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"salesman",label:"业务员",billcode:"sale.salesman",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}},q=i("dcb4"),M=["id","refno","billtype","billtitle","billdate","projectid","projcode","projname","applicantdept","belong","suppliers","itemname","quantity","price","amount","taxtotal","taxamount","invocode","aimdate","payment","operator","operatorid","summary","createby","createbyid","createdate","lister","listerid","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],j=["id","pid","invoid","invobillcode","invocode","invoamount","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],N={params:M,paramsItem:j},z=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,divided:!0,label:"付款单",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D07M14B1",children:[]}],E=[{show:1,divided:!1,label:"付款单",icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D07M14B1",label:"付款单"},children:[]}],V=i("d85f"),H=i("501c"),A=i("d680"),K={name:"Formedit",components:{FormTemp:q["a"],EditHeader:S,EditItem:L,D03M06B1:V["default"],D03M06B1List:H["a"]},props:["idx","isDialog","initData","billcode"],data:function(){return{title:"付款申请单",operateBar:z,processBar:E,formdata:{assessdate:new Date,assessor:"",billdate:new Date,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[],id:"",billtype:"付款申请",billtitle:"",projectid:"",projcode:"",projname:"",applicantdept:"",belong:"",amount:"",taxamount:"",invocode:"",aimdate:new Date,payment:"",operator:"",summary:"",suppliers:""},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:R,formstate:0,submitting:0,creditdquantity:0,creditduint:"day"}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp(),this.bindData()},mounted:function(){},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D01M05B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):R,t.formtemplate.footer.type||(t.formtemplate.footer=R.footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&p["a"].get("/D07M14B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.suppliers=t.groupname,this.formdata.linkman=t.linkman,this.formdata.telephone=t.telephone,this.formdata.groupid=t.id,this.formdata.deliadd=t.deliveradd,this.creditdquantity=t.creditdquantity,this.creditduint=t.creditduint,this.computerTime()},autoClear:function(t){this.formdata.suppliers="",this.formdata.linkman="",this.formdata.telephone="",this.formdata.groupid="",this.formdata.deliadd="",this.creditdquantity="",this.creditduint=""},submitForm:function(t){var e,i=this;e=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,e.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;i.saveForm()}))},saveForm:function(){var t=this;if("付款申请"==this.formdata.billtype){if(0==this.$refs.elitem.lst.length)return void this.$message.warning("单据内容不能为空");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var e=this.amountFloat(this.formdata.item);this.formdata.invoamount=e.invoamount,this.formdata.amount=e.amount,this.formdata.taxamount=e.amount;var i={item:[]};i=this.$getParam(N,i,this.formdata)}else i=Object.assign({},this.formdata);0==this.idx?g.add(i).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败")})):g.update(i).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败")})),setTimeout((function(){t.submitting=0}),500)},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){g.delete(e)})).catch((function(){}))},approval:function(){g.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?g.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){g.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},computerTime:function(){if(0!=this.creditdquantity){var t=this.formdata.invodate.getTime();"month"==this.creditduint?t+=2592e6*this.creditdquantity:t+=864e5*this.creditdquantity,this.formdata.aimdate=new Date(t)}},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},changeBillType:function(){formdata.item=[]},getProjectName:function(t){this.formdata.projname=t.projname,this.formdata.projectid=t.id,this.formdata.projcode=t.projcode},autoClearProject:function(t){this.formdata.projname="",this.formdata.projectid="",this.formdata.projcode=""},amountFloat:function(t,e){e||(e=2);var i={amount:0,invoamount:0};try{t.forEach((function(t){Object(A["b"])(i.amount+=Number(t.amount),e),Object(A["b"])(i.invoamount+=Number(t.invoamount),e)}))}catch(a){return}return i},billSwitch:function(t){if("D01M06B1"==t){this.formdata.billtitle="【"+this.initData.refno+"】发货单转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.linkman=this.initData.linkman,this.formdata.telephone=this.initData.telephone,this.formdata.item=[];for(var e=0;e<this.initData.item.length;e++){var i=this.initData.item[e],a=Object.assign({},P);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.goodsuid=i.goodsuid,a.partid=i.partid,a.amount=i.amount,a.billqty=i.quantity,a.itemtaxrate=i.itemtaxrate,a.price=i.price,a.quantity=i.quantity,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,a.custpo=i.custpo,a.delidate=i.billdate,a.deliitemid=i.id,a.deliuid=this.initData.refno,a.delitype=this.initData.billtype,a.machitemid=i.machitemid,a.machuid=i.machuid,a.virtualitem=i.virtualitem,a.delitype.includes("退货")&&(a.billqty=-a.billqty,a.quantity=-a.quantity,a.taxamount=-a.taxamount,a.amount=-a.amount),this.formdata.item.push(a)}}}}},W=K,J=(i("0733"),Object(c["a"])(W,h,f,!1,null,"1318bc18",null)),U=J.exports,G=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableTh",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize,pageSizes:[10,20,100,500]},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.selectList,printcode:"D07M14B1Edit",commonurl:"/D07M14B1/printBatchBill",weburl:"/D07M14B1/printBatchWebBill",modelurl:"/SaReports/getListByModuleCode"}}),i("PrintServer",{ref:"PrintPageServer",attrs:{formdata:t.lst,queryParams:t.queryParams,printcode:"D07M14B1Th",commonurl:"/D07M14B1/printPageTh",weburl:"/D07M14B1/printWebPageTh",modelurl:"/SaReports/getListByModuleCode"}})],1)},Y=[],X=i("f07e"),Q=i("f944"),Z={components:{MyPopover:X["a"]},props:["online","formtemplate"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:C,billList:F,billIndex:0,billListData:[],processList:Q["a"],processListIndex:0,processListData:[],selectList:[],totalfields:["refno","taxamount","receipted"],exportitle:"请款单",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectList=i?t.lst:[]}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn()},methods:{bindData:function(){var t=this;if(this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(s["d"])()[0],EndDate:Object(s["d"])()[1]}),this.online)var e="/D07M14B1/getOnlinePageTh";else e="/D07M14B1/getPageTh";p["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this,e=C;this.formtemplate.th.type&&(e.item=this.formtemplate.th.content),this.$getColumn(this.tableForm.formcode,e).then((function(e){t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row,n=(i.column,i.rowIndex);if("billdate"==t.itemcode||"itemplandate"==t.itemcode||"billplandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("groupuid"==t.itemcode){var r=a("GroupInfo",{attrs:{scopeVal:o[t.itemcode],searchUrl:"/D01M01B1/getGeneral?key="+o.groupid}});return r}if("status"==t.itemcode){r="";return o.receipted>=0&&o.receipted<o.taxamount&&(new Date).getTime()<new Date(o.aimdate).getTime()&&0==o.closed?r=a("span",{class:"textborder-blue"},["收款"]):o.receipted>0&&o.receipted>=o.taxamount&&0==o.closed?r=a("span",{class:"textborder-green"},["完成"]):o.receipted<o.taxamount&&(new Date).getTime()>new Date(o.aimdate).getTime()&&0==o.closed?r=a("span",{class:"textborder-red"},["逾期"]):1==o.closed&&(r=a("span",{class:"textborder-grey"},["关闭"])),r}if("refno"==t.itemcode){r=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return r}if("taxamount"==t.itemcode){r="";return r="其他应收"==o.billtype?a("span",[o[t.itemcode]]):a("el-popover",{attrs:{placement:"left",trigger:"click",title:"开票明细"},ref:"'popover-' + scope.$index"},[a("div",{style:"position: relative; min-height: 100px",directives:[{name:"show",value:n==e.billIndex}]},[a(X["a"],{ref:"mypopover",attrs:{tableForm:F,lst:e.billListData}})]),a("span",{slot:"reference",class:"textunderline",on:{click:function(){return e.getBillList(o,n)}}},[o[t.itemcode]])]),r}if("receipted"==t.itemcode){r="";return r=a("el-popover",{attrs:{placement:"left",trigger:"click",title:"收款记录"},ref:"popover-"+n},[a("div",{style:"position: relative; min-height: 100px",directives:[{name:"show",value:n==e.processListIndex}]},[a(X["a"],{ref:"mypopover",attrs:{tableForm:Q["a"],lst:e.processListData}})]),a("span",{slot:"reference",class:"textunderline",on:{click:function(){return e.getprocessListData(o,n)}}},[o[t.itemcode]])]),r}if("assessor"==t.itemcode){r="";return r=o.oaflowmark&&!o.assessor?a("span",{style:"color:#ff9800"},["审核中"]):a("span",[o.assessor]),r}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:40,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.customData=i,this.keynum+=1},pagePrint:function(){this.$refs.PrintPageServer.printButton(1,1)},btnPrint:function(){this.$refs.PrintServer.printButton(2,1)},countCellData:function(){var t=this.$refs.tableTh.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableTh.getRangeCellSelection().selectionRangeIndexes,i=["billtaxamount","billamount","billtaxtotal"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t,1)},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)},getColumnItem:function(){var t=this;this.$getColumn(F.formcode,F).then((function(e){t.mypopoverTable=Object.assign({},e.colList)}))},getBillList:function(t,e){var i=this;this.billIndex=e,p["a"].get("/D07M14B1/getBillEntity?key=".concat(t.id)).then((function(t){200==t.data.code?i.billListData=t.data.data.item:i.billListData=[]}))},getprocessListData:function(t,e){var i=this,a={PageNum:1,PageSize:20,OrderType:1,SearchType:1,SearchPojo:{citecode:t.refno}};this.processListIndex=e,p["a"].post("/D01M08B1/getPageTh",JSON.stringify(a)).then((function(t){200==t.data.code?i.processListData=t.data.data.list:i.processListData=[]}))}}},tt=Z,et=(i("5713"),Object(c["a"])(tt,G,Y,!1,null,"4a35c220",null)),it=et.exports,at=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.lst,queryParams:t.queryParams,printcode:"D07M14B1List",commonurl:"/D07M14B1/printPageList",weburl:"/D07M14B1/printWebPageList",modelurl:"/SaReports/getListByModuleCode"}})],1)},ot=[],nt=i("c7eb"),rt=i("1da1"),st={components:{},props:["online","formtemplate"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:_,customList:[],selectList:[],totalfields:["refno","taxamount","quantity"],exportitle:"请款单明细表",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectList=i?t.lst:[]}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},methods:{bindData:function(){var t=this;if(this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),this.online)var e="/D07M14B1/getOnlinePageList";else e="/D07M14B1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(s["d"])()[0],EndDate:Object(s["d"])()[1]}),p["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],n=0;n<o.length;n++)t.$set(t.lst[i],o[n].key,o[n].value)}else t.$message.warning(response.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(rt["a"])(Object(nt["a"])().mark((function e(){var i;return Object(nt["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=_,t.formtemplate.list.type&&(i.item=t.formtemplate.list.content),t.$getColumn(t.tableForm.formcode,i,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var n=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return n}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},pagePrint:function(){this.$refs.PrintServer.printButton(1)},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxamount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t,1)},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)}}},lt=st,dt=(i("09c6"),Object(c["a"])(lt,at,ot,!1,null,"16eb9cd2",null)),ct=dt.exports,mt={name:"D07M14B1",components:{ListHeader:u,FormEdit:U,TableTh:it,TableList:ct},props:["searchVal","isDialog"],data:function(){return{formvisible:!1,idx:0,total:0,thorList:!0,online:0,tableForm:{},showhelp:!1,formtemplate:R}},mounted:function(){this.bindTemp(),this.bindData()},methods:{bindTemp:function(){var t=this;this.formtemplate=R,this.$nextTick((function(){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()})),this.$request.get("/SaFormCustom/getEntityByCode?key=D01M05B1").then((function(e){200==e.data.code?(null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):R),t.$nextTick((function(){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()}))):t.$alert(e.data.msg||"获取页面信息失败")})).catch((function(e){t.$message.error("请求错误")}))},bindData:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.tableTh.bindData()})):this.$nextTick((function(){t.$refs.tableList.bindData()}))},sendTableForm:function(t){this.tableForm=t},pagePrint:function(){this.thorList?this.$refs.tableTh.pagePrint():this.$refs.tableList.pagePrint()},changeModelUrl:function(t){console.log(t),this.thorList=!!t,this.bindData()},btnExport:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.tableTh.btnExport()})):this.$nextTick((function(){t.$refs.tableList.btnExport()}))},search:function(t){this.thorList?this.$refs.tableTh.search(t):this.$refs.tableList.search(t)},advancedSearch:function(t){this.thorList?this.$refs.tableTh.advancedSearch(t):this.$refs.tableList.advancedSearch(t)},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},changeBalance:function(t){this.online=t,this.bindData()}}},ut=mt,ht=(i("1bb0"),Object(c["a"])(ut,a,o,!1,null,"48a9b52f",null));e["default"]=ht.exports},af2b:function(t,e,i){"use strict";i("7de4")},b75e:function(t,e,i){},e341:function(t,e,i){"use strict";i("5c1e")},f01c:function(t,e,i){"use strict";i("fe80")},f944:function(t,e,i){"use strict";i.d(e,"e",(function(){return a})),i.d(e,"d",(function(){return o})),i.d(e,"b",(function(){return n})),i.d(e,"a",(function(){return r})),i.d(e,"c",(function(){return s}));var a={formcode:"D01M08B1List",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Bus_Receipt.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Receipt.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Bus_Receipt.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Receipt.billdate"},{itemcode:"abbreviate",itemname:"简称",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.abbreviate"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Receipt.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Receipt.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Receipt.invobillcode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Receipt.summary"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Receipt.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Bus_Receipt.assessor"}]},o={formcode:"D01M08BItem",item:[{itemcode:"invobillcode",itemname:"发票单据号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"invocode",itemname:"发票编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"invoamount",itemname:"发票金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},n={formcode:"D01M08B1Cash",item:[{itemcode:"cashaccname",itemname:"出纳账户",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"amount",itemname:"金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D01M08B1Th",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,overflow:1},{itemcode:"groupname",itemname:"客户名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"billamount",itemname:"金额",minwidth:"100",displaymark:1,overflow:1},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1}]},s={formcode:"D01M08B1Cite",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Bus_Receipt.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Receipt.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Bus_Receipt.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Receipt.billdate"},{itemcode:"abbreviate",itemname:"简称",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.abbreviate"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Receipt.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Receipt.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Receipt.invobillcode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Receipt.summary"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Receipt.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Bus_Receipt.assessor"}]}},fe80:function(t,e,i){}}]);