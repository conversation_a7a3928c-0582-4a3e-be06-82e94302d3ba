(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-de15366a"],{"4fc0":function(t,e,a){},cb31:function(t,e,a){"use strict";a("4fc0")},ecac:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"profileBody"},[a("el-row",{attrs:{gutter:10,type:"flex"}},[a("el-col",{attrs:{span:6}},[a("div",{staticClass:"proInfo"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("个人信息")])]),a("div",{staticClass:"text item"},[a("div",{staticClass:"img-content"},[a("div",{staticClass:"img-content-box",on:{click:t.changePhoto}},[a("img",{attrs:{src:"https://oss.aliyuncs.com/aliyun_id_photo_bucket/default_handsome.jpg",alt:""}})])]),a("div",{staticClass:"detail-content"},[a("div",{staticClass:"proInfo-item"},[a("span",[t._v("用户账号")]),a("span",[t._v(t._s(t.listForm.username))])]),a("div",{staticClass:"proInfo-item"},[a("span",[t._v("姓名")]),a("span",[t._v(t._s(t.listForm.realname))])]),a("div",{staticClass:"proInfo-item"},[a("span",[t._v("手机号码")]),a("span",[t._v(t._s(t.listForm.phone||"-"))])]),a("div",{staticClass:"proInfo-item"},[a("span",[t._v("用户邮箱")]),a("span",[t._v(t._s(t.listForm.email||"-"))])])])])])],1)]),a("el-col",{attrs:{span:18}},[a("div",{staticClass:"baseInfo",staticStyle:{height:"100%"}},[a("el-card",{staticClass:"box-card",staticStyle:{height:"100%"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("基本资料")])]),a("div",{staticClass:"text item"},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"基本资料",name:"first"}},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"昵称",prop:"realname"}},[a("el-input",{staticStyle:{"min-width":"140px !important"},attrs:{placeholder:"请输入用户昵称",clearable:""},model:{value:t.formdata.realname,callback:function(e){t.$set(t.formdata,"realname",e)},expression:"formdata.realname"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"手机号码",prop:"mobile"}},[a("el-input",{staticStyle:{"min-width":"140px !important"},attrs:{placeholder:"请输入手机号码",clearable:""},model:{value:t.formdata.phone,callback:function(e){t.$set(t.formdata,"phone",e)},expression:"formdata.phone"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[a("el-input",{staticStyle:{"min-width":"140px !important"},attrs:{placeholder:"请输入邮箱",clearable:""},model:{value:t.formdata.email,callback:function(e){t.$set(t.formdata,"email",e)},expression:"formdata.email"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"性别"}},[a("el-radio",{attrs:{label:0},model:{value:t.formdata.sex,callback:function(e){t.$set(t.formdata,"sex",e)},expression:"formdata.sex"}},[t._v("男")]),a("el-radio",{attrs:{label:1},model:{value:t.formdata.sex,callback:function(e){t.$set(t.formdata,"sex",e)},expression:"formdata.sex"}},[t._v("女")])],1)],1),a("el-col",{attrs:{span:22,offset:2}},[a("el-button",{staticStyle:{"background-color":"#1890ff"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.saveBaseInfo()}}},[t._v(" 保 存 信 息")]),a("el-button",{staticStyle:{"background-color":"#ff4949"},attrs:{type:"danger",size:"small"},on:{click:function(e){return t.closeTag()}}},[t._v("关 闭")])],1)],1)],1)],1),a("el-tab-pane",{attrs:{label:"修改密码",name:"second"}},[a("el-form",{ref:"pwdformdata",staticClass:"custInfo",staticStyle:{"min-height":"200px"},attrs:{model:t.pwdformdata,rules:t.pwdformdataRule,"label-width":"100px","auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"原密码",prop:"originalpassword"}},[a("div",{staticStyle:{position:"relative"}},[a("el-input",{attrs:{placeholder:"请输入原密码",type:t.passwordType1},model:{value:t.pwdformdata.originalpassword,callback:function(e){t.$set(t.pwdformdata,"originalpassword",e)},expression:"pwdformdata.originalpassword"}}),a("span",{staticClass:"show-pwd",on:{click:function(e){return t.showPwd("passwordType1")}}},[a("svg-icon",{attrs:{"icon-class":"password"===t.passwordType1?"eye":"eye-open"}})],1)],1)])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"新密码",prop:"password"}},[a("div",{staticStyle:{position:"relative"}},[a("el-input",{attrs:{placeholder:"请输入新密码",type:t.passwordType},model:{value:t.pwdformdata.password,callback:function(e){t.$set(t.pwdformdata,"password",e)},expression:"pwdformdata.password"}}),a("span",{staticClass:"show-pwd",on:{click:function(e){return t.showPwd("passwordType")}}},[a("svg-icon",{attrs:{"icon-class":"password"===t.passwordType?"eye":"eye-open"}})],1)],1)])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"确认密码",prop:"oldpassword"}},[a("div",{staticStyle:{position:"relative"}},[a("el-input",{attrs:{placeholder:"请重新输入密码",type:t.passwordType2},model:{value:t.pwdformdata.oldpassword,callback:function(e){t.$set(t.pwdformdata,"oldpassword",e)},expression:"pwdformdata.oldpassword"}}),a("span",{staticClass:"show-pwd",on:{click:function(e){return t.showPwd("passwordType2")}}},[a("svg-icon",{attrs:{"icon-class":"password"===t.passwordType2?"eye":"eye-open"}})],1)],1)])],1)],1),a("el-row",[a("el-col",{attrs:{span:22,offset:2}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{"background-color":"#1890ff"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.changPwd()}}},[t._v(" 修 改 密 码")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){return t.resetBtn("pwdformdata")}}},[t._v("重置")])],1)],1)],1)],1),a("el-tab-pane",{attrs:{label:"个性化",name:"third"}},[a("div",{staticClass:"dash"},[a("el-form",{attrs:{"label-width":"100px"}},[a("el-form-item",{attrs:{label:"微信"}},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(t._s(t.isShow?"已绑定 ":"未绑定"))]),t.isShow?a("el-button",{attrs:{type:"warning",plain:"",size:"small"},on:{click:function(e){return t.deleteWeChart()}}},[t._v("解绑")]):a("el-button",{attrs:{type:"primary",plain:"",size:"small"},on:{click:function(e){return t.justWeChart()}}},[t._v("绑定")])],1)],1)],1)])],1)],1)])],1)])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[a("input",{ref:"upload",attrs:{type:"file"},on:{change:t.getFile}})]),a("el-dialog",{attrs:{title:"微信扫码",visible:t.qrcodeurlDialog,width:"250px","before-close":t.closeTimer,"append-to-body":!0},on:{"update:visible":function(e){t.qrcodeurlDialog=e}}},[a("div",{staticStyle:{width:"200px",height:"200px",position:"relative"}},[a("el-image",{staticStyle:{width:"200px",height:"200px",border:"1px solid #ccc","border-radius":"10px"},attrs:{src:t.qrcodeurl}},[a("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[a("i",{staticClass:"el-icon-picture-outline",staticStyle:{"font-size":"50px","font-weight":"200"}}),a("span",{staticStyle:{color:"#666","padding-top":"10px"}},[t._v("二维码加载中......")])])]),t.iscodeError?a("div",{staticClass:"qrcodeMark",on:{click:t.justWeChart}}):t._e()],1),t.iscodeError?a("div",{staticStyle:{width:"100%","text-align":"center",padding:"10px 8px 0 0",color:"#0e3df3"}},[t._v(" 二维码过期，点击二维码刷新 ")]):t._e()])],1)},o=[],r=a("c7eb"),i=a("1da1"),n=a("5530"),l=(a("99af"),a("caad"),a("b0c0"),a("e9c4"),a("b680"),a("b64b"),a("b775")),c=a("5f87"),d=a("2f62"),p=a("6ca8"),m=a.n(p),f=(a("a78e"),{data:function(){var t=this,e=function(e,a,s){""===a?s(new Error("请再次输入密码")):a!==t.pwdformdata.password?s(new Error("两次输入密码不一致!")):s()};return{iscodeError:!1,activeName:"first",formdata:{},pwdformdata:{password:"",oldpassword:"",originalpassword:""},listForm:{},pwdformdataRule:{originalpassword:[{required:!0,trigger:"blur",message:"原密码为必填项"},{min:6,trigger:"blur",message:"密码不能少于 6 位"}],password:[{required:!0,trigger:"blur",message:"密码为必填项"},{min:6,trigger:"blur",message:"密码不能少于 6 位"}],oldpassword:[{min:6,trigger:"blur",message:"密码不能少于 6 位"},{required:!0,validator:e,trigger:"blur"}]},passwordType:"password",passwordType1:"password",passwordType2:"password",isErcode:!1,dashValue:!0,weChatOpenid:"",time_set:null,sceneStr:"",qrcodeurl:"",qrcodeurlDialog:!1,isShow:!1}},computed:Object(n["a"])({},Object(d["b"])(["userinfo","avatar","name"])),created:function(){this.weChatOpenid=localStorage.getItem("getInfo")?JSON.parse(localStorage.getItem("getInfo")).openid:"",this.weChatOpenid?this.isShow=!0:this.isShow=!1,this.bindData()},mounted:function(){},methods:{bindData:function(){var t=this;return Object(i["a"])(Object(r["a"])().mark((function e(){var a;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=JSON.parse(window.localStorage.getItem("getInfo")).userid,e.next=3,l["a"].get("/SaUser/getEntity?key=".concat(a)).then((function(e){if(200==e.data.code)for(var a in t.formdata=e.data.data,e.data.data)t.listForm[a]=e.data.data[a]}));case 3:case"end":return e.stop()}}),e)})))()},saveBaseInfo:function(){var t=this,e={id:this.formdata.id,realname:this.formdata.realname,phone:this.formdata.phone,email:this.formdata.email,sex:this.formdata.sex};l["a"].post("/SaUser/update",JSON.stringify(e)).then((function(e){200==e.data.code?t.$message.success("个人信息修改成功"):t.$message.warning("个人信息修改失败"),t.bindData()}))},closeTag:function(){this.$store.dispatch("tagsView/delView",this.$route),this.$router.push({path:"/"})},resetBtn:function(t){this.$refs[t].resetFields()},changPwd:function(){var t=this;this.$refs["pwdformdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;l["a"].get("/SaUser/updatePass?oldpassword="+t.pwdformdata.originalpassword+"&newpassword="+t.pwdformdata.password).then((function(e){200==e.data.code?(t.active=3,t.$message.success(e.data.msg||"密码修改成功"),Object(c["b"])(),t.$router.push({path:"/login"}),t.$store.dispatch("user/logout"),t.$store.dispatch("tagsView/delAllViews")):t.$message.warning(e.data.msg||"密码重置失败")}))}))},showPwd:function(t){var e=this;this.$nextTick((function(){"password"===e[t]?e[t]="":e[t]="password"}))},getFile:function(t){var e=this.$refs.upload,a=e.files[0],s=this,o=[".png",".PNG",".jpg",".JPG"],r=(parseFloat(a.size)/1024/1024).toFixed(2),i=a.name.lastIndexOf("."),n=a.name.length,l=a.name.substring(i,n);o.includes(l)?m()(a).then((function(t){var e=(t.fileLen/1024/1024).toFixed(2);if(e>.4)s.$message.warning("请上传小于2MB的图片，此图片".concat(r,"MB"));else{var a=t.base64.split("data:image/jpeg;base64,")[1];s.formdata.avatar=a,s.saveBaseInfo()}})).catch((function(t){console.log("压缩失败了",t)})).always((function(){console.log("压缩成功")})):s.$message.warning("请添加正确的图片格式")},changePhoto:function(){this.$refs.upload.click()},handleClick:function(t,e){"个性化"==t.label&&this.getWeChatInfo()},justWeChart:function(){var t=this;this.iscodeError=!1,l["a"].post("".concat(this.$store.state.app.config.oamapi,"/wx/qrcode/").concat(this.$store.state.app.config.wxappid,"/qrcodes?expireSeconds=")+60).then((function(e){t.qrcodeurl=e.data.data.url,t.sceneStr=e.data.data.sceneStr,t.qrcodeurlDialog=!0,clearInterval(t.time_set),t.time_set=setInterval((function(){t.isLoginSuccess(t.sceneStr)}),1e3)}))},isLoginSuccess:function(t){var e=this;t&&l["a"].post("".concat(this.$store.state.app.config.oamapi,"/wx/qrcode/").concat(this.$store.state.app.config.wxappid,"/qrcodesResult?sceneStr=")+this.sceneStr).then((function(t){200==t.data.code?0!==t.data.data.result&&(clearInterval(e.time_set),e.qrcodeurlDialog=!1,e.updateUserInfo(t.data.data)):500==t.data.code&&(e.iscodeError=!0,clearInterval(e.time_set))}))},deleteWeChart:function(){var t=this,e=localStorage.getItem("getInfo")?JSON.parse(localStorage.getItem("getInfo")):{};l["a"].post("/SaUser/update",JSON.stringify({wxopenid:"",id:e.userid})).then((function(e){200===e.data.code?(t.$message.warning("解绑成功"),localStorage.removeItem("openid"),t.getWeChatInfo()):t.$message.warning("解绑失败")}))},getWeChatInfo:function(){localStorage.getItem("openid")?this.weChatOpenid=localStorage.getItem("openid"):this.weChatOpenid="",this.weChatOpenid?this.isShow=!0:this.isShow=!1},updateUserInfo:function(t,e){var a=this,s=localStorage.getItem("getInfo")?JSON.parse(localStorage.getItem("getInfo")):{};l["a"].post("/SaUser/updateOpenid",JSON.stringify({wxopenid:t.openidToken,id:s.userid})).then((function(s){200===s.data.code?(a.$message.success(e||"绑定成功"),a.weChatOpenid=t.openid,localStorage.setItem("openid",JSON.stringify(t.openidToken)),a.getWeChatInfo()):a.$message.warning("绑定失败")}))},closeTimer:function(){this.qrcodeurlDialog=!1,window.clearInterval(this.time_set)}}}),u=f,h=(a("cb31"),a("2877")),w=Object(h["a"])(u,s,o,!1,null,"02bab622",null);e["default"]=w.exports}}]);