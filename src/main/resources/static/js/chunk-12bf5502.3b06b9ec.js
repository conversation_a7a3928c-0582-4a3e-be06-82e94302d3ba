(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-12bf5502"],{"03ea":function(t,e,a){"use strict";a("65c6")},"5a78":function(t,e,a){},"65c6":function(t,e,a){},"92ce":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx,"get-type":t.getType}},{formcomp:t.formcomp,formclose:t.formclose,changeidx:t.changeidx,BindData:t.BindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.FormVisible,expression:"!FormVisible"}],ref:"index",staticClass:"index"},[a("listheader",{on:{btnadd:function(e){return t.showform(0,"create")},btnsearch:t.search,showAll:t.showAll,AdvancedSearch:t.AdvancedSearch}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"mytab tab-container",style:{height:t.MaxHeight}},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"powerTab tabItem"},[a("el-table",{staticStyle:{overflow:"auto"},attrs:{data:t.powerData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.$index+1))])]}}])}),a("el-table-column",{attrs:{label:"导航编码",align:"left",width:"250px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.navcode))])]}}])}),a("el-table-column",{attrs:{label:"导航名称",align:"left",width:"250px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.navname))])]}}])}),a("el-table-column",{attrs:{label:"排序",align:"center",width:"60px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.rownum))])]}}])}),a("el-table-column",{attrs:{label:"状态",align:"center","min-width":"50px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.enabledmark?a("el-tag",[t._v("正 常")]):a("el-tag",{attrs:{type:"warning"}},[t._v("停 用")])]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center","min-width":"60px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormat")(e.row.createdate)))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"80px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-edit"},on:{click:function(a){return t.showform(e.row.navid,"update")}}},[t._v("修改")]),a("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-delete"},on:{click:function(a){return t.deleteBtn(e.row.navid,"delete")}}},[t._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)])],1)])],1)],1)],1)])},i=[],o=(a("99af"),a("e9c4"),a("d3b7"),a("25f0"),a("4d90"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",size:"mini",plain:""},on:{click:t.btnadd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"}},[t._v("列设置")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:t.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini"},on:{click:function(e){t.iShow=!t.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"菜单名称"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入菜单名称",size:"small"},model:{value:t.formdata.navname,callback:function(e){t.$set(t.formdata,"navname",e)},expression:"formdata.navname"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"菜单路径"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入菜单路径",size:"small"},model:{value:t.formdata.mvcurl,callback:function(e){t.$set(t.formdata,"mvcurl",e)},expression:"formdata.mvcurl"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"菜单类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择菜单类型",size:"small"},model:{value:t.formdata.navtype,callback:function(e){t.$set(t.formdata,"navtype",e)},expression:"formdata.navtype"}},[a("el-option",{attrs:{label:"项目",value:"0"}}),a("el-option",{attrs:{label:"页面",value:"1"}}),a("el-option",{attrs:{label:"分组",value:"2"}}),a("el-option",{attrs:{label:"按键",value:"3"}})],1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"6px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.AdvancedSearch()}}},[t._v(" 搜索 ")])],1)],1)],1)],1)])],1)}),r=[],s={name:"Listheader",data:function(){return{strfilter:"",iShow:!1,formdata:{}}},methods:{AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},showAll:function(){this.$emit("showAll")},btnsearch:function(){console.log(this.strfilter),this.$emit("btnsearch",this.strfilter)}}},l=s,c=(a("9ec3"),a("2877")),d=Object(c["a"])(l,o,r,!1,null,"7a003a7c",null),m=d.exports,f=a("333d"),u=a("b775"),h=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"})],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.formclose(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"off",rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-divider",{attrs:{"content-position":"left"}},[a("b",[t._v("基础信息")])]),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"导航编码"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入导航编码",clearable:"",size:"small"},model:{value:t.formdata.navcode,callback:function(e){t.$set(t.formdata,"navcode",e)},expression:"formdata.navcode"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"导航名称"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入导航名称",clearable:"",size:"small"},model:{value:t.formdata.navname,callback:function(e){t.$set(t.formdata,"navname",e)},expression:"formdata.navname"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"显示排序"}},[a("el-input-number",{staticClass:"inputNumberContent",attrs:{controls:!0,type:"number",min:0,size:"small","controls-position":"right"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"菜单状态"}},[a("el-radio",{attrs:{label:1},model:{value:t.formdata.enabledmark,callback:function(e){t.$set(t.formdata,"enabledmark",e)},expression:"formdata.enabledmark"}},[t._v("正常")]),a("el-radio",{attrs:{label:0},model:{value:t.formdata.enabledmark,callback:function(e){t.$set(t.formdata,"enabledmark",e)},expression:"formdata.enabledmark"}},[t._v("停用")])],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"15px"}},[a("el-col",{attrs:{span:24}},[a("el-tabs",{staticStyle:{"min-height":"400px"},attrs:{"tab-position":"left"}},[a("el-tab-pane",{attrs:{label:"导航内容"}},[a("div",{staticClass:"box-card"},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入导航内容",size:"small",type:"textarea",autosize:{minRows:18,maxRows:21}},model:{value:t.formdata.navcontent,callback:function(e){t.$set(t.formdata,"navcontent",e)},expression:"formdata.navcontent"}})],1)])],1)],1)],1)],1),a("el-divider")],1),a("el-form",{attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},p=[],g=a("c7eb"),v=a("1da1");a("b64b"),a("498a"),a("159b");const b={add(t){return new Promise((e,a)=>{var n=JSON.stringify(t);u["a"].post("/SaWebNav/create",n).then(t=>{console.log(n,t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var n=JSON.stringify(t);u["a"].post("/SaWebNav/update",n).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{u["a"].get("/SaWebNav/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var w=b,x={name:"Formedit",components:{},props:["idx","getType"],data:function(){var t=function(t,e,a){console.log(e),0==e.trim().length?a(new Error("请选择员工")):a()};return{title:"PC端菜单",formdata:{navid:"",navcode:"",navname:"",rownum:0,enabledmark:1,remark:"",permissioncode:"",functionid:"",functionname:"",functioncode:"",lister:window.localStorage.getItem("getInfo")&&JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:window.localStorage.getItem("getInfo")&&JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{test:[{required:!0,trigger:"blur",validator:t}]},formLabelWidth:"100px",formheight:"500px",menuData:[],queryParams:{PageNum:1,PageSize:2e3,OrderType:1,SearchType:0},PwProcessFormVisible:!1}},computed:{formcontainHeight:function(){return window.innerHeight-100-23+"px"}},watch:{idx:function(t,e){this.BindData()}},created:function(){this.BindData()},methods:{BindData:function(){var t=this;return Object(v["a"])(Object(g["a"])().mark((function e(){return Object(g["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.listLoading=!0,e.next=3,u["a"].post("/SaWebNav/getPageList",JSON.stringify(t.queryParams)).then((function(e){console.log("=====00000000======",e),200==e.data.code&&(t.menuData=t.changeFormat(e.data.data.list))}));case 3:if("update"!=t.getType){e.next=8;break}return e.next=6,u["a"].get("/SaWebNav/getEntity?key=".concat(t.idx)).then((function(e){console.log("======0000=======",e),200==e.data.code&&(t.formdata=e.data.data),t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 6:e.next=9;break;case 8:0!=t.idx&&"create"==t.getType&&(t.formdata.navpid=t.idx);case 9:case"end":return e.stop()}}),e)})))()},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.formsave()}))},formsave:function(){var t=this;0==this.idx&&"create"==this.getType?(w.add(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("BindData"),t.BindData())})).catch((function(e){t.$message.warning("保存失败")})),console.log("完成窗口")):0!=this.idx&&"create"==this.getType?(this.formdata.navpid=this.idx,w.add(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("BindData"),t.BindData())})).catch((function(e){t.$message.warning("保存失败")}))):"update"==this.getType&&w.update(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("BindData"),t.BindData())})).catch((function(e){t.$message.warning("保存失败")}))},formclose:function(){this.$emit("formclose")},rowdel:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),w.delete(t).then((function(t){200==t.code&&e.$message({showClose:!0,message:"删除成功",type:"success"}),e.$emit("formcomp")})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},handleChange:function(t){t.length>0?this.formdata.navpid=t[t.length-1]:this.formdata.navpid="root",console.log(t,this.formdata.navpid)},changeFormat:function(t){var e=[];if(!Array.isArray(t))return e;t.forEach((function(t){delete t.children}));var a={};return t.forEach((function(t){a[t.navid]=t})),t.forEach((function(t){var n=a[t.navpid];n?(n.children||(n.children=[])).push(t):e.push(t)})),e},selectMenu:function(){this.PwProcessFormVisible=!0},selPwWork:function(){this.PwProcessFormVisible=!1;var t=this.$refs.selWebMenu.selrows;console.log("staffVal",t),this.formdata=t,this.formdata.children&&this.$delete(this.formdata,"children"),this.$delete(this.formdata,"createdate"),this.$delete(this.formdata,"deletedate"),this.$delete(this.formdata,"deletemark"),this.$delete(this.formdata,"lister"),this.$delete(this.formdata,"navid")}},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),n=(e.getMonth()+1).toString().padStart(2,"0"),i=e.getDate().toString().padStart(2,"0"),o=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),s=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(n,"-").concat(i," ").concat(o,":").concat(r,":").concat(s)}}},y=x,S=(a("af93"),Object(c["a"])(y,h,p,!1,null,"8062e3be",null)),_=S.exports,k={name:"SYSM05B4",components:{Pagination:f["a"],listheader:m,formedit:_},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),n=(e.getMonth()+1).toString().padStart(2,"0"),i=e.getDate().toString().padStart(2,"0"),o=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),s=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(n,"-").concat(i," ").concat(o,":").concat(r,":").concat(s)}},data:function(){return{lst:[],searchstr:"",total:0,FormVisible:!1,listLoading:!1,refreshTable:!1,isShowAll:!0,idx:0,getType:"create",queryParams:{PageNum:1,PageSize:20,OrderType:0,SearchType:0,OrderBy:"rownum"},projectData:[],openproject:!1,pageData:[],openpage:!1,groupData:[],opengroup:!1,powerData:[]}},computed:{MaxHeight:function(){return window.innerHeight-160+"px"},tableMaxHeight:function(){return window.innerHeight-165}},watch:{},created:function(){this.searchstr="",this.BindData()},methods:{BindData:function(){var t=this;this.listLoading=!0,u["a"].post("/SaWebNav/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.powerData=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},deleteBtn:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),u["a"].get("/SaWebNav/delete?key=".concat(t)).then((function(t){console.log("删除："+t),200==t.data.code?(e.$message.success("删除成功"),e.BindData()):e.$message.warning(t.data.msg)})).catch((function(t){e.$message.warning("删除失败")}))})).catch((function(){}))},search:function(t){""!=t?this.queryParams.SearchPojo={navcode:t,navname:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.BindData()},AdvancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.BindData()},showform:function(t,e){this.idx=t,this.getType=e,this.FormVisible=!0},formclose:function(){this.FormVisible=!1,console.log("关闭编码窗口")},formcomp:function(){this.BindData(),this.FormVisible=!1,console.log("完成并刷新index")},handleNodeClick:function(t){console.log(t)},showAll:function(){this.isShowAll=!this.isShowAll,this.isShowAll?this.handleOpen():this.handleClose()},handleOpen:function(){var t=this;this.refreshTable=!1,this.isShowAll=!0,this.$nextTick((function(){t.refreshTable=!0}))},handleClose:function(){var t=this;this.refreshTable=!1,this.isShowAll=!1,this.$nextTick((function(){t.refreshTable=!0}))},changeidx:function(t){this.idx=t},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.BindData()}}},$=k,P=(a("03ea"),Object(c["a"])($,n,i,!1,null,null,null));e["default"]=P.exports},"9ec3":function(t,e,a){"use strict";a("5a78")},af93:function(t,e,a){"use strict";a("ea3e")},ea3e:function(t,e,a){}}]);