(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c9e46aaa"],{"11c8":function(t,e,i){},"11f6":function(t,e,i){},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},1941:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D01M06B2Edit",commonurl:"/D01M06B2/printBill",weburl:"/D01M06B2/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D01M06B2Edit",examineurl:"/D01M06B2/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},["D04M01B2"==t.processModel?i("D04M01B2",{ref:"D04M01B2",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D01M06B2"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e()],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D04M01B2"==t.processModel?i("D04M01B2List",{ref:"D04M01B2List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=(i("b64b"),i("b775"));const n={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D01M06B2/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D01M06B2/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D01M06B2/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D01M06B2/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D01M06B2/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D01M06B2/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var r=n,l=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[]}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"其他发货",value:"其他发货"}}),i("el-option",{attrs:{label:"其他退货",value:"其他退货"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"客户",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B1/getOnlinePageList",type:"客户"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"联系人"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入联系人",clearable:"",size:"small"},model:{value:t.formdata.linkman,callback:function(e){t.$set(t.formdata,"linkman",e)},expression:"formdata.linkman"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"联系电话"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入联系电话",clearable:"",size:"small"},model:{value:t.formdata.telephone,callback:function(e){t.$set(t.formdata,"telephone",e)},expression:"formdata.telephone"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"送货地址",prop:"deliadd"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入送货地址",clearable:"",size:"small"},model:{value:t.formdata.deliadd,callback:function(e){t.$set(t.formdata,"deliadd",e)},expression:"formdata.deliadd"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"运输方式"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入运输方式",clearable:"",size:"small"},model:{value:t.formdata.transport,callback:function(e){t.$set(t.formdata,"transport",e)},expression:"formdata.transport"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"业务员"}},[i("el-popover",{ref:"dictionaryRef",staticClass:"salemanStyle",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.salesmanRef.bindData()}}},[i("SelDict",{ref:"salesmanRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"sale.salesman"},on:{singleSel:function(e){t.formdata.salesman=e.dictvalue,t.$refs["dictionaryRef"].doClose(),t.cleValidate("salesman")},closedic:function(e){return t.$refs["dictionaryRef"].doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"业务员",clearable:"",size:"small"},model:{value:t.formdata.salesman,callback:function(e){t.$set(t.formdata,"salesman",e)},expression:"formdata.salesman"}})],1)],1)],1)],1)],1)],1)},c=[],m={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"客户为必填项"}],billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},d=m,u=(i("2179"),i("2877")),h=Object(u["a"])(d,l,c,!1,null,"8b974f0e",null),f=h.exports,p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,showcontent:["add","dummy","moveup","movedown","delete","copyrow","refresh","billstate"],lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn,selDummy:t.selDummy},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"border-x":"","border-y":"","border-around":!0,"checkbox-option":t.checkboxOption,"column-width-resize-option":t.columnWidthResizeOption,editOption:t.editOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selgoodsvisible?i("el-dialog",{attrs:{title:"货品信息","append-to-body":!0,visible:t.selgoodsvisible,width:"1200px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selgoodsvisible=e}}},[i("SelGoods",{ref:"selGoods",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selGoods()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selgoodsvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},b=[],g=(i("c740"),i("caad"),i("d81d"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),i("da92"),{amount:0,attributejson:"",batchno:"",bfitemid:0,bussclosed:0,bussqty:0,citeitemid:"",citeuid:"",costgroupjson:"",costitemjson:"",custom1:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",custpo:"",disannuldate:new Date,disannullister:"",disannulmark:0,finishclosed:0,finishqty:0,freeqty:0,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",invoclosed:0,invoqty:0,itemcode:"",itemname:"",itemspec:"",itemtaxrate:0,itemunit:"",location:"",machdate:new Date,machitemid:"",machtype:"",machuid:"",partid:"",pickqty:0,pid:"",price:0,quantity:0,rebate:0,remark:"",returnclosed:0,returnmatqty:0,returnqty:0,rownum:0,salescost:0,sourcetype:0,statecode:"",statedate:new Date,stdamount:0,stdprice:0,taxamount:0,taxprice:0,taxtotal:0,virtualitem:0}),y=g,v=i("735b"),w=i("9bda"),x={name:"Elitem",components:{SelGoods:w["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{listLoading:!1,selgoodsvisible:!1,lst:[],setColumsVisible:!1,keynum:0,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:v["a"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate"],costgroupList:[],columsData:[],rowStyleOption:{clickHighlight:!1,hoverHighlight:!1,stripe:!1},columnHidden:[],checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},columnWidthResizeOption:{enable:!0,minWidth:10,sizeChange:function(e){var i=e.column,a=e.differWidth,o=e.columnWidth;t.columnResizeInfo.column=i,t.columnResizeInfo.differWidth=a,t.columnResizeInfo.columnWidth=o}},columnResizeInfo:{column:"",differWidth:"",columnWidth:""},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;if(t.editmarkfiles.includes(a.field)){t.countfiles.includes(a.field)&&t.changeInput("",i,a.field);var o=t.customList.findIndex((function(t){return t.attrkey==a.field}));-1!=o&&t.setAttributeJson(i,i.rownum)}}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},footerData:[],cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));if(-1!=s){t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[s],Object.keys(o)[1]);var n=t.customList.findIndex((function(t){return t.attrkey==Object.keys(o)[1]}));-1!=n&&t.setAttributeJson(t.lst[s],s)}}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;e.selectionRangeIndexes,e.selectionRangeKeys;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}},copyText:""}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){var i=this;if(0!=this.lstitem.length){this.nowitemtaxrate=this.lstitem[0].itemtaxrate;for(var a=0;a<this.lstitem.length;a++)if(""!=this.lstitem[a].attributejson&&null!=this.lstitem[a].attributejson)for(var o=JSON.parse(this.lstitem[a].attributejson),s=0;s<o.length;s++)i.$set(i.lstitem[a],o[s].key,o[s].value)}this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var s=JSON.parse(t[i].attributejson);for(a=0;a<s.length;a++)t[i][s[a].key]=s[a].value}}this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=v["a"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(v["a"].formcode,e,1).then((function(e){t.customList=e.customList,t.costgroupList=e.costgroupList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex,e.customList.findIndex((function(e){return t.itemcode==e.attrkey})));if(-1!=s){var n=e.customList[s].valuejson?e.customList[s].valuejson.split(","):[],r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]])]),a("div",{directives:[{name:"show",value:n.length}]},[a("div",{style:"width:0;height:0"},[a("el-select",{class:"wpselect",style:"width:0;height:0;opacity: 0;",attrs:{size:"small",id:t.itemcode+o.rownum+v["a"].formcode},on:{change:function(i){o[t.itemcode]=i,e.setAttributeJson(o,o.rownum)}},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}},[n.map((function(t,e){return a("el-option",{key:e,attrs:{label:t,value:t},style:"max-width:200px"})}))])]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-arrow-down",style:"font-weight:bold;font-size:14px;",on:{click:function(){document.getElementById(t.itemcode+o.rownum+v["a"].formcode).click()}}})])]);return r}if("goodsuid"==t.itemcode){r=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeVal:o[t.itemcode]}});return r}if("status"==t.itemcode){r="";return 0!=o.finishqty&&o.finishqty<o.quantity?r=a("span",{class:"textborder-blue"},[e.formdata.billtype.includes("退货")?"入库":"出库"]):o.finishqty==o.quantity&&0!=o.finishqty?r=a("span",{class:"textborder-green"},["完成"]):1==o.disannulmark?r=a("span",{class:"textborder-grey"},["撤销"]):o.finishclosed&&(r=a("span",{class:"textborder-grey"},["中止"])),r}r=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]);return r}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["goodsuid","amount","quantity","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],n=0;n<i.length;n++){var r=i[n];this.lst[e+o][r]=s[r].replace(/^\s*|\s*$/g,""),this.countfiles.includes(r)&&this.changeInput("",this.lst[e+o],r);var l=this.customList.findIndex((function(t){return t.attrkey==r}));-1!=l&&this.setAttributeJson(this.lst[e+o],e+o)}},selDummy:function(t){var e=t;if(0!=e.length){this.$refs.itemGroupBtns.DummyVisible=!1;for(var i=0;i<e.length;i++){var a=e[i],o=Object.assign({},y);o.goodsid=a.id,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.goodscustom1=a.custom1,o.goodscustom2=a.custom2,o.goodscustom3=a.custom3,o.goodscustom4=a.custom4,o.goodscustom5=a.custom5,o.goodscustom6=a.custom6,o.goodscustom7=a.custom7,o.goodscustom8=a.custom8,o.goodscustom9=a.custom9,o.goodscustom10=a.custom10,o.price=a.outprice,o.virtualitem=1,0!=this.idx&&(o.pid=this.idx);for(var s=0;s<this.customList.length;s++){a=this.customList[s];o[a.attrkey]=""}for(s=0;s<this.costgroupList.length;s++){a=this.costgroupList[s];o[a.attrkey]=""}this.lst.push(o)}}else this.$message.warning("请选择虚拟品内容")},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],s={key:o.attrkey,value:t[o.attrkey]?t[o.attrkey].replace(/\s*/g,""):""};""!=s.value&&i.push(s)}0==i.length?this.lst[e].attributejson="":this.lst[e].attributejson=JSON.stringify(i)},numFormat:function(t){t+="";var e=t.lastIndexOf(".")?t.lastIndexOf("."):0,i=t.length,a=t.substring(e,i);return a>0?t:t.substring(0,e)},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i)},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"finishqty",0),this.$set(e,"closed",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.selgoodsvisible=!0,this.multi=t},selGoods:function(){var t=this.$refs.selGoods.$refs.selectgoods.selection;if(0!=t.length){this.selgoodsvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=i.taxprice?i.taxprice:0,o=i.price?i.price:0,s=i.itemtaxrate?i.itemtaxrate:this.nowitemtaxrate,n=0,r=0,l=0,c=Object.assign({},y);if(c.goodsid=i.id,c.goodsuid=i.goodsuid,c.goodsname=i.goodsname,c.goodsunit=i.goodsunit,c.goodsspec=i.goodsspec,c.goodsuid=i.goodsuid,c.partid=i.partid,c.itemname=i.itemname,c.itemcode=i.itemcode,c.itemspec=i.itemspec,c.itemunit=i.itemunit,c.goodscustom1=i.custom1,c.goodscustom2=i.custom2,c.goodscustom3=i.custom3,c.goodscustom4=i.custom4,c.goodscustom5=i.custom5,c.goodscustom6=i.custom6,c.goodscustom7=i.custom7,c.goodscustom8=i.custom8,c.goodscustom9=i.custom9,c.goodscustom10=i.custom10,c.custpo=i.custorderid,c.machtype=i.billtype,c.citeitemid=i.id,c.citeuid=i.refno,c.machuid=i.refno,c.machitemid=i.id,c.machdate=i.billdate,c.amount=l,c.itemtaxrate=s,c.price=o,c.quantity=n,c.taxamount=r||0,c.taxprice=a,c.taxtotal=c.taxamount-c.amount,c.virtualitem=i.virtualitem?i.virtualitem:0,c.attributejson=i.attributejson,c.costitemjson=i.costitemjson,c.costgroupjson=i.costgroupjson,0!=this.idx&&(c.pid=this.idx),""==c.attributejson||null==c.attributejson);else for(var m=JSON.parse(c.attributejson),d=0;d<m.length;d++)c[m[d].key]=m[d].value;this.lst.push(c),this.setAttributeJson(c,this.lst.length-1)}}else this.$message.warning("请选择货品内容")}}},k=x,S=(i("585f"),Object(u["a"])(k,p,b,!1,null,"7c35feb8",null)),D=S.exports,$=i("f68b"),B=i("dcb4"),C=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","telephone","linkman","deliadd","taxrate","transport","salesman","salesmanid","operator","operatorid","summary","billtaxamount","billtaxtotal","billamount","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],F=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","itemcode","itemname","itemspec","itemunit","quantity","taxprice","taxamount","price","amount","itemtaxrate","taxtotal","stdprice","stdamount","rebate","freeqty","rownum","remark","citeuid","citeitemid","custpo","machtype","salescost","virtualitem","location","batchno","machuid","machitemid","disannulmark","bfitemid","attributejson","machdate","costitemjson","costgroupjson","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10","custom11","custom12","custom13","custom14","custom15","custom16","custom17","custom18"],O={params:C,paramsItem:F},P=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"特权审核",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"approvalByPassAdmin",param:"",children:[]},{show:1,divided:!1,ieval:1,label:'this.formdata.billtype == "订单退货" ? "客退入库" : "发货出库"',icon:"",disabled:"this.formstate!=2",methods:"operateBill",param:"D04M01B2",children:[]}],I=[{show:1,ieval:1,divided:!1,label:'this.formdata.billtype == "订单退货" ? "客退入库" : "发货出库"',icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D04M01B2",label:"出入库单"},children:[]}],q=i("13df"),T=i("27f6"),L={name:"Formedit",components:{FormTemp:B["a"],EditHeader:f,EditItem:D,D04M01B2:q["default"],D04M01B2List:T["a"]},props:["idx","isprocessDialog"],data:function(){return{title:"其他发货",operateBar:P,processBar:I,formdata:{assessdate:"",assessor:"",billamount:0,billdate:new Date,billreceived:0,billstatecode:"",billstatedate:"",billtaxamount:0,billtaxtotal:0,billtitle:"",billtype:"其他发货",deliadd:"",disannulmark:0,groupid:"",groupname:"",linkman:"",operator:"",refno:"",salesman:"",summary:"",taxrate:0,telephone:"",tenantid:"",transport:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:$["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData()},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D01M06B2").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):$["a"],t.formtemplate.footer.type||(t.formtemplate.footer=$["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&s["a"].get("/D01M06B2/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.$refs.elitem.multipleSelection=[],t.$refs.elitem.checkboxOption.selectedRowKeys=[],t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.salesman=t.seller,this.formdata.abbreviate=t.abbreviate,this.formdata.grouplevel=t.grouplevel,this.formdata.linkman=t.linkman,this.formdata.telephone=t.telephone,this.formdata.deliadd=t.deliveradd},autoClear:function(){this.formdata.groupname="",this.formdata.groupid="",this.formdata.salesman="",this.formdata.abbreviate="",this.formdata.grouplevel="",this.formdata.linkman="",this.formdata.telephone="",this.formdata.deliadd=""},changeIdx:function(t){this.dialogIdx=t},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){try{this.$refs.elitem.$refs.multipleTable.stopEditingCell()}catch(o){}for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0"),void this.$refs.elitem.saveRow(this.$refs.elitem.lst[e]);this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.billamount=i.billamount,this.formdata.billtaxamount=i.billtaxamount,this.formdata.billtaxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(O,a,this.formdata),0==this.idx?r.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.submitting=0,t.$message.warning(e||"保存失败")})):r.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.submitting=0,t.$message.warning(e||"保存失败")}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){r.delete(t)})).catch((function(){}))},approval:function(){r.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?r.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){r.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t,console.log(this.processModel,"ss");var e=this.$refs.elitem.multipleSelection;"D04M01B2"===t&&e.length>0&&(this.formdata.item=e)},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},changeBillType:function(){formdata.item=[]},approvalByPassAdmin:function(){var t=this;s["a"].get("/D01M06B2/approvalByPassAdmin?key="+this.formdata.id).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败"))}))}}},M=L,R=(i("8576"),Object(u["a"])(M,a,o,!1,null,"c39b1784",null));e["a"]=R.exports},2179:function(t,e,i){"use strict";i("ebf8")},"27f6":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},o=[],s=i("c7eb"),n=i("1da1"),r=(i("caad"),i("e9c4"),i("a9e3"),i("b64b"),i("d3b7"),i("2532"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),i("b775")),l=i("5e63"),c=i("b893"),m={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"13df"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:l["b"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={citeuid:this.searchVal};this.queryParams.SearchPojo=e;var i="/D04M01R1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(c["d"])()[0],EndDate:Object(c["d"])()[1]}),r["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(n["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(l["b"].formcode,l["b"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=a("GoodsInfo",{attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return s}if("billtype"==t.itemcode){s=a("span",[o[t.itemcode]]);return o[t.itemcode].includes("红冲")&&(s=a("span",{style:"color:#f44336"},[o[t.itemcode]])),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},d=m,u=(i("b83a"),i("2877")),h=Object(u["a"])(d,a,o,!1,null,"34f0715a",null);e["a"]=h.exports},"2ee7":function(t,e,i){},4848:function(t,e,i){},"585f":function(t,e,i){"use strict";i("c4d7")},"6dd5":function(t,e,i){},"735b":function(t,e,i){"use strict";i.d(e,"c",(function(){return a})),i.d(e,"b",(function(){return o})),i.d(e,"a",(function(){return s}));var a={formcode:"D01M06B2Th",item:[{itemcode:"refno",itemname:"发货单号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Bus_Deliery.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtitle"},{itemcode:"billdate",itemname:"发货日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deliery.billdate"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"linkman",itemname:"联系人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.linkman"},{itemcode:"telephone",itemname:"联系电话",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.telephone"},{itemcode:"billtaxamount",itemname:"含税金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtaxamount"},{itemcode:"transport",itemname:"运输方式",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.transport"},{itemcode:"salesman",itemname:"业务员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.salesman"},{itemcode:"summary",itemname:"摘要",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.summary"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.status"},{itemcode:"itemcount",itemname:"款数",sortable:1,minwidth:"60",displaymark:1,overflow:1,datasheet:"Bus_Deliery.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.assessor"}]},o={formcode:"D01M06B2List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Bus_Deliery.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deliery.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deliery.billdate"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_DelieryItem.quantity"},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_DelieryItem.taxprice"},{itemcode:"taxamount",itemname:"含税金额",sortable:1,minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_DelieryItem.taxamount"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1}]},s={formcode:"D01M06B2Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:0,overflow:1},{itemcode:"finishqty",itemname:"已出入库",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]}},"841c":function(t,e,i){"use strict";var a=i("d784"),o=i("825a"),s=i("1d80"),n=i("129f"),r=i("14c3");a("search",1,(function(t,e,i){return[function(e){var i=s(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,i):new RegExp(e)[t](String(i))},function(t){var a=i(e,t,this);if(a.done)return a.value;var s=o(t),l=String(this),c=s.lastIndex;n(c,0)||(s.lastIndex=0);var m=r(s,l);return n(s.lastIndex,c)||(s.lastIndex=c),null===m?-1:m.index}]}))},8576:function(t,e,i){"use strict";i("2ee7")},aa0d:function(t,e,i){"use strict";i("4848")},b83a:function(t,e,i){"use strict";i("11f6")},c014:function(t,e,i){"use strict";i("f3c5")},c4d7:function(t,e,i){},e7ad:function(t,e,i){"use strict";i("11c8")},ebf8:function(t,e,i){},f3c5:function(t,e,i){},f68b:function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"其他发货",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"其他发货",value:"其他发货"},{label:"其他退货",value:"其他退货"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"客户",searchtype:"customer",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"linkman",label:"联系人",type:"input",methods:"",param:""},{col:5,code:"telephone",label:"联系电话",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"deliadd",label:"送货地址",type:"input",methods:"",param:""},{col:5,code:"transport",label:"运输方式",type:"input",methods:"",param:""},{col:5,code:"salesman",label:"业务员",billcode:"sale.salesman",type:"dictionary",methods:"",param:""}]}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"salesman",label:"业务员",billcode:"sale.salesman",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},list:{type:0,content:[]},th:{type:0,content:[]},item:{type:0,content:[]}}},fb02:function(t,e,i){"use strict";i("6dd5")},fe11:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[i("ListHeader",{attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,changeModelUrl:t.changeModelUrl,btnExport:t.btnExport,btnHelp:t.btnHelp,changeBalance:t.changeBalance,pagePrint:t.pagePrint,btnPrint:function(e){return t.$refs.tableTh.btnPrint()},bindColumn:function(e){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()}}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{attrs:{span:t.showhelp?20:24}},[i("TableTh",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],ref:"tableTh",attrs:{online:t.online,formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,sendTableForm:t.sendTableForm,showForm:t.showForm}}),i("TableList",{directives:[{name:"show",rawName:"v-show",value:!t.thorList,expression:"!thorList"}],ref:"tableList",attrs:{online:t.online,formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,sendTableForm:t.sendTableForm,showForm:t.showForm}})],1),i("el-col",{attrs:{span:t.showhelp?4:0}},[i("HelpModel",{ref:"helpmodel",attrs:{code:"D01M06B2"}})],1)],1)],1)],1)])},o=[],s=(i("b64b"),i("ac1f"),i("841c"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{class:"filter-container flex j-s a-c"},[i("div",{staticStyle:{display:"flex"}},[i("div",{staticClass:"flex infoForm a-c"},[i("span",{staticClass:"infoForm-Title"},[t._v("时间范围")]),i("el-date-picker",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"unlink-panels":"",size:"mini","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")]),i("el-button",{attrs:{size:"mini",icon:"el-icon-printer",title:"打印列表"},on:{click:function(e){return t.$emit("pagePrint")}}},[t._v("打印")]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:"el-icon-printer",plain:"",size:"mini",title:"打印单据"},on:{click:function(e){return t.$emit("btnPrint")}}},[t._v(" 单据 ")])],1),i("div",{staticClass:"iShowBtn"},[i("div",{staticStyle:{display:"inline-block"}},[i("el-switch",{staticStyle:{"margin-right":"10px"},attrs:{"inactive-color":"#13ce66"},on:{change:t.changeModelUrl},model:{value:t.thorList,callback:function(e){t.thorList=e},expression:"thorList"}}),i("span",{staticStyle:{"margin-right":"10px","font-size":"14px"}},[t._v(t._s(t.thorList?"单据":"明细"))])],1),i("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"结存",size:"mini",border:""},on:{change:t.changeBalance},model:{value:t.balance,callback:function(e){t.balance=e},expression:"balance"}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}}),i("el-button",{attrs:{size:"mini",title:"帮助",icon:"el-icon-s-help"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,baseparam:"/SaDgFormat",tableForm:t.tableForm},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),n=[],r=i("b893"),l={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},dateRange:Object(r["d"])(),pickerOptions:Object(r["h"])(),thorList:!0,balance:!1,setColumsVisible:!1,searchVisible:!1}},methods:{advancedSearch:function(t){var e={dateRange:this.dateRange,formdata:t};this.$emit("advancedSearch",e),this.searchVisible=!1},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){var t={dateRange:this.dateRange,strfilter:this.strfilter};this.$emit("btnSearch",t)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList),this.$emit("bindColumn")},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},changeBalance:function(t){var e=0;e=t?1:0,this.$emit("changeBalance",e)}}},c=l,m=(i("fb02"),i("2877")),d=Object(m["a"])(c,s,n,!1,null,"9e762260",null),u=d.exports,h=i("1941"),f=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableTh",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"border-x":"","border-y":"","border-around":!0,"column-width-resize-option":t.columnWidthResizeOption,"checkbox-option":t.checkboxOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize,pageSizes:[10,20,100,500]},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.selectList,printcode:"D01M06B2Edit",commonurl:"/D01M06B2/printBatchBill",weburl:"/D01M06B2/printBatchWebBill"}}),i("PrintServer",{ref:"PrintPageServer",attrs:{formdata:t.lst,queryParams:t.queryParams,printcode:"D01M06B2Th",commonurl:"/D01M06B2/printPageTh",weburl:"/D01M06B2/printWebPageTh"}})],1)},p=[],b=(i("e9c4"),i("a9e3"),i("d3b7"),i("c7cd"),i("159b"),i("b775")),g=i("f07e"),y=i("735b"),v={components:{MyPopover:g["a"]},props:["online","formtemplate"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:y["c"],mypopoverTable:y["a"],mypopoverData:[],mypopoverIndex:-1,groupInfo:{},selectList:[],totalfields:["refno","billtaxamount","billamount"],exportitle:"其他发货",rowStyleOption:{clickHighlight:!1,hoverHighlight:!1},columnHidden:[],checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectList=i?t.lst:[]}},columnWidthResizeOption:{enable:!0,minWidth:50,sizeChange:function(e){var i=e.column,a=e.differWidth,o=e.columnWidth;t.columnResizeInfo.column=i,t.columnResizeInfo.differWidth=a,t.columnResizeInfo.columnWidth=o}},columnResizeInfo:{column:"",differWidth:"",columnWidth:""},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},footerData:[],customData:[],eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}},cellTotal:0,cellNum:0}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},methods:{bindData:function(){var t=this;this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(r["d"])()[0],EndDate:Object(r["d"])()[1]});var e="/D01M06B2/getPageTh";if(this.online)e="/D01M06B2/getOnlinePageTh";b["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this,e=y["c"];this.formtemplate.th.type&&(e.item=this.formtemplate.th.content),this.$getColumn(this.tableForm.formcode,e).then((function(e){t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex);if("billdate"==t.itemcode||"itemplandate"==t.itemcode||"billplandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("groupuid"==t.itemcode){var n=a("GroupInfo",{attrs:{scopeVal:o[t.itemcode],searchUrl:"/D01M01B1/getGeneral?key="+o.groupid}});return n}if("assessor"==t.itemcode){n="";return n=o.oaflowmark&&!o.assessor?a("span",{style:"color:#ff9800"},["审核中"]):a("span",[o.assessor]),n}if("status"==t.itemcode){n="";return o.finishcount>0&&o.finishcount+o.disannulcount<o.itemcount?n=a("span",{class:"textborder-blue"},["发货"]):o.finishcount+o.disannulcount==o.itemcount&&o.finishcount>0?n=a("span",{class:"textborder-green"},["完成"]):o.disannulcount>0&&o.disannulmark==o.itemcount&&(n=a("span",{class:"textborder-grey"},["撤销"])),n}if("refno"==t.itemcode){n=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return n}if("itemcount"==t.itemcode){n="";return n=a("el-popover",{attrs:{placement:"left",trigger:"click",title:"单据明细"},ref:"'popover-' + scope.$index"},[a("div",{style:"position: relative; min-height: 100px",directives:[{name:"show",value:s==e.mypopoverIndex}]},[a(g["a"],{ref:"mypopover",attrs:{tableForm:e.mypopoverTable,lst:e.mypopoverData}})]),a("span",{slot:"reference",class:"textunderline",on:{click:function(){return e.getBillList(o,s)}}},[o[t.itemcode]])]),n}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:40,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableTh.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableTh.getRangeCellSelection().selectionRangeIndexes,i=["billtaxamount","billamount","billtaxtotal"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t,1)},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){for(var t=0;t<this.lst.length;t++){var e=this.lst[t];e.finishcount>0&&e.finishcount+e.disannulcount<e.itemcount?e.status="发货":e.finishcount>0&&e.finishcount+e.disannulcount==e.itemcount?e.status="完成":e.disannulcount>0&&e.disannulcount==e.itemcount&&(e.status="撤销")}this.$btnExport(this.lst,this.tableForm,this.exportitle)},pagePrint:function(){this.$refs.PrintPageServer.printButton(1,1)},btnPrint:function(){this.$refs.PrintServer.printButton(2,1)},getColumnItem:function(){var t=this;this.$getColumn(y["a"].formcode,y["a"]).then((function(e){t.mypopoverTable=Object.assign({},e.colList)}))},getBillList:function(t,e){var i=this;this.mypopoverIndex=e,b["a"].get("/D01M06B2/getBillEntity?key=".concat(t.id)).then((function(t){if(200==t.data.code){i.mypopoverData=t.data.data.item;for(var e=0;e<i.mypopoverData.length;e++){var a=i.mypopoverData[e];if(""==a.attributejson||null==a.attributejson);else for(var o=JSON.parse(a.attributejson),s=0;s<o.length;s++)a[o[s].key]=o[s].value}}else i.mypopoverData=[]}))}}},w=v,x=(i("aa0d"),Object(m["a"])(w,f,p,!1,null,"53e1f7be",null)),k=x.exports,S=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"border-x":"","border-y":"","border-around":!0,"column-width-resize-option":t.columnWidthResizeOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.lst,queryParams:t.queryParams,printcode:"D01M06B2List",commonurl:"/D01M06R1/printPageList",weburl:"/D01M06R1/printWebPageList"}})],1)},D=[],$=i("c7eb"),B=i("1da1"),C=(i("caad"),i("2532"),{components:{},props:["online","formtemplate"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:y["b"],customList:[],totalfields:["refno","taxamount","quantity"],exportitle:"其他发货明细表",columnHidden:[],columnWidthResizeOption:{enable:!0,minWidth:50,sizeChange:function(e){var i=e.column,a=e.differWidth,o=e.columnWidth;t.columnResizeInfo.column=i,t.columnResizeInfo.differWidth=a,t.columnResizeInfo.columnWidth=o}},columnResizeInfo:{column:"",differWidth:"",columnWidth:""},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},footerData:[],customData:[],eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}},cellTotal:0,cellNum:0}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},methods:{bindData:function(){var t=this;if(this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),this.online)var e="/D01M06B2/getOnlinePageList";else e="/D01M06B2/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(r["d"])()[0],EndDate:Object(r["d"])()[1]}),b["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}else t.$message.warning(response.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(B["a"])(Object($["a"])().mark((function e(){var i;return Object($["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=y["b"],t.formtemplate.list.type&&(i.item=t.formtemplate.list.content),t.$getColumn(t.tableForm.formcode,i,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=a("GoodsInfo",{attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return s}if("status"==t.itemcode){s="";return 0!=o.finishqty&&o.finishqty<o.quantity?s=a("span",{class:"textborder-blue"},[o.billtype.includes("退货")?"入库":"出库"]):o.finishqty==o.quantity&&0!=o.finishqty?s=a("span",{class:"textborder-green"},["完成"]):1==o.disannulmark?s=a("span",{class:"textborder-grey"},["撤销"]):o.finishclosed&&(s=a("span",{class:"textborder-grey"},["中止"])),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxprice","taxamount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t,1)},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){for(var t=0;t<this.lst.length;t++){var e=this.lst[t];0!=e.finishqty&&e.finishqty<e.quantity?e.status="发货":0!=e.finishcount&&e.finishqty==e.quantity?e.status="完成":1==e.disannulmark?e.status="撤销":e.finishclosed&&(e.status="中止"),e.matused&&(e.matstatus="已领")}this.$btnExport(this.lst,this.tableForm,this.exportitle)},pagePrint:function(){this.$refs.PrintServer.printButton(1,1)}}}),F=C,O=(i("c014"),Object(m["a"])(F,S,D,!1,null,"71be9e9f",null)),P=O.exports,I=i("f68b"),q={name:"D01M06B2",components:{ListHeader:u,FormEdit:h["a"],TableTh:k,TableList:P},data:function(){return{formvisible:!1,idx:0,online:0,tableForm:{},thorList:!0,showhelp:!1,formtemplate:I["a"]}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},mounted:function(){this.bindData(),this.bindTemp()},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D01M06B2").then((function(e){200==e.data.code?(null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):I["a"]),t.$nextTick((function(){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()}))):t.$alert(e.data.msg||"获取页面信息失败")})).catch((function(e){t.$message.error("请求错误")}))},bindData:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.tableTh.bindData()})):this.$nextTick((function(){t.$refs.tableList.bindData()}))},sendTableForm:function(t){this.tableForm=t},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},pagePrint:function(){this.thorList?this.$refs.tableTh.pagePrint():this.$refs.tableList.pagePrint()},changeModelUrl:function(t){this.thorList=!!t,this.bindData()},btnExport:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.tableTh.btnExport()})):this.$nextTick((function(){t.$refs.tableList.btnExport()}))},search:function(t){this.thorList?this.$refs.tableTh.search(t):this.$refs.tableList.search(t)},advancedSearch:function(t){this.thorList?this.$refs.tableTh.advancedSearch(t):this.$refs.tableList.advancedSearch(t)},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},changeBalance:function(t){this.online=t,this.bindData()}}},T=q,L=(i("e7ad"),Object(m["a"])(T,a,o,!1,null,"3cfd52fd",null));e["default"]=L.exports}}]);