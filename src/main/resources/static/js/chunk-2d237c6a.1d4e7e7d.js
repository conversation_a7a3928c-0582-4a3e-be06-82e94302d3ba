(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d237c6a"],{fd0f:function(t,n,e){"use strict";e.r(n);var o=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div")},a=[],i=(e("e9c4"),e("b64b"),e("b775")),c=e("5f87"),s={data:function(){return{value:"",value1:"",zhi:"",isloading:!0}},created:function(){},mounted:function(){this.Bindata()},methods:{Bindata:function(){var t=this;this.value=window.location.href?window.location.href:"",this.value1=this.value.split("?key=")[1],i["a"].get("/auth/wxelogin/getLoginUser?key="+this.value1).then((function(n){if(200==n.data.code){var e=n.data.data.loginuser;localStorage.setItem("getInfo",JSON.stringify(e)),t.$store.state.user.userinfo=e;var o=n.data.data.access_token;Object(c["c"])(o),t.$store.state.user.token=o,t.readnav()}else t.$confirm("获取用户信息失败，是否返回登录？","提示",{confirmButtonText:"返回登录",cancelButtonText:"退出系统",type:"warning",closeOnClickModal:!1}).then((function(n){"confirm"===n?(Object(c["b"])(),t.$router.push("/login"),t.$store.dispatch("user/logout")):(window.location.href="about:blank",window.close())})).catch((function(){window.location.href="about:blank",window.close()}))})).catch((function(n){t.$confirm("获取用户信息失败，是否返回登录？","提示",{confirmButtonText:"返回登录",cancelButtonText:"退出系统",type:"warning",closeOnClickModal:!1}).then((function(n){console.log(n),"confirm"===n?(Object(c["b"])(),t.$router.push("/login"),t.$store.dispatch("user/logout")):window.close()})).catch((function(){window.close()}))}))},readnav:function(){var t=this;i["a"].get("/system/SYSM02B2/getMenuWebListBySelf").then((function(n){if(200==n.data.code){var e=n.data.data;t.isloading=!1,localStorage.setItem("navjson",JSON.stringify(e)),t.$store.dispatch("app/setnavdata",e),t.showWorkbench()}else t.$confirm("获取服务菜单失败，是否重试？","提示",{confirmButtonText:"重试",cancelButtonText:"退出系统",type:"warning",closeOnClickModal:!1}).then((function(n){"confirm"===n?t.readnav():window.close()})).catch((function(){window.close()}))})).catch((function(t){}))},showWorkbench:function(){var t=this.$store.state.user.userinfo.configs["system.style.dashboard"];if(null==t||""==t)this.$router.push({path:this.redirect||"/"});else{var n=JSON.parse(t);this.$router.push(n.value)}}}},r=s,u=e("2877"),l=Object(u["a"])(r,o,a,!1,null,null,null);n["default"]=l.exports}}]);