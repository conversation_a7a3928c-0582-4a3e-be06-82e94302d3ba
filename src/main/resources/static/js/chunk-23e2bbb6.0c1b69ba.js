(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-23e2bbb6"],{"10f6":function(t,e,n){"use strict";n("3f44")},"22e1":function(t,e,n){t.exports=n.p+"static/img/icon_qywx.ae4a491d.png"},2504:function(t,e,n){},"3f44":function(t,e,n){},"4eb6":function(t,e,n){t.exports=n.p+"static/img/icon_dingding.70dd0587.png"},"5afb":function(t,e,n){t.exports=n.p+"static/img/icon_qq.a2e8a939.png"},6806:function(t,e,n){},"697d":function(t,e,n){"use strict";n("2504")},8:function(t,e){},"9ed6":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"login-container flex a-c j-end",staticStyle:{width:"100%"}},[a("div",{staticClass:"p-r",staticStyle:{width:"100%",height:"100%",display:"flex","justify-content":"center","align-items":"center"}},[a("div",{staticClass:"logoBox"}),a("div",{staticClass:"login"},[t.formVisable?a("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:t.loginForm,rules:t.loginRules,"auto-complete":"on","label-position":"left"}},[a("div",{staticClass:"title-container"},[a("h3",{staticClass:"title"},[t._v(t._s(t.apptitle))])]),a("div",{directives:[{name:"show",rawName:"v-show",value:t.isErcode,expression:"isErcode"}]},[a("div",{staticStyle:{"font-size":"20px","text-align":"center",padding:"0 0 20px 0"}},[a("span",{staticStyle:{color:"#6285f7"}},[t._v("账号密码")]),t._v("登录 ")]),a("div",{staticClass:"ercode_tab swicth-ercode",on:{click:function(e){return t.openErcode()}}},[a("svg",{attrs:{width:"52",height:"52","xmlns:xlink":"http://www.w3.org/1999/xlink",fill:"currentColor"}},[a("defs",[a("path",{attrs:{id:"id-3938311804-a",d:"M0 0h48a4 4 0 0 1 4 4v48L0 0z"}})]),a("g",{attrs:{fill:"none","fill-rule":"evenodd"}},[a("mask",{attrs:{id:"id-3938311804-b",fill:"#fff"}},[a("use",{attrs:{"xlink:href":"#id-3938311804-a"}})]),a("use",{attrs:{fill:"#0084FF","xlink:href":"#id-3938311804-a"}}),a("image",{attrs:{width:"52",height:"52",mask:"url(#id-3938311804-b)","xlink:href":"data:image/png;base64,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"}})])])]),a("el-form-item",{staticStyle:{"margin-bottom":"30px"},attrs:{prop:"UserName"}},[a("span",{staticClass:"svg-container"},[a("svg-icon",{attrs:{"icon-class":"user"}})],1),a("el-input",{ref:"UserName",attrs:{placeholder:"手机和邮箱",name:"UserName",type:"text",tabindex:"1","auto-complete":"off"},model:{value:t.loginForm.UserName,callback:function(e){t.$set(t.loginForm,"UserName",e)},expression:"loginForm.UserName"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"30px"},attrs:{prop:"Password"}},[a("span",{staticClass:"svg-container"},[a("svg-icon",{attrs:{"icon-class":"password"}})],1),a("el-input",{key:t.passwordType,ref:"Password",attrs:{type:t.passwordType,placeholder:"密码",name:"Password",tabindex:"2","auto-complete":"off"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.validateCaptcha(e)}},model:{value:t.loginForm.Password,callback:function(e){t.$set(t.loginForm,"Password",e)},expression:"loginForm.Password"}}),a("span",{staticClass:"show-pwd",on:{click:t.showPwd}},[a("svg-icon",{attrs:{"icon-class":"password"===t.passwordType?"eye":"eye-open"}})],1)],1),t.codeNum>=2?a("div",[a("el-form-item",{staticStyle:{border:"0","margin-bottom":"30px"},attrs:{prop:"Code"}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[a("div",{staticStyle:{width:"60%",border:"1px solid rgba(0, 0, 0, 0.2)","border-radius":"5px",display:"flex"}},[a("span",{staticClass:"svg-container"},[a("svg-icon",{attrs:{"icon-class":"code"}})],1),a("el-input",{attrs:{type:"text",placeholder:"验证码","auto-complete":"off"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.validateCaptcha(e)}},model:{value:t.loginForm.Code,callback:function(e){t.$set(t.loginForm,"Code",e)},expression:"loginForm.Code"}})],1),a("div",{staticStyle:{width:"36%",height:"45px",border:"1px solid rgba(0, 0, 0, 0.2)",cursor:"pointer"}},[t.codeUrl?a("img",{staticStyle:{width:"100%",height:"100%",cursor:"pointer"},attrs:{src:"data:image/jpg;base64,"+t.codeUrl,alt:""},on:{click:t.getCode}}):a("div",{staticClass:"codeTip",on:{click:t.getCode}},[a("i",{staticClass:"el-icon-refresh-right"}),t._v(" 刷新验证码 ")])])])])],1):t._e(),a("div",{staticClass:"passwordOpera"},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.loginForm.lockPwd,callback:function(e){t.$set(t.loginForm,"lockPwd",e)},expression:"loginForm.lockPwd"}},[t._v("记住密码")])],1),a("br"),a("el-button",{staticStyle:{width:"100%","margin-bottom":"10px"},attrs:{loading:t.loading,type:"primary"},nativeOn:{click:function(e){return e.preventDefault(),t.validateCaptcha(e)}}},[t._v("登录")])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.isErcode,expression:"!isErcode"}]},[a("div",{staticStyle:{"font-size":"20px","text-align":"center",padding:"0 0 20px 0"}},[a("span",{staticStyle:{color:"#6285f7"}},[t._v("微信扫码")]),t._v("一键登录 ")]),a("div",{staticClass:"ercode_tab switch-input",on:{click:function(e){return t.changeErcode()}}},[a("svg",{attrs:{width:"52",height:"52","xmlns:xlink":"http://www.w3.org/1999/xlink",fill:"currentColor"}},[a("defs",[a("path",{attrs:{id:"id-14580708-a",d:"M0 0h48a4 4 0 0 1 4 4v48L0 0z"}})]),a("g",{attrs:{fill:"none","fill-rule":"evenodd"}},[a("mask",{attrs:{id:"id-14580708-b",fill:"#fff"}},[a("use",{attrs:{"xlink:href":"#id-14580708-a"}})]),a("use",{attrs:{fill:"#0084FF","xlink:href":"#id-14580708-a"}}),a("path",{attrs:{fill:"#FFF",d:"M22.125 4h13.75A4.125 4.125 0 0 1 40 8.125v27.75A4.125 4.125 0 0 1 35.875 40h-13.75A4.125 4.125 0 0 1 18 35.875V8.125A4.125 4.125 0 0 1 22.125 4zm6.938 34.222c1.139 0 2.062-.945 2.062-2.11 0-1.167-.923-2.112-2.063-2.112-1.139 0-2.062.945-2.062 2.111 0 1.166.923 2.111 2.063 2.111zM21 8.333v24h16v-24H21z",mask:"url(#id-14580708-b)"}}),a("g",{attrs:{mask:"url(#id-14580708-b)"}},[a("path",{attrs:{fill:"#FFF",d:"M46.996 15.482L39 19.064l-7.996-3.582A1.6 1.6 0 0 1 32.6 14h12.8a1.6 1.6 0 0 1 1.596 1.482zM47 16.646V24.4a1.6 1.6 0 0 1-1.6 1.6H32.6a1.6 1.6 0 0 1-1.6-1.6v-7.754l8 3.584 8-3.584z"}}),a("path",{attrs:{fill:"#0084FF",d:"M31 15.483v1.17l8 3.577 8-3.577v-1.17l-8 3.581z","fill-rule":"nonzero"}})])])])]),a("div",{staticClass:"ercode",staticStyle:{width:"200px",margin:"0 auto",position:"relative"}},[a("el-image",{staticStyle:{width:"200px",height:"200px",border:"1px solid #ccc","border-radius":"10px"},attrs:{src:t.qrcodeurl}},[a("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[a("i",{staticClass:"el-icon-picture-outline",staticStyle:{"font-size":"50px","font-weight":"200"}}),a("span",{staticStyle:{color:"#666","padding-top":"10px"}},[t._v("二维码加载中......")])])]),t.iscodeError?a("div",{staticClass:"qrcodeMark"}):t._e()],1),t.iscodeError?a("div",{staticClass:"codeError",on:{click:function(e){return t.openErcode()}}},[a("i",{staticClass:"el-icon-refresh-left"}),t._v(" 二维码失效，请重新生成 ")]):a("div",{staticClass:"ercode-foot"},[a("div",[t._v("打开 "),a("span",[t._v("微信App")])]),a("div",{staticStyle:{"font-size":"12px","margin-top":"10px"}},[t._v(" 在「首页」右上角打开扫一扫 ")])])]),a("el-divider",[t._v("登录方式")]),a("div",{staticClass:"loginType"},[a("div",{staticStyle:{cursor:"pointer"},on:{click:t.openErcode}},[a("svg-icon",{staticStyle:{color:"#8a8a8a"},attrs:{"icon-class":"wechat-fill"}}),a("span",{staticStyle:{"padding-left":"5px"}},[t._v("微信扫码")])],1),a("div",{staticStyle:{cursor:"pointer"},on:{click:t.changeErcode}},[a("svg-icon",{attrs:{"icon-class":"phone-iphone"}}),a("span",{staticStyle:{"padding-left":"5px"}},[t._v("账号密码")])],1)]),a("div",[t.intervalTime(t.passkey.ex)<=30?a("div",{staticStyle:{display:"flex","justify-content":"center","align-items":"center",width:"100%","margin-top":"15px",color:"red","font-size":"16px","font-weight":"700",cursor:"pointer"},on:{click:function(e){t.isContactShow=!0}}},[t._v(" 软件授权逾期，"),a("span",{staticStyle:{"text-decoration":"underline"}},[t._v("请先注册激活")])]):t._e(),t.intervalTime(t.passkey.ex)<=30?a("div",{staticStyle:{"font-size":"16px",display:"flex","justify-content":"center","align-items":"center","margin-top":"10px"},on:{click:function(e){t.isContactShow=!0}}},[t._v(" 授权期余 "),a("strong",{staticStyle:{color:"blue","text-decoration":"underline",cursor:"pointer",padding:"0 5px"}},[t._v(t._s(t.intervalTime(t.passkey.ex)))]),t._v(" ，请提前续约 ")]):t._e()])],1):t._e(),a("div",{staticStyle:{color:"#666","font-size":"14px","text-align":"center",position:"absolute",bottom:"10px"}},[t._v(" Copyright@嘉兴应凯科技有限公司 版权所有 ")])],1)]),a("div",{staticClass:"contactUs"},[a("a",{attrs:{href:"http://www.inkstech.com",target:"_blank"}},[t._v(" 联系我们")]),a("a",{attrs:{href:"#"},on:{click:function(e){return t.Collection(e)}}},[t._v(" 收藏")]),a("a",{attrs:{href:"#"},on:{click:t.routerTo}},[t._v(" 首页")])]),a("el-dialog",{attrs:{title:"忘记密码",visible:t.forgetPwdVisible,width:"600px","show-close":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.forgetPwdVisible=e}}},[t.forgetPwdVisible?a("forgetPassword",{ref:"forgetPassword",staticStyle:{height:"310px"},on:{forgetPwdClose:t.forgetPwdClose}}):t._e()],1),a("el-dialog",{attrs:{title:"钉钉二维码","append-to-body":!0,width:"500px",visible:t.isDDErcode,"close-on-click-modal":!1},on:{"update:visible":function(e){t.isDDErcode=e}}},[a("iframe",{ref:"iframe",staticStyle:{width:"100%",height:"500px"},attrs:{id:"codeiframe",src:t.dingdingcodeUrl,frameborder:"0"}})]),t.isContactShow?a("el-dialog",{attrs:{title:"联系我们",width:"700px",visible:t.isContactShow,"close-on-click-modal":!1},on:{"update:visible":function(e){t.isContactShow=e}}},[a("div",{staticClass:"contactBox"},[a("div",{staticClass:"leftBox"},[a("p",[t._v("产品咨询：186-0685-8808")]),a("p",[t._v("邮 箱：<EMAIL>")]),a("p",[t._v("Q Q：841850740")]),a("p",[t._v("地址：浙江省嘉兴市嘉善县惠民街道台升大道3号3号楼306室")])]),a("div",{staticClass:"qrCode"},[a("img",{staticStyle:{width:"120px",height:"120px"},attrs:{src:n("4680")}}),a("p",{staticStyle:{"font-size":"16px","font-weight":"700",color:"#000"}},[t._v("激活咨询")])])])]):t._e(),a("RegActive",{ref:"regactive"})],1)},i=[],l=n("ade3"),o=n("c7eb"),s=n("1da1"),r=(n("99af"),n("c740"),n("a434"),n("e9c4"),n("a9e3"),n("b64b"),n("d3b7"),n("25f0"),n("4d90"),n("498a"),n("159b"),n("b775")),c=n("ae30"),d=n("a78e"),A=n.n(d),p=n("3452"),g=n.n(p),u=n("5f87"),h=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{attrs:{id:"forgetStep"}},[n("el-steps",{attrs:{active:t.active,"finish-status":"success"}},[n("el-step",{attrs:{title:"身份验证"}}),n("el-step",{attrs:{title:"密码重置"}}),n("el-step",{attrs:{title:"重置完成"}})],1)],1),n("div",{directives:[{name:"show",rawName:"v-show",value:0==t.active,expression:"active == 0"}],staticClass:"stepBody"},[n("el-form",{ref:"formdata",attrs:{model:t.formdata,rules:t.formdataRule,"label-width":"20px"}},[n("el-form-item",{attrs:{prop:"username"}},[n("span",{staticClass:"svg-container"},[n("svg-icon",{staticStyle:{color:"#889aa4"},attrs:{"icon-class":"user"}})],1),n("el-input",{attrs:{placeholder:"请输入账号",size:"small","auto-complete":"off"},model:{value:t.formdata.username,callback:function(e){t.$set(t.formdata,"username",e)},expression:"formdata.username"}})],1),n("el-form-item",{attrs:{prop:"code"}},[n("div",{staticStyle:{display:"flex","align-items":"center"}},[n("span",{staticClass:"svg-container",staticStyle:{"align-self":"center"}},[n("svg-icon",{staticStyle:{color:"#889aa4","font-size":"18px","margin-left":"-4px"},attrs:{"icon-class":"code"}})],1),n("el-input",{attrs:{placeholder:"请输入邮箱验证码",size:"small",name:"code","auto-complete":"off","validate-event":!1},model:{value:t.formdata.code,callback:function(e){t.$set(t.formdata,"code",e)},expression:"formdata.code"}}),n("el-button",{staticStyle:{height:"47px"},attrs:{disabled:t.flag},on:{click:t.getEmailCode}},[t._v(t._s(t.btnTitle)+" ")])],1)]),n("div",{staticStyle:{"text-align":"right"}},[n("el-button",{staticStyle:{"margin-top":"12px"},attrs:{type:"primary"},on:{click:function(e){return t.checkCode()}}},[t._v("下一步")]),n("el-button",{staticStyle:{"margin-top":"12px"},on:{click:function(e){return t.resetBtn("formdata")}}},[t._v("重置")])],1)],1)],1),n("div",{directives:[{name:"show",rawName:"v-show",value:1==t.active,expression:"active == 1"}],staticClass:"stepBody"},[n("el-form",{ref:"pwdformdata",attrs:{model:t.pwdformdata,rules:t.pwdformdataRule,"label-width":"20px"}},[n("el-form-item",{attrs:{prop:"password"}},[n("span",{staticClass:"svg-container"},[n("svg-icon",{staticStyle:{color:"#889aa4"},attrs:{"icon-class":"password"}})],1),n("el-input",{attrs:{placeholder:"请输入新密码",type:t.passwordType,size:"small"},model:{value:t.pwdformdata.password,callback:function(e){t.$set(t.pwdformdata,"password",e)},expression:"pwdformdata.password"}}),n("span",{staticClass:"show-pwd",on:{click:function(e){return t.showPwd("passwordType")}}},[n("svg-icon",{attrs:{"icon-class":"password"===t.passwordType?"eye":"eye-open"}})],1)],1),n("el-form-item",{attrs:{prop:"oldpassword"}},[n("span",{staticClass:"svg-container"},[n("svg-icon",{staticStyle:{color:"#889aa4"},attrs:{"icon-class":"password"}})],1),n("el-input",{attrs:{placeholder:"请重新输入密码",type:t.passwordType2,size:"small"},model:{value:t.pwdformdata.oldpassword,callback:function(e){t.$set(t.pwdformdata,"oldpassword",e)},expression:"pwdformdata.oldpassword"}}),n("span",{staticClass:"show-pwd",on:{click:function(e){return t.showPwd("passwordType2")}}},[n("svg-icon",{attrs:{"icon-class":"password"===t.passwordType2?"eye":"eye-open"}})],1)],1),n("div",{staticStyle:{"text-align":"right"}},[n("el-button",{staticStyle:{"margin-top":"12px"},attrs:{type:"primary"},on:{click:function(e){return t.changPwd()}}},[t._v("下一步")]),n("el-button",{staticStyle:{"margin-top":"12px"},on:{click:function(e){return t.resetBtn("pwdformdata")}}},[t._v("重置")])],1)],1)],1),n("div",{directives:[{name:"show",rawName:"v-show",value:3==t.active,expression:"active == 3"}],staticClass:"stepBody"},[n("div",{staticClass:"stepLast"},[t._m(0),n("el-button",{staticStyle:{"margin-top":"40px"},attrs:{type:"primary"},on:{click:t.forgetclose}},[t._v("重新登陆")])],1)])])},m=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"lastTip"},[n("i",{staticClass:"el-icon-success"}),t._v("新密码重置成功，请重新登陆 ")])}],C=(n("ac1f"),n("00b4"),{data:function(){var t=this,e=function(t,e,n){if(e){var a=/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/,i=/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/;if(!a.test(e)&&!i.test(e))return n(new Error("请输入正确的手机或邮箱格式"));Object(r["a"])({url:"/auth/inspect",method:"get",params:{userName:e}}).then((function(t){200==t.data.code?n(new Error("该账号不存在，请确认你的账号")):n()}))}},n=function(e,n,a){""===n?a(new Error("请再次输入密码")):n!==t.pwdformdata.password?a(new Error("两次输入密码不一致!")):a()},a=function(e,n,a){n&&(console.log(n,t.formdata.username),Object(r["a"])({url:"/auth/checkCaptcha",method:"get",params:{code:n.toUpperCase(),username:t.formdata.username}}).then((function(t){200!=t.data.code?a(new Error(t.data.massage)):a()})))};return{formdata:{username:"",code:""},pwdformdata:{password:"",oldpassword:""},active:0,formdataRule:{username:[{required:!0,trigger:"blur",message:"账号为必填项"},{trigger:"blur",validator:e}],code:[{required:!0,trigger:"blur",message:"验证码为必填项"},{required:!0,validator:a}]},pwdformdataRule:{password:[{required:!0,trigger:"blur",message:"密码为必填项"},{min:6,trigger:"blur",message:"密码不能少于 6 位"}],oldpassword:[{min:6,trigger:"blur",message:"密码不能少于 6 位"},{required:!0,validator:n,trigger:"blur"}]},passwordType:"password",passwordType2:"password",count:5,btnTitle:"获取验证码",flag:!1}},methods:{getEmailCode:function(){var t=this;if(""!=this.formdata.username){var e=/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/,n=/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/;e.test(this.formdata.username)?r["a"].get("/auth/emailCaptcha?email="+this.formdata.username).then((function(e){if(200==e.data.code){t.$message.success("验证码已发送到邮箱上，请查看");var n=setInterval((function(){t.count<1?(t.flag=!1,t.btnTitle="获取验证码",t.count=5,clearInterval(n)):(t.flag=!0,t.btnTitle=t.count--+"s后重新获取")}),1e3)}})):n.test(this.formdata.username)&&r["a"].get("/auth/phoneCaptcha?phone="+this.formdata.username).then((function(e){if(200==e.data.code){t.$message.success("验证码已发送到手机短信上，请查看");var n=setInterval((function(){t.count<1?(t.flag=!1,t.btnTitle="获取验证码",t.count=5,clearInterval(n)):(t.flag=!0,t.btnTitle=t.count--+"s后重新获取")}),1e3)}}))}else this.$message.warning("账号不能为空")},checkCode:function(){var t=this;this.$refs["formdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.active=1,t.$message.success("身份验证成功")}))},resetBtn:function(t){console.log(this.$refs[t]),this.$refs[t].resetFields()},changPwd:function(){var t=this;this.$refs["pwdformdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.formdata.username,t.pwdformdata.password;r["a"].post("/auth/forgetPwd?key="+t.formdata.username+"&password="+t.pwdformdata.password).then((function(e){200==e.data.code?t.active=3:t.$message.warning("密码重置失败")}))}))},showPwd:function(t){var e=this;"passwordType"==t?this.$nextTick((function(){"password"===e.passwordType?e.passwordType="":e.passwordType="password"})):this.$nextTick((function(){"password"===e.passwordType2?e.passwordType2="":e.passwordType2="password"}))},forgetclose:function(){this.$emit("forgetPwdClose")}}}),B=C,E=(n("10f6"),n("697d"),n("2877")),f=Object(E["a"])(B,h,m,!1,null,"0a9235b0",null),I=f.exports,Q=[{name:"销售",path:"sale",pid:null,meta:{icon:"D01M03B1",title:"销售"},children:[{name:"订单管理",path:"",meta:{icon:"",title:"订单管理"},pid:null,children:[{children:null,name:"报价单",path:"/D01/M04B1",meta:{icon:"",title:"报价单"},pid:null},{children:null,name:"销售订单",path:"/D01/M03B1",meta:{icon:"",title:"销售订单"},pid:null},{children:null,name:"订单发货",path:"/D01/M06B1",meta:{icon:"",title:"订单发货"},pid:null},{children:null,name:"其他发货",path:"/D01/M06B2",meta:{icon:"",title:"其他发货"},pid:null}]},{name:"销售对账",path:"",meta:{icon:"",title:"销售对账"},pid:null,children:[{children:null,name:"销售扣款",path:"/D01/M09B1",meta:{icon:"",title:"销售扣款"},pid:null},{children:null,name:"销售开票",path:"/D01/M05B1",meta:{icon:"",title:"销售开票"},pid:null},{children:null,name:"销售结账",path:"/D01/M14B1",meta:{icon:"",title:"销售结账"},pid:null}]},{name:"基础信息",path:"",meta:{icon:"",title:"基础信息"},pid:null,children:[{children:null,name:"客户信息",path:"/D01/M01B1",meta:{icon:"",title:"客户信息"},pid:null},{children:null,name:"货品信息",path:"/D91/M01B1",meta:{icon:"",title:"货品信息"},pid:null},{children:null,name:"虚拟品",path:"/D91/M01B1VIR",meta:{icon:"",title:"虚拟品"},pid:null}]}]},{name:"采购",path:"buy",pid:null,meta:{icon:"D03M01B1",title:"采购"},children:[{name:"采购管理",path:"",meta:{icon:"",title:"采购管理"},pid:null,children:[{children:null,name:"请购单",path:"/D03/M01B1",meta:{icon:"",title:"请购单"},pid:null},{children:null,name:"采购订单",path:"/D03/M02B1",meta:{icon:"",title:"采购订单"},pid:null},{children:null,name:"采购验收",path:"/D03/M03B1",meta:{icon:"",title:"采购验收"},pid:null},{children:null,name:"其他收货",path:"/D03/M03B2",meta:{icon:"",title:"其他收货"},pid:null}]},{name:"采购对账",path:"",meta:{icon:"",title:"采购对账"},pid:null,children:[{children:null,name:"采购开票",path:"/D03/M05B1",meta:{icon:"",title:"采购开票"},pid:null},{children:null,name:"采购扣款",path:"/D03/M04B1",meta:{icon:"",title:"采购扣款"},pid:null},{children:null,name:"采购结账",path:"/D03/M10B1",meta:{icon:"",title:"采购结账"},pid:null}]},{name:"基础信息",path:"",meta:{icon:"",title:"基础信息"},pid:null,children:[{children:null,name:"供应商信息",path:"/D01/M01B2",meta:{icon:"",title:"供应商信息"},pid:null}]}]},{name:"仓库",path:"store",pid:null,meta:{icon:"D04M01B1",title:"仓库"},children:[{name:"出入库单",path:"",meta:{icon:"",title:"出入库单"},pid:null,children:[{children:null,name:"出入库单",path:"/D04/M01M1",meta:{icon:"",title:"出入库单"},pid:null},{children:null,name:"领料单",path:"/D04/M08B1",meta:{icon:"",title:"领料单"},pid:null},{children:null,name:"拆装单",path:"/D04/M12B1",meta:{icon:"",title:"拆装单"},pid:null}]},{name:"库存管理",path:"",meta:{icon:"",title:"库存管理"},pid:null,children:[{children:null,name:"调拨单",path:"/D04/M02B1",meta:{icon:"",title:"调拨单"},pid:null},{children:null,name:"盘点表",path:"/D04/M07B1",meta:{icon:"",title:"盘点表"},pid:null},{children:null,name:"仓库结账",path:"/D04/M05B1",meta:{icon:"",title:"仓库结账"},pid:null},{children:null,name:"库存查询",path:"/D04/M04B1",meta:{icon:"",title:"库存查询"},pid:null}]},{name:"基础信息",path:"",meta:{icon:"",title:"基础信息"},pid:null,children:[{children:null,name:"仓库信息",path:"/D04/M21S1",meta:{icon:"",title:"仓库信息"},pid:null},{children:null,name:"库位设置",path:"/D04/M21S2",meta:{icon:"",title:"库位设置"},pid:null},{children:null,name:"领用车间",path:"/D01/M01B3",meta:{icon:"",title:"领用车间"},pid:null}]}]},{name:"财务",path:"fm",pid:null,meta:{icon:"D08M12B2",title:"财务"},children:[{name:"收款管理",path:"",meta:{icon:"",title:"收款管理"},pid:null,children:[{children:null,name:"收款单",path:"/D01/M08B1",meta:{icon:"",title:"收款单"},pid:null},{children:null,name:"预收款",path:"/D01/M08B1DEP",meta:{icon:"",title:"预收款"},pid:null},{children:null,name:"其他收入",path:"/D07/M01B1IN",meta:{icon:"",title:"其他收入"},pid:null},{children:null,name:"往来核销",path:"/D07/M02B1",meta:{icon:"",title:"往来核销"},pid:null}]},{name:"付款管理",path:"",meta:{icon:"",title:"付款管理"},pid:null,children:[{children:null,name:"付款单",path:"/D03/M06B1",meta:{icon:"",title:"付款单"},pid:null},{children:null,name:"订单预付",path:"/D03/M06B1PRE",meta:{icon:"",title:"订单预付"},pid:null},{children:null,name:"费用支出",path:"/D07/M01B1OUT",meta:{icon:"",title:"费用支出"},pid:null}]},{name:"财务报表",path:"",meta:{icon:"",title:"财务报表"},pid:null,children:[{children:null,name:"客户应收款",path:"/D01/M12R1",meta:{icon:"",title:"客户应收款"},pid:null},{children:null,name:"采购应付款",path:"/D03/M08R1",meta:{icon:"",title:"采购应付款"},pid:null},{children:null,name:"银行现金明细",path:"/D07/M03R1",meta:{icon:"",title:"银行现金明细"},pid:null}]},{name:"基础信息",path:"",meta:{icon:"",title:"基础信息"},pid:null,children:[{children:null,name:"出纳账号",path:"/D07/M21B2",meta:{icon:"",title:"出纳账号"},pid:null},{children:null,name:"费用科目",path:"/D07/M21B1",meta:{icon:"",title:"费用科目"},pid:null},{children:null,name:"结算部门",path:"/D01/M01B5",meta:{icon:"",title:"结算部门"},pid:null}]}]},{name:"系统",path:"9",pid:null,meta:{icon:"user",title:"系统"},children:[{name:"用户参数",path:"",meta:{icon:"",title:"用户参数"},pid:null,children:[{children:null,name:"用户设置",path:"/Sa/User",meta:{icon:"",title:"用户设置"},pid:null},{children:null,name:"SPU属性",path:"/D91/M01S2",meta:{icon:"",title:"SPU属性"},pid:null},{children:null,name:"公司信息",path:"/Sa/Company",meta:{icon:"",title:"公司信息"},pid:null},{children:null,name:"系统参数",path:"/Sa/Config",meta:{icon:"",title:"系统参数"},pid:null}]},{name:"系统配置",path:"",meta:{icon:"",title:"系统配置"},pid:null,children:[{children:null,name:"恢复备份",path:"/init",meta:{icon:"",title:"恢复备份"},pid:null},{children:null,name:"打印模板",path:"/Sa/Reports",meta:{icon:"",title:"打印模板"},pid:null},{children:null,name:"角色管理",path:"/Sa/Role",meta:{icon:"",title:"角色管理"},pid:null},{children:null,name:"权限编码",path:"/Sa/PermCode",meta:{icon:"",title:"权限编码"},pid:null}]}]}],w=[{name:"销售",path:"sale",pid:null,meta:{icon:"D01M03B1",title:"销售"},children:[{name:"订单管理",path:"",meta:{icon:"",title:"订单管理"},pid:null,children:[{children:null,name:"报价单",path:"/D01/M04B1",meta:{icon:"",title:"报价单"},pid:null},{children:null,name:"销售订单",path:"/D01/M03B1",meta:{icon:"",title:"销售订单"},pid:null},{children:null,name:"订单发货",path:"/D01/M06B1",meta:{icon:"",title:"订单发货"},pid:null},{children:null,name:"其他发货",path:"/D01/M06B2",meta:{icon:"",title:"其他发货"},pid:null}]},{name:"销售对账",path:"",meta:{icon:"",title:"销售对账"},pid:null,children:[{children:null,name:"销售扣款",path:"/D01/M09B1",meta:{icon:"",title:"销售扣款"},pid:null},{children:null,name:"销售开票",path:"/D01/M05B1",meta:{icon:"",title:"销售开票"},pid:null},{children:null,name:"销售结账",path:"/D01/M14B1",meta:{icon:"",title:"销售结账"},pid:null}]},{name:"基础信息",path:"",meta:{icon:"",title:"基础信息"},pid:null,children:[{children:null,name:"客户信息",path:"/D01/M01B1",meta:{icon:"",title:"客户信息"},pid:null},{children:null,name:"货品信息",path:"/D91/M01B1",meta:{icon:"",title:"货品信息"},pid:null},{children:null,name:"虚拟品",path:"/D91/M01B1VIR",meta:{icon:"",title:"虚拟品"},pid:null}]}]},{name:"采购",path:"buy",pid:null,meta:{icon:"D03M01B1",title:"采购"},children:[{name:"采购管理",path:"",meta:{icon:"",title:"采购管理"},pid:null,children:[{children:null,name:"请购单",path:"/D03/M01B1",meta:{icon:"",title:"请购单"},pid:null},{children:null,name:"采购订单",path:"/D03/M02B1",meta:{icon:"",title:"采购订单"},pid:null},{children:null,name:"采购验收",path:"/D03/M03B1",meta:{icon:"",title:"采购验收"},pid:null},{children:null,name:"其他收货",path:"/D03/M03B2",meta:{icon:"",title:"其他收货"},pid:null}]},{name:"采购对账",path:"",meta:{icon:"",title:"采购对账"},pid:null,children:[{children:null,name:"采购开票",path:"/D03/M05B1",meta:{icon:"",title:"采购开票"},pid:null},{children:null,name:"采购扣款",path:"/D03/M04B1",meta:{icon:"",title:"采购扣款"},pid:null},{children:null,name:"采购结账",path:"/D03/M10B1",meta:{icon:"",title:"采购结账"},pid:null}]},{name:"基础信息",path:"",meta:{icon:"",title:"基础信息"},pid:null,children:[{children:null,name:"供应商信息",path:"/D01/M01B2",meta:{icon:"",title:"供应商信息"},pid:null}]}]},{name:"仓库",path:"store",pid:null,meta:{icon:"D04M01B1",title:"仓库"},children:[{name:"出入库单",path:"",meta:{icon:"",title:"出入库单"},pid:null,children:[{children:null,name:"出入库单",path:"/D04/M01M1",meta:{icon:"",title:"出入库单"},pid:null},{children:null,name:"领料单",path:"/D04/M08B1",meta:{icon:"",title:"领料单"},pid:null},{children:null,name:"拆装单",path:"/D04/M12B1",meta:{icon:"",title:"拆装单"},pid:null}]},{name:"库存管理",path:"",meta:{icon:"",title:"库存管理"},pid:null,children:[{children:null,name:"调拨单",path:"/D04/M02B1",meta:{icon:"",title:"调拨单"},pid:null},{children:null,name:"盘点表",path:"/D04/M07B1",meta:{icon:"",title:"盘点表"},pid:null},{children:null,name:"仓库结账",path:"/D04/M05B1",meta:{icon:"",title:"仓库结账"},pid:null},{children:null,name:"库存查询",path:"/D04/M04B1",meta:{icon:"",title:"库存查询"},pid:null}]},{name:"基础信息",path:"",meta:{icon:"",title:"基础信息"},pid:null,children:[{children:null,name:"仓库信息",path:"/D04/M21S1",meta:{icon:"",title:"仓库信息"},pid:null},{children:null,name:"库位设置",path:"/D04/M21S2",meta:{icon:"",title:"库位设置"},pid:null},{children:null,name:"领用车间",path:"/D01/M01B3",meta:{icon:"",title:"领用车间"},pid:null}]}]},{name:"财务",path:"fm",pid:null,meta:{icon:"D08M12B2",title:"财务"},children:[{name:"收款管理",path:"",meta:{icon:"",title:"收款管理"},pid:null,children:[{children:null,name:"收款单",path:"/D01/M08B1",meta:{icon:"",title:"收款单"},pid:null},{children:null,name:"预收款",path:"/D01/M08B1DEP",meta:{icon:"",title:"预收款"},pid:null},{children:null,name:"其他收入",path:"/D07/M01B1IN",meta:{icon:"",title:"其他收入"},pid:null},{children:null,name:"往来核销",path:"/D07/M02B1",meta:{icon:"",title:"往来核销"},pid:null}]},{name:"付款管理",path:"",meta:{icon:"",title:"付款管理"},pid:null,children:[{children:null,name:"付款单",path:"/D03/M06B1",meta:{icon:"",title:"付款单"},pid:null},{children:null,name:"订单预付",path:"/D03/M06B1PRE",meta:{icon:"",title:"订单预付"},pid:null},{children:null,name:"费用支出",path:"/D07/M01B1OUT",meta:{icon:"",title:"费用支出"},pid:null}]},{name:"财务报表",path:"",meta:{icon:"",title:"财务报表"},pid:null,children:[{children:null,name:"客户应收款",path:"/D01/M12R1",meta:{icon:"",title:"客户应收款"},pid:null},{children:null,name:"采购应付款",path:"/D03/M08R1",meta:{icon:"",title:"采购应付款"},pid:null},{children:null,name:"银行现金明细",path:"/D07/M03R1",meta:{icon:"",title:"银行现金明细"},pid:null}]},{name:"基础信息",path:"",meta:{icon:"",title:"基础信息"},pid:null,children:[{children:null,name:"出纳账号",path:"/D07/M21B2",meta:{icon:"",title:"出纳账号"},pid:null},{children:null,name:"费用科目",path:"/D07/M21B1",meta:{icon:"",title:"费用科目"},pid:null},{children:null,name:"结算部门",path:"/D01/M01B5",meta:{icon:"",title:"结算部门"},pid:null}]}]},{name:"系统",path:"9",pid:null,meta:{icon:"user",title:"系统"},children:[{name:"用户参数",path:"",meta:{icon:"",title:"用户参数"},pid:null,children:[{children:null,name:"用户设置",path:"/Sa/User",meta:{icon:"",title:"用户设置"},pid:null},{children:null,name:"SPU属性",path:"/D91/M01S2",meta:{icon:"",title:"SPU属性"},pid:null},{children:null,name:"公司信息",path:"/Sa/Company",meta:{icon:"",title:"公司信息"},pid:null},{children:null,name:"系统参数",path:"/Sa/Config",meta:{icon:"",title:"系统参数"},pid:null}]},{name:"系统配置",path:"",meta:{icon:"",title:"系统配置"},pid:null,children:[{children:null,name:"恢复备份",path:"/init",meta:{icon:"",title:"恢复备份"},pid:null},{children:null,name:"打印模板",path:"/Sa/Reports",meta:{icon:"",title:"打印模板"},pid:null},{children:null,name:"角色管理",path:"/Sa/Role",meta:{icon:"",title:"角色管理"},pid:null},{children:null,name:"权限编码",path:"/Sa/PermCode",meta:{icon:"",title:"权限编码"},pid:null}]}]}],v=n("a00a"),S=n("b893"),k=Object(l["a"])(Object(l["a"])({name:"Login",components:{forgetPassword:I,RegActive:v["a"]},data:function(){var t=function(t,e,n){0==e.trim().length?n(new Error("请输入正确的用户名")):n()},e=function(t,e,n){e.length<6?n(new Error("密码不能少于 6 位")):n()};return{formVisable:!0,loginForm:{UserName:"",Password:"",Code:"",lockPwd:0},loginRules:{UserName:[{required:!0,trigger:"blur",validator:t}],Password:[{required:!0,trigger:"blur",validator:e}],Code:[{required:!0,trigger:"blur",message:"验证码为必填项"}]},isErcode:!0,loading:!1,passwordType:"password",redirect:void 0,API:this.$store.state.app.config.baseURL,apptitle:this.$store.state.app.config.title,sessionLogin:[{icon:n("5afb")},{icon:n("4eb6")},{icon:n("22e1")},{icon:n("bfc7")}],iscodeError:!1,time_set:null,codeUrl:"",uuid:"",passTesting:!1,forgetPwdVisible:!1,isDDErcode:!1,dingdingcodeUrl:"",codeNum:0,passkey:{},isContactShow:!1,qrcodeurl:"",sceneStr:"",timer:null}},watch:{$route:{handler:function(t){this.redirect=t.query&&t.query.redirect},immediate:!0}},destroyed:function(){},created:function(){"notInDingTalk"!=this.$store.state.app.platform?this.showding=!0:this.showding=!1,A.a.set("platform",this.$store.state.appplatform),this.checkHard()},mounted:function(){r["a"].defaults.baseURL=A.a.get("baseApi"),this.getCookie()}},"destroyed",(function(){window.clearInterval(this.time_set)})),"methods",{routerTo:function(){this.$router.push("/home"),sessionStorage.setItem("navIndex",0)},dbFormat:function(){this.$router.push({path:"/init"})},getCode:function(){var t=this;r["a"].get("/auth/captchaImage").then((function(e){200==e.data.code?(t.codeUrl=e.data.img,t.uuid=e.data.uuid):t.$message.warning("验证么获取失败，请重新获取")})).catch((function(e){t.$message.error(e||"请求错误")}))},showPwd:function(){"password"===this.passwordType?this.passwordType="":this.passwordType="password"},validateCaptcha:function(){var t=this;this.$refs.loginForm.validate((function(e){if(!e)return!1;var n={username:t.loginForm.UserName,password:t.loginForm.Password};r["a"].post("/SaUser/login?type=1",JSON.stringify(n)).then((function(e){if(200==e.data.code){1==t.loginForm.lockPwd?t.setCookie(t.loginForm.UserName,t.loginForm.Password,30):t.clearCookie(),t.$notify({title:"登陆成功",type:"success",message:"欢迎登录"});var n=e.data.data.loginuser;n.sn=e.data.data.sn,t.sn=e.data.data.sn,n.openid=e.data.data.openid,n.registrkey=e.data.data.registrkey,localStorage.setItem("getInfo",JSON.stringify(n)),t.$store.state.user.userinfo=e.data.data.loginuser,Object(u["c"])(e.data.data.access_token),t.$store.state.user.token=Object(u["a"])(),t.remove(),t.getNavWeb(),t.sendMqttMsg({getInfo:e.data.data.loginuser,type:"登录成功"})}else t.$notify.error({title:"登陆失败",message:e.data.msg}),t.sendMqttMsg({getInfo:{logintime:new Date,realname:"",ipaddr:""},type:"登录失败"})}))}))},remove:function(){var t=this,e=[];r["a"].get("/SaUser/getUserInfo").then((function(n){if(200==n.data.code){if(e=n.data.data.adminmark?Q:w,2==n.data.data.adminmark){var a=e[e.length-1].children[0].children.findIndex((function(t){return"/Sa/User"==t.path}));-1==a&&e[e.length-1].children[0].children.push({children:null,name:"用户设置",path:"/Sa/User",meta:{icon:"",title:"用户设置"},pid:null})}else{a=e[e.length-1].children[0].children.findIndex((function(t){return"/Sa/SaUser"==t.path}));-1!=a&&e[e.length-1].children[0].children.splice(a,1)}localStorage.setItem("navjson",JSON.stringify(e)),t.$store.dispatch("app/setnavdata",e),t.$router.push({path:"/dashboard"})}}))},getNavWeb:function(){var t=this;this.$request.post("/SaWebNav/getAllList",{PageNum:1,PageSize:1e3,OrderType:1,SearchType:1}).then((function(e){if(200==e.data.code){var n=e.data.data;e.data.data.forEach((function(t,e){n[e].navcontent=t.navcontent?JSON.parse(t.navcontent):[]})),localStorage.setItem("navweb",JSON.stringify(n)),t.$store.dispatch("app/setnavweb",n)}else t.$message.error(e.data.msg||"获取导航失败")})).catch((function(t){console.log(t)}))},dateFormat:function(){var t=new Date,e=t.getFullYear(),n=(t.getMonth()+1).toString().padStart(2,"0"),a=t.getDate().toString().padStart(2,"0"),i=t.getHours().toString().padStart(2,"0"),l=t.getMinutes().toString().padStart(2,"0"),o=t.getSeconds().toString().padStart(2,"0");return"".concat(e,"-").concat(n,"-").concat(a," ").concat(i,":").concat(l,":").concat(o)},showWorkbench:function(){var t=this.$store.state.user.userinfo.configs["system.style.dashboard"];if(null==t||""==t)this.$router.push({path:"/dashboard"});else{var e=JSON.parse(t);this.$router.push(e.value)}},setCookie:function(t,e,n){var a=g.a.AES.encrypt(e,"inks").toString(),i=new Date;i.setTime(i.getTime()+864e5*n),window.document.cookie="userName="+t+";path=/;expires="+i.toGMTString(),window.document.cookie="userPwd="+a+";path=/;expires="+i.toGMTString(),window.document.cookie="lockPwd=1;path=/;expires="+i.toGMTString()},getCookie:function(){if(document.cookie.length>0)for(var t=document.cookie.split("; "),e=0;e<t.length;e++){var n=t[e].split("=");if("userName"==n[0])this.loginForm.UserName=n[1];else if("userPwd"==n[0]){var a=n[1],i=g.a.AES.decrypt(a,"inks");this.loginForm.Password=i.toString(g.a.enc.Utf8)}else"lockPwd"==n[0]&&(this.loginForm.lockPwd=Number(n[1]))}},clearCookie:function(){this.setCookie("","",-1)},openErcode:function(){var t=this;this.qrcodeurl="";var e=this;e.$nextTick((function(){e.isErcode=!1})),this.getWxQrcode().then((function(){clearInterval(t.time_set),t.time_set=setInterval((function(){t.isLoginSuccess(t.sceneStr)}),1e3)}))},getWxQrcode:function(){var t=this;return Object(s["a"])(Object(o["a"])().mark((function e(){return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.iscodeError=!1,r["a"].post("".concat(t.$store.state.app.config.oamapi,"/wx/qrcode/").concat(t.$store.state.app.config.wxappid,"/qrcodes?expireSeconds=")+60).then((function(e){t.qrcodeurl=e.data.data.url,t.sceneStr=e.data.data.sceneStr}));case 2:case"end":return e.stop()}}),e)})))()},isLoginSuccess:function(t){var e=this;t&&r["a"].post("".concat(this.$store.state.app.config.oamapi,"/wx/qrcode/").concat(this.$store.state.app.config.wxappid,"/qrcodesResult?sceneStr=")+this.sceneStr).then((function(t){200==t.data.code?0!==t.data.data.result&&e.getLogin(t.data.data.openidToken):500==t.data.code&&(e.iscodeError=!0,window.clearInterval(that.time_set))}))},getLogin:function(t){var e=this;r["a"].post("/SaUser/loginByOpenidToken?openidToken="+t).then((function(n){if(200===n.data.code){window.clearInterval(e.time_set),e.$notify({title:"登陆成功",type:"success",message:"欢迎登录"});var a=n.data.data.loginuser;a.openid=t,e.sn=n.data.data.sn,e.sendMqttMsg({getInfo:a,type:"扫码登录成功"}),localStorage.setItem("getInfo",JSON.stringify(a)),e.$store.state.user.userinfo=n.data.data.loginuser,Object(u["c"])(n.data.data.access_token),e.$store.state.user.token=Object(u["a"])(),e.remove(),e.getNavWeb()}else 500==n.data.code&&(e.$message.warning("请先绑定微信，再扫码登录！"),window.clearInterval(e.time_set),e.sendMqttMsg({getInfo:{logintime:new Date,realname:"",ipaddr:""},type:"扫码登录失败"}))}))},changeErcode:function(){var t=this;t.$nextTick((function(){t.isErcode=!0})),window.clearInterval(t.time_set)},scanDingding:function(){this.dingdingcodeUrl=A.a.get("baseApi")+"auth/justauth/render/dingtalk",this.isErcode=!0},Collection:function(){try{window.external.addFavorite(location.href,document.title)}catch(t){try{window.sidebar.addPanel(document.title,location.href,"")}catch(t){alert("加入收藏失败，请按Ctrl+D手动添加。")}}},forgetPwd:function(){this.forgetPwdVisible=!0},forgetPwdClose:function(){this.forgetPwdVisible=!1,this.loginForm.Password="",this.getCode()},checkHard:function(){var t=this;this.$request.get("/SaUser/checkHard").then((function(e){200==e.data.code||402===e.data.code?(""!=e.data.data&&(t.passkey=JSON.parse(e.data.data),localStorage.setItem("sn",t.passkey.sn),localStorage.setItem("topic",JSON.stringify({send:"inks/client/sasom/".concat(t.passkey.sn),subscribe:["inks/client/sasom/".concat(t.passkey.sn),"inks/server/sasom/".concat(t.passkey.sn),"inks/server/sasom/public"]}))),402===e.data.code&&t.$confirm("硬件不匹配请重新注册！","提示",{confirmButtonText:"确定",type:"warning",showCancelButton:!1}).then((function(){}))):500==e.data.code&&(localStorage.setItem("topic",JSON.stringify({send:"inks/client/sasom/register",subscribe:["inks/server/sasom/public"]})),t.$confirm("软件未注册，请登陆后注册！","提示",{confirmButtonText:"确定",type:"warning",showCancelButton:!1}).then((function(){})))})).catch((function(t){console.log(t||"请求错误")}))},intervalTime:function(t){var e=Date.parse(new Date)/1e3,n=e,a=t/1e3,i=1e3*(a-n),l=Math.floor(i/864e5),o=i%864e5,s=(Math.floor(o/36e5),o%36e5),r=(Math.floor(s/6e4),s%6e4);Math.round(r/1e3);return l<0?"已":l+"天 "},sendMqttMsg:function(t){var e=Object(c["a"])(this.$store.state.app.config);e.publish(JSON.parse(localStorage.getItem("topic")).send,JSON.stringify({msg:{log:{code:"login",date:Object(S["a"])(t.logintime),content:"姓名:".concat(t.getInfo.realname,",IP:").concat(t.getInfo.ipaddr,",公司名称:").concat(t.getInfo.tenantinfo.company,",联系电话:").concat(t.getInfo.tenantinfo.companytel)},msgtype:"log",sn:this.sn},modulecode:"system"}),{qos:2},(function(t,e){t?console.log("发送信息失败"):console.log("发送信息成功")}))}}),y=k,M=(n("a864"),n("e12a"),Object(E["a"])(y,a,i,!1,null,"8e2576b4",null));e["default"]=M.exports},a864:function(t,e,n){"use strict";n("c641")},bfc7:function(t,e,n){t.exports=n.p+"static/img/icon_wx.dfac8ef6.png"},c641:function(t,e,n){},e12a:function(t,e,n){"use strict";n("6806")}}]);