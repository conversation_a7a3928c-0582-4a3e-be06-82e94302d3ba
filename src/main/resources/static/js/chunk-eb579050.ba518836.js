(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-eb579050"],{"12f9":function(t,e,i){},"3d5d":function(t,e,i){"use strict";i.d(e,"e",(function(){return a})),i.d(e,"b",(function(){return o})),i.d(e,"d",(function(){return l})),i.d(e,"a",(function(){return r})),i.d(e,"c",(function(){return s}));var a={formcode:"D03M06B1List",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Voucher.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Voucher.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Voucher.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.summary"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.assessor"}]},o={formcode:"D03M06B1Cash",item:[{itemcode:"cashaccname",itemname:"出纳账户",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"amount",itemname:"金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},l={formcode:"D03M06B1Item",item:[{itemcode:"invobillcode",itemname:"发票单据号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"invocode",itemname:"发票编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"invoamount",itemname:"发票金额",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"金额",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D03M06B1List",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Voucher.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.summary"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.assessor"}]},s={formcode:"D03M06B1Cite",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Voucher.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Voucher.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Voucher.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.summary"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Voucher.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Voucher.assessor"}]}},"6c68":function(t,e,i){"use strict";i("12f9")},aeac:function(t,e,i){"use strict";i("be85")},b686:function(t,e,i){"use strict";i("fae8")},bca8:function(t,e,i){},be85:function(t,e,i){},bf47:function(t,e,i){},c113:function(t,e,i){},ce51:function(t,e,i){"use strict";i("bf47")},d85f:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{writedate:!1,formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getSuppGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,changeBillType:t.changeBillType}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px",height:"100%"}},[i("EditCash",{ref:"cashitem",style:{width:"99%",height:"其他付款"!=t.formdata.billtype?"50%":"100%"},attrs:{lstitem:t.formdata.cash,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate,itemAmount:t.itemAmount},on:{bindData:t.bindData,computerCashAmount:t.computerCashAmount}}),"其他付款"!=t.formdata.billtype?i("EditItem",{ref:"elitem",staticStyle:{width:"99%",height:"48%","margin-top":"10px"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData,computerItemAmount:t.computerItemAmount}}):t._e()],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M06B1Edit",commonurl:"/D03M06B1/printBill",weburl:"/D03M06B1/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M06B1Edit",examineurl:"/D03M06B1/sendapprovel"}}),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D03M06B1"==t.processModel?i("D03M06B1List",{ref:"D03M06B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0,isHongchong:!0}}):t._e()],1):t._e()],1)},o=[],l=i("2909"),r=(i("e9c4"),i("a9e3"),i("b64b"),i("d3b7"),i("ac1f"),i("6062"),i("3ca3"),i("5319"),i("159b"),i("ddb0"),i("b775"));const s={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D03M06B1/create",a).then(t=>{console.log(t),200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D03M06B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){r["a"].get("/D03M06B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,r["a"].get("/D03M06B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M06B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M06B1/closed?type="+(3==t?1:0);r["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=s,c=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){return t.$emit("changeBillType")}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购付款",value:"采购付款"}}),i("el-option",{attrs:{label:"单据付款",value:"单据付款"}}),i("el-option",{attrs:{label:"其他付款",value:"其他付款"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},["外协付款"==t.formdata.billtype?i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"外协厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B4/getPageList",type:"外协厂商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1):i("div",[i("el-form-item",{attrs:{label:"供应厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"付款金额："}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.billamount))])])],1)],1)],1)},m=[],d={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupname:[{required:!0,trigger:"blur",message:"客户为必填项"}],operator:[{required:!0,trigger:"blur",message:"经办人为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},u=d,f=(i("aeac"),i("2877")),h=Object(f["a"])(u,c,m,!1,null,"0ccea106",null),p=h.exports,b=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},editOption:t.editOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"采购发票","append-to-body":!0,visible:t.selordervisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"selOrder",attrs:{multi:1,selecturl:"/D03M05B1/getOnlinePageTh?groupid="+t.formdata.groupid+"&appl=1"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.deliveryvisible?i("el-dialog",{attrs:{title:"收货单","append-to-body":!0,visible:t.deliveryvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.deliveryvisible=e}}},[i("SelDelivery",{ref:"selDelivery",attrs:{multi:1,selecturl:"/D03M03R1/getOnlinePaidPageTh?groupid="+t.formdata.groupid+"&appl=1"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selDelivery()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.deliveryvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},g=[],y=i("ade3"),v=(i("c740"),i("caad"),i("2532"),i("c7cd"),{amount:0,custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",invoamount:0,invobillcode:"",invocode:"",invoid:"",pid:"",remark:"",rownum:0}),w={amount:0,cashaccid:"",cashaccname:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",pid:"",remark:"",rownum:0},k=i("3d5d"),x=i("dd56"),S=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto","min-height":"352 px",width:"100%"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据日期",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormats")(e.row.billdate)))])]}}])}),i("el-table-column",{attrs:{label:"供应商编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupuid))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"含税金额",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtaxamount))])]}}])}),i("el-table-column",{attrs:{label:"已付金额",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billpaid))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},$=[],_=i("333d"),D={components:{Pagination:_["a"]},props:["multi","groupid","selecturl"],data:function(){return{title:"采购订单",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M03B1/getPageList";r["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtype:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},O=D,B=(i("e79b"),Object(f["a"])(O,S,$,!1,null,"c89161e8",null)),C=B.exports,F={name:"Elitem",components:{SelOrder:x["a"],SelDelivery:C},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return Object(y["a"])(Object(y["a"])(Object(y["a"])(Object(y["a"])(Object(y["a"])(Object(y["a"])(Object(y["a"])({listLoading:!1,selordervisible:!1,deliveryvisible:!1,setColumsVisible:!1,lst:[],keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:k["d"],customList:[],editmarkfiles:[],countfiles:["amount","invoamount"],columsData:[],columnHidden:[],footerData:[],copyText:""},"columnHidden",[]),"checkboxOption",{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}}),"editOption",{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}}),"rowScroll",0),"virtualScrollOption",{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}}),"cellAutofillOption",{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],l=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=l&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}}),"clipboardOption",{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}})},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=k["d"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(k["d"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=[];this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["index","amount"]);var t=this.footerData[0].amount;this.$emit("computerItemAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var l=t[o],r=0;r<i.length;r++){var s=i[r];this.lst[e+o][s]=l[s].replace(/^\s*|\s*$/g,""),this.countfiles.includes(s)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.formdata.groupid?"采购付款"==this.formdata.billtype?this.selordervisible=!0:this.deliveryvisible=!0:this.$message.warning("请选择往来单位")},selOrder:function(){var t=this.$refs.selOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},v);a.amount=this.$fomatFloat(i.taxamount-i.paid,2),a.invoamount=i.taxamount,a.invobillcode=i.refno,a.invocode=i.invocode,a.invoid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selWaXie:function(){var t=this.$refs.selWaXie.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},v);a.invoamount=0,a.invobillcode=i.refno,a.invocode="",a.invoid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selDelivery:function(){var t=this.$refs.selDelivery.$refs.selectVal.selection;if(0!=t.length){this.deliveryvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},v);"采购退货"==i.billtype||"其他退货"==i.billtype?a.amount=this.$fomatFloat(0-i.billtaxamount-i.billpaid,2):a.amount=this.$fomatFloat(i.billtaxamount-i.billpaid,2),a.invoamount=i.billtaxamount,a.invobillcode=i.refno,a.invoid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},P=F,V=(i("eb4d"),Object(f["a"])(P,b,g,!1,null,"0a1b9eaa",null)),T=V.exports,M=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},editOption:t.editOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"出纳账户","append-to-body":!0,visible:t.selordervisible,width:"1000px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.SelOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},I=[],R=i("233f"),N={name:"Elitem",components:{SelOrder:R["a"]},props:["formdata","lstitem","idx","itemAmount","formstate","formtemplateItem"],data:function(){var t=this;return{listLoading:!1,selordervisible:!1,setColumsVisible:!1,lst:[],keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:k["b"],customList:[],editmarkfiles:[],countfiles:["amount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],l=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=l&&t.countfiles.includes(column.field)&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=k["b"];this.$getColumn(k["b"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=[];this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["cashaccname","amount"]);var t=this.footerData[0].amount;this.$emit("computerCashAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var l=t[o],r=0;r<i.length;r++){var s=i[r];this.lst[e+o][s]=l[s].replace(/^\s*|\s*$/g,""),this.countfiles.includes(s)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.selordervisible=!0},SelOrder:function(){var t=this.$refs.SelOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},w);a.amount=this.itemAmount,a.cashaccid=i.id,a.cashaccname=i.accountname,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},A=N,L=(i("6c68"),Object(f["a"])(A,M,I,!1,null,"7a7021c1",null)),E=L.exports,H={header:{type:0,title:"付款单",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购付款",value:"采购付款"},{label:"单据付款",value:"单据付款"},{label:"外协付款",value:"外协付款"},{label:"其他付款",value:"其他付款"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"供应厂商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"billamount",label:"付款金额：",type:"text",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},list:{type:0,content:[]},item:{type:0,content:[]}},K=i("dcb4"),j=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","billamount","operator","citecode","returnuid","orguid","summary","fmdocmark","fmdoccode","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],q=["id","pid","invoid","invobillcode","invocode","invoamount","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],z=["id","pid","cashaccid","cashaccname","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],G={params:j,paramsItem:q,paramsCash:z},J=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,divided:!0,label:"红 冲",icon:"el-icon-edit-outline",disabled:"this.formstate==0?true:(!!this.formdata.orguid? true : !!this.formdata.returnuid)",methods:"hongChong",param:"",children:[]}],W=[{show:1,divided:!1,label:"红冲记录",icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D01M08B1",label:"红冲记录"},children:[]}],U={name:"Formedit",components:{FormTemp:K["a"],EditHeader:p,EditItem:T,EditCash:E,D03M06B1List:function(){return Promise.all([i.e("chunk-commons"),i.e("chunk-0f9ed869")]).then(i.bind(null,"9b2e"))}},props:["idx","isDialog","initData","billcode","isprocessDialog"],data:function(){return{title:"付款单",operateBar:J,processBar:W,formdata:{abbreviate:"",billamount:0,assessdate:new Date,assessor:"",billdate:new Date,billtitle:"",billtype:"采购付款",citecode:"",createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,createdate:"",groupid:"",groupname:"",groupuid:"",item:[],cash:[],lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,operator:"",orguid:"",outamount:0,refno:"",returnuid:"",revision:0,summary:""},itemAmount:0,cashAmount:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:H,formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;r["a"].get("/SaFormCustom/getEntityByCode?key=D03M06B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):H,t.formtemplate.footer.type||(t.formtemplate.footer=H.footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&r["a"].get("/D03M06B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1,"其他收款"==t.formdata.billtype&&t.$refs.cashitem.catchHight()):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},computerItemAmount:function(t){this.itemAmount=t},computerCashAmount:function(t){this.cashAmount=t,this.formdata.billamount=Number(this.cashAmount)},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if("其他付款"!=this.formdata.billtype){if(this.formdata.billamount!=this.itemAmount||this.formdata.billamount!=this.cashAmount)return void this.$message.warning("付款金额 与 出纳金额和发票的金额不一致");this.formdata.item=this.$refs.elitem.lst,this.formdata.citecode="";for(var e="",i=0;i<this.formdata.item.length;i++){var a=this.formdata.item[i];e+=a.invobillcode+","}var o=/,$/gi;e=e.replace(o,"");var r=e.split(","),s=Object(l["a"])(new Set(r));for(i=0;i<s.length;i++)this.formdata.citecode+=s[i]+",";this.formdata.citecode=this.formdata.citecode.replace(o,"")}this.submitting=1,this.formdata.cash=this.$refs.cashitem.lst;var c={item:[],cash:[]};c=this.$getParam(G,c,this.formdata),0==this.idx?n.add(c).then((function(e){t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0})):n.update(c).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0}))},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(e)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t,console.log(this.processModel,"ss")},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.item=[],this.formdata.cash=[]},autoClear:function(t){this.formdata.groupname="",this.formdata.groupid=""},changeBillType:function(){this.formdata.cash=[],this.formdata.item=[],this.formdata.taxamount=0,this.$refs.cashitem.catchHight()},hongChong:function(){var t=this;console.log("红冲"),this.$confirm("红冲后不可退回，是否确认将该单据转为红冲单？","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((function(){var e=Object.assign({},t.formdata);e.billtype="付款红冲",e.billtitle="【"+t.formdata.refno+"】付款单转入",e.orguid=t.formdata.refno,e.billamount=0-t.formdata.billamount,e.outamount=0,e.cash.forEach((function(e,i){e.amount=0-t.formdata.cash[i].amount})),e.item.forEach((function(e,i){e.amount=0-t.formdata.item[i].amount})),e.billdate=new Date,t.$delete(e,"refno"),t.$delete(e,"assessor"),t.$delete(e,"assessorid"),t.$delete(e,"assessdate"),t.$delete(e,"lister"),t.$delete(e,"listerid"),t.$delete(e,"createby"),t.$delete(e,"createbyid"),t.$delete(e,"createdate"),t.$delete(e,"modifydate"),t.$delete(e,"operator"),r["a"].post("/D03M06B1/create",JSON.stringify(e)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"红冲成功"),t.$emit("changeIdx",e.data.data.id),t.$emit("bindData"),t.bindData()):t.$message.warning(e.data.msg||"红冲失败")})).catch((function(e){t.$message.warning(e||"红冲失败")}))})).catch((function(){}))},billSwitch:function(t){if("D03M05B1"==t){this.formdata.billtype="采购付款",this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】采购开票转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billamount=this.initData.billamount,this.formdata.citecode=this.initData.refno,this.formdata.item=[];var e={invoid:this.initData.id,invobillcode:this.initData.refno,invocode:this.initData.invocode,invoamount:this.initData.taxamount,amount:this.$fomatFloat(this.initData.taxamount-this.initData.paid,2)};this.formdata.item.push(e)}}}},X=U,Y=(i("b686"),Object(f["a"])(X,a,o,!1,null,"72ef1b58",null));e["default"]=Y.exports},dd56:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtitle))])]}}])}),i("el-table-column",{attrs:{label:"发票编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.invocode))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"税率%",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.taxrate))])]}}])}),i("el-table-column",{attrs:{label:"含税金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.taxamount)+" ")]}}])}),i("el-table-column",{attrs:{label:"已付金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.paid)+" ")]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],l=(i("99af"),i("e9c4"),i("d3b7"),i("25f0"),i("4d90"),i("b775")),r=i("333d"),s={components:{Pagination:r["a"]},filters:{dateFormat:function(t){var e=new Date(t),i=e.getFullYear(),a=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0");return"".concat(i,"/").concat(a,"/").concat(o)}},props:["multi","selecturl"],data:function(){return{title:"采购开票",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){console.log(t),this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M05B1/getPageTh";l["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},n=s,c=(i("ce51"),i("2877")),m=Object(c["a"])(n,a,o,!1,null,"c300cc12",null);e["a"]=m.exports},e79b:function(t,e,i){"use strict";i("bca8")},eb4d:function(t,e,i){"use strict";i("c113")},fae8:function(t,e,i){}}]);