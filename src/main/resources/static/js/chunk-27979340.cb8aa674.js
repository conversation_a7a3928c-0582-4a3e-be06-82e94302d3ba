(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-27979340"],{"015e":function(t,e,i){"use strict";i("0e1a")},"0362":function(t,e,i){},"0377":function(t,e,i){"use strict";i("6947")},"03b9":function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"采购订单",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购计划",value:"采购计划"},{label:"采购计划",value:"采购计划"},{label:"其他采购",value:"其他采购"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""},{col:5,code:"orderno",label:"订单编号",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"供应商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"arrivaladd",label:"收货地址",type:"input",methods:"",param:""},{col:5,code:"transport",label:"物流方式",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"billplandate",label:"单据计划",type:"date",methods:"",param:""},{col:5,code:"payment",label:"付款方式",type:"input",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},"04f2":function(t,e,i){},"059b":function(t,e,i){"use strict";i("7d2f")},"07ac":function(t,e,i){var a=i("23e7"),o=i("6f53").values;a({target:"Object",stat:!0},{values:function(t){return o(t)}})},"0b0b":function(t,e,i){},"0bdb":function(t,e,i){},"0e1a":function(t,e,i){},"0fdb":function(t,e,i){"use strict";i("d924")},1276:function(t,e,i){"use strict";var a=i("d784"),o=i("44e7"),s=i("825a"),r=i("1d80"),n=i("4840"),l=i("8aa5"),d=i("50c4"),m=i("14c3"),c=i("9263"),u=i("d039"),f=[].push,p=Math.min,h=4294967295,g=!u((function(){return!RegExp(h,"y")}));a("split",2,(function(t,e,i){var a;return a="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,i){var a=String(r(this)),s=void 0===i?h:i>>>0;if(0===s)return[];if(void 0===t)return[a];if(!o(t))return e.call(a,t,s);var n,l,d,m=[],u=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),p=0,g=new RegExp(t.source,u+"g");while(n=c.call(g,a)){if(l=g.lastIndex,l>p&&(m.push(a.slice(p,n.index)),n.length>1&&n.index<a.length&&f.apply(m,n.slice(1)),d=n[0].length,p=l,m.length>=s))break;g.lastIndex===n.index&&g.lastIndex++}return p===a.length?!d&&g.test("")||m.push(""):m.push(a.slice(p)),m.length>s?m.slice(0,s):m}:"0".split(void 0,0).length?function(t,i){return void 0===t&&0===i?[]:e.call(this,t,i)}:e,[function(e,i){var o=r(this),s=void 0==e?void 0:e[t];return void 0!==s?s.call(e,o,i):a.call(String(o),e,i)},function(t,o){var r=i(a,t,this,o,a!==e);if(r.done)return r.value;var c=s(t),u=String(this),f=n(c,RegExp),b=c.unicode,y=(c.ignoreCase?"i":"")+(c.multiline?"m":"")+(c.unicode?"u":"")+(g?"y":"g"),v=new f(g?c:"^(?:"+c.source+")",y),w=void 0===o?h:o>>>0;if(0===w)return[];if(0===u.length)return null===m(v,u)?[u]:[];var x=0,k=0,_=[];while(k<u.length){v.lastIndex=g?k:0;var D,S=m(v,g?u:u.slice(k));if(null===S||(D=p(d(v.lastIndex+(g?0:k)),u.length))===x)k=l(u,k,b);else{if(_.push(u.slice(x,k)),_.length===w)return _;for(var O=1;O<=S.length-1;O++)if(_.push(S[O]),_.length===w)return _;k=x=D}}return _.push(u.slice(x)),_}]}),!g)},1535:function(t,e,i){},"174a":function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_require__.d(__webpack_exports__,"a",(function(){return btnExpress})),__webpack_require__.d(__webpack_exports__,"b",(function(){return getTgcolumnVal}));var D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("c7eb"),D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("1da1"),core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("caad"),core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_2___default=__webpack_require__.n(core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_2__),core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("d81d"),core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_3___default=__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_3__),core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("ac1f"),core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_4___default=__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_4__),core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__("2532"),core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_5___default=__webpack_require__.n(core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_5__),core_js_modules_es_string_match_js__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__("466d"),core_js_modules_es_string_match_js__WEBPACK_IMPORTED_MODULE_6___default=__webpack_require__.n(core_js_modules_es_string_match_js__WEBPACK_IMPORTED_MODULE_6__),core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__("5319"),core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_7___default=__webpack_require__.n(core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_7__),_utils_request__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__("b775"),_inksyun_utils__WEBPACK_IMPORTED_MODULE_9__=__webpack_require__("ba11"),vue__WEBPACK_IMPORTED_MODULE_10__=__webpack_require__("2b0e"),exprData=[],modulecode="",countfiles=["quantity","price","amount","taxprice","taxamount","itemtaxrate"],tgcolumnVal="";function getFormula(t){return _getFormula.apply(this,arguments)}function _getFormula(){return _getFormula=Object(D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__["a"])(Object(D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__["a"])().mark((function t(e){return Object(D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,_utils_request__WEBPACK_IMPORTED_MODULE_8__["a"].get("/SaBillExpression/getListByCode?key="+e).then((function(t){exprData=200==t.data.code?t.data.data:[]}));case 2:case"end":return t.stop()}}),t)}))),_getFormula.apply(this,arguments)}function transExprTemp(t,e){var i=t.match(/{[^}{]*?}/g);i=i.map((function(t){return t=t.replace("{",""),t=t.replace("}",""),t}));for(var a=t,o=0;o<i.length;o++)a=a.replace("${"+i[o]+"}","row['"+i[o]+"']");return a}function setTgcolumnVal(t){var e=t?t.toLowerCase():"";return e}function btnExpress(t,e,i){return _btnExpress.apply(this,arguments)}function _btnExpress(){return _btnExpress=Object(D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__["a"])(Object(D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__["a"])().mark((function _callee2(code,row,colname){var copyRow,i,Item,orgcolumns;return Object(D_gitLab_saweb_ui_oneweb_som_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__["a"])().wrap((function _callee2$(_context2){while(1)switch(_context2.prev=_context2.next){case 0:if(modulecode==code){_context2.next=4;break}return modulecode=code,_context2.next=4,getFormula(code);case 4:if(tgcolumnVal="",!exprData.length){_context2.next=11;break}for(copyRow=Object.assign({},row),i=0;i<exprData.length;i++)Item=exprData[i],orgcolumns=Item.orgcolumns.split(","),orgcolumns.includes(colname)&&(1==Item.returntype?vue__WEBPACK_IMPORTED_MODULE_10__["default"].set(copyRow,Item.tgcolumn,eval(transExprTemp(Item.exprtemp))):(vue__WEBPACK_IMPORTED_MODULE_10__["default"].set(copyRow,Item.tgcolumn,_inksyun_utils__WEBPACK_IMPORTED_MODULE_9__["a"].count.fomatFloat(eval(transExprTemp(Item.exprtemp)),Item.decnum)),countfiles.includes(Item.tgcolumn.toLowerCase())&&(tgcolumnVal=setTgcolumnVal(Item.tgcolumn))));return _context2.abrupt("return",copyRow);case 11:return _context2.abrupt("return",row);case 12:case"end":return _context2.stop()}}),_callee2)}))),_btnExpress.apply(this,arguments)}function getTgcolumnVal(){return tgcolumnVal}},"1d9e":function(t,e,i){"use strict";i("a5ab")},"1f11":function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"采购验收",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购验收",value:"采购验收"},{label:"采购退货",value:"采购退货"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"供应商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"arrivaladd",label:"收货地址",type:"input",methods:"",param:""},{col:5,code:"transport",label:"物流方式",type:"input",methods:"",param:""}]}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},2499:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px",width:"100%"}},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1)]),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"450px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"客户订单号",align:"center",width:"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.custorderid))])]}}])}),i("el-table-column",{attrs:{label:"客户",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),t._l(t.customList,(function(t,e){return[i("el-table-column",{key:e,attrs:{"header-align":"center",align:"center",prop:t.attrkey,label:t.attrname}})]}))],2)],1),i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],s=i("c7eb"),r=i("1da1"),n=(i("c740"),i("e9c4"),i("b64b"),i("b775")),l=i("8daf"),d=i("40d9"),m={props:["multi","groupid","selecturl"],components:{Setcolums:l["a"]},data:function(){return{listLoading:!0,lst:[],strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},customList:[],setColumsVisible:!1,tableForm:d["d"]}},created:function(){this.bindData(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,n["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code?t.customList=e.data.data?e.data.data:[]:t.$message.warning(e.data.msg||"获取失败")})).catch((function(e){t.$message.error("请求错误")}));case 2:return e.next=4,n["a"].get("/SaDgFormat/getBillEntityByCode?code=D01M03B1Select").then((function(e){if(200==e.data.code){if(null==e.data.data){t.tableForm=d["d"];for(var i=0;i<t.customList.length;i++){var a=t.customList[i],o=t.tableForm.item.findIndex((function(e){return e.itemcode==t.customList[i].attrkey}));if(-1==o){var s={itemcode:a.attrkey,itemname:a.attrname,minwidth:"80",displaymark:a.listshow,overflow:1};t.tableForm.item.push(s)}}return}t.tableForm=e.data.data;for(i=0;i<t.customList.length;i++){a=t.customList[i],o=t.tableForm.item.findIndex((function(e){return e.itemcode==t.customList[i].attrkey}));if(-1==o){s={itemcode:a.attrkey,itemname:a.attrname,minwidth:"80",displaymark:a.listshow,overflow:1};t.tableForm.item.push(s)}}}})).catch((function(e){t.$message.error("请求出错")}));case 4:case"end":return e.stop()}}),e)})))()},getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){var i;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.listLoading=!0,t.listLoading=!0,i=t.selecturl?t.selecturl:"/D01M03B1/getPageList",e.next=5,n["a"].post(i,JSON.stringify(t.queryParams)).then((function(e){if(200==e.data.code){console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 5:return e.next=7,n["a"].get("/D91M01S2/getListByShow").then((function(e){200==e.data.code||t.$message.warning(e.data.msg||"获取spu失败")})).catch((function(e){t.$message.error("请求错误")}));case 7:case"end":return e.stop()}}),e)})))()},search:function(t){var e=t.split(",");0!=e.length?1==e.length?(this.queryParams.SearchPojo={goodsuid:e[0],goodsname:e[0],goodsunit:e[0],groupid:e[0],goodsspec:e[0],partid:e[0],refno:e[0],custorderid:e[0],groupname:e[0],attributestr:e[0]},this.$delete(this.queryParams,"scenedata")):e.length>1&&(this.queryParams.scenedata=[{field:"Mat_Goods.goodsuid",fieldtype:0,math:"like",value:"".concat(e[0])},{field:"Mat_Goods.goodsname",fieldtype:0,math:"like",value:"".concat(e[1])}],3==e.length&&this.queryParams.scenedata.push({field:"Mat_Goods.partid",fieldtype:0,math:"like",value:"".concat(e[2])}),this.$delete(this.queryParams,"SearchPojo")):(this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"scenedata")),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},c=m,u=(i("fb056"),i("2877")),f=Object(u["a"])(c,a,o,!1,null,"56660287",null);e["a"]=f.exports},"28f6":function(t,e,i){"use strict";i("c6bc")},3218:function(t,e,i){},3470:function(t,e,i){},"367a":function(t,e,i){},3823:function(t,e,i){"use strict";i("8626")},3949:function(t,e,i){"use strict";i("dea7")},"3ad2":function(t,e,i){"use strict";i("848e")},"3e09":function(t,e,i){},"3f1b":function(t,e,i){"use strict";i("90fe")},"3fbc":function(t,e,i){"use strict";i("a485")},"47e9":function(t,e,i){},"49b1":function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return s})),i.d(e,"b",(function(){return r}));var a={formcode:"D01M08B1DEPList",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Bus_Deposit.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deposit.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Bus_Deposit.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deposit.billdate"},{itemcode:"abbreviate",itemname:"简称",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.abbreviate"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户名称",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Deposit.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deposit.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deposit.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Deposit.summary"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deposit.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Bus_Deposit.assessor"},{itemcode:"outamount",itemname:"转出金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Deposit.outamount"}]},o={formcode:"D01M08B1DEPItem",item:[{itemcode:"machbillcode",itemname:"订单单号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"billtaxamount",itemname:"单据金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"金额",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},s={formcode:"D01M08B1DEPCash",item:[{itemcode:"cashaccname",itemname:"出纳账户",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"amount",itemname:"金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D01M08B1DEPCite",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Bus_Deposit.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deposit.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Bus_Deposit.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deposit.billdate"},{itemcode:"abbreviate",itemname:"简称",minwidth:"80",displaymark:1,overflow:1,datasheet:"App_Workgroup.abbreviate"},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"客户名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Deposit.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deposit.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Deposit.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Bus_Deposit.summary"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Deposit.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Bus_Deposit.assessor"},{itemcode:"outamount",itemname:"转出金额",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Deposit.outamount"}]}},"4b34":function(t,e,i){},"4cf0":function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"a",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"b",(function(){return r}));var a={formcode:"D03M06B1PREList",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Prepayments.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.summary"},{itemcode:"outamount",itemname:"转出金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.outamount"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.status"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.assessor"}]},o={formcode:"D03M06B1PRECash",item:[{itemcode:"cashaccname",itemname:"出纳账户",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"amount",itemname:"金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},s={formcode:"D03M06B1PREItem",item:[{itemcode:"orderbillcode",itemname:"订单单号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"billtaxamount",itemname:"单据金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"金额",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D03M06B1PRECite",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Prepayments.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billamount",itemname:"金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.billamount"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.operator"},{itemcode:"citecode",itemname:"相关单据",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.citecode"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.summary"},{itemcode:"outamount",itemname:"转出金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Prepayments.outamount"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.status"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Prepayments.assessor"}]}},"4d45":function(t,e,i){},"4e4d":function(t,e,i){},"4f5e":function(t,e,i){},"501c":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},o=[],s=i("c7eb"),r=i("1da1"),n=(i("caad"),i("e9c4"),i("a9e3"),i("d3b7"),i("2532"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),i("b775")),l=i("3d5d"),d=i("b893"),m={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"d85f"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:l["c"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={citecode:this.searchVal};this.queryParams.SearchPojo=e;var i="/D03M06B1/getPageTh";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(d["d"])()[0],EndDate:Object(d["d"])()[1]}),n["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total)})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(l["c"].formcode,l["c"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("billtype"==t.itemcode){s=a("span",[o[t.itemcode]]);return o[t.itemcode].includes("红冲")&&(s=a("span",{style:"color:#f44336"},[o[t.itemcode]])),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxamount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){console.log(t,"222"),this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},c=m,u=(i("5880"),i("2877")),f=Object(u["a"])(c,a,o,!1,null,"7c16eaee",null);e["a"]=f.exports},"57d8":function(t,e,i){},5880:function(t,e,i){"use strict";i("4f5e")},"58b8":function(t,e,i){"use strict";i("57d8")},"594e":function(t,e,i){"use strict";i("47e9")},"5b00":function(t,e,i){"use strict";i("66cd")},"65e3":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],s=(i("e9c4"),i("b775")),r=i("333d"),n={components:{Pagination:r["a"]},props:["multi","groupid","selecturl"],data:function(){return{title:"采购订单",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M02B1/getOnlinePageList?groupid="+this.groupid;s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={goodsname:t,goodsuid:t,goodsspec:t,groupuid:t,groupname:t,refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},l=n,d=(i("8da8"),i("2877")),m=Object(d["a"])(l,a,o,!1,null,"ecf1cb7c",null);e["a"]=m.exports},"664a":function(t,e,i){"use strict";i("4b34")},"66cd":function(t,e,i){},6947:function(t,e,i){},"6bae":function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"预收款",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"销售预收",value:"销售预收"},{label:"其他预收",value:"其他预收"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"客户",searchtype:"customer",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"billamount",label:"预收金额",type:"text",methods:"",param:""}]}]},cash:{type:0,content:[{itemcode:"cashaccname",itemname:"出纳账户",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"amount",itemname:"金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"业务员",billcode:"sale.salesman",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},list:{type:0,content:[]},item:{type:0,content:[]}}},"6ccf":function(t,e,i){},"6e4b":function(t,e,i){},"6f53":function(t,e,i){var a=i("83ab"),o=i("df75"),s=i("fc6a"),r=i("d1e7").f,n=function(t){return function(e){var i,n=s(e),l=o(n),d=l.length,m=0,c=[];while(d>m)i=l[m++],a&&!r.call(n,i)||c.push(t?[i,n[i]]:n[i]);return c}};t.exports={entries:n(!0),values:n(!1)}},"703e":function(t,e,i){},7085:function(t,e,i){"use strict";i("8b95")},7922:function(t,e,i){"use strict";i("0bdb")},7996:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据时间",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormat")(e.row.billdate)))])]}}])}),i("el-table-column",{attrs:{label:"客户",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"单据金额",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtaxamount))])]}}])}),i("el-table-column",{attrs:{label:"业务员",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.salesman))])]}}])}),i("el-table-column",{attrs:{label:"款数",align:"center",width:"60px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-popover",{attrs:{placement:"left",trigger:"click",title:"单据明细"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:e.$index==t.mypopoverIndex,expression:"scope.$index == mypopoverIndex"}],staticStyle:{position:"relative","min-height":"100px"}},[i("mypopover",{ref:"mypopover",attrs:{tableForm:t.mypopoverTable,lst:t.mypopoverData}})],1),i("span",{staticClass:"textunderline",attrs:{slot:"reference"},on:{click:function(i){return i.stopPropagation(),t.getBillList(e.row,e.$index)}},slot:"reference"},[t._v(" "+t._s(e.row.itemcount)+" ")])])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],s=(i("e9c4"),i("b64b"),i("b775")),r=i("333d"),n=i("40d9"),l=i("f07e"),d={components:{Pagination:r["a"],mypopover:l["a"]},props:["multi","selecturl"],data:function(){return{title:"销售订单",listLoading:!0,lst:[],strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},mypopoverTable:n["c"],mypopoverData:[],mypopoverIndex:-1}},created:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D01M03B1/getPageTh";s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},getBillList:function(t,e){var i=this;this.mypopoverIndex=e,s["a"].get("/D01M03B1/getBillEntity?key=".concat(t.id)).then((function(t){if(200==t.data.code){i.mypopoverData=t.data.data.item;for(var e=0;e<i.mypopoverData.length;e++){var a=i.mypopoverData[e];if(""==a.attributejson||null==a.attributejson);else for(var o=JSON.parse(a.attributejson),s=0;s<o.length;s++)a[o[s].key]=o[s].value}}else i.mypopoverData=[]}))}}},m=d,c=(i("1d9e"),i("2877")),u=Object(c["a"])(m,a,o,!1,null,"194684bb",null);e["a"]=u.exports},"7d2f":function(t,e,i){},"7e4c":function(t,e,i){"use strict";i("e149")},"7f963":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getWorkGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D04M08B1Edit",commonurl:"/D04M08B1/printBill",weburl:"/D04M08B1/printWebBill"}}),i("PrintServer",{ref:"PrintMultiServer",attrs:{formdata:t.formdata,printcode:"D04M08B1EditMulti",commonurl:"/D04M08B1/printWebBillMulti",weburl:"/D04M08B1/printWebBillMulti"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D04M08B1Edit",examineurl:"/D04M08B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},["D04M01B3"==t.processModel?i("D04M01B3",{ref:"D04M01B3",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D04M08B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e()],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D04M01B3"==t.processModel?i("D04M01B3List",{ref:"D04M01B3List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=(i("b64b"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D04M08B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D04M08B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D04M08B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D04M08B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D04M08B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D04M08B1/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=r,l=["id","refno","billtype","billdate","billtitle","groupid","branthname","groupuid","groupname","abbreviate","grouplevel","operator","summary","modulecode","citeuid","citeid","projectid","projcode","projname","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],d=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","quantity","plandate","remark","rownum","pickqty","finishcost","citeuid","citeitemid","customer","custpo","backqty","backcost","closed","stdqty","storeid","storename","location","batchno","machuid","machitemid","machitemgoodid","machgroupid","mainplanuid","mainplanitemid","mainplanitemgoodid","mrpuid","mrpitemid","attributejson","mrpitemgoodid","wkbilltype","workuid","workitemid","workitemgoodid","workitemmatid","parentgoodsid","disannulmark","sourcetype","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],m={params:l,paramsItem:d},c={backcost:0,backqty:0,batchno:"",citeitemid:"",citeuid:"",closed:0,custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",customer:"",custpo:"",disannulmark:0,finishcost:0,finishqty:0,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",id:"",location:"",machgroupid:"",machitemgoodid:"",machitemid:"",machuid:"",mainplanitemgoodid:"",mainplanitemid:"",mainplanuid:"",mrpitemgoodid:"",mrpitemid:"",mrpuid:"",parentgoodsid:"",partid:"",pickqty:0,pid:"",plandate:new Date,quantity:0,remark:"",rownum:0,sourcetype:0,statecode:"",statedate:new Date,stdqty:0,storeid:"",wkbilltype:1,workitemgoodid:"",workitemid:"",workitemmatid:"",workuid:""},u=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,label:"一页两联",icon:"el-icon-printer",disabled:"this.formstate==0",methods:"printMultiServer",children:[]},{show:1,divided:!0,ieval:1,label:'this.formdata.billtype == "生产领料"|| this.formdata.billtype == "领料单" || this.formdata.billtype == "外协领料"? "领料出库" : "退料入库"',icon:"",disabled:"this.formstate!=2",methods:"operateBill",param:"D04M01B3",children:[]}],f=[{show:1,ieval:1,divided:!1,label:'this.formdata.billtype == "生产领料"|| this.formdata.billtype == "领料单" || this.formdata.billtype == "外协领料"? "领料出库" : "退料入库"',icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D04M01B3",label:""},children:[]}],p=i("c7eb"),h=i("1da1");i("c740"),i("b680"),i("ac1f"),i("5319");function g(t,e,i){return b.apply(this,arguments)}function b(){return b=Object(h["a"])(Object(p["a"])().mark((function t(e,i,a){return Object(p["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,s["a"].get("/D91M01B1/getEntity?key="+i.goodsid).then((function(t){if(console.log("zhugoods",t),200==t.data.code){var e=t.data.data;1==e.matqtyunit?(i.goodsunit=e.weightunit,i.quantity=a.$fomatFloat(i.quantity*e.weightqty,4)):2==e.matqtyunit?(i.goodsunit=e.areaunit,i.quantity=a.$fomatFloat(i.quantity*e.areaqty,4)):3==e.matqtyunit?(i.goodsunit=e.volumeunit,i.quantity=a.$fomatFloat(i.quantity*e.volumeqty,4)):(i.goodsunit=e.goodsunit,i.quantity=a.$fomatFloat(i.quantity,4))}else a.$alert(t.data.msg||"查询物料为空","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){a.closeForm()}})}));case 2:return t.next=4,s["a"].get("/D91M01B1/getMapEntityByGoodsUid?key="+e).then((function(t){if(console.log("goods",t),200==t.data.code&&t.data.data.id){var o=t.data.data;i.goodsid=o.id,i.goodsname=o.goodsname,i.goodsspec=o.goodsspec,i.goodsuid=o.goodsuid,i.goodsphoto1=o.goodsphoto1,i.partid=o.partid,a.formdata.item.push(i)}else a.$alert(t.data.msg||"查询"+e+"物料为空","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){a.closeForm()}})}));case 4:case"end":return t.stop()}}),t)}))),b.apply(this,arguments)}function y(t,e,i,a){var o=t.$store.getters.userinfo.configs;if("D01M03B1"==e){t.formdata.billtype=i||"领料单",t.formdata.billtitle="【"+t.initData.refno+"】销售订单转入",t.formdata.item=[];for(var s=0;s<t.initData.item.length;s++){var r=t.initData.item[s],n=Object.assign({},a);n.citeitemid=r.id,n.citeuid=t.initData.refno,n.customer=t.initData.groupid,n.goodsid=r.goodsid,n.goodsname=r.goodsname,n.goodsphoto1=r.goodsphoto1,n.goodsspec=r.goodsspec,n.goodsuid=r.goodsuid,n.goodsunit=r.goodsunit,n.partid=r.partid,n.machgroupid=t.initData.groupid,n.machitemid=r.id,n.machuid=t.initData.refno,n.machdate=t.initData.billdate,n.mainplanitemid=r.mainplanitemid,n.mainplanuid=r.mainplanuid,n.mrpitemid=r.mrpitemid,n.mrpuid=r.mrpuid,n.location=r.location,n.batchno=r.batchno,n.quantity=t.$fomatFloat(r.quantity-r.outquantity,2),n.attributejson=r.attributejson,n.virtualitem=r.virtualitem,n.quantity<=0?(n.quantity=0,o&&o["system.bill.execfinishow"]&&JSON.parse(o["system.bill.execfinishow"])&&t.formdata.item.push(n)):t.formdata.item.push(n)}}else if("D01M03B1Mat"==e){t.formdata.billtype=i||"领料单",t.formdata.billtitle="【"+t.initData.refno+"】销售订单转入",t.formdata.item=[];for(s=0;s<t.initData.item.length;s++){r=t.initData.item[s],n=Object.assign({},a);n.citeitemid=r.id,n.citeuid=t.initData.refno,n.customer=t.initData.groupid,n.goodsid=r.goodsid,n.goodsname=r.goodsname,n.goodsphoto1=r.goodsphoto1,n.goodsspec=r.goodsspec,n.goodsuid=r.goodsuid,n.goodsunit=r.goodsunit,n.partid=r.partid,n.location=r.location,n.batchno=r.batchno,n.machgroupid=t.initData.groupid,n.machitemid=r.id,n.machuid=t.initData.refno,n.machdate=t.initData.billdate,n.mainplanitemid=r.mainplanitemid,n.mainplanuid=r.mainplanuid,n.mrpitemid=r.mrpitemid,n.mrpuid=r.mrpuid,n.quantity=t.$fomatFloat(r.quantity-r.outquantity,2),n.attributejson=r.attributejson,n.virtualitem=r.virtualitem,n.quantity<=0?(n.quantity=0,o&&o["system.bill.execfinishow"]&&JSON.parse(o["system.bill.execfinishow"])&&g(r.matcode,n,t)):g(r.matcode,n,t)}}else if("D05M01B4"==e||"D05M01B1"==e){t.formdata.billtype=i||"领料单",t.formdata.billtitle="【"+t.initData.refno+"】生产工单转入",t.formdata.groupid=t.initData.groupid,t.formdata.groupname=t.initData.groupname,t.formdata.groupuid=t.initData.groupuid,t.formdata.item=[];for(s=0;s<t.initData.item.length;s++){r=t.initData.item[s],n=Object.assign({},a);n.citeitemid=r.id,n.citeuid=t.initData.refno,n.customer=r.customer,n.goodsid=r.goodsid,n.goodsname=r.goodsname,n.goodsphoto1=r.goodsphoto1,n.goodsspec=r.goodsspec,n.goodsuid=r.goodsuid,n.goodsunit=r.goodsunit,n.partid=r.partid,n.location=r.location,n.batchno=r.batchno,n.machgroupid=r.machgroupid,n.machitemid=r.machitemid,n.machuid=r.machuid,n.machdate=r.machdate,n.mainplanitemid=r.mainplanitemid,n.mainplanuid=r.mainplanuid,n.mrpitemid=r.mrpitemid,n.mrpuid=r.mrpuid,n.workitemid=r.id,n.workuid=t.initData.refno,n.quantity=r.wkpcsqty.toFixed(2).replace(/\.00$/,""),n.attributejson=r.attributejson,n.virtualitem=r.virtualitem,n.quantity<=0?(n.quantity=0,o&&o["system.bill.execfinishow"]&&JSON.parse(o["system.bill.execfinishow"])&&t.formdata.item.push(n)):t.formdata.item.push(n)}}else if("D05M02B1"==e){t.formdata.billtype=i||"领料单",t.formdata.billtitle="【"+t.initData.refno+"】委外加工单转入",t.formdata.groupid=t.initData.groupid,t.formdata.groupname=t.initData.groupname,t.formdata.groupuid=t.initData.groupuid,t.formdata.item=[];for(s=0;s<t.initData.item.length;s++){r=t.initData.item[s],n=Object.assign({},a);n.citeitemid=r.id,n.citeuid=t.initData.refno,n.customer=r.groupid,n.goodsid=r.goodsid,n.goodsname=r.goodsname,n.goodsphoto1=r.goodsphoto1,n.goodsspec=r.goodsspec,n.goodsuid=r.goodsuid,n.goodsunit=r.goodsunit,n.partid=r.partid,n.machgroupid=r.machgroupid,n.machitemid=r.machitemid,n.machuid=r.machuid,n.machdate=r.machdate,n.mainplanitemid=r.mainplanitemid,n.mainplanuid=r.mainplanuid,n.mrpitemid=r.mrpitemid,n.mrpuid=r.mrpuid,n.workitemid=r.workitemid,n.workuid=r.workuid,n.quantity=r.subqty.toFixed(2).replace(/\.00$/,""),n.attributejson=r.attributejson,n.virtualitem=r.virtualitem,n.quantity<=0?(n.quantity=0,o&&o["system.bill.execfinishow"]&&JSON.parse(o["system.bill.execfinishow"])&&t.formdata.item.push(n)):t.formdata.item.push(n)}}else if("D05M02B2"===e){t.formdata.billtype=i||"领料单",t.formdata.billtitle="【"+t.initData.refno+"】其他委外加工转入",t.formdata.groupid=t.initData.groupid,t.formdata.groupname=t.initData.groupname,t.formdata.groupuid=t.initData.groupuid,t.formdata.item=[];for(s=0;s<t.initData.mat.length;s++){r=t.initData.mat[s],n=Object.assign({},a);n.citeitemid=r.id,n.citeuid=t.initData.refno,n.customer=r.groupid,n.goodsid=r.goodsid,n.goodsname=r.goodsname,n.goodsphoto1=r.goodsphoto1,n.goodsspec=r.goodsspec,n.goodsuid=r.goodsuid,n.goodsunit=r.goodsunit,n.partid=r.partid,n.machgroupid=r.machgroupid,n.machitemid=r.machitemid,n.machuid=r.machuid,n.machdate=r.machdate,n.mainplanitemid=r.mainplanitemid,n.mainplanuid=r.mainplanuid,n.mrpitemid=r.mrpitemid,n.mrpuid=r.mrpuid,n.workitemid=r.workitemid,n.workuid=r.workuid,n.quantity=r.quantity.toFixed(2).replace(/\.00$/,""),n.attributejson=r.attributejson,n.virtualitem=r.virtualitem,n.quantity<=0?(n.quantity=0,o&&o["system.bill.execfinishow"]&&JSON.parse(o["system.bill.execfinishow"])&&t.formdata.item.push(n)):t.formdata.item.push(n)}}else if("D05M01B1SL"==e||"D05M01B1ML"==e){console.log(t.selectList,"selectListselectListselectList"),console.log(t.initData,"initData"),t.formdata.billtype=i||"生产领料",t.formdata.billtitle="【"+t.initData.refno+"】生产工单转入",t.formdata.groupid=t.initData.groupid,t.formdata.groupname=t.initData.groupname,t.formdata.groupuid=t.initData.groupuid,t.formdata.item=[];var l=t.selectList.length>0?t.selectList:t.initData.mat;for(s=0;s<l.length;s++){r=l[s],n=Object.assign({},a);n.goodsid=r.goodsid,n.goodsname=r.goodsname,n.goodsphoto1=r.goodsphoto1,n.goodsspec=r.goodsspec,n.goodsuid=r.goodsuid,n.goodsunit=r.goodsunit,n.goodsmaterial=r.goodsmaterial,n.partid=r.partid,n.citeitemid=r.id,n.citeuid=t.initData.refno,n.customer=r.customer,n.custpo=r.custpo,n.machgroupid=r.machgroupid,n.machitemid=r.machitemid,n.machuid=r.machuid,n.mainplanitemid=r.mainplanitemid,n.mainplanuid=r.mainplanuid,n.mrpitemid=r.mrpitemid,n.mrpuid=r.mrpuid,n.plandate=t.initData.plandate?t.initData.plandate:new Date,n.quantity=i?r.finishqty:"物料"==r.goodsstate?t.$fomatFloat(r.bomqty-r.finishqty,2):t.$fomatFloat(r.stoplanqty-r.finishqty,2),n.workitemmatid=r.id,n.workuid=t.initData.refno,n.workitemgoodid=r.goodsid,n.attributejson=r.attributejson,n.virtualitem=r.virtualitem;var d=t.initData.item.findIndex((function(t){return t.rowcode==r.itemrowcode}));-1!=d&&(n.machuid=t.initData.item[d].machuid,n.machitemid=t.initData.item[d].machitemid,n.machgroupid=t.initData.item[d].machgroupid,n.mainplanitemid=t.initData.item[d].mainplanitemid,n.mainplanuid=t.initData.item[d].mainplanuid,n.mrpitemid=t.initData.item[d].mrpitemid,n.mrpuid=t.initData.item[d].mrpuid),n.quantity&&t.formdata.item.push(n)}}else if("D05M02B1ML"==e){t.formdata.billtype=i||"领料单",t.formdata.billtitle="【"+t.initData.refno+"】委外加工单转入",t.formdata.item=[];for(l=t.initData.mat,s=0;s<l.length;s++){r=l[s],n=Object.assign({},a);n.goodsid=r.goodsid,n.goodsname=r.goodsname,n.goodsphoto1=r.goodsphoto1,n.goodsspec=r.goodsspec,n.goodsuid=r.goodsuid,n.goodsunit=r.goodsunit,n.goodsmaterial=r.goodsmaterial,n.partid=r.partid,n.citeitemid=r.id,n.citeuid=t.initData.refno,n.customer=r.customer,n.custpo=r.custpo,n.machgroupid=r.machgroupid,n.machitemid=r.machitemid,n.machuid=r.machuid,n.machdate=r.machdate,n.mainplanitemid=r.mainplanitemid,n.mainplanuid=r.mainplanuid,n.mrpitemid=r.mrpitemid,n.mrpuid=r.mrpuid,n.quantity=i?r.finishqty:"物料"==r.goodsstate?t.$fomatFloat(r.bomqty-r.finishqty,2):t.$fomatFloat(r.stoplanqty-r.finishqty,2),n.workitemid=r.workitemid,n.workitemmatid=r.workitemmatid,n.workuid=r.workuid,n.workitemgoodid=r.workitemgoodid,n.attributejson=r.attributejson,n.virtualitem=r.virtualitem,n.wkbilltype=2;d=t.initData.item.findIndex((function(t){return t.rowcode==r.itemrowcode}));-1!=d&&(n.machuid=t.initData.item[d].machuid,n.machitemid=t.initData.item[d].machitemid,n.machgroupid=t.initData.item[d].machgroupid,n.mainplanitemid=t.initData.item[d].mainplanitemid,n.mainplanuid=t.initData.item[d].mainplanuid,n.mrpitemid=t.initData.item[d].mrpitemid,n.mrpuid=t.initData.item[d].mrpuid),t.formdata.item.push(n)}}else if("D05M03B1"==e){console.log(t.selectList,"selectListselectListselectList"),console.log(t.initData,"initData"),t.formdata.billtype=i||"生产领料",t.formdata.billtitle="【"+t.initData.refno+"】生产验收单转入",t.formdata.groupid=t.initData.groupid,t.formdata.groupname=t.initData.groupname,t.formdata.groupuid=t.initData.groupuid,t.formdata.item=[];for(l=t.selectList.length>0?t.selectList:t.initData.mat,s=0;s<l.length;s++){r=l[s],n=Object.assign({},a);n.goodsid=r.goodsid,n.goodsname=r.goodsname,n.goodsphoto1=r.goodsphoto1,n.goodsspec=r.goodsspec,n.goodsuid=r.goodsuid,n.goodsunit=r.goodsunit,n.goodsmaterial=r.goodsmaterial,n.partid=r.partid,n.sourcetype=2,n.citeitemid=r.id,n.citeuid=t.initData.refno,n.customer=r.customer,n.custpo=r.custpo,n.machgroupid=r.machgroupid,n.machitemid=r.machitemid,n.machuid=r.machuid,n.mainplanitemid=r.mainplanitemid,n.mainplanuid=r.mainplanuid,n.mrpitemid=r.mrpitemid,n.mrpuid=r.mrpuid,n.quantity=r.quantity,n.workitemmatid=r.workitemmatid,n.workitemid=r.workitemid;d=t.initData.item.findIndex((function(t){return t.id==r.itemid}));-1!=d&&(n.machuid=t.initData.item[d].machuid,n.machitemid=t.initData.item[d].machitemid,n.machgroupid=t.initData.item[d].machgroupid,n.mainplanitemid=t.initData.item[d].mainplanitemid,n.mainplanuid=t.initData.item[d].mainplanuid,n.mrpitemid=t.initData.item[d].mrpitemid,n.mrpuid=t.initData.item[d].mrpuid,n.workuid=t.initData.item[d].workuid),n.quantity&&t.formdata.item.push(n)}}}var v=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[]}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"领料单",value:"领料单"}}),i("el-option",{attrs:{label:"退料单",value:"退料单"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[i("el-form-item",{attrs:{label:"单据主题"}},[i("el-input",{attrs:{placeholder:"请输入单据主题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupid")}}},[i("el-form-item",{attrs:{label:"车间",prop:"groupid"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B3/getPageList",type:"生产车间"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupid")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)])],1)],1)},w=[],x={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupid:[{required:!0,trigger:"blur",message:"部门为必填项"}],storeid:[{required:!0,trigger:"blur",message:"仓库名称为必填项"}],direction:[{required:!0,trigger:"blur",message:"操作方向为必填项"}]},selVisible:!1}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},k=x,_=(i("8d15"),i("2877")),D=Object(_["a"])(k,v,w,!1,null,"72dd3a33",null),S=D.exports,O=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","copyrow","refresh","billstate"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selgoodsvisible?i("el-dialog",{attrs:{title:"货品信息","append-to-body":!0,visible:t.selgoodsvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selgoodsvisible=e}}},[i("SelGoods",{ref:"selGoods",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selGoods()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selgoodsvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.machuidVisible?i("el-dialog",{attrs:{title:"销售订单","append-to-body":!0,visible:t.machuidVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.machuidVisible=e}}},[i("SaleOrder",{ref:"SaleOrder",attrs:{multi:0}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selMachid()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.machuidVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.inventoryVisible?i("el-dialog",{attrs:{title:"BOM清单","append-to-body":!0,visible:t.inventoryVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.inventoryVisible=e}}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")]),i("el-table",{staticStyle:{width:"100%","max-height":"450px",overflow:"auto"},attrs:{data:t.inventoryData,border:"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"highlight-current-row":""},on:{"selection-change":t.handleBom,"row-click":t.saveinventory}},[i("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),i("el-table-column",{attrs:{prop:"goodsType",label:"类别","min-width":"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return["主料"==e.row.goodsType?i("span",{staticStyle:{color:"#67c23a"}},[t._v(t._s(e.row.goodsType))]):i("span",{staticStyle:{color:"#409eff"}},[t._v(t._s(e.row.goodsType))])]}}],null,!1,3971086252)}),i("el-table-column",{attrs:{prop:"goodsuid",label:"货品编码","min-width":"100",align:"center"}}),i("el-table-column",{attrs:{prop:"goodsname",label:"货品名称","min-width":"100",align:"center","show-overflow-tooltip":""}}),i("el-table-column",{attrs:{prop:"goodsspec",label:"货品规格","min-width":"100",align:"center","show-overflow-tooltip":""}}),i("el-table-column",{attrs:{prop:"partid",label:"外部编码","min-width":"100",align:"center","show-overflow-tooltip":""}}),i("el-table-column",{attrs:{prop:"quantity",label:"领用数量","min-width":"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-input-number",{directives:[{name:"show",rawName:"v-show",value:e.row.isEdit,expression:"scope.row.isEdit"}],staticStyle:{width:"100%"},attrs:{"controls-position":"right",step:1,min:0,size:"small",controls:!1},model:{value:e.row.quantity,callback:function(i){t.$set(e.row,"quantity",i)},expression:"scope.row.quantity"}}),i("span",{directives:[{name:"show",rawName:"v-show",value:!e.row.isEdit,expression:"!scope.row.isEdit"}]},[t._v(t._s(e.row.quantity))])]}}],null,!1,2527021124)})],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selinventory()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.inventoryVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},$=[],B=(i("caad"),i("a434"),i("e9c4"),i("a9e3"),i("d3b7"),i("2532"),i("c7cd"),i("159b"),i("c03f")),q=i("2499"),P=i("9bda"),C={name:"Elitem",components:{SelGoods:P["a"],SaleOrder:q["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],multi:0,keynum:0,selgoodsvisible:!1,setColumsVisible:!1,machuidVisible:!1,machuidIndex:0,replaceList:[],replaceVisible:!1,replaceIndex:0,radio:"",CurrentRow:{},total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:0},inventoryVisible:!1,getallList:[],inventoryData:[],selrowsList:[],strfilter:"",tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,index:0,tableForm:B["b"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(){var e=Object(h["a"])(Object(p["a"])().mark((function e(i){var a,o,s;return Object(p["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=i.row,o=i.column,t.editmarkfiles.includes(o.field)&&(t.countfiles.includes(o.field)&&t.changeInput("",a,o.field),s=t.customList.findIndex((function(t){return t.attrkey==o.field})),-1!=s&&t.setAttributeJson(a,a.rownum),t.$forceUpdate());case 2:case"end":return e.stop()}}),e)})));function i(t){return e.apply(this,arguments)}return i}()},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(){var e=Object(h["a"])(Object(p["a"])().mark((function e(i){var a,o,s,r,n;return Object(p["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(i.sourceSelectionData,a=i.targetSelectionData,o=0;o<a.length;o++)s=a[o],r=t.lst.findIndex((function(t){return t.rowKeys==s.rowKeys})),-1!=r&&(t.countfiles.includes(Object.keys(s)[1])&&t.changeInput("",t.lst[r],Object.keys(s)[1]),n=t.customList.findIndex((function(t){return t.attrkey==Object.keys(s)[1]})),-1!=n&&t.setAttributeJson(t.lst[r],r),t.$forceUpdate());case 2:case"end":return e.stop()}}),e)})));function i(t){return e.apply(this,arguments)}return i}()},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),t[i].parentgoodsid?t[i].goodsType="替代料":t[i].goodsType="主料",0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var s=JSON.parse(t[i].attributejson);for(a=0;a<s.length;a++)t[i][s[a].key]=s[a].value}}this.$forceUpdate(),this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(h["a"])(Object(p["a"])().mark((function e(){var i;return Object(p["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=B["b"],t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn(B["b"].formcode,i,1,0,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,operationColumn:!!t.operationmark,renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex);if("billdate"==t.itemcode||"plandate"==t.itemcode){var r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[e.$options.filters.dateFormat(o[t.itemcode])])]),a("div",[a("div",{style:"opacity: 0;width:0;height:0"},[a("el-date-picker",{style:"width:0;height:0",attrs:{size:"small",id:t.itemcode+s+B["b"].formcode,value:new Date(o[t.itemcode]),type:"date",format:"MM-dd","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},on:{input:function(e){o[t.itemcode]=e}}})]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-date",on:{click:function(){document.getElementById(t.itemcode+s+B["b"].formcode).focus()}}})])]);return r}if("goodsuid"==t.itemcode){r=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeVal:o[t.itemcode]}});return r}if("goodsType"==t.itemcode){r="";return r="主料"==o.goodsType?a("span",{style:"color: #67c23a"},[" ",o.goodsType]):a("span",{style:"color: #409eff"},[o.goodsType]),r}if("machuid"==t.itemcode){r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("span",{class:o.disannulmark?"textlinethrough":"",style:"flex:1"},[o[t.itemcode]]),a("i",{class:"writePacksn el-icon-circle-plus-outline",on:{click:function(){return e.openMachuid(o,o.rownum)}}})]);return r}r=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]);return r}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1,this.$forceUpdate()},getSummary:function(){this.$getSummary(this,["goodsuid","quantity"])},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.changeInput("",this.lst[e+o],n)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),this.getSummary()},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.$delete(e,"finishqty"),this.$delete(e,"statecode"),this.$delete(e,"statedate"),this.lst.push(e)}},getAdd:function(t){this.selgoodsvisible=!0},openMachuid:function(t,e){this.machuidVisible=!0,this.machuidIndex=e},selMachid:function(){var t=this.$refs.SaleOrder.selrows;this.lst[this.machuidIndex].machuid=t.refno,this.lst[this.machuidIndex].machgroupid=t.groupid,this.lst[this.machuidIndex].machitemid=t.id,this.machuidVisible=!1},selGoods:function(){var t=this.$refs.selGoods.$refs.selectgoods.selection;if(0!=t.length){this.selgoodsvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},c);a.goodsid=i.id,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.itemcode=i.goodsuid,a.itemname=i.goodsname,a.itemspec=i.goodsspec,a.itemunit=i.goodsunit,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selPwProcess:function(){var t=this;return Object(h["a"])(Object(p["a"])().mark((function e(){var i;return Object(p["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=t.$refs.selPwProcess.selrows,i){e.next=4;break}return t.$message.warning("选择内容不能为空"),e.abrupt("return");case 4:return t.formdata.billtitle="【"+i.refno+"】加工单转入",e.next=7,s["a"].get("/D05M01B1/getMatList?key="+i.pid).then((function(e){if(200==e.data.code){if(t.inventoryVisible=!0,!e.data.data)return void t.$message.warning("该货品暂无物料");t.inventoryData=[],e.data.data.forEach((function(e,a){var o={goodsid:e.goodsid,goodsuid:e.goodsuid,goodsname:e.goodsname,goodsunit:e.goodsunit,goodsspec:e.goodsspec,flowcode:e.flowcode,partid:e.partid,quantity:e.quantity,plandate:i.plandate,finishqty:0,citeuid:i.refno,citeitemid:i.id,machuid:i.machuid,machitemid:i.machitemid,machgroupid:i.machgroupid,mainplanitemid:i.mainplanitemid,mainplanuid:i.mainplanuid,workitemgoodid:i.goodsid,workuid:i.refno,workitemid:i.id,mrpuid:i.mrpuid,mrpitemid:i.mrpitemid,wkbilltype:1,workitemmatid:e.id};0!=t.idx&&(o.pid=t.idx),e.parentgoodsid?o.goodsType="替代料":o.goodsType="主料",t.getallList.push(o),t.inventoryData.push(o)}))}else t.$message.warning(e.data.code+"，物料查询失败")}));case 7:case"end":return e.stop()}}),e)})))()},search:function(t){if(""!=t){this.inventoryData=[];for(var e=0;e<this.getallList.length;e++){var i=this.getallList[e];t==i.flowcode&&this.inventoryData.push(i)}}else this.inventoryData=this.getallList},selReplace:function(){var t=this.lst[this.replaceIndex];if(Number(t.quantity)<Number(this.CurrentRow.quantity))this.$message.warning("替代料不能大于主料");else{t.quantity=Number(t.quantity)-Number(this.CurrentRow.quantity);var e={goodsid:this.CurrentRow.goodsid,goodsuid:this.CurrentRow.goodsuid,goodsname:this.CurrentRow.goodsname,goodsunit:this.CurrentRow.goodsunit,goodsspec:this.CurrentRow.goodsspec,partid:this.CurrentRow.partid,quantity:this.CurrentRow.quantity,goodsType:"替代料",plandate:t.plandate,finishqty:0,citeuid:t.citeuid,citeitemid:t.citeitemid,workitemgoodid:t.workitemgoodid,workuid:t.workuid,workitemid:t.workitemid};0!=this.idx&&(e.pid=this.idx),this.lst.splice(this.replaceIndex+1,0,e),this.replaceVisible=!1}},getCurrentRow:function(t){this.CurrentRow=t},handleBom:function(t){this.selrowsList=t},saveinventory:function(t){for(var e=0;e<this.inventoryData.length;e++)this.inventoryData[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0))},BomRowClassName:function(t){var e=t.row,i=t.rowIndex;e.index=i},savereplace:function(t){console.log(t,"savereplace"),this.radio=t.index;for(var e=0;e<this.replaceList.length;e++)this.replaceList[e].isEdit=!1;t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0)},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],s={key:o.attrkey,value:t[o.attrkey]?String(t[o.attrkey]).replace(/\s*/g,""):""};""!=s.value&&i.push(s)}if(0==i.length)this.lst[e].attributejson="";else{this.lst[e].attributejson=JSON.stringify(i);for(a=0;a<i.length;a++)this.$set(this.lst[e],i[a].key,i[a].value)}this.$forceUpdate()}}},M=C,F=(i("8c16"),Object(_["a"])(M,O,$,!1,null,"b75ac3d0",null)),I=F.exports,j=i("bb0f"),E=i("dcb4"),R=i("13df"),T=i("27f6"),L={name:"Formedit",components:{FormTemp:E["a"],EditHeader:S,EditItem:I,D04M01B3:R["default"],D04M01B3List:T["a"]},props:["idx","isDialog","initData","billcode","billType","selectList"],data:function(){return{title:"领料单据",operateBar:u,processBar:f,formdata:{billdate:new Date,billtitle:"",billtype:"领料单",branthname:"",disannulmark:0,groupid:"",groupname:"",groupuid:"",projectid:"",projcode:"",projname:"",item:[],modulecode:"",operator:"",refno:"",summary:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:j["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode,this.billType)},methods:{bindTemp:function(){var t=this;s["a"].get("/SaFormCustom/getEntityByCode?key=D04M08B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):j["a"],t.formtemplate.footer.type||(t.formtemplate.footer=j["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&s["a"].get("/D04M08B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id},autoClear:function(){this.formdata.groupname="",this.formdata.groupid=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i={item:[]};i=this.$getParam(m,i,this.formdata),0==this.idx?n.add(i).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):n.update(i).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){console.log("isDialog",this.isDialog),this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(e)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},changeBillType:function(){formdata.item=[]},printMultiServer:function(){this.$refs.PrintMultiServer.printButton(0,1)},billSwitch:function(t,e){y(this,t,e,c)}}},N=L,V=(i("bfc3"),Object(_["a"])(N,a,o,!1,null,"b00f9962",null));e["default"]=V.exports},"848e":function(t,e,i){},8626:function(t,e,i){},8679:function(t,e,i){},8955:function(t,e,i){"use strict";i("8679")},"8b95":function(t,e,i){},"8c16":function(t,e,i){"use strict";i("9c55")},"8d15":function(t,e,i){"use strict";i("c403")},"8da8":function(t,e,i){"use strict";i("3e09")},"90fe":function(t,e,i){},9368:function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r}));var a={formcode:"D03M02B1Th",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"130",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Order.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Buy_Order.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Order.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billtaxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,sortable:1,datasheet:"Buy_Order.billtaxamount"},{itemcode:"billamount",itemname:"未税金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,atasheet:"Buy_Order.billamount"},{itemcode:"billtaxtotal",itemname:"税额",minwidth:"80",sortable:1,displaymark:1,overflow:1,atasheet:"Buy_Order.billtaxtotal"},{itemcode:"orderno",itemname:"订单编号",minwidth:"100",displaymark:1,sortable:1,overflow:1,atasheet:"Buy_Order.orderno"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,atasheet:"Buy_Order.operator"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,atasheet:"Buy_Order.status"},{itemcode:"itemcount",itemname:"款数",minwidth:"60",sortable:1,displaymark:1,overflow:1,atasheet:"Buy_Order.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,atasheet:"Buy_Order.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,atasheet:"Buy_Order.assessor"}]},o={formcode:"D03M02B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"130",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",atasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,atasheet:"Buy_Order.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Buy_Order.billtitle"},{itemcode:"billdate",itemname:"单据日期",sortable:1,minwidth:"100",displaymark:1,overflow:1,atasheet:"Buy_Order.billdate"},{itemcode:"custpo",itemname:"客户订单号",minwidth:"100",displaymark:0,overflow:1},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"plandate",itemname:"计划完成",minwidth:"100",sortable:1,displaymark:1,overflow:1,atasheet:"Buy_OrderItem.billdate"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"规格",minwidth:"120",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.quantity"},{itemcode:"finishqty",itemname:"已收",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.status"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:0,overflow:1,datasheet:"Buy_Order.assessor"}]},s={formcode:"D03M02B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:1,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,fixed:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,fixed:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"60",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"groupuid",itemname:"客户编码",minwidth:"80",displaymark:0,overflow:1},{itemcode:"abbreviate",itemname:"客户简称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"groupname",itemname:"客户",minwidth:"120",displaymark:0,overflow:1},{itemcode:"machuid",itemname:"销售订单",minwidth:"100",displaymark:1,overflow:1},{itemcode:"custpo",itemname:"客户订单号",minwidth:"100",displaymark:0,overflow:1},{itemcode:"plandate",itemname:"计划完成",minwidth:"150",displaymark:1,overflow:1},{itemcode:"maxqty",itemname:"最大允收",minwidth:"90",displaymark:1,overflow:1,editmark:1},{itemcode:"finishqty",itemname:"已收货",minwidth:"100",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:0,overflow:1}]},r={formcode:"D03M02B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"130",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",atasheet:"Buy_Order.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,atasheet:"Buy_Order.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"120",displaymark:1,overflow:1,datasheet:"Buy_Order.billtitle"},{itemcode:"billdate",itemname:"单据日期",sortable:1,minwidth:"100",displaymark:1,overflow:1,atasheet:"Buy_Order.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"plandate",itemname:"计划完成",minwidth:"100",sortable:1,displaymark:1,overflow:1,atasheet:"Buy_OrderItem.billdate"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"规格",minwidth:"120",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.quantity"},{itemcode:"finishqty",itemname:"已收",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_OrderItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_OrderItem.status"}]}},9416:function(t,e,i){"use strict";var a=i("b775");const o={add(t){return new Promise((e,i)=>{var o=JSON.stringify(t);a["a"].post("/SaBillCode/create",o).then(t=>{console.log(t),200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var o=JSON.stringify(t);a["a"].post("/SaBillCode/update",o).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){return new Promise((e,i)=>{a["a"].get("/SaBillCode/delete?key="+t).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})}};e["a"]=o},"941f":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{writedate:!1,formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getSuppGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,changeBillType:t.changeBillType}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px",height:"100%"}},[i("EditCash",{ref:"cashitem",style:{width:"99%",height:"其他预付"!=t.formdata.billtype?"50%":"100%"},attrs:{lstitem:t.formdata.cash,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate,itemAmount:t.itemAmount},on:{bindData:t.bindData,computerCashAmount:t.computerCashAmount}}),"其他预付"!=t.formdata.billtype?i("EditItem",{ref:"elitem",staticStyle:{width:"99%",height:"48%","margin-top":"10px"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData,computerItemAmount:t.computerItemAmount}}):t._e()],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M06B1PREEdit",commonurl:"/D03M06B1PRE/printBill",weburl:"/D03M06B1PRE/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M06B1PREEdit",examineurl:"/D03M06B1PRE/sendapprovel"}})],1)},o=[],s=i("2909"),r=(i("a9e3"),i("b64b"),i("d3b7"),i("ac1f"),i("6062"),i("3ca3"),i("5319"),i("ddb0"),i("b775"));const n={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D03M06B1PRE/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D03M06B1PRE/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){r["a"].get("/D03M06B1PRE/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,r["a"].get("/D03M06B1PRE/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M06B1PRE/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M06B1PRE/closed?type="+(3==t?1:0);r["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var l=n,d=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){return t.$emit("changeBillType")}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购预付",value:"采购预付"}}),i("el-option",{attrs:{label:"其他预付",value:"其他预付"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},["外协预付"==t.formdata.billtype||"委外预付"==t.formdata.billtype?i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"外协厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B4/getPageList",type:"外协厂商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1):i("div",[i("el-form-item",{attrs:{label:"供应厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"预付金额"}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.billamount))])])],1)],1)],1)},m=[],c={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupname:[{required:!0,trigger:"blur",message:"往来单位为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},u=c,f=(i("d8b2"),i("2877")),p=Object(f["a"])(u,d,m,!1,null,"8c70604e",null),h=p.exports,g=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh","column"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn}}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},editOption:t.editOption,"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.deliveryvisible?i("el-dialog",{attrs:{title:"采购订单","append-to-body":!0,visible:t.deliveryvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.deliveryvisible=e}}},[i("SelDelivery",{ref:"selDelivery",attrs:{multi:1,selecturl:"/D03M02B1/getOnlinePageTh?groupid="+t.formdata.groupid+"&appl=1"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selDelivery()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.deliveryvisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},b=[],y=(i("c740"),i("caad"),i("e9c4"),i("2532"),i("c7cd"),i("159b"),{amount:0,cashaccid:"",cashaccname:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",pid:"",remark:"",rownum:0}),v={amount:0,cashaccid:"",cashaccname:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",pid:"",remark:"",rownum:0},w=i("4cf0"),x=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto","min-height":"352 px",width:"100%"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"单据日期",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormats")(e.row.billdate)))])]}}])}),i("el-table-column",{attrs:{label:"供应商编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupuid))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"含税金额",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtaxamount))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},k=[],_=i("333d"),D={components:{Pagination:_["a"]},props:["multi","groupid","selecturl"],data:function(){return{title:"采购订单",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M02B1/getPageTh";r["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtype:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},S=D,O=(i("5b00"),Object(f["a"])(S,x,k,!1,null,"892e62f4",null)),$=O.exports,B={name:"Elitem",components:{SelDelivery:$},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{listLoading:!1,deliveryvisible:!1,lst:[],keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:w["c"],customList:[],editmarkfiles:[],countfiles:["billtaxamount","amount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=w["c"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(w["c"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=[];this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["index","amount"]);var t=this.footerData[0].amount;this.$emit("computerItemAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.formdata.groupid?this.deliveryvisible=!0:this.$message.warning("请选择往来单位")},selWaXie:function(){var t=this.$refs.selWaXie.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},y);a.invoamount=0,a.invobillcode=i.refno,a.invocode="",a.invoid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selDelivery:function(){var t=this.$refs.selDelivery.$refs.selectVal.selection;if(0!=t.length){this.deliveryvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},y);a.amount=i.billtaxamount,a.billtaxamount=i.billtaxamount,a.orderbillcode=i.refno,a.orderbillid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},q=B,P=(i("015e"),Object(f["a"])(q,g,b,!1,null,"6a248a27",null)),C=P.exports,M=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh","column"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn}}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"出纳账户","append-to-body":!0,visible:t.selordervisible,width:"1000px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.SelOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},F=[],I=i("233f"),j={name:"Elitem",components:{SelOrder:I["a"]},props:["formdata","lstitem","idx","itemAmount","formstate","formtemplateItem"],data:function(){var t=this;return{listLoading:!1,selordervisible:!1,lst:[],keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:w["a"],customList:[],editmarkfiles:[],countfiles:["amount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=w["a"];this.$getColumn(w["a"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=[];this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["cashaccname","amount"]);var t=this.footerData[0].amount;this.$emit("computerCashAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}this.multipleSelection=[],this.checkboxOption.selectedRowKeys=[]},getAdd:function(t){this.selordervisible=!0},SelOrder:function(){var t=this.$refs.SelOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},v);a.amount=this.itemAmount,a.cashaccid=i.id,a.cashaccname=i.accountname,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},E=j,R=(i("3fbc"),Object(f["a"])(E,M,F,!1,null,"da1038a0",null)),T=R.exports,L={header:{type:0,title:"付款单",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购预付",value:"采购预付"},{label:"委外预付",value:"委外预付"},{label:"外协预付",value:"外协预付"},{label:"其他预付",value:"其他预付"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"供应厂商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"billamount",label:"预付金额",type:"text",methods:"",param:""}]}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},list:{type:0,content:[]},item:{type:0,content:[]}},N=i("dcb4"),V=["id","refno","billtype","billtitle","billdate","groupid","groupid","groupuid","groupname","abbreviate","grouplevel","billamount","operator","citecode","outamount","returnuid","orguid","summary","fmdocmark","fmdoccode","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],H=["id","pid","orderbillid","orderbillcode","finishbillid","finishbillcode","billtaxamount","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],K=["id","pid","cashaccid","cashaccname","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],z={params:V,paramsItem:H,paramsCash:K},A=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]}],G=[],J={name:"Formedit",components:{FormTemp:N["a"],EditHeader:h,EditItem:C,EditCash:T},props:["idx","isDialog","initData","billcode","isprocessDialog"],data:function(){return{title:"预付款",operateBar:A,processBar:G,formdata:{abbreviate:"",billamount:0,assessdate:new Date,assessor:"",billdate:new Date,billtitle:"",billtype:"采购预付",citecode:"",createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,createdate:"",groupid:"",groupname:"",groupuid:"",item:[],cash:[],lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,operator:"",orguid:"",outamount:0,refno:"",returnuid:"",revision:0,summary:""},itemAmount:0,cashAmount:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:L,formstate:0,submitting:0}},computed:{formcontainHeight:function(){return window.innerHeight-50-13-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp(),this.bindData()},mounted:function(){this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;r["a"].get("/SaFormCustom/getEntityByCode?key=D03M06B1PRE").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):L,t.formtemplate.footer.type||(t.formtemplate.footer=L.footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&r["a"].get("/D03M06B1PRE/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1,"其他收款"==t.formdata.billtype&&t.$refs.cashitem.catchHight()):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},computerItemAmount:function(t){this.itemAmount=t},computerCashAmount:function(t){this.cashAmount=t,this.formdata.billamount=Number(this.cashAmount)},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if("其他预付"!=this.formdata.billtype){if(this.formdata.billamount!=this.itemAmount||this.formdata.billamount!=this.cashAmount)return void this.$message.warning("预付金额 与 出纳金额和单据金额不一致");this.formdata.item=this.$refs.elitem.lst,this.formdata.citecode="";for(var e="",i=0;i<this.formdata.item.length;i++){var a=this.formdata.item[i];e+=a.orderbillcode+","}var o=/,$/gi;e=e.replace(o,"");var r=e.split(","),n=Object(s["a"])(new Set(r));for(i=0;i<n.length;i++)this.formdata.citecode+=n[i]+",";this.formdata.citecode=this.formdata.citecode.replace(o,"")}this.submitting=1,this.formdata.cash=this.$refs.cashitem.lst;var d={item:[],cash:[]};d=this.$getParam(z,d,this.formdata),0==this.idx?l.add(d).then((function(e){t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0})):l.update(d).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0}))},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.delete(e)})).catch((function(){}))},approval:function(){l.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?l.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t,console.log(this.processModel,"ss")},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.item=[]},autoClear:function(t){this.formdata.groupname="",this.formdata.groupid=""},changeBillType:function(){this.formdata.item=[],this.formdata.taxamount=0,"其他预付"==this.formdata.billtype&&this.$refs.cashitem.catchHight()},billSwitch:function(t){if(console.log(this.initData),"D03M02B1"==t){this.formdata.billtype="采购预付",this.formdata.billtitle="【"+this.initData.refno+"】采购订单转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billamount=this.initData.billamount,this.formdata.citecode=this.initData.refno,this.formdata.item=[];var e={billtaxamount:this.initData.billtaxamount,amount:this.initData.billtaxamount,finishbillid:"",finishbillcode:"",orderbillcode:this.initData.refno,orderbillid:this.initData.id,remark:"",rownum:0};this.formdata.item.push(e)}}}},W=J,U=(i("af2e"),Object(f["a"])(W,a,o,!1,null,"44f6e4d4",null));e["default"]=U.exports},"9c55":function(t,e,i){},"9fee":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){t.commonurl="/D03M02B1/printBill",t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%","overflow-y":"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getSuppGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{isDialog:t.isDialog,title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M02B1Edit",commonurl:t.commonurl,weburl:"/D03M02B1/printWebBill"}}),i("PrintServer",{ref:"PrintMultiServer",attrs:{formdata:t.formdata,printcode:"D03M02B1EditMulti",commonurl:"/D03M02B1/printWebBillMulti",weburl:"/D03M02B1/printWebBillMulti"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M02B1Edit",examineurl:"/D03M02B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},["D03M03B1"==t.processModel?i("D03M03B1",{ref:"D03M03B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M02B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e(),"D03M06B1PRE"==t.processModel?i("D03M06B1PRE",{ref:"D03M06B1PRE",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M02B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e()],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D03M06B1PRE"==t.processModel?i("D03M06B1PREList",{ref:"D03M06B1PREList",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e(),"D03M03B1"==t.processModel?i("D03M03B1List",{ref:"D03M03B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=(i("c740"),i("b64b"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M02B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M02B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D03M02B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D03M02B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M02B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M02B1/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=r,l=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","grouplevel","abbreviate","payment","orderno","arrivaladd","transport","linkman","linktel","taxrate","operator","summary","billtaxamount","billtaxtotal","billamount","billstatecode","billstatedate","billplandate","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],d=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","partid","itemcode","itemname","itemspec","itemunit","quantity","taxprice","taxamount","itemtaxrate","taxtotal","price","amount","abbreviate","groupname","groupuid","plandate","maxqty","remark","citeuid","citeitemid","statecode","statedate","closed","rownum","orggoodsid","subrate","machuid","machitemid","machgroupid","mainplanuid","mainplanitemid","mrpuid","mrpitemid","virtualitem","customer","custpo","batchno","disannulmark","passedqty","attributejson","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10","custom11","custom12","custom13","custom14","custom15","custom16","custom17","custom18"],m={params:l,paramsItem:d},c={abbreviate:"",amount:0,attributejson:"",batchno:"",citeitemid:"",citeuid:"",closed:0,custom1:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",customer:"",custpo:"",disannuldate:new Date,disannullister:"",disannullisterid:"",disannulmark:0,finishqty:0,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",groupname:"",groupuid:"",id:"",intqtymark:0,itemcode:"",itemname:"",itemspec:"",itemtaxrate:0,itemunit:"",machgroupid:"",machitemid:"",machuid:"",mainplanitemid:"",mainplanuid:"",maxqty:0,mrpitemid:"",mrpuid:"",orggoodsid:"",partid:"",pid:"",plandate:new Date,price:0,quantity:0,remark:"",rownum:0,sourcetype:0,statecode:"",statedate:new Date,subrate:0,taxamount:0,taxprice:0,taxtotal:0,virtualitem:0},u=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"合并打印",icon:"",disabled:"this.formstate==0",methods:"printButton",param:"merge",children:[]},{show:1,divided:!0,label:"一页两联",icon:"el-icon-printer",disabled:"this.formstate==0",methods:"printMultiServer",children:[]},{show:1,divided:!1,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,divided:!0,label:"采购验收",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D03M03B1",children:[]},{show:1,divided:!1,label:"采购预付",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D03M06B1PRE",children:[]}],f=[{show:1,divided:!1,label:"采购验收",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D03M03B1",children:[]},{show:1,divided:!1,label:"采购预付",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D03M06B1PRE",children:[]}],p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.isDialog||!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[]}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购计划",value:"采购计划"}}),i("el-option",{attrs:{label:"销售订单",value:"销售订单"}}),i("el-option",{attrs:{label:"其他采购",value:"其他采购"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据主题"}},[i("el-input",{attrs:{placeholder:"请输入单据主题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("orderno")}}},[i("el-form-item",{attrs:{label:"订单编号"}},[i("el-input",{attrs:{placeholder:"请输入订单编号",clearable:"",size:"small"},model:{value:t.formdata.orderno,callback:function(e){t.$set(t.formdata,"orderno",e)},expression:"formdata.orderno"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"供应商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("arrivaladd")}}},[i("el-form-item",{attrs:{label:"收货地址"}},[i("el-input",{attrs:{placeholder:"请输入收货地址",clearable:"",size:"small"},model:{value:t.formdata.arrivaladd,callback:function(e){t.$set(t.formdata,"arrivaladd",e)},expression:"formdata.arrivaladd"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("transport")}}},[i("el-form-item",{attrs:{label:"物流方式"}},[i("el-input",{attrs:{placeholder:"请输入物流方式",clearable:"",size:"small"},model:{value:t.formdata.transport,callback:function(e){t.$set(t.formdata,"transport",e)},expression:"formdata.transport"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据计划"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.billplandate,callback:function(e){t.$set(t.formdata,"billplandate",e)},expression:"formdata.billplandate"}})],1)],1),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("payment")}}},[i("el-form-item",{attrs:{label:"付款方式"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入付款方式",clearable:"",size:"small"},model:{value:t.formdata.payment,callback:function(e){t.$set(t.formdata,"payment",e)},expression:"formdata.payment"}})],1)],1)])],1)],1)},h=[],g={props:{formdata:{type:Object},title:{type:String},isDialog:{type:Boolean}},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"供应商为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},b=g,y=(i("7922"),i("2877")),v=Object(y["a"])(b,p,h,!1,null,"c4b439ae",null),w=v.exports,x=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,showcontent:["add","moveup","movedown","delete","copyrow","refresh","billstate"],lst:t.lst,multipleSelection:t.multipleSelection,dummyurl:"/D91M01B1/getVirOnlinePageList"},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,selDummy:t.selDummy,getColumn:t.getColumn},scopedSlots:t._u([{key:"center",fn:function(){return[i("el-dropdown",{attrs:{trigger:"click",placement:"bottom","hide-on-click":!1}},[i("el-button",{attrs:{size:"mini",type:"primary",disabled:!!t.formdata.assessor}},[t._v("批量操作"),i("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{disabled:0==t.multipleSelection.length},nativeOn:{click:function(e){t.deliverydatevisible=!0,t.dateType="plandate"}}},[t._v("计划完成")])],1)],1)]},proxy:!0},{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}}),i("div",{staticStyle:{display:"inline-block"}},[i("QuickTick",{directives:[{name:"show",rawName:"v-show",value:0!=t.lst.length,expression:"lst.length != 0"}],attrs:{droupList:t.droupList},on:{quickTickBtn:t.quickTickBtn,quickTickSearch:t.quickTickSearch,changeDropdown:t.changeDropdown}})],1)]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selgoodsvisible?i("el-dialog",{attrs:{title:"货品信息","append-to-body":!0,visible:t.selgoodsvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selgoodsvisible=e}}},[i("SelGoods",{ref:"selGoods",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selGoods()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selgoodsvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.buyPlanFormVisible?i("el-dialog",{attrs:{title:"请购单","append-to-body":!0,visible:t.buyPlanFormVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.buyPlanFormVisible=e}}},[i("BuyPlan",{ref:"buyPlan",attrs:{multi:1,selecturl:t.selecturl,groupid:t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.buyPlanBtn()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.buyPlanFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.saleOrderFormVisible?i("el-dialog",{attrs:{title:"订单信息","append-to-body":!0,visible:t.saleOrderFormVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.saleOrderFormVisible=e}}},[i("SaleOrder",{ref:"saleOrder",attrs:{multi:1,selecturl:t.selecturl}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.saleOrderBtn()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.saleOrderFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),i("el-dialog",{attrs:{title:"计划完成","append-to-body":!0,width:"400px",visible:t.deliverydatevisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.deliverydatevisible=e}}},[i("el-form",{ref:"customTimeForm"},[i("el-form-item",{attrs:{label:""}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期"},model:{value:t.deliverydate,callback:function(e){t.deliverydate=e},expression:"deliverydate"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitTime}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.deliverydatevisible=!1}}},[t._v("取 消")])],1)],1),i("el-dialog",{staticStyle:{"min-height":"500px"},attrs:{visible:t.isShowHistory,width:"72vw","append-to-body":!0},on:{"update:visible":function(e){t.isShowHistory=e}}},[i("HistoryOrder",{ref:"historyOrder",attrs:{historyData:t.historyData},on:{getHistoryPrice:t.getHistoryPrice}})],1),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},k=[],_=i("b85c"),D=i("c7eb"),S=i("1da1"),O=(i("caad"),i("d81d"),i("a434"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),i("9368")),$=i("9bda"),B=i("174a"),q=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"352px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"领用部门",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodscode))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsunit))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},P=[],C=(i("99af"),i("25f0"),i("4d90"),i("333d")),M={components:{Pagination:C["a"]},filters:{dateFormat:function(t){var e=new Date(t),i=e.getFullYear(),a=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0");return"".concat(i,"/").concat(a,"/").concat(o)}},props:["multi","groupid","selecturl"],data:function(){return{title:"请购单",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M01B1/getOnlinePageList?groupid="+this.groupid;s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={goodsname:t,goodsuid:t,goodsspec:t,groupuid:t,groupname:t,refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},F=M,I=(i("7085"),Object(y["a"])(F,q,P,!1,null,"ee397bf0",null)),j=I.exports,E=i("2499"),R=i("e82a"),T=i("6465"),L=i("da92"),N={name:"Elitem",components:{SelGoods:$["a"],BuyPlan:j,SaleOrder:E["a"],QuickTick:R["a"],HistoryOrder:T["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{isShowHistory:!1,setColumsVisible:!1,historyData:[],currentRowKey:"",lst:[],multi:0,keynum:0,selgoodsvisible:!1,buyPlanFormVisible:!1,saleOrderFormVisible:!1,selecturl:"",saleorderindex:-1,deliverydate:"",deliverydatevisible:!1,dateType:"",tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:O["b"],customList:[],editmarkfiles:[],droupList:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate","maxqty"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{afterCellValueChange:function(){var e=Object(S["a"])(Object(D["a"])().mark((function e(i){var a,o,s,r,n;return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=i.row,o=i.column,i.changeValue,!t.editmarkfiles.includes(o.field)){e.next=14;break}return e.t0=Object,e.t1={},e.next=6,Object(B["a"])("D03M02B1",a,o.field);case 6:for(r in e.t2=e.sent,s=e.t0.assign.call(e.t0,e.t1,e.t2),a)a[r]=s[r];t.setAttributeJson(a,a.rownum),t.countfiles.includes(o.field)?t.changeInput("",a,o.field):Object(B["b"])()&&t.changeInput("",a,Object(B["b"])()),n=t.customList.findIndex((function(t){return t.attrkey==o.field})),-1!=n&&t.setAttributeJson(a,a.rownum),t.$forceUpdate();case 14:case"end":return e.stop()}}),e)})));function i(t){return e.apply(this,arguments)}return i}()},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(){var e=Object(S["a"])(Object(D["a"])().mark((function e(i){var a,o,s,r,n,l,d;return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i.sourceSelectionData,a=i.targetSelectionData,o=0;case 2:if(!(o<a.length)){e.next=18;break}if(s=a[o],r=t.lst.findIndex((function(t){return t.rowKeys==s.rowKeys})),-1==r){e.next=15;break}return e.next=8,Object(B["a"])("D03M02B1",t.lst[r],Object.keys(s)[1]);case 8:for(l in n=e.sent,t.lst[r])t.lst[r][l]=n[l];t.setAttributeJson(t.lst[r],r),t.countfiles.includes(Object.keys(s)[1])?t.changeInput("",t.lst[r],Object.keys(s)[1]):Object(B["b"])()&&t.changeInput("",t.lst[r],Object(B["b"])()),d=t.customList.findIndex((function(t){return t.attrkey==Object.keys(s)[1]})),-1!=d&&t.setAttributeJson(t.lst[r],r),t.$forceUpdate();case 15:o++,e.next=2;break;case 18:case"end":return e.stop()}}),e)})));function i(t){return e.apply(this,arguments)}return i}()},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},beforePaste:function(){if(2==t.formstate)return!1},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var s=JSON.parse(t[i].attributejson);for(a=0;a<s.length;a++)t[i][s[a].key]=s[a].value}}this.getdroupList(),this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(S["a"])(Object(D["a"])().mark((function e(){var i;return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=O["b"],t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn(O["b"].formcode,i,1).then((function(e){if(t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),0!=t.customList.length)for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=0;o<t.customList.length;o++){var s=t.customList[o];a[s.attrkey]||(a[s.attrkey]="")}t.editmarkfiles=[];for(i=0;i<t.tableForm.item.length;i++){s=t.tableForm.item[i];s.editmark&&t.editmarkfiles.push(s.itemcode)}t.editmarkfiles.push("machuid"),t.editmarkfiles.push("plandate"),t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,operationColumn:!!t.operationmark,renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex),r="",n=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));if(-1!=n){var l=e.customList[n].valuejson?e.customList[n].valuejson.split(","):[];return r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]])]),a("div",{directives:[{name:"show",value:l.length}]},[a("div",{style:"width:0;height:0"},[a("el-select",{class:"wpselect",style:"width:0;height:0;opacity: 0;",attrs:{size:"small",id:t.itemcode+o.rownum+O["b"].formcode},on:{change:function(){var i=Object(S["a"])(Object(D["a"])().mark((function i(a){return Object(D["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:e.selSpuVal(o,t.itemcode,a);case 1:case"end":return i.stop()}}),i)})));return function(t){return i.apply(this,arguments)}}()},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}},[l.map((function(t,e){return a("el-option",{key:e,attrs:{label:t,value:t},style:"max-width:200px"})}))])]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-arrow-down",style:"font-weight:bold;font-size:14px;",on:{click:function(){document.getElementById(t.itemcode+o.rownum+O["b"].formcode).click()}}})])]),r}if("price"===t.itemcode){var d="";return d=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("span",{class:o.disannulmark?"textlinethrough":"",style:"flex:1"},[o[t.itemcode]]),a("el-tooltip",{class:"item",attrs:{effect:"dark",content:"历史价格",placement:"top-start"}},[a("svg-icon",{attrs:{"icon-class":"historyline"},directives:[{name:"show",value:!e.formdata.assessor}],style:"cursor:pointer;",on:{click:function(){return e.getHistoyrOrder(o,o.goodsuid,e.formdata.groupname)}}})])]),d}if("goodsuid"==t.itemcode)return r=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}}),r;if("plandate"==t.itemcode)return r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[e.$options.filters.dateFormat(o[t.itemcode])])]),a("div",[a("div",{style:"opacity: 0;width:0;height:0"},[a("el-date-picker",{style:"width:0;height:0",attrs:{size:"small",id:t.itemcode+s+O["b"].formcode,value:new Date(o[t.itemcode]),type:"date",format:"MM-dd","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},on:{input:function(e){o[t.itemcode]=e}}})]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-date",on:{click:function(){document.getElementById(t.itemcode+s+O["b"].formcode).focus()}}})])]),r;if("machuid"==t.itemcode||"custpo"==t.itemcode)return r="其他采购"!=e.formdata.billtype?a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]):a("div",{class:"flex a-c",style:"width: 100%;justify-content: space-between;"},[a("span",{style:"flex:1"},[o[t.itemcode]]),a("el-popover",{attrs:{placement:"bottom-start",trigger:"click",title:"单据明细"}},[a("div",{style:"position: relative; min-height: 300px",directives:[{name:"show",value:s==e.saleorderindex}]},[a(E["a"],{attrs:{multi:0},style:"width: 800px; height: 220px",on:{singleSel:function(t){return o.machuid=t.refno,o.custpo=t.custorderid}}})]),a("div",{slot:"reference"},[a("i",{class:"writePacksn el-icon-circle-plus-outline",on:{click:function(){e.saleorderindex=s}}})])])]),r;if("status"==t.itemcode){r="";return 0!=o.finishqty&&o.finishqty<o.quantity?r=a("span",{class:"textborder-blue"},["收货"]):o.finishqty>=o.quantity&&0!=o.finishqty?r=a("span",{class:"textborder-green"},["完成"]):o.disannulmark>1?r=a("span",{class:"textborder-grey"},["撤销"]):o.closed&&(r=a("span",{class:"textborder-grey"},["中止"])),r}return r=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]),r}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["goodsuid","amount","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=this;return Object(S["a"])(Object(D["a"])().mark((function a(){var o,s,r,n,l,d,m,c,u;return Object(D["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:for(s in o=[],t[0])o.push(s);r=0;case 3:if(!(r<t.length)){a.next=24;break}n=t[r],l=0;case 6:if(!(l<o.length)){a.next=21;break}return d=o[l],i.lst[e+r][d]=n[d].replace(/^\s*|\s*$/g,""),a.next=11,Object(B["a"])("D03M02B1",i.lst[e+r],Object.keys(n)[1]);case 11:for(c in m=a.sent,i.lst[e+r])i.lst[e+r][c]=m[c];i.setAttributeJson(i.lst[e+r],e+r),i.countfiles.includes(d)?i.changeInput("",i.lst[e+r],d):Object(B["b"])()&&i.changeInput("",i.lst[e+r],Object(B["b"])()),u=i.customList.findIndex((function(t){return t.attrkey==d})),-1!=u&&i.setAttributeJson(i.lst[e+r],e+r),i.$forceUpdate();case 18:l++,a.next=6;break;case 21:r++,a.next=3;break;case 24:case"end":return a.stop()}}),a)})))()},selSpuVal:function(t,e,i){var a=this;return Object(S["a"])(Object(D["a"])().mark((function o(){return Object(D["a"])().wrap((function(o){while(1)switch(o.prev=o.next){case 0:return t[e]=i,o.t0=a,o.next=4,Object(B["a"])("D03M02B1",t,e);case 4:o.t1=o.sent,o.t2=t.rownum,o.t0.setAttributeJson.call(o.t0,o.t1,o.t2),Object(B["b"])()&&a.changeInput("",t,Object(B["b"])());case 8:case"end":return o.stop()}}),o)})))()},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],s={key:o.attrkey,value:t[o.attrkey]?String(t[o.attrkey]).replace(/\s*/g,""):""};""!=s.value&&i.push(s)}if(0==i.length)this.lst[e].attributejson="";else{this.lst[e].attributejson=JSON.stringify(i);for(a=0;a<i.length;a++)this.$set(this.lst[e],i[a].key,i[a].value)}this.$forceUpdate()},changeInput:function(t,e,i){"quantity"==i&&(e.maxqty=e.quantity),"maxqty"==i&&e.maxqty-e.quantity<0&&(this.$message.warning("最大允收数不能小于于数量"),e.maxqty=e.quantity),"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i),this.getSummary()},getAdd:function(t){"采购计划"==this.formdata.billtype?(this.buyPlanFormVisible=!0,this.selecturl="/D03M01B1/getOnlinePageList?appl=1"):"销售订单"==this.formdata.billtype?(this.saleOrderFormVisible=!0,this.selecturl="/D01M03B1/getOnlineWkPageList?appl=1"):this.selgoodsvisible=!0},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"closed",0),this.$set(e,"finishqty",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},selGoods:function(){var t=this.$refs.selGoods.$refs.selectgoods.selection;if(0!=t.length){this.selgoodsvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},c);a.goodsid=i.id,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.goodsmaterial=i.material,a.partid=i.partid,a.goodscustom1=i.custom1,a.goodscustom2=i.custom2,a.goodscustom3=i.custom3,a.goodscustom4=i.custom4,a.goodscustom5=i.custom5,a.goodscustom6=i.custom6,a.goodscustom7=i.custom7,a.goodscustom8=i.custom8,a.goodscustom9=i.custom9,a.goodscustom10=i.custom10,a.itemtaxrate=i.taxrate?i.taxrate:this.nowitemtaxrate,a.quantity=0,a.taxprice=i.inprice?i.inprice:0,a.attributejson=i.attributejson,0!=this.idx&&(a.pid=this.idx),this.changeInput("",a,"taxprice"),this.lst.push(a),this.setAttributeJson(a,this.lst.length-1)}}else this.$message.warning("请选择单据内容")},selDummy:function(t){var e=t;if(0!=e.length){this.$refs.itemGroupBtns.DummyVisible=!1;for(var i=0;i<e.length;i++){var a=e[i],o=Object.assign({},c);o.goodsid=a.id,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.goodscustom1=a.custom1,o.goodscustom2=a.custom2,o.goodscustom3=a.custom3,o.goodscustom4=a.custom4,o.goodscustom5=a.custom5,o.goodscustom6=a.custom6,o.goodscustom7=a.custom7,o.goodscustom8=a.custom8,o.goodscustom9=a.custom9,o.goodscustom10=a.custom10,o.price=a.inprice?a.inprice:0,o.itemtaxrate=a.taxrate?a.taxrate:0,o.quantity=1,o.virtualitem=1,0!=this.idx&&(o.pid=this.idx),this.changeInput("",o,"quantity"),this.lst.push(o)}}else this.$message.warning("请选择虚拟品内容")},buyPlanBtn:function(){this.buyPlanFormVisible=!1;for(var t=this.$refs.buyPlan.$refs.selectVal.selection,e=0;e<t.length;e++){var i=t[e],a=Object.assign({},c);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.citeitemid=i.id,a.citeuid=i.refno,a.customer=i.customer,a.custpo=i.custpo,a.itemtaxrate=i.itemtaxrate,a.maxqty=i.quantity,a.price=i.price,a.quantity=i.quantity,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxamount-i.amount,a.attributejson=i.attributejson,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}},saleOrderBtn:function(){this.saleOrderFormVisible=!1;for(var t=this.$refs.saleOrder.$refs.selectVal.selection,e=0;e<t.length;e++){var i=t[e],a=Object.assign({},c);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=i.amount,a.citeitemid=i.id,a.citeuid=i.refno,a.machitemid=i.id,a.machuid=i.refno,a.machgroupid=i.groupid,a.mrpuid=i.mrpuid,a.mrpitemid=i.mrpitemid,a.customer=i.groupname,a.custpo=i.custorderid,a.groupname=i.groupname,a.groupuid=i.groupuid,a.abbreviate=i.abbreviate,a.itemtaxrate=i.itemtaxrate,a.maxqty=i.quantity,a.price=i.price,a.quantity=i.quantity,a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxamount-i.amount,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}},submitTime:function(){var t=this;this.multipleSelection.forEach((function(e,i){t.lst[e.rownum][t.dateType]=t.deliverydate})),this.deliverydatevisible=!1,this.$forceUpdate(),this.deliverydate=""},getHistoyrOrder:function(t,e,i){var a=this;this.currentRowKey=t.rowKeys;var o={PageNum:1,PageSize:20,OrderType:1,SearchType:1,scenedata:[{field:"App_Workgroup.groupname",fieldtype:0,math:"like",value:i},{field:"Mat_Goods.goodsuid",fieldtype:0,math:"like",value:e}]};this.$request.post("/D03M02B1/getPageList",JSON.stringify(o)).then((function(t){200===t.data.code&&(a.isShowHistory=!0,a.historyData=t.data.data.list,a.$nextTick((function(){a.$refs.historyOrder.initEchart(a.historyData)})))}))},getHistoryPrice:function(t,e){var i=this;this.isShowHistory=!1,"含税"==e?this.lst.map((function(e){e.rowKeys===i.currentRowKey&&(e.taxprice=t,e.price=L["a"].round(t/1.13,4))})):this.lst.map((function(e){e.rowKeys===i.currentRowKey&&(e.price=t)}))},getdroupList:function(){if(0!=this.lst.length){this.droupList=[];var t,e=[],i=Object(_["a"])(this.lst);try{for(i.s();!(t=i.n()).done;){var a=t.value;if(-1==e.indexOf(a["goodsname"])){e.push(a["goodsname"]);var o={label:a.goodsname,show:!1};this.droupList.push(o)}}}catch(s){i.e(s)}finally{i.f()}}},changeDropdown:function(t){if(t){this.checkboxOption.selectedRowKeys=[],this.multipleSelection=[];for(var e=0;e<this.lst.length;e++)for(var i=this.lst[e],a=0;a<this.droupList.length;a++){var o=this.droupList[a];i.goodsname==o.label&&o.show&&(this.checkboxOption.selectedRowKeys.push(i.rowKeys),this.multipleSelection.push(i))}}},quickTickBtn:function(t){for(var e=0;e<this.lst.length;e++){var i=this.lst[e];if(i.goodsname==t.label)if(t.show)this.checkboxOption.selectedRowKeys.push(i.rowKeys),this.multipleSelection.push(i);else{var a=this.checkboxOption.selectedRowKeys.indexOf(i.rowKeys);this.checkboxOption.selectedRowKeys.splice(a,1),this.multipleSelection.splice(a,1)}}this.$forceUpdate()},quickTickSearch:function(t){if(t){var e=[];this.droupList.forEach((function(i){i.label.indexOf(t)>=0&&e.push(i)})),this.droupList=e}else this.getdroupList()}}},V=N,H=(i("3f1b"),Object(y["a"])(V,x,k,!1,null,"1e0fe4be",null)),K=H.exports,z=i("03b9"),A=i("dcb4"),G=i("ed33"),J=i("941f"),W=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},U=[],Y=(i("3ca3"),i("ddb0"),i("4cf0")),Q=i("b893"),X={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"941f"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:Y["b"],customList:[],customData:[],columnHidden:[],footerData:[],rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={citecode:this.searchVal};this.queryParams.SearchPojo=e;var i="/D03M06B1PRE/getPageTh";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(Q["d"])()[0],EndDate:Object(Q["d"])()[1]}),s["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total)})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(S["a"])(Object(D["a"])().mark((function e(){return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(Y["b"].formcode,Y["b"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxamount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},Z=X,tt=(i("664a"),Object(y["a"])(Z,W,U,!1,null,"c3a7dfd2",null)),et=tt.exports,it=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},at=[],ot=i("cdcd"),st={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"ed33"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:ot["a"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={orderno:this.searchVal};if(this.queryParams.SearchPojo=e,this.online)var i="/D03M03B1/getOnlinePageList";else i="/D03M03B1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(Q["d"])()[0],EndDate:Object(Q["d"])()[1]}),s["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++){var a=t.lst[i];a.compcost=a.quantity-a.buyqty;for(var o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(S["a"])(Object(D["a"])().mark((function e(){return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(ot["a"].formcode,ot["a"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=a("GoodsInfo",{attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}});return s}if("status"==t.itemcode){s="";return 0!=o.finishqty&&o.finishqty<o.quantity?s=a("span",{class:"textborder-blue"},["入库"]):o.finishqty>=o.quantity&&0!=o.finishqty?s=a("span",{class:"textborder-green"},["完成"]):o.disannulmark>1&&(s=a("span",{class:"textborder-grey"},["撤销"])),s}if("compcost"==t.itemcode){s="";return s=o.quantity-o.finishqty<0?a("span",{style:"color:#F56C6C"},[o.quantity-o.finishqty]):o.quantity-o.finishqty==0?a("span",{style:"color:#67C23A"},[o.quantity-o.finishqty]):o.quantity-o.finishqty==o.quantity?a("span",[o.quantity-o.finishqty]):a("span",{style:"color:#409eff"},[o.quantity-o.finishqty]),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity","compcost"])},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","finishqty","taxamount","compcost"];this.$countCellData(this,i,t,e)},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},rt=st,nt=(i("f550"),Object(y["a"])(rt,it,at,!1,null,"0faf5092",null)),lt=nt.exports,dt={name:"Formedit",components:{FormTemp:A["a"],EditHeader:w,EditItem:K,D03M06B1PRE:J["default"],D03M03B1:G["default"],D03M03B1List:lt,D03M06B1PREList:et},props:["idx","isDialog","initData","billcode","selectList"],data:function(){return{title:"采购订单",operateBar:u,processBar:f,commonurl:"/D03M02B1/printBill",formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,acceptdate:new Date,accepter:"",acceptercode:"",acceptremark:"",arrivaladd:"",assessdate:new Date,assessor:"",billamount:0,billdate:new Date,billplandate:new Date,billstatecode:"",billstatedate:new Date,billtaxamount:0,billtaxtotal:0,billtitle:"",billtype:"销售订单",disannulmark:0,groupid:"",groupname:"",item:[],linkman:"",linktel:"",operator:"",orderno:"",payment:"",prepayments:0,summary:"",taxrate:0,transport:"",webbill:0},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:z["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D03M02B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):z["a"],t.formtemplate.footer.type||(t.formtemplate.footer=z["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&s["a"].get("/D03M02B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.$refs.elitem.multipleSelection=[],t.$refs.elitem.checkboxOption.selectedRowKeys=[],t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id},autoClear:function(){this.formdata.groupname="",this.formdata.groupid=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.billamount=i.billamount,this.formdata.billtaxamount=i.billtaxamount,this.formdata.billtaxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(m,a,this.formdata),0==this.idx?n.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):n.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(e)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){console.log(t,"vallll"),this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},printButton:function(t){this.commonurl="/D03M02B1/printBillMergeGoods",this.$refs.PrintServer.printButton(0,1)},printMultiServer:function(){this.$refs.PrintMultiServer.printButton(0,1)},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t;var e=this.$refs.elitem.multipleSelection;"D03M03B1"===t&&e.length>0&&(this.formdata.item=e)},processBill:function(t){this.processVisible=!0,this.processTitle="D03M03B1"==t?"采购验收":"采购预付",this.processModel=t},billSwitch:function(t){var e=this.$store.getters.userinfo.configs;if("D01M03B1"==t){this.formdata.billtype="销售订单",this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】销售订单转入",this.formdata.billtaxamount=this.initData.billtaxamount,this.formdata.billamount=this.initData.billamount,this.formdata.billtaxtotal=this.initData.billtaxtotal,this.formdata.taxrate=this.initData.taxrate,this.formdata.transport=this.initData.logisticsmode,this.formdata.arrivaladd=this.initData.logisticsport,this.formdata.orderno=this.initData.refno,this.formdata.prepayments=0,this.formdata.assessor="",this.formdata.billdate=new Date,this.formdata.item=[];for(var i=0;i<this.initData.item.length;i++){var a=this.initData.item[i],o=Object.assign({},c);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.custpo=this.initData.custorderid,o.customer=this.initData.groupname,o.groupuid=this.initData.groupuid,o.groupname=this.initData.groupname,o.abbreviate=this.initData.abbreviate,o.citeitemid=a.id,o.citeuid=this.initData.refno,o.machitemid=a.id,o.machuid=this.initData.refno,o.machgroupid=this.initData.groupid,o.mrpuid=a.mrpuid,o.mrpitemid=a.mrpitemid,o.quantity=this.$fomatFloat(a.quantity-a.buyquantity,2),o.itemtaxrate=a.itemtaxrate,o.taxprice=0,o.taxtotal=0,o.maxqty=o.quantity,o.price=0,o.taxamount=0,o.amount=0,o.batchno=a.batchno,o.finishclosed=a.virtualitem?1:0,o.attributejson=a.attributejson,o.virtualitem=a.virtualitem,o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):this.formdata.item.push(o)}}else if("D03M01B2"==t){this.formdata.billtype="采购计划",this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】采购计划转入",this.formdata.orderno=this.initData.refno,this.formdata.arrivaladd=this.initData.arrivaladd,this.formdata.billamount=this.initData.billamount,this.formdata.billtaxtotal=this.initData.billtaxtotal,this.formdata.taxrate=this.initData.taxrate;for(i=0;i<this.initData.item.length;i++){a=this.initData.item[i],o=Object.assign({},c);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.custpo=a.custpo,o.citeitemid=a.id,o.citeuid=this.initData.refno,o.machitemid=a.machitemid,o.machuid=a.machuid,o.machgroupid=a.machgroupid,o.mrpuid=a.mrpuid,o.mrpitemid=a.mrpitemid,o.mainplanuid=a.mainplanuid,o.mainplanitemid=a.mainplanitemid,o.quantity=this.$fomatFloat(a.quantity-a.buyqty,2),o.itemtaxrate=a.itemtaxrate,o.taxprice=a.taxprice,o.taxtotal=a.taxtotal,o.maxqty=o.quantity,o.price=a.price,o.taxamount=a.buyqty?this.$fomatFloat(o.quantity*a.taxprice,2):a.taxamount,o.amount=a.buyqty?this.$fomatFloat(o.quantity*a.price,2):a.amount,o.batchno=a.batchno,o.finishclosed=a.virtualitem?1:0,o.attributejson=a.attributejson,o.virtualitem=a.virtualitem,o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):this.formdata.item.push(o)}}else if("D05M11B1"==t);else if("D05M01B1ML"==t){this.formdata.billtype="其他采购",this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】生产工单转入",this.formdata.orderno=this.initData.refno,this.formdata.item=[];var s=this.selectList.length>0?this.selectList:this.initData.mat;for(i=0;i<s.length;i++){a=s[i],o=Object.assign({},c);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.custpo=a.custpo,o.citeuid=this.initData.refno,o.quantity="物料"==a.goodsstate?this.$fomatFloat(a.bomqty-a.finishqty,2):this.$fomatFloat(a.stoplanqty-a.finishqty,2),o.maxqty=a.bomqty,o.batchno=a.batchno,o.attributejson=a.attributejson;var r=this.initData.item.findIndex((function(t){return t.rowcode==a.itemrowcode}));-1!=r&&(o.citeitemid=this.initData.item[r].id,o.machuid=this.initData.item[r].machuid,o.machitemid=this.initData.item[r].machitemid,o.machgroupid=this.initData.item[r].machgroupid,o.mainplanitemid=this.initData.item[r].mainplanitemid,o.mainplanuid=this.initData.item[r].mainplanuid,o.mrpuid=this.initData.item[r].mrpuid,o.mrpitemid=this.initData.item[r].mrpitemid),o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):o.quantity&&this.formdata.item.push(o)}}}}},mt=dt,ct=(i("7e4c"),Object(y["a"])(mt,a,o,!1,null,"15cbc436",null));e["default"]=ct.exports},a3db:function(t,e,i){"use strict";i("04f2")},a485:function(t,e,i){},a5ab:function(t,e,i){},a7b6:function(t,e,i){},aa59:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}}),i("BillState",{attrs:{writedate:!1,formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,changeBillType:t.changeBillType}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px",height:"100%"}},[i("EditCash",{ref:"cashitem",style:{width:"99%",height:"其他预收"!=t.formdata.billtype?"50%":"100%"},attrs:{lstitem:t.formdata.cash,formdata:t.formdata,formtemplateItem:t.formtemplate.cash,idx:t.idx,formstate:t.formstate,itemAmount:t.itemAmount},on:{bindData:t.bindData,computerCashAmount:t.computerCashAmount}}),"其他预收"!=t.formdata.billtype?i("EditItem",{ref:"elitem",staticStyle:{width:"99%",height:"48%","margin-top":"10px"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData,computerItemAmount:t.computerItemAmount}}):t._e()],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D01M08B1DEPEdit",commonurl:"/D01M08B1DEP/printBill",weburl:"/D01M08B1DEP/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D01M08B1DEPEdit",examineurl:"/D01M08B1DEP/sendapprovel"}}),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D01M03B1"==t.processModel?i("D01M03B1List",{ref:"D01M03B1List",attrs:{searchVal:t.formdata.citecode,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=i("2909"),r=(i("a9e3"),i("b64b"),i("d3b7"),i("ac1f"),i("6062"),i("3ca3"),i("5319"),i("ddb0"),i("b775"));const n={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D01M08B1DEP/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);r["a"].post("/D01M08B1DEP/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){r["a"].get("/D01M08B1DEP/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,r["a"].get("/D01M08B1DEP/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D01M08B1DEP/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D01M08B1DEP/closed?type="+(3==t?1:0);r["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var l,d=n,m=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){return t.$emit("changeBillType")}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"销售预收",value:"销售预收"}}),i("el-option",{attrs:{label:"其他预收",value:"其他预收"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"客户",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B1/getOnlinePageList",type:"客户"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"预收金额"}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.billamount))])])],1)],1)],1)},c=[],u={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupname:[{required:!0,trigger:"blur",message:"客户为必填项"}],operator:[{required:!0,trigger:"blur",message:"经办人为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},f=u,p=(i("b9c6"),i("2877")),h=Object(p["a"])(f,m,c,!1,null,"3c40d3c4",null),g=h.exports,b=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","copyrow","refresh"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"订单信息","append-to-body":!0,visible:t.selordervisible,width:"1200px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1,selecturl:"/D01M03B1/getOnlineAdvaPageTh?groupid="+t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.SelOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},y=[],v=(i("c740"),i("caad"),i("e9c4"),i("2532"),i("c7cd"),i("159b"),i("ade3")),w=(l={amount:0},Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(l,"amount",0),"billtaxamount",0),"custom1",""),"custom10",""),"custom2",""),"custom3",""),"custom4",""),"custom5",""),"custom6",""),"custom7",""),Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(l,"custom8",""),"custom9",""),"delibillcode",""),"delibillid",""),"id",""),"machbillcode",""),"machbillid",""),"pid",""),"remark",""),"rownum",0)),x={amount:0,cashaccid:"",cashaccname:"",custom1:"",custom10:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",id:"",pid:"",remark:"",rownum:0},k=i("49b1"),_=i("7996"),D={name:"Elitem",components:{SelOrder:_["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],selordervisible:!1,setColumsVisible:!1,keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:k["c"],customList:[],editmarkfiles:[],countfiles:["amount","billtaxamount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=k["c"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(k["c"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(e,i){var a=e.row;e.column,e.rowIndex;if("goodsuid"==t.itemcode){var o=i("GoodsInfo",{class:a.disannulmark?"textlinethrough":"",attrs:{scopeVal:a[t.itemcode]}});return o}o=i("span",{class:a.disannulmark?"textlinethrough":""},[a[t.itemcode]]);return o}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["machbillcode","amount","billtaxamount"]);var t=this.footerData[0].amount;this.$emit("computerItemAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.formdata.groupid?this.selordervisible=!0:this.$message.warning("请选择客户")},SelOrder:function(){var t=this.$refs.SelOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},w);a.amount=i.billtaxamount-i.advaamount,a.billtaxamount=i.billtaxamount,a.machbillcode=i.refno,a.machbillid=i.id,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},S=D,O=(i("28f6"),Object(p["a"])(S,b,y,!1,null,"3d6ab2b1",null)),$=O.exports,B=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form"},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","refresh"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selordervisible?i("el-dialog",{attrs:{title:"出纳账户","append-to-body":!0,visible:t.selordervisible,width:"1000px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selordervisible=e}}},[i("SelOrder",{ref:"SelOrder",attrs:{multi:1}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.SelOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},q=[],P=i("233f"),C={name:"Elitem",components:{SelOrder:P["a"]},props:["formdata","lstitem","idx","itemAmount","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],selordervisible:!1,setColumsVisible:!1,keynum:0,tableHeight:0,multipleSelection:[],isEditOk:!0,selecturl:"",tableForm:k["a"],customList:[],editmarkfiles:[],countfiles:["amount"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)&&t.countfiles.includes(i.field)&&t.getSummary()}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.getSummary()}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=k["a"];this.formtemplateItem.type&&(e.item=this.formtemplateItem.content),this.$getColumn(k["a"].formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(e,i){var a=e.row;e.column,e.rowIndex;if("goodsuid"==t.itemcode){var o=i("GoodsInfo",{class:a.disannulmark?"textlinethrough":"",attrs:{scopeVal:a[t.itemcode]}});return o}o=i("span",{class:a.disannulmark?"textlinethrough":""},[a[t.itemcode]]);return o}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["cashaccname","amount"]);var t=this.footerData[0].amount;this.$emit("computerCashAmount",t)},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.getSummary()}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){this.selordervisible=!0},SelOrder:function(){var t=this.$refs.SelOrder.$refs.selectVal.selection;if(0!=t.length){this.selordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},x);a.amount=this.itemAmount,a.cashaccid=i.id,a.cashaccname=i.accountname,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},M=C,F=(i("b3dd"),Object(p["a"])(M,B,q,!1,null,"29c7bd7b",null)),I=F.exports,j=i("6bae"),E=i("dcb4"),R=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","billamount","operator","citecode","outamount","returnuid","orguid","summary","fmdoccode","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],T=["id","pid","machbillid","machbillcode","delibillid","delibillcode","billtaxamount","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],L=["id","pid","cashaccid","cashaccname","amount","rownum","remark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],N={params:R,paramsItem:T,paramsCash:L},V=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:0,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]}],H=[],K=i("aaa1"),z={name:"Formedit",components:{FormTemp:E["a"],EditHeader:g,EditItem:$,EditCash:I,D01M03B1List:K["default"]},props:["idx","isDialog","initData","billcode","isprocessDialog"],data:function(){return{title:"预收款",operateBar:V,processBar:H,formdata:{abbreviate:"",billamount:0,assessdate:new Date,assessor:"",billdate:new Date,billtitle:"",billtype:"销售预收",citecode:"",createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,createdate:"",groupid:"",groupname:"",groupuid:"",item:[],cash:[],lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,operator:"",orguid:"",outamount:0,refno:"",returnuid:"",revision:0,summary:""},formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupname:[{required:!0,trigger:"blur",message:"客户为必填项"}],operator:[{required:!0,trigger:"blur",message:"经办人为必填项"}]},itemAmount:0,cashAmount:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:j["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp(),this.bindData()},mounted:function(){this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D01M08B1DEP").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):j["a"],t.formtemplate.cash=j["a"].cash,t.formtemplate.footer.type||(t.formtemplate.footer=j["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&r["a"].get("/D01M08B1DEP/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1,"其他预收"==t.formdata.billtype&&t.$refs.cashitem.catchHight()):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},computerItemAmount:function(t){this.itemAmount=t},computerCashAmount:function(t){this.cashAmount=t,this.formdata.billamount=Number(this.cashAmount)},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if("其他预收"!=this.formdata.billtype){if(console.log(this.formdata,this.itemAmount),this.cashAmount,this.formdata.billamount!=this.itemAmount||this.formdata.billamount!=this.cashAmount)return void this.$message.warning("预收金额 与 出纳金额和单据金额不一致");this.formdata.item=this.$refs.elitem.lst,this.formdata.citecode="";for(var e="",i=0;i<this.formdata.item.length;i++){var a=this.formdata.item[i];e+=a.machbillcode+","}var o=/,$/gi;e=e.replace(o,"");var r=e.split(","),n=Object(s["a"])(new Set(r));for(i=0;i<n.length;i++)this.formdata.citecode+=n[i]+",";this.formdata.citecode=this.formdata.citecode.replace(o,"")}this.submitting=1,this.formdata.cash=this.$refs.cashitem.lst;var l={item:[],cash:[]};l=this.$getParam(N,l,this.formdata),0==this.idx?d.add(l).then((function(e){t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败")})):d.update(l).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){d.delete(e)})).catch((function(){}))},approval:function(){d.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?d.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){d.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t,console.log(this.processModel,"ss")},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.item=[],this.formdata.cash=[]},autoClear:function(t){this.formdata.groupname="",this.formdata.groupid=""},changeBillType:function(){this.formdata.item=[],this.formdata.cash=[],this.formdata.taxamount=0,this.$refs.cashitem.catchHight()},billSwitch:function(t){if(console.log(this.initData),"D01M03B1"==t){this.formdata.billtype="销售预收",this.formdata.billtitle="【"+this.initData.refno+"】销售订单转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billamount=this.initData.billtaxamount,this.formdata.citecode=this.initData.refno,this.formdata.item=[];var e={billtaxamount:this.initData.billtaxamount,amount:this.initData.billtaxamount,machbillid:this.initData.id,machbillcode:this.initData.refno,virtualitem:this.initData.virtualitem};this.formdata.item.push(e)}}}},A=z,G=(i("a3db"),Object(p["a"])(A,a,o,!1,null,"65205326",null));e["default"]=G.exports},af2e:function(t,e,i){"use strict";i("d56d")},b1a9:function(t,e,i){"use strict";i("3470")},b224:function(t,e,i){},b3dd:function(t,e,i){"use strict";i("0362")},b83f:function(t,e,i){"use strict";i("4e4d")},b9c6:function(t,e,i){"use strict";i("703e")},bb0f:function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"领料单",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"领料单",value:"领料单"},{label:"退料单",value:"退料单"},{label:"生产领料",value:"生产领料"},{label:"生产退料",value:"生产退料"}],required:!0},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"车间",searchtype:"workshop",type:"autocomplete",methods:"",param:"",require:!0},{col:5,code:"projectid",label:"项目",type:"input",methods:"",param:""}]}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"领用人员",type:"input",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},bc19:function(t,e,i){},bc99:function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));var a={header:{type:0,title:"采购开票",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"采购开票",value:"采购开票"},{label:"委外开票",value:"委外开票"},{label:"外协开票",value:"外协开票"},{label:"其他应付",value:"其他应付"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"外协厂商",searchtype:"factory",type:"autocomplete",methods:"",param:"",require:!0,show:"this.formdata.billtype == '外协开票' || this.formdata.billtype == '委外开票'"},{col:5,code:"groupname",label:"供应厂商",searchtype:"supplier",type:"autocomplete",methods:"",param:"",require:!0,show:"this.formdata.billtype == '采购开票' || this.formdata.billtype == '其他应付'"},{col:5,code:"invocode",label:"发票编码",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"invodate",label:"开票日期",type:"date",methods:"computerTime",param:""},{col:5,code:"aimdate",label:"付款计划",type:"date",methods:"",param:""},{col:3,code:"taxamount",label:"发票金额",type:"input",methods:"",param:"",show:"this.formdata.billtype == '其他应付'"},{col:3,code:"taxamount",label:"发票金额",type:"input",methods:"",param:"",show:"this.formdata.billtype != '其他应付'"},{col:3,code:"paid",label:"已付金额",type:"text",methods:"",param:""}]}]},footer:{type:1,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",billcode:"buy.operator",type:"dictionary",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}}},be8e:function(t,e,i){"use strict";i("1535")},bfc3:function(t,e,i){"use strict";i("a7b6")},c03f:function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r}));var a={formcode:"D04M08B1Th",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Mat_Requisition.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.billtype"},{itemcode:"billtitle",itemname:"单据主题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Requisition.billdate"},{itemcode:"groupname",itemname:"车间",minwidth:"70",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"operator",itemname:"领用人员",minwidth:"70",displaymark:1,overflow:1,datasheet:"Mat_Requisition.operator"},{itemcode:"summary",itemname:"摘要",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.summary"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemcount",itemname:"款数",sortable:1,minwidth:"60",displaymark:1,overflow:1,datasheet:"Mat_Requisition.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.assessor"}]},o={formcode:"D04M08B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",datasheet:"Mat_Requisition.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.billtype"},{itemcode:"billtitle",itemname:"单据主题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Requisition.billdate"},{itemcode:"groupname",itemname:"车间",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_RequisitionItem.quantity"},{itemcode:"workuid",itemname:"加工单号",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_RequisitionItem.workuid"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1}]},s={formcode:"D04M08B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"60",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"120",displaymark:1,overflow:1,editmark:1},{itemcode:"plandate",itemname:"计划完成",minwidth:"180",displaymark:1,overflow:1},{itemcode:"finishqty",itemname:"完成数",minwidth:"80",displaymark:1,overflow:1},{itemcode:"citeuid",itemname:"应用单号",minwidth:"100",displaymark:1,overflow:1},{itemcode:"machuid",itemname:"销售订单",minwidth:"125",displaymark:1,overflow:1,editmark:1},{itemcode:"goodsType",itemname:"物料类别",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"120",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D04M08B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",datasheet:"Mat_Requisition.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.billtype"},{itemcode:"billtitle",itemname:"单据主题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Requisition.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Requisition.billdate"},{itemcode:"groupname",itemname:"车间",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"goodsmaterial",itemname:"材质",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.material"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_RequisitionItem.quantity"},{itemcode:"workuid",itemname:"加工单号",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_RequisitionItem.workuid"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1}]}},c403:function(t,e,i){},c6bc:function(t,e,i){},c71a:function(t,e,i){"use strict";i("ebf1")},ca99:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.paid>0&&t.formdata.paid>=t.formdata.taxamount&&0==t.formdata.closed,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,beoverdue:t.formdata.paid<t.formdata.taxamount&&(new Date).getTime()>new Date(t.formdata.aimdate).getTime()&&0==t.formdata.closed,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear,computerTime:t.computerTime}})],1)]},proxy:!0},{key:"Item",fn:function(){return["其他应付"!=t.formdata.billtype?i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1):t._e()]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M05B1Edit",commonurl:"/D03M05B1/printBill",weburl:"/D03M05B1/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M05B1Edit",examineurl:"/D03M05B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},[i("D03M06B1",{ref:"D03M06B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M05B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}})],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D03M06B1"==t.processModel?i("D03M06B1List",{ref:"D03M06B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=(i("b64b"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M05B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M05B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D03M05B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D03M05B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M05B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M05B1/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=r,l=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","taxrate","taxamount","taxtotal","amount","invodate","invocode","aimdate","itemtaxrate","summary","closed","disannulmark","fmdocmark","fmdoccode","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],d=["id","pid","finishuid","finishdate","finishtype","finishitemid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","partid","billqty","quantity","taxprice","taxamount","price","amount","taxtotal","rownum","remark","machuid","machitemid","machgroupid","custpo","customer","mrpuid","mrpitemid","orderuid","orderitemid","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10","custom11","custom12","custom13","custom14","custom15","custom16","custom17","custom18"],m=["finishuid","amount","billqty","customer","custpo","partid","goodscustom1","goodscustom10","goodscustom2","goodscustom3","goodscustom4","goodscustom5","goodscustom6","goodscustom7","goodscustom8","goodscustom9","goodsid","goodsmaterial","goodsname","goodsphoto1","goodsspec","goodsuid","goodsunit","price","quantity","remark","taxamount","taxprice","taxtotal"],c={params:l,paramsItem:d,copyItem:m},u={amount:0,billqty:0,custom1:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",customer:"",custpo:"",finishdate:new Date,finishitemid:"",finishtype:"",finishuid:"",goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",id:"",intQtyMark:0,machgroupid:"",machitemid:"",machuid:"",mrpitemid:"",mrpuid:"",partid:"",pid:"",price:0,quantity:0,remark:"",revision:0,rownum:0,taxamount:0,taxprice:0,taxtotal:0},f=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,divided:!0,label:"采购付款",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D03M06B1",children:[]}],p=[{show:1,divided:!1,label:"采购付款",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D03M06B1",children:[]}],h=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[],t.formdata.taxamount=0,t.formdata.groupid="",t.formdata.groupname=""}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购开票",value:"采购开票"}}),i("el-option",{attrs:{label:"其他应付",value:"其他应付"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[i("el-form-item",{attrs:{label:"单据主题",prop:"billtitle"}},[i("el-input",{attrs:{placeholder:"请输入单据主题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)])],1),i("el-row",["外协开票"==t.formdata.billtype||"委外开票"==t.formdata.billtype?i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"外协厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B4/getPageList",type:"外协厂商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]):i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"供应厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("invocode")}}},[i("el-form-item",{attrs:{label:"发票编码"}},[i("el-input",{attrs:{placeholder:"请输入发票编码",clearable:"",size:"small"},model:{value:t.formdata.invocode,callback:function(e){t.$set(t.formdata,"invocode",e)},expression:"formdata.invocode"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("invodate")}}},[i("el-form-item",{attrs:{label:"开票日期",prop:"invodate"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期",size:"small"},on:{change:function(e){return t.$emit("computerTime")}},model:{value:t.formdata.invodate,callback:function(e){t.$set(t.formdata,"invodate",e)},expression:"formdata.invodate"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"付款计划"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.aimdate,callback:function(e){t.$set(t.formdata,"aimdate",e)},expression:"formdata.aimdate"}})],1)],1),i("el-col",{attrs:{span:9}},[i("div",{staticStyle:{display:"flex"}},[i("el-form-item",{attrs:{label:"发票金额"}},["其他应付"==t.formdata.billtype?i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入发票金额",size:"small"},model:{value:t.formdata.taxamount,callback:function(e){t.$set(t.formdata,"taxamount",e)},expression:"formdata.taxamount"}}):i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v(" ￥"+t._s(t.formdata.taxamount?t.formdata.taxamount:0))])],1),i("el-form-item",{attrs:{label:"已付金额"}},[i("span",{staticStyle:{"font-size":"18px",color:"#666"}},[t._v("￥"+t._s(t.formdata.paid?t.formdata.paid:0))])])],1)])],1)],1)},g=[],b={props:{formdata:{type:Object},title:{type:String}},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"往来单位为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},y=b,v=(i("b1a9"),i("2877")),w=Object(v["a"])(y,h,g,!1,null,"a883edd0",null),x=w.exports,k=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","copyrow","refresh"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"left",fn:function(){return[i("el-button",{attrs:{type:"primary",size:"mini",disabled:2==t.formstate},nativeOn:{click:function(e){return t.getSelKouKuan(1)}}},[i("i",{staticClass:"el-icon-remove-outline"}),t._v(" 扣款")])]},proxy:!0},{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.acceptFormVisible?i("el-dialog",{attrs:{title:"采购验收单","append-to-body":!0,visible:t.acceptFormVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.acceptFormVisible=e}}},[i("SelAccept",{ref:"selAccept",attrs:{multi:t.multi,selecturl:"/D03M03R1/getOnlineInvoPageList?groupid="+t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selAccept()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.acceptFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.KouKuanVisible?i("el-dialog",{attrs:{title:"扣款单","append-to-body":!0,visible:t.KouKuanVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.KouKuanVisible=e}}},[i("SelKouKuan",{ref:"selKouKuan",attrs:{multi:1,selecturl:"/D03M04B1/getOnlinePageList?groupid="+t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selKouKuan()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.KouKuanVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},_=[],D=i("c7eb"),S=i("1da1"),O=(i("c740"),i("caad"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),i("da86")),$=i("9a2c"),B=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.refno))])]}}])}),i("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.groupname))])]}}])}),i("el-table-column",{attrs:{label:"货品编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsuid))])]}}])}),i("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsname))])]}}])}),i("el-table-column",{attrs:{label:"规格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.goodsspec))])]}}])}),i("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.quantity))])]}}])}),i("el-table-column",{attrs:{label:"金额",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.taxamount))])]}}])}),i("el-table-column",{attrs:{label:"采购订单号",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.orderuid))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},q=[],P=i("333d"),C={components:{Pagination:P["a"]},props:["multi","selecturl"],data:function(){return{title:"采购扣款",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M04B1/getPageList";s["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},M=C,F=(i("c71a"),Object(v["a"])(M,B,q,!1,null,"3edf162c",null)),I=F.exports,j={name:"Elitem",components:{SelAccept:$["a"],SelKouKuan:I},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],multi:0,keynum:0,acceptFormVisible:!1,setColumsVisible:!1,KouKuanVisible:!1,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,index:0,tableForm:O["b"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate","billqty"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;t.editmarkfiles.includes(a.field)&&t.countfiles.includes(a.field)&&t.changeInput("",i,a.field)}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=s&&t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[s],Object.keys(o)[1])}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(S["a"])(Object(D["a"])().mark((function e(){var i;return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=O["b"],t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn(O["b"].formcode,i).then((function(e){t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,operationColumn:!!t.operationmark,renderBodyCell:function(e,i){var a=e.row;e.column,e.rowIndex;if("goodsuid"==t.itemcode){var o=i("GoodsInfo",{class:a.disannulmark?"textlinethrough":"",attrs:{scopeIndex:a.rownum,scopeVal:a[t.itemcode]}});return o}o=i("span",{class:a.disannulmark?"textlinethrough":""},[a[t.itemcode]]);return o}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1,this.$forceUpdate()},getSummary:function(){this.$getSummary(this,["goodsuid","amount","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.changeInput("",this.lst[e+o],n)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},changeInput:function(t,e,i){if("itemtaxrate"==i)this.nowitemtaxrate=e.itemtaxrate;else if("quantity"==i&&e.quantity>e.billqty)return this.$message.warning("本次数量不能大于单据数量"),void(e.quantity=e.billqty);e=this.$countInput(e,i),this.getSummary()},getAdd:function(t){this.formdata.groupid?(this.acceptFormVisible=!0,this.multi=t):this.$message.warning("请先选择往来单位")},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"closed",0),this.$set(e,"finishqty",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},selAccept:function(){var t=this.$refs.selAccept.$refs.selectVal.selection;if(0!=t.length){this.acceptFormVisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.price=i.price,a.amount=i.amount,a.quantity=i.quantity,a.taxprice=i.taxprice,a.taxamount=i.taxamount,a.itemtaxrate=i.itemtaxrate,a.billqty=i.quantity,a.taxtotal=i.taxtotal,a.customer=i.customer,a.custpo=i.custpo,a.finishitemid=i.id,a.finishtype=i.billtype,a.finishuid=i.refno,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.orderuid=i.orderuid,a.orderitemid=i.orderitemid,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},getSelKouKuan:function(){this.formdata.groupid?this.KouKuanVisible=!0:this.$message.warning("请先选择供应商")},selKouKuan:function(){var t=this.$refs.selKouKuan.$refs.selectVal.selection;if(0!=t.length){this.KouKuanVisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.amount=Number(0-i.amount),a.billqty=i.quantity,a.customer=i.customer,a.custpo=i.custpo,a.finishitemid=i.id,a.finishtype=i.billtype,a.finishuid=i.refno,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.mrpitemid=i.mrpitemid,a.mrpuid=i.mrpuid,a.price=Number(0-i.price),a.quantity=i.quantity,a.taxamount=Number(0-i.taxamount),a.taxprice=Number(0-i.taxprice),a.taxtotal=i.taxtotal,a.itemtaxrate=i.itemtaxrate,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")}}},E=j,R=(i("3949"),Object(v["a"])(E,k,_,!1,null,"4f9eee22",null)),T=R.exports,L=i("bc99"),N=i("dcb4"),V=i("d85f"),H=i("501c"),K={name:"Formedit",components:{FormTemp:N["a"],EditHeader:x,EditItem:T,D03M06B1:V["default"],D03M06B1List:H["a"]},props:["idx","isDialog","initData","billcode"],data:function(){return{title:"采购开票",operateBar:f,processBar:p,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,aimdate:new Date,amount:0,assessdate:new Date,assessor:"",billdate:new Date,billtitle:"",billtype:"采购开票",closed:0,disannulmark:0,groupid:"",groupname:"",invocode:"",invodate:new Date,item:[],paid:0,refno:"",statecode:"",statedate:new Date,summary:"",taxamount:0,taxrate:0,taxtotal:0},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",creditdquantity:0,creditduint:"day",formtemplate:L["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;s["a"].get("/SaFormCustom/getEntityByCode?key=D03M05B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):L["a"],t.formtemplate.footer.type||(t.formtemplate.footer=L["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&s["a"].get("/D03M05B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.creditdquantity=t.creditdquantity,this.creditduint=t.creditduint,this.computerTime()},autoClear:function(){this.formdata.groupname="",this.formdata.groupid=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if("其他应付"!=this.formdata.billtype){if(0==this.$refs.elitem.lst.length)return void this.$message.warning("单据内容不能为空");for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.amount=i.billamount,this.formdata.taxamount=i.billtaxamount,this.formdata.taxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(c,a,this.formdata)}else a=Object.assign({},this.formdata);0==this.idx?n.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):n.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(t)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},computerTime:function(){if(0!=this.creditdquantity){var t=this.formdata.invodate.getTime();"month"==this.creditduint?t+=2592e6*this.creditdquantity:t+=864e5*this.creditdquantity,this.formdata.aimdate=new Date(t)}},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t},processBill:function(t){this.processVisible=!0,this.processTitle="采购付款",this.processModel=t},billSwitch:function(t){if(console.log(this.initData,"123456"),"D03M03B1"==t){this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】采购验收转入",this.formdata.item=[];for(var e=0;e<this.initData.item.length;e++){var i=this.initData.item[e],a=Object.assign({},u);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.billqty=i.quantity,a.price=i.price,a.amount=i.amount,a.quantity=i.quantity,a.taxprice=i.taxprice,a.taxamount=i.taxamount,a.itemtaxrate=i.itemtaxrate,a.taxtotal=i.taxtotal,a.finishuid=this.initData.refno,a.finishitemid=i.id,a.finishtype=this.initData.billtype,a.orderuid=i.orderuid,a.orderitemid=i.orderitemid,a.machgroupid=i.machgroupid,a.machitemid=i.machitemid,a.machuid=i.machuid,a.customer=i.customer,a.custpo=i.custpo,a.virtualitem=i.virtualitem,this.formdata.item.push(a)}}else"D08M04B1PN"==t&&(this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】支出计划转入",this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.suppliers,this.formdata.groupuid=this.initData.groupuid,this.formdata.linkman=this.initData.linkman,this.formdata.telephone=this.initData.telephone,this.formdata.billtype="其他应付",this.formdata.invocode=this.initData.invocode,this.formdata.aimdate=this.initData.aimdate,this.formdata.taxamount=this.initData.taxamount,this.formdata.receipted=this.initData.finishamt,this.formdata.item=[])}}},z=K,A=(i("0377"),Object(v["a"])(z,a,o,!1,null,"c851054e",null));e["default"]=A.exports},cc06:function(t,e,i){"use strict";i("3218")},cdcd:function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r}));var a={formcode:"D03M03B1Th",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Buy_Finishing.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"billtaxamount",itemname:"含税金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billtaxamount"},{itemcode:"billamount",itemname:"未税金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_Finishing.billamount"},{itemcode:"billtaxtotal",itemname:"税额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billtaxtotal"},{itemcode:"arrivaladd",itemname:"收货地址",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Finishing.arrivaladd"},{itemcode:"transport",itemname:"物流方式",minwidth:"100",displaymark:1,overflow:1,datasheet:"Buy_Finishing.transport"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_Finishing.status"},{itemcode:"itemcount",itemname:"款数",minwidth:"60",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.itemcount"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.assessor"}]},o={formcode:"D03M03B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Buy_Finishing.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"orderno",itemname:"订单编号",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_FinishingItem.orderno"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.taxamount"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.quantity"},{itemcode:"finishqty",itemname:"已入库",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"50",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.status"}]},s={formcode:"D03M03B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"60",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"70",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"60",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"finishqty",itemname:"已出入库数",minwidth:"80",displaymark:1,overflow:1},{itemcode:"orderno",itemname:"订单编号",minwidth:"100",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"150",displaymark:1,overflow:1,editmark:1}]},r={formcode:"D03M03B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Buy_Finishing.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_Finishing.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_Finishing.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"orderno",itemname:"订单编号",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Buy_FinishingItem.orderno"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.taxamount"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.quantity"},{itemcode:"finishqty",itemname:"已入库",minwidth:"80",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.finishqty"},{itemcode:"compcost",itemname:"结余",minwidth:"50",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.compcost"},{itemcode:"status",itemname:"状态",minwidth:"60",displaymark:1,overflow:1,datasheet:"Buy_FinishingItem.status"}]}},d3ff:function(t,e,i){"use strict";i("bc19")},d51e:function(t,e,i){"use strict";i("0b0b")},d56d:function(t,e,i){},d8b2:function(t,e,i){"use strict";i("4d45")},d924:function(t,e,i){},d93a:function(t,e,i){"use strict";i("6e4b")},da86:function(t,e,i){"use strict";i.d(e,"d",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"b",(function(){return s})),i.d(e,"a",(function(){return r}));var a={formcode:"D03M05B1Th",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Bus_Invoice.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtitle"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Invoice.billdate"},{itemcode:"groupuid",itemname:"供应商编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupuid"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"invocode",itemname:"发票编码",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.invocode"},{itemcode:"aimdate",itemname:"付款计划",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Invoice.aimdate"},{itemcode:"taxrate",itemname:"税率%",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_Invoice.taxrate"},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.taxamount"},{itemcode:"paid",itemname:"已付金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.paid"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.status"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.assessor"}]},o={formcode:"D03M05B1List",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Bus_Invoice.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"finishuid",itemname:"收货单号",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_InvoiceItem.finishuid"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_InvoiceItem.quantity"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.taxamount"}]},s={formcode:"D03M05B1Item",item:[{itemcode:"finishuid",itemname:"收货单号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsname",itemname:"货品名称",minwidth:"100",displaymark:1,overflow:1},{itemcode:"billqty",itemname:"单据数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"本次数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"price",itemname:"未税单价",minwidth:"70",displaymark:1,overflow:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",displaymark:1,overflow:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"150",displaymark:1,overflow:1}]},r={formcode:"D03M05B1Cite",item:[{itemcode:"refno",itemname:"单据编号",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Bus_Invoice.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Bus_Invoice.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_Invoice.billdate"},{itemcode:"groupname",itemname:"供应商",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"finishuid",itemname:"收货单号",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_InvoiceItem.finishuid"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Bus_InvoiceItem.quantity"},{itemcode:"taxamount",itemname:"金额",minwidth:"80",displaymark:1,sortable:1,overflow:1,datasheet:"Bus_InvoiceItem.taxamount"}]}},dea7:function(t,e,i){},e149:function(t,e,i){},ebf1:function(t,e,i){},ed33:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1),i("BillState",{attrs:{formdata:t.formdata,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getSuppGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D03M03B1Edit",commonurl:"/D03M03B1/printBill",weburl:"/D03M03B1/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D03M03B1Edit",examineurl:"/D03M03B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},["D04M01B1"==t.processModel?i("D04M01B1",{ref:"D04M01B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M03B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e(),"D03M05B1"==t.processModel?i("D03M05B1",{ref:"D03M05B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D03M03B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e()],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D04M01B1"==t.processModel?i("D04M01B1List",{ref:"D04M01B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e(),"D03M05B1"==t.processModel?i("D03M05B1List",{ref:"D03M05B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},o=[],s=(i("b64b"),i("b775"));const r={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M03B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);s["a"].post("/D03M03B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){s["a"].get("/D03M03B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,s["a"].get("/D03M03B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D03M03B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D03M03B1/closed?type="+(3==t?1:0);s["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var n=r,l=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","operator","arrivaladd","transport","summary","billtaxamount","billtaxtotal","billamount","custom1","custom3","custom2","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],d=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","partid","itemcode","itemname","itemspec","itemunit","quantity","taxprice","taxamount","taxtotal","itemtaxrate","price","amount","remark","orderno","orderuid","orderitemid","closed","rownum","invoclosed","virtualitem","customer","custpo","location","batchno","machuid","machitemid","machgroupid","mainplanuid","mainplanitemid","mrpuid","mrpitemid","disannulmark","attributejson","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],m={params:l,paramsItem:d},c={amount:0,attributejson:"",batchno:"",closed:0,custom1:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",customer:"",custpo:"",deliqty:0,disannuldate:new Date,disannullister:"",disannullisterid:"",disannulmark:0,finishqty:0,goodscustom1:"",goodscustom10:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodsid:"",goodsmaterial:"",goodsname:"",goodsphoto1:"",goodsspec:"",goodsuid:"",goodsunit:"",id:"",inspectid:"",inspectuid:"",intQtyMark:0,invoclosed:0,invoqty:0,itemcode:"",itemname:"",itemspec:"",itemtaxrate:0,itemunit:"",location:"",machgroupid:"",machitemid:"",machuid:"",mainplanitemid:"",mainplanuid:"",mrpitemid:"",mrpuid:"",orderitemid:"",orderno:"",orderuid:"",partid:"",passedqty:0,pid:"",price:0,quantity:0,remark:"",revision:0,rownum:0,sourcetype:0,statecode:"",statedate:new Date,taxamount:0,taxprice:0,taxtotal:0,virtualitem:0},u=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"OA审批",icon:"el-icon-s-check",disabled:"this.formstate!=1",methods:"flowable",param:"",children:[]},{show:1,divided:!0,label:"出入库单",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D04M01B1",children:[]},{show:1,divided:!1,label:"采购开票",icon:"el-icon-plus",disabled:"this.formstate!=2",methods:"operateBill",param:"D03M05B1",children:[]}],f=[{show:1,divided:!1,label:"出入库单",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D04M01B1",children:[]},{show:1,divided:!1,label:"采购开票",icon:"",disabled:"this.formstate==0",methods:"processBill",param:"D03M05B1",children:[]}],p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请选择单据类型",size:"small"},on:{change:function(e){t.formdata.item=[]}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("el-option",{attrs:{label:"采购验收",value:"采购验收"}}),i("el-option",{attrs:{label:"采购退货",value:"采购退货"}})],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据主题"}},[i("el-input",{attrs:{placeholder:"请输入单据主题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"供应厂商",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B2/getOnlinePageList",type:"供应商"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("arrivaladd")}}},[i("el-form-item",{attrs:{label:"收货地址"}},[i("el-input",{attrs:{placeholder:"请输入收货地址",clearable:"",size:"small"},model:{value:t.formdata.arrivaladd,callback:function(e){t.$set(t.formdata,"arrivaladd",e)},expression:"formdata.arrivaladd"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("transport")}}},[i("el-form-item",{attrs:{label:"物流方式"}},[i("el-input",{attrs:{placeholder:"请输入物流方式",clearable:"",size:"small"},model:{value:t.formdata.transport,callback:function(e){t.$set(t.formdata,"transport",e)},expression:"formdata.transport"}})],1)],1)])],1)],1)},h=[],g={props:{formdata:{type:Object},title:{type:String}},data:function(){return{formRules:{groupname:[{required:!0,trigger:"blur",message:"供应商为必填项"}]}}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},b=g,y=(i("8955"),i("2877")),v=Object(y["a"])(b,p,h,!1,null,"8fa2c892",null),w=v.exports,x=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","dummy","moveup","movedown","delete","copyrow","refresh","billstate"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection,dummyurl:"/D91M01B1/getVirOnlinePageList"},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,selDummy:t.selDummy,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1),t.selbuyordervisible?i("el-dialog",{attrs:{title:"采购订单","append-to-body":!0,visible:t.selbuyordervisible,width:"1000px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selbuyordervisible=e}}},[i("SelBuyOrder",{ref:"selBuyOrder",attrs:{multi:t.multi,selecturl:t.selecturl,groupid:t.formdata.groupid}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selBuyOrder()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selbuyordervisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},k=[],_=i("c7eb"),D=i("1da1"),S=i("ade3"),O=(i("c740"),i("caad"),i("d81d"),i("e9c4"),i("a9e3"),i("d3b7"),i("ac1f"),i("2532"),i("5319"),i("c7cd"),i("159b"),i("da92")),$=i("cdcd"),B=i("65e3"),q={name:"Elitem",components:{SelBuyOrder:B["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return Object(S["a"])(Object(S["a"])(Object(S["a"])({lst:[],multi:0,keynum:0,selbuyordervisible:!1,setColumsVisible:!1,selecturl:"",tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:$["b"],customList:[],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate"],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;if(t.editmarkfiles.includes(a.field)){t.countfiles.includes(a.field)&&t.changeInput("",i,a.field);var o=t.customList.findIndex((function(t){return t.attrkey==a.field}));-1!=o&&t.setAttributeJson(i,i.rownum)}}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}}},"footerData",[]),"cellAutofillOption",{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],s=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));if(-1!=s){t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[s],Object.keys(o)[1]);var r=t.customList.findIndex((function(t){return t.attrkey==Object.keys(o)[1]}));-1!=r&&t.setAttributeJson(t.lst[s],s)}}}}),"clipboardOption",{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}})},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var s=JSON.parse(t[i].attributejson);for(a=0;a<s.length;a++)t[i][s[a].key]=s[a].value}}this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(D["a"])(Object(_["a"])().mark((function e(){var i;return Object(_["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=$["b"],t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn($["b"].formcode,i,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,operationColumn:!!t.operationmark,renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex),r="",n=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));if(-1!=n){var l=e.customList[n].valuejson?e.customList[n].valuejson.split(","):[];return r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]])]),a("div",{directives:[{name:"show",value:l.length}]},[a("div",{style:"width:0;height:0"},[a("el-select",{class:"wpselect",style:"width:0;height:0;opacity: 0;",attrs:{size:"small",id:t.itemcode+o.rownum+$["b"].formcode},on:{change:function(i){o[t.itemcode]=i,e.setAttributeJson(o,o.rownum)}},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}},[l.map((function(t,e){return a("el-option",{key:e,attrs:{label:t,value:t},style:"max-width:200px"})}))])]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-arrow-down",style:"font-weight:bold;font-size:14px;",on:{click:function(){document.getElementById(t.itemcode+o.rownum+$["b"].formcode).click()}}})])]),r}return"goodsuid"==t.itemcode?(r=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}}),r):"plandate"==t.itemcode?(r=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[e.$options.filters.dateFormat(o[t.itemcode])])]),a("div",[a("div",{style:"opacity: 0;width:0;height:0"},[a("el-date-picker",{style:"width:0;height:0",attrs:{size:"small",id:t.itemcode+s+$["b"].formcode,value:new Date(o[t.itemcode]),type:"date",format:"MM-dd","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},on:{input:function(e){o[t.itemcode]=e}}})]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-date",on:{click:function(){document.getElementById(t.itemcode+s+$["b"].formcode).focus()}}})])]),r):(r=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]),r)}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["goodsuid","amount","taxamount"]),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var s=t[o],r=0;r<i.length;r++){var n=i[r];this.lst[e+o][n]=s[n].replace(/^\s*|\s*$/g,""),this.countfiles.includes(n)&&this.changeInput("",this.lst[e+o],n);var l=this.customList.findIndex((function(t){return t.attrkey==n}));-1!=l&&this.setAttributeJson(this.lst[e+o],e+o)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],s={key:o.attrkey,value:t[o.attrkey]?t[o.attrkey].replace(/\s*/g,""):""};""!=s.value&&i.push(s)}0==i.length?this.lst[e].attributejson="":this.lst[e].attributejson=JSON.stringify(i),this.$forceUpdate()},changeInput:function(t,e,i){"itemtaxrate"==i&&(this.nowitemtaxrate=e.itemtaxrate),e=this.$countInput(e,i),this.getSummary()},getAdd:function(t){this.formdata.groupid?("采购验收"==this.formdata.billtype?this.selecturl="/D03M02B1/getOnlineFinishPageList?groupid="+this.formdata.groupid+"&appl=1":this.selecturl="/D03M02B1/getRetrunFinishPageList?groupid="+this.formdata.groupid+"&appl=1",this.selbuyordervisible=!0,this.multi=t):this.$message.warning("请选择供应商")},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"closed",0),this.$set(e,"finishqty",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},selBuyOrder:function(){var t=this.$refs.selBuyOrder.$refs.selectVal.selection;if(0!=t.length){this.selbuyordervisible=!1;for(var e=0;e<t.length;e++){var i=t[e];i.itemtaxrate&&(this.nowitemtaxrate=t[e].itemtaxrate);var a=Object.assign({},c);a.goodsid=i.goodsid,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.amount=i.amount,a.attributejson=i.attributejson,a.batchno=i.batchno,a.itemtaxrate=i.itemtaxrate,a.orderitemid=i.id,a.orderuid=i.refno,a.orderno=i.refno,a.price=i.price,a.quantity=O["a"].minus(i.quantity,i.finishqty),a.taxamount=i.taxamount,a.taxprice=i.taxprice,a.taxtotal=i.taxtotal,0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selDummy:function(t){var e=t;if(0!=e.length){this.$refs.itemGroupBtns.DummyVisible=!1;for(var i=0;i<e.length;i++){var a=e[i],o=Object.assign({},c);o.goodsid=a.id,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.goodscustom1=a.custom1,o.goodscustom2=a.custom2,o.goodscustom3=a.custom3,o.goodscustom4=a.custom4,o.goodscustom5=a.custom5,o.goodscustom6=a.custom6,o.goodscustom7=a.custom7,o.goodscustom8=a.custom8,o.goodscustom9=a.custom9,o.goodscustom10=a.custom10,o.price=a.inprice?a.inprice:0,o.itemtaxrate=a.taxrate?a.taxrate:0,o.quantity=1,o.virtualitem=1,0!=this.idx&&(o.pid=this.idx),this.changeInput("",o,"quantity"),this.lst.push(o)}}else this.$message.warning("请选择虚拟品内容")}}},P=q,C=(i("cc06"),Object(y["a"])(P,x,k,!1,null,"5fc99a69",null)),M=C.exports,F=i("1f11"),I=i("dcb4"),j=i("ca99"),E=i("13df"),R=i("27f6"),T=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},L=[],N=(i("3ca3"),i("ddb0"),i("da86")),V=i("b893"),H={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"ca99"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:N["a"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={finishuid:this.searchVal};if(this.queryParams.SearchPojo=e,this.online)var i="/D03M05B1/getOnlinePageList";else i="/D03M05B1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(V["d"])()[0],EndDate:Object(V["d"])()[1]}),s["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++){var a=t.lst[i];a.compcost=a.quantity-a.buyqty;for(var o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(D["a"])(Object(_["a"])().mark((function e(){return Object(_["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(N["a"].formcode,N["a"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=a("goodsInfo",{attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}});return s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","amount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},K=H,z=(i("d93a"),Object(y["a"])(K,T,L,!1,null,"0fc3ac8a",null)),A=z.exports,G={name:"Formedit",components:{FormTemp:I["a"],EditHeader:w,EditItem:M,D03M05B1:j["default"],D04M01B1:E["default"],D04M01B1List:R["a"],D03M05B1List:A},props:["idx","isDialog","initData","billcode","isprocessDialog"],data:function(){return{title:"采购验收",operateBar:u,processBar:f,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,arrivaladd:"",assessdate:new Date,assessor:"",billamount:0,billdate:new Date,billpaid:0,billstatecode:"",billstatedate:new Date,billtaxamount:0,billtaxtotal:0,billtitle:"",billtype:"采购验收",disannulmark:0,groupid:"",groupname:"",item:[],operator:"",refno:"",summary:"",transport:""},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:F["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){var t=this;this.$request.get("/SaFormCustom/getEntityByCode?key=D03M03B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):F["a"],t.formtemplate.footer.type||(t.formtemplate.footer=F["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx?s["a"].get("/D03M03B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.$refs.elitem.multipleSelection=[],t.$refs.elitem.checkboxOption.selectedRowKeys=[],t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")})):this.formdata.item=[]},setGroupRow:function(t){this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.item=[]},autoClear:function(){this.formdata.groupname="",this.formdata.groupid=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst;var i=this.$amountFloat(this.formdata.item);this.formdata.billamount=i.billamount,this.formdata.billtaxamount=i.billtaxamount,this.formdata.billtaxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(m,a,this.formdata),0==this.idx?n.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.formdata=e.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):n.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.formdata=e.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(t)})).catch((function(){}))},approval:function(){n.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?n.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){console.log(t,"vallll"),this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t;var e=this.$refs.elitem.multipleSelection;"D04M01B1"!==t&&"D03M05B1"!==t||e.length>0&&(this.formdata.item=e)},processBill:function(t){this.processVisible=!0,this.processTitle="D04M01B1"==t?"出入库单":"采购开票",this.processModel=t},billSwitch:function(t){console.log(this.initData);var e=this.$store.getters.userinfo.configs;if("D03M02B1"==t){this.formdata.billtitle=this.initData.billtitle?this.initData.billtitle:"【"+this.initData.refno+"】采购订单转入",this.formdata.abbreviate=this.initData.abbreviate,this.formdata.groupid=this.initData.groupid,this.formdata.groupname=this.initData.groupname,this.formdata.groupuid=this.initData.groupuid,this.formdata.arrivaladd=this.initData.arrivaladd,this.formdata.transport=this.initData.transport,this.formdata.amount=this.initData.amount,this.formdata.item=[];for(var i=0;i<this.initData.item.length;i++){var a=this.initData.item[i],o=Object.assign({},c);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.machgroupid=a.machgroupid,o.machitemid=a.machitemid,o.machuid=a.machuid,o.mrpitemid=a.mrpitemid,o.mrpuid=a.mrpuid,o.mainplanitemid=a.mainplanitemid,o.mainplanuid=a.mainplanuid,o.quantity=this.$fomatFloat(a.quantity-a.finishqty,2),o.price=a.price,o.amount=a.amount,o.taxprice=a.taxprice,o.taxamount=a.taxamount,o.taxtotal=a.taxtotal,o.itemtaxrate=a.itemtaxrate,o.customer=a.customer,o.custpo=a.custpo,o.orderitemid=a.id,o.orderno=this.initData.refno,o.orderuid=this.initData.refno,o.attributejson=a.attributejson,o.virtualitem=a.virtualitem,o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):(this.$refs.elitem.changeInput("",o,"quantity"),this.formdata.item.push(o))}}else if("D03M02B1List"==t){this.formdata.billtitle=this.initData[0].billtitle?this.initData[0].billtitle:"【"+this.initData[0].refno+"】采购订单转入",this.formdata.abbreviate=this.initData[0].abbreviate,this.formdata.groupid=this.initData[0].groupid,this.formdata.groupname=this.initData[0].groupname,this.formdata.groupuid=this.initData[0].groupuid,this.formdata.arrivaladd=this.initData[0].arrivaladd,this.formdata.transport=this.initData[0].transport,this.formdata.item=[];for(i=0;i<this.initData.length;i++){a=this.initData[i],o=Object.assign({},c);o.goodsid=a.goodsid,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.machgroupid=a.machgroupid,o.machitemid=a.machitemid,o.machuid=a.machuid,o.mrpitemid=a.mrpitemid,o.mrpuid=a.mrpuid,o.mainplanitemid=a.mainplanitemid,o.mainplanuid=a.mainplanuid,o.quantity=this.$fomatFloat(a.quantity-a.finishqty,2),o.price=a.price,o.amount=a.amount,o.taxprice=a.taxprice,o.taxamount=a.taxamount,o.taxtotal=a.taxtotal,o.itemtaxrate=a.itemtaxrate,o.customer=a.customer,o.custpo=a.custpo,o.orderitemid=a.id,o.orderno=a.refno,o.orderuid=a.refno,o.attributejson=a.attributejson,o.virtualitem=a.virtualitem,o.quantity<=0?(o.quantity=0,o.amount=0,o.taxamount=0,o.taxtotal=0,e&&e["system.bill.execfinishow"]&&JSON.parse(e["system.bill.execfinishow"])&&this.formdata.item.push(o)):(this.$refs.elitem.changeInput("",o,"quantity"),this.formdata.item.push(o))}}}}},J=G,W=(i("be8e"),Object(y["a"])(J,a,o,!1,null,"7255f33c",null));e["default"]=W.exports},f3ef:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize,pageSizes:[10,20,100,500]},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},o=[],s=i("c7eb"),r=i("1da1"),n=(i("e9c4"),i("a9e3"),i("b64b"),i("d3b7"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),i("b775")),l=i("9368"),d=i("b893"),m={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"9fee"))}},props:["online","searchVal","isDialog","billcode"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:l["a"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.bindData(),this.getColumn()},methods:{bindData:function(){var t=this;if(this.isDialog){var e={citeuid:this.searchVal};if("D01M03B1"==this.billcode)e={machuid:this.searchVal};this.queryParams.SearchType=0,this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e}this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(d["d"])()[0],EndDate:Object(d["d"])()[1]});var i="/D03M02B1/getPageList";n["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++){var a=t.lst[i];a.compcost=a.quantity-a.buyqty;for(var o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}}t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(t.tableForm.formcode,l["a"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=a("goodsInfo",{attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}});return s}if("status"==t.itemcode){s="";return 0!=o.finishqty&&o.finishqty<o.quantity?s=a("span",{class:"textborder-blue"},["收货"]):o.finishqty>=o.quantity&&0!=o.finishqty?s=a("span",{class:"textborder-green"},["完成"]):o.disannulmark>1?s=a("span",{class:"textborder-grey"},["撤销"]):o.closed&&(s=a("span",{class:"textborder-grey"},["中止"])),s}if("compcost"==t.itemcode){s="";return s=o.quantity-o.finishqty<0?a("span",{style:"color:#F56C6C"},[o.quantity-o.finishqty]):o.quantity-o.finishqty==0?a("span",{style:"color:#67C23A"},[o.quantity-o.finishqty]):o.quantity-o.finishqty==o.quantity?a("span",[o.quantity-o.finishqty]):a("span",{style:"color:#409EFF"},[o.quantity-o.finishqty]),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","compcost","finishqty"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},c=m,u=(i("d3ff"),i("2877")),f=Object(u["a"])(c,a,o,!1,null,"a9a67dec",null);e["a"]=f.exports},f550:function(t,e,i){"use strict";i("367a")},fa68:function(t,e,i){"use strict";i("b224")},fb056:function(t,e,i){"use strict";i("6ccf")},fc38:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton(0,1)},clickMethods:t.clickMethods}})],1),i("BillState",{attrs:{formdata:t.formdata,amtstatusfinish:t.formdata.lastamt+t.formdata.firstamt>=t.formdata.billtaxamount&&t.formdata.billtaxamount>0,amtstatusing:t.formdata.lastamt+t.formdata.firstamt<t.formdata.billtaxamount&&t.formdata.lastamt+t.formdata.firstamt>0,finished:t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount,revoke:t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount,approve:!!t.formdata.assessor,approving:!t.formdata.assessor&&!!t.formdata.oaflowmark}})],1),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"D01M03B1Edit",commonurl:"/D01M03B1/printBill",weburl:"/D01M03B1/printWebBill"}}),i("PrintServer",{ref:"PrintServerItem",attrs:{printTitle:"打印明细模板",formdata:t.formdata,selectList:t.printitemlist,printcode:"D01M03B1EditPtItem",commonurl:"/D01M03B1/printItem",weburl:"/D01M03B1/printWebItem"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"D01M03B1Edit",examineurl:"/D01M03B1/sendapprovel"},on:{bindData:t.bindData}}),t.approvevisible?i("el-dialog",{attrs:{title:"审批记录",width:"70vw","append-to-body":!0,visible:t.approvevisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.approvevisible=e}}},[i("selApprove",{ref:"selApprove",attrs:{idx:t.idx}})],1):t._e(),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},["D01M08B1DEP"==t.processModel?i("D01M08B1DEP",{ref:"D01M08B1DEP",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D01M03B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e(),"D01M06B1"==t.processModel?i("D01M06B1",{ref:"D01M06B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D01M03B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e(),"D03M02B1"==t.processModel?i("D03M02B1",{ref:"D03M02B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D01M03B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e(),"D04M08B1"==t.processModel?i("D04M08B1",{ref:"D04M08B1",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D01M03B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e(),"D04M08B1Mat"==t.processModel?i("D04M08B1",{ref:"D04M08B1Mat",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D01M03B1Mat"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1,t.bindData()}}}):t._e()],1):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"86vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D01M08B1DEP"==t.processModel?i("D01M08B1DEPList",{ref:"D01M08B1DEPList",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):"D01M06B1"==t.processModel?i("D01M06B1List",{ref:"D01M06B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):"D03M02B1"==t.processModel?i("D03M02B1List",{ref:"D03M02B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):"D04M08B1"==t.processModel?i("D04M08B1List",{ref:"D04M08B1List",attrs:{searchVal:t.formdata.refno,isDialog:!0,billcode:"D01M03B1"}}):t._e()],1):t._e(),i("el-dialog",{staticClass:"warnDialog",attrs:{title:"提示",visible:t.stockwarningvisible,width:"50vw","close-on-press-escape":!1,"close-on-click-modal":!1,"append-to-body":!0},on:{"update:visible":function(e){t.stockwarningvisible=e}}},[i("div",[i("div",{staticStyle:{display:"flex","align-items":"center",margin:"10px","justify-content":"space-between"}},[i("div",[i("i",{staticClass:"el-icon-warning",staticStyle:{"font-size":"24px",color:"#e6a23c",float:"left","margin-top":"-2px"}}),i("span",{staticStyle:{"margin-left":"10px","font-size":"15px","line-height":"20px"}},[t._v(" 物料可用数量不足，是否继续审核？")])]),i("div",{staticStyle:{"font-size":"18px"}},[t._v(" 订单号： "),i("span",[t._v(t._s(t.formdata.refno))])])]),i("el-table",{staticStyle:{width:"100%","min-height":"240px"},attrs:{data:t.stockwarndata,"header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px"},"cell-style":{padding:"4px 0px"},border:"","highlight-current-row":""}},[i("el-table-column",{attrs:{align:"center",label:"ID",width:"50px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),i("el-table-column",{attrs:{prop:"goodsuid",label:"编码",align:"center"}}),i("el-table-column",{attrs:{prop:"goodsname",label:"名称",align:"center"}}),i("el-table-column",{attrs:{prop:"goodsspec",label:"规格",align:"center"}}),i("el-table-column",{attrs:{prop:"quantity",label:"账面库存",align:"center"}}),i("el-table-column",{attrs:{prop:"stoqty",label:"可用数量",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",{staticStyle:{color:"red"}},[t._v(t._s(e.row.stoqty))])]}}])}),i("el-table-column",{attrs:{prop:"buyremqty",label:"采购待入",align:"center"}}),i("el-table-column",{attrs:{prop:"busremqty",label:"订单待用",align:"center"}}),i("el-table-column",{attrs:{prop:"requremqty",label:"领料待出",align:"center"}})],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.stockwarningvisible=!1}}},[t._v("取 消")]),i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.approvalRequest(t.formdata.id)}}},[t._v("确 定")])],1)]),t.historybillvisible?i("el-dialog",{attrs:{title:"历史单据信息","append-to-body":!0,visible:t.historybillvisible,width:"1200px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.historybillvisible=e}}},[i("historyBill",{ref:"historyBill",attrs:{multi:0,selecturl:"/D01M03B1/getBillList"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.submitHistory()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.historybillvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),i("el-dialog",{staticStyle:{height:"740px"},attrs:{title:"单据编码设置",visible:t.billRefnoDialogVisible,width:"800px","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1,top:"10vh"},on:{"update:visible":function(e){t.billRefnoDialogVisible=e}}},[i("div",{staticClass:"billRefnoBox"},[t.billRefnoDialogVisible?i("BillRefnoEdit",{ref:"BillRefnoEditRef",attrs:{billcode:"D01M03B1",billname:"销售订单",tablename:"Bus_Machining",idx:0},on:{closeBillRefno:t.closeBillRefno}}):t._e()],1),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"small"},on:{click:function(e){t.billRefnoDialogVisible=!1,t.$refs.BillRefnoEditRef.initFormData()}}},[t._v("取 消")]),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitBillRefno()}}},[t._v("确 定")])],1)])],1)},o=[],s=i("2909"),r=i("c7eb"),n=i("1da1"),l=(i("e9c4"),i("b64b"),i("d3b7"),i("ac1f"),i("6062"),i("3ca3"),i("5319"),i("159b"),i("ddb0"),i("b775"));const d={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);l["a"].post("/D01M03B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);l["a"].post("/D01M03B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){l["a"].get("/D01M03B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,l["a"].get("/D01M03B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningvisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/D01M03B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/D01M03B1/closed?type="+(3==t?1:0);l["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var m=d,c=["id","refno","billtype","billtitle","billdate","groupid","groupuid","groupname","abbreviate","grouplevel","custorderid","logisticsmode","logisticsport","country","salesman","salesmanid","taxrate","summary","billtaxamount","billtaxtotal","billamount","billplandate","groupcode","payment","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],u=["id","pid","goodsid","goodsuid","goodsname","goodsspec","goodsunit","goodsmaterial","goodsphoto1","partid","quantity","taxprice","taxamount","itemtaxrate","taxtotal","price","amount","itemorgdate","itemplandate","wkqty","stoqty","rownum","remark","outsecqty","editioninfo","virtualitem","closed","stdprice","stdamount","rebate","maxqty","location","batchno","disannulmark","ordercostuid","ordercostitemid","quotuid","quotitemid","bomtype","bomid","bomuid","bomstate","attributejson","machtype","reordermark","matcode","matid","costitemjson","costgroupjson","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10","custom11","custom12","custom13","custom14","custom15","custom16","custom17","custom18"],f={params:c,paramsItem:u},p={goodsid:"",goodsuid:"",goodsname:"",goodsunit:"",goodsspec:"",partid:"",goodscustom1:"",goodscustom2:"",goodscustom3:"",goodscustom4:"",goodscustom5:"",goodscustom6:"",goodscustom7:"",goodscustom8:"",goodscustom9:"",goodscustom10:"",pid:"",quantity:0,taxprice:0,taxamount:0,itemtaxrate:0,taxtotal:0,price:0,amount:0,itemorgdate:new Date,itemplandate:new Date,wkqty:0,stoqty:0,rownum:0,remark:"",engstatetext:"",engstatedate:new Date,wkstatetext:"",wkstatedate:"",busstatetext:"",busstatedate:new Date,buyquantity:"",wkquantity:0,inquantity:0,pickqty:0,finishqty:0,outquantity:0,outsecqty:0,editioninfo:"",itemcompdate:new Date,virtualitem:0,closed:0,stdprice:0,stdamount:0,rebate:0,mrpuid:"",mrpid:"",maxqty:0,location:"",batchno:"",disannulmark:0,wipused:0,wkwpid:"",wkwpcode:"",wkwpname:"",wkrownum:"",ordercostuid:"",ordercostitemid:"",quotuid:"",quotitemid:"",bomtype:0,bomid:"",bomuid:"",bomstate:"",attributejson:"",machtype:"",reordermark:0,matcode:"",matused:0,matid:"",costitemjson:"",costgroupjson:"",matcostamt:0,laborcostamt:0,directcostamt:0,indirectcostamt:0,sourcetype:0,custom1:"",custom2:"",custom3:"",custom4:"",custom5:"",custom6:"",custom7:"",custom8:"",custom9:"",custom10:"",custom11:"",custom12:"",custom13:"",custom14:"",custom15:"",custom16:"",custom17:"",custom18:""},h=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]},{show:1,divided:!1,label:"引入历史单据",icon:"el-icon-plus",disabled:"this.formstate!=0",methods:"openHistoryBill",param:"",children:[]},{show:1,divided:!1,label:"打印明细",icon:"",disabled:"this.formstate==0",methods:"printItemButton",param:"",children:[]},{show:1,divided:!0,label:"订单预收",icon:"",disabled:"this.formstate!=2",methods:"operateBill",param:"D01M08B1DEP",children:[]},{show:1,divided:!1,label:"订单发货",icon:"",disabled:"this.formstate!=2",methods:"operateBill",param:"D01M06B1",children:[]},{show:1,divided:!1,label:"采购订单",icon:"",disabled:"this.formstate!=2",methods:"operateBill",param:"D03M02B1",children:[]},{show:1,divided:!1,label:"领料单",icon:"",disabled:"this.formstate!=2",methods:"operateBill",param:"D04M08B1",children:[]},{show:1,divided:!1,label:"用户定义",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"单据编码",icon:"bianma",methods:"setBillRefno",param:1,children:[],disabled:""}]}],g=[{show:1,divided:!1,label:"订单预收",icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D01M08B1DEP",label:"订单预收"},children:[]},{show:1,divided:!1,label:"订单发货",icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D01M06B1",label:"订单发货"},children:[]},{show:1,divided:!1,label:"采购订单",icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D03M02B1",label:"采购订单"},children:[]},{show:1,divided:!1,label:"领料单",icon:"",disabled:"this.formstate==0",methods:"processBill",param:{code:"D04M08B1",label:"领料单"},children:[]}],b=i("70ef"),y=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[i("el-popover",{ref:"dictionaryRef",attrs:{placement:"bottom-start",trigger:"click"},on:{show:function(e){return t.$refs.selDictionaries.bindData()}}},[i("SelDict",{ref:"selDictionaries",staticStyle:{width:"200px"},attrs:{multi:0,baseparam:"/SaDict",billcode:"sale.machtype"},on:{singleSel:function(e){t.formdata.billtype=e.dictvalue,t.$refs["dictionaryRef"].doClose(),t.$forceUpdate()},closedic:function(e){return t.$refs["dictionaryRef"].doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"请选择单据类型",clearable:"",size:"small",readonly:""},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[i("i",{staticClass:"el-icon-circle-plus-outline",staticStyle:{"font-size":"15px",color:"rgb(64, 158, 255)"},attrs:{slot:"suffix"},slot:"suffix"})])],1)],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{attrs:{placeholder:"请输入单据",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("custorderid")}}},[i("el-form-item",{attrs:{label:"客户订单号"}},[i("el-input",{attrs:{placeholder:"请输入客户订单号",size:"small"},model:{value:t.formdata.custorderid,callback:function(e){t.$set(t.formdata,"custorderid",e)},expression:"formdata.custorderid"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"客户",prop:"groupname"}},[i("GroupAutoComplete",{attrs:{value:t.formdata.groupname,baseurl:"/D01M01B1/getOnlinePageList",type:"客户"},on:{setRow:function(e){t.$emit("getGroupName",e),t.cleValidate("groupname")},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billplandate")}}},[i("el-form-item",{attrs:{label:"计划时间",prop:"billplandate"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期",size:"small"},model:{value:t.formdata.billplandate,callback:function(e){t.$set(t.formdata,"billplandate",e)},expression:"formdata.billplandate"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{ref:"colRefs",on:{click:function(e){return t.cleValidate("logisticsmode")}}},[i("el-form-item",{attrs:{label:"交货方式",prop:"logisticsmode"}},[i("el-popover",{ref:"dictionaryRef",attrs:{placement:"bottom-start",trigger:"click",width:t.eleWidth},on:{show:function(e){return t.$refs.selDictionaries.bindData()}}},[i("SelDict",{ref:"selDictionaries",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"sale.logisticsmode",baseparam:"/SaDict"},on:{singleSel:function(e){t.formdata.logisticsmode=e.dictvalue,t.$refs["dictionaryRef"].doClose(),t.$forceUpdate()},closedic:function(e){return t.$refs["dictionaryRef"].doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"请选择交货方式",clearable:"",size:"small",readonly:""},model:{value:t.formdata.logisticsmode,callback:function(e){t.$set(t.formdata,"logisticsmode",e)},expression:"formdata.logisticsmode"}},[i("i",{staticClass:"el-icon-circle-plus-outline",staticStyle:{"font-size":"15px",color:"rgb(64, 158, 255)"},attrs:{slot:"suffix"},slot:"suffix"})])],1)],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"交货地址",prop:"logisticsport"}},[i("el-input",{attrs:{placeholder:"请输入交货地址",size:"small"},model:{value:t.formdata.logisticsport,callback:function(e){t.$set(t.formdata,"logisticsport",e)},expression:"formdata.logisticsport"}})],1)],1)],1)],1)},v=[],w={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{eleWidth:"",formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupname:[{required:!0,trigger:"blur",message:"客户为必填项"}],custorderid:[{required:!0,trigger:"blur",message:"客户加工单为必填项"}],billplandate:[{required:!0,trigger:"blur",message:" 计划时间为必填项"}]}}},mounted:function(){var t=this;window.addEventListener("resize",(function(){t.eleWidth=t.SelDict()})),this.eleWidth=this.SelDict()},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)},SelDict:function(){var t=this.$refs.colRefs;return"".concat(t.offsetWidth-100)}}},x=w,k=(i("b83f"),i("2877")),_=Object(k["a"])(x,y,v,!1,null,"7d79d350",null),D=_.exports,S=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","dummy","moveup","movedown","delete","copyrow","refresh","billstate"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection,dummyurl:"/D91M01B1/getVirOnlinePageList"},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,selDummy:t.selDummy,getColumn:t.getColumn},scopedSlots:t._u([{key:"center",fn:function(){return[i("el-button",{attrs:{disabled:!!t.formdata.assessor,type:"primary",size:"mini",icon:"el-icon-circle-plus-outline"},nativeOn:{click:function(e){return t.openQuickCode()}}},[t._v("快 码")]),i("el-dropdown",{attrs:{trigger:"click",placement:"bottom","hide-on-click":!1}},[i("el-button",{attrs:{size:"mini",type:"primary",disabled:!!t.formdata.assessor}},[t._v("批量操作"),i("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{disabled:0==t.multipleSelection.length},nativeOn:{click:function(e){t.deliverydatevisible=!0,t.dateType="itemorgdate"}}},[t._v("原始交期")]),i("el-dropdown-item",{attrs:{disabled:0==t.multipleSelection.length},nativeOn:{click:function(e){t.deliverydatevisible=!0,t.dateType="itemplandate"}}},[t._v("评审交期")])],1)],1)]},proxy:!0},{key:"right",fn:function(){return[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption,"cell-style-option":t.cellStyleOption}})],1),t.selgoodsvisible?i("el-dialog",{attrs:{title:"货品信息","append-to-body":!0,visible:t.selgoodsvisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.selgoodsvisible=e}}},[i("SelGoods",{ref:"selGoods",attrs:{multi:t.multi}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selGoods()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.selgoodsvisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.quickCodeVisible?i("el-dialog",{attrs:{title:"快码操作",visible:t.quickCodeVisible,width:"500px","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.quickCodeVisible=e}}},[i("el-form",{ref:"quickCodedata",staticClass:"custInfo",attrs:{model:t.quickCodedata,"auto-complete":"on",rules:t.formRule},nativeOn:{submit:function(t){t.preventDefault()}}},[i("el-row",[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"快码",prop:"fileoriname"}},[i("el-input",{ref:"quickcodeInput",staticStyle:{width:"100%"},attrs:{placeholder:"货品快码+sku快码+数量",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.submitQuickCode()}},model:{value:t.quickCodedata.quickcode,callback:function(e){t.$set(t.quickCodedata,"quickcode",e)},expression:"quickCodedata.quickcode"}})],1)],1)],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitQuickCode()}}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.quickCodeVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),i("el-dialog",{attrs:{title:"itemorgdate"==t.dateType?"原始交期":"评审交期","append-to-body":!0,width:"400px",visible:t.deliverydatevisible,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.deliverydatevisible=e}}},[i("el-form",{ref:"customTimeForm"},[i("el-form-item",{attrs:{label:""}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期"},model:{value:t.deliverydate,callback:function(e){t.deliverydate=e},expression:"deliverydate"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitTime}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.deliverydatevisible=!1}}},[t._v("取 消")])],1)],1),i("el-dialog",{staticStyle:{"min-height":"500px"},attrs:{visible:t.isShowHistory,width:"72vw","append-to-body":!0},on:{"update:visible":function(e){t.isShowHistory=e}}},[i("HistoryOrder",{ref:"historyOrder",attrs:{historyData:t.historyData},on:{getHistoryPrice:t.getHistoryPrice}})],1),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},O=[],$=i("ade3"),B=(i("99af"),i("c740"),i("caad"),i("d81d"),i("a9e3"),i("25f0"),i("2532"),i("4d90"),i("c7cd"),i("da92")),q=i("40d9"),P=i("9bda"),C=i("174a"),M=i("6465"),F={name:"Elitem",components:{SelGoods:P["a"],HistoryOrder:M["a"]},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return Object($["a"])(Object($["a"])(Object($["a"])(Object($["a"])(Object($["a"])(Object($["a"])(Object($["a"])(Object($["a"])({isShowHistory:!1,showHistory:!1,historyData:[],selgoodsvisible:!1,currentRowKey:"",lst:[],multi:0,keynum:0,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:q["c"],editmarkfiles:[],countfiles:["quantity","price","amount","taxprice","taxamount","itemtaxrate","stoqty"],customList:[],setColumsVisible:!1,quickCodeVisible:!1,quickCodedata:{quickcode:""},formRule:{quickcode:[{required:!0,trigger:"blur",message:"快码为必填项"}]},expression:null,priceExpression:null,stockInfo:{},goodsOk:0,goodsErr:[],deliverydate:"",deliverydatevisible:!1,dateType:"",columsData:[],columnHidden:[],footerData:[],totalfields:["goodsuid","amount","quantity","taxamount"],copyText:""},"setColumsVisible",!1),"cellStyleOption",{bodyCellClass:function(t){var e=t.row;t.column,t.rowIndex;if(e.finishqty>0)return e.finishqty==e.quantity?"table-body-cell-class-finish":"table-body-cell-class"}}),"checkboxOption",{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}}),"editOption",{afterCellValueChange:function(){var e=Object(n["a"])(Object(r["a"])().mark((function e(i){var a,o,s,n,l;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=i.row,o=i.column,i.changeValue,!t.editmarkfiles.includes(o.field)){e.next=14;break}return e.t0=Object,e.t1={},e.next=6,Object(C["a"])("D01M03B1",a,o.field);case 6:for(n in e.t2=e.sent,s=e.t0.assign.call(e.t0,e.t1,e.t2),a)a[n]=s[n];t.setAttributeJson(a,a.rownum),t.countfiles.includes(o.field)?t.changeInput("",a,o.field):Object(C["b"])()&&t.changeInput("",a,Object(C["b"])()),l=t.customList.findIndex((function(t){return t.attrkey==o.field})),-1!=l&&t.setAttributeJson(a,a.rownum),t.$forceUpdate();case 14:case"end":return e.stop()}}),e)})));function i(t){return e.apply(this,arguments)}return i}()}),"rowScroll",0),"virtualScrollOption",{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}}),"cellAutofillOption",{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData;if(2==t.formstate)return!1;var a=Object.keys(i[0])[1];return!!t.editmarkfiles.includes(a)&&void 0},afterAutofill:function(){var e=Object(n["a"])(Object(r["a"])().mark((function e(i){var a,o,s,n,l,d,m;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i.sourceSelectionData,a=i.targetSelectionData,o=0;case 2:if(!(o<a.length)){e.next=18;break}if(s=a[o],n=t.lst.findIndex((function(t){return t.rowKeys==s.rowKeys})),-1==n){e.next=15;break}return e.next=8,Object(C["a"])("D01M03B1",t.lst[n],Object.keys(s)[1]);case 8:for(d in l=e.sent,t.lst[n])t.lst[n][d]=l[d];t.setAttributeJson(t.lst[n],n),t.countfiles.includes(Object.keys(s)[1])?t.changeInput("",t.lst[n],Object.keys(s)[1]):Object(C["b"])()&&t.changeInput("",t.lst[n],Object(C["b"])()),m=t.customList.findIndex((function(t){return t.attrkey==Object.keys(s)[1]})),-1!=m&&t.setAttributeJson(t.lst[n],n),t.$forceUpdate();case 15:o++,e.next=2;break;case 18:case"end":return e.stop()}}),e)})));function i(t){return e.apply(this,arguments)}return i}()}),"clipboardOption",{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;t.copyText=i},beforePaste:function(){if(2==t.formstate)return!1},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}})},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++){if(t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i),0!=this.customList.length)for(var a=0;a<this.customList.length;a++){var o=this.customList[a];t[i][o.attrkey]||(t[i][o.attrkey]=o.defvalue?o.defvalue:"")}if(""==t[i].attributejson||null==t[i].attributejson);else{var s=JSON.parse(t[i].attributejson);for(a=0;a<s.length;a++)t[i][s[a].key]=s[a].value}}this.$forceUpdate(),this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this;return Object(n["a"])(Object(r["a"])().mark((function e(){var i;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=q["c"],t.formtemplateItem.type&&(i.item=t.formtemplateItem.content),t.$getColumn(q["c"].formcode,i,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(i,a){var o=i.row,s=(i.column,i.rowIndex,e.customList.findIndex((function(e){return t.itemcode==e.attrkey})));if("itemorgdate"==t.itemcode||"itemplandate"==t.itemcode){var l=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[e.monthFilter(o[t.itemcode])])]),a("div",[a("div",{style:"opacity: 0;width:0;height:0"},[a("el-date-picker",{style:"width:0;height:0",attrs:{size:"small",id:t.itemcode+o.rownum+q["c"].formcode,value:new Date(o[t.itemcode]),type:"date",format:"MM-dd","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},on:{input:function(e){o[t.itemcode]=e}}})]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-date",on:{click:function(){document.getElementById(t.itemcode+o.rownum+q["c"].formcode).focus()}}})])]);return l}if("price"==t.itemcode){l="";return l=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px",on:{mouseover:function(){return e.showHistory=!0},mouseout:function(){return e.showHistory=!1}}},[a("span",{class:o.disannulmark?"textlinethrough":"",style:"flex:1"},[o[t.itemcode]]),a("el-tooltip",{class:"item",attrs:{effect:"dark",content:"历史价格",placement:"top-start"}},[a("svg-icon",{attrs:{"icon-class":"historyline"},directives:[{name:"show",value:e.showHistory&&!e.formdata.assessor}],style:"cursor:pointer;",on:{click:function(){return e.getHistoyrOrder(o,o.goodsuid,e.formdata.groupname)}}})])]),l}if("goodsuid"==t.itemcode){l=a("GoodsInfo",{class:o.disannulmark?"textlinethrough":"",attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return l}if(-1!=s){var d=e.customList[s].valuejson?e.customList[s].valuejson.split(","):[];l=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]])]),a("div",{directives:[{name:"show",value:d.length}]},[a("div",{style:"width:0;height:0"},[a("el-select",{class:"wpselect",style:"width:0;height:0;opacity: 0;",attrs:{size:"small",id:t.itemcode+o.rownum+q["c"].formcode},on:{change:function(){var i=Object(n["a"])(Object(r["a"])().mark((function i(a){return Object(r["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:e.selSpuVal(o,t.itemcode,a);case 1:case"end":return i.stop()}}),i)})));return function(t){return i.apply(this,arguments)}}()},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}},[d.map((function(t,e){return a("el-option",{key:e,attrs:{label:t,value:t},style:"max-width:200px"})}))])]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-arrow-down",style:"font-weight:bold;font-size:14px;",on:{click:function(){document.getElementById(t.itemcode+o.rownum+q["c"].formcode).click()}}})])]);return l}if("status"==t.itemcode)return 0!=o.finishqty&&o.finishqty<o.quantity?l=a("span",{class:"textborder-blue"},["发货"]):o.finishqty==o.quantity&&0!=o.finishqty?l=a("span",{class:"textborder-green"},["完成"]):1==o.disannulmark?l=a("span",{class:"textborder-grey"},["撤销"]):o.closed&&(l=a("span",{class:"textborder-grey"},["中止"])),l;l=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]);return l}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1,this.$forceUpdate()},getSummary:function(){this.$getSummary(this,this.totalfields),this.formdata.billamount=this.footerData[0].amount,this.formdata.billtaxamount=this.footerData[0].taxamount,this.formdata.billtaxtotal=this.formdata.billtaxamount-this.formdata.billamount},pasteFun:function(t,e){var i=this;return Object(n["a"])(Object(r["a"])().mark((function a(){var o,s,n,l,d,m,c,u,f;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:for(s in o=[],t[0])o.push(s);n=0;case 3:if(!(n<t.length)){a.next=24;break}l=t[n],d=0;case 6:if(!(d<o.length)){a.next=21;break}return m=o[d],i.lst[e+n][m]=l[m].replace(/^\s*|\s*$/g,""),a.next=11,Object(C["a"])("D01M03B1",i.lst[e+n],Object.keys(l)[1]);case 11:for(u in c=a.sent,i.lst[e+n])i.lst[e+n][u]=c[u];i.setAttributeJson(i.lst[e+n],e+n),i.countfiles.includes(m)?i.changeInput("",i.lst[e+n],m):Object(C["b"])()&&i.changeInput("",i.lst[e+n],Object(C["b"])()),f=i.customList.findIndex((function(t){return t.attrkey==m})),-1!=f&&i.setAttributeJson(i.lst[e+n],e+n),i.$forceUpdate();case 18:d++,a.next=6;break;case 21:n++,a.next=3;break;case 24:case"end":return a.stop()}}),a)})))()},selSpuVal:function(t,e,i){var a=this;return Object(n["a"])(Object(r["a"])().mark((function o(){return Object(r["a"])().wrap((function(o){while(1)switch(o.prev=o.next){case 0:return t[e]=i,o.t0=a,o.next=4,Object(C["a"])("D01M03B1",t,e);case 4:o.t1=o.sent,o.t2=t.rownum,o.t0.setAttributeJson.call(o.t0,o.t1,o.t2),Object(C["b"])()&&a.changeInput("",t,Object(C["b"])());case 8:case"end":return o.stop()}}),o)})))()},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},setAttributeJson:function(t,e){for(var i=[],a=0;a<this.customList.length;a++){var o=this.customList[a],s={key:o.attrkey,value:t[o.attrkey]?String(t[o.attrkey]).replace(/\s*/g,""):""};""!=s.value&&i.push(s)}if(0==i.length)this.lst[e].attributejson="";else{this.lst[e].attributejson=JSON.stringify(i);for(a=0;a<i.length;a++)this.$set(this.lst[e],i[a].key,i[a].value)}this.$forceUpdate()},changeInput:function(t,e,i){if("stoqty"==i||"quantity"==i){if(Number(e.quantity)<Number(e.stoqty))return void this.$message.warning("库存发货不可以大于订单数量");e.wkqty=B["a"].minus(Number(e.quantity),e.stoqty),e.maxqty=e.quantity}e=this.$countInput(e,i),this.getSummary(),this.$forceUpdate()},getAdd:function(t){this.selgoodsvisible=!0,this.multi=t},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"matused",0),this.$set(e,"finishqty",0),this.$set(e,"outquantity",0),this.$set(e,"closed",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}},selGoods:function(){var t=this.$refs.selGoods.$refs.selectgoods.selection;if(0!=t.length){this.selgoodsvisible=!1;for(var e=0;e<t.length;e++){var i=t[e],a=(t[e].ivquantity,Object.assign({},p));a.goodsid=i.id,a.goodsuid=i.goodsuid,a.goodsname=i.goodsname,a.goodsunit=i.goodsunit,a.goodsspec=i.goodsspec,a.partid=i.partid,a.goodscustom1=i.custom1,a.goodscustom2=i.custom2,a.goodscustom3=i.custom3,a.goodscustom4=i.custom4,a.goodscustom5=i.custom5,a.goodscustom6=i.custom6,a.goodscustom7=i.custom7,a.goodscustom8=i.custom8,a.goodscustom9=i.custom9,a.goodscustom10=i.custom10,a.itemtaxrate=i.taxrate?i.taxrate:this.nowitemtaxrate,a.quantity=0,a.taxprice=i.outprice?i.outprice:0,a.wkqty=0,0!=this.idx&&(a.pid=this.idx);for(var o=0;o<this.customList.length;o++){i=this.customList[o];a[i.attrkey]=""}this.changeInput("",a,"taxprice"),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},selDummy:function(t){var e=t;if(0!=e.length){this.$refs.itemGroupBtns.DummyVisible=!1;for(var i=0;i<e.length;i++){var a=e[i],o=Object.assign({},p);o.goodsid=a.id,o.goodsuid=a.goodsuid,o.goodsname=a.goodsname,o.goodsunit=a.goodsunit,o.goodsspec=a.goodsspec,o.partid=a.partid,o.goodscustom1=a.custom1,o.goodscustom2=a.custom2,o.goodscustom3=a.custom3,o.goodscustom4=a.custom4,o.goodscustom5=a.custom5,o.goodscustom6=a.custom6,o.goodscustom7=a.custom7,o.goodscustom8=a.custom8,o.goodscustom9=a.custom9,o.goodscustom10=a.custom10,o.price=a.outprice,o.virtualitem=1,0!=this.idx&&(o.pid=this.idx);for(var s=0;s<this.customList.length;s++){a=this.customList[s];o[a.attrkey]=""}this.lst.push(o)}}else this.$message.warning("请选择虚拟品内容")},submitTime:function(){var t=this;this.multipleSelection.forEach((function(e,i){t.lst[e.rownum][t.dateType]=t.deliverydate})),this.deliverydatevisible=!1,this.$forceUpdate(),this.deliverydate=""},openQuickCode:function(){this.quickCodeVisible=!0,this.quickCodedata.quickcode="",this.$nextTick((function(){this.$refs.quickcodeInput.$el.querySelector("input").focus()}))},submitQuickCode:function(){var t=this.quickCodedata.quickcode.split("+")[0],e=this.quickCodedata.quickcode.split("+")[1],i=this.quickCodedata.quickcode.split("+")[2];t?this.getInfoBycode(t,e,i):this.$message.warning("请输入正确的快码格式")},getInfoBycode:function(t,e,i){var a=this;return Object(n["a"])(Object(r["a"])().mark((function o(){var s,n,d,m,c,u,f,h;return Object(r["a"])().wrap((function(o){while(1)switch(o.prev=o.next){case 0:return s={},n={},o.next=4,l["a"].get("/D91M01B1/getEntityByCode?key="+t);case 4:if(d=o.sent,200!=d.data.code){o.next=12;break}if(d.data.data){o.next=9;break}return a.$message.warning(d.data.msg||"未查询到该货品信息"),o.abrupt("return");case 9:s=d.data.data,o.next=14;break;case 12:return a.$message.warning(d.data.msg),o.abrupt("return");case 14:return o.next=16,l["a"].get("/D04M15S1/getEntityByCode?key="+e);case 16:for(m=o.sent,200==m.data.code?m.data.data?n=m.data.data:n.attributejson="":a.$message.warning(m.data.msg||"获取spu值失败"),c=Object.assign({},p),c.goodsid=s.id,c.goodsuid=s.goodsuid,c.goodsname=s.goodsname,c.goodsunit=s.goodsunit,c.goodsspec=s.goodsspec,c.partid=s.partid?s.partid:"",c.goodscustom1=s.custom1,c.goodscustom2=s.custom2,c.goodscustom3=s.custom3,c.goodscustom4=s.custom4,c.goodscustom5=s.custom5,c.goodscustom6=s.custom6,c.goodscustom7=s.custom7,c.goodscustom8=s.custom8,c.goodscustom9=s.custom9,c.goodscustom10=s.custom10,c.attributejson=n.attributejson,c.itemtaxrate=a.nowitemtaxrate,c.quantity=i||0,0!=a.idx&&(c.pid=a.idx),u=0;u<a.customList.length;u++)f=a.customList[u],c[f.attrkey]="";if(""==c.attributejson||null==c.attributejson);else for(h=JSON.parse(c.attributejson),u=0;u<h.length;u++)c[h[u].key]=h[u].value;a.lst.push(c),a.setAttributeJson(c,a.lst.length-1),a.quickCodeVisible=!1;case 44:case"end":return o.stop()}}),o)})))()},getHistoyrOrder:function(t,e,i){var a=this;this.currentRowKey=t.rowKeys;var o={PageNum:1,PageSize:20,OrderType:1,SearchType:1,scenedata:[{field:"App_Workgroup.groupname",fieldtype:0,math:"like",value:i},{field:"Mat_Goods.goodsuid",fieldtype:0,math:"like",value:e}]};this.$request.post("/D01M03B1/getPageList",JSON.stringify(o)).then((function(t){200===t.data.code&&(a.isShowHistory=!0,a.historyData=t.data.data.list,a.$nextTick((function(){a.$refs.historyOrder.initEchart(a.historyData)})))}))},getHistoryPrice:function(t,e){var i=this;this.isShowHistory=!1,"含税"==e?this.lst.map((function(e){e.rowKeys===i.currentRowKey&&(e.taxprice=t,e.price=B["a"].round(t/1.13,4))})):this.lst.map((function(e){e.rowKeys===i.currentRowKey&&(e.price=t)}))},monthFilter:function(t){if(t){var e=new Date(t),i=(e.getMonth()+1).toString().padStart(2,"0"),a=e.getDate().toString().padStart(2,"0");return"".concat(i,"-").concat(a)}}}},I=F,j=(i("594e"),Object(k["a"])(I,S,O,!1,null,"645112b1",null)),E=j.exports,R=i("78a4"),T=i("dcb4"),L=i("525f"),N=i("aa59"),V=i("5ad6"),H=i("9fee"),K=i("7f963"),z=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},A=[],G=i("49b1"),J=i("b893"),W={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"aa59"))}},props:["searchVal","isDialog","billcode"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:G["b"],customList:[],progressData:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.bindData(),this.getColumn()},methods:{bindData:function(){var t=this;if(this.isDialog){var e={citecode:this.searchVal};this.queryParams.SearchType=0,this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e}var i="/D01M08B1DEP/getPageTh";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(J["d"])()[0],EndDate:Object(J["d"])()[1]}),l["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total)})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(n["a"])(Object(r["a"])().mark((function e(){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(t.tableForm.formcode,G["b"]).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("assessor"==t.itemcode){s="";return s=o.oaflowmark&&!o.assessor?a("span",{style:"color:#ff9800"},["审核中"]):a("span",[o.assessor]),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["refno","billamount"])},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["billamount"];this.$countCellData(this,i,t,e)},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},U=W,Y=(i("58b8"),Object(k["a"])(U,z,A,!1,null,"91bf6ed4",null)),Q=Y.exports,X=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},Z=[],tt=i("1e58"),et={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"5ad6"))}},props:["online","searchVal","isDialog","billcode"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:tt["a"],customList:[],progressData:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},created:function(){},mounted:function(){this.bindData(),this.getColumn()},methods:{bindData:function(){var t=this;if(this.isDialog){var e={machuid:this.searchVal};this.queryParams.SearchType=0,this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e}if(this.online)var i="/D01M06B1/getOnlinePageList";else i="/D01M06B1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(J["d"])()[0],EndDate:Object(J["d"])()[1]}),l["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++){var a=t.lst[i];a.compcost=a.quantity-a.buyqty;for(var o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(n["a"])(Object(r["a"])().mark((function e(){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(t.tableForm.formcode,tt["a"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("goodsuid"==t.itemcode){var s=a("GoodsInfo",{attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}});return s}if("refno"==t.itemcode){s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("status"==t.itemcode){s="";return 0!=o.finishqty&&o.finishqty<o.quantity?s=a("span",{class:"textborder-blue"},[o.billtype.includes("退货")?"入库":"出库"]):o.finishqty==o.quantity&&0!=o.finishqty?s=a("span",{class:"textborder-green"},["完成"]):1==o.disannulmark?s=a("span",{class:"textborder-grey"},["撤销"]):o.finishclosed&&(s=a("span",{class:"textborder-grey"},["中止"])),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxprice","taxamount"];this.$countCellData(this,i,t,e)},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},it=et,at=(i("fa68"),Object(k["a"])(it,X,Z,!1,null,"72dfd597",null)),ot=at.exports,st=i("f3ef"),rt=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[i("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},nt=[],lt=i("c03f"),dt={components:{FormEdit:function(){return Promise.resolve().then(i.bind(null,"7f963"))}},props:["online","searchVal","isDialog","billcode"],data:function(){var t=this;return{lst:[],keynum:0,formvisible:!1,total:0,idx:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:lt["a"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.bindData(),this.getColumn()},methods:{bindData:function(){var t=this;if(this.isDialog){if("D05M05P4"==this.billcode)var e={workuid:this.searchVal};else if("D01M03B1"==this.billcode)e={machuid:this.searchVal};else if("D05M02B1"==this.billcode)e={workuid:this.searchVal};else e={citeuid:this.searchVal};this.queryParams.SearchType=0,this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e}if(this.online)var i="/D04M08B1/getOnlinePageList";else i="/D04M08B1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(J["d"])()[0],EndDate:Object(J["d"])()[1]}),l["a"].post(i,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[i],o[s].key,o[s].value)}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(n["a"])(Object(r["a"])().mark((function e(){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(t.tableForm.formcode,lt["a"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("status"==t.itemcode){s="";return 0!=o.finishqty&&o.finishqty<o.quantity?s=a("span",{class:"textborder-blue"},["领料"]):o.finishqty>=o.quantity&&0!=o.finishqty?s=a("span",{class:"textborder-green"},["完成"]):1==o.disannulmark&&(s=a("span",{class:"textborder-grey"},["撤销"])),s}if("goodsuid"==t.itemcode){s=a("GoodsInfo",{attrs:{scopeIndex:o.rownum,scopeVal:o[t.itemcode]}});return s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,i=["quantity","finishqty"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","quantity"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}}}},mt=dt,ct=(i("0fdb"),Object(k["a"])(mt,rt,nt,!1,null,"6506a0f1",null)),ut=ct.exports,ft=i("7996"),pt=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto","min-height":"260px"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[i("el-table-column",{attrs:{align:"center",label:"ID","min-width":"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.$index+1))])]}}])}),i("el-table-column",{attrs:{label:"模块编码",align:"center",width:"120px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.modulecode))])]}}])}),i("el-table-column",{attrs:{label:"审批名称",align:"center","min-width":"60px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.apprname))])]}}])}),i("el-table-column",{attrs:{label:"审批类型",align:"center","min-width":"60px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.apprtype))])]}}])}),i("el-table-column",{attrs:{label:"审批SN",align:"center","min-width":"60px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.apprsn))])]}}])}),i("el-table-column",{attrs:{label:"执行条件",align:"center","min-width":"100px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.resultcode))])]}}])}),i("el-table-column",{attrs:{label:"回调Url",align:"center","min-width":"100px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.callbackurl))])]}}])}),i("el-table-column",{attrs:{label:"回调Bean",align:"center","min-width":"100px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.callbackbean))])]}}])}),i("el-table-column",{attrs:{label:"回调日期",align:"center","min-width":"80px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormat")(e.row.callbackdate)))])]}}])}),i("el-table-column",{attrs:{label:"回调人员",align:"center","min-width":"100px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.callbackname))])]}}])}),i("el-table-column",{attrs:{label:"回调结果",align:"center","min-width":"100px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.callbackresult))])]}}])}),i("el-table-column",{attrs:{label:"回调信息",align:"center","min-width":"100px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.callbackmsg))])]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)])},ht=[],gt=i("333d"),bt={components:{Pagination:gt["a"]},props:["idx"],data:function(){return{title:"查询审批记录",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:{},queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr=""},mounted:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},bindData:function(){var t=this;return Object(n["a"])(Object(r["a"])().mark((function e(){var i;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.listLoading=!0,i={ModuleCode:"D01M03B1Edit",Billid:t.idx},t.queryParams.SearchPojo?t.queryParams.SearchPojo=Object.assign(t.queryParams.SearchPojo,i):t.queryParams.SearchPojo=i,e.next=5,l["a"].post("/D96M13B1REC/getPageList",JSON.stringify(t.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 5:case"end":return e.stop()}}),e)})))()},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={apprname:t,modulecode:t,apprtype:t,apprsn:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},yt=bt,vt=(i("3823"),i("d51e"),Object(k["a"])(yt,pt,ht,!1,null,"47045fed",null)),wt=vt.exports,xt=i("baaf"),kt=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"on",rules:t.formRules}},[i("el-row",[i("el-col",{attrs:{span:12}},[i("div",{on:{click:function(e){return t.cleValidate("counttype")}}},[i("el-form-item",{attrs:{label:"类型(年月日)",prop:"counttype"}},[i("el-select",{attrs:{placeholder:"请选择",size:"small"},model:{value:t.formdata.counttype,callback:function(e){t.$set(t.formdata,"counttype",e)},expression:"formdata.counttype"}},[i("el-option",{attrs:{label:"日",value:"day"}}),i("el-option",{attrs:{label:"月",value:"month"}}),i("el-option",{attrs:{label:"年",value:"year"}})],1)],1)],1)]),i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"跳步"}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",size:"small",min:1,max:10},model:{value:t.formdata.step,callback:function(e){t.$set(t.formdata,"step",e)},expression:"formdata.step"}})],1)],1)],1),i("el-divider"),t._l(5,(function(e,a){return i("el-row",{key:a},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"前缀"+(a+1)}},[i("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"","allow-create":"",clearable:"",placeholder:"前缀"+(a+1),size:"small",disabled:t.disabledForm["prefix"+(a+1)]},model:{value:t.formdata["prefix"+(a+1)],callback:function(e){t.$set(t.formdata,"prefix"+(a+1),e)},expression:"formdata['prefix' + (index + 1)]"}},[i("el-option",{attrs:{label:"YYYY",value:"YYYY"}}),i("el-option",{attrs:{label:"YY",value:"YY"}}),i("el-option",{attrs:{label:"MM",value:"MM"}}),i("el-option",{attrs:{label:"DD",value:"DD"}}),i("el-option",{attrs:{label:"dd",value:"dd"}}),i("el-option",{attrs:{label:"[00]",value:"[00]"}}),i("el-option",{attrs:{label:"[000]",value:"[000]"}}),i("el-option",{attrs:{label:"[0000]",value:"[0000]"}}),i("el-option",{attrs:{label:"[00000]",value:"[00000]"}})],1)],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"后缀"+(a+1)}},[i("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"","allow-create":"",clearable:"",placeholder:"后缀"+(a+1),size:"small",disabled:t.disabledForm["suffix"+(a+1)]},model:{value:t.formdata["suffix"+(a+1)],callback:function(e){t.$set(t.formdata,"suffix"+(a+1),e)},expression:"formdata['suffix' + (index + 1)]"}},[i("el-option",{attrs:{label:"-",value:"-"}}),i("el-option",{attrs:{label:"/",value:"/"}})],1)],1)],1)],1)}))],2),i("el-row",{staticStyle:{"margin-top":"30px"}},[i("span",{staticStyle:{color:"#f56c6c","padding-left":"30px"}},[t._v(" 例如：BM-2024-02-0001")]),i("div",{staticStyle:{display:"flex","justify-content":"flex-start"}},[i("ul",[i("li",{staticStyle:{margin:"5px 0"}},[i("span",[t._v("前缀1：BM (手动输入)")])]),i("li",{staticStyle:{"margin-bottom":"5px"}},[i("span",[t._v("后缀1：-")])]),i("li",{staticStyle:{"margin-bottom":"5px"}},[i("span",[t._v("前缀2：选择YYYY")])]),i("li",{staticStyle:{"margin-bottom":"5px"}},[i("span",[t._v("后缀2：-")])])]),i("ul",[i("li",{staticStyle:{"margin-bottom":"5px"}},[i("span",[t._v("前缀3：选择MM")])]),i("li",{staticStyle:{"margin-bottom":"5px"}},[i("span",[t._v("后缀3：-")])]),i("li",{staticStyle:{"margin-bottom":"5px"}},[i("span",[t._v("前缀4：[0000]")])]),i("li",{staticStyle:{"margin-bottom":"5px"}},[i("span",[t._v("依次类推 ......")])])])])])],1)])},_t=[],Dt=i("9416"),St=i("08a9"),Ot={name:"Formedit",components:{ElImageViewer:St["a"]},props:["billcode","billname","tablename"],data:function(){return{title:"单据编码",formdata:{modulecode:"",billname:"",prefix1:"",prefix2:"",prefix3:"",prefix4:"",prefix5:"",suffix1:"",suffix2:"",suffix3:"",suffix4:"",suffix5:"",counttype:"",step:1,currentnum:0,tablename:"",datecolumn:"CreateDate",columnname:"RefNo",dbfilter:"",allowedit:0,allowdelete:0,tenantid:"default",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{counttype:[{required:!0,trigger:"blur",message:"类型(年月日)为必填项"}]},disabledForm:{prefix1:!1,prefix2:!1,prefix3:!1,prefix4:!1,prefix5:!1,suffix1:!1,suffix2:!1,suffix3:!1,suffix4:!1,suffix5:!1},formLabelWidth:"100px",tenantData:[],selVisible:!1,idx:0,lst:[]}},watch:{idx:function(t,e){this.bindData()},"formdata.prefix1":function(t,e){this.isdisable(t,1)},"formdata.prefix2":function(t,e){this.isdisable(t,2)},"formdata.prefix3":function(t,e){this.isdisable(t,3)},"formdata.prefix4":function(t,e){this.isdisable(t,4)},"formdata.prefix5":function(t,e){this.isdisable(t,5)}},created:function(){this.bindData()},methods:{bindData:function(){var t=this,e={PageNum:1,PageSize:200,OrderType:1,SearchType:1,scenedata:[{field:"Sa_BillCode.modulecode",fieldtype:0,math:"equal",value:this.billcode}]};this.$request.post("/SaBillCode/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code?0!=e.data.data.list.length?(console.log("22w"),t.lst=e.data.data.list,t.idx=t.lst[0].id,t.$request.get("/SaBillCode/getEntity?key=".concat(t.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:(t.$message.warning("请设置编码！"),t.initFormData())}))):(t.idx=0,t.initFormData()):t.$message.warning("请设置编码！")}))},submitForm:function(){var t=this;this.$refs.formdata.validate((function(e){if(!e)return!1;t.formsave()}))},formsave:function(){var t=this;null!=this.formdata.tenantid&&""!=this.formdata.tenantid||(this.formdata.tenantid="default"),0==this.idx?Dt["a"].add(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("closeBillRefno"))})).catch((function(e){t.$message.warning("保存失败")})):Dt["a"].update(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("closeBillRefno"))})).catch((function(e){t.$message.warning("保存失败")}))},initFormData:function(){console.log("111"),this.formdata={modulecode:this.billcode,billname:this.billname,prefix1:"",prefix2:"",prefix3:"",prefix4:"",prefix5:"",suffix1:"",suffix2:"",suffix3:"",suffix4:"",suffix5:"",counttype:"",step:1,currentnum:0,tablename:this.tablename,datecolumn:"CreateDate",columnname:"",dbfilter:"",allowedit:0,allowdelete:0,tenantid:"default",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname}},isdisable:function(t,e){if("[00]"==t||"[000]"==t||"[0000]"==t||"[00000]"==t)for(var i=e;i<6;i++)i!=e&&(this.formdata["prefix"+i]="",this.disabledForm["prefix"+i]=!0),this.formdata["suffix"+i]="",this.disabledForm["suffix"+i]=!0;else for(i=e;i<6;i++)i!=e&&(this.disabledForm["prefix"+i]=!1),this.disabledForm["suffix"+i]=!1},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}},filters:{dateFormat:function(t){var e=new Date(t),i=e.getFullYear(),a=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0");return"".concat(i,"/").concat(a,"/").concat(o)}}},$t=Ot,Bt=(i("3ad2"),Object(k["a"])($t,kt,_t,!1,null,"2589d048",null)),qt=Bt.exports,Pt={name:"FormEdit",components:{FormTemp:T["a"],EditHeader:D,EditItem:E,BillState:L["a"],historyBill:ft["a"],TransferTemp:xt["a"],D01M08B1DEP:N["default"],D01M06B1:V["default"],D03M02B1:H["default"],D04M08B1:K["default"],selApprove:wt,D01M08B1DEPList:Q,D01M06B1List:ot,D03M02B1List:st["a"],D04M08B1List:ut,BillRefnoEdit:qt},props:["idx","isDialog","initData","billcode"],data:function(){return{title:"销售单据",operateBar:h,processBar:g,formdata:{refno:"",billtype:"量产订单",billtitle:"",billdate:new Date,groupid:"",groupname:"",grouplevel:"",custorderid:"",logisticsmode:"",advaamount:"",salesman:"",taxrate:"",billtaxamount:"",billtaxtotal:"",billamount:"",billstatecode:"",billstatedate:"",billplandate:new Date,groupcode:"",disannulmark:0,summary:"",assessor:"",assessdate:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},printitemlist:[],approvevisible:!1,operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",stockwarndata:[],stockwarningvisible:!1,historybillvisible:!1,matcodeNum:0,billRefnoDialogVisible:!1,formtemplate:R["a"],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){var t=this;this.bindData(),this.$nextTick((function(){t.isDialog&&t.billSwitch(t.billcode)}))},methods:{bindTemp:function(){var t=this;l["a"].get("/SaFormCustom/getEntityByCode?key=D01M03B1").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):R["a"],t.formtemplate.footer.type||(t.formtemplate.footer=R["a"].footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&l["a"].get("/D01M03B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.$refs.elitem.multipleSelection=[],t.$refs.elitem.checkboxOption.selectedRowKeys=[],t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error(e||"请求错误")}))},setGroupRow:function(t){console.log(t),this.formdata.groupname=t.groupname,this.formdata.groupid=t.id,this.formdata.salesman=t.seller,this.formdata.abbreviate=t.abbreviate,this.formdata.grouplevel=t.grouplevel,this.formdata.logisticsport=t.groupadd},autoClear:function(t){this.formdata.groupname="",this.formdata.groupid="",this.formdata.salesman="",this.formdata.abbreviate="",this.formdata.grouplevel=""},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;0!=e.$refs.elitem.lst.length?e.saveForm():e.$message.warning("单据内容不能为空")}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){try{this.$refs.elitem.$refs.multipleTable.stopEditingCell()}catch(o){}for(var e=0;e<this.$refs.elitem.lst.length;e++)if(0==this.$refs.elitem.lst[e].quantity)return void this.$message.warning(this.$refs.elitem.lst[e].goodsname+"的数量不能为0");this.submitting=1,this.formdata.item=this.$refs.elitem.lst,this.formdata.billplandate=Object(J["b"])(this.formdata.billplandate);var i=this.$amountFloat(this.formdata.item);this.formdata.billamount=i.billamount,this.formdata.billtaxamount=i.billtaxamount,this.formdata.billtaxtotal=i.billtaxtotal;var a={item:[]};a=this.$getParam(f,a,this.formdata),0==this.idx?m.add(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):m.update(a).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){m.delete(e)})).catch((function(){}))},approval:function(){m.approval(this)},approvalRequest:function(t){var e=this;return Object(n["a"])(Object(r["a"])().mark((function t(){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:m.approval(e);case 1:case"end":return t.stop()}}),t)})))()},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?m.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){m.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t;var e=this.$refs.elitem.multipleSelection;"D01M06B1"!==t&&"D03M02B1"!==t&&"D04M08B1"!==t||e.length>0&&(this.formdata.item=e)},openHistoryBill:function(){this.historybillvisible=!0,this.dialogIdx=0},processBill:function(t){console.log(t,"data"),this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},setBillRefno:function(){var t=this;this.$nextTick((function(){t.billRefnoDialogVisible=!0}))},submitBillRefno:function(){this.$refs.BillRefnoEditRef.submitForm()},closeBillRefno:function(){this.billRefnoDialogVisible=!1},printItemButton:function(){var t=this;this.printitemlist=this.$refs.elitem.multipleSelection,setTimeout((function(){t.$refs.PrintServerItem.printButton(3,1)}))},savematcode:function(){var t=this;l["a"].get("/system/SYSM06B2/getListByPrefix?key=module.sale.machmatrequ").then((function(e){if(200==e.data.code){if(!e.data.data)return void t.saveForm();var i=e.data.data["module.sale.machmatrequ"]?JSON.parse(e.data.data["module.sale.machmatrequ"]):[];t.$nextTick((function(){t.matcodeNum=0,t.$refs.elitem.goodsOk=0;for(var e=t.$refs.elitem.lst,a=0;a<e.length;a++)for(var o=0;o<i.length;o++)e[a][i[o].key]&&e[a][i[o].key].indexOf(i[o].value)>=0&&(e[a].matcode?t.$refs.elitem.checkGoods(e[a],a):(t.$refs.elitem.searchGoods(e[a],a,!0),t.matcodeNum+=1))})),setTimeout((function(){t.setTip()}),100)}}))},setTip:function(){var t=this;this.$nextTick((function(){if(0!=t.$refs.elitem.goodsErr.length){for(var e="",i=0;i<t.$refs.elitem.goodsErr.length;i++){var a=t.$refs.elitem.goodsErr[i];e+=a+","}e=e.replace(/,$/gi,""),t.$confirm("物料<span style='color:red;font-weight:bold'>"+e+"</span>与基板信息不一致,是否继续保存？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).then((function(e){"confirm"==e&&t.setTipSec()})).catch((function(){}))}else t.setTipSec()}))},setTipSec:function(){var t=this;this.matcodeNum>this.$refs.elitem.goodsOk&&0!=this.matcodeNum?this.$confirm("共"+this.matcodeNum+"条物料编码，成功识别"+this.$refs.elitem.goodsOk+"条,是否继续保存？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(e){"confirm"==e&&t.saveForm()})).catch((function(){})):this.saveForm()},isDoQtyWarning:function(){var t=this;l["a"].get("/system/SYSM06B2/getListByPrefix?key=module.sale.machchkinveqty").then((function(e){200==e.data.code?null==e.data.data||""==e.data.data["module.sale.machchkinveqty"]?t.approvalRequest(t.formdata.id):JSON.parse(e.data.data["module.sale.machchkinveqty"])?t.isQtyWarning():t.approvalRequest(t.formdata.id):t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},isQtyWarning:function(){var t=this;return Object(n["a"])(Object(r["a"])().mark((function e(){var i,a,o,n,d,m,c;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=t,a=[],t.formdata.item.forEach((function(t){t.matcode.replace(/^\s*|\s*$/g,"")&&a.push(t.matcode)})),a=Object(s["a"])(new Set(a)),0!=a.length){e.next=7;break}return i.approvalRequest(t.formdata.id),e.abrupt("return");case 7:for(o="",i.stockwarndata=[],n=!0,d=[],m=0;m<a.length;m++)c=new Promise((function(t,e){var s={PageNum:1,PageSize:1,OrderType:1,SearchType:0};s.SearchPojo={goodsuid:a[m]},l["a"].post("/D04M04B1/getMachMatQtyPageListByGoods?itemid="+i.idx,JSON.stringify(s)).then((function(a){if(200==a.data.code){var s=a.data.data.list[0];s.stoqty<0?(o+=s.goodsname+",",i.stockwarndata.push(s),t(o)):t(o)}else e(a.data.msg||"获取物料信息失败")})).catch((function(t){e(t||"请求错误")}))})),d.push(c);return e.next=14,Promise.all(d).then((function(t){})).catch((function(t){i.$message.warning(t),n=!1})).finally((function(){if(n){var t=/,$/gi;o=o.replace(t,""),0==i.stockwarndata.length?i.approvalRequest(i.formdata.id):i.stockwarningvisible=!0}}));case 14:case"end":return e.stop()}}),e)})))()},selApprove:function(){this.approvevisible=!0},submitHistory:function(){var t=this.$refs.historyBill.selrows,e={item:[]};e=this.$getParam(f,e,t),this.$delete(e,"id"),this.$delete(e,"refno"),this.$set(e,"billdate",new Date);for(var i=0;i<e.item.length;i++){var a=e.item[i];this.$delete(a,"id"),this.$delete(a,"pid"),this.$delete(a,"quotuid"),this.$delete(a,"quotitemid"),this.$delete(a,"ordercostuid"),this.$delete(a,"ordercostitemid"),this.$set(a,"closed",0),this.$set(a,"disannulmark",0),this.$set(a,"matused",0),this.$set(a,"itemorgdate",new Date),this.$set(a,"itemplandate",new Date)}this.formdata=e,this.historybillvisible=!1},billSwitch:function(t){"D01M02B1"==t?Object(b["d01m02b1"])(this.initData,this.formdata,p):"D01M04B1"==t&&Object(b["d01m04b1"])(this.initData,this.formdata,p)}}},Ct=Pt,Mt=(i("059b"),Object(k["a"])(Ct,a,o,!1,null,"834bad74",null));e["default"]=Mt.exports},fd87:function(t,e,i){var a=i("74e8");a("Int8",(function(t){return function(e,i,a){return t(this,e,i,a)}}))}}]);