(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-49d25b93"],{"0c31":function(t,a,e){"use strict";e("0c4c")},"0c4c":function(t,a,e){},2982:function(t,a,e){},"63fe":function(t,a,e){},"8ab3":function(t,a,e){"use strict";e("63fe")},aad1:function(t,a,e){t.exports=e.p+"static/img/noFace.855a718f.jpg"},bcc6:function(t,a,e){"use strict";e("2982")},c3bd:function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("div",{staticClass:"container"},[e("div",{staticClass:"page-content",style:{height:t.tableMaxHeight+"px"}},[e("div",{staticClass:"page-content-left"},[e("h3",[t._v("参数设置")]),e("ul",{staticClass:"navigation ulstyle",style:{height:t.tableMaxHeight-40+"px"}},t._l(t.navlist,(function(a,i){return e("li",{key:i,class:t.navindex==i?"li-active":"",on:{click:function(e){t.navindex=i,t.navtitle=a.cfgname,t.childlst=a.children}}},[null==a.cfgicon||""==a.cfgicon?e("div",[e("i",{staticClass:"el-icon-price-tag"})]):e("div",[a.cfgicon.includes("el-icon")?e("i",{class:a.cfgicon}):e("svg-icon",{attrs:{"icon-class":a.cfgicon}})],1),e("span",{staticStyle:{"margin-left":"5px"}},[t._v(t._s(a.cfgname))])])})),0)]),e("div",{staticClass:"page-content-right"},[e("div",{staticClass:"settings-section"},[e("div",{staticClass:"header"},[e("h2",{staticClass:"title"},[t._v(t._s(t.navtitle))])]),e("div",{staticClass:"card"},[e("ul",{staticClass:"ulstyle",style:{"max-height":t.tableMaxHeight-90+"px"}},t._l(t.childlst,(function(a,i){return e("li",{key:i},[e("div",[e("div",{staticClass:"nameStyle"},[a.cfgicon&&a.cfgicon.includes("el-icon")?e("i",{class:a.cfgicon}):e("svg-icon",{attrs:{"icon-class":a.cfgicon?a.cfgicon:""}}),e("span",{staticStyle:{"margin-left":"5px"}},[t._v(t._s(a.cfgname))])],1),e("div",{staticClass:"valStyle"},[6==a.ctrltype?e("div",{staticClass:"valImg"},[e("img",{attrs:{src:a.cfgvalue,alt:""}}),e("span",{staticClass:"ellipsis tipStyle",attrs:{title:a.cfgvalue}},[t._v(t._s(a.cfgvalue))])]):e("span",{staticClass:"ellipsis tipStyle",attrs:{title:a.cfgvalue}},[t._v(t._s(a.cfgvalue))])])]),e("div",[e("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(e){return t.editShow(a,i)}}},[t._v(" 编辑 ")])],1)])})),0)])])])])]),t.dialogVisible?e("el-dialog",{attrs:{title:t.dialogTitle,width:t.dialogwidth,visible:t.dialogVisible,"close-on-click-modal":!1},on:{"update:visible":function(a){t.dialogVisible=a}}},[e("editItem",{ref:"editItem",attrs:{row:t.childRow}}),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(a){return t.submitDialog()}}},[t._v("确 定")]),e("el-button",{attrs:{size:"small"},on:{click:function(a){t.dialogVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.ImgInfoVisible?e("el-dialog",{attrs:{title:"素材库",visible:t.ImgInfoVisible,width:"860px",top:"5vh","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(a){t.ImgInfoVisible=a}}},[e("selImgInfo",{ref:"selImgInfo",attrs:{multi:1}}),e("div",{attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitselImg}},[t._v("确 定")]),e("el-button",{attrs:{size:"small"},on:{click:function(a){t.ImgInfoVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},s=[],l=e("c7eb"),n=e("1da1"),o=(e("99af"),e("e9c4"),e("d3b7"),e("25f0"),e("4d90"),e("159b"),e("b775")),c=e("333d"),r=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("div",[e("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-position":"top"}},[e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{staticStyle:{"line-height":"30px"},attrs:{label:t.formdata.cfgname}},[0==t.formdata.ctrltype?e("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入参数内容",clearable:""},model:{value:t.formdata.cfgvalue,callback:function(a){t.$set(t.formdata,"cfgvalue",a)},expression:"formdata.cfgvalue"}}):1==t.formdata.ctrltype?e("el-input-number",{staticStyle:{width:"80%"},attrs:{step:1,min:0},model:{value:t.formdata.cfgvalue,callback:function(a){t.$set(t.formdata,"cfgvalue",a)},expression:"formdata.cfgvalue"}}):2==t.formdata.ctrltype?e("el-select",{staticStyle:{width:"100%"},model:{value:t.formdata.cfgvalue,callback:function(a){t.$set(t.formdata,"cfgvalue",a)},expression:"formdata.cfgvalue"}},t._l(t.option,(function(t,a){return e("el-option",{key:a,attrs:{label:t.name,value:t.value}})})),1):t._e()],1)],1)],1)],1)],1)])},d=[],u=(e("b64b"),{props:["row"],data:function(){return{formdata:{},option:[]}},created:function(){this.bindData()},methods:{bindData:function(){this.formdata=Object.assign({},this.row),this.formdata.cfgoption?this.option=JSON.parse(this.formdata.cfgoption):this.option=[]}}}),f=u,g=(e("8ab3"),e("2877")),p=Object(g["a"])(f,r,d,!1,null,"8f2724d2",null),m=p.exports,h=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{},[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"small"},model:{value:t.strfilter,callback:function(a){t.strfilter=a},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(a){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",{staticClass:"uploadImg"},[i("el-button",{attrs:{type:"primary",size:"small\n        "},on:{click:function(a){return t.openImgUpload()}}},[t._v("上传")])],1)]),i("div",{staticClass:"material-content"},[0!=t.lst.length?i("div",{staticStyle:{display:"flex","flex-wrap":"wrap",flex:"1","align-content":"flex-start"}},t._l(t.lst,(function(a,s){return i("div",{key:s,staticClass:"img-item",on:{click:function(e){return t.getCurrentRow(a,s)}}},[i("div",{staticClass:"imgcountent",style:t.selIndex==s?" border: 2px solid #409eff; ":""},[a.fileurl?i("img",{attrs:{src:a.fileurl,alt:""}}):i("img",{attrs:{src:e("aad1"),alt:""}})]),i("div",{staticClass:"imgTitle ellipsis"},[i("span",{style:t.selIndex==s?"color:#409eff":""},[t._v(t._s(a.fileoriname)+" ")])])])})),0):i("div",{staticClass:"noData"},[t._v("暂无图片内容")])]),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(a){return t.$set(t.queryParams,"PageNum",a)},"update:limit":function(a){return t.$set(t.queryParams,"PageSize",a)},pagination:t.GetList}}),i("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[i("input",{ref:"upload",attrs:{type:"file"},on:{change:t.getFile}})]),t.ImgUploadVisible?i("el-dialog",{attrs:{title:"图片上传",width:"500px",visible:t.ImgUploadVisible,"append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(a){t.ImgUploadVisible=a}}},[i("div",[i("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.$refs.upload.click()}}},[t._v("选择文件")]),i("div",{staticStyle:{display:"inline-block",margin:"0 10px"}},[i("el-cascader",{attrs:{options:t.groupData,props:t.defaultProps,placeholder:"请选择分组",clearable:"","show-all-levels":!1},on:{change:t.handleChange},model:{value:t.gengroupid,callback:function(a){t.gengroupid=a},expression:"gengroupid"}})],1),i("div",{staticClass:"imgUpload"},[i("div",{staticClass:"imgUploadShow"},[i("img",{attrs:{src:t.uploadImg?t.uploadImg:e("aad1"),alt:""},on:{click:function(a){return t.$refs.upload.click()}}})]),t.uploadImg?i("div",{staticClass:"imgUploadInfo"},[i("p",[t._v("名称："+t._s(t.uploadImgName))]),i("p",[t._v("大小："+t._s(t.uploadImgSize))]),i("p",[t._v("类型："+t._s(t.uploadImgType))])]):t._e()])],1),i("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"-20px"},attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(a){return t.submitImgUpload()}}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(a){t.ImgUploadVisible=!1}}},[t._v("取 消")])],1)]):t._e()],1)},v=[],b=e("2909"),y=(e("d81d"),e("b0c0"),e("a9e3"),e("b680"),e("6ca8")),I=e.n(y),S={components:{Pagination:c["a"]},props:["multi"],data:function(){return{title:"素材库",lst:[],strfilter:"",selrows:"",total:0,selIndex:-1,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},ImgUploadVisible:!1,uploadImg:"",uploadImgName:"",uploadImgSize:0,uploadImgType:"",fileTemp:{},pid:"root",treeTitle:"素材库",groupData:[],gengroupid:"",defaultProps:{children:"children",label:"label",value:"id"}}},watch:{},created:function(){this.bindData(),this.BindTreeData()},methods:{getCurrentRow:function(t,a){this.selIndex=a,this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;o["a"].post("/utils/D96M02B1/getPageList",JSON.stringify(this.queryParams)).then((function(a){200==a.data.code&&(t.lst=a.data.data.list,t.total=a.data.data.total)})).catch((function(a){t.$message.error("请求错误")}))},search:function(t){""!=t?this.queryParams.SearchPojo={fileoriname:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},openImgUpload:function(){this.ImgUploadVisible=!0,this.uploadImg="",this.uploadImgName="",this.uploadImgSize="0KB",this.uploadImgType=""},getFile:function(){var t=this,a=this.$refs.upload,e=a.files[0];I()(e).then((function(a){console.log(a),t.uploadImg=a.base64,t.uploadImgName=a.origin.name,t.uploadImgSize=Number(a.fileLen/1024).toFixed(2)+"KB",t.uploadImgType=a.file.type,t.fileTemp=a}))},submitImgUpload:function(){this.uploadBase64(this.fileTemp.formData)},uploadBase64:function(t){var a=this;o["a"].post("/utils/D96M02B1/uploadImage",t,{headers:{"Content-Type":"multipart/form-data"}}).then((function(t){200==t.data.code?(a.uploadImg="",a.$refs.upload.value="",a.fileTemp={},a.ImgUploadVisible=!1,a.bindData()):a.$message.warning(t.data.msg||"上传图片失败")}))},handleChange:function(t){console.log(t),t.length>0?this.gengroupid=t[t.length-1]:this.gengroupid="0"},BindTreeData:function(){var t=this;o["a"].get("/system/SYSM07B8/getListByModuleCode?Code=D96M02B1").then((function(a){if(200==a.data.code){var e=a.data.data.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname}})),i=[{id:"0",pid:"root",label:t.treeTitle}],s=[].concat(Object(b["a"])(e),i);t.gengroupData=s,t.groupData=t.transData(s,"id","pid","children")}}))},transData:function(t,a,e,i){for(var s=[],l={},n=a,o=e,c=i,r=0,d=0,u=t.length;r<u;r++)l[t[r][n]]=t[r];for(;d<u;d++){var f=t[d],g=l[f[o]];g?(!g[c]&&(g[c]=[]),g[c].push(f)):s.push(f)}return s}}},w=S,x=(e("0c31"),Object(g["a"])(w,h,v,!1,null,"6708a5ca",null)),_=x.exports,C={components:{Pagination:c["a"],editItem:m,selImgInfo:_},data:function(){return{title:"系统参数",lst:[],navlist:[],childlst:[],childRow:{},navtitle:"",navindex:0,searchstr:" ",formvisible:!1,ImgInfoVisible:!1,refreshTable:!1,isShowAll:!0,getType:"create",idx:0,total:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:0},dialogwidth:"500px",dialogTitle:"编辑",dialogVisible:!1,formdata:{}}},computed:{tableMaxHeight:function(){return window.innerHeight-90}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;return Object(n["a"])(Object(l["a"])().mark((function a(){var e;return Object(l["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:e={PageNum:1,PageSize:20,OrderType:0,SearchType:1,scenedata:[{field:"Sa_Config.cfgkey",fieldtype:4,math:"equal",value:"system.appurl"}]},t.$request.post("/SaConfig/getPageList",JSON.stringify(e)).then((function(a){200===a.data.code?(t.formdata=a.data.data.list.length>0?a.data.data.list[0]:{id:0,cfgvalue:""},t.idx=t.formdata.id,t.navlist=[{cfgname:"App参数设置",childlst:[{cfgicon:"",cfgname:"App地址",cfgvalue:t.formdata.cfgvalue,ctrltype:0}]}],t.childlst=[{cfgicon:"",cfgname:"App地址",cfgvalue:t.formdata.cfgvalue,ctrltype:0}]):t.$message.warning("查询参数列表失败")})),t.listLoading=!0;case 3:case"end":return a.stop()}}),a)})))()},editShow:function(t,a){this.childRow=t,6==t.ctrltype?this.ImgInfoVisible=!0:this.dialogVisible=!0},submitselImg:function(){var t=this,a=this.$refs.selImgInfo.selrows;this.childRow.cfgvalue=a.fileurl,this.ImgInfoVisible=!1;for(var e=0;e<this.lst.length;e++){var i=this.lst[e];if(i.id==this.childRow.id){this.lst[e]=this.childRow;break}}o["a"].post("/SaConfig/updateList",JSON.stringify(this.lst)).then((function(a){200==a.data.code?(t.dialogVisible=!1,t.$message.success("保存成功"),t.bindData()):t.$message.warning("保存失败")})).catch((function(a){t.$message.error("请求错误")}))},submitDialog:function(){var t=this,a=this.$refs.editItem.formdata,e={cfgkey:"system.appurl",cfgvalue:a.cfgvalue},i="";this.idx?(e.id=this.idx,i="/SaConfig/update"):i="/SaConfig/setConfig",this.$request.post(i,JSON.stringify(e)).then((function(a){200==a.data.code?(t.dialogVisible=!1,t.$message.success("设置地址成功"),t.bindData()):t.$message.warning("设置地址失败")}))},changeFormat:function(t){var a=[];if(!Array.isArray(t))return a;t.forEach((function(t){delete t.children}));var e={};return t.forEach((function(t){e[t.id]=t})),t.forEach((function(t){var i=e[t.parentid];i?(i.children||(i.children=[])).push(t):a.push(t)})),a}},filters:{dateFormat:function(t){if(t){var a=new Date(t),e=a.getFullYear(),i=(a.getMonth()+1).toString().padStart(2,"0"),s=a.getDate().toString().padStart(2,"0"),l=a.getHours().toString().padStart(2,"0"),n=a.getMinutes().toString().padStart(2,"0"),o=a.getSeconds().toString().padStart(2,"0");return"".concat(e,"-").concat(i,"-").concat(s," ").concat(l,":").concat(n,":").concat(o)}}}},k=C,P=(e("bcc6"),Object(g["a"])(k,i,s,!1,null,"6ff1fb62",null));a["default"]=P.exports}}]);