(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2ae9a078"],{1134:function(t,e,a){},"11f6":function(t,e,a){},"27f6":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.formvisible?a("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[a("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[a("ve-table",{key:t.keynum,ref:"tableCite",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("div",{staticClass:"flex a-c"},[a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?a("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[a("span",[t._v("计数="+t._s(t.cellNum))]),a("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)])},o=[],s=a("c7eb"),n=a("1da1"),r=(a("caad"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("2532"),a("3ca3"),a("c7cd"),a("159b"),a("ddb0"),a("b775")),l=a("5e63"),d=a("b893"),c={components:{FormEdit:function(){return Promise.resolve().then(a.bind(null,"13df"))}},props:["online","searchVal","isDialog"],data:function(){var t=this;return{lst:[],total:0,formvisible:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:l["b"],customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var a=e.startRowIndex;t.rowScroll=a}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var a=this.tableForm.item[e];a.displaymark&&(t+=Number(a.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},mounted:function(){this.getColumn(),this.bindData()},methods:{bindData:function(){var t=this,e={citeuid:this.searchVal};this.queryParams.SearchPojo=e;var a="/D04M01R1/getPageList";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(d["d"])()[0],EndDate:Object(d["d"])()[1]}),r["a"].post(a,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var a=0;a<t.lst.length;a++)for(var i=t.lst[a],o=i.attributejson?JSON.parse(i.attributejson):[],s=0;s<o.length;s++)t.$set(t.lst[a],o[s].key,o[s].value)}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(n["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(l["b"].formcode,l["b"],1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,a=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,i){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(a,i){var o=a.row;a.column,a.rowIndex;if("billdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("refno"==t.itemcode){var s=i("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return s}if("goodsuid"==t.itemcode){s=i("GoodsInfo",{attrs:{scopeVal:o[t.itemcode],baseurl:"/D04M04B1/getMachMatQtyPageListByGoods"}});return s}if("billtype"==t.itemcode){s=i("span",[o[t.itemcode]]);return o[t.itemcode].includes("红冲")&&(s=i("span",{style:"color:#f44336"},[o[t.itemcode]])),s}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),a.push(o)})),a.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,a){t.row,t.column;var i=t.rowIndex;return i+e.rowScroll+1}}),this.customData=a,this.keynum+=1},countCellData:function(){var t=this.$refs.tableCite.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableCite.getRangeCellSelection().selectionRangeIndexes,a=["quantity"];this.$countCellData(this,a,t,e)},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var a={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(a,this.tableForm),this.bindData();break}}}},m=c,u=(a("b83a"),a("2877")),f=Object(u["a"])(m,i,o,!1,null,"34f0715a",null);e["a"]=f.exports},"49a8":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):a("div",{ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,btnHelp:t.btnHelp,advancedSearch:t.advancedSearch,bindColumn:t.getColumn}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:t.showhelp?20:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"table-container",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"1px 0px 1px 0px"},height:t.tableMaxHeight},on:{"sort-change":t.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["refno"==e.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showForm(i.row.id)}}},[t._v(t._s(i.row.refno?i.row.refno:"单据编号"))]):"billdate"==e.itemcode||"startdate"==e.itemcode||"enddate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))]):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),a("div",{staticStyle:{"margin-right":"40px"}},[a("Scene",{ref:"scene",attrs:{code:"D04M07B1Th"},on:{bindData:t.bindData}})],1)],1)],1),a("el-col",{attrs:{span:t.showhelp?4:0}},[a("helpmodel",{ref:"helpmodel",attrs:{code:"D04M07B1"}})],1)],1)],1)],1)])},o=[],s=(a("e9c4"),a("b64b"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",{staticStyle:{display:"flex"}},[a("div",{staticClass:"flex infoForm"},[a("span",{staticClass:"infoForm-Title"},[t._v("时间范围")]),a("el-date-picker",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"unlink-panels":"",size:"mini","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-help",title:"帮助"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),a("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[a("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),n=[],r=a("b893"),l={name:"Listheader",props:["tableForm"],components:{},data:function(){return{strfilter:"",iShow:!1,formdata:{},dateRange:Object(r["d"])(),pickerOptions:Object(r["h"])(),setColumsVisible:!1,searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(t){var e={dateRange:this.dateRange,formdata:t};this.$emit("advancedSearch",e),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){var t={dateRange:this.dateRange,strfilter:this.strfilter};this.$emit("btnSearch",t)},bindData:function(){this.$emit("bindData")}}},d=l,c=(a("9227"),a("2877")),m=Object(c["a"])(d,s,n,!1,null,"46a16ba8",null),u=m.exports,f=a("333d"),p=a("b775"),h=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small",disabled:!!t.formdata.assessor},on:{click:function(e){return t.submitForm("formdata")}}},[t._v("保 存")]),a("div",{staticStyle:{display:"inline-block",margin:"0 10px"}},[t.formdata.assessor?a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.DeApproval()}}},[t._v(" 反审核")]):a("el-button",{attrs:{size:"small",type:"primary",disabled:!t.formdata.id},on:{click:function(e){return t.approval()}}},[t._v(" 审 核")])],1),a("el-button",{attrs:{disabled:!t.formdata.id,type:"primary",size:"small"},nativeOn:{click:function(e){return t.printButton()}}},[t._v("打 印")]),a("el-dropdown",{staticStyle:{"margin-left":"10px"},attrs:{trigger:"click",placement:"bottom","hide-on-click":!1}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.formdata.id||!!t.formdata.assessor},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")]),a("el-dropdown-item",{attrs:{icon:"el-icon-s-check",disabled:!t.formdata.id||!!t.formdata.assessor},nativeOn:{click:function(e){return t.flowable()}}},[t._v(" OA审批 ")]),a("el-dropdown-item",{attrs:{divided:"",icon:"el-icon-plus",disabled:!t.formdata.assessor},nativeOn:{click:function(e){return t.generate()}}},[t._v("生成盈亏")])],1)],1),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("过程"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{nativeOn:{click:function(e){t.processVisible=!0,t.processTitle="盈亏查看",t.processModel="D04M01B9"}}},[t._v("盈亏查看")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("div",{staticClass:"flex j-end p-a",staticStyle:{top:"5px",right:"0"}},[a("div",{staticClass:"refNo flex j-end"},[a("div",{staticClass:"refNo-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),a("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini"},model:{value:t.formdata.billdate,callback:function(e){t.$set(t.formdata,"billdate",e)},expression:"formdata.billdate"}})],1),a("div",[a("span",[t._v("NO：")]),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini",readonly:!!t.idx},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)]),a("div",{staticStyle:{display:"flex","justify-content":"flex-end"}},[a("div",{staticStyle:{"margin-right":"10px",width:"100px"}},[t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount?a("svg-icon",{staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"finish1"}}):t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount?a("svg-icon",{staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"revoke"}}):t._e()],1),a("div",{staticStyle:{"margin-right":"10px",width:"90px"}},[a("svg-icon",{directives:[{name:"show",rawName:"v-show",value:!!t.formdata.assessor,expression:"!!formdata.assessor"}],staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"approve"}})],1)])])]),a("el-form",{ref:"formdata",staticClass:"custInfo",staticStyle:{"min-height":"115px"},attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"盘点类型"}},[a("el-input",{attrs:{placeholder:"请输入盘点类型",clearable:"",size:"small"},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"年份"}},[a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("el-input-number",{attrs:{"controls-position":"right",min:2020,size:"small"},model:{value:t.formdata.inveyear,callback:function(e){t.$set(t.formdata,"inveyear",e)},expression:"formdata.inveyear"}}),a("span",{staticStyle:{margin:"0 5px"}},[t._v(" 年 ")]),a("el-input-number",{attrs:{"controls-position":"right",min:1,max:12,size:"small"},model:{value:t.formdata.invemonth,callback:function(e){t.$set(t.formdata,"invemonth",e)},expression:"formdata.invemonth"}}),a("span",{staticStyle:{margin:"0 5px"}},[t._v(" 月 ")])],1)])],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"盘点仓库"}},[a("el-popover",{attrs:{disabled:!!t.idx,placement:"bottom-start",trigger:"click"},on:{show:function(e){return t.$refs.selectStore.bindData()}},model:{value:t.selVisible4store,callback:function(e){t.selVisible4store=e},expression:"selVisible4store"}},[a("selstore",{ref:"selectStore",staticStyle:{width:"620px",height:"420px"},attrs:{multi:t.multi},on:{singleSel:t.selectStore}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{disabled:!!t.idx,placeholder:"请输入仓库名称",size:"small"},model:{value:t.formdata.storename,callback:function(e){t.$set(t.formdata,"storename",e)},expression:"formdata.storename"}},[a("i",{staticClass:"el-icon-circle-plus-outline",staticStyle:{"font-size":"15px",color:"rgb(64, 158, 255)"},attrs:{slot:"suffix"},slot:"suffix"})])],1)],1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"计划开始"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.startdate,callback:function(e){t.$set(t.formdata,"startdate",e)},expression:"formdata.startdate"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"计划结束"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.enddate,callback:function(e){t.$set(t.formdata,"enddate",e)},expression:"formdata.enddate"}})],1)],1),0==t.idx?a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{"label-width":"10px"}},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.getAllBill()}}},[a("i",{staticClass:"el-icon-plus"}),t._v(" 生成盘点表")])],1)],1):t._e()],1)],1),a("el-divider")],1),a("div",{staticClass:"form-body form f-1"},[a("elitem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx},on:{bindData:t.bindData}})],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.summary,callback:function(e){t.$set(t.formdata,"summary",e)},expression:"formdata.summary"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"审核"}},[a("span",{staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.assessor))])])],1),a("el-col",{directives:[{name:"show",rawName:"v-show",value:t.formdata.assessor,expression:"formdata.assessor"}],attrs:{span:4}},[a("el-form-item",{attrs:{label:"审核日期"}},[a("span",{staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.assessdate)))])])],1)],1)],1)],1)]),a("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible},on:{"update:visible":function(e){t.ReportVisible=e}}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button-group",{staticStyle:{float:"left"}},[a("el-button",{attrs:{type:"print"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="print",t.submitRemoteReport()}}},[t._v("云打印")]),a("el-button",{attrs:{type:"preview"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="preview",t.submitRemoteReport()}}},[t._v("云预览")])],1),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("打印")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),a("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[a("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})]),t.operationVisible?a("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}},["D04M01B9"==t.processModel?a("D04M01B9",{ref:"D04M01B9",attrs:{idx:t.dialogIdx,isDialog:!0,initData:t.formdata,billcode:"D04M07B1"},on:{changeIdx:t.changeIdx,closeDialog:function(e){t.operationVisible=!1}}}):t._e()],1):t._e(),t.processVisible?a("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"80vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}},["D04M01B9"==t.processModel?a("D04M01B9List",{ref:"D04M01B9List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e(),t.ExamineVisible?a("el-dialog",{attrs:{title:"审批模板",width:"400px",visible:t.ExamineVisible,"append-to-body":!0},on:{"update:visible":function(e){t.ExamineVisible=e}}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择审批模板"},on:{change:t.changeExamineModel},model:{value:t.ExamineModel,callback:function(e){t.ExamineModel=e},expression:"ExamineModel"}},t._l(t.ExamineData,(function(e){return a("el-option",{key:e.id,attrs:{label:e.apprname,value:e.id}},[a("span",{staticStyle:{float:"left"}},[t._v(t._s(e.apprname))]),a("span",{staticClass:"selectSpan",staticStyle:{float:"right","text-align":"right",color:"#8492a6","font-size":"13px"}},[t._v(t._s("wxe"==e.sendType?"企业微信":"钉钉"))])])})),1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitExamine}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ExamineVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},g=[],b=a("c7eb"),v=a("1da1");a("d3b7"),a("3ca3"),a("159b"),a("ddb0"),a("2b3d"),a("9861");const w={add(t){return console.log("==================执行新增===================="),new Promise((e,a)=>{var i=JSON.stringify(t);p["a"].post("/D04M07B1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);p["a"].post("/D04M07B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{p["a"].get("/D04M07B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var y=w,x=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[a("el-row",{staticStyle:{display:"flex"}},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",disabled:!!t.formdata.assessor},nativeOn:{click:function(e){return t.getselPwProcess(1)}}},[a("i",{staticClass:"el-icon-plus"}),t._v(" 添加")])],1),a("div",{staticStyle:{display:"flex","margin-left":"20px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px",heigt:"30px"},attrs:{placeholder:"请输入SN","prefix-icon":"el-icon-search",size:"small"},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1)],1),a("div",{staticStyle:{"margin-right":"10px",position:"relative"}},[a("el-button",{staticStyle:{"font-weight":"bold"},attrs:{size:"mini",icon:"el-icon-refresh-right"},nativeOn:{click:function(e){return t.$emit("bindData")}}}),a("el-popover",{attrs:{placement:"top",title:"单据状态",width:"200",trigger:"click"}},[a("div",[a("div",[a("i",{staticClass:"el-icon-document"}),t._v(" 记录数："+t._s(this.formdata.itemcount?this.formdata.itemcount:0)+" ")]),a("div",{staticClass:"text-blue"},[a("i",{staticClass:"el-icon-printer"}),t._v(" 打印数："+t._s(this.formdata.printcount?this.formdata.printcount:0)+" ")]),a("div",{staticClass:"text-green"},[a("i",{staticClass:"el-icon-success"}),t._v(" 完成数："+t._s(this.formdata.finishcount?this.formdata.finishcount:0)+" ")]),a("div",{staticClass:"text-red"},[a("i",{staticClass:"el-icon-remove"}),t._v(" 作废数："+t._s(this.formdata.disannulcount?this.formdata.disannulcount:0)+" ")])]),a("el-button",{staticStyle:{margin:"0 10px"},attrs:{slot:"reference",size:"mini",icon:"el-icon-info"},slot:"reference"})],1),a("el-button",{staticStyle:{float:"right"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}})],1)],1),a("div",{staticClass:"table-container f-1 table-position"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",staticStyle:{overflow:"auto"},attrs:{"summary-method":t.getSummaries,"show-summary":"",data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",size:"small",height:t.tableHeight-42,"header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange,"row-click":t.saveRow}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"ID",width:"55"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e){return[!e.displaymark?t._e():a("el-table-column",{key:e.id,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["currqty"==e.itemcode||"curramt"==e.itemcode||"location"==e.itemcode||"price"==e.itemcode||"batchno"==e.itemcode?a("div",[i.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:e.itemname},on:{input:function(a){return t.changeInput(a,i.row,e.itemcode)},focus:function(t){return t.currentTarget.select()}},model:{value:i.row[e.itemcode],callback:function(a){t.$set(i.row,e.itemcode,a)},expression:"scope.row[i.itemcode]"}}):a("span",[t._v(t._s(i.row[e.itemcode]))])],1):"panyingshuliang"==e.itemcode?a("div",[Number(i.row.quantity)<Number(i.row.currqty)?a("span",{staticStyle:{color:"#67c23a","font-weight":"bold"}},[t._v(t._s((Number(i.row.currqty)-Number(i.row.quantity)).toFixed(2).replace(/\.00$/,"")))]):a("span",[t._v(" 0")])]):"panyingjine"==e.itemcode?a("div",[Number(i.row.amount)<Number(i.row.curramt)?a("span",{staticStyle:{color:"#67c23a","font-weight":"bold"}},[t._v(t._s((Number(i.row.curramt)-Number(i.row.amount)).toFixed(2).replace(/\.00$/,"")))]):a("span",[t._v(" 0")])]):"pankuishuliang"==e.itemcode?a("div",[Number(i.row.quantity)>Number(i.row.currqty)?a("span",{staticStyle:{color:"#f56c6c","font-weight":"bold"}},[t._v(t._s((Number(i.row.quantity)-Number(i.row.currqty)).toFixed(2).replace(/\.00$/,"")))]):a("span",[t._v(" 0")])]):"pankuijine"==e.itemcode?a("div",[Number(i.row.amount)>Number(i.row.curramt)?a("span",{staticStyle:{color:"#f56c6c","font-weight":"bold"}},[t._v(t._s((Number(i.row.amount)-Number(i.row.curramt)).toFixed(2).replace(/\.00$/,"")))]):a("span",[t._v(" 0")])]):a("div",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2)],1),a("pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),t.PwProcessFormVisible?a("el-dialog",{attrs:{title:"库存信息","append-to-body":!0,visible:t.PwProcessFormVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.PwProcessFormVisible=e}}},[a("selPwProcess",{ref:"selPwProcess",attrs:{multi:t.multi,storeid:t.formdata.storeid}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selPwProcess()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.PwProcessFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("Setcolums",{ref:"setcolums",attrs:{code:"D04M07B1Item",tableForm:t.tableForm},on:{bindData:t.getColumn,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},S=[],k=(a("7db0"),a("d81d"),a("13d5"),a("a434"),a("a9e3"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"352px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"编码",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsuid))])]}}])}),a("el-table-column",{attrs:{label:"名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(" "+t._s(e.row.goodsname))])]}}])}),a("el-table-column",{attrs:{label:"规格",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsspec))])]}}])}),a("el-table-column",{attrs:{label:"单位",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.goodsunit))])]}}])}),a("el-table-column",{attrs:{label:"外部编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.partid))])]}}])}),a("el-table-column",{attrs:{label:"数量",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.quantity))])]}}])}),a("el-table-column",{attrs:{label:"SN",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.packsn))])]}}])}),a("el-table-column",{attrs:{label:"仓库名称",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storename))])]}}])}),a("el-table-column",{attrs:{label:"库位编码",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.location))])]}}])}),a("el-table-column",{attrs:{label:"批号",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.batchno))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)}),_=[],P=(a("99af"),a("25f0"),a("4d90"),{components:{Pagination:f["a"]},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0"),r=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(s,":").concat(n,":").concat(r)}}},props:["multi","storeid"],data:function(){return{title:"库存信息",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr=""},mounted:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel")},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.queryParams.SearchType=1,p["a"].post("/D04M04B1/getZeroPageList?storeid="+this.storeid,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total?e.data.data.total:0),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={packsn:t,goodsname:t,goodsuid:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}}),D=P,$=(a("713a"),Object(c["a"])(D,k,_,!1,null,"16fd3fd4",null)),C=$.exports,N=a("da92"),F=a("8daf"),q={formcode:"D04M07B1Th",item:[{itemcode:"refno",itemname:"盘点编号",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Mat_InveNote.refno"},{itemcode:"billtype",itemname:"盘点类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_InveNote.billtype"},{itemcode:"billdate",itemname:"盘点日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_InveNote.billdate"},{itemcode:"inveyear",itemname:"盘点年份",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_InveNote.inveyear"},{itemcode:"invemonth",itemname:"盘点月份",minwidth:"70",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_InveNote.invemonth"},{itemcode:"startdate",itemname:"开始日期",minwidth:"70",displaymark:1,sortable:1,overflow:1,datasheet:"Mat_InveNote.startdate"},{itemcode:"enddate",itemname:"结束日期",minwidth:"70",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_InveNote.enddate"},{itemcode:"storename",itemname:"仓库名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_InveNote.storename"},{itemcode:"summary",itemname:"简述",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_InveNote.summary"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_InveNote.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_InveNote.assessor"},{itemcode:"acceuidin",itemname:"入库单",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_InveNote.acceuidin"},{itemcode:"acceuidout",itemname:"出库单",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_InveNote.acceuidout"}]},M={formcode:"D04M07B1Item",item:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"60",displaymark:1,overflow:1},{itemcode:"packsn",itemname:"SN",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"账面数量",minwidth:"100",displaymark:1,overflow:1},{itemcode:"amount",itemname:"账面金额",minwidth:"100",displaymark:1,overflow:1},{itemcode:"currqty",itemname:"盘点数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"price",itemname:"单价",minwidth:"80",displaymark:1,overflow:1},{itemcode:"curramt",itemname:"盘点金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"panyingshuliang",itemname:"盘盈-数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"panyingjine",itemname:"盘盈-金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"pankuishuliang",itemname:"盘亏-数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"pankuijine",itemname:"盘亏-金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"overflowqty",itemname:"溢出数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"overflowamt",itemname:"溢出金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"finishqty",itemname:"完成数量",minwidth:"80",displaymark:1,overflow:1},{itemcode:"finishamt",itemname:"完成金额",minwidth:"80",displaymark:1,overflow:1},{itemcode:"location",itemname:"库位",minwidth:"80",displaymark:1,overflow:1},{itemcode:"batchno",itemname:"批号",minwidth:"80",displaymark:1,overflow:1}]},O={name:"Elitem",components:{selPwProcess:C,Pagination:f["a"],Setcolums:F["a"]},props:["formdata","lstitem","idx"],data:function(){return{title:"库存盘点",formLabelWidth:"100px",listLoading:!1,PwProcessFormVisible:!1,lst:[],multi:0,billamount:0,selected:!1,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,strfilter:"",getallList:[],queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},total:0,tableForm:M,customList:[],setColumsVisible:!1}},watch:{lstitem:function(t,e){this.lst=this.lstitem},"formdata.assessor":function(t,e){this.formdata.assessor?this.isEditOk=!1:this.isEditOk=!0},lst:function(t,e){void 0==t&&(this.lst=[])}},created:function(){this.lst=[]},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},mounted:function(){this.catchHight(),this.getColumn()},methods:{bindData:function(){var t=this;p["a"].post("/D04M07B1/getItemPageList?key="+this.formdata.id,JSON.stringify(this.queryParams)).then((function(e){if(console.log(e),200==e.data.code){for(var a=0;a<e.data.data.list.length;a++)e.data.data.list[a].overflowqty=Number(e.data.data.list[a].currqty)-Number(e.data.data.list[a].quantity),e.data.data.list[a].overflowamt=Number(e.data.data.list[a].curramt)-Number(e.data.data.list[a].amount);t.lst=e.data.data.list,t.total=e.data.data.total,t.getallList=e.data.data.list}else t.$message.warning("查询盘点表失败")}))},getColumn:function(){var t=this;return Object(v["a"])(Object(b["a"])().mark((function e(){return Object(b["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,p["a"].get("/SaDgFormat/getBillEntityByCode?code=D04M07B1Item").then((function(e){if(200==e.data.code){if(null==e.data.data)return void(t.tableForm=M);t.tableForm=e.data.data}})).catch((function(e){t.$message.error("请求出错")}));case 2:case"end":return e.stop()}}),e)})))()},openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate(),this.$forceUpdate()},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},getSummaries:function(t){var e=t.columns,a=t.data,i=["amount","currqty"],o=[];return e.forEach((function(t,e){if(0!==e){var s=!1;i.length>0&&void 0!=i.find((function(e){return e==t.property}))&&(s=!0);var n=a.map((function(e){return Number(e[t.property])}));!n.every((function(t){return isNaN(t)}))&&s?o[e]=n.reduce((function(t,e){var a=Number(e);return isNaN(a)?Number(t):N["a"].plus(Number(t),Number(e))}),0):o[e]=""}else o[e]="合计"})),this.$forceUpdate(),o},getselPwProcess:function(t){this.formdata.storeid?(this.PwProcessFormVisible=!0,this.multi=t):this.$message.warning("请先选择仓库")},selPwProcess:function(){var t=this.$refs.selPwProcess.$refs.selectVal.selection;if(0!=t.length){console.log(t),this.PwProcessFormVisible=!1;for(var e=0;e<t.length;e++){var a={amount:t[e].amount,batchno:t[e].batchno,curramt:0,currqty:0,endindate:t[e].endindate,endinuid:t[e].endinuid,endoutdate:t[e].endoutdate,endoutuid:t[e].endoutuid,enduid:t[e].enduid,expidate:t[e].expidate,finishamt:0,finishqty:0,goodsid:t[e].goodsid,goodsname:t[e].goodsname,goodsphoto1:t[e].goodsphoto1,goodsspec:t[e].goodsspec,goodsuid:t[e].goodsuid,goodsunit:t[e].goodsunit,inveid:t[e].storeid,itemcode:t[e].goodsuid,itemname:t[e].goodsname,itemspec:t[e].goodsspec,itemunit:t[e].goodsunit,location:t[e].location,overflowamt:0,overflowqty:0,packsn:t[e].packsn,partid:t[e].partid,quantity:0,remark:"",rownum:0};0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("请选择单据内容")},numFormat:function(t){t+="";var e=t.lastIndexOf(".")?t.lastIndexOf("."):0,a=t.length,i=t.substring(e,a);return i>0?t:t.substring(0,e)},changeInput:function(t,e,a){e.overflowqty=Number(e.currqty)-Number(e.quantity),e.overflowamt=Number(e.curramt)-Number(e.amount)},search:function(t){if(""!=t){this.lst=[];for(var e=0;e<this.getallList.length;e++){var a=this.getallList[e];t==a.packsn&&this.lst.push(a)}}else this.lst=this.getallList},handleSelectionChange:function(t){t.length>0?this.selected=!0:this.selected=!1,this.multipleSelection=t},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},deleteRows:function(t,e){var a=this,i=this.multipleSelection;console.log("选中数据",i),i&&i.forEach((function(t,e){a.lst.forEach((function(e,i){t.goodsid===e.goodsid&&t.goodsname===e.goodsname&&a.lst.splice(i,1)}))})),this.$refs.multipleTable.clearSelection(),this.selected=!1},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-34,console.log(" this.tableHeight",t.tableHeight))}))},saveRow:function(t){for(var e=0;e<this.lst.length;e++)this.lst[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0)),this.$forceUpdate()},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a;this.getallList=this.lst}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a;this.getallList=this.lst}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}},V=O,z=(a("8875"),Object(c["a"])(V,x,S,!1,null,"88a64854",null)),B=z.exports,E=a("27f6"),L=a("13df"),T=a("4c0c"),R={name:"Formedit",components:{elitem:B,D04M01B9List:E["a"],selstore:T["a"],D04M01B9:L["default"]},props:["idx"],data:function(){return{title:"仓库盘点",formdata:{item:[],summary:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,acceuidin:"",acceuidout:"",assessor:"",enddate:new Date,startdate:new Date,billdate:new Date,storecode:"",storeid:"",storename:"",invemonth:(new Date).getMonth()+1,billtype:"月度盘点",inveyear:(new Date).getFullYear(),refno:""},formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupid:[{required:!0,trigger:"blur",message:"部门为必填项"}],storeid:[{required:!0,trigger:"blur",message:"仓库名称为必填项"}],direction:[{required:!0,trigger:"blur",message:"操作方向为必填项"}]},formLabelWidth:"100px",multi:0,deptData:[],defaultProps:{children:"children",label:"fullname",value:"id"},selVisible4store:!1,ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",printType:"print",isViewPdf20:!1,WarehousingVisible:!1,processVisible:!1,processTitle:"",processModel:"",operationVisible:!1,dialogIdx:0,ExamineVisible:!1,ExamineData:[],ExamineModel:"",sendType:"wxe"}},computed:{formcontainHeight:function(){return window.innerHeight-50-13-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},destroyed:function(){document.onkeydown=function(){},this.$setWs.wsClose()},methods:{bindData:function(){var t=this;return Object(v["a"])(Object(b["a"])().mark((function e(){return Object(b["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.listLoading=!0,0==t.idx){e.next=6;break}return e.next=4,p["a"].get("/D04M07B1/getEntity?key=".concat(t.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")}));case 4:return e.next=6,t.$refs.elitem.bindData();case 6:case"end":return e.stop()}}),e)})))()},getAllBill:function(){var t=this;return Object(v["a"])(Object(b["a"])().mark((function e(){return Object(b["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.formdata.storeid){e.next=3;break}return t.$message.warning("请先选择仓库"),e.abrupt("return");case 3:return e.next=5,p["a"].post("/D04M07B1/createByNote",JSON.stringify(t.formdata)).then((function(e){200==e.data.code?(e.data.data.item=[],t.formdata=e.data.data,t.$emit("changeIdx",e.data.data.id),t.$message.success("保存成功")):t.$message.warning("保存失败")})).catch((function(e){t.$message.error("请求错误")}));case 5:t.$refs.elitem.bindData();case 6:case"end":return e.stop()}}),e)})))()},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;0!=this.$refs.elitem.lst.length?(this.formdata.item=this.$refs.elitem.lst,this.$delete(this.formdata,"acceuidin"),this.$delete(this.formdata,"acceuidout"),this.formdata.item.forEach((function(e){t.$delete(e,"finishqty"),t.$delete(e,"finishamt")})),0==this.idx?y.add(this.formdata).then((function(e){console.log("res",e),t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data})).catch((function(e){t.$message.warning("保存失败")})):y.update(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data})).catch((function(e){t.$message.warning("保存失败")}))):this.$message.warning("单据内容不能为空")},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){y.delete(t).then((function(){e.$message.success({message:"删除成功"}),e.$emit("compForm")})).catch((function(){e.$message.error({message:"删除失败"})})),console.log(t)})).catch((function(){}))},approval:function(){var t=this;p["a"].get("/D04M07B1/approval?key="+this.formdata.id).then((function(e){200==e.data.code?(t.$message.success("审核成功"),t.formdata=e.data.data,t.$emit("bindData")):t.$message.warning("审核失败")}))},DeApproval:function(){var t=this;p["a"].get("/D04M07B1/approval?key="+this.formdata.id).then((function(e){200==e.data.code?(t.$message.success("反审核成功"),t.formdata=e.data.data,t.$emit("bindData")):t.$message.warning("反审核失败")}))},printButton:function(){var t=this;p["a"].get("/SaReports/getListByModuleCode?code=D04M07B1Edit").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0}))},submitReport:function(){var t=this;""!=this.reportModel?p["a"].get("/D04M07B1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.formdata.printcount+=1,t.ReportVisible=!1;var a=[];a.push(e.data);var i=window.URL.createObjectURL(new Blob(a,{type:"application/pdf"}));t.pdfUrl=i,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},submitRemoteReport:function(){var t=this;if(""!=this.reportModel){var e="/D04M07B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel;"preview"==this.printType&&(e="/D04M07B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel+"&cmd=1"),p["a"].get(e).then((function(e){200==e.data.code?null==e.data.data||""==e.data.data?t.$message.warning(e.data.msg||"打印失败"):"连接成功"!=t.$store.state.webSocketMsg.webSocketState?(t.$setWs.initWebSocket("ws://127.0.0.1:18800"),setTimeout((function(){t.remoteprinting(e.data.data)}),500)):setTimeout((function(){t.remoteprinting(e.data.data)}),100):t.$message.warning(e.data.msg||"打印失败")}))}else this.$message.warning("打印模板不能为空!")},remoteprinting:function(t){"连接成功"==this.$store.state.webSocketMsg.webSocketState?(this.formdata.printcount+=1,this.$setWs.wsSend(t),this.ReportVisible=!1):this.$message.warning("连接打印服务器失败")},selectStore:function(t){var e=t;this.formdata.storecode=e.storecode,this.formdata.storename=e.storename,this.formdata.storeid=e.id,this.selVisible4store=!1,this.$refs.formdata.clearValidate("storeid"),this.$refs.elitem.lst=[]},flowable:function(t){var e=this;return Object(v["a"])(Object(b["a"])().mark((function t(){return Object(b["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.ExamineData=[],!JSON.parse(e.$store.getters.userinfo.configs["system.wxe.apprstart"])){t.next=4;break}return t.next=4,p["a"].get("/utils/D96M13B1/getListByModuleCode?code=D04M07B1Edit").then((function(t){for(var a=0;a<t.data.data.length;a++){var i=t.data.data[a];i.sendType="wxe",e.ExamineData.push(i)}}));case 4:if(!JSON.parse(e.$store.getters.userinfo.configs["system.ding.apprstart"])){t.next=7;break}return t.next=7,p["a"].get("/utils/D96M13B2/getListByModuleCode?code=D04M07B1Edit").then((function(t){for(var a=0;a<t.data.data.length;a++){var i=t.data.data[a];i.sendType="ding",e.ExamineData.push(i)}}));case 7:e.$nextTick((function(){0!=e.ExamineData.length&&(e.ExamineModel=e.ExamineData[0].id,e.sendType=e.ExamineData[0].sendType),e.ExamineVisible=!0}));case 8:case"end":return t.stop()}}),t)})))()},changeExamineModel:function(t){var e=this;this.ExamineData.forEach((function(a){a.id==t&&(e.sendType=a.sendType,e.ExamineModel=a.id)}))},submitExamine:function(){var t=this;""!=this.ExamineModel?p["a"].get("/D04M07B1/sendapprovel?key="+this.idx+"&apprid="+this.ExamineModel+"&type="+this.sendType).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"通知发送成功"),t.ExamineVisible=!1):t.$message.warning(e.data.msg||"通知发送失败")})):this.$message.warning("审批模板不能为空!")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},changeIdx:function(t){this.dialogIdx=t},generate:function(){var t=this;p["a"].get("/D04M07B1/createAccess?key="+this.idx).then((function(e){200==e.data.code?(t.processVisible=!0,t.processTitle="盈亏查看",t.processModel="D04M01B9",t.$message.success(e.data.msg||"生成盈亏成")):t.$message.warning(e.data.msg||"生成盈亏失败")})).catch((function(e){t.$message.error("请求错误")}))},handleFocus:function(){this.selVisible4group=!0},handleBlur:function(){this.selVisible4group=!1},handleChange:function(t){t.length>0?this.formdata.groupid=t[t.length-1]:this.formdata.groupid=""},changeFormat:function(t){var e=[];if(!Array.isArray(t))return e;t.forEach((function(t){delete t.children}));var a={};return t.forEach((function(t){a[t.id]=t})),t.forEach((function(t){var i=a[t.parentid];i?(i.children||(i.children=[])).push(t):e.push(t)})),e}}},j=R,I=(a("5f65"),Object(c["a"])(j,h,g,!1,null,"2136b2a8",null)),H=I.exports,J=a("0521"),U={name:"D04M07B1",components:{Pagination:f["a"],listheader:u,formedit:H,helpmodel:J["a"]},data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:q,showhelp:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){this.bindData(),this.getColumn()},methods:{bindData:function(){var t=this;this.listLoading=!0,this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(r["d"])()[0],EndDate:Object(r["d"])()[1]}),this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),p["a"].post("/D04M07B1/getPageTh",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getColumn:function(){var t=this;p["a"].get("/SaDgFormat/getBillEntityByCode?code=D04M07B1Th").then((function(e){if(200==e.data.code){if(null==e.data.data)return void(t.tableForm=q);t.tableForm=e.data.data}})).catch((function(e){t.$message.error("请求出错")}))},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){t.dateRange?this.queryParams.DateRange={StartDate:Object(r["c"])(t.dateRange[0]),EndDate:Object(r["b"])(t.dateRange[1])}:this.$delete(this.queryParams,"DateRange");var e=t.strfilter;""!=e?this.queryParams.SearchPojo={refno:e,billtype:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){t.dateRange?this.queryParams.DateRange={StartDate:Object(r["c"])(t.dateRange[0]),EndDate:Object(r["b"])(t.dateRange[1])}:this.$delete(this.queryParams,"DateRange"),this.queryParams.scenedata=t.formdata,""==t.formdata[0].field?this.queryParams.SearchType=1:this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showForm:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(t,this.tableForm),this.bindData()}}},W=U,A=(a("6676"),Object(c["a"])(W,i,o,!1,null,"ce9cd6e0",null));e["default"]=A.exports},"4c0c":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selStore",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?a("el-table-column",{attrs:{fixed:"left",type:"selection",width:"55"}}):a("el-table-column",{attrs:{label:"",width:"40",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index,size:"small"},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" ")+" ")])]}}])}),a("el-table-column",{attrs:{label:"仓库编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storecode))])]}}])}),a("el-table-column",{attrs:{label:"仓库名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storename))])]}}])}),a("el-table-column",{attrs:{label:"仓库地址",align:"center","min-width":"150","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storeadd))])]}}])}),a("el-table-column",{attrs:{label:"仓管员",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.operator))])]}}])}),a("el-table-column",{attrs:{label:"电话",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storetel))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},o=[],s=(a("e9c4"),a("b775")),n=a("333d"),r={components:{Pagination:n["a"]},props:["multi","storeid"],data:function(){return{title:"选择仓库",listLoading:!0,lst:[],strfilter:"",radio:"",selrows:"",total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},watch:{},mounted:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.$emit("singleSel",t),this.selrows=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,this.storeid&&(this.queryParams.SearchPojo={storeid:this.storeid}),s["a"].post("/D04M21S1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={storename:t,storecode:t,operator:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},l=r,d=(a("6dd7"),a("2877")),c=Object(d["a"])(l,i,o,!1,null,"655e468e",null);e["a"]=c.exports},"519f":function(t,e,a){},"5e51":function(t,e,a){},"5f1f":function(t,e,a){},"5f65":function(t,e,a){"use strict";a("519f")},6676:function(t,e,a){"use strict";a("f73c")},"6dd7":function(t,e,a){"use strict";a("5f1f")},"70f2":function(t,e,a){},"713a":function(t,e,a){"use strict";a("5e51")},8875:function(t,e,a){"use strict";a("1134")},9227:function(t,e,a){"use strict";a("70f2")},b83a:function(t,e,a){"use strict";a("11f6")},f73c:function(t,e,a){}}]);