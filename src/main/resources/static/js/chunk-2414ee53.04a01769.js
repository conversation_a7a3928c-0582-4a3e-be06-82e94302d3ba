(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2414ee53"],{1969:function(t,e,i){},"29ff":function(t,e,i){},"5cc6":function(t,e,i){var a=i("74e8");a("Uint8",(function(t){return function(e,i,a){return t(this,e,i,a)}}))},"7e26":function(t,e,i){},"965c":function(t,e,i){"use strict";i("7e26")},9970:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selStore",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"360px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{fixed:"left",type:"selection",width:"55"}}):i("el-table-column",{attrs:{label:"",width:"40",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"库位编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.spacecode))])]}}])}),i("el-table-column",{attrs:{label:"库位名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.spacename))])]}}])}),i("el-table-column",{attrs:{label:"仓库编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.storecode))])]}}])}),i("el-table-column",{attrs:{label:"仓库名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.storename))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},o=[],n=(i("e9c4"),i("b775")),s=i("333d"),r={components:{Pagination:s["a"]},props:["multi"],data:function(){return{title:"选择库位",listLoading:!0,lst:[],searchstr:" ",strfilter:"",radio:"",selrows:"",total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},watch:{},mounted:function(){},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.$emit("singleSel",t),this.selrows=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,n["a"].post("/D04M21S2/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={storename:t,storecode:t,operator:t,spacecode:t,spacename:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},l=r,c=(i("aedd"),i("2877")),d=Object(c["a"])(l,a,o,!1,null,"5c0a2726",null);e["a"]=d.exports},aecf:function(t,e,i){},aedd:function(t,e,i){"use strict";i("aecf")},b72e:function(t,e,i){"use strict";i("1969")},bf19:function(t,e,i){"use strict";var a=i("23e7");a({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return URL.prototype.toString.call(this)}})},c03b:function(t,e,i){"use strict";i("29ff")},c19f:function(t,e,i){"use strict";var a=i("23e7"),o=i("da84"),n=i("621a"),s=i("2626"),r="ArrayBuffer",l=n[r],c=o[r];a({global:!0,forced:c!==l},{ArrayBuffer:l}),s(r)},fa41:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{ref:"index",staticClass:"index"},[i("ListHeader",{ref:"listheader",attrs:{showTree:t.treeVisble,tableForm:t.tableForm},on:{btnshowGroup:function(e){t.treeVisble=!t.treeVisble},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,btnExport:t.btnExport,Export:t.Export,btnHelp:t.btnHelp,splitQty:t.splitQty,moveLocation:t.moveLocation,changeBalance:t.changeBalance,bindColumn:t.getColumn}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{directives:[{name:"show",rawName:"v-show",value:t.treeVisble,expression:"treeVisble"}],attrs:{span:3}},[i("div",[i("div",{staticClass:"groupTitle"},[i("span",[t._v("分组")])]),i("el-tree",{staticStyle:{"font-size":"14px",height:"80vh",overflow:"auto"},attrs:{data:t.groupData,"node-key":"id","default-expand-all":""},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.node,o=e.data;return i("span",{staticClass:"custom-tree-node",on:{click:function(e){return e.stopPropagation(),function(){return t.handleNodeClick(o)}()}}},[i("span",[t._v(t._s(a.label))])])}}])})],1)]),i("el-col",{attrs:{span:t.treeVisble?t.showhelp?17:21:t.showhelp?20:24}},[i("ve-table",{ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"border-x":"","border-y":"","border-around":!0,"column-width-resize-option":t.columnWidthResizeOption,"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getlist}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:"D04M04B1List"},on:{bindData:t.bindData}})],1)])],1),i("el-col",{attrs:{span:t.showhelp?4:0}},[i("HelpModel",{ref:"helpmodel",attrs:{code:"D04M04B1"}})],1)],1)],1)],1),t.drawerVisible?i("el-dialog",{attrs:{title:"出入库明细",visible:t.drawerVisible,width:"80vw"},on:{"update:visible":function(e){t.drawerVisible=e}}},[i("div",{staticClass:"export-content"},[t.drawerVisible?i("Search4Goodsid",t._g({ref:"search4goodsid",staticStyle:{padding:"0 10px"},attrs:{goodsid:t.idx}},{drawclose:t.drawclose})):t._e()],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(e){t.drawerVisible=!1}}},[t._v("确 定")])],1)]):t._e(),t.exportInfoVisble?i("el-dialog",{attrs:{title:"批量导出",visible:t.exportInfoVisble,width:"400px"},on:{"update:visible":function(e){t.exportInfoVisble=e}}},[i("div",{staticClass:"export-content"},[i("div",{staticStyle:{"margin-bottom":"10px",display:"flex","align-items":"center","justify-content":"space-around"}},[i("span",[t._v("页码")]),i("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"small",min:1},model:{value:t.exportInfo.PageNum,callback:function(e){t.$set(t.exportInfo,"PageNum",e)},expression:"exportInfo.PageNum"}})],1),i("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-around"}},[i("span",[t._v("数量")]),i("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"small",min:1,step:100},model:{value:t.exportInfo.PageSize,callback:function(e){t.$set(t.exportInfo,"PageSize",e)},expression:"exportInfo.PageSize"}})],1)]),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:t.submitExport}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.exportInfoVisble=!1}}},[t._v("取 消")])],1)]):t._e(),i("el-dialog",{attrs:{title:"拆分数量","append-to-body":!0,width:"400px","v-if":t.splitVisible,visible:t.splitVisible,"close-on-click-modal":!1},on:{"update:visible":function(e){t.splitVisible=e}}},[i("div",[i("el-input",{attrs:{size:"small",placeholder:"请输入拆分数量"},model:{value:t.splitQuantity,callback:function(e){t.splitQuantity=e},expression:"splitQuantity"}})],1),i("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between","margin-top":"10px"}},[i("el-checkbox",{attrs:{label:"标签打印","true-label":1,"false-label":0,size:"mini"},model:{value:t.isPrint,callback:function(e){t.isPrint=e},expression:"isPrint"}}),i("el-select",{attrs:{placeholder:"请选择打印机",size:"small"},model:{value:t.PrinterVal,callback:function(e){t.PrinterVal=e},expression:"PrinterVal"}},t._l(t.PrinterData,(function(t){return i("el-option",{key:t.printersn,attrs:{label:t.printername,value:t.printersn}})})),1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitsplitQty}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.splitVisible=!1}}},[t._v("取 消")])],1)]),i("el-dialog",{attrs:{title:"移动库位","append-to-body":!0,width:"400px","v-if":t.moveLocationVisible,visible:t.moveLocationVisible,"close-on-click-modal":!1},on:{"update:visible":function(e){t.moveLocationVisible=e}}},[i("el-form",{ref:"form",attrs:{"label-width":"80px"}},[i("el-form-item",{attrs:{label:"库位"}},[i("el-popover",{attrs:{placement:"bottom-start",trigger:"click"},on:{show:function(e){return t.$refs.selLocation.bindData()}},model:{value:t.locationPopover,callback:function(e){t.locationPopover=e},expression:"locationPopover"}},[i("SelLocation",{ref:"selLocation",staticStyle:{width:"600px",height:"420px"},attrs:{multi:0},on:{singleSel:t.selLocation}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{staticStyle:{},attrs:{placeholder:"调入库位",size:"small",readonly:""},model:{value:t.formdata.location,callback:function(e){t.$set(t.formdata,"location",e)},expression:"formdata.location"}},[i("i",{staticClass:"el-icon-circle-plus-outline",staticStyle:{"font-size":"15px",color:"rgb(64, 158, 255)"},attrs:{slot:"suffix"},slot:"suffix"})])],1)],1)],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitMoveLocation}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.moveLocationVisible=!1}}},[t._v("取 消")])],1)],1)],1)},o=[],n=i("2909"),s=i("c7eb"),r=i("1da1"),l=(i("99af"),i("d81d"),i("e9c4"),i("a9e3"),i("b64b"),i("d3b7"),i("3ca3"),i("c7cd"),i("159b"),i("ddb0"),i("2b3d"),i("9861"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[i("div",{staticStyle:{display:"flex"}},[i("div",{staticClass:"showgroup",on:{click:t.btnshowGroup}},[i("i",{class:[t.showTree?"el-icon-s-fold":"el-icon-s-unfold"],staticStyle:{"font-size":"20px",top:"4px"},attrs:{title:"分组"}})]),i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-download",plain:"",size:"mini"},on:{click:t.Export}},[t._v(" 批量导出 ")]),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-shopping-cart-full",plain:"",size:"mini"},on:{click:t.moveLocation}},[t._v(" 移动库位 ")]),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-copy-document",plain:"",size:"mini"},on:{click:t.splitQty}},[t._v(" 拆分数量 ")])],1),i("div",{staticClass:"iShowBtn"},[i("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"结存",size:"mini",border:""},on:{change:t.changeBalance},model:{value:t.balance,callback:function(e){t.balance=e},expression:"balance"}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.code?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}})],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("Setcolums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.code,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),c=[],d={name:"Listheader",props:["showTree","tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},balance:!1,code:"D04M04B1List",setColumsVisible:!1,searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(t){this.iShow=!1,this.$emit("advancedSearch",t)},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnAdd:function(){this.$emit("btnAdd")},btnshowGroup:function(){this.$emit("btnshowGroup")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")},Export:function(){this.$emit("Export")},moveLocation:function(){this.$emit("moveLocation")},splitQty:function(){this.$emit("splitQty")},changeBalance:function(t){var e=0;e=t?1:0,this.$emit("changeBalance",e)}}},m=d,u=(i("c03b"),i("2877")),p=Object(u["a"])(m,l,c,!1,null,"28e050e1",null),f=p.exports,h=i("b775"),b=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("div"),i("div",[i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}})],1)]),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selStore",staticStyle:{overflow:"auto","min-height":"430px"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[i("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,a){return[!e.displaymark?t._e():i("el-table-column",{key:a,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(a){return["spumark"==e.itemcode?i("div",[a.row.attributejson?i("span",[t._v("是")]):t._e()]):"billdate"==e.itemcode?i("span",[t._v(t._s(t._f("dateFormat")(a.row[e.itemcode])))]):i("span",[t._v(t._s(a.row[e.itemcode]))])]}}],null,!0)})]}))],2)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("Setcolums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.bindData,closeDialog:function(e){t.setColumsVisible=!1}}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitUpdate()}}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.setColumsVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},g=[],v=i("333d"),y=i("48da"),w={formcode:"D04M04B1List",item:[{itemcode:"storename",itemname:"仓库名称",minwidth:"100",defwidth:"",displaymark:1,fixed:1,sortable:0,overflow:1,aligntype:"center",datasheet:"Mat_Storage.storename"},{itemcode:"spumark",itemname:"SPU",minwidth:"60",fixed:1,displaymark:1,overflow:1,datasheet:"Mat_Storage.spumark"},{itemcode:"goodsuid",itemname:"货品编码",minwidth:"120",sortable:1,fixed:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsuid"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"120",sortable:1,fixed:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsname"},{itemcode:"goodsspec",itemname:"货品规格",minwidth:"100",displaymark:1,fixed:1,overflow:1,datasheet:"Mat_Goods.goodsspec"},{itemcode:"goodsunit",itemname:"货品单位",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Goods.goodsunit"},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Mat_Goods.partid"},{itemcode:"quantity",itemname:"数量",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Inventory.quantity"},{itemcode:"amount",itemname:"金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Inventory.amount"},{itemcode:"ivquantity",itemname:"库存总数",minwidth:"120",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Goods.ivquantity"},{itemcode:"location",itemname:"库位编码",minwidth:"120",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Inventory.location"},{itemcode:"packsn",itemname:"SN",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Inventory.packsn"},{itemcode:"batchno",itemname:"批号",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Mat_Inventory.batchno"},{itemcode:"endinuid",itemname:"末次入库",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Inventory.endinuid"},{itemcode:"endoutuid",itemname:"末次出库",minwidth:"100",displaymark:1,overflow:1,datasheet:"Mat_Inventory.endoutuid"}]},x={formcode:"D01M01B4Search",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"billtype",itemname:"单据类型",minwidth:"60",displaymark:1,overflow:1},{itemcode:"billdate",itemname:"单据日期",minwidth:"60",displaymark:1,overflow:1},{itemcode:"billtype",itemname:"单据主题",minwidth:"60",displaymark:1,overflow:1},{itemcode:"groupname",itemname:"客户名称",minwidth:"60",displaymark:1,overflow:1},{itemcode:"goodsname",itemname:"名称",minwidth:"60",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"60",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"60",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"60",displaymark:1,overflow:1},{itemcode:"location",itemname:"库位编码",minwidth:"60",displaymark:1,overflow:1},{itemcode:"batchno",itemname:"批号",minwidth:"60",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"60",displaymark:1,overflow:1}]},k=i("8daf"),S={components:{Pagination:v["a"],Setcolums:k["a"]},props:["goodsid"],data:function(){return{title:"选择仓库",listLoading:!0,lst:[],strfilter:"",radio:"",selrows:"",total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:0},tableForm:x,setColumsVisible:!1}},watch:{},created:function(){this.bindData()},methods:{getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,this.queryParams.SearchPojo={goodsid:this.goodsid},h["a"].post("/D04M01R1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},btnExport:function(){var t=this;Promise.resolve().then(function(){for(var e=[],i=[],a=0;a<t.tableForm.item.length;a++){var o=t.tableForm.item[a];o.displaymark&&o.displaymark&&(e.push(o.itemname),i.push(o.itemcode))}var n=t.lst,s=t.formatJson(i,n);Object(y["a"])(e,s,NaN)}.bind(null,i)).catch(i.oe)},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))}}},P=S,L=(i("965c"),Object(u["a"])(P,b,g,!1,null,"f50784fe",null)),_=L.exports,D=i("9970"),C={name:"D04M04B1",components:{ListHeader:f,Search4Goodsid:_,SelLocation:D["a"]},data:function(){var t=this;return{title:"库存信息",lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},drawerVisible:!1,drawerSize:"50%",treeVisble:!0,groupData:[],pid:0,treeEdit:!1,exportInfoVisble:!1,exportInfo:{PageNum:1,PageSize:100},selectionList:[],splitQuantity:0,splitVisible:!1,isPrint:1,PrinterVal:"",PrinterData:[],locationPopover:!1,moveLocationVisible:!1,formdata:{location:"",storeid:"",storecode:"",storename:"",id:""},tableForm:w,customList:[],baseUrl:"/D04M04B1/getPageList",showhelp:!1,columnHidden:[],columnWidthResizeOption:{enable:!0,minWidth:50,sizeChange:function(e){var i=e.column,a=e.differWidth,o=e.columnWidth;t.columnResizeInfo.column=i,t.columnResizeInfo.differWidth=a,t.columnResizeInfo.columnWidth=o}},columnResizeInfo:{column:"",differWidth:"",columnWidth:""},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectionList=[],0!=i.length)for(var a=0;a<i.length;a++)t.selectionList.push(i[a])},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectionList=i?t.lst:[]}},footerData:[],customData:[],eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}},cellTotal:0,cellNum:0}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},created:function(){this.BindStoreGoods()},mounted:function(){this.bindData(),this.getColumn()},methods:{bindData:function(){var t=this;this.listLoading=!0,this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),h["a"].post(this.baseUrl,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var i=0;i<t.lst.length;i++)for(var a=t.lst[i],o=a.attributejson?JSON.parse(a.attributejson):[],n=0;n<o.length;n++)t.$set(t.lst[i],o[n].key,o[n].value)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getColumn:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(t.tableForm.formcode,w,1).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("billdate"==t.itemcode||"createdate"==t.itemcode)return e.$options.filters.dateFormat(o[t.itemcode]);if("goodsname"==t.itemcode){var n=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showGoodsDraw(o.goodsid)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]);return n}if("spumark"==t.itemcode){n="";return o.attributejson&&(n=a("span",["是"])),n}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.customData=i},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,i=["quantity","ivquantity"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,["refno","quantity","ivquantity"])},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},changeBalance:function(t){this.baseUrl=t?"/D04M04B1/getOnlinePageList":"/D04M04B1/getPageList",this.bindData()},btnExport:function(){this.$btnExport(this.lst,this.tableForm,"库存信息")},handleSelectionChange:function(t){this.selectionList=t},getlist:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t)},advancedSearch:function(t){this.$advancedSearch(this,t)},moveLocation:function(){0!=this.selectionList.length?(this.moveLocationVisible=!0,this.formdata.location="",this.formdata.id="",this.formdata.storeid="",this.formdata.storename="",this.formdata.storecode=""):this.$message.warning("至少选择一个货品")},submitMoveLocation:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){var i,a,o,n,r,l;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(i=t,a="",a=1==t.selectionList.length?t.selectionList[0].goodsuid+"转入"+t.formdata.storename+"-"+t.formdata.location:"多个转入"+t.formdata.storename+"-"+t.formdata.location,o={billtitle:a,billtype:"转移仓库",billdate:new Date,outstoreid:t.formdata.storeid,outstorecode:t.formdata.storecode,outstorename:t.formdata.storename,instoreid:t.formdata.storeid,instorecode:t.formdata.storecode,instorename:t.formdata.storename,item:[]},n=0;n<t.selectionList.length;n++)r=t.selectionList[n],l={goodsid:r.goodsid,goodsuid:r.goodsuid,goodsname:r.goodsname,goodsunit:r.goodsunit,goodsspec:r.goodsspec,partid:r.partid,batchno:r.batchno,inlocation:t.formdata.location,inveid:r.id,outlocation:r.location,quantity:r.quantity,packsn:r.packsn,rownum:n},o.item.push(l);h["a"].post("/D04M02B1/create",JSON.stringify(o)).then((function(t){200==t.data.code?(i.$message.success("移动库位成功"),i.moveLocationVisible=!1,i.bindData()):i.$message.warning("移动库位失败")}));case 6:case"end":return e.stop()}}),e)})))()},selLocation:function(t){console.log(t),this.formdata.location=t.spacecode,this.formdata.id=t.id,this.formdata.storeid=t.storeid,this.formdata.storename=t.storename,this.formdata.storecode=t.storecode,this.locationPopover=!1},splitQty:function(){1==this.selectionList.length?(this.splitVisible=!0,this.splitQuantity=this.selectionList[0].quantity,this.getPrinter()):this.$message.warning("请选择一个货品")},submitsplitQty:function(){var t=this,e=this.selectionList[0];if(this.splitQuantity>this.selectionList[0].quantity)this.$message.warning("拆分数量不能超过库存数量");else{var i={goodsid:e.goodsid,goodsuid:e.goodsuid,goodsname:e.goodsname,goodsunit:e.goodsunit,goodsspec:e.goodsspec,partid:e.partid,accesstype:0,quantity:Number(this.splitQuantity),batchno:e.batchno,location:e.location,packsn:e.packsn,inveid:e.id,price:0,amount:0,remark:"",rownum:0},a=Object.assign({},i);a.quantity=Number(this.splitQuantity),a.accesstype=1,a.inveid="",a.packsn=i.packsn+"-1";var o={billdate:new Date,billtype:"拆分数量",instorecode:e.storecode,instoreid:e.storeid,instorename:e.storename,outstorecode:e.storecode,outstoreid:e.storeid,outstorename:e.storename,item:[]};o.item=[],o.item.push(i),o.item.push(a),h["a"].post("/D04M12B1/create",JSON.stringify(o)).then((function(e){if(200==e.data.code){if(t.$message.success("拆分数量成功"),t.bindData(),t.splitVisible=!1,t.isPrint)for(var i=e.data.data,a=0;a<i.item.length;a++)0!=i.item[a].quantity&&t.submitPrinter(i.item[a].id)}else t.$message.warning("拆分数量失败")}))}},getPrinter:function(){var t=this;h["a"].get("/SaDgFormat/getListByModuleCode?code=D04M12B1").then((function(e){200==e.data.code?(t.PrinterData=e.data.data,0!=t.PrinterData.length?(t.PrinterVal=t.PrinterData[0].printersn,t.isPrint=1):t.isPrint=0):(t.isPrint=0,t.$message.warning("获取打印机设备失败"))}))},submitPrinter:function(t){var e=this;h["a"].get("/D04M12B1/printWsLabel?key="+t+"&sn="+this.PrinterVal).then((function(t){200==t.data.code?e.$message.success("打印成功"):e.$message.warning("打印失败")}))},showGoodsDraw:function(t){this.idx=t,this.drawerVisible=!0},drawclose:function(){this.drawerVisible=!1},Export:function(){this.exportInfoVisble=!0},submitExport:function(){var t=this,e={PageNum:this.exportInfo.PageNum,PageSize:this.exportInfo.PageSize,OrderType:1,SearchType:1};h["a"].post("/D04M04B1/exportOnlineList",JSON.stringify(e),{responseType:"blob"}).then((function(e){var i=document.createElement("a"),a=new Blob([e.data],{type:"application/vnd.ms-excel"});i.style.display="none",i.href=URL.createObjectURL(a),i.download="库存信息",document.body.appendChild(i),i.click(),t.exportInfoVisble=!1}))},BindStoreGoods:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function e(){var i,a,o,r;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.listLoading=!0,i=[],a={PageNum:1,PageSize:1e4,OrderType:1,SearchType:1},e.next=5,h["a"].post("/D04M21S1/getPageList",JSON.stringify(a)).then((function(e){if(200==e.data.code){var a=e.data.data.list.map((function(t){return{id:t.id,pid:"store",label:t.storename}}));i=[].concat(Object(n["a"])(i),Object(n["a"])(a))}t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 5:o=[{id:"store",pid:-1,label:"仓库名称"}],r=[].concat(Object(n["a"])(i),o),t.groupData=t.transData(r,"id","pid","children");case 8:case"end":return e.stop()}}),e)})))()},transData:function(t,e,i,a){for(var o=[],n={},s=e,r=i,l=a,c=0,d=0,m=t.length;c<m;c++)n[t[c][s]]=t[c];for(;d<m;d++){var u=t[d],p=n[u[r]];p?(!p[l]&&(p[l]=[]),p[l].push(u)):o.push(u)}return o},handleNodeClick:function(t){if(console.log(t),-1==t.pid)this.$refs.listheader.balance?this.baseUrl="/D04M04B1/getOnlinePageList":this.baseUrl="/D04M04B1/getPageList",this.bindData();else{var e=t.id;this.$refs.listheader.balance?this.baseUrl="/D04M04B1/getOnlinePageList?storeid="+e:this.baseUrl="/D04M04B1/getPageList?store="+e,this.bindData()}}}},$=C,z=(i("b72e"),Object(u["a"])($,a,o,!1,null,"0590425c",null));e["default"]=z.exports}}]);