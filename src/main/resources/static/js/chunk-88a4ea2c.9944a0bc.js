(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-88a4ea2c"],{"0a6d3":function(t,e,a){},"0c51":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.groupuid))])]}}])}),a("el-table-column",{attrs:{label:"简称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.abbreviate))])]}}])}),a("el-table-column",{attrs:{label:"名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.groupname))])]}}])}),a("el-table-column",{attrs:{label:"客户类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.grouptype))])]}}])}),a("el-table-column",{attrs:{label:"客户等级",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.grouplevel))])]}}])}),a("el-table-column",{attrs:{label:"联系人",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.linkman))])]}}])})],1)],1),a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},o=[],l=(a("e9c4"),a("b775")),s={props:["multi","groupid"],data:function(){return{title:"客户信息",listLoading:!0,lst:[],strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},mounted:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;l["a"].post("/D01M01B1/getOnlinePageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={groupuid:t,groupname:t,abbreviate:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},n=s,r=(a("1af6"),a("2877")),c=Object(r["a"])(n,i,o,!1,null,"675734f0",null);e["a"]=c.exports},"0fbe":function(t,e,a){"use strict";a("9473")},"1af6":function(t,e,a){"use strict";a("93684")},"1eef":function(t,e,a){"use strict";a("ba1c")},"1f558":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.groupuid))])]}}])}),a("el-table-column",{attrs:{label:"简称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.abbreviate))])]}}])}),a("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.groupname))])]}}])}),a("el-table-column",{attrs:{label:"类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.grouptype))])]}}])}),a("el-table-column",{attrs:{label:"等级",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.grouplevel))])]}}])}),a("el-table-column",{attrs:{label:"联系人",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.linkman))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},o=[],l=(a("e9c4"),a("b775")),s=a("333d"),n={components:{Pagination:s["a"]},props:["multi"],data:function(){return{title:"供应商信息",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr=""},mounted:function(){this.bindData()},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,l["a"].post("/D01M01B2/getOnlinePageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={groupuid:t,groupname:t,abbreviate:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},r=n,c=(a("3abc"),a("2877")),d=Object(c["a"])(r,i,o,!1,null,"de036698",null);e["a"]=d.exports},"3abc":function(t,e,a){"use strict";a("0a6d3")},5301:function(t,e,a){},"5cc6":function(t,e,a){var i=a("74e8");i("Uint8",(function(t){return function(e,a,i){return t(this,e,a,i)}}))},"68a7":function(t,e,a){"use strict";a("eed5")},"72a7":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx}))],1):a("div",{ref:"index",staticClass:"index"},[a("listheader",{directives:[{name:"show",rawName:"v-show",value:!t.isDialog,expression:"!isDialog"}],attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,btnExport:t.btnExport,btnHelp:t.btnHelp}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:t.showhelp?20:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"table-container",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"1px 0px 1px 0px"},height:t.tableMaxHeight},on:{"sort-change":t.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["refno"==e.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showForm(i.row.id)}}},[t._v(t._s(i.row.refno?i.row.refno:"编码"))]):"billdate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))]):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}}),a("div",{staticStyle:{"margin-right":"40px"}},[a("scene",{ref:"scene",attrs:{code:"D07M02B1Th"},on:{bindData:t.bindData}})],1)],1)],1),a("el-col",{attrs:{span:t.showhelp?4:0}},[a("helpmodel",{ref:"helpmodel",attrs:{code:"D07M02B1"}})],1)],1)],1)],1)])},o=[],l=(a("d81d"),a("e9c4"),a("b64b"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",{staticStyle:{display:"flex"}},[a("div",{staticClass:"flex infoForm"},[a("span",{staticClass:"infoForm-Title"},[t._v("时间范围")]),a("el-date-picker",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"unlink-panels":"",size:"mini","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.code?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-help",title:"帮助"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("Setcolums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:t.bindData,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),a("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[a("searchForm",{ref:"searchForm",attrs:{code:t.code,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),s=[],n=a("8daf"),r=a("b893"),c=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"sceneContent"},[a("div",{staticClass:"sceneItem"},[a("div",{staticClass:"sceneTitle"},[t._v("查询条件")]),t._l(t.jsondata,(function(e,i){return a("div",{key:i,staticClass:"screen"},[a("div",{staticClass:"screen-item"},[a("el-select",{staticStyle:{width:"180px"},attrs:{placeholder:"请选择要查询的字段",clearable:""},on:{change:function(a){return t.filiterType(a,e)},clear:function(t){e.field="",e.math="",e.value="",e.fieldtype=0}},model:{value:e.field,callback:function(a){t.$set(e,"field",a)},expression:"i.field"}},t._l(t.fieldData,(function(t){return a("el-option",{key:t.fieldcode,attrs:{label:t.fieldname,value:t.fieldcode}})})),1)],1),a("div",{staticClass:"screen-item"},[a("el-select",{staticStyle:{width:"120px"},attrs:{placeholder:"判断条件"},on:{change:function(a){return t.changeMath(a,e)}},model:{value:e.math,callback:function(a){t.$set(e,"math",a)},expression:"i.math"}},[0==e.fieldtype?t._l(t.textType,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})):t._e(),1==e.fieldtype?t._l(t.numType,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})):t._e(),2==e.fieldtype?t._l(t.dateType,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})):t._e(),3==e.fieldtype?t._l(t.boolType,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})):t._e()],2)],1),a("div",{staticClass:"screen-item"},[2==e.fieldtype?a("div",{staticClass:"dateStyle",staticStyle:{width:"300px"}},[a("el-date-picker",{staticStyle:{width:"50%"},attrs:{type:"date","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},on:{change:function(a){return t.changeDate(a,e,"value")}},model:{value:e.value,callback:function(a){t.$set(e,"value",a)},expression:"i.value"}}),a("span",{staticStyle:{margin:"0 4px"}},[t._v(" - ")]),a("el-date-picker",{staticStyle:{width:"50%"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss",type:"date",placeholder:"选择日期"},on:{change:function(a){return t.changeDate(a,e,"valueb")}},model:{value:e.valueb,callback:function(a){t.$set(e,"valueb",a)},expression:"i.valueb"}})],1):3==e.fieldtype?a("div"):a("div",{staticStyle:{width:"300px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"查询条件"},model:{value:e.value,callback:function(a){t.$set(e,"value",a)},expression:"i.value"}})],1)]),a("div",{directives:[{name:"show",rawName:"v-show",value:0!=i,expression:"index != 0"}],on:{click:function(e){return t.deleteBtn(i)}}},[a("i",{staticClass:"el-icon-delete deleteBtn"})])])})),a("div",{staticClass:"addBtn",on:{click:function(e){return t.addBtn()}}},[a("i",{staticClass:"el-icon-plus"}),a("span",[t._v("添加查询条件")])])],2)]),a("div",{staticClass:"footer"},[a("el-button",{staticStyle:{float:"left"},attrs:{type:"primary"},on:{click:t.transScene}},[t._v("转固定场景")]),a("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.clearForm()}}},[t._v("清空")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){return t.$emit("closedDialog")}}},[t._v("取 消")])],1)])},d=[],u=(a("c740"),a("a434"),a("b775")),m={name:"searchForm",props:["code"],data:function(){return{formdata:{enabledmark:1,modulecode:"",scenename:"",scenedata:"",remark:"",rownum:0,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},fieldData:[],jsondata:[{field:"",fieldtype:0,math:"",value:""}],textType:[{value:"like",label:"包含"},{value:"not like",label:"不包含"},{value:"equal",label:"等于"},{value:"not equal",label:"不等于"}],numType:[{value:">",label:"大于"},{value:">=",label:"大于等于"},{value:"<",label:"小于"},{value:"=<",label:"小于等于"},{value:"=",label:"等于"},{value:"!=",label:"不等于"}],dateType:[{value:"between",label:"时间"},{value:"today",label:"当天"},{value:"month",label:"本月"}],boolType:[{value:"true",label:"是"},{value:"false",label:"否"}]}},mounted:function(){this.getInit()},methods:{getInit:function(){var t=this;if(this.fieldData=[],u["a"].get("/SaScene/getFieldListByCode?code="+this.code).then((function(e){200==e.data.code&&e.data.data&&(t.fieldData=e.data.data)})),this.$store.state.advancedSearch.modulecode==this.code){var e=this.$store.state.advancedSearch;this.jsondata=e.jsondata}else this.jsondata=[{field:"",fieldtype:0,math:"",value:""}]},submitForm:function(){var t={modulecode:this.jsondata[0].field?this.code:"",scenename:"",jsondata:this.jsondata};this.$store.commit("advancedSearch/setSearchData",t),this.$emit("advancedSearch",this.jsondata)},clearForm:function(){this.jsondata=[{field:"",fieldtype:0,math:"",value:""}];var t={modulecode:this.jsondata[0].field?this.code:"",scenename:"",jsondata:this.jsondata};this.$store.commit("advancedSearch/setSearchData",t),this.$emit("advancedSearch",this.jsondata)},addBtn:function(){if(10!=this.jsondata.length){var t={field:"",fieldtype:0,math:"",value:""};this.jsondata.push(t)}else this.$message.warning("筛选条件最多为10条")},deleteBtn:function(t){this.jsondata.splice(t,1)},filiterType:function(t,e){var a=this.fieldData.findIndex((function(e){return e.fieldcode==t}));if(-1!=a){var i=this.fieldData[a];e.fieldtype=i.fieldtype,0==i.fieldtype?e.math=this.textType[0].value:1==i.fieldtype?e.math=this.numType[0].value:2==i.fieldtype&&(e.math=this.dateType[0].value),this.$forceUpdate()}},changeMath:function(t,e){"today"==t?(e["value"]=Object(r["b"])(new Date),e["valueb"]=Object(r["c"])(new Date)):"month"==t&&(e["value"]=this.setMonthDate()[0],e["valueb"]=this.setMonthDate()[1])},changeDate:function(t,e,a){e[a]="valueb"==a?Object(r["b"])(t):Object(r["c"])(t)},setMonthDate:function(){var t=new Date,e=t.getFullYear(),a=t.getMonth()+1;0==a&&(a=12,e-=1),a<10&&(a="0"+a);var i=new Date(e,a,0),o=e+"-"+a+"-01 00:00:00",l=e+"-"+a+"-"+i.getDate()+" 23:59:59";return[o,l]},transScene:function(){var t=this;this.$prompt("请输入场景名称","提示",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"场景名称为必填项",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.formdata.scenename=a,t.submitItemUpdate()}))},submitItemUpdate:function(){var t=this;this.formdata.modulecode=this.code,this.formdata.scenedata=JSON.stringify(this.jsondata),u["a"].post("/SaScene/create",JSON.stringify(this.formdata)).then((function(e){200==e.data.code&&(t.$message.success("保存成功"),t.$emit("bindData"))}))}}},p=m,f=(a("81da"),a("2877")),h=Object(f["a"])(p,c,d,!1,null,"f1fd34ca",null),g=h.exports,b={name:"Listheader",props:["tableForm"],components:{Setcolums:n["a"],searchForm:g},data:function(){return{strfilter:"",iShow:!1,formdata:{},dateRange:Object(r["d"])(),pickerOptions:Object(r["h"])(),setColumsVisible:!1,code:"D07M02B1List",searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(t){this.iShow=!1;var e={dateRange:this.dateRange,formdata:t};this.$emit("advancedSearch",e),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){var t={dateRange:this.dateRange,strfilter:this.strfilter};this.$emit("btnSearch",t)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")}}},v=b,y=(a("0fbe"),Object(f["a"])(v,l,s,!1,null,"1f73202a",null)),w=y.exports,x=a("333d"),S=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small",disabled:!!t.formdata.assessor},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v("保 存")]),a("el-button",{attrs:{type:"primary",disabled:!t.formdata.id,size:"small"},nativeOn:{click:function(e){return t.printButton()}}},[t._v(" 打 印")]),a("el-dropdown",{staticStyle:{"margin-left":"10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.formdata.id||!!t.formdata.assessor},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")])],1)],1),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("过程"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"})],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info",staticStyle:{width:"100%"}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("div",{staticClass:"flex j-end p-a",staticStyle:{top:"5px",right:"0"}},[a("div",{staticClass:"refNo flex j-end"},[a("div",{staticClass:"refNo-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),a("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini"},model:{value:t.formdata.billdate,callback:function(e){t.$set(t.formdata,"billdate",e)},expression:"formdata.billdate"}})],1),a("div",[a("span",[t._v("NO：")]),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini",readonly:""},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)]),a("div",{staticStyle:{display:"flex","justify-content":"flex-end"}},[a("div",{staticStyle:{"margin-right":"10px",width:"100px"}},[t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount?a("svg-icon",{staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"finish1"}}):t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount?a("svg-icon",{staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"revoke"}}):t._e()],1),a("div",{staticStyle:{"margin-right":"10px",width:"90px"}},[a("svg-icon",{directives:[{name:"show",rawName:"v-show",value:!!t.formdata.assessor,expression:"!!formdata.assessor"}],staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"approve"}})],1)])])]),a("el-form",{ref:"formdata",staticClass:"custInfo",staticStyle:{"min-height":"110px"},attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"on",rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[a("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.id,placeholder:"请输入单据类型",size:"small"},on:{change:function(e){t.formdata.item=[],t.formdata.cash=[],t.formdata.paygroupid="",t.formdata.paygroupname="",t.formdata.paygroupuid="",t.formdata.applygroupuid="",t.formdata.applygroupname="",t.formdata.applygroupid="",t.formdata.groupname=""}},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[a("el-option",{attrs:{label:"预收冲应收",value:"预收冲应收"}}),a("el-option",{attrs:{label:"预付冲应付",value:"预付冲应付"}})],1)],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单据标题"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),a("el-row",["预收冲应收"==t.formdata.billtype?a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("paygroupid")}}},[a("el-form-item",{attrs:{label:"客户",prop:"paygroupid"}},[a("el-popover",{attrs:{placement:"bottom-start",trigger:"click"},on:{show:function(e){return t.$refs.selpaygroup.bindData()}},model:{value:t.selVisible,callback:function(e){t.selVisible=e},expression:"selVisible"}},[a("selpaygroup",{ref:"selpaygroup",staticStyle:{width:"560px",height:"420px"},attrs:{multi:t.multi},on:{singleSel:t.selectPaygroup}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"客户",clearable:"",size:"small",readonly:""},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}},[a("i",{staticClass:"el-icon-circle-plus-outline",staticStyle:{"font-size":"15px",color:"rgb(64, 158, 255)"},attrs:{slot:"suffix"},slot:"suffix"})])],1)],1)],1)],1)]):a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("applygroupid")}}},[a("el-form-item",{attrs:{label:"供应商",prop:"applygroupid"}},[a("el-popover",{attrs:{placement:"bottom-start",trigger:"click"},on:{show:function(e){return t.$refs.selapplygroup.bindData()}},model:{value:t.selapplyVisible,callback:function(e){t.selapplyVisible=e},expression:"selapplyVisible"}},[a("selapplygroup",{ref:"selapplygroup",staticStyle:{width:"560px",height:"420px"},attrs:{multi:t.multi},on:{singleSel:t.selectApplyGroup}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"供应商",clearable:"",size:"small",readonly:""},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}},[a("i",{staticClass:"el-icon-circle-plus-outline",staticStyle:{"font-size":"15px",color:"rgb(64, 158, 255)"},attrs:{slot:"suffix"},slot:"suffix"})])],1)],1)],1)],1)]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"flex"},[a("el-form-item",{attrs:{label:"预付金额"}},[a("span",[t._v("￥"+t._s(t.formdata.payamount?t.formdata.payamount:0))])]),a("el-form-item",{attrs:{label:"核销金额"}},[a("span",[t._v("￥"+t._s(t.formdata.applied?t.formdata.applied:0))])])],1)])],1)],1),a("el-divider")],1),a("div",{staticClass:"form-body"},[a("cashitem",{ref:"cashitem",attrs:{lstitem:t.formdata.cash,formdata:t.formdata,itemAmount:t.itemAmount,idx:t.idx},on:{computerCashAmount:t.computerCashAmount}}),a("elitem",{directives:[{name:"show",rawName:"v-show",value:"其他付款"!=t.formdata.billtype,expression:"formdata.billtype != '其他付款'"}],ref:"elitem",attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx},on:{computerItemAmount:t.computerItemAmount}})],1),a("el-divider"),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:18}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.summary,callback:function(e){t.$set(t.formdata,"summary",e)},expression:"formdata.summary"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"经办人",prop:"operator"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"经办人",clearable:"",size:"small"},model:{value:t.formdata.operator,callback:function(e){t.$set(t.formdata,"operator",e)},expression:"formdata.operator"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"审核"}},[a("span",{staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.assessor))])])],1),a("el-col",{directives:[{name:"show",rawName:"v-show",value:t.formdata.assessor,expression:"formdata.assessor"}],attrs:{span:4}},[a("el-form-item",{attrs:{label:"审核日期"}},[a("span",{staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.assessdate)))])])],1)],1)],1)],1)]),a("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible},on:{"update:visible":function(e){t.ReportVisible=e}}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticStyle:{float:"left"},attrs:{type:"primary",size:"small"},on:{click:t.submitRemoteReport}},[t._v("云打印")]),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("打印")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),a("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[a("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})])],1)},_=[],k=a("c7eb"),P=a("1da1");a("99af"),a("a9e3"),a("d3b7"),a("25f0"),a("3ca3"),a("4d90"),a("ddb0"),a("2b3d"),a("9861");const $={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);u["a"].post("/D07M02B1/create",i).then(t=>{console.log(t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);u["a"].post("/D07M02B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{u["a"].get("/D07M02B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var D=$,C=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex f-d-c form",staticStyle:{height:"280px"}},[a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",disabled:!!t.formdata.assessor},nativeOn:{click:function(e){return t.getselPwProcess(1)}}},[a("i",{staticClass:"el-icon-circle-plus-outline"}),t._v(" 添 加")]),a("el-button",{attrs:{disabled:!!t.formdata.assessor||1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveUp()}}},[a("i",{staticClass:"el-icon-top"}),t._v(" 上 移")]),a("el-button",{attrs:{disabled:!!t.formdata.assessor||1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveDown()}}},[a("i",{staticClass:"el-icon-bottom"}),t._v(" 下 移")]),a("el-button",{attrs:{disabled:!!t.formdata.assessor||!t.selected,type:"danger",size:"mini"},nativeOn:{click:function(e){return t.delItem()}}},[a("i",{staticClass:"el-icon-delete"}),t._v(" 批量删除")]),a("el-button",{attrs:{disabled:!!t.formdata.assessor||1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.copyRow()}}},[a("i",{staticClass:"el-icon-document-copy"}),t._v(" 复 制")])],1)],1),a("div",{staticClass:"table-container f-1 table-position",staticStyle:{width:"100%"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",attrs:{"summary-method":t.getSummaries,"show-summary":"",data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"248px","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange,"row-click":t.saveRow}},[a("el-table-column",{attrs:{type:"selection",width:"60"}}),a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"发票单号",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.invobillcode))])]}}])}),a("el-table-column",{attrs:{label:"发票编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.invocode))])]}}])}),a("el-table-column",{attrs:{label:"发票金额",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.invoamount))])]}}])}),a("el-table-column",{attrs:{label:"金额",align:"center","min-width":"60","show-overflow-tooltip":"",prop:"amount"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"请输入内容"},model:{value:e.row.amount,callback:function(a){t.$set(e.row,"amount",a)},expression:"scope.row.amount"}}):a("span",[t._v(t._s(e.row.amount))])]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"请输入内容"},model:{value:e.row.remark,callback:function(a){t.$set(e.row,"remark",a)},expression:"scope.row.remark"}}):a("span",[t._v(t._s(e.row.remark))])]}}])})],1)],1),t.PwProcessFormVisible?a("el-dialog",{attrs:{title:"采购发票","append-to-body":!0,visible:t.PwProcessFormVisible,width:"1000px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.PwProcessFormVisible=e}}},[a("selPwProcess",{ref:"selPwProcess",attrs:{multi:t.multi,selecturl:"/D03M05B1/getOnlinePageTh?groupid="+t.formdata.applygroupid}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selPwProcess()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.PwProcessFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.OrderVisible?a("el-dialog",{attrs:{title:"销售开票","append-to-body":!0,visible:t.OrderVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.OrderVisible=e}}},[a("selOrder",{ref:"selOrder",attrs:{multi:t.multi,selecturl:"/D01M05B1/getOnlineRecePageTh?groupid="+t.formdata.paygroupid}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selOrder()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.OrderVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},O=[],F=(a("7db0"),a("13d5"),a("b680"),a("159b"),a("dd56")),q=a("fb0b"),N=a("da92"),L={name:"Elitem",components:{selPwProcess:F["a"],selOrder:q["a"]},props:["formdata","lstitem","idx"],data:function(){return{title:"发票",formLabelWidth:"100px",listLoading:!1,PwProcessFormVisible:!1,OrderVisible:!1,lst:[],multi:0,billamount:0,selected:!1,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0}},watch:{lstitem:function(t,e){this.lst=this.lstitem,0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate)},"formdata.assessor":function(t,e){this.formdata.assessor?this.isEditOk=!1:this.isEditOk=!0},lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){this.lst=[]},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},mounted:function(){this.catchHight()},methods:{getSummaries:function(t){var e=this,a=t.columns,i=t.data,o=["amount"],l=[];return a.forEach((function(t,a){if(0!==a){var s=!1;o.length>0&&void 0!=o.find((function(e){return e==t.property}))&&(s=!0);var n=i.map((function(e){return Number(e[t.property])}));!n.every((function(t){return isNaN(t)}))&&s?l[a]=n.reduce((function(t,a){var i=Number(a);return isNaN(i)?e.numFormat(Number(t).toFixed(4)):e.numFormat(N["a"].plus(Number(t),Number(a)).toFixed(4))}),0):l[a]=""}else l[a]="合计"})),this.$emit("computerItemAmount",l[5]),l},getselPwProcess:function(t){""!=this.formdata.groupid?("预收冲应收"==this.formdata.billtype?this.OrderVisible=!0:this.PwProcessFormVisible=!0,this.multi=t):this.$message.warning("请选择客户")},selPwProcess:function(){this.PwProcessFormVisible=!1;for(var t=this.$refs.selPwProcess.$refs.selectVal.selection,e=0;e<t.length;e++){var a={invobillid:t[e].id,invobillcode:t[e].refno,invocode:t[e].invocode,invoamount:t[e].taxamount,amount:N["a"].minus(t[e].taxamount,t[e].paid),remark:"",rownum:0};0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}},selOrder:function(){var t=this.$refs.selOrder.$refs.selectVal.selection;if(0!=t.length){this.OrderVisible=!1,console.log("oldiLst",t);for(var e=0;e<t.length;e++){var a={amount:N["a"].minus(t[e].taxamount,t[e].receipted),invoamount:t[e].taxamount,invobillcode:t[e].refno,invocode:t[e].invocode,invobillid:t[e].id,remark:"",rownum:0};0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("选择内容不能为空")},numFormat:function(t){t+="";var e=t.lastIndexOf(".")?t.lastIndexOf("."):0,a=t.length,i=t.substring(e,a);return i>0?t:t.substring(0,e)},changeInput:function(t,e){console.log("compute"),e.amount=this.numFormat(N["a"].times(e.price,e.quantity).toFixed(4)),e.taxprice=this.numFormat(N["a"].times(e.price,1+.01*e.itemtaxrate).toFixed(4)),e.taxamount=this.numFormat(N["a"].times(e.price,e.quantity*(1+.01*e.itemtaxrate)).toFixed(4))},handleSelectionChange:function(t){t.length>0?this.selected=!0:this.selected=!1;for(var e=0;e<this.lst.length;e++)this.lst[e].rownum=e;this.multipleSelection=t},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},deleteRows:function(t,e){var a=this,i=this.multipleSelection;i&&i.forEach((function(t,e){a.lst.forEach((function(e,i){t.rownum===e.rownum&&t.invoid===e.invoid&&a.lst.splice(i,1)}))})),this.$refs.multipleTable.clearSelection(),this.selected=!1},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},copyRow:function(){var t=Object.assign({},this.multipleSelection[0]);this.$delete(t,"id"),this.lst.push(t),this.$refs.multipleTable.clearSelection()},saveRow:function(t){for(var e=0;e<this.lst.length;e++)this.lst[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0)),this.$forceUpdate()},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}},z=L,V=(a("d7d6"),Object(f["a"])(z,C,O,!1,null,"6328774c",null)),R=V.exports,T=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"elitem",staticClass:"flex f-d-c form",style:{height:"其他付款"==t.formdata.billtype?"470px":"180px","margin-bottom":"10px"}},[a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",disabled:!!t.formdata.assessor},nativeOn:{click:function(e){return t.getselPwProcess(1)}}},[a("i",{staticClass:"el-icon-circle-plus-outline"}),t._v(" 添 加")]),a("el-button",{attrs:{disabled:!!t.formdata.assessor||1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveUp()}}},[a("i",{staticClass:"el-icon-top"}),t._v(" 上 移")]),a("el-button",{attrs:{disabled:!!t.formdata.assessor||1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveDown()}}},[a("i",{staticClass:"el-icon-bottom"}),t._v(" 下 移")]),a("el-button",{attrs:{disabled:!!t.formdata.assessor||!t.selected,type:"danger",size:"mini"},nativeOn:{click:function(e){return t.delItem()}}},[a("i",{staticClass:"el-icon-delete"}),t._v(" 批量删除")]),a("el-button",{attrs:{disabled:!!t.formdata.assessor||1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.copyRow()}}},[a("i",{staticClass:"el-icon-document-copy"}),t._v(" 复 制")])],1)],1),a("div",{staticClass:"table-container f-1 table-position",staticStyle:{width:"100%"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",attrs:{"summary-method":t.getSummaries,"show-summary":"",data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",size:"small",height:t.tableHeight,"header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange,"row-click":t.saveRow}},[a("el-table-column",{attrs:{type:"selection",width:"60"}}),a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.paybillcode))])]}}])}),a("el-table-column",{attrs:{label:"单据金额",align:"center","min-width":"60","show-overflow-tooltip":"",prop:"paybillamount"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.paybillamount))])]}}])}),a("el-table-column",{attrs:{label:"核销金额",align:"center","min-width":"60","show-overflow-tooltip":"",prop:"amount"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"核销金额"},model:{value:e.row.amount,callback:function(a){t.$set(e.row,"amount",a)},expression:"scope.row.amount"}}):a("span",[t._v(t._s(e.row.amount))])]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"请输入内容"},model:{value:e.row.remark,callback:function(a){t.$set(e.row,"remark",a)},expression:"scope.row.remark"}}):a("span",[t._v(t._s(e.row.remark))])]}}])})],1)],1),t.PayVisible?a("el-dialog",{attrs:{title:"销售预收单","append-to-body":!0,visible:t.PayVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.PayVisible=e}}},[a("selPay",{ref:"selPay",attrs:{multi:t.multi,selecturl:"/D01M08B1DEP/getOnlinePageTh?groupid="+t.formdata.paygroupid}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selPay()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.PayVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.ApplyVisible?a("el-dialog",{attrs:{title:"采购预付单","append-to-body":!0,visible:t.ApplyVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.ApplyVisible=e}}},[a("selApply",{ref:"selApply",attrs:{multi:t.multi,selecturl:"/D03M06B1PRE/getOnlinePageTh?groupid="+t.formdata.applygroupid}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selApply()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.ApplyVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},j=[],M=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"380px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.refno))])]}}])}),a("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.billtype))])]}}])}),a("el-table-column",{attrs:{label:"单据标题",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.billtitle))])]}}])}),a("el-table-column",{attrs:{label:"客户",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.groupname))])]}}])}),a("el-table-column",{attrs:{label:"金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.billamount))])]}}])}),a("el-table-column",{attrs:{label:"转出金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.outamount))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},E=[],B={components:{Pagination:x["a"]},props:["multi","groupid","selecturl"],data:function(){return{title:"客户信息",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D01M08B1DEP/getPageTh";u["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},A=B,I=(a("e3e0"),Object(f["a"])(A,M,E,!1,null,"bb4b05cc",null)),U=I.exports,H=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"380px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.refno))])]}}])}),a("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.billtype))])]}}])}),a("el-table-column",{attrs:{label:"单据标题",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.billtitle))])]}}])}),a("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.groupname))])]}}])}),a("el-table-column",{attrs:{label:"金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.billamount))])]}}])}),a("el-table-column",{attrs:{label:"转出金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.outamount))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},J=[],G={components:{Pagination:x["a"]},props:["multi","groupid","selecturl"],data:function(){return{title:"客户信息",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M06B1PRE/getPageTh";u["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},W=G,Y=(a("af0d"),Object(f["a"])(W,H,J,!1,null,"e696db9c",null)),K=Y.exports,Q={name:"Elitem",components:{selPay:U,selApply:K},props:["formdata","lstitem","idx","itemAmount"],data:function(){return{title:"订单预收",formLabelWidth:"100px",listLoading:!1,PayVisible:!1,ApplyVisible:!1,lst:[],multi:0,selected:!1,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0}},watch:{lstitem:function(t,e){this.lst=this.lstitem},"formdata.assessor":function(t,e){this.formdata.assessor?this.isEditOk=!1:this.isEditOk=!0},lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){this.lst=[]},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},mounted:function(){this.catchHight()},methods:{catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},getSummaries:function(t){var e=this,a=t.columns,i=t.data,o=["paybillamount","amount"],l=[];return a.forEach((function(t,a){if(0!==a){var s=!1;o.length>0&&void 0!=o.find((function(e){return e==t.property}))&&(s=!0);var n=i.map((function(e){return Number(e[t.property])}));!n.every((function(t){return isNaN(t)}))&&s?l[a]=n.reduce((function(t,a){var i=Number(a);return isNaN(i)?e.numFormat(Number(t).toFixed(4)):e.numFormat(N["a"].plus(Number(t),Number(a)).toFixed(4))}),0):l[a]=""}else l[a]="合计"})),this.formdata.payamount=l[3],this.$emit("computerCashAmount",l[4]),l},getselPwProcess:function(t){"预收冲应收"==this.formdata.billtype?this.PayVisible=!0:this.ApplyVisible=!0,this.multi=t},selPay:function(){var t=this.$refs.selPay.$refs.selectVal.selection;if(0!=t.length){this.PayVisible=!1,console.log("oldiLst",t);for(var e=0;e<t.length;e++){var a={amount:N["a"].minus(t[e].billamount,t[e].outamount),paybillamount:t[e].billamount,paybillid:t[e].id,paybillcode:t[e].refno,remark:"",rownum:0};0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("选择内容不能为空")},selApply:function(){var t=this.$refs.selApply.$refs.selectVal.selection;if(0!=t.length){this.ApplyVisible=!1,console.log("oldiLst",t);for(var e=0;e<t.length;e++){var a={amount:N["a"].minus(t[e].billamount,t[e].outamount),paybillamount:t[e].billamount,paybillid:t[e].id,paybillcode:t[e].refno,remark:"",rownum:0};0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}}else this.$message.warning("选择内容不能为空")},numFormat:function(t){t+="";var e=t.lastIndexOf(".")?t.lastIndexOf("."):0,a=t.length,i=t.substring(e,a);return i>0?t:t.substring(0,e)},changeInput:function(t,e){console.log("compute")},handleSelectionChange:function(t){t.length>0?this.selected=!0:this.selected=!1;for(var e=0;e<this.lst.length;e++)this.lst[e].rownum=e;this.multipleSelection=t},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},deleteRows:function(t,e){var a=this,i=this.multipleSelection;i&&i.forEach((function(t,e){a.lst.forEach((function(e,i){t.rownum===e.rownum&&a.lst.splice(i,1)}))})),this.$refs.multipleTable.clearSelection(),this.selected=!1},copyRow:function(){var t=Object.assign({},this.multipleSelection[0]);this.$delete(t,"id"),this.lst.push(t),this.$refs.multipleTable.clearSelection()},saveRow:function(t){for(var e=0;e<this.lst.length;e++)this.lst[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0)),this.$forceUpdate()},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}},X=Q,Z=(a("68a7"),Object(f["a"])(X,T,j,!1,null,"2f667744",null)),tt=Z.exports,et=a("1f558"),at=a("0c51"),it={name:"Formedit",components:{elitem:R,selpaygroup:at["a"],selapplygroup:et["a"],cashitem:tt},props:["idx"],data:function(){return{title:"往来核销",formdata:{createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,applied:0,applyabbreviate:"",applygroupid:"",applygroupname:"",applygroupuid:"",billdate:new Date,billtitle:"",billtype:"预收冲应收",cash:[],item:[],operator:"",payabbreviate:"",payamount:0,paygroupid:"",paygroupname:"",paygroupuid:"",refno:"",summary:""},formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],applygroupid:[{required:!0,trigger:"blur",message:"供应商为必填项"}],paygroupid:[{required:!0,trigger:"blur",message:"客户为必填项"}],operator:[{required:!0,trigger:"blur",message:"经办人为必填项"}]},multi:0,selVisible:!1,selapplyVisible:!1,formLabelWidth:"100px",formheight:"500px",ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,itemAmount:0,cashAmount:0}},computed:{formcontainHeight:function(){return window.innerHeight-50-13-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},destroyed:function(){document.onkeydown=function(){},this.$setWs.wsClose()},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&u["a"].get("/D07M02B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")}))},computerItemAmount:function(t){this.itemAmount=Number(t)},computerCashAmount:function(t){this.cashAmount=Number(t),this.formdata.applied=Number(this.cashAmount)},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;console.log("可保存",e.idx),e.saveForm()}))},saveForm:function(){var t=this;if("其他付款"!=this.formdata.billtype){if(this.formdata.applied!=this.itemAmount||this.formdata.applied!=this.cashAmount)return void this.$message.warning("核销总金额 与 核销金额和发票的金额不一致");this.formdata.item=this.$refs.elitem.lst}this.formdata.cash=this.$refs.cashitem.lst,0==this.idx?D.add(this.formdata).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.bindData()})).catch((function(e){t.$message.warning(e||"保存失败")})):D.update(this.formdata).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.bindData()})).catch((function(e){t.$message.warning(e||"保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){D.delete(t).then((function(t){e.$message.success(t.msg||"删除成功"),e.$emit("compForm")})).catch((function(t){e.$message.warning(t||"删除失败")}))})).catch((function(){}))},approval:function(){var t=this;this.formdata.id?this.approvalRequest():this.$refs["formdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;if("其他付款"!=t.formdata.billtype){if(t.formdata.applied!=t.itemAmount||t.formdata.applied!=t.cashAmount)return void t.$message.warning("核销总金额 与 核销金额和发票的金额不一致");t.formdata.item=t.$refs.elitem.lst}t.formdata.cash=t.$refs.cashitem.lst,D.add(t.formdata).then((function(e){t.formdata=e.data,t.$emit("changeIdx",t.formdata.id),t.approvalRequest()})).catch((function(e){t.$message.warning("保存失败")}))}))},DeApproval:function(){var t=this;u["a"].get("/D07M02B1/approval?key="+this.formdata.id).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"反审核成功"),t.$emit("bindData"),t.formdata=e.data.data):t.$message.warning(e.data.msg||"反审核失败")}))},approvalRequest:function(){var t=this;return Object(P["a"])(Object(k["a"])().mark((function e(){return Object(k["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u["a"].get("/D07M02B1/approval?key="+t.formdata.id).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"审核成功"),t.$emit("bindData"),t.formdata=e.data.data):t.$message.warning(e.data.msg||"审核失败")}));case 2:case"end":return e.stop()}}),e)})))()},printButton:function(){var t=this;u["a"].get("/SaReports/getListByModuleCode?code=D07M02B1Edit").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0}))},submitReport:function(){var t=this;""!=this.reportModel?u["a"].get("/D07M02B1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.formdata.printcount+=1,t.ReportVisible=!1;var a=[];a.push(e.data);var i=window.URL.createObjectURL(new Blob(a,{type:"application/pdf"}));t.pdfUrl=i,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},submitRemoteReport:function(){var t=this;""!=this.reportModel?u["a"].get("/D07M02B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel).then((function(e){200==e.data.code?null==e.data.data||""==e.data.data?t.$message.warning(e.data.msg||"打印失败"):"连接成功"!=t.$store.state.webSocketMsg.webSocketState?(t.$setWs.initWebSocket("ws://127.0.0.1:18800"),setTimeout((function(){t.remoteprinting(e.data.data)}),500)):setTimeout((function(){t.remoteprinting(e.data.data)}),100):t.$message.warning(e.data.msg||"打印失败")})):this.$message.warning("打印模板不能为空!")},remoteprinting:function(t){"连接成功"==this.$store.state.webSocketMsg.webSocketState?(this.formdata.printcount+=1,this.$setWs.wsSend(t),this.ReportVisible=!1):this.$message.warning("连接打印服务器失败")},fastKey:function(){var t=this;document.onkeydown=function(e){var a=e.keyCode;83==a&&e.ctrlKey?(e.preventDefault(),t.formdata.assessor?t.$message.warning("单据已审核，保存失败！"):t.submitForm("formdata")):27==a&&(e.preventDefault(),t.closeForm())}},selectPaygroup:function(t){console.log(t),this.formdata.groupname=t.groupname,this.formdata.paygroupname=t.groupname,this.formdata.paygroupid=t.id,this.formdata.paygroupuid=t.groupuid,this.formdata.item=[],this.formdata.cash=[],this.selVisible=!1,this.$refs.formdata.clearValidate("paygroupid")},selectApplyGroup:function(t){console.log(t),this.formdata.groupname=t.groupname,this.formdata.applygroupname=t.groupname,this.formdata.applygroupid=t.id,this.formdata.applygroupuid=t.groupuid,this.formdata.item=[],this.formdata.cash=[],this.selapplyVisible=!1,this.$refs.formdata.clearValidate("applygroupid")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),l=e.getHours().toString().padStart(2,"0"),s=e.getMinutes().toString().padStart(2,"0"),n=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(l,":").concat(s,":").concat(n)}}},ot=it,lt=(a("d84d"),Object(f["a"])(ot,S,_,!1,null,"727edf98",null)),st=lt.exports,nt=a("48da"),rt={formcode:"D07M02B1List",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Fm_PayApply.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Fm_PayApply.billtype"},{itemcode:"billdate",itemname:"单据日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Fm_PayApply.billdate"},{itemcode:"groupname",itemname:"往来单位",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"App_Workgroup.groupname"},{itemcode:"payamount",itemname:"预付金额",minwidth:"100",sortable:1,displaymark:1,overflow:1,datasheet:"Fm_PayApply.payamount"},{itemcode:"applied",itemname:"核销金额",minwidth:"100",displaymark:1,overflow:1,datasheet:"Fm_PayApply.applied"},{itemcode:"operator",itemname:"经办人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Fm_PayApply.operator"},{itemcode:"summary",itemname:"摘要",minwidth:"100",displaymark:1,overflow:1,datasheet:"Fm_PayApply.summary"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Fm_PayApply.status"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"Fm_PayApply.lister"},{itemcode:"assessor",itemname:"审核员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Fm_PayApply.assessor"}]},ct=a("4363"),dt=a("0521"),ut={name:"D07M02B1",components:{Pagination:x["a"],listheader:w,formedit:st,scene:ct["a"],helpmodel:dt["a"]},props:["searchVal","isDialog"],data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:rt,showhelp:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},mounted:function(){this.bindData(),this.getColumn()},methods:{bindData:function(){var t=this;if(this.listLoading=!0,this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(r["d"])()[0],EndDate:Object(r["d"])()[1]}),this.isDialog){var e={citecode:this.searchVal};this.queryParams.SearchType=0,this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e,this.$delete(this.queryParams,"DateRange")}this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),u["a"].post("/D07M02B1/getPageTh",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;u["a"].get("/SaDgFormat/getBillEntityByCode?code=D07M02B1List").then((function(e){if(200==e.data.code){if(null==e.data.data)return void(t.tableForm=rt);t.tableForm=e.data.data}})).catch((function(e){t.$message.error("请求出错")}))},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},btnExport:function(){var t=this;Promise.resolve().then(function(){for(var e=[],a=[],i=0;i<t.tableForm.item.length;i++){var o=t.tableForm.item[i];o.displaymark&&o.displaymark&&(e.push(o.itemname),a.push(o.itemcode))}var l=t.lst,s=t.formatJson(a,l);Object(nt["a"])(e,s,"往来核销")}.bind(null,a)).catch(a.oe)},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(t,this.tableForm),this.bindData()},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){t.dateRange?this.queryParams.DateRange={StartDate:Object(r["c"])(t.dateRange[0]),EndDate:Object(r["b"])(t.dateRange[1])}:this.$delete(this.queryParams,"DateRange");var e=t.strfilter;""!=e?this.queryParams.SearchPojo={UserName:e,RealName:e,Mobile:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){t.dateRange?this.queryParams.DateRange={StartDate:Object(r["c"])(t.dateRange[0]),EndDate:Object(r["b"])(t.dateRange[1])}:this.$delete(this.queryParams,"DateRange"),this.queryParams.scenedata=t.formdata,""==t.formdata[0].field?this.queryParams.SearchType=1:this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showForm:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeIdx:function(t){this.idx=t}}},mt=ut,pt=(a("1eef"),Object(f["a"])(mt,i,o,!1,null,"511cf973",null));e["default"]=pt.exports},"78c7":function(t,e,a){},"7da1":function(t,e,a){},"81da":function(t,e,a){"use strict";a("c33e")},93684:function(t,e,a){},9473:function(t,e,a){},"99fe":function(t,e,a){"use strict";a("7da1")},af0d:function(t,e,a){"use strict";a("c389")},ba1c:function(t,e,a){},bf19:function(t,e,a){"use strict";var i=a("23e7");i({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return URL.prototype.toString.call(this)}})},bf47:function(t,e,a){},c19f:function(t,e,a){"use strict";var i=a("23e7"),o=a("da84"),l=a("621a"),s=a("2626"),n="ArrayBuffer",r=l[n],c=o[n];i({global:!0,forced:c!==r},{ArrayBuffer:r}),s(n)},c33e:function(t,e,a){},c389:function(t,e,a){},c8cf:function(t,e,a){},ce51:function(t,e,a){"use strict";a("bf47")},d7d6:function(t,e,a){"use strict";a("5301")},d84d:function(t,e,a){"use strict";a("c8cf")},dd56:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.refno))])]}}])}),a("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.billtype))])]}}])}),a("el-table-column",{attrs:{label:"单据名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.billtitle))])]}}])}),a("el-table-column",{attrs:{label:"发票编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.invocode))])]}}])}),a("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.groupname))])]}}])}),a("el-table-column",{attrs:{label:"税率%",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.taxrate))])]}}])}),a("el-table-column",{attrs:{label:"含税金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.taxamount)+" ")]}}])}),a("el-table-column",{attrs:{label:"已付金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.paid)+" ")]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],l=(a("99af"),a("e9c4"),a("d3b7"),a("25f0"),a("4d90"),a("b775")),s=a("333d"),n={components:{Pagination:s["a"]},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0");return"".concat(a,"/").concat(i,"/").concat(o)}},props:["multi","selecturl"],data:function(){return{title:"采购开票",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){console.log(t),this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D03M05B1/getPageTh";l["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},r=n,c=(a("ce51"),a("2877")),d=Object(c["a"])(r,i,o,!1,null,"c300cc12",null);e["a"]=d.exports},e3e0:function(t,e,a){"use strict";a("78c7")},eed5:function(t,e,a){},fb0b:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.refno))])]}}])}),a("el-table-column",{attrs:{label:"单据类型",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.billtype))])]}}])}),a("el-table-column",{attrs:{label:"单据名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.billtitle))])]}}])}),a("el-table-column",{attrs:{label:"发票编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.invocode))])]}}])}),a("el-table-column",{attrs:{label:"客户",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.groupname))])]}}])}),a("el-table-column",{attrs:{label:"含税金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.taxamount)+" ")]}}])}),a("el-table-column",{attrs:{label:"已付金额",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.receipted))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],l=(a("99af"),a("e9c4"),a("d3b7"),a("25f0"),a("4d90"),a("b775")),s=a("333d"),n={components:{Pagination:s["a"]},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0");return"".concat(a,"/").concat(i,"/").concat(o)}},props:["multi","selecturl"],data:function(){return{title:"销售开票",listLoading:!0,lst:[],PageIndex:1,PageSize:10,searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){console.log(t),this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;if(this.listLoading=!0,this.selecturl)var e=this.selecturl;else e="/D01M05B1/getPageTh";l["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(console.log("销售订单查询",e),t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},r=n,c=(a("99fe"),a("2877")),d=Object(c["a"])(r,i,o,!1,null,"2eb1492a",null);e["a"]=d.exports}}]);