(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-commons"],{"0521":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"help-warp"},[a("div",{staticClass:"help-header"},[a("span",[e._v(e._s(e.formdata.introname?e.formdata.introname:"功能简介"))])]),a("div",{staticClass:"help-content"},[e.formdata.introcontent?a("div",[a("div",{domProps:{innerHTML:e._s(e.formdata.introcontent)}})]):a("div",{staticClass:"noData",staticStyle:{"font-size":"22px"}},[e._v("暂无内容")])])])},o=[],i=a("b775"),n={props:["code"],data:function(){return{formdata:{}}},created:function(){},methods:{bindData:function(){var e=this;i["a"].get("/system/SYSM06B7/getEntityByCode?key="+this.code).then((function(t){if(200==t.data.code){if(null==t.data.data)return void(e.formdata={introname:"",introcontent:""});e.formdata=t.data.data}else e.$message.warning(t.data.msg||"获取功能简介失败")})).catch((function(t){e.$message.error(t||"请求错误")}))}}},s=n,l=(a("f4e2"),a("2877")),c=Object(l["a"])(s,r,o,!1,null,"41c88702",null);t["a"]=c.exports},1065:function(e,t,a){"use strict";a("e2bf")},"2ed0":function(e,t,a){},"2f34":function(e,t,a){},4e3:function(e,t,a){},"48da":function(e,t,a){"use strict";a.d(t,"a",(function(){return l}));a("2909"),a("99af"),a("d81d"),a("c19f"),a("ace4"),a("d3b7"),a("ac1f"),a("00b4"),a("25f0"),a("4d90"),a("5319"),a("5cc6"),a("9a8c"),a("a975"),a("735e"),a("c1ac"),a("d139"),a("3a7b"),a("d5d6"),a("82f8"),a("e91f"),a("60bd"),a("5f96"),a("3280"),a("3fcc"),a("ca91"),a("25a1"),a("cd26"),a("3c5d"),a("2954"),a("649e"),a("219c"),a("170b"),a("b39a"),a("72f7"),a("159b"),a("bf19");function r(e,t){t&&(e+=1462);var a=Date.parse(e);return(a-new Date(Date.UTC(1899,11,30)))/864e5}function o(e,t){for(var a={},o={s:{c:1e7,r:1e7},e:{c:0,r:0}},i=0;i!=e.length;++i)for(var n=0;n!=e[i].length;++n){o.s.r>i&&(o.s.r=i),o.s.c>n&&(o.s.c=n),o.e.r<i&&(o.e.r=i),o.e.c<n&&(o.e.c=n);var s={v:e[i][n]};if(null!=s.v){var l=XLSX.utils.encode_cell({c:n,r:i});"number"===typeof s.v?s.t="n":"boolean"===typeof s.v?s.t="b":s.v instanceof Date?(s.t="n",s.z=XLSX.SSF._table[14],s.v=r(s.v)):s.t="s",a[l]=s}}return o.s.c<1e7&&(a["!ref"]=XLSX.utils.encode_range(o)),a}function i(){if(!(this instanceof i))return new i;this.SheetNames=[],this.Sheets={}}function n(e){for(var t=new ArrayBuffer(e.length),a=new Uint8Array(t),r=0;r!=e.length;++r)a[r]=255&e.charCodeAt(r);return t}function s(e){if(e){var t=new Date(e),a=t.getFullYear(),r=(t.getMonth()+1).toString().padStart(2,"0"),o=t.getDate().toString().padStart(2,"0"),i=t.getHours().toString().padStart(2,"0"),n=t.getMinutes().toString().padStart(2,"0");return"".concat(a,"-").concat(r,"-").concat(o," ").concat(i,":").concat(n)}}function l(e,t,a){var r=[],l=/^\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[1-2]\d|3[0-1])T(?:[0-1]\d|2[0-3]):[0-5]\d:[0-5]\d(?:\.\d+|)(?:Z|(?:\+|\-)(?:\d{2}):?(?:\d{2}))$/;t.forEach((function(e,t){for(var a=[],o=0;o<e.length;o++){var i=e[o];l.test(i)&&(i=s(i)),a.push(i)}r.push(a)}));var c=r;c.unshift(e);for(var d="SheetJS",u=new i,f=o(c),m=c.map((function(e){return e.map((function(e){return null==e?{wch:10}:e.toString().charCodeAt(0)>255?{wch:2*e.toString().length}:{wch:e.toString().length}}))})),p=m[0],h=1;h<m.length;h++)for(var b=0;b<m[h].length;b++)p[b]["wch"]<m[h][b]["wch"]&&(p[b]["wch"]=m[h][b]["wch"]);f["!cols"]=p,u.SheetNames.push(d),u.Sheets[d]=f;var v=XLSX.write(u,{bookType:"xlsx",bookSST:!1,type:"binary"}),g=a||"列表";saveAs(new Blob([n(v)],{type:"application/octet-stream"}),g+".xlsx")}a("0fd4"),a("f71d"),a("1447")},"60ff":function(e,t,a){"use strict";a("2ed0")},6465:function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement;e._self._c;return e._m(0)},o=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"orderBox"},[a("div",{staticStyle:{height:"500px",width:"100%"},attrs:{id:"histoyrOrder"}})])}],i=(a("99af"),a("4de4"),a("b0c0"),a("d3b7"),a("25f0"),a("4d90"),{props:["historyData"],data:function(){return{}},mounted:function(){},methods:{initEchart:function(e){var t=this;document.getElementById("histoyrOrder").removeAttribute("_echarts_instance_");for(var a=document.getElementById("histoyrOrder"),r=this.$echarts.init(a),o=[],i=[],n=[],s=0;s<e.length;s++)o.push(e[s].refno),i.push(e[s].taxprice?e[s].taxprice:0),n.push(e[s].price?e[s].price:0);var l={title:{text:"历史订单价格"},tooltip:{trigger:"axis",formatter:function(e){var a=t.historyData.filter((function(t){return t.refno==e[0].name}));if(a.length>0)return"<div>订单编号: "+a[0].refno+"<br>订单日期: "+t.dateFormats(a[0].billdate)+"<br>未税单价: "+a[0].price+'<span style="color: #00B83F;"></span>元<br>含税单价: '+a[0].taxprice+'<span style="color: #00B83F;"></span>元</div>'}},legend:{data:["含税单价","未税单价"]},xAxis:{type:"category",boundaryGap:!1,data:o},yAxis:{type:"value"},grid:[{left:"5%",right:"5%"}],series:[{name:"含税单价",type:"line",data:i},{name:"未税单价",type:"line",data:n}]};r.setOption(l),window.addEventListener("resize",(function(){r.resize()})),r.on("click",(function(e){0===e.seriesIndex&&t.$emit("getHistoryPrice",e.value,"含税"),1===e.seriesIndex&&t.$emit("getHistoryPrice",e.value,"未税")}))},dateFormats:function(e){if(e){var t=new Date(e),a=t.getFullYear(),r=(t.getMonth()+1).toString().padStart(2,"0"),o=t.getDate().toString().padStart(2,"0");t.getHours().toString().padStart(2,"0"),t.getMinutes().toString().padStart(2,"0"),t.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(r,"-").concat(o," ")}}}}),n=i,s=(a("8bf8"),a("2877")),l=Object(s["a"])(n,r,o,!1,null,"62bc98ce",null);t["a"]=l.exports},"7f3e":function(e,t,a){"use strict";a("b7bb")},"82f0":function(module,__webpack_exports__,__webpack_require__){"use strict";var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("b64b"),core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__),_views_modules_Sa_SaDict_components_select_vue__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("5c73");__webpack_exports__["a"]={components:{selDictionaries:_views_modules_Sa_SaDict_components_select_vue__WEBPACK_IMPORTED_MODULE_1__["a"]},props:{title:{type:String,default:"单据标题"},formdata:{type:Object,default:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname}},formtemplate:{type:[Array],default:[]},selectform:{type:[Array],default:function(){return[]}}},mounted:function(){},data:function(){return{}},methods:{returnEval:function returnEval(data,itrue){return"string"==typeof data?eval(data):data},clickMethods:function(e,t){if(e){var a={meth:e,param:t};this.$emit("clickMethods",a)}},cleValidate:function(e){this.$refs.formdata.clearValidate(e)}}}},"8a8c":function(e,t,a){},"8bf8":function(e,t,a){"use strict";a("d0fd")},"8daf":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"right"},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==e.ActiveIndex,icon:"el-icon-top"},on:{click:function(t){return e.getMoveUp()}}},[e._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==e.ActiveIndex,icon:"el-icon-bottom"},on:{click:function(t){return e.getMoveDown()}}},[e._v("下 移")])],1),a("el-button-group",{staticStyle:{"margin-right":"12px"}})],1),a("div",{staticClass:"left"},[a("div",[a("table",{staticClass:"productlTable",attrs:{cellspacing:"0",cellpadding:"0"}},[a("thead",[a("tr",[a("th",{staticClass:"tabTh",staticStyle:{width:"50px"}},[e._v("序号")]),e.showcode?a("th",{staticClass:"tabTh"},[e._v("编码")]):e._e(),a("th",{staticClass:"tabTh",on:{dblclick:function(t){e.showcode=!e.showcode}}},[e._v("名称")]),a("th",{staticClass:"tabTh",staticStyle:{width:"150px"}},[e._v("最小宽度")]),a("th",{staticClass:"tabTh",staticStyle:{width:"100px"}},[e._v("是否固定")]),a("th",{staticClass:"tabTh",staticStyle:{width:"140px"}},[e._v("显示/隐藏")])])]),a("tbody",e._l(e.lst,(function(t,r){return a("tr",{key:r,class:e.ActiveIndex==r?"isActive":"",on:{click:function(t){e.ActiveIndex=r}}},[a("td",{staticStyle:{width:"50px"}},[e._v(e._s(r+1))]),e.showcode?a("td",[e._v(e._s(t.itemcode))]):e._e(),a("td",[e._v(e._s(t.itemname))]),a("td",{staticStyle:{width:"150px"}},[e.ActiveIndex==r?a("el-input",{attrs:{placeholder:"最小宽度",size:"small"},model:{value:t.minwidth,callback:function(a){e.$set(t,"minwidth",a)},expression:"i.minwidth"}}):a("span",[e._v(e._s(t.minwidth))])],1),a("td",{staticStyle:{width:"100px"}},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.fixed,callback:function(a){e.$set(t,"fixed",a)},expression:"i.fixed"}})],1),a("td",{staticStyle:{width:"140px"}},[a("el-switch",{staticStyle:{"margin-right":"10px"},attrs:{"inactive-color":"#ff4949","active-value":1,"inactive-value":0},model:{value:t.displaymark,callback:function(a){e.$set(t,"displaymark",a)},expression:"i.displaymark"}})],1)])})),0)])])]),a("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between","margin-top":"20px","margin-right":"12px"}},[a("div",[!this.$store.state.user.userinfo.isadmin?e._e():a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.submitUpdateTen()}}},[e._v("保存默认格式")]),a("el-button",{attrs:{size:"mini",icon:"el-icon-view"},nativeOn:{click:function(t){return e.getDataTen(!0)}}},[e._v("预览默认列")])],1),a("div",[e.formdata.id&&!e.formdata.defmark?a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"danger",size:"mini",icon:"el-icon-refresh-left"},on:{click:function(t){return e.refreshLeft()}}},[e._v("重置")]):e._e(),a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(t){return e.submitUpdate()}}},[e._v("保 存")]),a("el-button",{attrs:{size:"small"},on:{click:function(t){return e.$emit("closeDialog")}}},[e._v("关 闭")])],1)])]),a("el-dialog",{attrs:{title:"预览默认列","append-to-body":!0,width:"60vw",visible:e.showcolumnvisible,"close-on-click-modal":!1},on:{"update:visible":function(t){e.showcolumnvisible=t}}},[a("div",[a("table",{staticClass:"productlTable",attrs:{cellspacing:"0",cellpadding:"0"}},[a("thead",[a("tr",[a("th",{staticClass:"tabTh",staticStyle:{width:"50px"}},[e._v("序号")]),e.showcode?a("th",{staticClass:"tabTh"},[e._v("编码")]):e._e(),a("th",{staticClass:"tabTh",on:{dblclick:function(t){e.showcode=!e.showcode}}},[e._v("名称")]),a("th",{staticClass:"tabTh",staticStyle:{width:"150px"}},[e._v("最小宽度")]),a("th",{staticClass:"tabTh",staticStyle:{width:"100px"}},[e._v("是否固定")]),a("th",{staticClass:"tabTh",staticStyle:{width:"150px"}},[e._v("显示/隐藏")])])]),a("tbody",e._l(e.defformdata.item,(function(t,r){return a("tr",{key:r,class:e.ActiveIndex==r?"isActive":"",on:{click:function(t){e.ActiveIndex=r}}},[a("td",{staticStyle:{width:"50px"}},[e._v(e._s(r+1))]),e.showcode?a("td",[e._v(e._s(t.itemcode))]):e._e(),a("td",[e._v(e._s(t.itemname))]),a("td",{staticStyle:{width:"150px"}},[a("span",[e._v(e._s(t.minwidth))])]),a("td",{staticStyle:{width:"100px"}},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.fixed,callback:function(a){e.$set(t,"fixed",a)},expression:"i.fixed"}})],1),a("td",{staticStyle:{width:"150px"}},[a("el-switch",{staticStyle:{"margin-right":"10px"},attrs:{"inactive-color":"#ff4949","active-value":1,"inactive-value":0},model:{value:t.displaymark,callback:function(a){e.$set(t,"displaymark",a)},expression:"i.displaymark"}})],1)])})),0)])]),a("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between","margin-top":"20px","margin-right":"12px"}},[a("div",[!this.$store.state.user.userinfo.isadmin?e._e():a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.submitUpdateTen("defformdata")}}},[e._v("保存默认列")])],1),a("div",[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(t){return e.imptDefData()}}},[e._v("导入默认列")]),a("el-button",{attrs:{size:"small"},on:{click:function(t){e.showcolumnvisible=!1}}},[e._v("关闭")])],1)])])],1)},o=[],i=(a("99af"),a("a434"),a("e9c4"),a("b64b"),{name:"SetColums",inject:["reload"],props:["code","tableForm","baseparam"],data:function(){return{title:"列设置",lst:[],formdata:{item:[]},showcolumnvisible:!1,defformdata:{item:[]},showcode:!1,ActiveIndex:-1,itemFormdata:{aligntype:"center",classname:"",defwidth:"",displaymark:1,eventname:"",fixed:0,formatter:"",itemcode:"",itemname:"",minwidth:100,overflow:1,pid:"",remark:"",rownum:0,sortable:0}}},watch:{lst:function(e,t){void 0==e&&(this.lst=[]);for(var a=0;a<e.length;a++)e[a].rownum=a}},created:function(){},mounted:function(){var e=this;this.$nextTick((function(){e.bindData()}))},methods:{bindData:function(){this.formdata=JSON.parse(JSON.stringify(this.tableForm)),1==this.formdata.defmark&&this.$delete(this.formdata,"id"),this.lst=[].concat(this.formdata.item)},refreshLeft:function(){var e=this,t=this;this.$confirm("此操作将初始化表格内容, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a="/SaDgFormat";e.baseparam&&(a=e.baseparam),t.$request.get(a+"/delete?key="+t.formdata.id).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"重置内容成功"),t.getColumn(t.formdata.formcode),t.$emit("bindData"),t.$emit("closeDialog")):t.$message.warning(e.data.msg||"重置内容失败")}))})).catch((function(){}))},getDataTen:function(e){var t=this;this.defformdata={item:[]};var a="/SaDgFormat";this.baseparam&&(a=this.baseparam);var r=a+"/getTenBillEntityByCode?code="+this.tableForm.formcode;this.$request.get(r).then((function(a){if(200==a.data.code){if(null==a.data.data||!a.data.data)return void t.$message.warning(a.data.msg||"未查询到默认列，请联系管理员设置默认列");t.defformdata=a.data.data,e&&(t.showcolumnvisible=!0)}else t.$message.warning(a.data.msg||"获取默认列失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},imptDefData:function(){this.lst=[];for(var e=0;e<this.defformdata.item.length;e++){var t=this.defformdata.item[e];this.$delete(t,"id"),this.$delete(t,"pid"),this.lst.push(t)}this.showcolumnvisible=!1,this.$forceUpdate()},submitUpdateTen:function(e){var t=this,a=Object.assign({},this.formdata);a.item=this.lst,"defformdata"==e&&(a=Object.assign({},this.defformdata),a.item=this.defformdata.item),a.enabledmark=1,a.defmark=1;var r="/SaDgFormat";this.baseparam&&(r=this.baseparam);var o=r+"/updateTen";this.$request.post(o,JSON.stringify(a)).then((function(e){200==e.data.code?t.$message.success(e.data.msg||"保存成功"):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},submitUpdate:function(){var e=this;this.formdata.item=this.lst,this.formdata.enabledmark=1,this.formdata.defmark=0;var t="/SaDgFormat";if(this.baseparam&&(t=this.baseparam),this.formdata.id)a=t+"/update";else var a=t+"/create";this.$request.post(a,JSON.stringify(this.formdata)).then((function(t){200==t.data.code?(e.$message.success(t.data.msg||"保存成功"),e.formdata.id=t.data.data.id,e.$forceUpdate(),e.$emit("bindData"),e.$emit("closeDialog")):e.$message.warning(t.data.msg||"保存失败")})).catch((function(t){e.$message.error(t||"请求错误")}))},getColumn:function(e){var t=this,a="/SaDgFormat";this.baseparam&&(a=this.baseparam),this.$request.get(a+"/getBillEntityByCode?code="+e).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata.id="",void t.$forceUpdate();t.formdata=e.data.data,t.lst=e.data.data.item,t.$forceUpdate()}})).catch((function(e){t.$message.error("请求出错")}))},cleValidate:function(e){this.$refs.itemFormdata.clearValidate(e)},getMoveUp:function(){if(0!=this.ActiveIndex){var e=this.lst[this.ActiveIndex],t=this.lst[this.ActiveIndex].rownum;this.lst.splice(t,1),this.lst.splice(t-1,0,e),this.ActiveIndex-=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var e=this.lst[this.ActiveIndex],t=this.lst[this.ActiveIndex].rownum;this.lst.splice(t,1),this.lst.splice(t+1,0,e),this.ActiveIndex+=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")}}}),n=i,s=(a("60ff"),a("2877")),l=Object(s["a"])(n,r,o,!1,null,"dec47f96",null);t["a"]=l.exports},"8efa":function(e,t,a){"use strict";a("2f34")},b7bb:function(e,t,a){},baaf:function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"transfertemp"},[a("el-transfer",{staticStyle:{"text-align":"left",display:"inline-block"},attrs:{filterable:"",titles:e.titles,"target-order":"push","left-default-checked":e.leftCheckArr,"right-default-checked":e.rightCheckArr,data:e.data},on:{"left-check-change":e.leftCheck,"right-check-change":e.rightCheck},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.option;return a("span",{},[e._v(e._s(r.label))])}}]),model:{value:e.transferVal,callback:function(t){e.transferVal=t},expression:"transferVal"}},[a("el-button",{staticClass:"transfer-footer",attrs:{slot:"right-footer",size:"mini",disabled:1!=e.rightCheckArr.length},on:{click:function(t){return e.getMoveUp("right")}},slot:"right-footer"},[e._v("上移")]),a("el-button",{staticClass:"transfer-footer",attrs:{slot:"right-footer",size:"mini",disabled:1!=e.rightCheckArr.length},on:{click:function(t){return e.getMoveDown("right")}},slot:"right-footer"},[e._v("下移")]),a("el-button",{staticClass:"transfer-footer",attrs:{slot:"right-footer",size:"mini",type:"primary"},on:{click:function(t){return e.handleChange()}},slot:"right-footer"},[e._v("确认")])],1)],1)},o=[],i=(a("c740"),a("a434"),a("d3b7"),a("159b"),{props:{titles:{type:Array,default:function(){return["列表1","列表2"]}},data:{type:Array,default:function(){return[]}},leftVal:{type:Array,default:function(){return[]}},rightVal:{type:Array,default:function(){return[]}}},data:function(){return{transferVal:[],leftCheckArr:[],rightCheckArr:[]}},mounted:function(){this.bindData()},methods:{bindData:function(){this.transferVal=[];for(var e=0;e<this.rightVal.length;e++){var t=this.rightVal[e];this.transferVal.push(t.key)}},handleChange:function(e,t,a){for(var r=[],o=0;o<this.transferVal.length;o++){var i=this.transferVal[o];this.data.forEach((function(e,t){e.key==i&&r.push(e)}))}this.$emit("savetranser",r)},leftCheck:function(e){this.leftCheckArr=e},rightCheck:function(e){this.rightCheckArr=e},getMoveUp:function(e){var t=this;if("left"==e)var a=this.transferVal.findIndex((function(e){return e==t.leftCheckArr[0]}));else a=this.transferVal.findIndex((function(e){return e==t.rightCheckArr[0]}));if(console.log(a),-1!=a){if(0==a)return void this.$message.warning("已经是第一行了！");var r=this.transferVal[a];this.transferVal.splice(a,1),this.transferVal.splice(a-1,0,r)}},getMoveDown:function(e){var t=this;if("left"==e)var a=this.transferVal.findIndex((function(e){return e==t.leftCheckArr[0]}));else a=this.transferVal.findIndex((function(e){return e==t.rightCheckArr[0]}));if(-1!=a){if(a==this.transferVal.length-1)return void this.$message.warning("已经是最后一行了！");var r=this.transferVal[a];this.transferVal.splice(a,1),this.transferVal.splice(a+1,0,r)}}}}),n=i,s=(a("7f3e"),a("2877")),l=Object(s["a"])(n,r,o,!1,null,"4d5dbd1a",null);t["a"]=l.exports},bec6:function(e,t,a){"use strict";a("8a8c")},d0fd:function(e,t,a){},dc0f:function(e,t,a){},dcb4:function(e,t,a){"use strict";var r,o,i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.$slots.Header?a("div",[e._t("Header")],2):a("div",{staticStyle:{"min-height":"150px"}},[a("Header",{ref:"formHeader",attrs:{formdata:e.formdata,formtemplate:e.formtemplate.header.content,title:e.formtemplate.header.title,selectform:e.selectform},on:{clickMethods:function(t){return e.$emit("clickMethods",t)},cleValidate:function(t){return e.$emit("cleValidate",t)},getAllGroupName:function(t){return e.$emit("getAllGroupName",t)},getGroupName:function(t){return e.$emit("getGroupName",t)},getSuppGroupName:function(t){return e.$emit("getSuppGroupName",t)},getFactGroupName:function(t){return e.$emit("getFactGroupName",t)},getWorkGroupName:function(t){return e.$emit("getWorkGroupName",t)},getBranGroupName:function(t){return e.$emit("getBranGroupName",t)},getProsGroupName:function(t){return e.$emit("getProsGroupName",t)},getStoreName:e.getStoreName,getProcName:function(t){return e.$emit("getProcName",t)},getRoleProcName:function(t){return e.$emit("getRoleProcName",t)},getGoodsName:function(t){return e.$emit("getGoodsName",t)},getFlawName:function(t){return e.$emit("getFlawName",t)},autoClear:function(t){return e.$emit("autoClear")},autoStoreClear:function(t){return e.$emit("autoStoreClear",t)},autoProcClear:function(t){return e.$emit("autoProcClear")},autoRoleProcClear:function(t){return e.$emit("autoRoleProcClear")},autoFlawClear:function(t){return e.$emit("autoFlawClear")},autoGoodsClear:function(t){return e.$emit("autoGoodsClear")}}})],1),e._t("Item"),e.$slots.Footer?a("div",[e._t("Footer")],2):a("div",[a("Footer",{attrs:{formdata:e.formdata,formtemplate:e.formtemplate.footer.content}})],1)],2)},n=[],s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form",{ref:"formdata",staticClass:"custInfo",staticStyle:{"margin-top":"20px"},attrs:{model:e.formdata,"label-width":"100px"}},[a("p",{staticClass:"formTitle"},[e._v(e._s(e.title?e.title:"单据标题"))]),e._l(e.formtemplate,(function(t,r){return[a("div",{key:r},["divider"==t.type?a("el-divider",{attrs:{"content-position":t.center?t.center:"left"}},[e._v(e._s(t.label))]):a("el-row",[e._l(t.rowitem,(function(t,r){return[(t.show?e.returnEval(t.show):!t.show)?a("el-col",{key:r,attrs:{span:t.col}},[a("div",{on:{click:function(a){return e.cleValidate(t.code)}}},[a("el-form-item",{attrs:{label:"checkbox"==t.type?"":t.label,prop:t.code,"label-width":t.labelwidth?t.labelwidth:"100px",rules:[{required:!!t.required&&t.required,trigger:"blur",message:t.label+"为必填项"}]}},["input"==t.type?a("el-input",{attrs:{placeholder:"请输入"+t.label,clearable:"",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}},[t.iconbtn?[a("i",{directives:[{name:"show",rawName:"v-show",value:e.returnEval(t.iconbtn.show,1),expression:"returnEval(b.iconbtn.show, 1)"}],staticClass:"getNextCode",class:t.iconbtn.icon,attrs:{slot:"suffix"},on:{click:function(a){return e.clickMethods(t.iconbtn.methods,t.iconbtn.param)}},slot:"suffix"})]:e._e()],2):"select"==t.type?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"+t.label,size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},on:{change:function(a){return e.clickMethods(t.methods,t.param)}},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}},e._l(-1!=e.selectform.findIndex((function(e){return e.code==t.code}))?e.selectform[e.selectform.findIndex((function(e){return e.code==t.code}))].data:t.options,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1):"checkbox"==t.type?a("div",[a("el-checkbox",{attrs:{label:t.label,"true-label":1,"false-label":0,disabled:!!t.disabled&&e.returnEval(t.disabled)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"number"==t.type?a("div",[a("el-input-number",{attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"date"==t.type?a("div",[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:t.size?t.size:"small",clearable:"",placeholder:"选择日期",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"dictionary"==t.type?a("div",[a("el-popover",{ref:t.code+"PropRef",refInFor:!0,attrs:{placement:"bottom",trigger:"click"},on:{show:function(a){e.$refs[t.code+"Ref"][0].bindData()}}},[a("selDictionaries",{ref:t.code+"Ref",refInFor:!0,staticStyle:{width:"200px"},attrs:{multi:0,billcode:t.billcode},on:{singleSel:function(a){e.formdata[t.code]=a.dictvalue,e.$refs[t.code+"PropRef"][0].doClose()},closedic:function(a){e.$refs[t.code+"PropRef"][0].doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择"+t.label,clearable:"",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1)],1)],1):"textarea"==t.type?a("div",[a("el-input",{attrs:{placeholder:"请输入"+t.label,clearable:"",type:"textarea",size:t.size?t.size:"small",autosize:t.autosize?t.autosize:{minRows:2,maxRows:4},disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"text"==t.type?a("div",[a("span",{staticStyle:{"font-size":"18px",color:"#666"}},[e._v(e._s(e.formdata[t.code]))])]):"autocomplete"==t.type?a("div",["customer"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/S16M03R1/getOnlinePageList",type:"客户",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"supplier"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/D01M01B2/getOnlinePageList",type:"供应商",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getSuppGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"workshop"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/D01M01B3/getPageList",type:"生产车间",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getWorkGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"factory"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/D01M01B4/getPageList",type:"外协厂商",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getFactGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"branch"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled),baseurl:"/D01M01B5/getPageList",type:"部门"},on:{setRow:function(a){e.$emit("getBranGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"prospects"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled),baseurl:"/D01M01B6/getPageList",type:"潜在客户"},on:{setRow:function(a){e.$emit("getProsGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"group"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getAllGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"store"==t.searchtype?a("div",[a("StoreAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getStoreName",a,t.code),e.cleValidate(t.code)},autoClear:function(a){return e.$emit("autoStoreClear",t.code)}}})],1):"procedure"==t.searchtype?a("div",[a("ProcAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getProcName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoProcClear")}}})],1):"roleproc"==t.searchtype?a("div",[a("RoleProcAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getRoleProcName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoRoleProcClear")}}})],1):"goods"==t.searchtype?a("div",[a("GoodsAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getGoodsName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoGoodsClear")}}})],1):e._e()]):e._e()],1)],1)]):e._e()]}))],2)],1)]}))],2)},l=[],c=a("82f0"),d=c["a"],u=(a("1065"),a("2877")),f=Object(u["a"])(d,s,l,!1,null,"08275700",null),m=f.exports,p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{ref:"formdata",staticClass:"footFormContent",attrs:{"label-width":"100px"}},[e._l(e.tempData,(function(t,r){return[a("div",{key:r},["divider"==t.type?a("el-divider",{attrs:{"content-position":t.center?t.center:"left"}},[e._v(e._s(t.label))]):"foot"==t.type?a("el-row",[e._l(t.rowitem,(function(t,r){return[a("el-col",{directives:[{name:"show",rawName:"v-show",value:"assessdate"!=t.code||e.formdata.assessor,expression:"b.code == 'assessdate' ? formdata.assessor : true"}],key:r,attrs:{span:t.col}},[a("el-form-item",{attrs:{label:t.label,prop:t.code}},["assessdate"==t.code||"modifydate"==t.code||"createdate"==t.code?a("div",[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata[t.code],expression:"formdata[b.code]"}],staticClass:"el-form-item__label",staticStyle:{"white-space":"nowrap"}},[e._v(e._s(e._f("dateFormats")(e.formdata[t.code])))])]):a("div",[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata[t.code],expression:"formdata[b.code]"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata[t.code]))])])])],1)]}))],2):a("el-row",[e._l(t.rowitem,(function(t,r){return[(t.show?e.returnEval(t.show):!t.show)?a("el-col",{key:r,attrs:{span:t.col}},[a("div",{on:{click:function(a){return e.cleValidate(t.code)}}},[a("el-form-item",{attrs:{label:"checkbox"==t.type?"":t.label,prop:t.code}},["input"==t.type?a("el-input",{attrs:{placeholder:"请输入"+t.label,clearable:"",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}}):"select"==t.type?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"+t.label,size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},on:{change:function(a){return e.clickMethods(t.methods,t.param)}},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}},e._l(t.options,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1):"checkbox"==t.type?a("div",[a("el-checkbox",{attrs:{label:t.label,"true-label":0,"false-label":1,size:t.size?t.size:"mini",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},on:{change:function(a){return e.clickMethods(t.methods,t.param)}},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"number"==t.type?a("div",[a("el-input-number",{attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},on:{change:function(a){return e.clickMethods(t.methods,t.param)}},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"date"==t.type?a("div",[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:t.size?t.size:"small",clearable:"",placeholder:"选择日期",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"dictionary"==t.type?a("div",[a("el-popover",{ref:t.code+"PropRef",refInFor:!0,attrs:{placement:"bottom",trigger:"click"},on:{show:function(a){e.$refs[t.code+"Ref"][0].bindData()}}},[a("selDictionaries",{ref:t.code+"Ref",refInFor:!0,staticStyle:{width:"200px"},attrs:{multi:0,billcode:t.billcode},on:{singleSel:function(a){e.formdata[t.code]=a.dictvalue,e.$refs[t.code+"PropRef"][0].doClose()},closedic:function(a){e.$refs[t.code+"PropRef"][0].doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择"+t.label,clearable:"",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1)],1)],1):"textarea"==t.type?a("div",[a("el-input",{attrs:{placeholder:"请输入"+t.label,clearable:"",type:"textarea",size:t.size?t.size:"small",autosize:{minRows:2,maxRows:4},disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"text"==t.type?a("div",[a("span",{staticStyle:{"font-size":"18px",color:"#606266"}},[e._v(e._s(e.formdata[t.code]))])]):"autocomplete"==t.type?a("div",["customer"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/S16M03R1/getOnlinePageList",type:"客户",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"supplier"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/D01M01B2/getOnlinePageList",type:"供应商",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getSuppGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"workshop"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/D01M01B3/getPageList",type:"生产车间",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getWorkGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"factory"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/D01M01B4/getPageList",type:"外协厂商",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getFactGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"branch"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled),baseurl:"/D01M01B5/getPageList",type:"部门"},on:{setRow:function(a){e.$emit("getBranGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"prospects"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled),baseurl:"/D01M01B6/getPageList",type:"潜在客户"},on:{setRow:function(a){e.$emit("getProsGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"group"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getAllGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"store"==t.searchtype?a("div",[a("StoreAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(t){return e.$emit("getStoreName",t)},autoClear:function(t){return e.$emit("autoStoreClear")}}})],1):"proc"==t.searchtype?a("div",[a("ProcAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(t){return e.$emit("getProcName",t)},autoClear:function(t){return e.$emit("autoProcClear")}}})],1):"roleproc"==t.searchtype?a("div",[a("RoleProcAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(t){return e.$emit("getRoleProcName",t)},autoClear:function(t){return e.$emit("autoRoleProcClear")}}})],1):"goods"==t.searchtype?a("div",[a("GoodsAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(t){return e.$emit("getGoodsName",t)},autoClear:function(t){return e.$emit("autoGoodsClear")}}})],1):e._e()]):e._e()],1)],1)]):e._e()]}))],2)],1)]}))],2)],1)},h=[],b=a("e08f"),v=b["a"],g=(a("bec6"),Object(u["a"])(v,p,h,!1,null,"3c138b22",null)),_=g.exports,y={},w=Object(u["a"])(y,r,o,!1,null,null,null),k=w.exports,x={components:{Header:m,Item:k,Footer:_},props:{formdata:{type:Object},formtemplate:{type:Object},selectform:{type:[Object,Array]}},methods:{getStoreName:function(e,t){this.$emit("getStoreName",e,t)}}},C=x,$=Object(u["a"])(C,i,n,!1,null,null,null);t["a"]=$.exports},e08f:function(module,__webpack_exports__,__webpack_require__){"use strict";var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("b64b"),core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__),_views_modules_Sa_SaDict_components_select_vue__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("5c73");__webpack_exports__["a"]={components:{selDictionaries:_views_modules_Sa_SaDict_components_select_vue__WEBPACK_IMPORTED_MODULE_1__["a"]},props:{title:{type:String,default:"单据标题"},formdata:{type:Object,default:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname}},formtemplate:{type:[Array],default:function(){return[]}}},data:function(){return{tempData:this.formtemplate}},watch:{formtemplate:function(e,t){console.log("foot",e),this.formtemplate.length?this.tempData=this.formtemplate:this.tempData=[{type:"divider",label:"",center:""},{type:"form",item:[{col:23,type:"input",code:"summary",label:"摘  要"}]},{type:"foot",item:[{col:4,type:"text",code:"createby",label:"创建人"},{col:4,type:"text",code:"createdate",label:"创建日期"},{col:4,type:"text",code:"lister",label:"制表"},{col:4,type:"text",code:"modifydate",label:"修改日期"},{col:4,type:"text",code:"assessor",label:"审核"},{col:4,type:"text",code:"assessdate",label:"审核日期"}]}]}},mounted:function(){},methods:{returnEval:function returnEval(data,itrue){return"string"==typeof data?eval(data):data},cleValidate:function(e){this.$refs.formdata.clearValidate(e)},clickMethods:function(e,t){var a={meth:e,param:t};this.$emit("clickMethods",a)}}}},e2bf:function(e,t,a){},e82a:function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click","hide-on-click":!1,placement:"bottom"}},[a("el-button",{staticStyle:{padding:"7px 15px"},attrs:{size:"small",icon:"el-icon-edit-outline"}},[e._v("快选"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{staticClass:"quickTick myscrollbar",attrs:{slot:"dropdown"},slot:"dropdown"},[a("div",{staticClass:"fixed"},[a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{placeholder:"搜索",size:"small",clearable:""},on:{focus:function(e){return e.currentTarget.select()},input:function(t){return e.$emit("quickTickSearch",e.searchVal)}},model:{value:e.searchVal,callback:function(t){e.searchVal="string"===typeof t?t.trim():t},expression:"searchVal"}})],1),a("el-dropdown-item",{staticStyle:{"border-bottom":"1px solid #edecec"}},[a("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.changeAll},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选")])],1)],1),e._l(e.droupList,(function(t,r){return a("el-dropdown-item",{key:r},[a("el-tooltip",{attrs:{effect:"dark",content:t.label,placement:"left"}},[a("el-checkbox",{attrs:{label:t.label},on:{change:function(a){return e.$emit("quickTickBtn",t)}},model:{value:t.show,callback:function(a){e.$set(t,"show",a)},expression:"item.show"}},[a("span",{staticClass:"quickTickLabel"},[e._v(e._s(t.label))])])],1)],1)}))],2)],1)],1)},o=[],i={props:["droupList"],data:function(){return{checkAll:!1,isIndeterminate:!1,searchVal:""}},created:function(){},watch:{droupList:function(e,t){this.checkAll=!0;for(var a=0;a<this.droupList.length;a++)if(!this.droupList[a].show){this.checkAll=!1;break}}},methods:{changeAll:function(e){for(var t=0;t<this.droupList.length;t++){var a=this.droupList[t];a.show=e,this.$emit("quickTickBtn",a)}}}},n=i,s=(a("8efa"),a("2877")),l=Object(s["a"])(n,r,o,!1,null,"01b4660f",null);t["a"]=l.exports},ee81:function(e,t,a){"use strict";a("dc0f")},f07e:function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"mypopver"},[a("div",{staticClass:"mypopver-card"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"itemTable",staticClass:"tb-edit tableBox",attrs:{data:e.lst,border:"",height:"220px",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}}},[a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"40",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.$index+1))])]}}])}),0!=e.lst.length&&e.lst[0].hasOwnProperty("wkwpname")?a("el-table-column",{attrs:{label:"当前工序",align:"center","min-width":"100","show-overflow-tooltip":"",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{class:t.row.wkwpname?"textborder":"",style:{background:e.getBackGroundColor(t.row,"wkwpid"),color:e.getColor(t.row,"wkwpid")}},[e._v(" "+e._s(t.row.wkwpname)+" ")])]}}],null,!1,1048854122)}):e._e(),e._l(e.tableForm.item,(function(t){return[!t.displaymark?e._e():a("el-table-column",{key:t.id,attrs:{prop:t.itemcode,columnKey:t.itemcode,label:t.itemname,align:t.aligntype?t.aligntype:"center","min-width":t.minwidth,"show-overflow-tooltip":!!t.overflow,sortable:!!t.sortable,fixed:!!t.fixed},scopedSlots:e._u([{key:"default",fn:function(r){return["itemorgdate"==t.itemcode||"itemplandate"==t.itemcode||"plandate"==t.itemcode||"startdate"==t.itemcode||"enddate"==t.itemcode||"billdate"==t.itemcode?a("div",[a("span",[e._v(e._s(e._f("dateFormat")(r.row[t.itemcode])))])]):"bomtype"==t.itemcode?a("div",[a("span",[e._v(e._s(e._f("bomtypeFormate")(r.row.bomtype)))])]):"wpname"==t.itemcode?a("div",[a("div",{class:r.row.wpname?"textborder":"",style:{background:e.getBackGroundColor(r.row,"wpid"),color:e.getColor(r.row,"wpid")}},[e._v(" "+e._s(r.row.wpname)+" ")])]):"requisite"==t.itemcode||"stonly"==t.itemcode?a("div",[a("el-checkbox",{attrs:{"true-label":1,"false-label":0,size:"mini"},model:{value:r.row[t.itemcode],callback:function(a){e.$set(r.row,t.itemcode,a)},expression:"scope.row[i.itemcode]"}})],1):"matstatus"==t.itemcode?a("div",[r.row.matcode?a("div",[r.row.matused?a("el-tag",{attrs:{effect:"dark",size:"small"}},[e._v("已领 ")]):a("el-tag",{attrs:{effect:"plain",size:"small"}},[e._v("未领 ")])],1):e._e()]):"compcost"==t.itemcode?a("div",[r.row.quantity-r.row.finishqty<0?a("span",{staticStyle:{color:"#f56c6c"}},[e._v(e._s(r.row.quantity-r.row.finishqty))]):r.row.quantity-r.row.finishqty==0?a("span",{staticStyle:{color:"#67c23a"}},[e._v(e._s(r.row.quantity-r.row.finishqty))]):r.row.quantity-r.row.finishqty==r.row.quantity?a("span",[e._v(e._s(r.row.quantity-r.row.finishqty))]):a("span",{staticStyle:{color:"#409eff"}},[e._v(e._s(r.row.quantity-r.row.finishqty))])]):a("span",[a("span",[e._v(e._s(r.row[t.itemcode]))])])]}}],null,!0)})]}))],2)],1)])},o=[],i=(a("99af"),a("c740"),a("e9c4"),a("d3b7"),a("25f0"),a("4d90"),a("b775")),n={props:{tableForm:{type:Object,default:function(){return{item:[]}}},lst:{type:Array,default:function(){return[]}},progressData:{type:Array,default:function(){return[]}}},data:function(){return{listLoading:!1}},created:function(){},updated:function(){var e=this;this.$nextTick((function(){e.$refs.itemTable.doLayout()}))},watch:{lst:function(e,t){0!=this.lst.length&&this.lst[0].hasOwnProperty("wkwpname")}},methods:{getprogressData:function(){var e=this,t={PageNum:1,PageSize:1e3,OrderType:0,SearchType:1,OrderBy:"rownum"};i["a"].post("/D05M21S1/getPageTh",JSON.stringify(t)).then((function(t){200==t.data.code&&(e.progressData=t.data.data.list)}))},getBackGroundColor:function(e,t){var a="#FFF";if(0==this.progressData.length)return a;var r=this.progressData.findIndex((function(a){return a.id==e[t]}));return-1!=r&&(a=this.progressData[r].backcolorargb?this.progressData[r].backcolorargb:"#FFF"),a},getColor:function(e,t){var a="#000";if(0==this.progressData.length)return a;var r=this.progressData.findIndex((function(a){return a.id==e[t]}));return-1!=r&&(a=this.progressData[r].forecolorargb?this.progressData[r].forecolorargb:"#000"),a}},filters:{dateFormat:function(e){if(e){var t=new Date(e),a=t.getFullYear(),r=(t.getMonth()+1).toString().padStart(2,"0"),o=t.getDate().toString().padStart(2,"0");return"".concat(a,"-").concat(r,"-").concat(o)}},bomtypeFormate:function(e){return 0==e?"未选":1==e?"标准Bom":2==e?"订单Bom":3==e?"销售Bom":void 0}}},s=n,l=(a("ee81"),a("2877")),c=Object(l["a"])(s,r,o,!1,null,"b1d873e6",null);t["a"]=c.exports},f4e2:function(e,t,a){"use strict";a("4000")},f71d:function(e,t,a){a("a15b"),a("fb6a"),a("b0c0"),a("d3b7"),a("ac1f"),a("25f0"),a("466d"),
/*! @source http://purl.eligrey.com/github/Blob.js/blob/master/Blob.js */
function(e){"use strict";if(e.URL=e.URL||e.webkitURL,e.Blob&&e.URL)try{return void new Blob}catch(a){}var t=e.BlobBuilder||e.WebKitBlobBuilder||e.MozBlobBuilder||function(e){var t=function(e){return Object.prototype.toString.call(e).match(/^\[object\s(.*)\]$/)[1]},a=function(){this.data=[]},r=function(e,t,a){this.data=e,this.size=e.length,this.type=t,this.encoding=a},o=a.prototype,i=r.prototype,n=e.FileReaderSync,s=function(e){this.code=this[this.name=e]},l="NOT_FOUND_ERR SECURITY_ERR ABORT_ERR NOT_READABLE_ERR ENCODING_ERR NO_MODIFICATION_ALLOWED_ERR INVALID_STATE_ERR SYNTAX_ERR".split(" "),c=l.length,d=e.URL||e.webkitURL||e,u=d.createObjectURL,f=d.revokeObjectURL,m=d,p=e.btoa,h=e.atob,b=e.ArrayBuffer,v=e.Uint8Array;r.fake=i.fake=!0;while(c--)s.prototype[l[c]]=c+1;return d.createObjectURL||(m=e.URL={}),m.createObjectURL=function(e){var t,a=e.type;return null===a&&(a="application/octet-stream"),e instanceof r?(t="data:"+a,"base64"===e.encoding?t+";base64,"+e.data:"URI"===e.encoding?t+","+decodeURIComponent(e.data):p?t+";base64,"+p(e.data):t+","+encodeURIComponent(e.data)):u?u.call(d,e):void 0},m.revokeObjectURL=function(e){"data:"!==e.substring(0,5)&&f&&f.call(d,e)},o.append=function(e){var a=this.data;if(v&&(e instanceof b||e instanceof v)){for(var o="",i=new v(e),l=0,c=i.length;l<c;l++)o+=String.fromCharCode(i[l]);a.push(o)}else if("Blob"===t(e)||"File"===t(e)){if(!n)throw new s("NOT_READABLE_ERR");var d=new n;a.push(d.readAsBinaryString(e))}else e instanceof r?"base64"===e.encoding&&h?a.push(h(e.data)):"URI"===e.encoding?a.push(decodeURIComponent(e.data)):"raw"===e.encoding&&a.push(e.data):("string"!==typeof e&&(e+=""),a.push(unescape(encodeURIComponent(e))))},o.getBlob=function(e){return arguments.length||(e=null),new r(this.data.join(""),e,"raw")},o.toString=function(){return"[object BlobBuilder]"},i.slice=function(e,t,a){var o=arguments.length;return o<3&&(a=null),new r(this.data.slice(e,o>1?t:this.data.length),a,this.encoding)},i.toString=function(){return"[object Blob]"},i.close=function(){this.size=this.data.length=0},a}(e);e.Blob=function(e,a){var r=a&&a.type||"",o=new t;if(e)for(var i=0,n=e.length;i<n;i++)o.append(e[i]);return o.getBlob(r)}}("undefined"!==typeof self&&self||"undefined"!==typeof window&&window||this.content||this)}}]);