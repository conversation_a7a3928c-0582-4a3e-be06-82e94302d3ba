SET FOREIGN_KEY_CHECKS = 0;

CREATE TABLE `Sa_AuthCode`
(
    `id`           varchar(50)  NOT NULL COMMENT 'ID',
    `AuthCode`     varchar(50)  NULL DEFAULT NULL COMMENT '授权Code',
    `AuthDesc`     varchar(100) NULL DEFAULT NULL COMMENT '授权作用',
    `UserName`     varchar(50)  NOT NULL COMMENT '登录名',
    `UserPassword` varchar(50)  NOT NULL COMMENT '密码',
    `RowNum`       int          NOT NULL COMMENT '行号',
    `EnabledMark`  int          NOT NULL COMMENT '有效标识',
    `Remark`       varchar(500) NULL DEFAULT NULL COMMENT '备注',
    `CreateBy`     varchar(50)  NOT NULL COMMENT '创建者',
    `CreateByid`   varchar(50)  NOT NULL COMMENT '创建者id',
    `CreateDate`   datetime     NOT NULL COMMENT '新建日期',
    `Lister`       varchar(50)  NOT NULL COMMENT '制表',
    `Listerid`     varchar(50)  NOT NULL COMMENT '制表id',
    `ModifyDate`   datetime     NOT NULL COMMENT '修改日期',
    `Custom1`      varchar(100) NULL DEFAULT NULL COMMENT '自定义1',
    `Custom2`      varchar(100) NULL DEFAULT NULL COMMENT '自定义2',
    `Custom3`      varchar(100) NULL DEFAULT NULL COMMENT '自定义3',
    `Custom4`      varchar(100) NULL DEFAULT NULL COMMENT '自定义4',
    `Custom5`      varchar(100) NULL DEFAULT NULL COMMENT '自定义5',
    `Tenantid`     varchar(50)  NOT NULL COMMENT '租户id',
    `TenantName`   varchar(50)  NOT NULL COMMENT '租户Name',
    `Revision`     int          NOT NULL COMMENT '乐观锁'
);

CREATE TABLE `Sa_JustAuth`
(
    `id`         varchar(50)  NOT NULL COMMENT 'ID',
    `Userid`     varchar(50)  NOT NULL COMMENT '用户id',
    `UserName`   varchar(50)  NOT NULL COMMENT '登录名',
    `RealName`   varchar(50)  NOT NULL COMMENT '姓名',
    `NickName`   varchar(50)  NOT NULL COMMENT '昵称',
    `AuthType`   varchar(50)  NOT NULL COMMENT 'ding/wxe/openid',
    `AuthUuid`   varchar(50)  NOT NULL COMMENT 'uuid',
    `Unionid`    varchar(50)  NULL DEFAULT NULL COMMENT 'Unionid',
    `AuthAvatar` varchar(500) NULL DEFAULT NULL COMMENT 'avatar',
    `CreateBy`   varchar(50)  NOT NULL COMMENT '创建者',
    `CreateByid` varchar(50)  NOT NULL COMMENT '创建者id',
    `CreateDate` datetime     NOT NULL COMMENT '新建日期',
    `Lister`     varchar(50)  NOT NULL COMMENT '制表',
    `Listerid`   varchar(50)  NOT NULL COMMENT '制表id',
    `ModifyDate` datetime     NOT NULL COMMENT '修改日期',
    `Custom1`    varchar(100) NULL DEFAULT NULL COMMENT '自定义1',
    `Custom2`    varchar(100) NULL DEFAULT NULL COMMENT '自定义2',
    `Custom3`    varchar(100) NULL DEFAULT NULL COMMENT '自定义3',
    `Custom4`    varchar(100) NULL DEFAULT NULL COMMENT '自定义4',
    `Custom5`    varchar(100) NULL DEFAULT NULL COMMENT '自定义5',
    `Tenantid`   varchar(50)  NOT NULL COMMENT '租户id',
    `TenantName` varchar(50)  NULL DEFAULT NULL COMMENT '租户名称',
    `Revision`   int          NOT NULL COMMENT '乐观锁'
);
create table Sa_Attachment
(
    id          varchar(50)   not null comment 'ID'
        primary key,
    GenGroupid  varchar(50)   null comment '通用分组',
    FileOriName varchar(100)  not null comment '原文件名',
    BucketName  varchar(50)   not null comment '文件桶',
    DirName     varchar(50)   not null comment '目录',
    FileName    varchar(50)   not null comment '文件名',
    FileSize    bigint        not null comment '文件大小',
    ContentType varchar(100)  not null comment '文件格式',
    FileSuffix  varchar(50)   not null comment '文件后缀 扩展名',
    Storage     varchar(50)   not null comment '存储方式',
    Relateid    varchar(50)   null comment '关联id 单据id',
    FileUrl     varchar(1000) not null comment 'OSS位置',
    EnabledMark int           not null comment '有效',
    RowNum      int           not null comment '行号',
    Remark      varchar(1000) null comment '备注',
    CreateBy    varchar(50)   not null comment '创建者',
    CreateByid  varchar(50)   not null comment '创建者id',
    CreateDate  datetime      not null comment '新建日期',
    Lister      varchar(50)   not null comment '制表',
    Listerid    varchar(50)   not null comment '制表id',
    ModifyDate  datetime      not null comment '修改日期',
    ModuleCode  varchar(50)   null comment '功能编码',
    Custom1     varchar(100)  null comment '自定义1',
    Custom2     varchar(100)  null comment '自定义2',
    Custom3     varchar(100)  null comment '自定义3',
    Custom4     varchar(100)  null comment '自定义4',
    Custom5     varchar(100)  null comment '自定义5',
    Custom6     varchar(100)  null comment '自定义6',
    Custom7     varchar(100)  null comment '自定义7',
    Custom8     varchar(100)  null comment '自定义8',
    Custom9     varchar(100)  null comment '自定义9',
    Custom10    varchar(100)  null comment '自定义10',
    Deptid      varchar(50)   not null comment '部门ID',
    Tenantid    varchar(50)   not null comment '租户id',
    TenantName  varchar(50)   null comment '租户名称',
    Revision    int           not null comment '乐观锁'
)
    comment '附件中心';

ALTER TABLE `Sa_Redis`
    MODIFY COLUMN `RedisValue` mediumtext NOT NULL COMMENT 'value' AFTER `RedisKey`;

ALTER TABLE `Sa_User`
    MODIFY COLUMN `Avatar` varchar(1000) NULL DEFAULT NULL COMMENT '邮箱' AFTER `Sex`;

ALTER TABLE `Sa_User`
    DROP COLUMN `WxOpenid`;

ALTER TABLE `Sa_User`
    DROP COLUMN `DingUserid`;

alter table Buy_PlanItem
    add PlanQty decimal(18, 4) null comment '需求数量' after MergeFinishQty;
SET FOREIGN_KEY_CHECKS = 1;
alter table Buy_Plan
    add Trimor varchar(50) null comment '调整人员' after MergeCount;

alter table Buy_Plan
    add Trimdate datetime null comment '调整日期' after Trimor;
ALTER TABLE Mat_Transfer
    ADD COLUMN ReturnUid VARCHAR(50) NULL COMMENT '红冲单号' AFTER ModifyDate,
    ADD COLUMN OrgUid    VARCHAR(50) NULL COMMENT '原始单号' AFTER ReturnUid;
ALTER TABLE Wk_MrpItem
    ADD COLUMN GrossQty decimal(18, 4) null comment '毛需求' AFTER AttributeJson,
    ADD COLUMN MergeMark int null comment '1被合并2合并为' AFTER GrossQty;
ALTER TABLE Bus_MachiningItem
    ADD InvoAmt DECIMAL(18, 4) COMMENT '开票额' AFTER MainPlanClosed;

ALTER TABLE Buy_OrderItem
    ADD InvoAmt DECIMAL(18, 4) COMMENT '开票额' AFTER AvgLastAmt;

