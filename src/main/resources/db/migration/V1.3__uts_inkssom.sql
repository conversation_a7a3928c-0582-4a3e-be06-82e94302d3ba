SET FOREIGN_KEY_CHECKS=0;

CREATE TABLE `Uts_Database`  (
                                           `id` varchar(50) NOT NULL COMMENT 'id',
                                           `Title` varchar(50) NOT NULL COMMENT '标题',
                                           `Url` varchar(255) NOT NULL COMMENT '数据库url',
                                           `UserName` varchar(50) NOT NULL COMMENT '数据库username',
                                           `Password` varchar(50) NOT NULL COMMENT '数据库password',
                                           `DriverClassName` varchar(255) NOT NULL COMMENT '数据库驱动',
                                           `EnabledMark` int NOT NULL COMMENT '有效性',
                                           `RowNum` int NOT NULL COMMENT '行号',
                                           `Remark` varchar(100) NULL DEFAULT NULL COMMENT '备注',
                                           `CreateBy` varchar(50) NOT NULL COMMENT '创建者',
                                           `CreateByid` varchar(50) NOT NULL COMMENT '创建者id',
                                           `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                           `Lister` varchar(50) NOT NULL COMMENT '制表',
                                           `Listerid` varchar(50) NOT NULL COMMENT '制表id',
                                           `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                           `Tenantid` varchar(50) NOT NULL COMMENT '租户id',
                                           `TenantName` varchar(50) NULL DEFAULT NULL COMMENT '租户名称',
                                           `Revision` int NOT NULL COMMENT '乐观锁'
);

CREATE TABLE `Uts_FreeReports`  (
                                              `id` varchar(50) NOT NULL COMMENT 'id',
                                              `FRType` varchar(100) NOT NULL COMMENT '报表Type',
                                              `FRGroupid` varchar(50) NOT NULL COMMENT '报表分组id',
                                              `LocalMark` int NOT NULL COMMENT '本地数据库',
                                              `ReportCode` varchar(50) NOT NULL COMMENT '报表编码',
                                              `ReportName` varchar(50) NOT NULL COMMENT '报表名称',
                                              `DynType` varchar(50) NULL DEFAULT NULL COMMENT '动态查询类型sql、http',
                                              `DynSentence` text NULL COMMENT '动态查询sql或者接口中的请求体',
                                              `SqlFull` text NOT NULL COMMENT '拼接的完整SQL',
                                              `SqlSelect` text NOT NULL COMMENT '查询字段',
                                              `SqlFrom` varchar(1000) NOT NULL COMMENT '表名join',
                                              `SqlWhere` varchar(1000) NOT NULL COMMENT '查询条件',
                                              `SqlGroupBy` varchar(1000) NOT NULL COMMENT '分组操作',
                                              `SqlHaving` varchar(1000) NOT NULL COMMENT '聚合筛选',
                                              `SqlOrderBy` varchar(1000) NOT NULL COMMENT '排序',
                                              `SqlLimit` varchar(1000) NOT NULL COMMENT '限制输出',
                                              `MainTable` varchar(50) NOT NULL COMMENT '主表',
                                              `EnabledMark` int NOT NULL COMMENT '有效性',
                                              `PublicMark` int NOT NULL COMMENT '公共报表',
                                              `Databaseid` varchar(50) NOT NULL COMMENT '数据库连接id',
                                              `DomainNum` int NULL DEFAULT NULL COMMENT ' Userid来源:0本地,1rms',
                                              `Userid` varchar(50) NOT NULL COMMENT '所属用户id',
                                              `ImageIndex` varchar(500) NULL DEFAULT NULL COMMENT '图标',
                                              `PermissionCode` varchar(50) NULL DEFAULT NULL COMMENT '许可编码',
                                              `ImageStyle` varchar(50) NOT NULL COMMENT '图标样式',
                                              `DatePath` varchar(100) NULL DEFAULT NULL COMMENT '提取数据的路径',
                                              `AuthCode` varchar(50) NULL DEFAULT NULL COMMENT 'authcode授权码',
                                              `CaseResult` text NULL COMMENT '结果案例',
                                              `ReportType` varchar(50) NULL DEFAULT NULL COMMENT '报表类型',
                                              `ChartType` varchar(50) NULL DEFAULT NULL COMMENT '图表类型',
                                              `RowNum` int NOT NULL COMMENT 'RowNum',
                                              `Summary` varchar(200) NULL DEFAULT NULL COMMENT '摘要',
                                              `CreateBy` varchar(50) NOT NULL COMMENT '创建者',
                                              `CreateByid` varchar(50) NOT NULL COMMENT '创建者id',
                                              `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                              `Lister` varchar(50) NOT NULL COMMENT '制表',
                                              `Listerid` varchar(50) NOT NULL COMMENT '制表id',
                                              `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                              `Custom1` varchar(100) NULL DEFAULT NULL COMMENT '自定义1',
                                              `Custom2` varchar(100) NULL DEFAULT NULL COMMENT '自定义2',
                                              `Custom3` varchar(100) NULL DEFAULT NULL COMMENT '自定义3',
                                              `Custom4` varchar(100) NULL DEFAULT NULL COMMENT '自定义4',
                                              `Custom5` varchar(100) NULL DEFAULT NULL COMMENT '自定义5',
                                              `Tenantid` varchar(50) NOT NULL COMMENT '租户id',
                                              `TenantName` varchar(50) NULL DEFAULT NULL COMMENT '租户名称',
                                              `Revision` int NOT NULL COMMENT '乐观锁'
);

CREATE TABLE `Uts_FreeReportsItem`  (
                                                  `id` varchar(50) NOT NULL COMMENT 'id',
                                                  `Pid` varchar(50) NOT NULL COMMENT 'Pid',
                                                  `FieldType` varchar(50) NOT NULL COMMENT '字段类型',
                                                  `FieldName` varchar(50) NOT NULL COMMENT '字段名称',
                                                  `HeaderText` varchar(50) NOT NULL COMMENT '列头显示',
                                                  `DataPropertyName` varchar(50) NOT NULL COMMENT '数据邦定',
                                                  `OrderStr` varchar(50) NOT NULL COMMENT '排序字符',
                                                  `ColWidth` int NOT NULL COMMENT '列宽',
                                                  `ColAlign` varchar(50) NOT NULL COMMENT '列对齐',
                                                  `DisplayNo` int NOT NULL COMMENT '显示编号',
                                                  `DisplayState` int NOT NULL COMMENT '显示状态',
                                                  `FormatString` varchar(50) NULL DEFAULT NULL COMMENT '文本格式',
                                                  `DefWidth` varchar(50) NOT NULL COMMENT '默认宽度',
                                                  `MinWidth` varchar(50) NULL DEFAULT NULL COMMENT '最小宽度',
                                                  `Fixed` int NOT NULL COMMENT '1固定0否',
                                                  `Sortable` int NOT NULL COMMENT '1可排序',
                                                  `OrderField` varchar(50) NULL DEFAULT NULL COMMENT '排序表.字段',
                                                  `Overflow` int NOT NULL COMMENT '1溢出隐藏',
                                                  `Formatter` varchar(50) NULL DEFAULT NULL COMMENT '格式化',
                                                  `ClassName` varchar(50) NULL DEFAULT NULL COMMENT '自定义类',
                                                  `AlignType` varchar(50) NOT NULL COMMENT 'left/center/right',
                                                  `EventName` varchar(50) NULL DEFAULT NULL COMMENT '事件名称',
                                                  `EditMark` int NULL DEFAULT NULL COMMENT '可编辑',
                                                  `OperationMark` int NULL DEFAULT NULL COMMENT '可操作',
                                                  `DisplayIndex` int NULL DEFAULT NULL COMMENT '显示位',
                                                  `RowNum` int NOT NULL COMMENT 'RowNum',
                                                  `Custom1` varchar(100) NULL DEFAULT NULL COMMENT '自定义1',
                                                  `Custom2` varchar(100) NULL DEFAULT NULL COMMENT '自定义2',
                                                  `Custom3` varchar(100) NULL DEFAULT NULL COMMENT '自定义3',
                                                  `Custom4` varchar(100) NULL DEFAULT NULL COMMENT '自定义4',
                                                  `Custom5` varchar(100) NULL DEFAULT NULL COMMENT '自定义5',
                                                  `Tenantid` varchar(50) NOT NULL COMMENT '租户id',
                                                  `TenantName` varchar(50) NULL DEFAULT NULL COMMENT '租户名称',
                                                  `Revision` int NOT NULL COMMENT '乐观锁'
);

SET FOREIGN_KEY_CHECKS=1;