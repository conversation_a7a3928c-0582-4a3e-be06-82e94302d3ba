2025-01-08 12:57:29,065 - Initializing Velocity, Calling init()...
2025-01-08 12:57:29,065 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 12:57:29,065 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 12:57:29,065 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 12:57:29,065 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-01-08 12:57:29,065 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:57:29,065 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:57:29,080 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 12:57:29,084 - Do unicode file recognition:  false
2025-01-08 12:57:29,084 - FileResourceLoader : adding path '.'
2025-01-08 12:57:29,185 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 12:57:29,196 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 12:57:29,199 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 12:57:29,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 12:57:29,204 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 12:57:29,206 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 12:57:29,210 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 12:57:29,213 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 12:57:29,216 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 12:57:29,219 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 12:57:29,380 - Created '20' parsers.
2025-01-08 12:57:29,386 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 12:57:29,387 - Velocimacro : Default library not found.
2025-01-08 12:57:29,387 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 12:57:29,387 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 12:57:29,387 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 12:57:29,387 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 12:58:14,484 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 12:58:14,485 - Initializing Velocity, Calling init()...
2025-01-08 12:58:14,485 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 12:58:14,485 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 12:58:14,485 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 12:58:14,485 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 12:58:14,485 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:58:14,485 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:58:14,485 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 12:58:14,485 - Do unicode file recognition:  false
2025-01-08 12:58:14,485 - FileResourceLoader : adding path '.'
2025-01-08 12:58:14,485 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 12:58:14,486 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 12:58:14,486 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 12:58:14,486 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 12:58:14,486 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 12:58:14,486 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 12:58:14,486 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 12:58:14,486 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 12:58:14,486 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 12:58:14,486 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 12:58:14,487 - Created '20' parsers.
2025-01-08 12:58:14,487 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 12:58:14,487 - Velocimacro : Default library not found.
2025-01-08 12:58:14,487 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 12:58:14,487 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 12:58:14,487 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 12:58:14,487 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 12:58:36,653 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 12:58:36,654 - Initializing Velocity, Calling init()...
2025-01-08 12:58:36,654 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 12:58:36,654 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 12:58:36,654 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 12:58:36,654 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 12:58:36,654 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:58:36,654 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:58:36,654 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 12:58:36,654 - Do unicode file recognition:  false
2025-01-08 12:58:36,654 - FileResourceLoader : adding path '.'
2025-01-08 12:58:36,654 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 12:58:36,655 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 12:58:36,655 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 12:58:36,655 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 12:58:36,655 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 12:58:36,655 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 12:58:36,655 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 12:58:36,655 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 12:58:36,655 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 12:58:36,655 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 12:58:36,656 - Created '20' parsers.
2025-01-08 12:58:36,656 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 12:58:36,656 - Velocimacro : Default library not found.
2025-01-08 12:58:36,656 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 12:58:36,656 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 12:58:36,656 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 12:58:36,656 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 12:59:24,959 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 12:59:24,960 - Initializing Velocity, Calling init()...
2025-01-08 12:59:24,960 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 12:59:24,960 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 12:59:24,960 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 12:59:24,960 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 12:59:24,960 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:59:24,960 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:59:24,960 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 12:59:24,960 - Do unicode file recognition:  false
2025-01-08 12:59:24,960 - FileResourceLoader : adding path '.'
2025-01-08 12:59:24,960 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 12:59:24,962 - Created '20' parsers.
2025-01-08 12:59:24,962 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 12:59:24,962 - Velocimacro : Default library not found.
2025-01-08 12:59:24,962 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 12:59:24,962 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 12:59:24,962 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 12:59:24,962 - Velocimacro : autoload off : VM system will not automatically reload global library macros
in scope if allowed.
2025-01-08 12:58:36,656 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 12:58:36,656 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 12:59:24,959 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 12:59:24,959 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 12:59:24,959 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 12:59:24,960 - Initializing Velocity, Calling init()...
2025-01-08 12:59:24,960 - Initializing Velocity, Calling init()...
2025-01-08 12:59:24,960 - Initializing Velocity, Calling init()...
2025-01-08 12:59:24,960 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 12:59:24,960 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 12:59:24,960 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 12:59:24,960 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 12:59:24,960 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 12:59:24,960 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 12:59:24,960 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 12:59:24,960 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 12:59:24,960 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 12:59:24,960 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 12:59:24,960 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 12:59:24,960 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 12:59:24,960 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:59:24,960 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:59:24,960 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:59:24,960 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:59:24,960 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:59:24,960 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 12:59:24,960 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 12:59:24,960 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 12:59:24,960 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 12:59:24,960 - Do unicode file recognition:  false
2025-01-08 12:59:24,960 - Do unicode file recognition:  false
2025-01-08 12:59:24,960 - Do unicode file recognition:  false
2025-01-08 12:59:24,960 - FileResourceLoader : adding path '.'
2025-01-08 12:59:24,960 - FileResourceLoader : adding path '.'
2025-01-08 12:59:24,960 - FileResourceLoader : adding path '.'
2025-01-08 12:59:24,960 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 12:59:24,960 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 12:59:24,960 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 12:59:24,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 12:59:24,962 - Created '20' parsers.
2025-01-08 12:59:24,962 - Created '20' parsers.
2025-01-08 12:59:24,962 - Created '20' parsers.
2025-01-08 12:59:24,962 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 12:59:24,962 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 12:59:24,962 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 12:59:24,962 - Velocimacro : Default library not found.
2025-01-08 12:59:24,962 - Velocimacro : Default library not found.
2025-01-08 12:59:24,962 - Velocimacro : Default library not found.
2025-01-08 12:59:24,962 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 12:59:24,962 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 12:59:24,962 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 12:59:24,962 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 12:59:24,962 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 12:59:24,962 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 12:59:24,962 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 12:59:24,962 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 12:59:24,962 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 12:59:24,962 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 12:59:24,962 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 12:59:24,962 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:24:56,603 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:24:56,604 - Initializing Velocity, Calling init()...
2025-01-08 13:24:56,604 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:24:56,604 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:24:56,604 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:24:56,604 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-01-08 13:24:56,604 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:24:56,604 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:24:56,618 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:24:56,622 - Do unicode file recognition:  false
2025-01-08 13:24:56,622 - FileResourceLoader : adding path '.'
2025-01-08 13:24:56,715 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:24:56,723 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:24:56,726 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:24:56,728 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:24:56,730 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:24:56,732 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:24:56,734 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:24:56,737 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:24:56,739 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:24:56,742 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:24:56,871 - Created '20' parsers.
2025-01-08 13:24:56,877 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:24:56,877 - Velocimacro : Default library not found.
2025-01-08 13:24:56,877 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:24:56,877 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:24:56,877 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:24:56,877 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:25:28,232 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:25:28,232 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:25:28,233 - Initializing Velocity, Calling init()...
2025-01-08 13:25:28,233 - Initializing Velocity, Calling init()...
2025-01-08 13:25:28,233 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:25:28,233 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:25:28,233 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:25:28,233 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:25:28,233 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:25:28,233 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:25:28,233 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:25:28,233 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:25:28,233 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:25:28,233 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:25:28,233 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:25:28,233 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:25:28,233 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:25:28,233 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:25:28,233 - Do unicode file recognition:  false
2025-01-08 13:25:28,233 - Do unicode file recognition:  false
2025-01-08 13:25:28,233 - FileResourceLoader : adding path '.'
2025-01-08 13:25:28,233 - FileResourceLoader : adding path '.'
2025-01-08 13:25:28,233 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:25:28,233 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:25:28,234 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:25:28,235 - Created '20' parsers.
2025-01-08 13:25:28,235 - Created '20' parsers.
2025-01-08 13:25:28,235 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:25:28,235 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:25:28,235 - Velocimacro : Default library not found.
2025-01-08 13:25:28,235 - Velocimacro : Default library not found.
2025-01-08 13:25:28,235 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:25:28,235 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:25:28,235 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:25:28,235 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:25:28,235 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:25:28,235 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:25:28,235 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:25:28,235 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:25:49,625 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:25:49,625 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:25:49,625 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:25:49,626 - Initializing Velocity, Calling init()...
2025-01-08 13:25:49,626 - Initializing Velocity, Calling init()...
2025-01-08 13:25:49,626 - Initializing Velocity, Calling init()...
2025-01-08 13:25:49,626 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:25:49,626 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:25:49,626 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:25:49,626 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:25:49,626 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:25:49,626 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:25:49,626 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:25:49,626 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:25:49,626 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:25:49,626 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:25:49,626 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:25:49,626 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:25:49,626 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:25:49,626 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:25:49,626 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:25:49,626 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:25:49,626 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:25:49,626 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:25:49,626 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:25:49,626 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:25:49,626 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:25:49,626 - Do unicode file recognition:  false
2025-01-08 13:25:49,626 - Do unicode file recognition:  false
2025-01-08 13:25:49,626 - Do unicode file recognition:  false
2025-01-08 13:25:49,626 - FileResourceLoader : adding path '.'
2025-01-08 13:25:49,626 - FileResourceLoader : adding path '.'
2025-01-08 13:25:49,626 - FileResourceLoader : adding path '.'
2025-01-08 13:25:49,627 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:25:49,627 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:25:49,627 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:25:49,627 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:25:49,628 - Created '20' parsers.
2025-01-08 13:25:49,628 - Created '20' parsers.
2025-01-08 13:25:49,628 - Created '20' parsers.
2025-01-08 13:25:49,628 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:25:49,628 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:25:49,628 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:25:49,628 - Velocimacro : Default library not found.
2025-01-08 13:25:49,628 - Velocimacro : Default library not found.
2025-01-08 13:25:49,628 - Velocimacro : Default library not found.
2025-01-08 13:25:49,628 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:25:49,628 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:25:49,628 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:25:49,628 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:25:49,628 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:25:49,628 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:25:49,628 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:25:49,628 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:25:49,628 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:25:49,628 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:25:49,628 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:25:49,628 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:30:35,237 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:30:35,237 - Initializing Velocity, Calling init()...
2025-01-08 13:30:35,237 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:30:35,237 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:30:35,237 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:30:35,237 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-01-08 13:30:35,237 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:30:35,237 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:30:35,250 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:30:35,254 - Do unicode file recognition:  false
2025-01-08 13:30:35,254 - FileResourceLoader : adding path '.'
2025-01-08 13:30:35,338 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:30:35,347 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:30:35,350 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:30:35,353 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:30:35,354 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:30:35,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:30:35,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:30:35,363 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:30:35,365 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:30:35,368 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:30:35,500 - Created '20' parsers.
2025-01-08 13:30:35,507 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:30:35,508 - Velocimacro : Default library not found.
2025-01-08 13:30:35,508 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:30:35,508 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:30:35,508 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:30:35,508 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:32:28,131 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:32:28,131 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:32:28,132 - Initializing Velocity, Calling init()...
2025-01-08 13:32:28,132 - Initializing Velocity, Calling init()...
2025-01-08 13:32:28,132 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:32:28,132 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:32:28,132 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:32:28,132 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:32:28,132 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:32:28,132 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:32:28,132 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:32:28,132 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:32:28,132 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:32:28,132 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:32:28,132 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:32:28,132 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:32:28,132 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:32:28,132 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:32:28,132 - Do unicode file recognition:  false
2025-01-08 13:32:28,132 - Do unicode file recognition:  false
2025-01-08 13:32:28,132 - FileResourceLoader : adding path '.'
2025-01-08 13:32:28,132 - FileResourceLoader : adding path '.'
2025-01-08 13:32:28,132 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:32:28,132 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:32:28,133 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:32:28,134 - Created '20' parsers.
2025-01-08 13:32:28,134 - Created '20' parsers.
2025-01-08 13:32:28,134 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:32:28,134 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:32:28,134 - Velocimacro : Default library not found.
2025-01-08 13:32:28,134 - Velocimacro : Default library not found.
2025-01-08 13:32:28,134 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:32:28,134 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:32:28,134 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:32:28,134 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:32:28,134 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:32:28,134 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:32:28,134 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:32:28,134 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:33:13,378 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:33:13,378 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:33:13,378 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:33:13,379 - Initializing Velocity, Calling init()...
2025-01-08 13:33:13,379 - Initializing Velocity, Calling init()...
2025-01-08 13:33:13,379 - Initializing Velocity, Calling init()...
2025-01-08 13:33:13,379 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:33:13,379 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:33:13,379 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:33:13,379 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:33:13,379 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:33:13,379 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:33:13,379 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:33:13,379 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:33:13,379 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:33:13,379 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:33:13,379 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:33:13,379 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:33:13,379 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:33:13,379 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:33:13,379 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:33:13,379 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:33:13,379 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:33:13,379 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:33:13,379 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:33:13,379 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:33:13,379 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:33:13,379 - Do unicode file recognition:  false
2025-01-08 13:33:13,379 - Do unicode file recognition:  false
2025-01-08 13:33:13,379 - Do unicode file recognition:  false
2025-01-08 13:33:13,379 - FileResourceLoader : adding path '.'
2025-01-08 13:33:13,379 - FileResourceLoader : adding path '.'
2025-01-08 13:33:13,379 - FileResourceLoader : adding path '.'
2025-01-08 13:33:13,379 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:33:13,379 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:33:13,379 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:33:13,379 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:33:13,379 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:33:13,379 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:33:13,380 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:33:13,381 - Created '20' parsers.
2025-01-08 13:33:13,381 - Created '20' parsers.
2025-01-08 13:33:13,381 - Created '20' parsers.
2025-01-08 13:33:13,381 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:33:13,381 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:33:13,381 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:33:13,381 - Velocimacro : Default library not found.
2025-01-08 13:33:13,381 - Velocimacro : Default library not found.
2025-01-08 13:33:13,381 - Velocimacro : Default library not found.
2025-01-08 13:33:13,381 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:33:13,381 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:33:13,381 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:33:13,381 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:33:13,381 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:33:13,381 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:33:13,381 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:33:13,381 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:33:13,381 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:33:13,381 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:33:13,381 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:33:13,381 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:35:01,959 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:35:01,959 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:35:01,959 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:35:01,959 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:35:01,960 - Initializing Velocity, Calling init()...
2025-01-08 13:35:01,960 - Initializing Velocity, Calling init()...
2025-01-08 13:35:01,960 - Initializing Velocity, Calling init()...
2025-01-08 13:35:01,960 - Initializing Velocity, Calling init()...
2025-01-08 13:35:01,960 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:35:01,960 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:35:01,960 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:35:01,960 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:35:01,960 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:35:01,960 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:35:01,960 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:35:01,960 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:35:01,960 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:35:01,960 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:35:01,960 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:35:01,960 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:35:01,960 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:35:01,960 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:35:01,960 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:35:01,960 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:35:01,961 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:35:01,961 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:35:01,961 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:35:01,961 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:35:01,961 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:35:01,961 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:35:01,961 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:35:01,961 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:35:01,961 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:35:01,961 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:35:01,961 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:35:01,961 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:35:01,961 - Do unicode file recognition:  false
2025-01-08 13:35:01,961 - Do unicode file recognition:  false
2025-01-08 13:35:01,961 - Do unicode file recognition:  false
2025-01-08 13:35:01,961 - Do unicode file recognition:  false
2025-01-08 13:35:01,961 - FileResourceLoader : adding path '.'
2025-01-08 13:35:01,961 - FileResourceLoader : adding path '.'
2025-01-08 13:35:01,961 - FileResourceLoader : adding path '.'
2025-01-08 13:35:01,961 - FileResourceLoader : adding path '.'
2025-01-08 13:35:01,961 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:35:01,961 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:35:01,961 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:35:01,961 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:35:01,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:35:01,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:35:01,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:35:01,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:35:01,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:35:01,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:35:01,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:35:01,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:35:01,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:35:01,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:35:01,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:35:01,961 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:35:01,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:35:01,963 - Created '20' parsers.
2025-01-08 13:35:01,963 - Created '20' parsers.
2025-01-08 13:35:01,963 - Created '20' parsers.
2025-01-08 13:35:01,963 - Created '20' parsers.
2025-01-08 13:35:01,963 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:35:01,963 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:35:01,963 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:35:01,963 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:35:01,963 - Velocimacro : Default library not found.
2025-01-08 13:35:01,963 - Velocimacro : Default library not found.
2025-01-08 13:35:01,963 - Velocimacro : Default library not found.
2025-01-08 13:35:01,963 - Velocimacro : Default library not found.
2025-01-08 13:35:01,963 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:35:01,963 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:35:01,963 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:35:01,963 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:35:01,963 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:35:01,963 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:35:01,963 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:35:01,963 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:35:01,963 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:35:01,963 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:35:01,963 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:35:01,963 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:35:01,963 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:35:01,963 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:35:01,963 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:35:01,963 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:36:18,448 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:36:18,448 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:36:18,448 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:36:18,448 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:36:18,448 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:36:18,449 - Initializing Velocity, Calling init()...
2025-01-08 13:36:18,449 - Initializing Velocity, Calling init()...
2025-01-08 13:36:18,449 - Initializing Velocity, Calling init()...
2025-01-08 13:36:18,449 - Initializing Velocity, Calling init()...
2025-01-08 13:36:18,449 - Initializing Velocity, Calling init()...
2025-01-08 13:36:18,449 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:36:18,449 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:36:18,449 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:36:18,449 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:36:18,449 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:36:18,449 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:36:18,449 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:36:18,449 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:36:18,449 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:36:18,449 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:36:18,449 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:36:18,449 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:36:18,449 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:36:18,449 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:36:18,449 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:36:18,449 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:36:18,449 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:36:18,449 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:36:18,449 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:36:18,449 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:36:18,449 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:18,449 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:18,449 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:18,449 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:18,449 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:18,449 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:18,449 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:18,449 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:18,449 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:18,449 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:18,449 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:36:18,449 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:36:18,449 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:36:18,449 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:36:18,449 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:36:18,449 - Do unicode file recognition:  false
2025-01-08 13:36:18,449 - Do unicode file recognition:  false
2025-01-08 13:36:18,449 - Do unicode file recognition:  false
2025-01-08 13:36:18,449 - Do unicode file recognition:  false
2025-01-08 13:36:18,449 - Do unicode file recognition:  false
2025-01-08 13:36:18,449 - FileResourceLoader : adding path '.'
2025-01-08 13:36:18,449 - FileResourceLoader : adding path '.'
2025-01-08 13:36:18,449 - FileResourceLoader : adding path '.'
2025-01-08 13:36:18,449 - FileResourceLoader : adding path '.'
2025-01-08 13:36:18,449 - FileResourceLoader : adding path '.'
2025-01-08 13:36:18,449 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:36:18,449 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:36:18,449 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:36:18,449 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:36:18,449 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:36:18,450 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:36:18,451 - Created '20' parsers.
2025-01-08 13:36:18,451 - Created '20' parsers.
2025-01-08 13:36:18,451 - Created '20' parsers.
2025-01-08 13:36:18,451 - Created '20' parsers.
2025-01-08 13:36:18,451 - Created '20' parsers.
2025-01-08 13:36:18,451 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:36:18,451 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:36:18,451 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:36:18,451 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:36:18,451 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:36:18,451 - Velocimacro : Default library not found.
2025-01-08 13:36:18,451 - Velocimacro : Default library not found.
2025-01-08 13:36:18,451 - Velocimacro : Default library not found.
2025-01-08 13:36:18,451 - Velocimacro : Default library not found.
2025-01-08 13:36:18,451 - Velocimacro : Default library not found.
2025-01-08 13:36:18,451 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:36:18,451 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:36:18,451 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:36:18,451 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:36:18,451 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:36:18,451 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:36:18,451 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:36:18,451 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:36:18,451 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:36:18,451 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:36:18,451 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:36:18,451 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:36:18,451 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:36:18,451 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:36:18,451 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:36:18,451 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:36:18,451 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:36:18,451 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:36:18,451 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:36:18,451 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:36:56,475 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:36:56,475 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:36:56,475 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:36:56,475 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:36:56,475 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:36:56,475 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:36:56,475 - Initializing Velocity, Calling init()...
2025-01-08 13:36:56,475 - Initializing Velocity, Calling init()...
2025-01-08 13:36:56,475 - Initializing Velocity, Calling init()...
2025-01-08 13:36:56,475 - Initializing Velocity, Calling init()...
2025-01-08 13:36:56,475 - Initializing Velocity, Calling init()...
2025-01-08 13:36:56,475 - Initializing Velocity, Calling init()...
2025-01-08 13:36:56,475 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:36:56,475 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:36:56,475 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:36:56,475 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:36:56,475 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:36:56,475 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:36:56,475 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:36:56,475 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:36:56,475 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:36:56,475 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:36:56,475 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:36:56,475 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:36:56,475 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:36:56,475 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:36:56,475 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:36:56,475 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:36:56,475 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:36:56,475 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:36:56,476 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:36:56,476 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:36:56,476 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:36:56,476 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:36:56,476 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:36:56,476 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:36:56,476 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:56,476 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:56,476 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:56,476 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:56,476 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:56,476 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:56,476 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:56,476 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:56,476 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:56,476 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:56,476 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:56,476 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:36:56,476 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:36:56,476 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:36:56,476 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:36:56,476 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:36:56,476 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:36:56,476 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:36:56,476 - Do unicode file recognition:  false
2025-01-08 13:36:56,476 - Do unicode file recognition:  false
2025-01-08 13:36:56,476 - Do unicode file recognition:  false
2025-01-08 13:36:56,476 - Do unicode file recognition:  false
2025-01-08 13:36:56,476 - Do unicode file recognition:  false
2025-01-08 13:36:56,476 - Do unicode file recognition:  false
2025-01-08 13:36:56,476 - FileResourceLoader : adding path '.'
2025-01-08 13:36:56,476 - FileResourceLoader : adding path '.'
2025-01-08 13:36:56,476 - FileResourceLoader : adding path '.'
2025-01-08 13:36:56,476 - FileResourceLoader : adding path '.'
2025-01-08 13:36:56,476 - FileResourceLoader : adding path '.'
2025-01-08 13:36:56,476 - FileResourceLoader : adding path '.'
2025-01-08 13:36:56,476 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:36:56,476 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:36:56,476 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:36:56,476 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:36:56,476 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:36:56,476 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:36:56,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:36:56,477 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:36:56,477 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:36:56,477 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:36:56,477 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:36:56,477 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:36:56,477 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:36:56,477 - Created '20' parsers.
2025-01-08 13:36:56,477 - Created '20' parsers.
2025-01-08 13:36:56,477 - Created '20' parsers.
2025-01-08 13:36:56,477 - Created '20' parsers.
2025-01-08 13:36:56,477 - Created '20' parsers.
2025-01-08 13:36:56,477 - Created '20' parsers.
2025-01-08 13:36:56,477 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:36:56,477 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:36:56,477 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:36:56,477 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:36:56,477 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:36:56,477 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:36:56,477 - Velocimacro : Default library not found.
2025-01-08 13:36:56,477 - Velocimacro : Default library not found.
2025-01-08 13:36:56,477 - Velocimacro : Default library not found.
2025-01-08 13:36:56,477 - Velocimacro : Default library not found.
2025-01-08 13:36:56,477 - Velocimacro : Default library not found.
2025-01-08 13:36:56,477 - Velocimacro : Default library not found.
2025-01-08 13:36:56,477 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:36:56,477 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:36:56,477 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:36:56,477 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:36:56,477 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:36:56,477 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:36:56,478 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:36:56,478 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:36:56,478 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:36:56,478 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:36:56,478 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:36:56,478 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:36:56,478 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:36:56,478 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:36:56,478 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:36:56,478 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:36:56,478 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:36:56,478 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:36:56,478 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:36:56,478 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:36:56,478 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:36:56,478 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:36:56,478 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:36:56,478 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:37:53,718 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:37:53,718 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:37:53,718 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:37:53,718 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:37:53,718 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:37:53,718 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:37:53,718 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:37:53,719 - Initializing Velocity, Calling init()...
2025-01-08 13:37:53,719 - Initializing Velocity, Calling init()...
2025-01-08 13:37:53,719 - Initializing Velocity, Calling init()...
2025-01-08 13:37:53,719 - Initializing Velocity, Calling init()...
2025-01-08 13:37:53,719 - Initializing Velocity, Calling init()...
2025-01-08 13:37:53,719 - Initializing Velocity, Calling init()...
2025-01-08 13:37:53,719 - Initializing Velocity, Calling init()...
2025-01-08 13:37:53,719 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:37:53,719 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:37:53,719 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:37:53,719 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:37:53,719 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:37:53,719 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:37:53,719 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:37:53,719 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:37:53,719 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:37:53,719 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:37:53,719 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:37:53,719 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:37:53,719 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:37:53,719 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:37:53,719 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:37:53,719 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:37:53,719 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:37:53,719 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:37:53,719 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:37:53,719 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:37:53,719 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:37:53,719 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:37:53,719 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:37:53,719 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:37:53,719 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:37:53,719 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:37:53,719 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:37:53,719 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:37:53,720 - Do unicode file recognition:  false
2025-01-08 13:37:53,720 - Do unicode file recognition:  false
2025-01-08 13:37:53,720 - Do unicode file recognition:  false
2025-01-08 13:37:53,720 - Do unicode file recognition:  false
2025-01-08 13:37:53,720 - Do unicode file recognition:  false
2025-01-08 13:37:53,720 - Do unicode file recognition:  false
2025-01-08 13:37:53,720 - Do unicode file recognition:  false
2025-01-08 13:37:53,720 - FileResourceLoader : adding path '.'
2025-01-08 13:37:53,720 - FileResourceLoader : adding path '.'
2025-01-08 13:37:53,720 - FileResourceLoader : adding path '.'
2025-01-08 13:37:53,720 - FileResourceLoader : adding path '.'
2025-01-08 13:37:53,720 - FileResourceLoader : adding path '.'
2025-01-08 13:37:53,720 - FileResourceLoader : adding path '.'
2025-01-08 13:37:53,720 - FileResourceLoader : adding path '.'
2025-01-08 13:37:53,720 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:37:53,720 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:37:53,720 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:37:53,720 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:37:53,720 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:37:53,720 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:37:53,720 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:37:53,723 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:37:53,723 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:37:53,723 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:37:53,723 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:37:53,723 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:37:53,723 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:37:53,723 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:37:53,724 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:37:53,725 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:37:53,726 - Created '20' parsers.
2025-01-08 13:37:53,726 - Created '20' parsers.
2025-01-08 13:37:53,726 - Created '20' parsers.
2025-01-08 13:37:53,726 - Created '20' parsers.
2025-01-08 13:37:53,726 - Created '20' parsers.
2025-01-08 13:37:53,726 - Created '20' parsers.
2025-01-08 13:37:53,726 - Created '20' parsers.
2025-01-08 13:37:53,726 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:37:53,726 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:37:53,726 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:37:53,726 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:37:53,726 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:37:53,726 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:37:53,726 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:37:53,727 - Velocimacro : Default library not found.
2025-01-08 13:37:53,727 - Velocimacro : Default library not found.
2025-01-08 13:37:53,727 - Velocimacro : Default library not found.
2025-01-08 13:37:53,727 - Velocimacro : Default library not found.
2025-01-08 13:37:53,727 - Velocimacro : Default library not found.
2025-01-08 13:37:53,727 - Velocimacro : Default library not found.
2025-01-08 13:37:53,727 - Velocimacro : Default library not found.
2025-01-08 13:37:53,727 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:37:53,727 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:37:53,727 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:37:53,727 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:37:53,727 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:37:53,727 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:37:53,727 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:37:53,727 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:37:53,727 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:37:53,727 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:37:53,727 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:37:53,727 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:37:53,727 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:37:53,727 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:37:53,727 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:37:53,727 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:37:53,727 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:37:53,727 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:37:53,727 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:37:53,727 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:37:53,727 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:37:53,727 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:37:53,727 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:37:53,727 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:37:53,727 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:37:53,727 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:37:53,727 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:37:53,727 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:38:37,270 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:38:37,270 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:38:37,270 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:38:37,270 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:38:37,270 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:38:37,270 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:38:37,270 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:38:37,270 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:38:37,271 - Initializing Velocity, Calling init()...
2025-01-08 13:38:37,271 - Initializing Velocity, Calling init()...
2025-01-08 13:38:37,271 - Initializing Velocity, Calling init()...
2025-01-08 13:38:37,271 - Initializing Velocity, Calling init()...
2025-01-08 13:38:37,271 - Initializing Velocity, Calling init()...
2025-01-08 13:38:37,271 - Initializing Velocity, Calling init()...
2025-01-08 13:38:37,271 - Initializing Velocity, Calling init()...
2025-01-08 13:38:37,271 - Initializing Velocity, Calling init()...
2025-01-08 13:38:37,272 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:38:37,272 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:38:37,272 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:38:37,272 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:38:37,272 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:38:37,272 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:38:37,272 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:38:37,272 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:38:37,272 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:38:37,272 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:38:37,272 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:38:37,272 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:38:37,272 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:38:37,272 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:38:37,272 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:38:37,272 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:38:37,272 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:38:37,272 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:38:37,272 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:38:37,272 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:38:37,272 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:38:37,272 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:38:37,272 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:38:37,272 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:38:37,272 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:38:37,272 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:38:37,272 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:38:37,272 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:38:37,272 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:38:37,272 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:38:37,272 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:38:37,272 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:38:37,272 - Do unicode file recognition:  false
2025-01-08 13:38:37,272 - Do unicode file recognition:  false
2025-01-08 13:38:37,272 - Do unicode file recognition:  false
2025-01-08 13:38:37,272 - Do unicode file recognition:  false
2025-01-08 13:38:37,272 - Do unicode file recognition:  false
2025-01-08 13:38:37,272 - Do unicode file recognition:  false
2025-01-08 13:38:37,272 - Do unicode file recognition:  false
2025-01-08 13:38:37,272 - Do unicode file recognition:  false
2025-01-08 13:38:37,272 - FileResourceLoader : adding path '.'
2025-01-08 13:38:37,272 - FileResourceLoader : adding path '.'
2025-01-08 13:38:37,272 - FileResourceLoader : adding path '.'
2025-01-08 13:38:37,272 - FileResourceLoader : adding path '.'
2025-01-08 13:38:37,272 - FileResourceLoader : adding path '.'
2025-01-08 13:38:37,272 - FileResourceLoader : adding path '.'
2025-01-08 13:38:37,272 - FileResourceLoader : adding path '.'
2025-01-08 13:38:37,272 - FileResourceLoader : adding path '.'
2025-01-08 13:38:37,272 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:38:37,272 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:38:37,272 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:38:37,272 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:38:37,272 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:38:37,272 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:38:37,272 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:38:37,272 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:38:37,273 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:38:37,274 - Created '20' parsers.
2025-01-08 13:38:37,274 - Created '20' parsers.
2025-01-08 13:38:37,274 - Created '20' parsers.
2025-01-08 13:38:37,274 - Created '20' parsers.
2025-01-08 13:38:37,274 - Created '20' parsers.
2025-01-08 13:38:37,274 - Created '20' parsers.
2025-01-08 13:38:37,274 - Created '20' parsers.
2025-01-08 13:38:37,274 - Created '20' parsers.
2025-01-08 13:38:37,275 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:38:37,275 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:38:37,275 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:38:37,275 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:38:37,275 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:38:37,275 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:38:37,275 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:38:37,275 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:38:37,275 - Velocimacro : Default library not found.
2025-01-08 13:38:37,275 - Velocimacro : Default library not found.
2025-01-08 13:38:37,275 - Velocimacro : Default library not found.
2025-01-08 13:38:37,275 - Velocimacro : Default library not found.
2025-01-08 13:38:37,275 - Velocimacro : Default library not found.
2025-01-08 13:38:37,275 - Velocimacro : Default library not found.
2025-01-08 13:38:37,275 - Velocimacro : Default library not found.
2025-01-08 13:38:37,275 - Velocimacro : Default library not found.
2025-01-08 13:38:37,275 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:38:37,275 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:38:37,275 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:38:37,275 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:38:37,275 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:38:37,275 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:38:37,275 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:38:37,275 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:38:37,275 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:38:37,275 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:38:37,275 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:38:37,275 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:38:37,275 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:38:37,275 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:38:37,275 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:38:37,275 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:38:37,275 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:38:37,275 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:38:37,275 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:38:37,275 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:38:37,275 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:38:37,275 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:38:37,275 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:38:37,275 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:38:37,275 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:38:37,275 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:38:37,275 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:38:37,275 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:38:37,275 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:38:37,275 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:38:37,275 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:38:37,275 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:44:19,355 - Log4JLogChute initialized using file 'velocity.log'
