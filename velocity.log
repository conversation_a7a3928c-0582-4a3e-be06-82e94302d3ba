2025-01-08 13:44:19,358 - Initializing Velocity, Calling init()...
2025-01-08 13:44:19,359 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:44:19,359 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:44:19,359 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:44:19,359 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-01-08 13:44:19,359 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:44:19,359 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:44:19,371 - <PERSON><PERSON>oader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:44:19,375 - Do unicode file recognition:  false
2025-01-08 13:44:19,375 - FileResourceLoader : adding path '.'
2025-01-08 13:44:19,467 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:44:19,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:44:19,480 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:44:19,482 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:44:19,484 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:44:19,486 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:44:19,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:44:19,492 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:44:19,495 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:44:19,498 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:44:19,642 - Created '20' parsers.
2025-01-08 13:44:19,648 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:44:19,648 - Velocimacro : Default library not found.
2025-01-08 13:44:19,648 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:44:19,648 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:44:19,648 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:44:19,648 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:44:26,771 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:44:26,772 - Initializing Velocity, Calling init()...
2025-01-08 13:44:26,772 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:44:26,772 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:44:26,772 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:44:26,772 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:44:26,772 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:44:26,772 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:44:26,772 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:44:26,772 - Do unicode file recognition:  false
2025-01-08 13:44:26,773 - FileResourceLoader : adding path '.'
2025-01-08 13:44:26,773 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:44:26,773 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:44:26,773 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:44:26,773 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:44:26,773 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:44:26,773 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:44:26,773 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:44:26,773 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:44:26,773 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:44:26,773 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:44:26,774 - Created '20' parsers.
2025-01-08 13:44:26,774 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:44:26,774 - Velocimacro : Default library not found.
2025-01-08 13:44:26,774 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:44:26,774 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:44:26,774 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:44:26,774 - Velocimacro : autoload off : VM system will not automatically reload global library macros
 VM definitions
2025-01-08 13:44:26,774 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:44:26,774 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:52:40,567 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:52:40,568 - Initializing Velocity, Calling init()...
2025-01-08 13:52:40,568 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:52:40,568 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:52:40,568 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:52:40,568 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-01-08 13:52:40,568 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:52:40,568 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:52:40,581 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:52:40,585 - Do unicode file recognition:  false
2025-01-08 13:52:40,585 - FileResourceLoader : adding path '.'
2025-01-08 13:52:40,675 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:52:40,684 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:52:40,688 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:52:40,690 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:52:40,692 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:52:40,694 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:52:40,697 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:52:40,700 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:52:40,703 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:52:40,706 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:52:40,843 - Created '20' parsers.
2025-01-08 13:52:40,849 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:52:40,849 - Velocimacro : Default library not found.
2025-01-08 13:52:40,849 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:52:40,849 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:52:40,849 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:52:40,849 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:59:20,709 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:59:20,709 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 13:59:20,710 - Initializing Velocity, Calling init()...
2025-01-08 13:59:20,710 - Initializing Velocity, Calling init()...
2025-01-08 13:59:20,710 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:59:20,710 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 13:59:20,710 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:59:20,710 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 13:59:20,710 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:59:20,710 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 13:59:20,710 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:59:20,710 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 13:59:20,710 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:59:20,710 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:59:20,710 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:59:20,710 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 13:59:20,710 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:59:20,710 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 13:59:20,710 - Do unicode file recognition:  false
2025-01-08 13:59:20,710 - Do unicode file recognition:  false
2025-01-08 13:59:20,710 - FileResourceLoader : adding path '.'
2025-01-08 13:59:20,710 - FileResourceLoader : adding path '.'
2025-01-08 13:59:20,710 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:59:20,710 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:59:20,711 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 13:59:20,712 - Created '20' parsers.
2025-01-08 13:59:20,712 - Created '20' parsers.
2025-01-08 13:59:20,712 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:59:20,712 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 13:59:20,712 - Velocimacro : Default library not found.
2025-01-08 13:59:20,712 - Velocimacro : Default library not found.
2025-01-08 13:59:20,712 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:59:20,712 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 13:59:20,712 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:59:20,712 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 13:59:20,712 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:59:20,712 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 13:59:20,712 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 13:59:20,712 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:00:34,906 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:00:34,906 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:00:34,906 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:00:34,907 - Initializing Velocity, Calling init()...
2025-01-08 14:00:34,907 - Initializing Velocity, Calling init()...
2025-01-08 14:00:34,907 - Initializing Velocity, Calling init()...
2025-01-08 14:00:34,907 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:00:34,907 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:00:34,907 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:00:34,907 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:00:34,907 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:00:34,907 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:00:34,907 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:00:34,907 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:00:34,907 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:00:34,907 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:00:34,907 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:00:34,907 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:00:34,907 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:00:34,907 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:00:34,907 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:00:34,907 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:00:34,907 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:00:34,907 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:00:34,907 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:00:34,907 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:00:34,907 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:00:34,907 - Do unicode file recognition:  false
2025-01-08 14:00:34,907 - Do unicode file recognition:  false
2025-01-08 14:00:34,907 - Do unicode file recognition:  false
2025-01-08 14:00:34,907 - FileResourceLoader : adding path '.'
2025-01-08 14:00:34,907 - FileResourceLoader : adding path '.'
2025-01-08 14:00:34,907 - FileResourceLoader : adding path '.'
2025-01-08 14:00:34,908 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:00:34,908 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:00:34,908 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:00:34,908 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:00:34,909 - Created '20' parsers.
2025-01-08 14:00:34,909 - Created '20' parsers.
2025-01-08 14:00:34,909 - Created '20' parsers.
2025-01-08 14:00:34,909 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:00:34,909 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:00:34,909 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:00:34,909 - Velocimacro : Default library not found.
2025-01-08 14:00:34,909 - Velocimacro : Default library not found.
2025-01-08 14:00:34,909 - Velocimacro : Default library not found.
2025-01-08 14:00:34,909 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:00:34,909 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:00:34,909 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:00:34,910 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:00:34,910 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:00:34,910 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:00:34,910 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:00:34,910 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:00:34,910 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:00:34,910 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:00:34,910 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:00:34,910 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:01:05,560 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:01:05,560 - Initializing Velocity, Calling init()...
2025-01-08 14:01:05,560 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:01:05,560 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:01:05,560 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:01:05,560 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:01:05,560 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:01:05,560 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:01:05,560 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:01:05,560 - Do unicode file recognition:  false
2025-01-08 14:01:05,561 - FileResourceLoader : adding path '.'
2025-01-08 14:01:05,561 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:01:05,561 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:01:05,561 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:01:05,561 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:01:05,561 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:01:05,561 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:01:05,561 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:01:05,561 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:01:05,561 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:01:05,561 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:01:05,562 - Created '20' parsers.
2025-01-08 14:01:05,562 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:01:05,562 - Velocimacro : Default library not found.
2025-01-08 14:01:05,562 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:01:05,562 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:01:05,562 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:01:05,562 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:02:25,107 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:02:25,107 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:02:25,108 - Initializing Velocity, Calling init()...
2025-01-08 14:02:25,108 - Initializing Velocity, Calling init()...
2025-01-08 14:02:25,108 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:02:25,108 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:02:25,108 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:02:25,108 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:02:25,108 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:02:25,108 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:02:25,108 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:02:25,108 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:02:25,108 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:02:25,108 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:02:25,108 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:02:25,108 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:02:25,109 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:02:25,109 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:02:25,109 - Do unicode file recognition:  false
2025-01-08 14:02:25,109 - Do unicode file recognition:  false
2025-01-08 14:02:25,109 - FileResourceLoader : adding path '.'
2025-01-08 14:02:25,109 - FileResourceLoader : adding path '.'
2025-01-08 14:02:25,109 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:02:25,109 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:02:25,109 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:02:25,109 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:02:25,109 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:02:25,109 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:02:25,109 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:02:25,109 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:02:25,109 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:02:25,109 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:02:25,110 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:02:25,110 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:02:25,110 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:02:25,110 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:02:25,110 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:02:25,110 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:02:25,110 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:02:25,110 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:02:25,110 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:02:25,110 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:02:25,111 - Created '20' parsers.
2025-01-08 14:02:25,111 - Created '20' parsers.
2025-01-08 14:02:25,111 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:02:25,111 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:02:25,111 - Velocimacro : Default library not found.
2025-01-08 14:02:25,111 - Velocimacro : Default library not found.
2025-01-08 14:02:25,111 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:02:25,111 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:02:25,111 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:02:25,111 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:02:25,111 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:02:25,111 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:02:25,111 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:02:25,111 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:09:05,872 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:09:05,872 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:09:05,872 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:09:05,873 - Initializing Velocity, Calling init()...
2025-01-08 14:09:05,873 - Initializing Velocity, Calling init()...
2025-01-08 14:09:05,873 - Initializing Velocity, Calling init()...
2025-01-08 14:09:05,873 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:09:05,873 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:09:05,873 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:09:05,873 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:09:05,873 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:09:05,873 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:09:05,873 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:09:05,873 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:09:05,873 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:09:05,873 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:09:05,873 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:09:05,873 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:09:05,873 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:09:05,873 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:09:05,873 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:09:05,873 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:09:05,873 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:09:05,873 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:09:05,873 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:09:05,873 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:09:05,873 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:09:05,874 - Do unicode file recognition:  false
2025-01-08 14:09:05,874 - Do unicode file recognition:  false
2025-01-08 14:09:05,874 - Do unicode file recognition:  false
2025-01-08 14:09:05,874 - FileResourceLoader : adding path '.'
2025-01-08 14:09:05,874 - FileResourceLoader : adding path '.'
2025-01-08 14:09:05,874 - FileResourceLoader : adding path '.'
2025-01-08 14:09:05,874 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:09:05,874 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:09:05,874 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:09:05,874 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:09:05,875 - Created '20' parsers.
2025-01-08 14:09:05,875 - Created '20' parsers.
2025-01-08 14:09:05,875 - Created '20' parsers.
2025-01-08 14:09:05,876 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:09:05,876 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:09:05,876 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:09:05,876 - Velocimacro : Default library not found.
2025-01-08 14:09:05,876 - Velocimacro : Default library not found.
2025-01-08 14:09:05,876 - Velocimacro : Default library not found.
2025-01-08 14:09:05,876 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:09:05,876 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:09:05,876 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:09:05,876 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:09:05,876 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:09:05,876 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:09:05,876 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:09:05,876 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:09:05,876 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:09:05,876 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:09:05,876 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:09:05,876 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:10:05,088 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:10:05,088 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:10:05,088 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:10:05,088 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:10:05,089 - Initializing Velocity, Calling init()...
2025-01-08 14:10:05,089 - Initializing Velocity, Calling init()...
2025-01-08 14:10:05,089 - Initializing Velocity, Calling init()...
2025-01-08 14:10:05,089 - Initializing Velocity, Calling init()...
2025-01-08 14:10:05,089 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:10:05,089 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:10:05,089 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:10:05,089 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:10:05,089 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:10:05,089 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:10:05,089 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:10:05,089 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:10:05,089 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:10:05,089 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:10:05,089 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:10:05,089 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:10:05,089 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:10:05,089 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:10:05,089 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:10:05,089 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:10:05,089 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:10:05,089 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:10:05,089 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:10:05,089 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:10:05,089 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:10:05,089 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:10:05,089 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:10:05,089 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:10:05,089 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:10:05,089 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:10:05,089 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:10:05,089 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:10:05,089 - Do unicode file recognition:  false
2025-01-08 14:10:05,089 - Do unicode file recognition:  false
2025-01-08 14:10:05,089 - Do unicode file recognition:  false
2025-01-08 14:10:05,089 - Do unicode file recognition:  false
2025-01-08 14:10:05,089 - FileResourceLoader : adding path '.'
2025-01-08 14:10:05,089 - FileResourceLoader : adding path '.'
2025-01-08 14:10:05,089 - FileResourceLoader : adding path '.'
2025-01-08 14:10:05,089 - FileResourceLoader : adding path '.'
2025-01-08 14:10:05,089 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:10:05,089 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:10:05,089 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:10:05,089 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:10:05,090 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:10:05,091 - Created '20' parsers.
2025-01-08 14:10:05,091 - Created '20' parsers.
2025-01-08 14:10:05,091 - Created '20' parsers.
2025-01-08 14:10:05,091 - Created '20' parsers.
2025-01-08 14:10:05,091 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:10:05,091 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:10:05,091 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:10:05,091 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:10:05,091 - Velocimacro : Default library not found.
2025-01-08 14:10:05,091 - Velocimacro : Default library not found.
2025-01-08 14:10:05,091 - Velocimacro : Default library not found.
2025-01-08 14:10:05,091 - Velocimacro : Default library not found.
2025-01-08 14:10:05,091 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:10:05,091 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:10:05,091 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:10:05,091 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:10:05,091 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:10:05,091 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:10:05,091 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:10:05,091 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:10:05,091 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:10:05,091 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:10:05,091 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:10:05,091 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:10:05,091 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:10:05,091 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:10:05,091 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:10:05,091 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:12:23,207 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:12:23,207 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:12:23,207 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:12:23,207 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:12:23,207 - Log4JLogChute initialized using file 'velocity.log'
2025-01-08 14:12:23,208 - Initializing Velocity, Calling init()...
2025-01-08 14:12:23,208 - Initializing Velocity, Calling init()...
2025-01-08 14:12:23,208 - Initializing Velocity, Calling init()...
2025-01-08 14:12:23,208 - Initializing Velocity, Calling init()...
2025-01-08 14:12:23,208 - Initializing Velocity, Calling init()...
2025-01-08 14:12:23,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:12:23,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:12:23,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:12:23,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:12:23,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-08 14:12:23,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:12:23,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:12:23,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:12:23,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:12:23,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-08 14:12:23,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:12:23,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:12:23,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:12:23,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:12:23,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-08 14:12:23,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:12:23,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:12:23,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:12:23,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:12:23,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-08 14:12:23,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:12:23,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:12:23,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:12:23,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:12:23,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:12:23,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:12:23,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:12:23,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:12:23,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:12:23,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-08 14:12:23,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:12:23,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:12:23,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:12:23,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:12:23,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-08 14:12:23,208 - Do unicode file recognition:  false
2025-01-08 14:12:23,208 - Do unicode file recognition:  false
2025-01-08 14:12:23,208 - Do unicode file recognition:  false
2025-01-08 14:12:23,208 - Do unicode file recognition:  false
2025-01-08 14:12:23,208 - Do unicode file recognition:  false
2025-01-08 14:12:23,208 - FileResourceLoader : adding path '.'
2025-01-08 14:12:23,208 - FileResourceLoader : adding path '.'
2025-01-08 14:12:23,208 - FileResourceLoader : adding path '.'
2025-01-08 14:12:23,208 - FileResourceLoader : adding path '.'
2025-01-08 14:12:23,208 - FileResourceLoader : adding path '.'
2025-01-08 14:12:23,208 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:12:23,208 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:12:23,208 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:12:23,208 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:12:23,208 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:12:23,209 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-08 14:12:23,210 - Created '20' parsers.
2025-01-08 14:12:23,210 - Created '20' parsers.
2025-01-08 14:12:23,210 - Created '20' parsers.
2025-01-08 14:12:23,210 - Created '20' parsers.
2025-01-08 14:12:23,210 - Created '20' parsers.
2025-01-08 14:12:23,210 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:12:23,210 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:12:23,210 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:12:23,210 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:12:23,210 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-08 14:12:23,210 - Velocimacro : Default library not found.
2025-01-08 14:12:23,210 - Velocimacro : Default library not found.
2025-01-08 14:12:23,210 - Velocimacro : Default library not found.
2025-01-08 14:12:23,210 - Velocimacro : Default library not found.
2025-01-08 14:12:23,210 - Velocimacro : Default library not found.
2025-01-08 14:12:23,210 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:12:23,210 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:12:23,210 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:12:23,210 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:12:23,210 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-08 14:12:23,210 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:12:23,210 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:12:23,210 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:12:23,210 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:12:23,210 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-08 14:12:23,210 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:12:23,210 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:12:23,210 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:12:23,210 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:12:23,210 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-08 14:12:23,210 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:12:23,210 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:12:23,210 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:12:23,210 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-08 14:12:23,210 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-08-05 13:52:44,482 - Log4JLogChute initialized using file 'velocity.log'
2025-08-05 13:52:44,483 - Initializing Velocity, Calling init()...
2025-08-05 13:52:44,483 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-08-05 13:52:44,483 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-08-05 13:52:44,483 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-08-05 13:52:44,483 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-08-05 13:52:44,483 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-05 13:52:44,483 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-05 13:52:44,486 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-08-05 13:52:44,487 - Do unicode file recognition:  false
2025-08-05 13:52:44,487 - FileResourceLoader : adding path '.'
2025-08-05 13:52:44,495 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-08-05 13:52:44,499 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-08-05 13:52:44,500 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-08-05 13:52:44,500 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-08-05 13:52:44,501 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-08-05 13:52:44,501 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-08-05 13:52:44,502 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-08-05 13:52:44,502 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-08-05 13:52:44,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-08-05 13:52:44,504 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-08-05 13:52:44,524 - Created '20' parsers.
2025-08-05 13:52:44,528 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-08-05 13:52:44,528 - Velocimacro : Default library not found.
2025-08-05 13:52:44,528 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-08-05 13:52:44,528 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-08-05 13:52:44,528 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-08-05 13:52:44,528 - Velocimacro : autoload off : VM system will not automatically reload global library macros
